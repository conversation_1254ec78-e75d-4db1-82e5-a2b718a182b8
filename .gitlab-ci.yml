stages:
  - lint
  - build
  - deploy

# 设置相关变量
variables:
  BuildDir: ./build/app/outputs/apk/release
  ServerDir: /bfdx_server
  AppDir: $ServerDir/webclient/bf8100/assets/software
  CacheKey: $CI_PROJECT_NAME.$CI_COMMIT_REF_NAME.$CI_COMMIT_REF_SLUG
  PkgpushGit: https://$<EMAIL>/bfdx/bf8100pkg.git
  PkgBranch: bf8100_deviceapp
  Bf8100pkgFiles: https://git.kicad99.com/api/v4/projects/118/repository/files

.env_script:
  before_script:
    - export PUB_HOSTED_URL=https://pub.flutter-io.cn
    - export FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn
    - git config --global http.sslVerify false
    - git config --global http.version HTTP/1.1

.go-cache:
  variables:
    GOMODCACHE: $CI_PROJECT_DIR/.gomodcache
  before_script:
    - mkdir -p ${GOMODCACHE}
    - export GOPROXY='https://goproxy.cn|https://goproxy.io,direct'
  cache:
    key:
      files:
        - go.sum
    paths:
      - ${GOMODCACHE}/

.gradle-cache:
  before_script:
    - mkdir -p ~/.gradle
  cache:
    key: $CacheKey.gradle
    paths:
      - ~/.gradle/caches/
      - ~/.gradle/wrapper/

.flutter-cache:
  before_script:
    - mkdir -p ~/.pub-cache
  cache:
    key: $CacheKey.flutter
    paths:
      - ~/.pub-cache

flutter-lint:
  resource_group: bf8100app
  extends:
    - .env_script
    - .flutter-cache
  image: ghcr.io/yangjuncode/flutter-builder:3.24.3
  stage: lint
  tags:
    - docker
  only:
    changes:
      - lib/**/*.{dart}
  script:
    - touch .env
    - flutter pub get
    - flutter analyze

build-poc-config:
  resource_group: bf8100app
  extends:
    - .env_script
    - .go-cache
  image: ghcr.io/yangjuncode/go:1.21.13
  stage: build
  tags:
    - docker
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - app_config/version.txt
  script:
    - cd app_config && ./win-build-x86.sh && ./win-build-x64.sh
    - rm -rf $AppDir/bf8100_poc_app_config*.exe
    - \cp -f build/*.exe $AppDir/
  artifacts:
    expire_in: 30 day
    name: poc_app_config
    paths:
      - app_config/build/



build-android:
  resource_group: bf8100app
  extends:
    - .env_script
    - .go-cache
    - .gradle-cache
  image: ghcr.io/yangjuncode/flutter-builder:3.24.3
  stage: build
  tags:
    - docker
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.properties
  script:
    - export GOPRIVATE=git.kicad99.com
    - mkdir -p ~/.gradle && cp gradle.properties ~/.gradle/
    - ./build-android.sh
  artifacts:
    expire_in: 90 day
    name: android-apk
    paths:
      - $BuildDir

deploy-android:
  resource_group: bf8100app
  stage: deploy
  tags:
    - docker
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.properties
  dependencies:
    - build-android
  script:
    - rm -rf $AppDir/bf8100_poc*.apk
    - \cp -f $BuildDir/bf8100_poc*.apk $AppDir/
    - echo "$(ls -hl $AppDir | grep bf8100_poc)"

pkg-push:
  image: node:14-bullseye
  stage: deploy
  tags:
    - docker
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main" && $CI_COMMIT_TITLE =~ /.*release.*/'
      changes:
        - version.properties
  dependencies:
    - build-android
  script:
    # 把版本号放到环境变量中
    - source version.properties
    - echo "$PkgBranch version:$versionName"
    - ls -hl $BuildDir
    - mkdir -p pkgpush/$PkgBranch/$versionName && cd pkgpush
    - mv ../$BuildDir/bf8100_poc*.apk $PkgBranch/$versionName/
    - git init && git config user.name "linfl" && git config user.email "<EMAIL>"
    - git remote add origin $PkgpushGit && git branch -m $PkgBranch
    - curl --header "PRIVATE-TOKEN:$automr" "$Bf8100pkgFiles/.gitlab-ci.yml/raw?ref=main" -o .gitlab-ci.yml
    - git add . && git commit -m "$versionName" && git push -f origin $PkgBranch
  cache: { }
