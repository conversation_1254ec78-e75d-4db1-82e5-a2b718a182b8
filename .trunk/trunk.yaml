# This file controls the behavior of Trunk: https://docs.trunk.io/cli
# To learn more about the format of this file, see https://docs.trunk.io/reference/trunk-yaml
version: 0.1
cli:
  version: 1.22.8
# Trunk provides extensibility via plugins. (https://docs.trunk.io/plugins)
plugins:
  sources:
    - id: trunk
      ref: v1.6.4
      uri: https://github.com/trunk-io/plugins
# Many linters and tools depend on runtimes - configure them here. (https://docs.trunk.io/runtimes)
runtimes:
  enabled:
    - go@1.21.0
    - java@13.0.11
    - node@18.12.1
    - python@3.10.8
# This is the section where you manage your linters. (https://docs.trunk.io/check/configuration)
lint:
  enabled:
    - bandit@1.7.10
    - black@24.10.0
    - checkov@3.2.291
    - git-diff-check
    - gofmt@1.20.4
    - golangci-lint@1.62.0
    - isort@5.13.2
    - ktlint@1.4.1
    - markdownlint@0.42.0
    - osv-scanner@1.9.1
    - oxipng@9.1.2
    - prettier@3.3.3
    - ruff@0.7.3
    - shellcheck@0.10.0
    - shfmt@3.6.0
    - svgo@3.3.2
    - trufflehog@3.83.6
    - yamllint@1.35.1
