# bf8100deviceapp

## i18n

所有语言翻译在 `assets/i18n` 目录下，修改之后请运行 `i18n_key_gen.sh` 重新生成 `LocaleKeys`

## riverpod generator

修改 `riverpod` 目录下的代码时，运行 `riverpod_generator.sh`或者`flutter pub run build_runner watch -d`命令实时生成 riverpod
providers

## Android JNI调用CGO

#### 添加C++代码文档参考 [向您的项目添加 C 和 C++ 代码](https://blog.csdn.net/qq_33033586/article/details/108878977)

- 调用`goproxy/libproxy/build-android.sh`，将go代码编译成对应abi版本的动态库，放到`app/src/main/jniLibs`目录下
- 在Android工程的`app/src/main`目录下创建`cpp`目录，添加`CMakeLists.txt`和`native-lib.cpp`文件，配置编译参数和编写C++调用go代码
- 在`app/build.gradle`文件中添加`externalNativeBuild`,配置`cmake`来编译，并配置对应的`ndk.abiFilters`
- 在Android源代码中使用`external fun xxx()`来调用C++代码

## Gradle插件

- java版本与gradle插件版本不兼容，最新版本的Android Studio编译报错("Unsupported class file major version 65")，要求使用`AGP 8.1.0`版本
- 解决方案：配置`flutter config --jdk-dir`

```shell
  export JAVA_HOME=/usr/lib/jvm/default
  flutter config --jdk-dir=$JAVA_HOME
  # 或者全局配置
  echo 'eval "flutter config --jdk-dir=$JAVA_HOME"' >> ~/.bashrc
  echo 'eval "flutter config --jdk-dir=$JAVA_HOME"' >> ~/.zshrc
```

## App编译

- 直接调用根目录下的`build-android.sh`脚本即可自动编译成apk

## 开发环境配置

- 调试`goproxy`代理时，主要在PC端上运行，需要配置`.env`文件，指定代理服务器地址和端口
- `.env`文件内容参考如下：

```dotenv
SERVER_PROTOCOL=ws
SERVER_HOST=127.0.0.1
SERVER_PORT=12255
SERVER_PROXY=proxy
```

## 安装Launcher流程

- 先卸载旧的应用，再安装新版本
- 启动新的Launcher
- App开启了无障碍AccessibilityService服务，以便熄屏时监听报警键双击事件，第一次安装运行时需要用户主动开启服务

```shell
# 先将安装包拷贝到设备上
adb push BF8100DeviceApp-v1.0.0-202410310926-release.apk /data/local/tmp/
# 通过adb广播Intent静默升级应用
adb shell am broadcast -a unipro.install.pack -e package_path "/data/local/tmp/BF8100DeviceApp-v1.0.0-202410310926-release.apk" -e package_name "com.bfdx.bf8100deviceapp"

# 如果上面的静默升级失败，可以尝试以下方式
adb uninstall com.bfdx.bf8100deviceapp
adb shell rm -rf /data/data/com.bfdx.bf8100deviceapp
adb shell pm clear com.bfdx.bf8100deviceapp
# 替换apk文件路径, ./build/app/outputs/apk/release/BF8100DeviceApp-v1.0.0-202410310926-release.apk
adb install xxx.apk
adb shell am start -n com.bfdx.bf8100deviceapp/.LauncherActivity -a android.intent.action.MAIN -c android.intent.category.LAUNCHER -f 0x10008000
```

## App内检测升级

- 下载新版本安装包后，需要调用指定的接口以便静默升级

```kotlin
val intent = Intent()
intent.setAction("android.intent.action.MAIN")
intent.setComponent(ComponentName("cc.unipro.silentinstaller", "cc.unipro.silentinstaller.Main"))
intent.putExtra("cc.unipro.silentinstaller.action", "cc.unipro.silentinstaller.action.INSTALL")
// 这里请修改为你自己制定的内置SD卡文件路径
intent.putExtra("cc.unipro.silentinstaller.extra.FILE_PATH", " /data/local/tmp/xxxx.apk")
// mainActivity是主Activity对象
mainActivity.startActivityForResult(intent, 0xAA)
```

### 使用阿里字体图标`iconfont`

* 在`app_flutter/assets/iconfont/`目录下存放`iconfont`字体图标
* 在`pubspec.yaml`文件配置`assets`和`fonts`，注意`fonts`中的`family`要和生成的`iconfont.dart`库类名一致
* 生成`iconfont.dart`
  ```shell
  ./build_iconfont.sh
  # or
  node ./generateIconfont.js
  ```
* 使用方法：和`flutter`官方图标库使用方法一致


### android wav音频播放

* 调用`PlatformChannel.playSoundEffects("wav_file_name")`播放指定的音频文件
* android端的`WavMediaAudio`只支持播放`wav`格式的音频文件
* wav音频文件需要放在`android/app/src/main/res/raw`目录下
* wav音频只支持`8k`采样率，`16位`深度的音频文件，支持单声道和双声道
