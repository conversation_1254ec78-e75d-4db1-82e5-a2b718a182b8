plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

// 加载自定义签名配置文件
def keyStorePropertiesFile = rootProject.file('key.properties')
def keyStoreProperties = new Properties()
keyStoreProperties.load(new FileInputStream(file(keyStorePropertiesFile)))

// 自定义编译环境配置文件
def buildConfig = new Properties()
def buildConfigPropsFile = rootProject.file("build.properties")
buildConfig.load(new FileInputStream(file(buildConfigPropsFile)))

if (buildConfig.minSdkVersion == null) {
    buildConfig.minSdkVersion = flutter.minSdkVersion
}
if (buildConfig.targetSdkVersion == null) {
    buildConfig.targetSdkVersion = flutter.targetSdkVersion
}

android {
    // 签名的配置
    signingConfigs {
        release {
            storeFile file(keyStoreProperties["storeFile"])
            storePassword keyStoreProperties["storePassword"]
            keyAlias keyStoreProperties["keyAlias"]
            keyPassword keyStoreProperties["keyPassword"]
        }
    }

    namespace = "com.bfdx.bf8100deviceapp"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.bfdx.bf8100deviceapp"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdk = buildConfig.minSdkVersion.toInteger()
        targetSdk = buildConfig.targetSdkVersion.toInteger()
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        archivesBaseName = "bf8100_poc-$versionName"
        buildConfigField "String", "APP_VERSION", "\"$versionName\""
        def buildTime = new Date().format('yyyy-MM-dd HH:mm:ss')
        buildConfigField "String", "BUILD_TIME", "\"$buildTime\""

        ndk {
            abiFilters 'armeabi-v7a'//, 'arm64-v8a' //, 'x86_64' //,'x86'
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
//            signingConfig = signingConfigs.debug

            // 使用指定的签名文件配置
            signingConfig signingConfigs.release
            // 开启zipalign优化
            zipAlignEnabled true
            // 混淆
            minifyEnabled true
            // 移除无用的resource文件，必须在minifyEnabled=true的情况下才能使用
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            // 使用指定的签名文件配置
            signingConfig signingConfigs.release
        }
    }

    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
}
