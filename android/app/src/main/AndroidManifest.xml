<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.WRITE_OWNER_DATA" />
    <uses-permission android:name="android.permission.READ_OWNER_DATA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.WRITE_SMS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_SETTINGS" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission
        android:name="android.permission.CHANGE_CONFIGURATION"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.BIND_ACCESSIBILITY_SERVICE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.WRITE_SECURE_SETTINGS"
        tools:ignore="ProtectedPermissions" />

    <!--SMART_PTT2-BF-SCP810-9.9.14.1.apk 拥有的权限声明-->
    <uses-permission android:name="android.permission.WRITE_CONTACTS"/>
    <uses-permission android:name="android.permission.INJECT_EVENTS"/>
    <uses-permission android:name="android.permission.USES_POLICY_FORCE_LOCK"/>
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS"/>
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>
    <uses-permission android:name="android.permission.USB_PERMISSION"/>
    <uses-permission android:name="android.permission.SILENT_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY"/>
    <uses-permission android:name="android.permission.REORDER_TASKS"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.DELETE_PACKAGES"/>
    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA"/>
    <uses-permission android:name="android.permission.CLEAR_APP_CACHE"/>
    <uses-permission android:name="android.permission.MANAGE_USB"/>
    <uses-permission android:name="android.permission.REAL_GET_TASKS"/>
    <uses-permission android:name="android.permission.SHUTDOWN"/>
    <uses-permission android:name="android.permission.GRANT_RUNTIME_PERMISSIONS"/>
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"/>
    <uses-permission android:name="android.permission.GET_TOP_ACTIVITY_INFO"/>
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL"/>
    <uses-permission android:name="android.permission.READ_CONTACTS"/>
    <uses-permission android:name="android.permission.BODY_SENSORS"/>
    <uses-permission android:name="android.permission.DEVICE_POWER"/>
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE"/>
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE"/>
    <uses-permission android:name="com.motorolasolutions.permission.PTT_INPUT"/>
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM"/>
    <uses-permission android:name="com.tdtech.permission.PTT_KEY"/>
    <uses-permission android:name="com.tdtech.permission.FUNCTION_KEY"/>
    <uses-permission android:name="com.tdtech.permission.AUDIO_RECORD_KEY"/>
    <uses-permission android:name="com.tdtech.permission.IMP_KEY"/>
    <uses-permission android:name="com.tdtech.permission.MEDIA_RECORD_KEY"/>
    <uses-permission android:name="com.tdtech.permission.CAMERA_KEY"/>
    <uses-permission android:name="com.tdtech.permission.LIGHT_MANAGER"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES"/>

    <uses-feature
        android:name="android.hardware.nfc"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.Camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <application
        android:name="${applicationName}"
        android:icon="@mipmap/launcher_icon"
        android:label="bf8100deviceapp"
        android:supportsRtl="true">

        <!-- Launcher activity -->
        <activity
            android:name=".LauncherActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:excludeFromRecents="true"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleInstance"
            android:resumeWhilePausing="true"
            android:stateNotNeeded="true"
            android:taskAffinity="com.bfdx.bf8100deviceapp"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <!--                <category android:name="android.intent.category.HOME" />-->
                <!--                <category android:name="android.intent.category.DEFAULT" />-->
                <!--                <category android:name="android.intent.category.LAUNCHER_APP" />-->
            </intent-filter>
        </activity>

        <!-- Home activity -->
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:excludeFromRecents="true"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:launchMode="singleInstance"
            android:stateNotNeeded="true"
            android:taskAffinity="com.bfdx.bf8100deviceapp"
            android:theme="@style/MainTheme"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <!--                <category android:name="android.intent.category.LAUNCHER" />-->
                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
                <!--                <category android:name="android.intent.category.LAUNCHER_APP" />-->
            </intent-filter>
        </activity>

        <receiver
            android:name=".StaticBroadcastReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <!-- PTT -->
                <action android:name="unipro.hotkey.ptt.down" />
                <action android:name="unipro.hotkey.ptt.up" />
                <action android:name="unipro.hotkey.headset.ptt.down" />
                <action android:name="unipro.hotkey.headset.ptt.up" />

                <!--设备名称播报键-->
                <action android:name="unipro.hotkey.vb.down" />
                <action android:name="unipro.hotkey.vb.up" />
                <action android:name="unipro.hotkey.vb.long" />
                <!--群组播报键-->
                <action android:name="unipro.hotkey.sos.down" />
                <action android:name="unipro.hotkey.sos.up" />
                <action android:name="unipro.hotkey.sos.long" />
                <!--菜单键-->
                <action android:name="unipro.hotkey.call.down" />
                <action android:name="unipro.hotkey.call.up" />
                <action android:name="unipro.hotkey.call.long" />
                <!--帮助键-->
                <action android:name="unipro.hotkey.help.long" />
                <!-- HOME 键-->
                <action android:name="unipro.hotkey.home.down" />
                <action android:name="unipro.hotkey.home.up" />
                <action android:name="unipro.hotkey.home.long" />
                <!-- 熄屏键-->
                <action android:name="unipro.hotkey.p1.down" />
                <action android:name="unipro.hotkey.p1.up" />
                <action android:name="unipro.hotkey.p1.long" />
                <!--单呼键-->
                <action android:name="unipro.hotkey.p2.down" />
                <action android:name="unipro.hotkey.p2.up" />
                <action android:name="unipro.hotkey.p2.long" />
                <!--组呼键-->
                <action android:name="unipro.hotkey.p3.down" />
                <action android:name="unipro.hotkey.p3.up" />
                <action android:name="unipro.hotkey.p3.long" />
                <!-- 耳机插入 -->
                <action android:name="unipro.hotkey.headset.down" />
                <action android:name="android.media.VOLUME_CHANGED_ACTION" />
                <action android:name="android.intent.action.BATTERY_CHANGED" />
                <action android:name="android.intent.action.SCREEN_OFF" />
                <action android:name="android.intent.action.SCREEN_ON" />
                <action android:name="android.intent.action.MEDIA_BUTTON" />

                <!-- 专用蓝牙耳机事件 -->
                <action android:name="android.bluetooth.headset.action.VENDOR_SPECIFIC_HEADSET_EVENT" />
                <!-- 蓝牙耳机的连接 -->
                <action android:name="android.bluetooth.device.action.ACL_DISCONNECTED" />
                <action android:name="android.bluetooth.device.action.ACL_CONNECTED" />
            </intent-filter>
        </receiver>

        <receiver android:name=".MediaButtonReceiver"
                  android:enabled="true"
                  android:exported="true">
            <intent-filter android:priority="1001">
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

        <service
            android:name=".ProxyService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="location" />

        <service
            android:name=".PocAccessibilityService"
            android:enabled="true"
            android:exported="true"
            android:label="@string/accessibility_service_label"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility" />
        </service>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <meta-data
            android:name="io.flutter.embedding.android.EnableImpeller"
            android:value="false" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
    </queries>
</manifest>
