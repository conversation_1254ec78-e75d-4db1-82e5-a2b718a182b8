#include <jni.h>
#include <string>
#include <android/log.h>
#include "opus.h"
#include "libproxy.h"

#define LOG_TAG "proxy"

JavaVM *javaVM;

static jclass gClazz;

static bool isProxyRunning = false;

static OpusDecoder *opusDecoder;

static OpusEncoder *opusEncoder;

JNIEXPORT jint JNICALL
JNI_OnLoad(JavaVM *vm, void *reserved) {
    JNIEnv *env = NULL;

    if ((vm->GetEnv((void **) &env, JNI_VERSION_1_6)) != JNI_OK) {
        return JNI_ERR;
    }

    // 动态注册native函数 ...
    jclass clazz = env->FindClass("com/bfdx/bf8100deviceapp/ProxyService");
    if (clazz == NULL) { return JNI_ERR; }
    gClazz = (jclass) env->NewGlobalRef(clazz);
    if (gClazz == NULL) { return JNI_ERR; }

//    jclass clazz_player = env->FindClass("com/bfdx/bf8100deviceapp/MediaPlayer");
//    if (clazz_player == NULL) { return JNI_ERR; }

    javaVM = vm;

    return JNI_VERSION_1_6;
}

JNIEXPORT void JNICALL
JNI_OnUnload(JavaVM *vm, void *reserved) {
    JNIEnv *env = NULL;
    bool isOk = false;
    if (javaVM->GetEnv((void **) &env, JNI_VERSION_1_6) == JNI_OK) {
        isOk = true;
    }
    if (isOk && gClazz != NULL) {
        env->DeleteGlobalRef(gClazz);
    }

    javaVM = NULL;
}

void wakeUp() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID holdLockMth = env->GetStaticMethodID(gClazz, "holdWakeLock", "()V");
        if (holdLockMth != NULL) {
            env->CallStaticVoidMethod(gClazz, holdLockMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void wakeRelease() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID holdReleaseMth = env->GetStaticMethodID(gClazz, "releaseWakeLock", "()V");
        if (holdReleaseMth != NULL) {
            env->CallStaticVoidMethod(gClazz, holdReleaseMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void mediaStart() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID holdReleaseMth =
                env->GetStaticMethodID(gClazz, "updateNotificationStateMediaPlayerStart", "()V");
        if (holdReleaseMth != NULL) {
            env->CallStaticVoidMethod(gClazz, holdReleaseMth);
        }
    }

    javaVM->DetachCurrentThread();
}

//void kcpConn() {
//    if (javaVM == NULL) { return; }
//
//    JNIEnv *env = NULL;
//    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
//        return;
//    }
//
//    if (env != NULL && gClazz != NULL) {
//        jmethodID holdReleaseMth =
//                env->GetStaticMethodID(gClazz, "updateNotificationStateKcpConn", "()V");
//        if (holdReleaseMth != NULL) {
//            env->CallStaticVoidMethod(gClazz, holdReleaseMth);
//        }
//    }
//
//    javaVM->DetachCurrentThread();
//}
//
//void kcpConnLose() {
//    if (javaVM == NULL) { return; }
//
//    JNIEnv *env = NULL;
//    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
//        return;
//    }
//
//    if (env != NULL && gClazz != NULL) {
//        jmethodID holdReleaseMth =
//                env->GetStaticMethodID(gClazz, "updateNotificationStateKcpConnLose", "()V");
//        if (holdReleaseMth != NULL) {
//            env->CallStaticVoidMethod(gClazz, holdReleaseMth);
//        }
//    }
//
//    javaVM->DetachCurrentThread();
//}

//void updateNotificationTitle(char *data, int data_len) {
//    if (javaVM == NULL) { return; }
//
//    JNIEnv *env = NULL;
//    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
//        return;
//    }
//
//    if (env != NULL && gClazz != NULL) {
//        jmethodID holdReleaseMth =
//                env->GetStaticMethodID(gClazz, "updateNotificationTitle", "(Ljava/lang/String;)V");
//        std::string s(data, data_len);
//        jstring title = env->NewStringUTF(s.c_str());
//        if (holdReleaseMth != NULL) {
//            env->CallStaticVoidMethod(gClazz, holdReleaseMth, title);
//        }
//    }
//
//    javaVM->DetachCurrentThread();
//}
//
//void updateNotificationContent(char *data, int data_len) {
//    std::string s = "";
//    if (data_len != 0) {
//        s = std::string(data, data_len);
//    }
//    if (javaVM == NULL) { return; }
//
//    JNIEnv *env = NULL;
//    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
//        return;
//    }
//
//    if (env != NULL && gClazz != NULL) {
//        jmethodID holdReleaseMth =
//                env->GetStaticMethodID(gClazz, "updateNotificationContent",
//                                       "(Ljava/lang/String;)V");
//        jstring title = env->NewStringUTF(s.c_str());
//        if (holdReleaseMth != NULL) {
//            env->CallStaticVoidMethod(gClazz, holdReleaseMth, title);
//        }
//    }
//
//    javaVM->DetachCurrentThread();
//}

void locationOnce(int force) {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID locationOnceMth =
                env->GetStaticMethodID(gClazz, "LocationOnce", "(I)V");
        if (locationOnceMth != NULL) {
            env->CallStaticVoidMethod(gClazz, locationOnceMth, force);
        }
    }

    javaVM->DetachCurrentThread();
}

void startLocation() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID startLocationMth =
                env->GetStaticMethodID(gClazz, "StartLocation", "()V");
        if (startLocationMth != NULL) {
            env->CallStaticVoidMethod(gClazz, startLocationMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void stopLocation() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID StopLocationMth =
                env->GetStaticMethodID(gClazz, "StopLocation", "()V");
        if (StopLocationMth != NULL) {
            env->CallStaticVoidMethod(gClazz, StopLocationMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void startRecorder() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID startLocationMth =
                env->GetStaticMethodID(gClazz, "StartRecorder", "()V");
        if (startLocationMth != NULL) {
            env->CallStaticVoidMethod(gClazz, startLocationMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void stopRecorder() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID StopLocationMth =
                env->GetStaticMethodID(gClazz, "StopRecorder", "()V");
        if (StopLocationMth != NULL) {
            env->CallStaticVoidMethod(gClazz, StopLocationMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void startPlayer() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID startPlayerMth =
                env->GetStaticMethodID(gClazz, "StartPlayer", "()V");
        if (startPlayerMth != NULL) {
            env->CallStaticVoidMethod(gClazz, startPlayerMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void stopPlayer() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID StopPlayerMth =
                env->GetStaticMethodID(gClazz, "StopPlayer", "()V");
        if (StopPlayerMth != NULL) {
            env->CallStaticVoidMethod(gClazz, StopPlayerMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void playOk() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID StopPlayerMth =
                env->GetStaticMethodID(gClazz, "PlayOk", "()V");
        if (StopPlayerMth != NULL) {
            env->CallStaticVoidMethod(gClazz, StopPlayerMth);
        }
    }

    javaVM->DetachCurrentThread();
}

void playErr() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID StopPlayerMth =
                env->GetStaticMethodID(gClazz, "PlayErr", "()V");
        if (StopPlayerMth != NULL) {
            env->CallStaticVoidMethod(gClazz, StopPlayerMth);
        }
    }

    javaVM->DetachCurrentThread();
}

int opusEncode(unsigned char *opus, int opus_len, short *pcm, int pcm_frame_size) {
    if (!opusEncoder) {
        int error;
        opusEncoder = opus_encoder_create(8000, 1, OPUS_APPLICATION_VOIP, &error);
        if (error != OPUS_OK) {
//            __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "=========> create opus encoder error: %d", error);
            return -1;
        }
    }

    return opus_encode(opusEncoder, pcm, pcm_frame_size, opus, opus_len);
}

int OpusDecode(const unsigned char *opus, int opus_len, short *pcm, int pcm_frame_size) {
    if (!opusDecoder) {
        int error;
        opusDecoder = opus_decoder_create(8000, 1, &error);
        if (error != OPUS_OK) {
//            __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "=========> create opus decoder error: %d", error);
            return -1;
        }
    }

    return opus_decode(opusDecoder, opus, opus_len, pcm, pcm_frame_size, 0);
}

int onGetAppBuildInfo(const char *data, int data_len) {
    if (javaVM == NULL) {
        return -1;
    }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return -1;
    }

    if (env == NULL || gClazz == NULL) {
        return -1;
    }

    jmethodID getAppBuildInfoMth =
            env->GetStaticMethodID(gClazz, "getAppBuildInfo", "()Ljava/lang/String;");
    if (getAppBuildInfoMth == NULL)
    {
        return -1;
    }
    jstring info =  (jstring)env->CallStaticObjectMethod(gClazz, getAppBuildInfoMth);

    jsize jstring_len = env->GetStringUTFLength(info);
    if (jstring_len > data_len) {
        // 字符串长度超过最大长度
        return -2;
    }

    env->GetStringUTFRegion(info, 0, jstring_len, (char*)data);

    javaVM->DetachCurrentThread();

    return jstring_len;
}

void enableGps() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID methodId = env->GetStaticMethodID(gClazz, "enableGps", "()V");
        if (methodId != NULL) {
            env->CallStaticVoidMethod(gClazz, methodId);
        }
    }

    javaVM->DetachCurrentThread();
}

void disableGps() {
    if (javaVM == NULL) { return; }

    JNIEnv *env = NULL;
    if (javaVM->AttachCurrentThread(&env, NULL) != JNI_OK) {
        return;
    }

    if (env != NULL && gClazz != NULL) {
        jmethodID methodId = env->GetStaticMethodID(gClazz, "disableGps", "()V");
        if (methodId != NULL) {
            env->CallStaticVoidMethod(gClazz, methodId);
        }
    }

    javaVM->DetachCurrentThread();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_ProxyService_regCallBackFuncs(JNIEnv *env, jobject thiz) {
    RegisterWakeLockFunc((void *) wakeUp, (void *) wakeRelease);
    RegisterMediaStateChangeFunc((void *) mediaStart);
//    RegisterUpdateNotificationChangeFunc((void *) updateNotificationTitle,
//                                         (void *) updateNotificationContent);
//    RegisterKcpStateChangeFunc((void *) kcpConn, (void *) kcpConnLose);
    RegisterLocatorFunc((void *) locationOnce, (void *) startLocation, (void *) stopLocation);
    RegisterRecorderFunc((void *) startRecorder, (void *) stopRecorder);
    RegisterPlayerFunc((void *) startPlayer, (void *) stopPlayer);
    RegisterOkErrPlayerFunc((void *) playOk, (void *) playErr);
    RegisterOpusCodecFunc((void *) opusEncode, (void *) OpusDecode);
    RegisterGetAppBuildInfoFunc((void *) onGetAppBuildInfo);
    RegisterUniproGpsSetFunc((void *) enableGps, (void *) disableGps);
}

extern "C"
void proxyLog(char *data, int data_len) {
    std::string s(data, data_len);
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "=========> %s", s.c_str());
    return;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_ProxyService_startProxy(JNIEnv *env, jobject thiz) {
    isProxyRunning = true;
    SetLogOutput((void *) (proxyLog));
    ProxyMain();
    return;
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_bfdx_bf8100deviceapp_MainActivity_00024DartCall_queryProxyPort(JNIEnv *env, jobject thiz) {
    return GetProxyPort();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_PhoneReceiver_onPhoneStateCallIn(JNIEnv *env, jobject thiz) {
    if (!isProxyRunning) { return; }
    GetPhoneStateCallIn();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_PhoneReceiver_onPhoneStateCallOut(JNIEnv *env, jobject thiz) {
    if (!isProxyRunning) { return; }
    GetPhoneStateCallOut();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_PhoneReceiver_onPhoneStateIDLE(JNIEnv *env, jobject thiz) {
    if (!isProxyRunning) { return; }
    GetPhoneStateIDLE();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_MainActivity_onNetworkConn(JNIEnv *env, jobject thiz) {
    GetNetworkConn();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_MainActivity_onNetworkLose(JNIEnv *env, jobject thiz) {
    GetNetworkLoseConn();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_ProxyService_stopProxy(JNIEnv *env, jobject thiz) {
    isProxyRunning = false;
    ExitProxyMain();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_ProxyService_gotValidLocation(JNIEnv *env, jobject thiz, jlong time, jdouble lon,
                                                            jdouble lat, jfloat direction, jfloat speed, jdouble altitude) {
    GetValidLocation(time, lon, lat, direction, speed, altitude);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_MediaRecorder_onGot8kPcmData(JNIEnv *env, jobject thiz, jbyteArray data,
                                                           jint len) {
    char *chars = NULL;
    jbyte *bytes;
    bytes = env->GetByteArrayElements(data, 0);
    int chars_len = env->GetArrayLength(data);
    if (len > 0 && len < chars_len) {
        chars_len = len;
    }

    chars = new char[chars_len + 1];
    memset(chars, 0, chars_len + 1);
    memcpy(chars, bytes, chars_len);
    chars[chars_len] = 0;

    env->ReleaseByteArrayElements(data, bytes, 0);

//    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "=========> %s", bytes);

    On8K16BitsSamples(chars, chars_len, len);
}


extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_MediaRecorder_onGot48kPcmData(JNIEnv *env, jobject thiz, jbyteArray data,
                                                            jint len) {
    char *chars = NULL;
    jbyte *bytes;
    bytes = env->GetByteArrayElements(data, 0);
    int chars_len = env->GetArrayLength(data);
    if (len > 0 && len < chars_len) {
        chars_len = len;
    }

    chars = new char[chars_len + 1];
    memset(chars, 0, chars_len + 1);
    memcpy(chars, bytes, chars_len);
    chars[chars_len] = 0;

    env->ReleaseByteArrayElements(data, bytes, 0);

//    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "=========> %s", bytes);

    On48K16BitsSamples(chars, chars_len, len);
}

extern "C"
JNIEXPORT jbyteArray JNICALL
Java_com_bfdx_bf8100deviceapp_MediaPlayer_getOnePcmData(JNIEnv *env, jobject thiz
) {
    int frameCount = 512;
    unsigned char buf[frameCount * 2];
    auto len = GetOnePackPcmData(buf, frameCount);
    jbyteArray byteArray = env->NewByteArray(len);
    if (len == 0) {
        return byteArray;
    }

    env->SetByteArrayRegion(byteArray, 0, len,
                            reinterpret_cast<jbyte *>(buf));

    return byteArray;
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_bfdx_bf8100deviceapp_ProxyService_denoiseSetting(JNIEnv *env, jclass clazz) {
    return GetDenoiseSetting();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_ProxyService_setAppFilesDirPath(JNIEnv *env, jobject thiz, jstring files_dir_path) {
    const char *c_str = env->GetStringUTFChars(files_dir_path, reinterpret_cast<jboolean *>(0));
    if (c_str != NULL) {
        // 调用 Go 函数，将 c_str 作为参数传递
        GoString goString;
        goString.p = c_str;
        goString.n = strlen(c_str);
        SetAppFilesDirPath(goString);
        env->ReleaseStringUTFChars(files_dir_path, c_str);
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_com_bfdx_bf8100deviceapp_ProxyService_initSqliteDataBase(JNIEnv *env, jobject thiz) {
    InitSqliteDataBase();
}

