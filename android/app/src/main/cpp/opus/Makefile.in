# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@




VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
@FIXED_POINT_TRUE@am__append_1 = $(SILK_SOURCES_FIXED)
@FIXED_POINT_TRUE@@HAVE_SSE4_1_TRUE@am__append_2 = $(SILK_SOURCES_SSE4_1) $(SILK_SOURCES_FIXED_SSE4_1)
@FIXED_POINT_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__append_3 = $(SILK_SOURCES_FIXED_ARM_NEON_INTR)
@FIXED_POINT_FALSE@am__append_4 = $(SILK_SOURCES_FLOAT)
@FIXED_POINT_FALSE@@HAVE_SSE4_1_TRUE@am__append_5 = $(SILK_SOURCES_SSE4_1)
@DISABLE_FLOAT_API_FALSE@am__append_6 = $(OPUS_SOURCES_FLOAT)
@CPU_X86_TRUE@@HAVE_RTCD_TRUE@am__append_7 = $(CELT_SOURCES_X86_RTCD)
@CPU_X86_TRUE@@HAVE_RTCD_TRUE@am__append_8 = $(SILK_SOURCES_X86_RTCD)
@CPU_X86_TRUE@@HAVE_SSE_TRUE@am__append_9 = $(CELT_SOURCES_SSE)
@CPU_X86_TRUE@@HAVE_SSE2_TRUE@am__append_10 = $(CELT_SOURCES_SSE2)
@CPU_X86_TRUE@@HAVE_SSE4_1_TRUE@am__append_11 = $(CELT_SOURCES_SSE4_1)
@CPU_ARM_TRUE@@HAVE_RTCD_TRUE@am__append_12 = $(CELT_SOURCES_ARM_RTCD)
@CPU_ARM_TRUE@@HAVE_RTCD_TRUE@am__append_13 = $(SILK_SOURCES_ARM_RTCD)
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__append_14 = $(CELT_SOURCES_ARM_NEON_INTR)
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__append_15 = $(SILK_SOURCES_ARM_NEON_INTR)
@CPU_ARM_TRUE@@HAVE_ARM_NE10_TRUE@am__append_16 = $(CELT_SOURCES_ARM_NE10)
@OPUS_ARM_EXTERNAL_ASM_TRUE@am__append_17 = libarmasm.la
@EXTRA_PROGRAMS_TRUE@noinst_PROGRAMS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_cwrs32$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_dft$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_entropy$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_laplace$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_mathops$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_mdct$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_rotation$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_types$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	opus_compare$(EXEEXT) opus_demo$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	repacketizer_demo$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	silk/tests/test_unit_LPC_inv_pred_gain$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_api$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_decode$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_encode$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_padding$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_projection$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	trivial_example$(EXEEXT) $(am__EXEEXT_1)
@EXTRA_PROGRAMS_TRUE@TESTS = celt/tests/test_unit_cwrs32$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_dft$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_entropy$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_laplace$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_mathops$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_mdct$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_rotation$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_types$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	silk/tests/test_unit_LPC_inv_pred_gain$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_api$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_decode$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_encode$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_padding$(EXEEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_projection$(EXEEXT)
@EXTRA_PROGRAMS_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@am__append_18 = libarmasm.la
@EXTRA_PROGRAMS_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@am__append_19 = libarmasm.la
@EXTRA_PROGRAMS_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@am__append_20 = libarmasm.la
@EXTRA_PROGRAMS_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@am__append_21 = libarmasm.la
@EXTRA_PROGRAMS_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@am__append_22 = libarmasm.la
@EXTRA_PROGRAMS_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@am__append_23 = libarmasm.la
@CUSTOM_MODES_TRUE@am__append_24 = include/opus_custom.h
@CUSTOM_MODES_TRUE@@EXTRA_PROGRAMS_TRUE@am__append_25 = opus_custom_demo
subdir = .
SUBDIRS =
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/as-gcc-inline-assembly.m4 \
	$(top_srcdir)/m4/ax_add_fortify_source.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/opus-intrinsics.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(top_srcdir)/configure \
	$(am__configure_deps) $(noinst_HEADERS) \
	$(am__pkginclude_HEADERS_DIST) $(am__DIST_COMMON)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = config.h
CONFIG_CLEAN_FILES = opus.pc opus-uninstalled.pc celt/arm/armopts.s
CONFIG_CLEAN_VPATH_FILES =
@CUSTOM_MODES_TRUE@@EXTRA_PROGRAMS_TRUE@am__EXEEXT_1 = opus_custom_demo$(EXEEXT)
PROGRAMS = $(noinst_PROGRAMS)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(m4datadir)" \
	"$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(pkgincludedir)"
LTLIBRARIES = $(lib_LTLIBRARIES) $(noinst_LTLIBRARIES)
libarmasm_la_LIBADD =
am__libarmasm_la_SOURCES_DIST = celt/arm/celt_pitch_xcorr_arm-gnu.S
am__dirstamp = $(am__leading_dot)dirstamp
am__objects_1 = celt/arm/celt_pitch_xcorr_arm-gnu.lo
@CPU_ARM_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@am_libarmasm_la_OBJECTS =  \
@CPU_ARM_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@	$(am__objects_1)
libarmasm_la_OBJECTS = $(am_libarmasm_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
@CPU_ARM_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@am_libarmasm_la_rpath =
am__DEPENDENCIES_1 =
libopus_la_DEPENDENCIES = $(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__append_17)
am__libopus_la_SOURCES_DIST = celt/bands.c celt/celt.c \
	celt/celt_encoder.c celt/celt_decoder.c celt/cwrs.c \
	celt/entcode.c celt/entdec.c celt/entenc.c celt/kiss_fft.c \
	celt/laplace.c celt/mathops.c celt/mdct.c celt/modes.c \
	celt/pitch.c celt/celt_lpc.c celt/quant_bands.c celt/rate.c \
	celt/vq.c celt/x86/x86cpu.c celt/x86/x86_celt_map.c \
	celt/x86/pitch_sse.c celt/x86/pitch_sse2.c celt/x86/vq_sse2.c \
	celt/x86/celt_lpc_sse4_1.c celt/x86/pitch_sse4_1.c \
	celt/arm/armcpu.c celt/arm/arm_celt_map.c \
	celt/arm/celt_neon_intr.c celt/arm/pitch_neon_intr.c \
	celt/arm/celt_fft_ne10.c celt/arm/celt_mdct_ne10.c silk/CNG.c \
	silk/code_signs.c silk/init_decoder.c silk/decode_core.c \
	silk/decode_frame.c silk/decode_parameters.c \
	silk/decode_indices.c silk/decode_pulses.c \
	silk/decoder_set_fs.c silk/dec_API.c silk/enc_API.c \
	silk/encode_indices.c silk/encode_pulses.c silk/gain_quant.c \
	silk/interpolate.c silk/LP_variable_cutoff.c \
	silk/NLSF_decode.c silk/NSQ.c silk/NSQ_del_dec.c silk/PLC.c \
	silk/shell_coder.c silk/tables_gain.c silk/tables_LTP.c \
	silk/tables_NLSF_CB_NB_MB.c silk/tables_NLSF_CB_WB.c \
	silk/tables_other.c silk/tables_pitch_lag.c \
	silk/tables_pulses_per_block.c silk/VAD.c \
	silk/control_audio_bandwidth.c silk/quant_LTP_gains.c \
	silk/VQ_WMat_EC.c silk/HP_variable_cutoff.c silk/NLSF_encode.c \
	silk/NLSF_VQ.c silk/NLSF_unpack.c silk/NLSF_del_dec_quant.c \
	silk/process_NLSFs.c silk/stereo_LR_to_MS.c \
	silk/stereo_MS_to_LR.c silk/check_control_input.c \
	silk/control_SNR.c silk/init_encoder.c silk/control_codec.c \
	silk/A2NLSF.c silk/ana_filt_bank_1.c silk/biquad_alt.c \
	silk/bwexpander_32.c silk/bwexpander.c silk/debug.c \
	silk/decode_pitch.c silk/inner_prod_aligned.c silk/lin2log.c \
	silk/log2lin.c silk/LPC_analysis_filter.c \
	silk/LPC_inv_pred_gain.c silk/table_LSF_cos.c silk/NLSF2A.c \
	silk/NLSF_stabilize.c silk/NLSF_VQ_weights_laroia.c \
	silk/pitch_est_tables.c silk/resampler.c \
	silk/resampler_down2_3.c silk/resampler_down2.c \
	silk/resampler_private_AR2.c silk/resampler_private_down_FIR.c \
	silk/resampler_private_IIR_FIR.c \
	silk/resampler_private_up2_HQ.c silk/resampler_rom.c \
	silk/sigm_Q15.c silk/sort.c silk/sum_sqr_shift.c \
	silk/stereo_decode_pred.c silk/stereo_encode_pred.c \
	silk/stereo_find_predictor.c silk/stereo_quant_pred.c \
	silk/LPC_fit.c silk/fixed/LTP_analysis_filter_FIX.c \
	silk/fixed/LTP_scale_ctrl_FIX.c silk/fixed/corrMatrix_FIX.c \
	silk/fixed/encode_frame_FIX.c silk/fixed/find_LPC_FIX.c \
	silk/fixed/find_LTP_FIX.c silk/fixed/find_pitch_lags_FIX.c \
	silk/fixed/find_pred_coefs_FIX.c \
	silk/fixed/noise_shape_analysis_FIX.c \
	silk/fixed/process_gains_FIX.c \
	silk/fixed/regularize_correlations_FIX.c \
	silk/fixed/residual_energy16_FIX.c \
	silk/fixed/residual_energy_FIX.c \
	silk/fixed/warped_autocorrelation_FIX.c \
	silk/fixed/apply_sine_window_FIX.c silk/fixed/autocorr_FIX.c \
	silk/fixed/burg_modified_FIX.c silk/fixed/k2a_FIX.c \
	silk/fixed/k2a_Q16_FIX.c silk/fixed/pitch_analysis_core_FIX.c \
	silk/fixed/vector_ops_FIX.c silk/fixed/schur64_FIX.c \
	silk/fixed/schur_FIX.c silk/x86/NSQ_sse4_1.c \
	silk/x86/NSQ_del_dec_sse4_1.c silk/x86/VAD_sse4_1.c \
	silk/x86/VQ_WMat_EC_sse4_1.c \
	silk/fixed/x86/vector_ops_FIX_sse4_1.c \
	silk/fixed/x86/burg_modified_FIX_sse4_1.c \
	silk/fixed/arm/warped_autocorrelation_FIX_neon_intr.c \
	silk/float/apply_sine_window_FLP.c silk/float/corrMatrix_FLP.c \
	silk/float/encode_frame_FLP.c silk/float/find_LPC_FLP.c \
	silk/float/find_LTP_FLP.c silk/float/find_pitch_lags_FLP.c \
	silk/float/find_pred_coefs_FLP.c \
	silk/float/LPC_analysis_filter_FLP.c \
	silk/float/LTP_analysis_filter_FLP.c \
	silk/float/LTP_scale_ctrl_FLP.c \
	silk/float/noise_shape_analysis_FLP.c \
	silk/float/process_gains_FLP.c \
	silk/float/regularize_correlations_FLP.c \
	silk/float/residual_energy_FLP.c \
	silk/float/warped_autocorrelation_FLP.c \
	silk/float/wrappers_FLP.c silk/float/autocorrelation_FLP.c \
	silk/float/burg_modified_FLP.c silk/float/bwexpander_FLP.c \
	silk/float/energy_FLP.c silk/float/inner_product_FLP.c \
	silk/float/k2a_FLP.c silk/float/LPC_inv_pred_gain_FLP.c \
	silk/float/pitch_analysis_core_FLP.c \
	silk/float/scale_copy_vector_FLP.c \
	silk/float/scale_vector_FLP.c silk/float/schur_FLP.c \
	silk/float/sort_FLP.c silk/x86/x86_silk_map.c \
	silk/arm/arm_silk_map.c silk/arm/biquad_alt_neon_intr.c \
	silk/arm/LPC_inv_pred_gain_neon_intr.c \
	silk/arm/NSQ_del_dec_neon_intr.c silk/arm/NSQ_neon.c \
	src/opus.c src/opus_decoder.c src/opus_encoder.c \
	src/opus_multistream.c src/opus_multistream_encoder.c \
	src/opus_multistream_decoder.c src/repacketizer.c \
	src/opus_projection_encoder.c src/opus_projection_decoder.c \
	src/mapping_matrix.c src/analysis.c src/mlp.c src/mlp_data.c
am__objects_2 = celt/x86/x86cpu.lo celt/x86/x86_celt_map.lo
@CPU_X86_TRUE@@HAVE_RTCD_TRUE@am__objects_3 = $(am__objects_2)
am__objects_4 = celt/x86/pitch_sse.lo
@CPU_X86_TRUE@@HAVE_SSE_TRUE@am__objects_5 = $(am__objects_4)
am__objects_6 = celt/x86/pitch_sse2.lo celt/x86/vq_sse2.lo
@CPU_X86_TRUE@@HAVE_SSE2_TRUE@am__objects_7 = $(am__objects_6)
am__objects_8 = celt/x86/celt_lpc_sse4_1.lo celt/x86/pitch_sse4_1.lo
@CPU_X86_TRUE@@HAVE_SSE4_1_TRUE@am__objects_9 = $(am__objects_8)
am__objects_10 = celt/arm/armcpu.lo celt/arm/arm_celt_map.lo
@CPU_ARM_TRUE@@HAVE_RTCD_TRUE@am__objects_11 = $(am__objects_10)
am__objects_12 = celt/arm/celt_neon_intr.lo \
	celt/arm/pitch_neon_intr.lo
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__objects_13 =  \
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@	$(am__objects_12)
am__objects_14 = celt/arm/celt_fft_ne10.lo celt/arm/celt_mdct_ne10.lo
@CPU_ARM_TRUE@@HAVE_ARM_NE10_TRUE@am__objects_15 = $(am__objects_14)
am__objects_16 = celt/bands.lo celt/celt.lo celt/celt_encoder.lo \
	celt/celt_decoder.lo celt/cwrs.lo celt/entcode.lo \
	celt/entdec.lo celt/entenc.lo celt/kiss_fft.lo celt/laplace.lo \
	celt/mathops.lo celt/mdct.lo celt/modes.lo celt/pitch.lo \
	celt/celt_lpc.lo celt/quant_bands.lo celt/rate.lo celt/vq.lo \
	$(am__objects_3) $(am__objects_5) $(am__objects_7) \
	$(am__objects_9) $(am__objects_11) $(am__objects_13) \
	$(am__objects_15)
am__objects_17 = silk/fixed/LTP_analysis_filter_FIX.lo \
	silk/fixed/LTP_scale_ctrl_FIX.lo silk/fixed/corrMatrix_FIX.lo \
	silk/fixed/encode_frame_FIX.lo silk/fixed/find_LPC_FIX.lo \
	silk/fixed/find_LTP_FIX.lo silk/fixed/find_pitch_lags_FIX.lo \
	silk/fixed/find_pred_coefs_FIX.lo \
	silk/fixed/noise_shape_analysis_FIX.lo \
	silk/fixed/process_gains_FIX.lo \
	silk/fixed/regularize_correlations_FIX.lo \
	silk/fixed/residual_energy16_FIX.lo \
	silk/fixed/residual_energy_FIX.lo \
	silk/fixed/warped_autocorrelation_FIX.lo \
	silk/fixed/apply_sine_window_FIX.lo silk/fixed/autocorr_FIX.lo \
	silk/fixed/burg_modified_FIX.lo silk/fixed/k2a_FIX.lo \
	silk/fixed/k2a_Q16_FIX.lo \
	silk/fixed/pitch_analysis_core_FIX.lo \
	silk/fixed/vector_ops_FIX.lo silk/fixed/schur64_FIX.lo \
	silk/fixed/schur_FIX.lo
@FIXED_POINT_TRUE@am__objects_18 = $(am__objects_17)
am__objects_19 = silk/x86/NSQ_sse4_1.lo silk/x86/NSQ_del_dec_sse4_1.lo \
	silk/x86/VAD_sse4_1.lo silk/x86/VQ_WMat_EC_sse4_1.lo
am__objects_20 = silk/fixed/x86/vector_ops_FIX_sse4_1.lo \
	silk/fixed/x86/burg_modified_FIX_sse4_1.lo
@FIXED_POINT_TRUE@@HAVE_SSE4_1_TRUE@am__objects_21 =  \
@FIXED_POINT_TRUE@@HAVE_SSE4_1_TRUE@	$(am__objects_19) \
@FIXED_POINT_TRUE@@HAVE_SSE4_1_TRUE@	$(am__objects_20)
am__objects_22 =  \
	silk/fixed/arm/warped_autocorrelation_FIX_neon_intr.lo
@FIXED_POINT_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__objects_23 =  \
@FIXED_POINT_TRUE@@HAVE_ARM_NEON_INTR_TRUE@	$(am__objects_22)
am__objects_24 = silk/float/apply_sine_window_FLP.lo \
	silk/float/corrMatrix_FLP.lo silk/float/encode_frame_FLP.lo \
	silk/float/find_LPC_FLP.lo silk/float/find_LTP_FLP.lo \
	silk/float/find_pitch_lags_FLP.lo \
	silk/float/find_pred_coefs_FLP.lo \
	silk/float/LPC_analysis_filter_FLP.lo \
	silk/float/LTP_analysis_filter_FLP.lo \
	silk/float/LTP_scale_ctrl_FLP.lo \
	silk/float/noise_shape_analysis_FLP.lo \
	silk/float/process_gains_FLP.lo \
	silk/float/regularize_correlations_FLP.lo \
	silk/float/residual_energy_FLP.lo \
	silk/float/warped_autocorrelation_FLP.lo \
	silk/float/wrappers_FLP.lo silk/float/autocorrelation_FLP.lo \
	silk/float/burg_modified_FLP.lo silk/float/bwexpander_FLP.lo \
	silk/float/energy_FLP.lo silk/float/inner_product_FLP.lo \
	silk/float/k2a_FLP.lo silk/float/LPC_inv_pred_gain_FLP.lo \
	silk/float/pitch_analysis_core_FLP.lo \
	silk/float/scale_copy_vector_FLP.lo \
	silk/float/scale_vector_FLP.lo silk/float/schur_FLP.lo \
	silk/float/sort_FLP.lo
@FIXED_POINT_FALSE@am__objects_25 = $(am__objects_24)
@FIXED_POINT_FALSE@@HAVE_SSE4_1_TRUE@am__objects_26 =  \
@FIXED_POINT_FALSE@@HAVE_SSE4_1_TRUE@	$(am__objects_19)
am__objects_27 = silk/x86/x86_silk_map.lo
@CPU_X86_TRUE@@HAVE_RTCD_TRUE@am__objects_28 = $(am__objects_27)
am__objects_29 = silk/arm/arm_silk_map.lo
@CPU_ARM_TRUE@@HAVE_RTCD_TRUE@am__objects_30 = $(am__objects_29)
am__objects_31 = silk/arm/biquad_alt_neon_intr.lo \
	silk/arm/LPC_inv_pred_gain_neon_intr.lo \
	silk/arm/NSQ_del_dec_neon_intr.lo silk/arm/NSQ_neon.lo
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__objects_32 =  \
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@	$(am__objects_31)
am__objects_33 = silk/CNG.lo silk/code_signs.lo silk/init_decoder.lo \
	silk/decode_core.lo silk/decode_frame.lo \
	silk/decode_parameters.lo silk/decode_indices.lo \
	silk/decode_pulses.lo silk/decoder_set_fs.lo silk/dec_API.lo \
	silk/enc_API.lo silk/encode_indices.lo silk/encode_pulses.lo \
	silk/gain_quant.lo silk/interpolate.lo \
	silk/LP_variable_cutoff.lo silk/NLSF_decode.lo silk/NSQ.lo \
	silk/NSQ_del_dec.lo silk/PLC.lo silk/shell_coder.lo \
	silk/tables_gain.lo silk/tables_LTP.lo \
	silk/tables_NLSF_CB_NB_MB.lo silk/tables_NLSF_CB_WB.lo \
	silk/tables_other.lo silk/tables_pitch_lag.lo \
	silk/tables_pulses_per_block.lo silk/VAD.lo \
	silk/control_audio_bandwidth.lo silk/quant_LTP_gains.lo \
	silk/VQ_WMat_EC.lo silk/HP_variable_cutoff.lo \
	silk/NLSF_encode.lo silk/NLSF_VQ.lo silk/NLSF_unpack.lo \
	silk/NLSF_del_dec_quant.lo silk/process_NLSFs.lo \
	silk/stereo_LR_to_MS.lo silk/stereo_MS_to_LR.lo \
	silk/check_control_input.lo silk/control_SNR.lo \
	silk/init_encoder.lo silk/control_codec.lo silk/A2NLSF.lo \
	silk/ana_filt_bank_1.lo silk/biquad_alt.lo \
	silk/bwexpander_32.lo silk/bwexpander.lo silk/debug.lo \
	silk/decode_pitch.lo silk/inner_prod_aligned.lo \
	silk/lin2log.lo silk/log2lin.lo silk/LPC_analysis_filter.lo \
	silk/LPC_inv_pred_gain.lo silk/table_LSF_cos.lo silk/NLSF2A.lo \
	silk/NLSF_stabilize.lo silk/NLSF_VQ_weights_laroia.lo \
	silk/pitch_est_tables.lo silk/resampler.lo \
	silk/resampler_down2_3.lo silk/resampler_down2.lo \
	silk/resampler_private_AR2.lo \
	silk/resampler_private_down_FIR.lo \
	silk/resampler_private_IIR_FIR.lo \
	silk/resampler_private_up2_HQ.lo silk/resampler_rom.lo \
	silk/sigm_Q15.lo silk/sort.lo silk/sum_sqr_shift.lo \
	silk/stereo_decode_pred.lo silk/stereo_encode_pred.lo \
	silk/stereo_find_predictor.lo silk/stereo_quant_pred.lo \
	silk/LPC_fit.lo $(am__objects_18) $(am__objects_21) \
	$(am__objects_23) $(am__objects_25) $(am__objects_26) \
	$(am__objects_28) $(am__objects_30) $(am__objects_32)
am__objects_34 = src/analysis.lo src/mlp.lo src/mlp_data.lo
@DISABLE_FLOAT_API_FALSE@am__objects_35 = $(am__objects_34)
am__objects_36 = src/opus.lo src/opus_decoder.lo src/opus_encoder.lo \
	src/opus_multistream.lo src/opus_multistream_encoder.lo \
	src/opus_multistream_decoder.lo src/repacketizer.lo \
	src/opus_projection_encoder.lo src/opus_projection_decoder.lo \
	src/mapping_matrix.lo $(am__objects_35)
am_libopus_la_OBJECTS = $(am__objects_16) $(am__objects_33) \
	$(am__objects_36)
libopus_la_OBJECTS = $(am_libopus_la_OBJECTS)
libopus_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libopus_la_LDFLAGS) $(LDFLAGS) -o $@
am__celt_tests_test_unit_cwrs32_SOURCES_DIST =  \
	celt/tests/test_unit_cwrs32.c
@EXTRA_PROGRAMS_TRUE@am_celt_tests_test_unit_cwrs32_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_cwrs32.$(OBJEXT)
celt_tests_test_unit_cwrs32_OBJECTS =  \
	$(am_celt_tests_test_unit_cwrs32_OBJECTS)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_cwrs32_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__celt_tests_test_unit_dft_SOURCES_DIST =  \
	celt/tests/test_unit_dft.c
@EXTRA_PROGRAMS_TRUE@am_celt_tests_test_unit_dft_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_dft.$(OBJEXT)
celt_tests_test_unit_dft_OBJECTS =  \
	$(am_celt_tests_test_unit_dft_OBJECTS)
am__DEPENDENCIES_2 = celt/x86/x86cpu.lo celt/x86/x86_celt_map.lo
@CPU_X86_TRUE@@HAVE_RTCD_TRUE@am__DEPENDENCIES_3 =  \
@CPU_X86_TRUE@@HAVE_RTCD_TRUE@	$(am__DEPENDENCIES_2)
am__DEPENDENCIES_4 = celt/x86/pitch_sse.lo
@CPU_X86_TRUE@@HAVE_SSE_TRUE@am__DEPENDENCIES_5 =  \
@CPU_X86_TRUE@@HAVE_SSE_TRUE@	$(am__DEPENDENCIES_4)
am__DEPENDENCIES_6 = celt/x86/pitch_sse2.lo celt/x86/vq_sse2.lo
@CPU_X86_TRUE@@HAVE_SSE2_TRUE@am__DEPENDENCIES_7 =  \
@CPU_X86_TRUE@@HAVE_SSE2_TRUE@	$(am__DEPENDENCIES_6)
am__DEPENDENCIES_8 = celt/x86/celt_lpc_sse4_1.lo \
	celt/x86/pitch_sse4_1.lo
@CPU_X86_TRUE@@HAVE_SSE4_1_TRUE@am__DEPENDENCIES_9 =  \
@CPU_X86_TRUE@@HAVE_SSE4_1_TRUE@	$(am__DEPENDENCIES_8)
am__DEPENDENCIES_10 = celt/arm/armcpu.lo celt/arm/arm_celt_map.lo
@CPU_ARM_TRUE@@HAVE_RTCD_TRUE@am__DEPENDENCIES_11 =  \
@CPU_ARM_TRUE@@HAVE_RTCD_TRUE@	$(am__DEPENDENCIES_10)
am__DEPENDENCIES_12 = celt/arm/celt_neon_intr.lo \
	celt/arm/pitch_neon_intr.lo
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__DEPENDENCIES_13 =  \
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@	$(am__DEPENDENCIES_12)
am__DEPENDENCIES_14 = celt/arm/celt_fft_ne10.lo \
	celt/arm/celt_mdct_ne10.lo
@CPU_ARM_TRUE@@HAVE_ARM_NE10_TRUE@am__DEPENDENCIES_15 =  \
@CPU_ARM_TRUE@@HAVE_ARM_NE10_TRUE@	$(am__DEPENDENCIES_14)
am__DEPENDENCIES_16 = celt/bands.lo celt/celt.lo celt/celt_encoder.lo \
	celt/celt_decoder.lo celt/cwrs.lo celt/entcode.lo \
	celt/entdec.lo celt/entenc.lo celt/kiss_fft.lo celt/laplace.lo \
	celt/mathops.lo celt/mdct.lo celt/modes.lo celt/pitch.lo \
	celt/celt_lpc.lo celt/quant_bands.lo celt/rate.lo celt/vq.lo \
	$(am__DEPENDENCIES_3) $(am__DEPENDENCIES_5) \
	$(am__DEPENDENCIES_7) $(am__DEPENDENCIES_9) \
	$(am__DEPENDENCIES_11) $(am__DEPENDENCIES_13) \
	$(am__DEPENDENCIES_15)
@EXTRA_PROGRAMS_TRUE@am__DEPENDENCIES_17 = $(am__DEPENDENCIES_16)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_dft_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_17) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) $(am__append_20)
am__celt_tests_test_unit_entropy_SOURCES_DIST =  \
	celt/tests/test_unit_entropy.c
@EXTRA_PROGRAMS_TRUE@am_celt_tests_test_unit_entropy_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_entropy.$(OBJEXT)
celt_tests_test_unit_entropy_OBJECTS =  \
	$(am_celt_tests_test_unit_entropy_OBJECTS)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_entropy_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__celt_tests_test_unit_laplace_SOURCES_DIST =  \
	celt/tests/test_unit_laplace.c
@EXTRA_PROGRAMS_TRUE@am_celt_tests_test_unit_laplace_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_laplace.$(OBJEXT)
celt_tests_test_unit_laplace_OBJECTS =  \
	$(am_celt_tests_test_unit_laplace_OBJECTS)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_laplace_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__celt_tests_test_unit_mathops_SOURCES_DIST =  \
	celt/tests/test_unit_mathops.c
@EXTRA_PROGRAMS_TRUE@am_celt_tests_test_unit_mathops_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_mathops.$(OBJEXT)
celt_tests_test_unit_mathops_OBJECTS =  \
	$(am_celt_tests_test_unit_mathops_OBJECTS)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_mathops_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_17) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) $(am__append_21)
am__celt_tests_test_unit_mdct_SOURCES_DIST =  \
	celt/tests/test_unit_mdct.c
@EXTRA_PROGRAMS_TRUE@am_celt_tests_test_unit_mdct_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_mdct.$(OBJEXT)
celt_tests_test_unit_mdct_OBJECTS =  \
	$(am_celt_tests_test_unit_mdct_OBJECTS)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_mdct_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_17) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) $(am__append_22)
am__celt_tests_test_unit_rotation_SOURCES_DIST =  \
	celt/tests/test_unit_rotation.c
@EXTRA_PROGRAMS_TRUE@am_celt_tests_test_unit_rotation_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_rotation.$(OBJEXT)
celt_tests_test_unit_rotation_OBJECTS =  \
	$(am_celt_tests_test_unit_rotation_OBJECTS)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_rotation_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_17) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) $(am__append_23)
am__celt_tests_test_unit_types_SOURCES_DIST =  \
	celt/tests/test_unit_types.c
@EXTRA_PROGRAMS_TRUE@am_celt_tests_test_unit_types_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	celt/tests/test_unit_types.$(OBJEXT)
celt_tests_test_unit_types_OBJECTS =  \
	$(am_celt_tests_test_unit_types_OBJECTS)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_types_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__opus_compare_SOURCES_DIST = src/opus_compare.c
@EXTRA_PROGRAMS_TRUE@am_opus_compare_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	src/opus_compare.$(OBJEXT)
opus_compare_OBJECTS = $(am_opus_compare_OBJECTS)
@EXTRA_PROGRAMS_TRUE@opus_compare_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__opus_custom_demo_SOURCES_DIST = celt/opus_custom_demo.c
@CUSTOM_MODES_TRUE@@EXTRA_PROGRAMS_TRUE@am_opus_custom_demo_OBJECTS = celt/opus_custom_demo.$(OBJEXT)
opus_custom_demo_OBJECTS = $(am_opus_custom_demo_OBJECTS)
@CUSTOM_MODES_TRUE@@EXTRA_PROGRAMS_TRUE@opus_custom_demo_DEPENDENCIES =  \
@CUSTOM_MODES_TRUE@@EXTRA_PROGRAMS_TRUE@	libopus.la \
@CUSTOM_MODES_TRUE@@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__opus_demo_SOURCES_DIST = src/opus_demo.c
@EXTRA_PROGRAMS_TRUE@am_opus_demo_OBJECTS = src/opus_demo.$(OBJEXT)
opus_demo_OBJECTS = $(am_opus_demo_OBJECTS)
@EXTRA_PROGRAMS_TRUE@opus_demo_DEPENDENCIES = libopus.la \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__repacketizer_demo_SOURCES_DIST = src/repacketizer_demo.c
@EXTRA_PROGRAMS_TRUE@am_repacketizer_demo_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	src/repacketizer_demo.$(OBJEXT)
repacketizer_demo_OBJECTS = $(am_repacketizer_demo_OBJECTS)
@EXTRA_PROGRAMS_TRUE@repacketizer_demo_DEPENDENCIES = libopus.la \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__silk_tests_test_unit_LPC_inv_pred_gain_SOURCES_DIST =  \
	silk/tests/test_unit_LPC_inv_pred_gain.c
@EXTRA_PROGRAMS_TRUE@am_silk_tests_test_unit_LPC_inv_pred_gain_OBJECTS = silk/tests/test_unit_LPC_inv_pred_gain.$(OBJEXT)
silk_tests_test_unit_LPC_inv_pred_gain_OBJECTS =  \
	$(am_silk_tests_test_unit_LPC_inv_pred_gain_OBJECTS)
am__DEPENDENCIES_18 = silk/fixed/LTP_analysis_filter_FIX.lo \
	silk/fixed/LTP_scale_ctrl_FIX.lo silk/fixed/corrMatrix_FIX.lo \
	silk/fixed/encode_frame_FIX.lo silk/fixed/find_LPC_FIX.lo \
	silk/fixed/find_LTP_FIX.lo silk/fixed/find_pitch_lags_FIX.lo \
	silk/fixed/find_pred_coefs_FIX.lo \
	silk/fixed/noise_shape_analysis_FIX.lo \
	silk/fixed/process_gains_FIX.lo \
	silk/fixed/regularize_correlations_FIX.lo \
	silk/fixed/residual_energy16_FIX.lo \
	silk/fixed/residual_energy_FIX.lo \
	silk/fixed/warped_autocorrelation_FIX.lo \
	silk/fixed/apply_sine_window_FIX.lo silk/fixed/autocorr_FIX.lo \
	silk/fixed/burg_modified_FIX.lo silk/fixed/k2a_FIX.lo \
	silk/fixed/k2a_Q16_FIX.lo \
	silk/fixed/pitch_analysis_core_FIX.lo \
	silk/fixed/vector_ops_FIX.lo silk/fixed/schur64_FIX.lo \
	silk/fixed/schur_FIX.lo
@FIXED_POINT_TRUE@am__DEPENDENCIES_19 = $(am__DEPENDENCIES_18)
am__DEPENDENCIES_20 = silk/x86/NSQ_sse4_1.lo \
	silk/x86/NSQ_del_dec_sse4_1.lo silk/x86/VAD_sse4_1.lo \
	silk/x86/VQ_WMat_EC_sse4_1.lo
am__DEPENDENCIES_21 = silk/fixed/x86/vector_ops_FIX_sse4_1.lo \
	silk/fixed/x86/burg_modified_FIX_sse4_1.lo
@FIXED_POINT_TRUE@@HAVE_SSE4_1_TRUE@am__DEPENDENCIES_22 =  \
@FIXED_POINT_TRUE@@HAVE_SSE4_1_TRUE@	$(am__DEPENDENCIES_20) \
@FIXED_POINT_TRUE@@HAVE_SSE4_1_TRUE@	$(am__DEPENDENCIES_21)
am__DEPENDENCIES_23 =  \
	silk/fixed/arm/warped_autocorrelation_FIX_neon_intr.lo
@FIXED_POINT_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__DEPENDENCIES_24 = $(am__DEPENDENCIES_23)
am__DEPENDENCIES_25 = silk/float/apply_sine_window_FLP.lo \
	silk/float/corrMatrix_FLP.lo silk/float/encode_frame_FLP.lo \
	silk/float/find_LPC_FLP.lo silk/float/find_LTP_FLP.lo \
	silk/float/find_pitch_lags_FLP.lo \
	silk/float/find_pred_coefs_FLP.lo \
	silk/float/LPC_analysis_filter_FLP.lo \
	silk/float/LTP_analysis_filter_FLP.lo \
	silk/float/LTP_scale_ctrl_FLP.lo \
	silk/float/noise_shape_analysis_FLP.lo \
	silk/float/process_gains_FLP.lo \
	silk/float/regularize_correlations_FLP.lo \
	silk/float/residual_energy_FLP.lo \
	silk/float/warped_autocorrelation_FLP.lo \
	silk/float/wrappers_FLP.lo silk/float/autocorrelation_FLP.lo \
	silk/float/burg_modified_FLP.lo silk/float/bwexpander_FLP.lo \
	silk/float/energy_FLP.lo silk/float/inner_product_FLP.lo \
	silk/float/k2a_FLP.lo silk/float/LPC_inv_pred_gain_FLP.lo \
	silk/float/pitch_analysis_core_FLP.lo \
	silk/float/scale_copy_vector_FLP.lo \
	silk/float/scale_vector_FLP.lo silk/float/schur_FLP.lo \
	silk/float/sort_FLP.lo
@FIXED_POINT_FALSE@am__DEPENDENCIES_26 = $(am__DEPENDENCIES_25)
@FIXED_POINT_FALSE@@HAVE_SSE4_1_TRUE@am__DEPENDENCIES_27 =  \
@FIXED_POINT_FALSE@@HAVE_SSE4_1_TRUE@	$(am__DEPENDENCIES_20)
am__DEPENDENCIES_28 = silk/x86/x86_silk_map.lo
@CPU_X86_TRUE@@HAVE_RTCD_TRUE@am__DEPENDENCIES_29 =  \
@CPU_X86_TRUE@@HAVE_RTCD_TRUE@	$(am__DEPENDENCIES_28)
am__DEPENDENCIES_30 = silk/arm/arm_silk_map.lo
@CPU_ARM_TRUE@@HAVE_RTCD_TRUE@am__DEPENDENCIES_31 =  \
@CPU_ARM_TRUE@@HAVE_RTCD_TRUE@	$(am__DEPENDENCIES_30)
am__DEPENDENCIES_32 = silk/arm/biquad_alt_neon_intr.lo \
	silk/arm/LPC_inv_pred_gain_neon_intr.lo \
	silk/arm/NSQ_del_dec_neon_intr.lo silk/arm/NSQ_neon.lo
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@am__DEPENDENCIES_33 =  \
@CPU_ARM_TRUE@@HAVE_ARM_NEON_INTR_TRUE@	$(am__DEPENDENCIES_32)
am__DEPENDENCIES_34 = silk/CNG.lo silk/code_signs.lo \
	silk/init_decoder.lo silk/decode_core.lo silk/decode_frame.lo \
	silk/decode_parameters.lo silk/decode_indices.lo \
	silk/decode_pulses.lo silk/decoder_set_fs.lo silk/dec_API.lo \
	silk/enc_API.lo silk/encode_indices.lo silk/encode_pulses.lo \
	silk/gain_quant.lo silk/interpolate.lo \
	silk/LP_variable_cutoff.lo silk/NLSF_decode.lo silk/NSQ.lo \
	silk/NSQ_del_dec.lo silk/PLC.lo silk/shell_coder.lo \
	silk/tables_gain.lo silk/tables_LTP.lo \
	silk/tables_NLSF_CB_NB_MB.lo silk/tables_NLSF_CB_WB.lo \
	silk/tables_other.lo silk/tables_pitch_lag.lo \
	silk/tables_pulses_per_block.lo silk/VAD.lo \
	silk/control_audio_bandwidth.lo silk/quant_LTP_gains.lo \
	silk/VQ_WMat_EC.lo silk/HP_variable_cutoff.lo \
	silk/NLSF_encode.lo silk/NLSF_VQ.lo silk/NLSF_unpack.lo \
	silk/NLSF_del_dec_quant.lo silk/process_NLSFs.lo \
	silk/stereo_LR_to_MS.lo silk/stereo_MS_to_LR.lo \
	silk/check_control_input.lo silk/control_SNR.lo \
	silk/init_encoder.lo silk/control_codec.lo silk/A2NLSF.lo \
	silk/ana_filt_bank_1.lo silk/biquad_alt.lo \
	silk/bwexpander_32.lo silk/bwexpander.lo silk/debug.lo \
	silk/decode_pitch.lo silk/inner_prod_aligned.lo \
	silk/lin2log.lo silk/log2lin.lo silk/LPC_analysis_filter.lo \
	silk/LPC_inv_pred_gain.lo silk/table_LSF_cos.lo silk/NLSF2A.lo \
	silk/NLSF_stabilize.lo silk/NLSF_VQ_weights_laroia.lo \
	silk/pitch_est_tables.lo silk/resampler.lo \
	silk/resampler_down2_3.lo silk/resampler_down2.lo \
	silk/resampler_private_AR2.lo \
	silk/resampler_private_down_FIR.lo \
	silk/resampler_private_IIR_FIR.lo \
	silk/resampler_private_up2_HQ.lo silk/resampler_rom.lo \
	silk/sigm_Q15.lo silk/sort.lo silk/sum_sqr_shift.lo \
	silk/stereo_decode_pred.lo silk/stereo_encode_pred.lo \
	silk/stereo_find_predictor.lo silk/stereo_quant_pred.lo \
	silk/LPC_fit.lo $(am__DEPENDENCIES_19) $(am__DEPENDENCIES_22) \
	$(am__DEPENDENCIES_24) $(am__DEPENDENCIES_26) \
	$(am__DEPENDENCIES_27) $(am__DEPENDENCIES_29) \
	$(am__DEPENDENCIES_31) $(am__DEPENDENCIES_33)
@EXTRA_PROGRAMS_TRUE@am__DEPENDENCIES_35 = $(am__DEPENDENCIES_34)
@EXTRA_PROGRAMS_TRUE@silk_tests_test_unit_LPC_inv_pred_gain_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_35) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_17) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) $(am__append_19)
am__tests_test_opus_api_SOURCES_DIST = tests/test_opus_api.c \
	tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@am_tests_test_opus_api_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_api.$(OBJEXT)
tests_test_opus_api_OBJECTS = $(am_tests_test_opus_api_OBJECTS)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_api_DEPENDENCIES = libopus.la \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__tests_test_opus_decode_SOURCES_DIST = tests/test_opus_decode.c \
	tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@am_tests_test_opus_decode_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_decode.$(OBJEXT)
tests_test_opus_decode_OBJECTS = $(am_tests_test_opus_decode_OBJECTS)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_decode_DEPENDENCIES = libopus.la \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__tests_test_opus_encode_SOURCES_DIST = tests/test_opus_encode.c \
	tests/opus_encode_regressions.c tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@am_tests_test_opus_encode_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_encode.$(OBJEXT) \
@EXTRA_PROGRAMS_TRUE@	tests/opus_encode_regressions.$(OBJEXT)
tests_test_opus_encode_OBJECTS = $(am_tests_test_opus_encode_OBJECTS)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_encode_DEPENDENCIES = libopus.la \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__tests_test_opus_padding_SOURCES_DIST = tests/test_opus_padding.c \
	tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@am_tests_test_opus_padding_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_padding.$(OBJEXT)
tests_test_opus_padding_OBJECTS =  \
	$(am_tests_test_opus_padding_OBJECTS)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_padding_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	libopus.la $(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
am__tests_test_opus_projection_SOURCES_DIST =  \
	tests/test_opus_projection.c tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@am_tests_test_opus_projection_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	tests/test_opus_projection.$(OBJEXT)
tests_test_opus_projection_OBJECTS =  \
	$(am_tests_test_opus_projection_OBJECTS)
am__DEPENDENCIES_36 = src/analysis.lo src/mlp.lo src/mlp_data.lo
@DISABLE_FLOAT_API_FALSE@am__DEPENDENCIES_37 = $(am__DEPENDENCIES_36)
am__DEPENDENCIES_38 = src/opus.lo src/opus_decoder.lo \
	src/opus_encoder.lo src/opus_multistream.lo \
	src/opus_multistream_encoder.lo \
	src/opus_multistream_decoder.lo src/repacketizer.lo \
	src/opus_projection_encoder.lo src/opus_projection_decoder.lo \
	src/mapping_matrix.lo $(am__DEPENDENCIES_37)
@EXTRA_PROGRAMS_TRUE@am__DEPENDENCIES_39 = $(am__DEPENDENCIES_38)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_projection_DEPENDENCIES =  \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_39) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_35) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_17) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1) $(am__append_18)
am__trivial_example_SOURCES_DIST = doc/trivial_example.c
@EXTRA_PROGRAMS_TRUE@am_trivial_example_OBJECTS =  \
@EXTRA_PROGRAMS_TRUE@	doc/trivial_example.$(OBJEXT)
trivial_example_OBJECTS = $(am_trivial_example_OBJECTS)
@EXTRA_PROGRAMS_TRUE@trivial_example_DEPENDENCIES = libopus.la \
@EXTRA_PROGRAMS_TRUE@	$(am__DEPENDENCIES_1)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = celt/$(DEPDIR)/bands.Plo celt/$(DEPDIR)/celt.Plo \
	celt/$(DEPDIR)/celt_decoder.Plo \
	celt/$(DEPDIR)/celt_encoder.Plo celt/$(DEPDIR)/celt_lpc.Plo \
	celt/$(DEPDIR)/cwrs.Plo celt/$(DEPDIR)/entcode.Plo \
	celt/$(DEPDIR)/entdec.Plo celt/$(DEPDIR)/entenc.Plo \
	celt/$(DEPDIR)/kiss_fft.Plo celt/$(DEPDIR)/laplace.Plo \
	celt/$(DEPDIR)/mathops.Plo celt/$(DEPDIR)/mdct.Plo \
	celt/$(DEPDIR)/modes.Plo celt/$(DEPDIR)/opus_custom_demo.Po \
	celt/$(DEPDIR)/pitch.Plo celt/$(DEPDIR)/quant_bands.Plo \
	celt/$(DEPDIR)/rate.Plo celt/$(DEPDIR)/vq.Plo \
	celt/arm/$(DEPDIR)/arm_celt_map.Plo \
	celt/arm/$(DEPDIR)/armcpu.Plo \
	celt/arm/$(DEPDIR)/celt_fft_ne10.Plo \
	celt/arm/$(DEPDIR)/celt_mdct_ne10.Plo \
	celt/arm/$(DEPDIR)/celt_neon_intr.Plo \
	celt/arm/$(DEPDIR)/celt_pitch_xcorr_arm-gnu.Plo \
	celt/arm/$(DEPDIR)/pitch_neon_intr.Plo \
	celt/tests/$(DEPDIR)/test_unit_cwrs32.Po \
	celt/tests/$(DEPDIR)/test_unit_dft.Po \
	celt/tests/$(DEPDIR)/test_unit_entropy.Po \
	celt/tests/$(DEPDIR)/test_unit_laplace.Po \
	celt/tests/$(DEPDIR)/test_unit_mathops.Po \
	celt/tests/$(DEPDIR)/test_unit_mdct.Po \
	celt/tests/$(DEPDIR)/test_unit_rotation.Po \
	celt/tests/$(DEPDIR)/test_unit_types.Po \
	celt/x86/$(DEPDIR)/celt_lpc_sse4_1.Plo \
	celt/x86/$(DEPDIR)/pitch_sse.Plo \
	celt/x86/$(DEPDIR)/pitch_sse2.Plo \
	celt/x86/$(DEPDIR)/pitch_sse4_1.Plo \
	celt/x86/$(DEPDIR)/vq_sse2.Plo \
	celt/x86/$(DEPDIR)/x86_celt_map.Plo \
	celt/x86/$(DEPDIR)/x86cpu.Plo doc/$(DEPDIR)/trivial_example.Po \
	silk/$(DEPDIR)/A2NLSF.Plo silk/$(DEPDIR)/CNG.Plo \
	silk/$(DEPDIR)/HP_variable_cutoff.Plo \
	silk/$(DEPDIR)/LPC_analysis_filter.Plo \
	silk/$(DEPDIR)/LPC_fit.Plo \
	silk/$(DEPDIR)/LPC_inv_pred_gain.Plo \
	silk/$(DEPDIR)/LP_variable_cutoff.Plo \
	silk/$(DEPDIR)/NLSF2A.Plo silk/$(DEPDIR)/NLSF_VQ.Plo \
	silk/$(DEPDIR)/NLSF_VQ_weights_laroia.Plo \
	silk/$(DEPDIR)/NLSF_decode.Plo \
	silk/$(DEPDIR)/NLSF_del_dec_quant.Plo \
	silk/$(DEPDIR)/NLSF_encode.Plo \
	silk/$(DEPDIR)/NLSF_stabilize.Plo \
	silk/$(DEPDIR)/NLSF_unpack.Plo silk/$(DEPDIR)/NSQ.Plo \
	silk/$(DEPDIR)/NSQ_del_dec.Plo silk/$(DEPDIR)/PLC.Plo \
	silk/$(DEPDIR)/VAD.Plo silk/$(DEPDIR)/VQ_WMat_EC.Plo \
	silk/$(DEPDIR)/ana_filt_bank_1.Plo \
	silk/$(DEPDIR)/biquad_alt.Plo silk/$(DEPDIR)/bwexpander.Plo \
	silk/$(DEPDIR)/bwexpander_32.Plo \
	silk/$(DEPDIR)/check_control_input.Plo \
	silk/$(DEPDIR)/code_signs.Plo silk/$(DEPDIR)/control_SNR.Plo \
	silk/$(DEPDIR)/control_audio_bandwidth.Plo \
	silk/$(DEPDIR)/control_codec.Plo silk/$(DEPDIR)/debug.Plo \
	silk/$(DEPDIR)/dec_API.Plo silk/$(DEPDIR)/decode_core.Plo \
	silk/$(DEPDIR)/decode_frame.Plo \
	silk/$(DEPDIR)/decode_indices.Plo \
	silk/$(DEPDIR)/decode_parameters.Plo \
	silk/$(DEPDIR)/decode_pitch.Plo \
	silk/$(DEPDIR)/decode_pulses.Plo \
	silk/$(DEPDIR)/decoder_set_fs.Plo silk/$(DEPDIR)/enc_API.Plo \
	silk/$(DEPDIR)/encode_indices.Plo \
	silk/$(DEPDIR)/encode_pulses.Plo silk/$(DEPDIR)/gain_quant.Plo \
	silk/$(DEPDIR)/init_decoder.Plo \
	silk/$(DEPDIR)/init_encoder.Plo \
	silk/$(DEPDIR)/inner_prod_aligned.Plo \
	silk/$(DEPDIR)/interpolate.Plo silk/$(DEPDIR)/lin2log.Plo \
	silk/$(DEPDIR)/log2lin.Plo silk/$(DEPDIR)/pitch_est_tables.Plo \
	silk/$(DEPDIR)/process_NLSFs.Plo \
	silk/$(DEPDIR)/quant_LTP_gains.Plo \
	silk/$(DEPDIR)/resampler.Plo \
	silk/$(DEPDIR)/resampler_down2.Plo \
	silk/$(DEPDIR)/resampler_down2_3.Plo \
	silk/$(DEPDIR)/resampler_private_AR2.Plo \
	silk/$(DEPDIR)/resampler_private_IIR_FIR.Plo \
	silk/$(DEPDIR)/resampler_private_down_FIR.Plo \
	silk/$(DEPDIR)/resampler_private_up2_HQ.Plo \
	silk/$(DEPDIR)/resampler_rom.Plo \
	silk/$(DEPDIR)/shell_coder.Plo silk/$(DEPDIR)/sigm_Q15.Plo \
	silk/$(DEPDIR)/sort.Plo silk/$(DEPDIR)/stereo_LR_to_MS.Plo \
	silk/$(DEPDIR)/stereo_MS_to_LR.Plo \
	silk/$(DEPDIR)/stereo_decode_pred.Plo \
	silk/$(DEPDIR)/stereo_encode_pred.Plo \
	silk/$(DEPDIR)/stereo_find_predictor.Plo \
	silk/$(DEPDIR)/stereo_quant_pred.Plo \
	silk/$(DEPDIR)/sum_sqr_shift.Plo \
	silk/$(DEPDIR)/table_LSF_cos.Plo silk/$(DEPDIR)/tables_LTP.Plo \
	silk/$(DEPDIR)/tables_NLSF_CB_NB_MB.Plo \
	silk/$(DEPDIR)/tables_NLSF_CB_WB.Plo \
	silk/$(DEPDIR)/tables_gain.Plo silk/$(DEPDIR)/tables_other.Plo \
	silk/$(DEPDIR)/tables_pitch_lag.Plo \
	silk/$(DEPDIR)/tables_pulses_per_block.Plo \
	silk/arm/$(DEPDIR)/LPC_inv_pred_gain_neon_intr.Plo \
	silk/arm/$(DEPDIR)/NSQ_del_dec_neon_intr.Plo \
	silk/arm/$(DEPDIR)/NSQ_neon.Plo \
	silk/arm/$(DEPDIR)/arm_silk_map.Plo \
	silk/arm/$(DEPDIR)/biquad_alt_neon_intr.Plo \
	silk/fixed/$(DEPDIR)/LTP_analysis_filter_FIX.Plo \
	silk/fixed/$(DEPDIR)/LTP_scale_ctrl_FIX.Plo \
	silk/fixed/$(DEPDIR)/apply_sine_window_FIX.Plo \
	silk/fixed/$(DEPDIR)/autocorr_FIX.Plo \
	silk/fixed/$(DEPDIR)/burg_modified_FIX.Plo \
	silk/fixed/$(DEPDIR)/corrMatrix_FIX.Plo \
	silk/fixed/$(DEPDIR)/encode_frame_FIX.Plo \
	silk/fixed/$(DEPDIR)/find_LPC_FIX.Plo \
	silk/fixed/$(DEPDIR)/find_LTP_FIX.Plo \
	silk/fixed/$(DEPDIR)/find_pitch_lags_FIX.Plo \
	silk/fixed/$(DEPDIR)/find_pred_coefs_FIX.Plo \
	silk/fixed/$(DEPDIR)/k2a_FIX.Plo \
	silk/fixed/$(DEPDIR)/k2a_Q16_FIX.Plo \
	silk/fixed/$(DEPDIR)/noise_shape_analysis_FIX.Plo \
	silk/fixed/$(DEPDIR)/pitch_analysis_core_FIX.Plo \
	silk/fixed/$(DEPDIR)/process_gains_FIX.Plo \
	silk/fixed/$(DEPDIR)/regularize_correlations_FIX.Plo \
	silk/fixed/$(DEPDIR)/residual_energy16_FIX.Plo \
	silk/fixed/$(DEPDIR)/residual_energy_FIX.Plo \
	silk/fixed/$(DEPDIR)/schur64_FIX.Plo \
	silk/fixed/$(DEPDIR)/schur_FIX.Plo \
	silk/fixed/$(DEPDIR)/vector_ops_FIX.Plo \
	silk/fixed/$(DEPDIR)/warped_autocorrelation_FIX.Plo \
	silk/fixed/arm/$(DEPDIR)/warped_autocorrelation_FIX_neon_intr.Plo \
	silk/fixed/x86/$(DEPDIR)/burg_modified_FIX_sse4_1.Plo \
	silk/fixed/x86/$(DEPDIR)/vector_ops_FIX_sse4_1.Plo \
	silk/float/$(DEPDIR)/LPC_analysis_filter_FLP.Plo \
	silk/float/$(DEPDIR)/LPC_inv_pred_gain_FLP.Plo \
	silk/float/$(DEPDIR)/LTP_analysis_filter_FLP.Plo \
	silk/float/$(DEPDIR)/LTP_scale_ctrl_FLP.Plo \
	silk/float/$(DEPDIR)/apply_sine_window_FLP.Plo \
	silk/float/$(DEPDIR)/autocorrelation_FLP.Plo \
	silk/float/$(DEPDIR)/burg_modified_FLP.Plo \
	silk/float/$(DEPDIR)/bwexpander_FLP.Plo \
	silk/float/$(DEPDIR)/corrMatrix_FLP.Plo \
	silk/float/$(DEPDIR)/encode_frame_FLP.Plo \
	silk/float/$(DEPDIR)/energy_FLP.Plo \
	silk/float/$(DEPDIR)/find_LPC_FLP.Plo \
	silk/float/$(DEPDIR)/find_LTP_FLP.Plo \
	silk/float/$(DEPDIR)/find_pitch_lags_FLP.Plo \
	silk/float/$(DEPDIR)/find_pred_coefs_FLP.Plo \
	silk/float/$(DEPDIR)/inner_product_FLP.Plo \
	silk/float/$(DEPDIR)/k2a_FLP.Plo \
	silk/float/$(DEPDIR)/noise_shape_analysis_FLP.Plo \
	silk/float/$(DEPDIR)/pitch_analysis_core_FLP.Plo \
	silk/float/$(DEPDIR)/process_gains_FLP.Plo \
	silk/float/$(DEPDIR)/regularize_correlations_FLP.Plo \
	silk/float/$(DEPDIR)/residual_energy_FLP.Plo \
	silk/float/$(DEPDIR)/scale_copy_vector_FLP.Plo \
	silk/float/$(DEPDIR)/scale_vector_FLP.Plo \
	silk/float/$(DEPDIR)/schur_FLP.Plo \
	silk/float/$(DEPDIR)/sort_FLP.Plo \
	silk/float/$(DEPDIR)/warped_autocorrelation_FLP.Plo \
	silk/float/$(DEPDIR)/wrappers_FLP.Plo \
	silk/tests/$(DEPDIR)/test_unit_LPC_inv_pred_gain.Po \
	silk/x86/$(DEPDIR)/NSQ_del_dec_sse4_1.Plo \
	silk/x86/$(DEPDIR)/NSQ_sse4_1.Plo \
	silk/x86/$(DEPDIR)/VAD_sse4_1.Plo \
	silk/x86/$(DEPDIR)/VQ_WMat_EC_sse4_1.Plo \
	silk/x86/$(DEPDIR)/x86_silk_map.Plo src/$(DEPDIR)/analysis.Plo \
	src/$(DEPDIR)/mapping_matrix.Plo src/$(DEPDIR)/mlp.Plo \
	src/$(DEPDIR)/mlp_data.Plo src/$(DEPDIR)/opus.Plo \
	src/$(DEPDIR)/opus_compare.Po src/$(DEPDIR)/opus_decoder.Plo \
	src/$(DEPDIR)/opus_demo.Po src/$(DEPDIR)/opus_encoder.Plo \
	src/$(DEPDIR)/opus_multistream.Plo \
	src/$(DEPDIR)/opus_multistream_decoder.Plo \
	src/$(DEPDIR)/opus_multistream_encoder.Plo \
	src/$(DEPDIR)/opus_projection_decoder.Plo \
	src/$(DEPDIR)/opus_projection_encoder.Plo \
	src/$(DEPDIR)/repacketizer.Plo \
	src/$(DEPDIR)/repacketizer_demo.Po \
	tests/$(DEPDIR)/opus_encode_regressions.Po \
	tests/$(DEPDIR)/test_opus_api.Po \
	tests/$(DEPDIR)/test_opus_decode.Po \
	tests/$(DEPDIR)/test_opus_encode.Po \
	tests/$(DEPDIR)/test_opus_padding.Po \
	tests/$(DEPDIR)/test_opus_projection.Po
am__mv = mv -f
CPPASCOMPILE = $(CCAS) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CCASFLAGS) $(CCASFLAGS)
LTCPPASCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CCAS) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CCASFLAGS) $(CCASFLAGS)
AM_V_CPPAS = $(am__v_CPPAS_@AM_V@)
am__v_CPPAS_ = $(am__v_CPPAS_@AM_DEFAULT_V@)
am__v_CPPAS_0 = @echo "  CPPAS   " $@;
am__v_CPPAS_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libarmasm_la_SOURCES) $(libopus_la_SOURCES) \
	$(celt_tests_test_unit_cwrs32_SOURCES) \
	$(celt_tests_test_unit_dft_SOURCES) \
	$(celt_tests_test_unit_entropy_SOURCES) \
	$(celt_tests_test_unit_laplace_SOURCES) \
	$(celt_tests_test_unit_mathops_SOURCES) \
	$(celt_tests_test_unit_mdct_SOURCES) \
	$(celt_tests_test_unit_rotation_SOURCES) \
	$(celt_tests_test_unit_types_SOURCES) $(opus_compare_SOURCES) \
	$(opus_custom_demo_SOURCES) $(opus_demo_SOURCES) \
	$(repacketizer_demo_SOURCES) \
	$(silk_tests_test_unit_LPC_inv_pred_gain_SOURCES) \
	$(tests_test_opus_api_SOURCES) \
	$(tests_test_opus_decode_SOURCES) \
	$(tests_test_opus_encode_SOURCES) \
	$(tests_test_opus_padding_SOURCES) \
	$(tests_test_opus_projection_SOURCES) \
	$(trivial_example_SOURCES)
DIST_SOURCES = $(am__libarmasm_la_SOURCES_DIST) \
	$(am__libopus_la_SOURCES_DIST) \
	$(am__celt_tests_test_unit_cwrs32_SOURCES_DIST) \
	$(am__celt_tests_test_unit_dft_SOURCES_DIST) \
	$(am__celt_tests_test_unit_entropy_SOURCES_DIST) \
	$(am__celt_tests_test_unit_laplace_SOURCES_DIST) \
	$(am__celt_tests_test_unit_mathops_SOURCES_DIST) \
	$(am__celt_tests_test_unit_mdct_SOURCES_DIST) \
	$(am__celt_tests_test_unit_rotation_SOURCES_DIST) \
	$(am__celt_tests_test_unit_types_SOURCES_DIST) \
	$(am__opus_compare_SOURCES_DIST) \
	$(am__opus_custom_demo_SOURCES_DIST) \
	$(am__opus_demo_SOURCES_DIST) \
	$(am__repacketizer_demo_SOURCES_DIST) \
	$(am__silk_tests_test_unit_LPC_inv_pred_gain_SOURCES_DIST) \
	$(am__tests_test_opus_api_SOURCES_DIST) \
	$(am__tests_test_opus_decode_SOURCES_DIST) \
	$(am__tests_test_opus_encode_SOURCES_DIST) \
	$(am__tests_test_opus_padding_SOURCES_DIST) \
	$(am__tests_test_opus_projection_SOURCES_DIST) \
	$(am__trivial_example_SOURCES_DIST)
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
DATA = $(m4data_DATA) $(pkgconfig_DATA)
am__pkginclude_HEADERS_DIST = include/opus.h \
	include/opus_multistream.h include/opus_types.h \
	include/opus_defines.h include/opus_projection.h \
	include/opus_custom.h
HEADERS = $(noinst_HEADERS) $(pkginclude_HEADERS)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	cscope check recheck distdir distdir-am dist dist-all \
	distcheck
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) \
	$(LISP)config.h.in
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
CSCOPE = cscope
am__tty_colors_dummy = \
  mgn= red= grn= lgn= blu= brg= std=; \
  am__color_tests=no
am__tty_colors = { \
  $(am__tty_colors_dummy); \
  if test "X$(AM_COLOR_TESTS)" = Xno; then \
    am__color_tests=no; \
  elif test "X$(AM_COLOR_TESTS)" = Xalways; then \
    am__color_tests=yes; \
  elif test "X$$TERM" != Xdumb && { test -t 1; } 2>/dev/null; then \
    am__color_tests=yes; \
  fi; \
  if test $$am__color_tests = yes; then \
    red='[0;31m'; \
    grn='[0;32m'; \
    lgn='[1;32m'; \
    blu='[1;34m'; \
    mgn='[0;35m'; \
    brg='[1m'; \
    std='[m'; \
  fi; \
}
am__recheck_rx = ^[ 	]*:recheck:[ 	]*
am__global_test_result_rx = ^[ 	]*:global-test-result:[ 	]*
am__copy_in_global_log_rx = ^[ 	]*:copy-in-global-log:[ 	]*
# A command that, given a newline-separated list of test names on the
# standard input, print the name of the tests that are to be re-run
# upon "make recheck".
am__list_recheck_tests = $(AWK) '{ \
  recheck = 1; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
        { \
          if ((getline line2 < ($$0 ".log")) < 0) \
	    recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[nN][Oo]/) \
        { \
          recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[yY][eE][sS]/) \
        { \
          break; \
        } \
    }; \
  if (recheck) \
    print $$0; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# A command that, given a newline-separated list of test names on the
# standard input, create the global log from their .trs and .log files.
am__create_global_log = $(AWK) ' \
function fatal(msg) \
{ \
  print "fatal: making $@: " msg | "cat >&2"; \
  exit 1; \
} \
function rst_section(header) \
{ \
  print header; \
  len = length(header); \
  for (i = 1; i <= len; i = i + 1) \
    printf "="; \
  printf "\n\n"; \
} \
{ \
  copy_in_global_log = 1; \
  global_test_result = "RUN"; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
         fatal("failed to read from " $$0 ".trs"); \
      if (line ~ /$(am__global_test_result_rx)/) \
        { \
          sub("$(am__global_test_result_rx)", "", line); \
          sub("[ 	]*$$", "", line); \
          global_test_result = line; \
        } \
      else if (line ~ /$(am__copy_in_global_log_rx)[nN][oO]/) \
        copy_in_global_log = 0; \
    }; \
  if (copy_in_global_log) \
    { \
      rst_section(global_test_result ": " $$0); \
      while ((rc = (getline line < ($$0 ".log"))) != 0) \
      { \
        if (rc < 0) \
          fatal("failed to read from " $$0 ".log"); \
        print line; \
      }; \
      printf "\n"; \
    }; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# Restructured Text title.
am__rst_title = { sed 's/.*/   &   /;h;s/./=/g;p;x;s/ *$$//;p;g' && echo; }
# Solaris 10 'make', and several other traditional 'make' implementations,
# pass "-e" to $(SHELL), and POSIX 2008 even requires this.  Work around it
# by disabling -e (using the XSI extension "set +e") if it's set.
am__sh_e_setup = case $$- in *e*) set +e;; esac
# Default flags passed to test drivers.
am__common_driver_flags = \
  --color-tests "$$am__color_tests" \
  --enable-hard-errors "$$am__enable_hard_errors" \
  --expect-failure "$$am__expect_failure"
# To be inserted before the command running the test.  Creates the
# directory for the log if needed.  Stores in $dir the directory
# containing $f, in $tst the test, in $log the log.  Executes the
# developer- defined test setup AM_TESTS_ENVIRONMENT (if any), and
# passes TESTS_ENVIRONMENT.  Set up options for the wrapper that
# will run the test scripts (or their associated LOG_COMPILER, if
# thy have one).
am__check_pre = \
$(am__sh_e_setup);					\
$(am__vpath_adj_setup) $(am__vpath_adj)			\
$(am__tty_colors);					\
srcdir=$(srcdir); export srcdir;			\
case "$@" in						\
  */*) am__odir=`echo "./$@" | sed 's|/[^/]*$$||'`;;	\
    *) am__odir=.;; 					\
esac;							\
test "x$$am__odir" = x"." || test -d "$$am__odir" 	\
  || $(MKDIR_P) "$$am__odir" || exit $$?;		\
if test -f "./$$f"; then dir=./;			\
elif test -f "$$f"; then dir=;				\
else dir="$(srcdir)/"; fi;				\
tst=$$dir$$f; log='$@'; 				\
if test -n '$(DISABLE_HARD_ERRORS)'; then		\
  am__enable_hard_errors=no; 				\
else							\
  am__enable_hard_errors=yes; 				\
fi; 							\
case " $(XFAIL_TESTS) " in				\
  *[\ \	]$$f[\ \	]* | *[\ \	]$$dir$$f[\ \	]*) \
    am__expect_failure=yes;;				\
  *)							\
    am__expect_failure=no;;				\
esac; 							\
$(AM_TESTS_ENVIRONMENT) $(TESTS_ENVIRONMENT)
# A shell command to get the names of the tests scripts with any registered
# extension removed (i.e., equivalently, the names of the test logs, with
# the '.log' extension removed).  The result is saved in the shell variable
# '$bases'.  This honors runtime overriding of TESTS and TEST_LOGS.  Sadly,
# we cannot use something simpler, involving e.g., "$(TEST_LOGS:.log=)",
# since that might cause problem with VPATH rewrites for suffix-less tests.
# See also 'test-harness-vpath-rewrite.sh' and 'test-trs-basic.sh'.
am__set_TESTS_bases = \
  bases='$(TEST_LOGS)'; \
  bases=`for i in $$bases; do echo $$i; done | sed 's/\.log$$//'`; \
  bases=`echo $$bases`
RECHECK_LOGS = $(TEST_LOGS)
TEST_SUITE_LOG = test-suite.log
TEST_EXTENSIONS = @EXEEXT@ .test
LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
LOG_COMPILE = $(LOG_COMPILER) $(AM_LOG_FLAGS) $(LOG_FLAGS)
am__set_b = \
  case '$@' in \
    */*) \
      case '$*' in \
        */*) b='$*';; \
          *) b=`echo '$@' | sed 's/\.log$$//'`; \
       esac;; \
    *) \
      b='$*';; \
  esac
am__test_logs1 = $(TESTS:=.log)
am__test_logs2 = $(am__test_logs1:@EXEEXT@.log=.log)
TEST_LOGS = $(am__test_logs2:.test.log=.log)
TEST_LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
TEST_LOG_COMPILE = $(TEST_LOG_COMPILER) $(AM_TEST_LOG_FLAGS) \
	$(TEST_LOG_FLAGS)
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/celt_headers.mk \
	$(srcdir)/celt_sources.mk $(srcdir)/config.h.in \
	$(srcdir)/opus-uninstalled.pc.in $(srcdir)/opus.pc.in \
	$(srcdir)/opus_headers.mk $(srcdir)/opus_sources.mk \
	$(srcdir)/silk_headers.mk $(srcdir)/silk_sources.mk \
	$(top_srcdir)/celt/arm/armopts.s.in AUTHORS COPYING ChangeLog \
	INSTALL NEWS README compile config.guess config.sub depcomp \
	install-sh ltmain.sh missing test-driver
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -200 -exec chmod u+w {} ';' \
      && rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
DIST_ARCHIVES = $(distdir).tar.gz
GZIP_ENV = --best
DIST_TARGETS = dist-gzip
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = find . -type f -print
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
ARM2GNU_PARAMS = @ARM2GNU_PARAMS@
ARM_NEON_INTR_CFLAGS = @ARM_NEON_INTR_CFLAGS@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCAS = @CCAS@
CCASDEPMODE = @CCASDEPMODE@
CCASFLAGS = @CCASFLAGS@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GREP = @GREP@
HAVE_ARM_NE10 = @HAVE_ARM_NE10@
HAVE_DOT = @HAVE_DOT@
HAVE_DOXYGEN = @HAVE_DOXYGEN@
HAVE_PERL = @HAVE_PERL@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBM = @LIBM@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NE10_CFLAGS = @NE10_CFLAGS@
NE10_LIBS = @NE10_LIBS@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OPUS_ARM_MAY_HAVE_EDSP = @OPUS_ARM_MAY_HAVE_EDSP@
OPUS_ARM_MAY_HAVE_MEDIA = @OPUS_ARM_MAY_HAVE_MEDIA@
OPUS_ARM_MAY_HAVE_NEON = @OPUS_ARM_MAY_HAVE_NEON@
OPUS_ARM_NEON_INTR_CFLAGS = @OPUS_ARM_NEON_INTR_CFLAGS@
OPUS_HAVE_RTCD = @OPUS_HAVE_RTCD@
OPUS_LT_AGE = @OPUS_LT_AGE@
OPUS_LT_CURRENT = @OPUS_LT_CURRENT@
OPUS_LT_REVISION = @OPUS_LT_REVISION@
OPUS_X86_AVX_CFLAGS = @OPUS_X86_AVX_CFLAGS@
OPUS_X86_SSE2_CFLAGS = @OPUS_X86_SSE2_CFLAGS@
OPUS_X86_SSE4_1_CFLAGS = @OPUS_X86_SSE4_1_CFLAGS@
OPUS_X86_SSE_CFLAGS = @OPUS_X86_SSE_CFLAGS@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PC_BUILD = @PC_BUILD@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
VERSION = @VERSION@
X86_AVX_CFLAGS = @X86_AVX_CFLAGS@
X86_SSE2_CFLAGS = @X86_SSE2_CFLAGS@
X86_SSE4_1_CFLAGS = @X86_SSE4_1_CFLAGS@
X86_SSE_CFLAGS = @X86_SSE_CFLAGS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
AUTOMAKE_OPTIONS = subdir-objects
ACLOCAL_AMFLAGS = -I m4
lib_LTLIBRARIES = libopus.la
DIST_SUBDIRS = doc
AM_CPPFLAGS = -I$(top_srcdir)/include -I$(top_srcdir)/celt -I$(top_srcdir)/silk \
              -I$(top_srcdir)/silk/float -I$(top_srcdir)/silk/fixed $(NE10_CFLAGS)

CELT_SOURCES = celt/bands.c celt/celt.c celt/celt_encoder.c \
	celt/celt_decoder.c celt/cwrs.c celt/entcode.c celt/entdec.c \
	celt/entenc.c celt/kiss_fft.c celt/laplace.c celt/mathops.c \
	celt/mdct.c celt/modes.c celt/pitch.c celt/celt_lpc.c \
	celt/quant_bands.c celt/rate.c celt/vq.c $(am__append_7) \
	$(am__append_9) $(am__append_10) $(am__append_11) \
	$(am__append_12) $(am__append_14) $(am__append_16)
CELT_SOURCES_X86_RTCD = \
celt/x86/x86cpu.c \
celt/x86/x86_celt_map.c

CELT_SOURCES_SSE = \
celt/x86/pitch_sse.c

CELT_SOURCES_SSE2 = \
celt/x86/pitch_sse2.c \
celt/x86/vq_sse2.c

CELT_SOURCES_SSE4_1 = \
celt/x86/celt_lpc_sse4_1.c \
celt/x86/pitch_sse4_1.c

CELT_SOURCES_ARM_RTCD = \
celt/arm/armcpu.c \
celt/arm/arm_celt_map.c

CELT_SOURCES_ARM_ASM = \
celt/arm/celt_pitch_xcorr_arm.s

CELT_AM_SOURCES_ARM_ASM = \
celt/arm/armopts.s.in

CELT_SOURCES_ARM_NEON_INTR = \
celt/arm/celt_neon_intr.c \
celt/arm/pitch_neon_intr.c

CELT_SOURCES_ARM_NE10 = \
celt/arm/celt_fft_ne10.c \
celt/arm/celt_mdct_ne10.c

SILK_SOURCES = silk/CNG.c silk/code_signs.c silk/init_decoder.c \
	silk/decode_core.c silk/decode_frame.c \
	silk/decode_parameters.c silk/decode_indices.c \
	silk/decode_pulses.c silk/decoder_set_fs.c silk/dec_API.c \
	silk/enc_API.c silk/encode_indices.c silk/encode_pulses.c \
	silk/gain_quant.c silk/interpolate.c silk/LP_variable_cutoff.c \
	silk/NLSF_decode.c silk/NSQ.c silk/NSQ_del_dec.c silk/PLC.c \
	silk/shell_coder.c silk/tables_gain.c silk/tables_LTP.c \
	silk/tables_NLSF_CB_NB_MB.c silk/tables_NLSF_CB_WB.c \
	silk/tables_other.c silk/tables_pitch_lag.c \
	silk/tables_pulses_per_block.c silk/VAD.c \
	silk/control_audio_bandwidth.c silk/quant_LTP_gains.c \
	silk/VQ_WMat_EC.c silk/HP_variable_cutoff.c silk/NLSF_encode.c \
	silk/NLSF_VQ.c silk/NLSF_unpack.c silk/NLSF_del_dec_quant.c \
	silk/process_NLSFs.c silk/stereo_LR_to_MS.c \
	silk/stereo_MS_to_LR.c silk/check_control_input.c \
	silk/control_SNR.c silk/init_encoder.c silk/control_codec.c \
	silk/A2NLSF.c silk/ana_filt_bank_1.c silk/biquad_alt.c \
	silk/bwexpander_32.c silk/bwexpander.c silk/debug.c \
	silk/decode_pitch.c silk/inner_prod_aligned.c silk/lin2log.c \
	silk/log2lin.c silk/LPC_analysis_filter.c \
	silk/LPC_inv_pred_gain.c silk/table_LSF_cos.c silk/NLSF2A.c \
	silk/NLSF_stabilize.c silk/NLSF_VQ_weights_laroia.c \
	silk/pitch_est_tables.c silk/resampler.c \
	silk/resampler_down2_3.c silk/resampler_down2.c \
	silk/resampler_private_AR2.c silk/resampler_private_down_FIR.c \
	silk/resampler_private_IIR_FIR.c \
	silk/resampler_private_up2_HQ.c silk/resampler_rom.c \
	silk/sigm_Q15.c silk/sort.c silk/sum_sqr_shift.c \
	silk/stereo_decode_pred.c silk/stereo_encode_pred.c \
	silk/stereo_find_predictor.c silk/stereo_quant_pred.c \
	silk/LPC_fit.c $(am__append_1) $(am__append_2) $(am__append_3) \
	$(am__append_4) $(am__append_5) $(am__append_8) \
	$(am__append_13) $(am__append_15)
SILK_SOURCES_X86_RTCD = \
silk/x86/x86_silk_map.c

SILK_SOURCES_SSE4_1 = \
silk/x86/NSQ_sse4_1.c \
silk/x86/NSQ_del_dec_sse4_1.c \
silk/x86/VAD_sse4_1.c \
silk/x86/VQ_WMat_EC_sse4_1.c

SILK_SOURCES_ARM_RTCD = \
silk/arm/arm_silk_map.c

SILK_SOURCES_ARM_NEON_INTR = \
silk/arm/biquad_alt_neon_intr.c \
silk/arm/LPC_inv_pred_gain_neon_intr.c \
silk/arm/NSQ_del_dec_neon_intr.c \
silk/arm/NSQ_neon.c

SILK_SOURCES_FIXED = \
silk/fixed/LTP_analysis_filter_FIX.c \
silk/fixed/LTP_scale_ctrl_FIX.c \
silk/fixed/corrMatrix_FIX.c \
silk/fixed/encode_frame_FIX.c \
silk/fixed/find_LPC_FIX.c \
silk/fixed/find_LTP_FIX.c \
silk/fixed/find_pitch_lags_FIX.c \
silk/fixed/find_pred_coefs_FIX.c \
silk/fixed/noise_shape_analysis_FIX.c \
silk/fixed/process_gains_FIX.c \
silk/fixed/regularize_correlations_FIX.c \
silk/fixed/residual_energy16_FIX.c \
silk/fixed/residual_energy_FIX.c \
silk/fixed/warped_autocorrelation_FIX.c \
silk/fixed/apply_sine_window_FIX.c \
silk/fixed/autocorr_FIX.c \
silk/fixed/burg_modified_FIX.c \
silk/fixed/k2a_FIX.c \
silk/fixed/k2a_Q16_FIX.c \
silk/fixed/pitch_analysis_core_FIX.c \
silk/fixed/vector_ops_FIX.c \
silk/fixed/schur64_FIX.c \
silk/fixed/schur_FIX.c

SILK_SOURCES_FIXED_SSE4_1 = \
silk/fixed/x86/vector_ops_FIX_sse4_1.c \
silk/fixed/x86/burg_modified_FIX_sse4_1.c

SILK_SOURCES_FIXED_ARM_NEON_INTR = \
silk/fixed/arm/warped_autocorrelation_FIX_neon_intr.c

SILK_SOURCES_FLOAT = \
silk/float/apply_sine_window_FLP.c \
silk/float/corrMatrix_FLP.c \
silk/float/encode_frame_FLP.c \
silk/float/find_LPC_FLP.c \
silk/float/find_LTP_FLP.c \
silk/float/find_pitch_lags_FLP.c \
silk/float/find_pred_coefs_FLP.c \
silk/float/LPC_analysis_filter_FLP.c \
silk/float/LTP_analysis_filter_FLP.c \
silk/float/LTP_scale_ctrl_FLP.c \
silk/float/noise_shape_analysis_FLP.c \
silk/float/process_gains_FLP.c \
silk/float/regularize_correlations_FLP.c \
silk/float/residual_energy_FLP.c \
silk/float/warped_autocorrelation_FLP.c \
silk/float/wrappers_FLP.c \
silk/float/autocorrelation_FLP.c \
silk/float/burg_modified_FLP.c \
silk/float/bwexpander_FLP.c \
silk/float/energy_FLP.c \
silk/float/inner_product_FLP.c \
silk/float/k2a_FLP.c \
silk/float/LPC_inv_pred_gain_FLP.c \
silk/float/pitch_analysis_core_FLP.c \
silk/float/scale_copy_vector_FLP.c \
silk/float/scale_vector_FLP.c \
silk/float/schur_FLP.c \
silk/float/sort_FLP.c

OPUS_SOURCES = src/opus.c src/opus_decoder.c src/opus_encoder.c \
	src/opus_multistream.c src/opus_multistream_encoder.c \
	src/opus_multistream_decoder.c src/repacketizer.c \
	src/opus_projection_encoder.c src/opus_projection_decoder.c \
	src/mapping_matrix.c $(am__append_6)
OPUS_SOURCES_FLOAT = \
src/analysis.c \
src/mlp.c \
src/mlp_data.c

@CPU_ARM_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@noinst_LTLIBRARIES = libarmasm.la
@CPU_ARM_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@libarmasm_la_SOURCES = $(CELT_SOURCES_ARM_ASM:.s=-gnu.S)
@CPU_ARM_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@BUILT_SOURCES = $(CELT_SOURCES_ARM_ASM:.s=-gnu.S) \
@CPU_ARM_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@ $(CELT_AM_SOURCES_ARM_ASM:.s.in=.s) \
@CPU_ARM_TRUE@@OPUS_ARM_EXTERNAL_ASM_TRUE@ $(CELT_AM_SOURCES_ARM_ASM:.s.in=-gnu.S)

CLEANFILES = $(CELT_SOURCES_ARM_ASM:.s=-gnu.S) \
 $(CELT_AM_SOURCES_ARM_ASM:.s.in=-gnu.S)

CELT_HEAD = \
celt/arch.h \
celt/bands.h \
celt/celt.h \
celt/cpu_support.h \
include/opus_types.h \
include/opus_defines.h \
include/opus_custom.h \
celt/cwrs.h \
celt/ecintrin.h \
celt/entcode.h \
celt/entdec.h \
celt/entenc.h \
celt/fixed_debug.h \
celt/fixed_generic.h \
celt/float_cast.h \
celt/_kiss_fft_guts.h \
celt/kiss_fft.h \
celt/laplace.h \
celt/mathops.h \
celt/mdct.h \
celt/mfrngcod.h \
celt/modes.h \
celt/os_support.h \
celt/pitch.h \
celt/celt_lpc.h \
celt/x86/celt_lpc_sse.h \
celt/quant_bands.h \
celt/rate.h \
celt/stack_alloc.h \
celt/vq.h \
celt/static_modes_float.h \
celt/static_modes_fixed.h \
celt/static_modes_float_arm_ne10.h \
celt/static_modes_fixed_arm_ne10.h \
celt/arm/armcpu.h \
celt/arm/fixed_armv4.h \
celt/arm/fixed_armv5e.h \
celt/arm/fixed_arm64.h \
celt/arm/kiss_fft_armv4.h \
celt/arm/kiss_fft_armv5e.h \
celt/arm/pitch_arm.h \
celt/arm/fft_arm.h \
celt/arm/mdct_arm.h \
celt/mips/celt_mipsr1.h \
celt/mips/fixed_generic_mipsr1.h \
celt/mips/kiss_fft_mipsr1.h \
celt/mips/mdct_mipsr1.h \
celt/mips/pitch_mipsr1.h \
celt/mips/vq_mipsr1.h \
celt/x86/pitch_sse.h \
celt/x86/vq_sse.h \
celt/x86/x86cpu.h

SILK_HEAD = \
silk/debug.h \
silk/control.h \
silk/errors.h \
silk/API.h \
silk/typedef.h \
silk/define.h \
silk/main.h \
silk/x86/main_sse.h \
silk/PLC.h \
silk/structs.h \
silk/tables.h \
silk/tuning_parameters.h \
silk/Inlines.h \
silk/MacroCount.h \
silk/MacroDebug.h \
silk/macros.h \
silk/NSQ.h \
silk/pitch_est_defines.h \
silk/resampler_private.h \
silk/resampler_rom.h \
silk/resampler_structs.h \
silk/SigProc_FIX.h \
silk/x86/SigProc_FIX_sse.h \
silk/arm/biquad_alt_arm.h \
silk/arm/LPC_inv_pred_gain_arm.h \
silk/arm/macros_armv4.h \
silk/arm/macros_armv5e.h \
silk/arm/macros_arm64.h \
silk/arm/SigProc_FIX_armv4.h \
silk/arm/SigProc_FIX_armv5e.h \
silk/arm/NSQ_del_dec_arm.h \
silk/arm/NSQ_neon.h \
silk/fixed/main_FIX.h \
silk/fixed/structs_FIX.h \
silk/fixed/arm/warped_autocorrelation_FIX_arm.h \
silk/fixed/mips/noise_shape_analysis_FIX_mipsr1.h \
silk/fixed/mips/warped_autocorrelation_FIX_mipsr1.h \
silk/float/main_FLP.h \
silk/float/structs_FLP.h \
silk/float/SigProc_FLP.h \
silk/mips/macros_mipsr1.h \
silk/mips/NSQ_del_dec_mipsr1.h \
silk/mips/sigproc_fix_mipsr1.h

OPUS_HEAD = \
include/opus.h \
include/opus_multistream.h \
include/opus_projection.h \
src/opus_private.h \
src/analysis.h \
src/mapping_matrix.h \
src/mlp.h \
src/tansig_table.h

libopus_la_SOURCES = $(CELT_SOURCES) $(SILK_SOURCES) $(OPUS_SOURCES)
libopus_la_LDFLAGS = -no-undefined -version-info @OPUS_LT_CURRENT@:@OPUS_LT_REVISION@:@OPUS_LT_AGE@
libopus_la_LIBADD = $(NE10_LIBS) $(LIBM) $(am__append_17)
pkginclude_HEADERS = include/opus.h include/opus_multistream.h \
	include/opus_types.h include/opus_defines.h \
	include/opus_projection.h $(am__append_24)
noinst_HEADERS = $(OPUS_HEAD) $(SILK_HEAD) $(CELT_HEAD)
@EXTRA_PROGRAMS_TRUE@opus_demo_SOURCES = src/opus_demo.c
@EXTRA_PROGRAMS_TRUE@opus_demo_LDADD = libopus.la $(NE10_LIBS) $(LIBM)
@EXTRA_PROGRAMS_TRUE@repacketizer_demo_SOURCES = src/repacketizer_demo.c
@EXTRA_PROGRAMS_TRUE@repacketizer_demo_LDADD = libopus.la $(NE10_LIBS) $(LIBM)
@EXTRA_PROGRAMS_TRUE@opus_compare_SOURCES = src/opus_compare.c
@EXTRA_PROGRAMS_TRUE@opus_compare_LDADD = $(LIBM)
@EXTRA_PROGRAMS_TRUE@trivial_example_SOURCES = doc/trivial_example.c
@EXTRA_PROGRAMS_TRUE@trivial_example_LDADD = libopus.la $(LIBM)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_api_SOURCES = tests/test_opus_api.c tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@tests_test_opus_api_LDADD = libopus.la $(NE10_LIBS) $(LIBM)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_encode_SOURCES = tests/test_opus_encode.c tests/opus_encode_regressions.c tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@tests_test_opus_encode_LDADD = libopus.la $(NE10_LIBS) $(LIBM)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_decode_SOURCES = tests/test_opus_decode.c tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@tests_test_opus_decode_LDADD = libopus.la $(NE10_LIBS) $(LIBM)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_padding_SOURCES = tests/test_opus_padding.c tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@tests_test_opus_padding_LDADD = libopus.la $(NE10_LIBS) $(LIBM)
@EXTRA_PROGRAMS_TRUE@CELT_OBJ = $(CELT_SOURCES:.c=.lo)
@EXTRA_PROGRAMS_TRUE@SILK_OBJ = $(SILK_SOURCES:.c=.lo)
@EXTRA_PROGRAMS_TRUE@OPUS_OBJ = $(OPUS_SOURCES:.c=.lo)
@EXTRA_PROGRAMS_TRUE@tests_test_opus_projection_SOURCES = tests/test_opus_projection.c tests/test_opus_common.h
@EXTRA_PROGRAMS_TRUE@tests_test_opus_projection_LDADD = $(OPUS_OBJ) \
@EXTRA_PROGRAMS_TRUE@	$(SILK_OBJ) $(CELT_OBJ) $(NE10_LIBS) \
@EXTRA_PROGRAMS_TRUE@	$(LIBM) $(am__append_18)
@EXTRA_PROGRAMS_TRUE@silk_tests_test_unit_LPC_inv_pred_gain_SOURCES = silk/tests/test_unit_LPC_inv_pred_gain.c
@EXTRA_PROGRAMS_TRUE@silk_tests_test_unit_LPC_inv_pred_gain_LDADD =  \
@EXTRA_PROGRAMS_TRUE@	$(SILK_OBJ) $(CELT_OBJ) $(NE10_LIBS) \
@EXTRA_PROGRAMS_TRUE@	$(LIBM) $(am__append_19)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_cwrs32_SOURCES = celt/tests/test_unit_cwrs32.c
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_cwrs32_LDADD = $(LIBM)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_dft_SOURCES = celt/tests/test_unit_dft.c
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_dft_LDADD = $(CELT_OBJ) \
@EXTRA_PROGRAMS_TRUE@	$(NE10_LIBS) $(LIBM) $(am__append_20)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_entropy_SOURCES = celt/tests/test_unit_entropy.c
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_entropy_LDADD = $(LIBM)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_laplace_SOURCES = celt/tests/test_unit_laplace.c
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_laplace_LDADD = $(LIBM)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_mathops_SOURCES = celt/tests/test_unit_mathops.c
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_mathops_LDADD = $(CELT_OBJ) \
@EXTRA_PROGRAMS_TRUE@	$(NE10_LIBS) $(LIBM) $(am__append_21)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_mdct_SOURCES = celt/tests/test_unit_mdct.c
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_mdct_LDADD = $(CELT_OBJ) \
@EXTRA_PROGRAMS_TRUE@	$(NE10_LIBS) $(LIBM) $(am__append_22)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_rotation_SOURCES = celt/tests/test_unit_rotation.c
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_rotation_LDADD =  \
@EXTRA_PROGRAMS_TRUE@	$(CELT_OBJ) $(NE10_LIBS) $(LIBM) \
@EXTRA_PROGRAMS_TRUE@	$(am__append_23)
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_types_SOURCES = celt/tests/test_unit_types.c
@EXTRA_PROGRAMS_TRUE@celt_tests_test_unit_types_LDADD = $(LIBM)
@CUSTOM_MODES_TRUE@@EXTRA_PROGRAMS_TRUE@opus_custom_demo_SOURCES = celt/opus_custom_demo.c
@CUSTOM_MODES_TRUE@@EXTRA_PROGRAMS_TRUE@opus_custom_demo_LDADD = libopus.la $(LIBM)
EXTRA_DIST = opus.pc.in \
             opus-uninstalled.pc.in \
             opus.m4 \
             Makefile.mips \
             Makefile.unix \
             CMakeLists.txt \
             cmake/CFeatureCheck.cmake \
             cmake/OpusBuildtype.cmake \
             cmake/OpusConfig.cmake \
             cmake/OpusConfig.cmake.in \
             cmake/OpusFunctions.cmake \
             cmake/OpusPackageVersion.cmake \
             cmake/OpusSources.cmake \
             cmake/RunTest.cmake \
             cmake/config.h.cmake.in \
             cmake/vla.c \
             cmake/cpu_info_by_asm.c \
             cmake/cpu_info_by_c.c \
             meson/get-version.py \
             meson/read-sources-list.py \
             meson.build \
             meson_options.txt \
             include/meson.build \
             celt/meson.build \
             celt/tests/meson.build \
             silk/meson.build \
             silk/tests/meson.build \
             src/meson.build \
             tests/meson.build \
             doc/meson.build \
             tests/run_vectors.sh \
             celt/arm/arm2gnu.pl \
             celt/arm/celt_pitch_xcorr_arm.s \
             win32/VS2015/opus.vcxproj \
             win32/VS2015/test_opus_encode.vcxproj.filters \
             win32/VS2015/test_opus_encode.vcxproj \
             win32/VS2015/opus_demo.vcxproj \
             win32/VS2015/test_opus_api.vcxproj.filters \
             win32/VS2015/test_opus_api.vcxproj \
             win32/VS2015/test_opus_decode.vcxproj.filters \
             win32/VS2015/opus_demo.vcxproj.filters \
             win32/VS2015/opus.vcxproj.filters \
             win32/VS2015/test_opus_decode.vcxproj \
             win32/VS2015/opus.sln \
             win32/VS2015/common.props \
             win32/genversion.bat \
             win32/config.h

pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = opus.pc
m4datadir = $(datadir)/aclocal
m4data_DATA = opus.m4
OPT_UNIT_TEST_OBJ = $(celt_tests_test_unit_mathops_SOURCES:.c=.o) \
                    $(celt_tests_test_unit_rotation_SOURCES:.c=.o) \
                    $(celt_tests_test_unit_mdct_SOURCES:.c=.o) \
                    $(celt_tests_test_unit_dft_SOURCES:.c=.o) \
                    $(silk_tests_test_unit_LPC_inv_pred_gain_SOURCES:.c=.o)

@HAVE_SSE_TRUE@SSE_OBJ = $(CELT_SOURCES_SSE:.c=.lo)
@HAVE_SSE2_TRUE@SSE2_OBJ = $(CELT_SOURCES_SSE2:.c=.lo)
@HAVE_SSE4_1_TRUE@SSE4_1_OBJ = $(CELT_SOURCES_SSE4_1:.c=.lo) \
@HAVE_SSE4_1_TRUE@             $(SILK_SOURCES_SSE4_1:.c=.lo) \
@HAVE_SSE4_1_TRUE@             $(SILK_SOURCES_FIXED_SSE4_1:.c=.lo)

@HAVE_ARM_NEON_INTR_TRUE@ARM_NEON_INTR_OBJ = $(CELT_SOURCES_ARM_NEON_INTR:.c=.lo) \
@HAVE_ARM_NEON_INTR_TRUE@                    $(SILK_SOURCES_ARM_NEON_INTR:.c=.lo) \
@HAVE_ARM_NEON_INTR_TRUE@                    $(SILK_SOURCES_FIXED_ARM_NEON_INTR:.c=.lo)

all: $(BUILT_SOURCES) config.h
	$(MAKE) $(AM_MAKEFLAGS) all-recursive

.SUFFIXES:
.SUFFIXES: .S .c .lo .log .o .obj .test .test$(EXEEXT) .trs
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am $(srcdir)/celt_sources.mk $(srcdir)/silk_sources.mk $(srcdir)/opus_sources.mk $(srcdir)/celt_headers.mk $(srcdir)/silk_headers.mk $(srcdir)/opus_headers.mk $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --gnu'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --gnu \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles);; \
	esac;
$(srcdir)/celt_sources.mk $(srcdir)/silk_sources.mk $(srcdir)/opus_sources.mk $(srcdir)/celt_headers.mk $(srcdir)/silk_headers.mk $(srcdir)/opus_headers.mk $(am__empty):

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):

config.h: stamp-h1
	@test -f $@ || rm -f stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) stamp-h1

stamp-h1: $(srcdir)/config.h.in $(top_builddir)/config.status
	@rm -f stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status config.h
$(srcdir)/config.h.in: @MAINTAINER_MODE_TRUE@ $(am__configure_deps) 
	($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	rm -f stamp-h1
	touch $@

distclean-hdr:
	-rm -f config.h stamp-h1
opus.pc: $(top_builddir)/config.status $(srcdir)/opus.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
opus-uninstalled.pc: $(top_builddir)/config.status $(srcdir)/opus-uninstalled.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
celt/arm/armopts.s: $(top_builddir)/config.status $(top_srcdir)/celt/arm/armopts.s.in
	cd $(top_builddir) && $(SHELL) ./config.status $@

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
celt/arm/$(am__dirstamp):
	@$(MKDIR_P) celt/arm
	@: > celt/arm/$(am__dirstamp)
celt/arm/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) celt/arm/$(DEPDIR)
	@: > celt/arm/$(DEPDIR)/$(am__dirstamp)
celt/arm/celt_pitch_xcorr_arm-gnu.lo: celt/arm/$(am__dirstamp) \
	celt/arm/$(DEPDIR)/$(am__dirstamp)

libarmasm.la: $(libarmasm_la_OBJECTS) $(libarmasm_la_DEPENDENCIES) $(EXTRA_libarmasm_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK) $(am_libarmasm_la_rpath) $(libarmasm_la_OBJECTS) $(libarmasm_la_LIBADD) $(LIBS)
celt/$(am__dirstamp):
	@$(MKDIR_P) celt
	@: > celt/$(am__dirstamp)
celt/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) celt/$(DEPDIR)
	@: > celt/$(DEPDIR)/$(am__dirstamp)
celt/bands.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/celt.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/celt_encoder.lo: celt/$(am__dirstamp) \
	celt/$(DEPDIR)/$(am__dirstamp)
celt/celt_decoder.lo: celt/$(am__dirstamp) \
	celt/$(DEPDIR)/$(am__dirstamp)
celt/cwrs.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/entcode.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/entdec.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/entenc.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/kiss_fft.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/laplace.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/mathops.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/mdct.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/modes.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/pitch.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/celt_lpc.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/quant_bands.lo: celt/$(am__dirstamp) \
	celt/$(DEPDIR)/$(am__dirstamp)
celt/rate.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/vq.lo: celt/$(am__dirstamp) celt/$(DEPDIR)/$(am__dirstamp)
celt/x86/$(am__dirstamp):
	@$(MKDIR_P) celt/x86
	@: > celt/x86/$(am__dirstamp)
celt/x86/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) celt/x86/$(DEPDIR)
	@: > celt/x86/$(DEPDIR)/$(am__dirstamp)
celt/x86/x86cpu.lo: celt/x86/$(am__dirstamp) \
	celt/x86/$(DEPDIR)/$(am__dirstamp)
celt/x86/x86_celt_map.lo: celt/x86/$(am__dirstamp) \
	celt/x86/$(DEPDIR)/$(am__dirstamp)
celt/x86/pitch_sse.lo: celt/x86/$(am__dirstamp) \
	celt/x86/$(DEPDIR)/$(am__dirstamp)
celt/x86/pitch_sse2.lo: celt/x86/$(am__dirstamp) \
	celt/x86/$(DEPDIR)/$(am__dirstamp)
celt/x86/vq_sse2.lo: celt/x86/$(am__dirstamp) \
	celt/x86/$(DEPDIR)/$(am__dirstamp)
celt/x86/celt_lpc_sse4_1.lo: celt/x86/$(am__dirstamp) \
	celt/x86/$(DEPDIR)/$(am__dirstamp)
celt/x86/pitch_sse4_1.lo: celt/x86/$(am__dirstamp) \
	celt/x86/$(DEPDIR)/$(am__dirstamp)
celt/arm/armcpu.lo: celt/arm/$(am__dirstamp) \
	celt/arm/$(DEPDIR)/$(am__dirstamp)
celt/arm/arm_celt_map.lo: celt/arm/$(am__dirstamp) \
	celt/arm/$(DEPDIR)/$(am__dirstamp)
celt/arm/celt_neon_intr.lo: celt/arm/$(am__dirstamp) \
	celt/arm/$(DEPDIR)/$(am__dirstamp)
celt/arm/pitch_neon_intr.lo: celt/arm/$(am__dirstamp) \
	celt/arm/$(DEPDIR)/$(am__dirstamp)
celt/arm/celt_fft_ne10.lo: celt/arm/$(am__dirstamp) \
	celt/arm/$(DEPDIR)/$(am__dirstamp)
celt/arm/celt_mdct_ne10.lo: celt/arm/$(am__dirstamp) \
	celt/arm/$(DEPDIR)/$(am__dirstamp)
silk/$(am__dirstamp):
	@$(MKDIR_P) silk
	@: > silk/$(am__dirstamp)
silk/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) silk/$(DEPDIR)
	@: > silk/$(DEPDIR)/$(am__dirstamp)
silk/CNG.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/code_signs.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/init_decoder.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/decode_core.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/decode_frame.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/decode_parameters.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/decode_indices.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/decode_pulses.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/decoder_set_fs.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/dec_API.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/enc_API.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/encode_indices.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/encode_pulses.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/gain_quant.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/interpolate.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/LP_variable_cutoff.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/NLSF_decode.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/NSQ.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/NSQ_del_dec.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/PLC.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/shell_coder.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/tables_gain.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/tables_LTP.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/tables_NLSF_CB_NB_MB.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/tables_NLSF_CB_WB.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/tables_other.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/tables_pitch_lag.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/tables_pulses_per_block.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/VAD.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/control_audio_bandwidth.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/quant_LTP_gains.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/VQ_WMat_EC.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/HP_variable_cutoff.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/NLSF_encode.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/NLSF_VQ.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/NLSF_unpack.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/NLSF_del_dec_quant.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/process_NLSFs.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/stereo_LR_to_MS.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/stereo_MS_to_LR.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/check_control_input.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/control_SNR.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/init_encoder.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/control_codec.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/A2NLSF.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/ana_filt_bank_1.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/biquad_alt.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/bwexpander_32.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/bwexpander.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/debug.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/decode_pitch.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/inner_prod_aligned.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/lin2log.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/log2lin.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/LPC_analysis_filter.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/LPC_inv_pred_gain.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/table_LSF_cos.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/NLSF2A.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/NLSF_stabilize.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/NLSF_VQ_weights_laroia.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/pitch_est_tables.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/resampler.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/resampler_down2_3.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/resampler_down2.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/resampler_private_AR2.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/resampler_private_down_FIR.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/resampler_private_IIR_FIR.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/resampler_private_up2_HQ.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/resampler_rom.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/sigm_Q15.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/sort.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/sum_sqr_shift.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/stereo_decode_pred.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/stereo_encode_pred.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/stereo_find_predictor.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/stereo_quant_pred.lo: silk/$(am__dirstamp) \
	silk/$(DEPDIR)/$(am__dirstamp)
silk/LPC_fit.lo: silk/$(am__dirstamp) silk/$(DEPDIR)/$(am__dirstamp)
silk/fixed/$(am__dirstamp):
	@$(MKDIR_P) silk/fixed
	@: > silk/fixed/$(am__dirstamp)
silk/fixed/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) silk/fixed/$(DEPDIR)
	@: > silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/LTP_analysis_filter_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/LTP_scale_ctrl_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/corrMatrix_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/encode_frame_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/find_LPC_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/find_LTP_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/find_pitch_lags_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/find_pred_coefs_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/noise_shape_analysis_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/process_gains_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/regularize_correlations_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/residual_energy16_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/residual_energy_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/warped_autocorrelation_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/apply_sine_window_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/autocorr_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/burg_modified_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/k2a_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/k2a_Q16_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/pitch_analysis_core_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/vector_ops_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/schur64_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/fixed/schur_FIX.lo: silk/fixed/$(am__dirstamp) \
	silk/fixed/$(DEPDIR)/$(am__dirstamp)
silk/x86/$(am__dirstamp):
	@$(MKDIR_P) silk/x86
	@: > silk/x86/$(am__dirstamp)
silk/x86/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) silk/x86/$(DEPDIR)
	@: > silk/x86/$(DEPDIR)/$(am__dirstamp)
silk/x86/NSQ_sse4_1.lo: silk/x86/$(am__dirstamp) \
	silk/x86/$(DEPDIR)/$(am__dirstamp)
silk/x86/NSQ_del_dec_sse4_1.lo: silk/x86/$(am__dirstamp) \
	silk/x86/$(DEPDIR)/$(am__dirstamp)
silk/x86/VAD_sse4_1.lo: silk/x86/$(am__dirstamp) \
	silk/x86/$(DEPDIR)/$(am__dirstamp)
silk/x86/VQ_WMat_EC_sse4_1.lo: silk/x86/$(am__dirstamp) \
	silk/x86/$(DEPDIR)/$(am__dirstamp)
silk/fixed/x86/$(am__dirstamp):
	@$(MKDIR_P) silk/fixed/x86
	@: > silk/fixed/x86/$(am__dirstamp)
silk/fixed/x86/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) silk/fixed/x86/$(DEPDIR)
	@: > silk/fixed/x86/$(DEPDIR)/$(am__dirstamp)
silk/fixed/x86/vector_ops_FIX_sse4_1.lo:  \
	silk/fixed/x86/$(am__dirstamp) \
	silk/fixed/x86/$(DEPDIR)/$(am__dirstamp)
silk/fixed/x86/burg_modified_FIX_sse4_1.lo:  \
	silk/fixed/x86/$(am__dirstamp) \
	silk/fixed/x86/$(DEPDIR)/$(am__dirstamp)
silk/fixed/arm/$(am__dirstamp):
	@$(MKDIR_P) silk/fixed/arm
	@: > silk/fixed/arm/$(am__dirstamp)
silk/fixed/arm/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) silk/fixed/arm/$(DEPDIR)
	@: > silk/fixed/arm/$(DEPDIR)/$(am__dirstamp)
silk/fixed/arm/warped_autocorrelation_FIX_neon_intr.lo:  \
	silk/fixed/arm/$(am__dirstamp) \
	silk/fixed/arm/$(DEPDIR)/$(am__dirstamp)
silk/float/$(am__dirstamp):
	@$(MKDIR_P) silk/float
	@: > silk/float/$(am__dirstamp)
silk/float/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) silk/float/$(DEPDIR)
	@: > silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/apply_sine_window_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/corrMatrix_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/encode_frame_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/find_LPC_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/find_LTP_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/find_pitch_lags_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/find_pred_coefs_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/LPC_analysis_filter_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/LTP_analysis_filter_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/LTP_scale_ctrl_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/noise_shape_analysis_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/process_gains_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/regularize_correlations_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/residual_energy_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/warped_autocorrelation_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/wrappers_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/autocorrelation_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/burg_modified_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/bwexpander_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/energy_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/inner_product_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/k2a_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/LPC_inv_pred_gain_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/pitch_analysis_core_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/scale_copy_vector_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/scale_vector_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/schur_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/float/sort_FLP.lo: silk/float/$(am__dirstamp) \
	silk/float/$(DEPDIR)/$(am__dirstamp)
silk/x86/x86_silk_map.lo: silk/x86/$(am__dirstamp) \
	silk/x86/$(DEPDIR)/$(am__dirstamp)
silk/arm/$(am__dirstamp):
	@$(MKDIR_P) silk/arm
	@: > silk/arm/$(am__dirstamp)
silk/arm/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) silk/arm/$(DEPDIR)
	@: > silk/arm/$(DEPDIR)/$(am__dirstamp)
silk/arm/arm_silk_map.lo: silk/arm/$(am__dirstamp) \
	silk/arm/$(DEPDIR)/$(am__dirstamp)
silk/arm/biquad_alt_neon_intr.lo: silk/arm/$(am__dirstamp) \
	silk/arm/$(DEPDIR)/$(am__dirstamp)
silk/arm/LPC_inv_pred_gain_neon_intr.lo: silk/arm/$(am__dirstamp) \
	silk/arm/$(DEPDIR)/$(am__dirstamp)
silk/arm/NSQ_del_dec_neon_intr.lo: silk/arm/$(am__dirstamp) \
	silk/arm/$(DEPDIR)/$(am__dirstamp)
silk/arm/NSQ_neon.lo: silk/arm/$(am__dirstamp) \
	silk/arm/$(DEPDIR)/$(am__dirstamp)
src/$(am__dirstamp):
	@$(MKDIR_P) src
	@: > src/$(am__dirstamp)
src/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/$(DEPDIR)
	@: > src/$(DEPDIR)/$(am__dirstamp)
src/opus.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/opus_decoder.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/opus_encoder.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/opus_multistream.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/opus_multistream_encoder.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/opus_multistream_decoder.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/repacketizer.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/opus_projection_encoder.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/opus_projection_decoder.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/mapping_matrix.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/analysis.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/mlp.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/mlp_data.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)

libopus.la: $(libopus_la_OBJECTS) $(libopus_la_DEPENDENCIES) $(EXTRA_libopus_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libopus_la_LINK) -rpath $(libdir) $(libopus_la_OBJECTS) $(libopus_la_LIBADD) $(LIBS)
celt/tests/$(am__dirstamp):
	@$(MKDIR_P) celt/tests
	@: > celt/tests/$(am__dirstamp)
celt/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) celt/tests/$(DEPDIR)
	@: > celt/tests/$(DEPDIR)/$(am__dirstamp)
celt/tests/test_unit_cwrs32.$(OBJEXT): celt/tests/$(am__dirstamp) \
	celt/tests/$(DEPDIR)/$(am__dirstamp)

celt/tests/test_unit_cwrs32$(EXEEXT): $(celt_tests_test_unit_cwrs32_OBJECTS) $(celt_tests_test_unit_cwrs32_DEPENDENCIES) $(EXTRA_celt_tests_test_unit_cwrs32_DEPENDENCIES) celt/tests/$(am__dirstamp)
	@rm -f celt/tests/test_unit_cwrs32$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(celt_tests_test_unit_cwrs32_OBJECTS) $(celt_tests_test_unit_cwrs32_LDADD) $(LIBS)
celt/tests/test_unit_dft.$(OBJEXT): celt/tests/$(am__dirstamp) \
	celt/tests/$(DEPDIR)/$(am__dirstamp)

celt/tests/test_unit_dft$(EXEEXT): $(celt_tests_test_unit_dft_OBJECTS) $(celt_tests_test_unit_dft_DEPENDENCIES) $(EXTRA_celt_tests_test_unit_dft_DEPENDENCIES) celt/tests/$(am__dirstamp)
	@rm -f celt/tests/test_unit_dft$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(celt_tests_test_unit_dft_OBJECTS) $(celt_tests_test_unit_dft_LDADD) $(LIBS)
celt/tests/test_unit_entropy.$(OBJEXT): celt/tests/$(am__dirstamp) \
	celt/tests/$(DEPDIR)/$(am__dirstamp)

celt/tests/test_unit_entropy$(EXEEXT): $(celt_tests_test_unit_entropy_OBJECTS) $(celt_tests_test_unit_entropy_DEPENDENCIES) $(EXTRA_celt_tests_test_unit_entropy_DEPENDENCIES) celt/tests/$(am__dirstamp)
	@rm -f celt/tests/test_unit_entropy$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(celt_tests_test_unit_entropy_OBJECTS) $(celt_tests_test_unit_entropy_LDADD) $(LIBS)
celt/tests/test_unit_laplace.$(OBJEXT): celt/tests/$(am__dirstamp) \
	celt/tests/$(DEPDIR)/$(am__dirstamp)

celt/tests/test_unit_laplace$(EXEEXT): $(celt_tests_test_unit_laplace_OBJECTS) $(celt_tests_test_unit_laplace_DEPENDENCIES) $(EXTRA_celt_tests_test_unit_laplace_DEPENDENCIES) celt/tests/$(am__dirstamp)
	@rm -f celt/tests/test_unit_laplace$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(celt_tests_test_unit_laplace_OBJECTS) $(celt_tests_test_unit_laplace_LDADD) $(LIBS)
celt/tests/test_unit_mathops.$(OBJEXT): celt/tests/$(am__dirstamp) \
	celt/tests/$(DEPDIR)/$(am__dirstamp)

celt/tests/test_unit_mathops$(EXEEXT): $(celt_tests_test_unit_mathops_OBJECTS) $(celt_tests_test_unit_mathops_DEPENDENCIES) $(EXTRA_celt_tests_test_unit_mathops_DEPENDENCIES) celt/tests/$(am__dirstamp)
	@rm -f celt/tests/test_unit_mathops$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(celt_tests_test_unit_mathops_OBJECTS) $(celt_tests_test_unit_mathops_LDADD) $(LIBS)
celt/tests/test_unit_mdct.$(OBJEXT): celt/tests/$(am__dirstamp) \
	celt/tests/$(DEPDIR)/$(am__dirstamp)

celt/tests/test_unit_mdct$(EXEEXT): $(celt_tests_test_unit_mdct_OBJECTS) $(celt_tests_test_unit_mdct_DEPENDENCIES) $(EXTRA_celt_tests_test_unit_mdct_DEPENDENCIES) celt/tests/$(am__dirstamp)
	@rm -f celt/tests/test_unit_mdct$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(celt_tests_test_unit_mdct_OBJECTS) $(celt_tests_test_unit_mdct_LDADD) $(LIBS)
celt/tests/test_unit_rotation.$(OBJEXT): celt/tests/$(am__dirstamp) \
	celt/tests/$(DEPDIR)/$(am__dirstamp)

celt/tests/test_unit_rotation$(EXEEXT): $(celt_tests_test_unit_rotation_OBJECTS) $(celt_tests_test_unit_rotation_DEPENDENCIES) $(EXTRA_celt_tests_test_unit_rotation_DEPENDENCIES) celt/tests/$(am__dirstamp)
	@rm -f celt/tests/test_unit_rotation$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(celt_tests_test_unit_rotation_OBJECTS) $(celt_tests_test_unit_rotation_LDADD) $(LIBS)
celt/tests/test_unit_types.$(OBJEXT): celt/tests/$(am__dirstamp) \
	celt/tests/$(DEPDIR)/$(am__dirstamp)

celt/tests/test_unit_types$(EXEEXT): $(celt_tests_test_unit_types_OBJECTS) $(celt_tests_test_unit_types_DEPENDENCIES) $(EXTRA_celt_tests_test_unit_types_DEPENDENCIES) celt/tests/$(am__dirstamp)
	@rm -f celt/tests/test_unit_types$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(celt_tests_test_unit_types_OBJECTS) $(celt_tests_test_unit_types_LDADD) $(LIBS)
src/opus_compare.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)

opus_compare$(EXEEXT): $(opus_compare_OBJECTS) $(opus_compare_DEPENDENCIES) $(EXTRA_opus_compare_DEPENDENCIES) 
	@rm -f opus_compare$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(opus_compare_OBJECTS) $(opus_compare_LDADD) $(LIBS)
celt/opus_custom_demo.$(OBJEXT): celt/$(am__dirstamp) \
	celt/$(DEPDIR)/$(am__dirstamp)

opus_custom_demo$(EXEEXT): $(opus_custom_demo_OBJECTS) $(opus_custom_demo_DEPENDENCIES) $(EXTRA_opus_custom_demo_DEPENDENCIES) 
	@rm -f opus_custom_demo$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(opus_custom_demo_OBJECTS) $(opus_custom_demo_LDADD) $(LIBS)
src/opus_demo.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)

opus_demo$(EXEEXT): $(opus_demo_OBJECTS) $(opus_demo_DEPENDENCIES) $(EXTRA_opus_demo_DEPENDENCIES) 
	@rm -f opus_demo$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(opus_demo_OBJECTS) $(opus_demo_LDADD) $(LIBS)
src/repacketizer_demo.$(OBJEXT): src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)

repacketizer_demo$(EXEEXT): $(repacketizer_demo_OBJECTS) $(repacketizer_demo_DEPENDENCIES) $(EXTRA_repacketizer_demo_DEPENDENCIES) 
	@rm -f repacketizer_demo$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(repacketizer_demo_OBJECTS) $(repacketizer_demo_LDADD) $(LIBS)
silk/tests/$(am__dirstamp):
	@$(MKDIR_P) silk/tests
	@: > silk/tests/$(am__dirstamp)
silk/tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) silk/tests/$(DEPDIR)
	@: > silk/tests/$(DEPDIR)/$(am__dirstamp)
silk/tests/test_unit_LPC_inv_pred_gain.$(OBJEXT):  \
	silk/tests/$(am__dirstamp) \
	silk/tests/$(DEPDIR)/$(am__dirstamp)

silk/tests/test_unit_LPC_inv_pred_gain$(EXEEXT): $(silk_tests_test_unit_LPC_inv_pred_gain_OBJECTS) $(silk_tests_test_unit_LPC_inv_pred_gain_DEPENDENCIES) $(EXTRA_silk_tests_test_unit_LPC_inv_pred_gain_DEPENDENCIES) silk/tests/$(am__dirstamp)
	@rm -f silk/tests/test_unit_LPC_inv_pred_gain$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(silk_tests_test_unit_LPC_inv_pred_gain_OBJECTS) $(silk_tests_test_unit_LPC_inv_pred_gain_LDADD) $(LIBS)
tests/$(am__dirstamp):
	@$(MKDIR_P) tests
	@: > tests/$(am__dirstamp)
tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) tests/$(DEPDIR)
	@: > tests/$(DEPDIR)/$(am__dirstamp)
tests/test_opus_api.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

tests/test_opus_api$(EXEEXT): $(tests_test_opus_api_OBJECTS) $(tests_test_opus_api_DEPENDENCIES) $(EXTRA_tests_test_opus_api_DEPENDENCIES) tests/$(am__dirstamp)
	@rm -f tests/test_opus_api$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(tests_test_opus_api_OBJECTS) $(tests_test_opus_api_LDADD) $(LIBS)
tests/test_opus_decode.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

tests/test_opus_decode$(EXEEXT): $(tests_test_opus_decode_OBJECTS) $(tests_test_opus_decode_DEPENDENCIES) $(EXTRA_tests_test_opus_decode_DEPENDENCIES) tests/$(am__dirstamp)
	@rm -f tests/test_opus_decode$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(tests_test_opus_decode_OBJECTS) $(tests_test_opus_decode_LDADD) $(LIBS)
tests/test_opus_encode.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)
tests/opus_encode_regressions.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

tests/test_opus_encode$(EXEEXT): $(tests_test_opus_encode_OBJECTS) $(tests_test_opus_encode_DEPENDENCIES) $(EXTRA_tests_test_opus_encode_DEPENDENCIES) tests/$(am__dirstamp)
	@rm -f tests/test_opus_encode$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(tests_test_opus_encode_OBJECTS) $(tests_test_opus_encode_LDADD) $(LIBS)
tests/test_opus_padding.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

tests/test_opus_padding$(EXEEXT): $(tests_test_opus_padding_OBJECTS) $(tests_test_opus_padding_DEPENDENCIES) $(EXTRA_tests_test_opus_padding_DEPENDENCIES) tests/$(am__dirstamp)
	@rm -f tests/test_opus_padding$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(tests_test_opus_padding_OBJECTS) $(tests_test_opus_padding_LDADD) $(LIBS)
tests/test_opus_projection.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

tests/test_opus_projection$(EXEEXT): $(tests_test_opus_projection_OBJECTS) $(tests_test_opus_projection_DEPENDENCIES) $(EXTRA_tests_test_opus_projection_DEPENDENCIES) tests/$(am__dirstamp)
	@rm -f tests/test_opus_projection$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(tests_test_opus_projection_OBJECTS) $(tests_test_opus_projection_LDADD) $(LIBS)
doc/$(am__dirstamp):
	@$(MKDIR_P) doc
	@: > doc/$(am__dirstamp)
doc/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) doc/$(DEPDIR)
	@: > doc/$(DEPDIR)/$(am__dirstamp)
doc/trivial_example.$(OBJEXT): doc/$(am__dirstamp) \
	doc/$(DEPDIR)/$(am__dirstamp)

trivial_example$(EXEEXT): $(trivial_example_OBJECTS) $(trivial_example_DEPENDENCIES) $(EXTRA_trivial_example_DEPENDENCIES) 
	@rm -f trivial_example$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(trivial_example_OBJECTS) $(trivial_example_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f celt/*.$(OBJEXT)
	-rm -f celt/*.lo
	-rm -f celt/arm/*.$(OBJEXT)
	-rm -f celt/arm/*.lo
	-rm -f celt/tests/*.$(OBJEXT)
	-rm -f celt/x86/*.$(OBJEXT)
	-rm -f celt/x86/*.lo
	-rm -f doc/*.$(OBJEXT)
	-rm -f silk/*.$(OBJEXT)
	-rm -f silk/*.lo
	-rm -f silk/arm/*.$(OBJEXT)
	-rm -f silk/arm/*.lo
	-rm -f silk/fixed/*.$(OBJEXT)
	-rm -f silk/fixed/*.lo
	-rm -f silk/fixed/arm/*.$(OBJEXT)
	-rm -f silk/fixed/arm/*.lo
	-rm -f silk/fixed/x86/*.$(OBJEXT)
	-rm -f silk/fixed/x86/*.lo
	-rm -f silk/float/*.$(OBJEXT)
	-rm -f silk/float/*.lo
	-rm -f silk/tests/*.$(OBJEXT)
	-rm -f silk/x86/*.$(OBJEXT)
	-rm -f silk/x86/*.lo
	-rm -f src/*.$(OBJEXT)
	-rm -f src/*.lo
	-rm -f tests/*.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/bands.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/celt.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/celt_decoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/celt_encoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/celt_lpc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/cwrs.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/entcode.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/entdec.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/entenc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/kiss_fft.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/laplace.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/mathops.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/mdct.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/modes.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/opus_custom_demo.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/pitch.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/quant_bands.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/rate.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/$(DEPDIR)/vq.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/arm/$(DEPDIR)/arm_celt_map.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/arm/$(DEPDIR)/armcpu.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/arm/$(DEPDIR)/celt_fft_ne10.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/arm/$(DEPDIR)/celt_mdct_ne10.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/arm/$(DEPDIR)/celt_neon_intr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/arm/$(DEPDIR)/celt_pitch_xcorr_arm-gnu.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/arm/$(DEPDIR)/pitch_neon_intr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/tests/$(DEPDIR)/test_unit_cwrs32.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/tests/$(DEPDIR)/test_unit_dft.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/tests/$(DEPDIR)/test_unit_entropy.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/tests/$(DEPDIR)/test_unit_laplace.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/tests/$(DEPDIR)/test_unit_mathops.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/tests/$(DEPDIR)/test_unit_mdct.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/tests/$(DEPDIR)/test_unit_rotation.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/tests/$(DEPDIR)/test_unit_types.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/x86/$(DEPDIR)/celt_lpc_sse4_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/x86/$(DEPDIR)/pitch_sse.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/x86/$(DEPDIR)/pitch_sse2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/x86/$(DEPDIR)/pitch_sse4_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/x86/$(DEPDIR)/vq_sse2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/x86/$(DEPDIR)/x86_celt_map.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@celt/x86/$(DEPDIR)/x86cpu.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@doc/$(DEPDIR)/trivial_example.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/A2NLSF.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/CNG.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/HP_variable_cutoff.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/LPC_analysis_filter.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/LPC_fit.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/LPC_inv_pred_gain.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/LP_variable_cutoff.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NLSF2A.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NLSF_VQ.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NLSF_VQ_weights_laroia.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NLSF_decode.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NLSF_del_dec_quant.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NLSF_encode.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NLSF_stabilize.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NLSF_unpack.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NSQ.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/NSQ_del_dec.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/PLC.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/VAD.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/VQ_WMat_EC.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/ana_filt_bank_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/biquad_alt.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/bwexpander.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/bwexpander_32.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/check_control_input.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/code_signs.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/control_SNR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/control_audio_bandwidth.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/control_codec.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/debug.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/dec_API.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/decode_core.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/decode_frame.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/decode_indices.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/decode_parameters.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/decode_pitch.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/decode_pulses.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/decoder_set_fs.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/enc_API.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/encode_indices.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/encode_pulses.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/gain_quant.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/init_decoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/init_encoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/inner_prod_aligned.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/interpolate.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/lin2log.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/log2lin.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/pitch_est_tables.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/process_NLSFs.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/quant_LTP_gains.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/resampler.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/resampler_down2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/resampler_down2_3.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/resampler_private_AR2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/resampler_private_IIR_FIR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/resampler_private_down_FIR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/resampler_private_up2_HQ.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/resampler_rom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/shell_coder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/sigm_Q15.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/sort.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/stereo_LR_to_MS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/stereo_MS_to_LR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/stereo_decode_pred.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/stereo_encode_pred.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/stereo_find_predictor.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/stereo_quant_pred.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/sum_sqr_shift.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/table_LSF_cos.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/tables_LTP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/tables_NLSF_CB_NB_MB.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/tables_NLSF_CB_WB.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/tables_gain.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/tables_other.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/tables_pitch_lag.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/$(DEPDIR)/tables_pulses_per_block.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/arm/$(DEPDIR)/LPC_inv_pred_gain_neon_intr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/arm/$(DEPDIR)/NSQ_del_dec_neon_intr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/arm/$(DEPDIR)/NSQ_neon.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/arm/$(DEPDIR)/arm_silk_map.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/arm/$(DEPDIR)/biquad_alt_neon_intr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/LTP_analysis_filter_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/LTP_scale_ctrl_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/apply_sine_window_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/autocorr_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/burg_modified_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/corrMatrix_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/encode_frame_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/find_LPC_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/find_LTP_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/find_pitch_lags_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/find_pred_coefs_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/k2a_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/k2a_Q16_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/noise_shape_analysis_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/pitch_analysis_core_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/process_gains_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/regularize_correlations_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/residual_energy16_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/residual_energy_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/schur64_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/schur_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/vector_ops_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/$(DEPDIR)/warped_autocorrelation_FIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/arm/$(DEPDIR)/warped_autocorrelation_FIX_neon_intr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/x86/$(DEPDIR)/burg_modified_FIX_sse4_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/fixed/x86/$(DEPDIR)/vector_ops_FIX_sse4_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/LPC_analysis_filter_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/LPC_inv_pred_gain_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/LTP_analysis_filter_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/LTP_scale_ctrl_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/apply_sine_window_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/autocorrelation_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/burg_modified_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/bwexpander_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/corrMatrix_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/encode_frame_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/energy_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/find_LPC_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/find_LTP_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/find_pitch_lags_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/find_pred_coefs_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/inner_product_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/k2a_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/noise_shape_analysis_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/pitch_analysis_core_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/process_gains_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/regularize_correlations_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/residual_energy_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/scale_copy_vector_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/scale_vector_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/schur_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/sort_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/warped_autocorrelation_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/float/$(DEPDIR)/wrappers_FLP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/tests/$(DEPDIR)/test_unit_LPC_inv_pred_gain.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/x86/$(DEPDIR)/NSQ_del_dec_sse4_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/x86/$(DEPDIR)/NSQ_sse4_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/x86/$(DEPDIR)/VAD_sse4_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/x86/$(DEPDIR)/VQ_WMat_EC_sse4_1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@silk/x86/$(DEPDIR)/x86_silk_map.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/analysis.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/mapping_matrix.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/mlp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/mlp_data.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_compare.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_decoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_demo.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_encoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_multistream.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_multistream_decoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_multistream_encoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_projection_decoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/opus_projection_encoder.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/repacketizer.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/repacketizer_demo.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/opus_encode_regressions.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/test_opus_api.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/test_opus_decode.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/test_opus_encode.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/test_opus_padding.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/test_opus_projection.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.S.o:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCCAS_TRUE@	$(CPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(CPPASCOMPILE) -c -o $@ $<

.S.obj:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCCAS_TRUE@	$(CPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(CPPASCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.S.lo:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCCAS_TRUE@	$(LTCPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(LTCPPASCOMPILE) -c -o $@ $<

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf celt/.libs celt/_libs
	-rm -rf celt/arm/.libs celt/arm/_libs
	-rm -rf celt/tests/.libs celt/tests/_libs
	-rm -rf celt/x86/.libs celt/x86/_libs
	-rm -rf silk/.libs silk/_libs
	-rm -rf silk/arm/.libs silk/arm/_libs
	-rm -rf silk/fixed/.libs silk/fixed/_libs
	-rm -rf silk/fixed/arm/.libs silk/fixed/arm/_libs
	-rm -rf silk/fixed/x86/.libs silk/fixed/x86/_libs
	-rm -rf silk/float/.libs silk/float/_libs
	-rm -rf silk/tests/.libs silk/tests/_libs
	-rm -rf silk/x86/.libs silk/x86/_libs
	-rm -rf src/.libs src/_libs
	-rm -rf tests/.libs tests/_libs

distclean-libtool:
	-rm -f libtool config.lt
install-m4dataDATA: $(m4data_DATA)
	@$(NORMAL_INSTALL)
	@list='$(m4data_DATA)'; test -n "$(m4datadir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(m4datadir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(m4datadir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(m4datadir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(m4datadir)" || exit $$?; \
	done

uninstall-m4dataDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(m4data_DATA)'; test -n "$(m4datadir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(m4datadir)'; $(am__uninstall_files_from_dir)
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)
install-pkgincludeHEADERS: $(pkginclude_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(pkginclude_HEADERS)'; test -n "$(pkgincludedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgincludedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgincludedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(pkgincludedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(pkgincludedir)" || exit $$?; \
	done

uninstall-pkgincludeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(pkginclude_HEADERS)'; test -n "$(pkgincludedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgincludedir)'; $(am__uninstall_files_from_dir)

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files

# Recover from deleted '.trs' file; this should ensure that
# "rm -f foo.log; make foo.trs" re-run 'foo.test', and re-create
# both 'foo.log' and 'foo.trs'.  Break the recipe in two subshells
# to avoid problems with "make -n".
.log.trs:
	rm -f $< $@
	$(MAKE) $(AM_MAKEFLAGS) $<

# Leading 'am--fnord' is there to ensure the list of targets does not
# expand to empty, as could happen e.g. with make check TESTS=''.
am--fnord $(TEST_LOGS) $(TEST_LOGS:.log=.trs): $(am__force_recheck)
am--force-recheck:
	@:

$(TEST_SUITE_LOG): $(TEST_LOGS)
	@$(am__set_TESTS_bases); \
	am__f_ok () { test -f "$$1" && test -r "$$1"; }; \
	redo_bases=`for i in $$bases; do \
	              am__f_ok $$i.trs && am__f_ok $$i.log || echo $$i; \
	            done`; \
	if test -n "$$redo_bases"; then \
	  redo_logs=`for i in $$redo_bases; do echo $$i.log; done`; \
	  redo_results=`for i in $$redo_bases; do echo $$i.trs; done`; \
	  if $(am__make_dryrun); then :; else \
	    rm -f $$redo_logs && rm -f $$redo_results || exit 1; \
	  fi; \
	fi; \
	if test -n "$$am__remaking_logs"; then \
	  echo "fatal: making $(TEST_SUITE_LOG): possible infinite" \
	       "recursion detected" >&2; \
	elif test -n "$$redo_logs"; then \
	  am__remaking_logs=yes $(MAKE) $(AM_MAKEFLAGS) $$redo_logs; \
	fi; \
	if $(am__make_dryrun); then :; else \
	  st=0;  \
	  errmsg="fatal: making $(TEST_SUITE_LOG): failed to create"; \
	  for i in $$redo_bases; do \
	    test -f $$i.trs && test -r $$i.trs \
	      || { echo "$$errmsg $$i.trs" >&2; st=1; }; \
	    test -f $$i.log && test -r $$i.log \
	      || { echo "$$errmsg $$i.log" >&2; st=1; }; \
	  done; \
	  test $$st -eq 0 || exit 1; \
	fi
	@$(am__sh_e_setup); $(am__tty_colors); $(am__set_TESTS_bases); \
	ws='[ 	]'; \
	results=`for b in $$bases; do echo $$b.trs; done`; \
	test -n "$$results" || results=/dev/null; \
	all=`  grep "^$$ws*:test-result:"           $$results | wc -l`; \
	pass=` grep "^$$ws*:test-result:$$ws*PASS"  $$results | wc -l`; \
	fail=` grep "^$$ws*:test-result:$$ws*FAIL"  $$results | wc -l`; \
	skip=` grep "^$$ws*:test-result:$$ws*SKIP"  $$results | wc -l`; \
	xfail=`grep "^$$ws*:test-result:$$ws*XFAIL" $$results | wc -l`; \
	xpass=`grep "^$$ws*:test-result:$$ws*XPASS" $$results | wc -l`; \
	error=`grep "^$$ws*:test-result:$$ws*ERROR" $$results | wc -l`; \
	if test `expr $$fail + $$xpass + $$error` -eq 0; then \
	  success=true; \
	else \
	  success=false; \
	fi; \
	br='==================='; br=$$br$$br$$br$$br; \
	result_count () \
	{ \
	    if test x"$$1" = x"--maybe-color"; then \
	      maybe_colorize=yes; \
	    elif test x"$$1" = x"--no-color"; then \
	      maybe_colorize=no; \
	    else \
	      echo "$@: invalid 'result_count' usage" >&2; exit 4; \
	    fi; \
	    shift; \
	    desc=$$1 count=$$2; \
	    if test $$maybe_colorize = yes && test $$count -gt 0; then \
	      color_start=$$3 color_end=$$std; \
	    else \
	      color_start= color_end=; \
	    fi; \
	    echo "$${color_start}# $$desc $$count$${color_end}"; \
	}; \
	create_testsuite_report () \
	{ \
	  result_count $$1 "TOTAL:" $$all   "$$brg"; \
	  result_count $$1 "PASS: " $$pass  "$$grn"; \
	  result_count $$1 "SKIP: " $$skip  "$$blu"; \
	  result_count $$1 "XFAIL:" $$xfail "$$lgn"; \
	  result_count $$1 "FAIL: " $$fail  "$$red"; \
	  result_count $$1 "XPASS:" $$xpass "$$red"; \
	  result_count $$1 "ERROR:" $$error "$$mgn"; \
	}; \
	{								\
	  echo "$(PACKAGE_STRING): $(subdir)/$(TEST_SUITE_LOG)" |	\
	    $(am__rst_title);						\
	  create_testsuite_report --no-color;				\
	  echo;								\
	  echo ".. contents:: :depth: 2";				\
	  echo;								\
	  for b in $$bases; do echo $$b; done				\
	    | $(am__create_global_log);					\
	} >$(TEST_SUITE_LOG).tmp || exit 1;				\
	mv $(TEST_SUITE_LOG).tmp $(TEST_SUITE_LOG);			\
	if $$success; then						\
	  col="$$grn";							\
	 else								\
	  col="$$red";							\
	  test x"$$VERBOSE" = x || cat $(TEST_SUITE_LOG);		\
	fi;								\
	echo "$${col}$$br$${std}"; 					\
	echo "$${col}Testsuite summary for $(PACKAGE_STRING)$${std}";	\
	echo "$${col}$$br$${std}"; 					\
	create_testsuite_report --maybe-color;				\
	echo "$$col$$br$$std";						\
	if $$success; then :; else					\
	  echo "$${col}See $(subdir)/$(TEST_SUITE_LOG)$${std}";		\
	  if test -n "$(PACKAGE_BUGREPORT)"; then			\
	    echo "$${col}Please report to $(PACKAGE_BUGREPORT)$${std}";	\
	  fi;								\
	  echo "$$col$$br$$std";					\
	fi;								\
	$$success || exit 1

check-TESTS: 
	@list='$(RECHECK_LOGS)';           test -z "$$list" || rm -f $$list
	@list='$(RECHECK_LOGS:.log=.trs)'; test -z "$$list" || rm -f $$list
	@test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	trs_list=`for i in $$bases; do echo $$i.trs; done`; \
	log_list=`echo $$log_list`; trs_list=`echo $$trs_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) TEST_LOGS="$$log_list"; \
	exit $$?;
recheck: all 
	@test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	bases=`for i in $$bases; do echo $$i; done \
	         | $(am__list_recheck_tests)` || exit 1; \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	log_list=`echo $$log_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) \
	        am__force_recheck=am--force-recheck \
	        TEST_LOGS="$$log_list"; \
	exit $$?
celt/tests/test_unit_cwrs32.log: celt/tests/test_unit_cwrs32$(EXEEXT)
	@p='celt/tests/test_unit_cwrs32$(EXEEXT)'; \
	b='celt/tests/test_unit_cwrs32'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
celt/tests/test_unit_dft.log: celt/tests/test_unit_dft$(EXEEXT)
	@p='celt/tests/test_unit_dft$(EXEEXT)'; \
	b='celt/tests/test_unit_dft'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
celt/tests/test_unit_entropy.log: celt/tests/test_unit_entropy$(EXEEXT)
	@p='celt/tests/test_unit_entropy$(EXEEXT)'; \
	b='celt/tests/test_unit_entropy'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
celt/tests/test_unit_laplace.log: celt/tests/test_unit_laplace$(EXEEXT)
	@p='celt/tests/test_unit_laplace$(EXEEXT)'; \
	b='celt/tests/test_unit_laplace'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
celt/tests/test_unit_mathops.log: celt/tests/test_unit_mathops$(EXEEXT)
	@p='celt/tests/test_unit_mathops$(EXEEXT)'; \
	b='celt/tests/test_unit_mathops'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
celt/tests/test_unit_mdct.log: celt/tests/test_unit_mdct$(EXEEXT)
	@p='celt/tests/test_unit_mdct$(EXEEXT)'; \
	b='celt/tests/test_unit_mdct'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
celt/tests/test_unit_rotation.log: celt/tests/test_unit_rotation$(EXEEXT)
	@p='celt/tests/test_unit_rotation$(EXEEXT)'; \
	b='celt/tests/test_unit_rotation'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
celt/tests/test_unit_types.log: celt/tests/test_unit_types$(EXEEXT)
	@p='celt/tests/test_unit_types$(EXEEXT)'; \
	b='celt/tests/test_unit_types'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
silk/tests/test_unit_LPC_inv_pred_gain.log: silk/tests/test_unit_LPC_inv_pred_gain$(EXEEXT)
	@p='silk/tests/test_unit_LPC_inv_pred_gain$(EXEEXT)'; \
	b='silk/tests/test_unit_LPC_inv_pred_gain'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/test_opus_api.log: tests/test_opus_api$(EXEEXT)
	@p='tests/test_opus_api$(EXEEXT)'; \
	b='tests/test_opus_api'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/test_opus_decode.log: tests/test_opus_decode$(EXEEXT)
	@p='tests/test_opus_decode$(EXEEXT)'; \
	b='tests/test_opus_decode'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/test_opus_encode.log: tests/test_opus_encode$(EXEEXT)
	@p='tests/test_opus_encode$(EXEEXT)'; \
	b='tests/test_opus_encode'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/test_opus_padding.log: tests/test_opus_padding$(EXEEXT)
	@p='tests/test_opus_padding$(EXEEXT)'; \
	b='tests/test_opus_padding'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
tests/test_opus_projection.log: tests/test_opus_projection$(EXEEXT)
	@p='tests/test_opus_projection$(EXEEXT)'; \
	b='tests/test_opus_projection'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
.test.log:
	@p='$<'; \
	$(am__set_b); \
	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
@am__EXEEXT_TRUE@.test$(EXEEXT).log:
@am__EXEEXT_TRUE@	@p='$<'; \
@am__EXEEXT_TRUE@	$(am__set_b); \
@am__EXEEXT_TRUE@	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
@am__EXEEXT_TRUE@	--log-file $$b.log --trs-file $$b.trs \
@am__EXEEXT_TRUE@	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
@am__EXEEXT_TRUE@	"$$tst" $(AM_TESTS_FD_REDIRECT)

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	$(am__remove_distdir)
	test -d "$(distdir)" || mkdir "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
	$(MAKE) $(AM_MAKEFLAGS) \
	  top_distdir="$(top_distdir)" distdir="$(distdir)" \
	  dist-hook
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).tar.gz
	$(am__post_remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)

dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).shar.gz
	$(am__post_remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_build/sub $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build/sub \
	  && ../../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=../.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) dvi \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) check-TESTS
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-recursive
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES) $(DATA) $(HEADERS) \
		config.h all-local
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(m4datadir)" "$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(pkgincludedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:
	-test -z "$(TEST_LOGS)" || rm -f $(TEST_LOGS)
	-test -z "$(TEST_LOGS:.log=.trs)" || rm -f $(TEST_LOGS:.log=.trs)
	-test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f celt/$(DEPDIR)/$(am__dirstamp)
	-rm -f celt/$(am__dirstamp)
	-rm -f celt/arm/$(DEPDIR)/$(am__dirstamp)
	-rm -f celt/arm/$(am__dirstamp)
	-rm -f celt/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f celt/tests/$(am__dirstamp)
	-rm -f celt/x86/$(DEPDIR)/$(am__dirstamp)
	-rm -f celt/x86/$(am__dirstamp)
	-rm -f doc/$(DEPDIR)/$(am__dirstamp)
	-rm -f doc/$(am__dirstamp)
	-rm -f silk/$(DEPDIR)/$(am__dirstamp)
	-rm -f silk/$(am__dirstamp)
	-rm -f silk/arm/$(DEPDIR)/$(am__dirstamp)
	-rm -f silk/arm/$(am__dirstamp)
	-rm -f silk/fixed/$(DEPDIR)/$(am__dirstamp)
	-rm -f silk/fixed/$(am__dirstamp)
	-rm -f silk/fixed/arm/$(DEPDIR)/$(am__dirstamp)
	-rm -f silk/fixed/arm/$(am__dirstamp)
	-rm -f silk/fixed/x86/$(DEPDIR)/$(am__dirstamp)
	-rm -f silk/fixed/x86/$(am__dirstamp)
	-rm -f silk/float/$(DEPDIR)/$(am__dirstamp)
	-rm -f silk/float/$(am__dirstamp)
	-rm -f silk/tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f silk/tests/$(am__dirstamp)
	-rm -f silk/x86/$(DEPDIR)/$(am__dirstamp)
	-rm -f silk/x86/$(am__dirstamp)
	-rm -f src/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/$(am__dirstamp)
	-rm -f tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f tests/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-recursive

clean-am: clean-generic clean-libLTLIBRARIES clean-libtool clean-local \
	clean-noinstLTLIBRARIES clean-noinstPROGRAMS mostlyclean-am

distclean: distclean-recursive
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
		-rm -f celt/$(DEPDIR)/bands.Plo
	-rm -f celt/$(DEPDIR)/celt.Plo
	-rm -f celt/$(DEPDIR)/celt_decoder.Plo
	-rm -f celt/$(DEPDIR)/celt_encoder.Plo
	-rm -f celt/$(DEPDIR)/celt_lpc.Plo
	-rm -f celt/$(DEPDIR)/cwrs.Plo
	-rm -f celt/$(DEPDIR)/entcode.Plo
	-rm -f celt/$(DEPDIR)/entdec.Plo
	-rm -f celt/$(DEPDIR)/entenc.Plo
	-rm -f celt/$(DEPDIR)/kiss_fft.Plo
	-rm -f celt/$(DEPDIR)/laplace.Plo
	-rm -f celt/$(DEPDIR)/mathops.Plo
	-rm -f celt/$(DEPDIR)/mdct.Plo
	-rm -f celt/$(DEPDIR)/modes.Plo
	-rm -f celt/$(DEPDIR)/opus_custom_demo.Po
	-rm -f celt/$(DEPDIR)/pitch.Plo
	-rm -f celt/$(DEPDIR)/quant_bands.Plo
	-rm -f celt/$(DEPDIR)/rate.Plo
	-rm -f celt/$(DEPDIR)/vq.Plo
	-rm -f celt/arm/$(DEPDIR)/arm_celt_map.Plo
	-rm -f celt/arm/$(DEPDIR)/armcpu.Plo
	-rm -f celt/arm/$(DEPDIR)/celt_fft_ne10.Plo
	-rm -f celt/arm/$(DEPDIR)/celt_mdct_ne10.Plo
	-rm -f celt/arm/$(DEPDIR)/celt_neon_intr.Plo
	-rm -f celt/arm/$(DEPDIR)/celt_pitch_xcorr_arm-gnu.Plo
	-rm -f celt/arm/$(DEPDIR)/pitch_neon_intr.Plo
	-rm -f celt/tests/$(DEPDIR)/test_unit_cwrs32.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_dft.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_entropy.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_laplace.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_mathops.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_mdct.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_rotation.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_types.Po
	-rm -f celt/x86/$(DEPDIR)/celt_lpc_sse4_1.Plo
	-rm -f celt/x86/$(DEPDIR)/pitch_sse.Plo
	-rm -f celt/x86/$(DEPDIR)/pitch_sse2.Plo
	-rm -f celt/x86/$(DEPDIR)/pitch_sse4_1.Plo
	-rm -f celt/x86/$(DEPDIR)/vq_sse2.Plo
	-rm -f celt/x86/$(DEPDIR)/x86_celt_map.Plo
	-rm -f celt/x86/$(DEPDIR)/x86cpu.Plo
	-rm -f doc/$(DEPDIR)/trivial_example.Po
	-rm -f silk/$(DEPDIR)/A2NLSF.Plo
	-rm -f silk/$(DEPDIR)/CNG.Plo
	-rm -f silk/$(DEPDIR)/HP_variable_cutoff.Plo
	-rm -f silk/$(DEPDIR)/LPC_analysis_filter.Plo
	-rm -f silk/$(DEPDIR)/LPC_fit.Plo
	-rm -f silk/$(DEPDIR)/LPC_inv_pred_gain.Plo
	-rm -f silk/$(DEPDIR)/LP_variable_cutoff.Plo
	-rm -f silk/$(DEPDIR)/NLSF2A.Plo
	-rm -f silk/$(DEPDIR)/NLSF_VQ.Plo
	-rm -f silk/$(DEPDIR)/NLSF_VQ_weights_laroia.Plo
	-rm -f silk/$(DEPDIR)/NLSF_decode.Plo
	-rm -f silk/$(DEPDIR)/NLSF_del_dec_quant.Plo
	-rm -f silk/$(DEPDIR)/NLSF_encode.Plo
	-rm -f silk/$(DEPDIR)/NLSF_stabilize.Plo
	-rm -f silk/$(DEPDIR)/NLSF_unpack.Plo
	-rm -f silk/$(DEPDIR)/NSQ.Plo
	-rm -f silk/$(DEPDIR)/NSQ_del_dec.Plo
	-rm -f silk/$(DEPDIR)/PLC.Plo
	-rm -f silk/$(DEPDIR)/VAD.Plo
	-rm -f silk/$(DEPDIR)/VQ_WMat_EC.Plo
	-rm -f silk/$(DEPDIR)/ana_filt_bank_1.Plo
	-rm -f silk/$(DEPDIR)/biquad_alt.Plo
	-rm -f silk/$(DEPDIR)/bwexpander.Plo
	-rm -f silk/$(DEPDIR)/bwexpander_32.Plo
	-rm -f silk/$(DEPDIR)/check_control_input.Plo
	-rm -f silk/$(DEPDIR)/code_signs.Plo
	-rm -f silk/$(DEPDIR)/control_SNR.Plo
	-rm -f silk/$(DEPDIR)/control_audio_bandwidth.Plo
	-rm -f silk/$(DEPDIR)/control_codec.Plo
	-rm -f silk/$(DEPDIR)/debug.Plo
	-rm -f silk/$(DEPDIR)/dec_API.Plo
	-rm -f silk/$(DEPDIR)/decode_core.Plo
	-rm -f silk/$(DEPDIR)/decode_frame.Plo
	-rm -f silk/$(DEPDIR)/decode_indices.Plo
	-rm -f silk/$(DEPDIR)/decode_parameters.Plo
	-rm -f silk/$(DEPDIR)/decode_pitch.Plo
	-rm -f silk/$(DEPDIR)/decode_pulses.Plo
	-rm -f silk/$(DEPDIR)/decoder_set_fs.Plo
	-rm -f silk/$(DEPDIR)/enc_API.Plo
	-rm -f silk/$(DEPDIR)/encode_indices.Plo
	-rm -f silk/$(DEPDIR)/encode_pulses.Plo
	-rm -f silk/$(DEPDIR)/gain_quant.Plo
	-rm -f silk/$(DEPDIR)/init_decoder.Plo
	-rm -f silk/$(DEPDIR)/init_encoder.Plo
	-rm -f silk/$(DEPDIR)/inner_prod_aligned.Plo
	-rm -f silk/$(DEPDIR)/interpolate.Plo
	-rm -f silk/$(DEPDIR)/lin2log.Plo
	-rm -f silk/$(DEPDIR)/log2lin.Plo
	-rm -f silk/$(DEPDIR)/pitch_est_tables.Plo
	-rm -f silk/$(DEPDIR)/process_NLSFs.Plo
	-rm -f silk/$(DEPDIR)/quant_LTP_gains.Plo
	-rm -f silk/$(DEPDIR)/resampler.Plo
	-rm -f silk/$(DEPDIR)/resampler_down2.Plo
	-rm -f silk/$(DEPDIR)/resampler_down2_3.Plo
	-rm -f silk/$(DEPDIR)/resampler_private_AR2.Plo
	-rm -f silk/$(DEPDIR)/resampler_private_IIR_FIR.Plo
	-rm -f silk/$(DEPDIR)/resampler_private_down_FIR.Plo
	-rm -f silk/$(DEPDIR)/resampler_private_up2_HQ.Plo
	-rm -f silk/$(DEPDIR)/resampler_rom.Plo
	-rm -f silk/$(DEPDIR)/shell_coder.Plo
	-rm -f silk/$(DEPDIR)/sigm_Q15.Plo
	-rm -f silk/$(DEPDIR)/sort.Plo
	-rm -f silk/$(DEPDIR)/stereo_LR_to_MS.Plo
	-rm -f silk/$(DEPDIR)/stereo_MS_to_LR.Plo
	-rm -f silk/$(DEPDIR)/stereo_decode_pred.Plo
	-rm -f silk/$(DEPDIR)/stereo_encode_pred.Plo
	-rm -f silk/$(DEPDIR)/stereo_find_predictor.Plo
	-rm -f silk/$(DEPDIR)/stereo_quant_pred.Plo
	-rm -f silk/$(DEPDIR)/sum_sqr_shift.Plo
	-rm -f silk/$(DEPDIR)/table_LSF_cos.Plo
	-rm -f silk/$(DEPDIR)/tables_LTP.Plo
	-rm -f silk/$(DEPDIR)/tables_NLSF_CB_NB_MB.Plo
	-rm -f silk/$(DEPDIR)/tables_NLSF_CB_WB.Plo
	-rm -f silk/$(DEPDIR)/tables_gain.Plo
	-rm -f silk/$(DEPDIR)/tables_other.Plo
	-rm -f silk/$(DEPDIR)/tables_pitch_lag.Plo
	-rm -f silk/$(DEPDIR)/tables_pulses_per_block.Plo
	-rm -f silk/arm/$(DEPDIR)/LPC_inv_pred_gain_neon_intr.Plo
	-rm -f silk/arm/$(DEPDIR)/NSQ_del_dec_neon_intr.Plo
	-rm -f silk/arm/$(DEPDIR)/NSQ_neon.Plo
	-rm -f silk/arm/$(DEPDIR)/arm_silk_map.Plo
	-rm -f silk/arm/$(DEPDIR)/biquad_alt_neon_intr.Plo
	-rm -f silk/fixed/$(DEPDIR)/LTP_analysis_filter_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/LTP_scale_ctrl_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/apply_sine_window_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/autocorr_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/burg_modified_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/corrMatrix_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/encode_frame_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/find_LPC_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/find_LTP_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/find_pitch_lags_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/find_pred_coefs_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/k2a_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/k2a_Q16_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/noise_shape_analysis_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/pitch_analysis_core_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/process_gains_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/regularize_correlations_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/residual_energy16_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/residual_energy_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/schur64_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/schur_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/vector_ops_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/warped_autocorrelation_FIX.Plo
	-rm -f silk/fixed/arm/$(DEPDIR)/warped_autocorrelation_FIX_neon_intr.Plo
	-rm -f silk/fixed/x86/$(DEPDIR)/burg_modified_FIX_sse4_1.Plo
	-rm -f silk/fixed/x86/$(DEPDIR)/vector_ops_FIX_sse4_1.Plo
	-rm -f silk/float/$(DEPDIR)/LPC_analysis_filter_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/LPC_inv_pred_gain_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/LTP_analysis_filter_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/LTP_scale_ctrl_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/apply_sine_window_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/autocorrelation_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/burg_modified_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/bwexpander_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/corrMatrix_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/encode_frame_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/energy_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/find_LPC_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/find_LTP_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/find_pitch_lags_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/find_pred_coefs_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/inner_product_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/k2a_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/noise_shape_analysis_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/pitch_analysis_core_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/process_gains_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/regularize_correlations_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/residual_energy_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/scale_copy_vector_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/scale_vector_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/schur_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/sort_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/warped_autocorrelation_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/wrappers_FLP.Plo
	-rm -f silk/tests/$(DEPDIR)/test_unit_LPC_inv_pred_gain.Po
	-rm -f silk/x86/$(DEPDIR)/NSQ_del_dec_sse4_1.Plo
	-rm -f silk/x86/$(DEPDIR)/NSQ_sse4_1.Plo
	-rm -f silk/x86/$(DEPDIR)/VAD_sse4_1.Plo
	-rm -f silk/x86/$(DEPDIR)/VQ_WMat_EC_sse4_1.Plo
	-rm -f silk/x86/$(DEPDIR)/x86_silk_map.Plo
	-rm -f src/$(DEPDIR)/analysis.Plo
	-rm -f src/$(DEPDIR)/mapping_matrix.Plo
	-rm -f src/$(DEPDIR)/mlp.Plo
	-rm -f src/$(DEPDIR)/mlp_data.Plo
	-rm -f src/$(DEPDIR)/opus.Plo
	-rm -f src/$(DEPDIR)/opus_compare.Po
	-rm -f src/$(DEPDIR)/opus_decoder.Plo
	-rm -f src/$(DEPDIR)/opus_demo.Po
	-rm -f src/$(DEPDIR)/opus_encoder.Plo
	-rm -f src/$(DEPDIR)/opus_multistream.Plo
	-rm -f src/$(DEPDIR)/opus_multistream_decoder.Plo
	-rm -f src/$(DEPDIR)/opus_multistream_encoder.Plo
	-rm -f src/$(DEPDIR)/opus_projection_decoder.Plo
	-rm -f src/$(DEPDIR)/opus_projection_encoder.Plo
	-rm -f src/$(DEPDIR)/repacketizer.Plo
	-rm -f src/$(DEPDIR)/repacketizer_demo.Po
	-rm -f tests/$(DEPDIR)/opus_encode_regressions.Po
	-rm -f tests/$(DEPDIR)/test_opus_api.Po
	-rm -f tests/$(DEPDIR)/test_opus_decode.Po
	-rm -f tests/$(DEPDIR)/test_opus_encode.Po
	-rm -f tests/$(DEPDIR)/test_opus_padding.Po
	-rm -f tests/$(DEPDIR)/test_opus_projection.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-libtool distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am: install-data-local install-m4dataDATA \
	install-pkgconfigDATA install-pkgincludeHEADERS

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am: install-libLTLIBRARIES

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
		-rm -f celt/$(DEPDIR)/bands.Plo
	-rm -f celt/$(DEPDIR)/celt.Plo
	-rm -f celt/$(DEPDIR)/celt_decoder.Plo
	-rm -f celt/$(DEPDIR)/celt_encoder.Plo
	-rm -f celt/$(DEPDIR)/celt_lpc.Plo
	-rm -f celt/$(DEPDIR)/cwrs.Plo
	-rm -f celt/$(DEPDIR)/entcode.Plo
	-rm -f celt/$(DEPDIR)/entdec.Plo
	-rm -f celt/$(DEPDIR)/entenc.Plo
	-rm -f celt/$(DEPDIR)/kiss_fft.Plo
	-rm -f celt/$(DEPDIR)/laplace.Plo
	-rm -f celt/$(DEPDIR)/mathops.Plo
	-rm -f celt/$(DEPDIR)/mdct.Plo
	-rm -f celt/$(DEPDIR)/modes.Plo
	-rm -f celt/$(DEPDIR)/opus_custom_demo.Po
	-rm -f celt/$(DEPDIR)/pitch.Plo
	-rm -f celt/$(DEPDIR)/quant_bands.Plo
	-rm -f celt/$(DEPDIR)/rate.Plo
	-rm -f celt/$(DEPDIR)/vq.Plo
	-rm -f celt/arm/$(DEPDIR)/arm_celt_map.Plo
	-rm -f celt/arm/$(DEPDIR)/armcpu.Plo
	-rm -f celt/arm/$(DEPDIR)/celt_fft_ne10.Plo
	-rm -f celt/arm/$(DEPDIR)/celt_mdct_ne10.Plo
	-rm -f celt/arm/$(DEPDIR)/celt_neon_intr.Plo
	-rm -f celt/arm/$(DEPDIR)/celt_pitch_xcorr_arm-gnu.Plo
	-rm -f celt/arm/$(DEPDIR)/pitch_neon_intr.Plo
	-rm -f celt/tests/$(DEPDIR)/test_unit_cwrs32.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_dft.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_entropy.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_laplace.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_mathops.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_mdct.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_rotation.Po
	-rm -f celt/tests/$(DEPDIR)/test_unit_types.Po
	-rm -f celt/x86/$(DEPDIR)/celt_lpc_sse4_1.Plo
	-rm -f celt/x86/$(DEPDIR)/pitch_sse.Plo
	-rm -f celt/x86/$(DEPDIR)/pitch_sse2.Plo
	-rm -f celt/x86/$(DEPDIR)/pitch_sse4_1.Plo
	-rm -f celt/x86/$(DEPDIR)/vq_sse2.Plo
	-rm -f celt/x86/$(DEPDIR)/x86_celt_map.Plo
	-rm -f celt/x86/$(DEPDIR)/x86cpu.Plo
	-rm -f doc/$(DEPDIR)/trivial_example.Po
	-rm -f silk/$(DEPDIR)/A2NLSF.Plo
	-rm -f silk/$(DEPDIR)/CNG.Plo
	-rm -f silk/$(DEPDIR)/HP_variable_cutoff.Plo
	-rm -f silk/$(DEPDIR)/LPC_analysis_filter.Plo
	-rm -f silk/$(DEPDIR)/LPC_fit.Plo
	-rm -f silk/$(DEPDIR)/LPC_inv_pred_gain.Plo
	-rm -f silk/$(DEPDIR)/LP_variable_cutoff.Plo
	-rm -f silk/$(DEPDIR)/NLSF2A.Plo
	-rm -f silk/$(DEPDIR)/NLSF_VQ.Plo
	-rm -f silk/$(DEPDIR)/NLSF_VQ_weights_laroia.Plo
	-rm -f silk/$(DEPDIR)/NLSF_decode.Plo
	-rm -f silk/$(DEPDIR)/NLSF_del_dec_quant.Plo
	-rm -f silk/$(DEPDIR)/NLSF_encode.Plo
	-rm -f silk/$(DEPDIR)/NLSF_stabilize.Plo
	-rm -f silk/$(DEPDIR)/NLSF_unpack.Plo
	-rm -f silk/$(DEPDIR)/NSQ.Plo
	-rm -f silk/$(DEPDIR)/NSQ_del_dec.Plo
	-rm -f silk/$(DEPDIR)/PLC.Plo
	-rm -f silk/$(DEPDIR)/VAD.Plo
	-rm -f silk/$(DEPDIR)/VQ_WMat_EC.Plo
	-rm -f silk/$(DEPDIR)/ana_filt_bank_1.Plo
	-rm -f silk/$(DEPDIR)/biquad_alt.Plo
	-rm -f silk/$(DEPDIR)/bwexpander.Plo
	-rm -f silk/$(DEPDIR)/bwexpander_32.Plo
	-rm -f silk/$(DEPDIR)/check_control_input.Plo
	-rm -f silk/$(DEPDIR)/code_signs.Plo
	-rm -f silk/$(DEPDIR)/control_SNR.Plo
	-rm -f silk/$(DEPDIR)/control_audio_bandwidth.Plo
	-rm -f silk/$(DEPDIR)/control_codec.Plo
	-rm -f silk/$(DEPDIR)/debug.Plo
	-rm -f silk/$(DEPDIR)/dec_API.Plo
	-rm -f silk/$(DEPDIR)/decode_core.Plo
	-rm -f silk/$(DEPDIR)/decode_frame.Plo
	-rm -f silk/$(DEPDIR)/decode_indices.Plo
	-rm -f silk/$(DEPDIR)/decode_parameters.Plo
	-rm -f silk/$(DEPDIR)/decode_pitch.Plo
	-rm -f silk/$(DEPDIR)/decode_pulses.Plo
	-rm -f silk/$(DEPDIR)/decoder_set_fs.Plo
	-rm -f silk/$(DEPDIR)/enc_API.Plo
	-rm -f silk/$(DEPDIR)/encode_indices.Plo
	-rm -f silk/$(DEPDIR)/encode_pulses.Plo
	-rm -f silk/$(DEPDIR)/gain_quant.Plo
	-rm -f silk/$(DEPDIR)/init_decoder.Plo
	-rm -f silk/$(DEPDIR)/init_encoder.Plo
	-rm -f silk/$(DEPDIR)/inner_prod_aligned.Plo
	-rm -f silk/$(DEPDIR)/interpolate.Plo
	-rm -f silk/$(DEPDIR)/lin2log.Plo
	-rm -f silk/$(DEPDIR)/log2lin.Plo
	-rm -f silk/$(DEPDIR)/pitch_est_tables.Plo
	-rm -f silk/$(DEPDIR)/process_NLSFs.Plo
	-rm -f silk/$(DEPDIR)/quant_LTP_gains.Plo
	-rm -f silk/$(DEPDIR)/resampler.Plo
	-rm -f silk/$(DEPDIR)/resampler_down2.Plo
	-rm -f silk/$(DEPDIR)/resampler_down2_3.Plo
	-rm -f silk/$(DEPDIR)/resampler_private_AR2.Plo
	-rm -f silk/$(DEPDIR)/resampler_private_IIR_FIR.Plo
	-rm -f silk/$(DEPDIR)/resampler_private_down_FIR.Plo
	-rm -f silk/$(DEPDIR)/resampler_private_up2_HQ.Plo
	-rm -f silk/$(DEPDIR)/resampler_rom.Plo
	-rm -f silk/$(DEPDIR)/shell_coder.Plo
	-rm -f silk/$(DEPDIR)/sigm_Q15.Plo
	-rm -f silk/$(DEPDIR)/sort.Plo
	-rm -f silk/$(DEPDIR)/stereo_LR_to_MS.Plo
	-rm -f silk/$(DEPDIR)/stereo_MS_to_LR.Plo
	-rm -f silk/$(DEPDIR)/stereo_decode_pred.Plo
	-rm -f silk/$(DEPDIR)/stereo_encode_pred.Plo
	-rm -f silk/$(DEPDIR)/stereo_find_predictor.Plo
	-rm -f silk/$(DEPDIR)/stereo_quant_pred.Plo
	-rm -f silk/$(DEPDIR)/sum_sqr_shift.Plo
	-rm -f silk/$(DEPDIR)/table_LSF_cos.Plo
	-rm -f silk/$(DEPDIR)/tables_LTP.Plo
	-rm -f silk/$(DEPDIR)/tables_NLSF_CB_NB_MB.Plo
	-rm -f silk/$(DEPDIR)/tables_NLSF_CB_WB.Plo
	-rm -f silk/$(DEPDIR)/tables_gain.Plo
	-rm -f silk/$(DEPDIR)/tables_other.Plo
	-rm -f silk/$(DEPDIR)/tables_pitch_lag.Plo
	-rm -f silk/$(DEPDIR)/tables_pulses_per_block.Plo
	-rm -f silk/arm/$(DEPDIR)/LPC_inv_pred_gain_neon_intr.Plo
	-rm -f silk/arm/$(DEPDIR)/NSQ_del_dec_neon_intr.Plo
	-rm -f silk/arm/$(DEPDIR)/NSQ_neon.Plo
	-rm -f silk/arm/$(DEPDIR)/arm_silk_map.Plo
	-rm -f silk/arm/$(DEPDIR)/biquad_alt_neon_intr.Plo
	-rm -f silk/fixed/$(DEPDIR)/LTP_analysis_filter_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/LTP_scale_ctrl_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/apply_sine_window_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/autocorr_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/burg_modified_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/corrMatrix_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/encode_frame_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/find_LPC_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/find_LTP_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/find_pitch_lags_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/find_pred_coefs_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/k2a_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/k2a_Q16_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/noise_shape_analysis_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/pitch_analysis_core_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/process_gains_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/regularize_correlations_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/residual_energy16_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/residual_energy_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/schur64_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/schur_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/vector_ops_FIX.Plo
	-rm -f silk/fixed/$(DEPDIR)/warped_autocorrelation_FIX.Plo
	-rm -f silk/fixed/arm/$(DEPDIR)/warped_autocorrelation_FIX_neon_intr.Plo
	-rm -f silk/fixed/x86/$(DEPDIR)/burg_modified_FIX_sse4_1.Plo
	-rm -f silk/fixed/x86/$(DEPDIR)/vector_ops_FIX_sse4_1.Plo
	-rm -f silk/float/$(DEPDIR)/LPC_analysis_filter_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/LPC_inv_pred_gain_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/LTP_analysis_filter_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/LTP_scale_ctrl_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/apply_sine_window_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/autocorrelation_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/burg_modified_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/bwexpander_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/corrMatrix_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/encode_frame_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/energy_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/find_LPC_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/find_LTP_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/find_pitch_lags_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/find_pred_coefs_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/inner_product_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/k2a_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/noise_shape_analysis_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/pitch_analysis_core_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/process_gains_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/regularize_correlations_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/residual_energy_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/scale_copy_vector_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/scale_vector_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/schur_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/sort_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/warped_autocorrelation_FLP.Plo
	-rm -f silk/float/$(DEPDIR)/wrappers_FLP.Plo
	-rm -f silk/tests/$(DEPDIR)/test_unit_LPC_inv_pred_gain.Po
	-rm -f silk/x86/$(DEPDIR)/NSQ_del_dec_sse4_1.Plo
	-rm -f silk/x86/$(DEPDIR)/NSQ_sse4_1.Plo
	-rm -f silk/x86/$(DEPDIR)/VAD_sse4_1.Plo
	-rm -f silk/x86/$(DEPDIR)/VQ_WMat_EC_sse4_1.Plo
	-rm -f silk/x86/$(DEPDIR)/x86_silk_map.Plo
	-rm -f src/$(DEPDIR)/analysis.Plo
	-rm -f src/$(DEPDIR)/mapping_matrix.Plo
	-rm -f src/$(DEPDIR)/mlp.Plo
	-rm -f src/$(DEPDIR)/mlp_data.Plo
	-rm -f src/$(DEPDIR)/opus.Plo
	-rm -f src/$(DEPDIR)/opus_compare.Po
	-rm -f src/$(DEPDIR)/opus_decoder.Plo
	-rm -f src/$(DEPDIR)/opus_demo.Po
	-rm -f src/$(DEPDIR)/opus_encoder.Plo
	-rm -f src/$(DEPDIR)/opus_multistream.Plo
	-rm -f src/$(DEPDIR)/opus_multistream_decoder.Plo
	-rm -f src/$(DEPDIR)/opus_multistream_encoder.Plo
	-rm -f src/$(DEPDIR)/opus_projection_decoder.Plo
	-rm -f src/$(DEPDIR)/opus_projection_encoder.Plo
	-rm -f src/$(DEPDIR)/repacketizer.Plo
	-rm -f src/$(DEPDIR)/repacketizer_demo.Po
	-rm -f tests/$(DEPDIR)/opus_encode_regressions.Po
	-rm -f tests/$(DEPDIR)/test_opus_api.Po
	-rm -f tests/$(DEPDIR)/test_opus_decode.Po
	-rm -f tests/$(DEPDIR)/test_opus_encode.Po
	-rm -f tests/$(DEPDIR)/test_opus_padding.Po
	-rm -f tests/$(DEPDIR)/test_opus_projection.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-libLTLIBRARIES uninstall-local \
	uninstall-m4dataDATA uninstall-pkgconfigDATA \
	uninstall-pkgincludeHEADERS

.MAKE: $(am__recursive_targets) all check check-am install install-am \
	install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am all-local \
	am--depfiles am--refresh check check-TESTS check-am clean \
	clean-cscope clean-generic clean-libLTLIBRARIES clean-libtool \
	clean-local clean-noinstLTLIBRARIES clean-noinstPROGRAMS \
	cscope cscopelist-am ctags ctags-am dist dist-all dist-bzip2 \
	dist-gzip dist-hook dist-lzip dist-shar dist-tarZ dist-xz \
	dist-zip distcheck distclean distclean-compile \
	distclean-generic distclean-hdr distclean-libtool \
	distclean-tags distcleancheck distdir distuninstallcheck dvi \
	dvi-am html html-am info info-am install install-am \
	install-data install-data-am install-data-local install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am \
	install-libLTLIBRARIES install-m4dataDATA install-man \
	install-pdf install-pdf-am install-pkgconfigDATA \
	install-pkgincludeHEADERS install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	installdirs-am maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am recheck tags tags-am \
	uninstall uninstall-am uninstall-libLTLIBRARIES \
	uninstall-local uninstall-m4dataDATA uninstall-pkgconfigDATA \
	uninstall-pkgincludeHEADERS

.PRECIOUS: Makefile


# Provide the full test output for failed tests when using the parallel
# test suite (which is enabled by default with automake 1.13+).
export VERBOSE = yes

# Targets to build and install just the library without the docs
opus check-opus install-opus: export NO_DOXYGEN = 1

opus: all
check-opus: check
install-opus: install

# Or just the docs
docs:
	( cd doc && $(MAKE) $(AM_MAKEFLAGS) )

install-docs:
	( cd doc && $(MAKE) $(AM_MAKEFLAGS) install )

# Or everything (by default)
all-local:
	@[ -n "$(NO_DOXYGEN)" ] || ( cd doc && $(MAKE) $(AM_MAKEFLAGS) )

install-data-local:
	@[ -n "$(NO_DOXYGEN)" ] || ( cd doc && $(MAKE) $(AM_MAKEFLAGS) install )

clean-local:
	-( cd doc && $(MAKE) $(AM_MAKEFLAGS) clean )

uninstall-local:
	( cd doc && $(MAKE) $(AM_MAKEFLAGS) uninstall )

# We check this every time make is run, with configure.ac being touched to
# trigger an update of the build system files if update_version changes the
# current PACKAGE_VERSION (or if package_version was modified manually by a
# user with either AUTO_UPDATE=no or no update_version script present - the
# latter being the normal case for tarball releases).
#
# We can't just add the package_version file to CONFIGURE_DEPENDENCIES since
# simply running autoconf will not actually regenerate configure for us when
# the content of that file changes (due to autoconf dependency checking not
# knowing about that without us creating yet another file for it to include).
#
# The MAKECMDGOALS check is a gnu-make'ism, but will degrade 'gracefully' for
# makes that don't support it.  The only loss of functionality is not forcing
# an update of package_version for `make dist` if AUTO_UPDATE=no, but that is
# unlikely to be a real problem for any real user.
$(top_srcdir)/configure.ac: force
	@case "$(MAKECMDGOALS)" in \
	    dist-hook)                             exit 0       ;; \
	    dist-* | dist | distcheck | distclean) _arg=release ;; \
	esac; \
	if ! $(top_srcdir)/update_version $$_arg 2> /dev/null; then \
	    if [ ! -e $(top_srcdir)/package_version ]; then \
		echo 'PACKAGE_VERSION="unknown"' > $(top_srcdir)/package_version; \
	    fi; \
	    . $(top_srcdir)/package_version || exit 1; \
	    [ "$(PACKAGE_VERSION)" != "$$PACKAGE_VERSION" ] || exit 0; \
	fi; \
	touch $@

force:

# Create a minimal package_version file when make dist is run.
dist-hook:
	echo 'PACKAGE_VERSION="$(PACKAGE_VERSION)"' > $(top_distdir)/package_version

.PHONY: opus check-opus install-opus docs install-docs

# automake doesn't do dependency tracking for asm files, that I can tell
$(CELT_SOURCES_ARM_ASM:%.s=%-gnu.S): celt/arm/armopts-gnu.S
$(CELT_SOURCES_ARM_ASM:%.s=%-gnu.S): $(top_srcdir)/celt/arm/arm2gnu.pl

# convert ARM asm to GNU as format
%-gnu.S: $(top_srcdir)/%.s
	$(top_srcdir)/celt/arm/arm2gnu.pl @ARM2GNU_PARAMS@ < $< > $@
# For autoconf-modified sources (e.g., armopts.s)
%-gnu.S: %.s
	$(top_srcdir)/celt/arm/arm2gnu.pl @ARM2GNU_PARAMS@ < $< > $@
@HAVE_SSE_TRUE@$(SSE_OBJ): CFLAGS += $(OPUS_X86_SSE_CFLAGS)
@HAVE_SSE2_TRUE@$(SSE2_OBJ): CFLAGS += $(OPUS_X86_SSE2_CFLAGS)
@HAVE_SSE4_1_TRUE@$(SSE4_1_OBJ): CFLAGS += $(OPUS_X86_SSE4_1_CFLAGS)
@HAVE_ARM_NEON_INTR_TRUE@$(ARM_NEON_INTR_OBJ): CFLAGS += \
@HAVE_ARM_NEON_INTR_TRUE@ $(OPUS_ARM_NEON_INTR_CFLAGS)  $(NE10_CFLAGS)

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
