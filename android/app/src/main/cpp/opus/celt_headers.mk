CELT_HEAD = \
celt/arch.h \
celt/bands.h \
celt/celt.h \
celt/cpu_support.h \
include/opus_types.h \
include/opus_defines.h \
include/opus_custom.h \
celt/cwrs.h \
celt/ecintrin.h \
celt/entcode.h \
celt/entdec.h \
celt/entenc.h \
celt/fixed_debug.h \
celt/fixed_generic.h \
celt/float_cast.h \
celt/_kiss_fft_guts.h \
celt/kiss_fft.h \
celt/laplace.h \
celt/mathops.h \
celt/mdct.h \
celt/mfrngcod.h \
celt/modes.h \
celt/os_support.h \
celt/pitch.h \
celt/celt_lpc.h \
celt/x86/celt_lpc_sse.h \
celt/quant_bands.h \
celt/rate.h \
celt/stack_alloc.h \
celt/vq.h \
celt/static_modes_float.h \
celt/static_modes_fixed.h \
celt/static_modes_float_arm_ne10.h \
celt/static_modes_fixed_arm_ne10.h \
celt/arm/armcpu.h \
celt/arm/fixed_armv4.h \
celt/arm/fixed_armv5e.h \
celt/arm/fixed_arm64.h \
celt/arm/kiss_fft_armv4.h \
celt/arm/kiss_fft_armv5e.h \
celt/arm/pitch_arm.h \
celt/arm/fft_arm.h \
celt/arm/mdct_arm.h \
celt/mips/celt_mipsr1.h \
celt/mips/fixed_generic_mipsr1.h \
celt/mips/kiss_fft_mipsr1.h \
celt/mips/mdct_mipsr1.h \
celt/mips/pitch_mipsr1.h \
celt/mips/vq_mipsr1.h \
celt/x86/pitch_sse.h \
celt/x86/vq_sse.h \
celt/x86/x86cpu.h
