package com.bfdx.bf8100deviceapp

import android.annotation.SuppressLint
import android.bluetooth.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import android.os.Handler
import android.os.Looper
import android.os.Parcelable
import android.util.Log
import android.view.KeyEvent
import java.io.IOException
import java.io.InputStream
import java.lang.Thread.sleep
import java.util.*

class BluetoothSPPManager(proxyService: ProxyService) {
    private val TAG: String = "${TagPrefix}BleSPPManager"
    private val alreadyConnectNames = ArrayList<String>()
    private val alreadyConnectedNames = ArrayList<String>()
    private var mA2dp: BluetoothA2dp? = null
    private val service: ProxyService = proxyService
    private var socket: BluetoothSocket? = null
    private var mPortvalue = 1
    private var GGADataSendSum: Byte = 0
    private var lastConnectingBlueToothDevice: BluetoothDevice? = null
    private val bluetoothAdapter: BluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
    private var audioManager: AudioManager = proxyService.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    // 蓝牙耳机是否连接到POC终端
    private var isConnectedBluetoothDevice = false
    private var receiver: BroadcastReceiver = object : BroadcastReceiver() {
        // from class: com.corget.engine.MyBluetoothSPPManager.1
        // android.content.BroadcastReceiver
        @SuppressLint("MissingPermission")
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            Log.d(TAG, "onReceive:$action")
            when (action) {
                BluetoothDevice.ACTION_ACL_CONNECTED -> {
                    val device = intent.getParcelableExtra<Parcelable>(BluetoothDevice.EXTRA_DEVICE) as BluetoothDevice?
                    if (device != null && device.name != null) {
                        Log.d(TAG, "Connected Device:" + device.name + "|" + device.address)
                        isConnectedBluetoothDevice = true
                        if (alreadyConnectNames.contains(device.name)) {
                            return
                        }

                        sleep(2000)
                        connectedDevice(device)
                        return
                    }
                }

                BluetoothDevice.ACTION_ACL_DISCONNECTED -> {
                    isConnectedBluetoothDevice = false
                    closeSocket(socket, lastConnectingBlueToothDevice?.name ?: "DISCONNECTED")
                    updateBluetoothSco()
                }

                BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED -> {
                    val state = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, 0)
                    Log.i(TAG, "a2dp state:$state")
                }

                BluetoothA2dp.ACTION_PLAYING_STATE_CHANGED -> {
                    val state2 = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, 11)
                    Log.i(TAG, "a2dp play state:$state2")
                }

                AudioManager.ACTION_SCO_AUDIO_STATE_UPDATED -> {
                    val state = intent.getIntExtra(AudioManager.EXTRA_SCO_AUDIO_STATE, -1)
                    Log.i(TAG, "EXTRA_SCO_AUDIO_STATE: $state")
                    BluetoothUtil.updateScoState(state)
                    when (state) {
                        AudioManager.SCO_AUDIO_STATE_DISCONNECTED -> {
                            Log.i(TAG, "sco未连接, isBluetoothScoOn=${audioManager.isBluetoothScoOn}")
                        }

                        AudioManager.SCO_AUDIO_STATE_CONNECTED -> {
                            Log.d(TAG, "sco已连接")
                        }

                        AudioManager.SCO_AUDIO_STATE_CONNECTING -> {
                            Log.i(TAG, "sco正在连接")
                        }

                        AudioManager.SCO_AUDIO_STATE_ERROR -> {
                            Log.i(TAG, "sco连接错误")
                        }
                    }
                }
            }
        }
    }
    private val bluetoothProfileListener: BluetoothProfile.ServiceListener = object : BluetoothProfile.ServiceListener {
        // from class: com.corget.engine.MyBluetoothSPPManager.2
        // android.bluetooth.BluetoothProfile.ServiceListener
        override fun onServiceDisconnected(profile: Int) {
            Log.i(TAG, "onServiceDisconnected profile=$profile")
            if (profile == 2) {
                mA2dp = null
            }
        }

        // android.bluetooth.BluetoothProfile.ServiceListener
        override fun onServiceConnected(profile: Int, proxy: BluetoothProfile) {
            Log.i(TAG, "onServiceConnected profile=$profile")
            if (profile == 2) {
                mA2dp = proxy as BluetoothA2dp
            }
        }
    }

    init {
        Log.d(TAG, "MyBluetoothSPPManager init")
        BluetoothSPPManager.instance = this

        val intentFilter = IntentFilter()
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED)
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
        intentFilter.addAction(BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED)
        intentFilter.addAction(BluetoothA2dp.ACTION_PLAYING_STATE_CHANGED)
        intentFilter.addAction(AudioManager.ACTION_SCO_AUDIO_STATE_UPDATED)
        intentFilter.priority = Int.MAX_VALUE
        service.registerReceiver(receiver, intentFilter)
        bluetoothAdapter.getProfileProxy(service, bluetoothProfileListener, BluetoothProfile.A2DP)

        isConnectedBluetoothDevice = BluetoothUtil.isBluetoothConnected(service)
        if (isConnectedBluetoothDevice) {
            tryConnectDevice();
        }
    }

    companion object {
        private val SPP_UUID: String = "00001101-0000-1000-8000-00805F9B34FB"
        lateinit var instance: BluetoothSPPManager
    }

    fun updateAudioMode(context: Context, forceSco: Boolean = false) {
        if (forceSco) {
            audioManager.let {
                it.mode = AudioManager.MODE_IN_COMMUNICATION
                it.startBluetoothSco()
                it.isBluetoothScoOn = true
                it.isSpeakerphoneOn = false
            }
        } else {
            audioManager.let {
                it.stopBluetoothSco()
                it.isBluetoothScoOn = false
                it.isSpeakerphoneOn = true
                it.mode = AudioManager.MODE_NORMAL
            }
        }
    }

    fun updateBluetoothSco() {
        Log.d(TAG, "updateBluetoothSco: isConnected=$isConnectedBluetoothDevice, isBluetoothScoOn=${audioManager.isBluetoothScoOn}, isScoConnected=${BluetoothUtil.isScoConnected()}")
        // 蓝牙耳机已连接，且sco未连接，则设置sco
        if (isConnectedBluetoothDevice) {
            if (!audioManager.isBluetoothScoOn || !BluetoothUtil.isScoConnected()) {
                updateAudioMode(service, true)
            }
            return
        }

        // 如果蓝牙已断开，则断开sco
        updateAudioMode(service, false)
    }

    fun addConnectName(name: String?) {
        alreadyConnectNames.add(name!!)
    }

    fun removeConnectName(name: String?) {
        alreadyConnectNames.remove(name)
    }

    @SuppressLint("MissingPermission")
    fun logDeviceType(device: BluetoothDevice) {
        val deviceClass = device.bluetoothClass.majorDeviceClass
        Log.i(TAG, "name:" + device.name)
        Log.i(TAG, "type:$deviceClass")
        when (deviceClass) {
            256 -> Log.i(TAG, "type:平板电脑")
            512 -> Log.i(TAG, "type:手机")
            else -> Log.i(TAG, "type:其他类型")
        }
    }

    @SuppressLint("MissingPermission")
    fun connectedDevice(device: BluetoothDevice) {
        Log.d(TAG, "connectedDevice:${device.name}")
        if (!device.name.startsWith("DellKing")) {
            return
        }

        lastConnectingBlueToothDevice = device
        logDeviceType(device)
        mPortvalue = 1
        addConnectName(device.name)
        try {
            val m = device.javaClass.getMethod("createInsecureRfcommSocketToServiceRecord", UUID::class.java)
            val socket = m.invoke(device, UUID.fromString(SPP_UUID)) as BluetoothSocket
            ConnectThread(device.name, socket, device).start()
        } catch (e: Exception) {
            Log.d(TAG, "获取Socket失败:" + device.name + "," + e.message)
            removeConnectName(device.name)
        }
    }

    fun tryConnectDevice() {
        val isEnabled = bluetoothAdapter.isEnabled
        Log.d(TAG, "tryConnectDevice: bluetoothAdapter isEnabled=$isEnabled")
        if (!isEnabled) {
            return
        }

        val connectedBluetoothDevice: BluetoothDevice? = BluetoothUtil.getConnectedDevice()
        if (connectedBluetoothDevice != null) {
            connectedDevice(connectedBluetoothDevice)
            return
        }

        val specialBluetoothDevice: BluetoothDevice? = BluetoothUtil.getLastConnectedDeviceByName("DellKing")
        if (specialBluetoothDevice != null) {
            connectedDevice(specialBluetoothDevice)
            return
        }

        if (lastConnectingBlueToothDevice != null) {
            connectedDevice(lastConnectingBlueToothDevice!!)
        }
    }

    @SuppressLint("MissingPermission")
    fun reConnectBluetoothDevice(device: BluetoothDevice, value: Int) {
        try {
            Log.i(TAG, "reConnectBluetoothDevice:$value:$device")
            val socket = device.javaClass.getMethod("createRfcommSocket", Integer.TYPE).invoke(device, value) as BluetoothSocket
            ConnectThread(device.name, socket, device).start()
        } catch (e: java.lang.Exception) {
            Log.i(TAG, "reConnectBluetoothDevice:" + e.message)
            closeAllConnections()
        }
    }

    fun closeSocket(socket: BluetoothSocket?, name: String) {
        if (socket != null) {
            try {
                socket.close()
            } catch (ex: IOException) {
                Log.d(TAG, "closeSocket catch error:" + name + "," + ex.message)
            } finally {
                this.socket = null
            }
        }
    }

    fun closeInputStream(inStream: InputStream?, name: String) {
        if (inStream != null) {
            try {
                inStream.close()
            } catch (ex: IOException) {
                Log.d(TAG, "closeInputStream catch error:" + name + "," + ex.message)
            }
        }
    }

    @SuppressLint("MissingPermission")
    fun closeAllConnections() {
        alreadyConnectNames.clear()
        alreadyConnectedNames.clear()
        mPortvalue = 1
        closeSocket(socket, "cleanup")
        socket = null
    }

    @SuppressLint("MissingPermission")
    fun SPPscan() {
        if (!bluetoothAdapter.isEnabled) {
            return
        }

        cancelSPPScan()

        Log.d(TAG, "SPPscan startDiscovery")
        bluetoothAdapter.startDiscovery()
    }

    @SuppressLint("MissingPermission")
    fun cancelSPPScan() {
        if (!bluetoothAdapter.isDiscovering()) {
            return
        }
        Log.d(TAG, "cancelSPPScan cancelDiscovery")
        bluetoothAdapter.cancelDiscovery()
    }

    class ConnectThread(private val name: String, private val socket: BluetoothSocket, private val device: BluetoothDevice) : Thread() {
        private val TAG: String = "${TagPrefix}BleConnectThread"

        // java.lang.Thread, java.lang.Runnable
        @SuppressLint("MissingPermission")
        override fun run() {
            try {
                Log.d(TAG, "ConnectThread run, bondState=${device.bondState}")
                // Cancel discovery as it interferes with connection
                BluetoothSPPManager.instance.apply {
                    cancelSPPScan()
                }
                socket.connect()
                Log.d(TAG, "连接成功: $name")
                BluetoothSPPManager.instance.apply {
                    this.socket = <EMAIL>
                    alreadyConnectedNames.add(name)
                    mPortvalue = 1
                    GGADataSendSum = 0
                    isConnectedBluetoothDevice = true
                    sleep(500)
                    updateBluetoothSco()
                }
                ReadThread(name, socket).start()
            } catch (e: IOException) {
                Log.d(TAG, "连接失败: $name, ${e.message}")
                // Clean up failed connection
                BluetoothSPPManager.instance.apply {
                    closeSocket(socket, name)
                    removeConnectName(name)
                    alreadyConnectedNames.remove(name)

                    if (mPortvalue < 10) {
                        // 等待几秒后再重连
                        sleep(6000)
                        reConnectBluetoothDevice(device, mPortvalue)
                        mPortvalue++
                    } else {
                        closeAllConnections()
                    }
                }
            }
        }
    }

    class ReadThread(private val name: String, private val socket: BluetoothSocket) : Thread() {
        private val TAG: String = "${TagPrefix}BleReadThread"

        // java.lang.Thread, java.lang.Runnable
        override fun run() {
            try {
                Log.i(TAG, "ReadThread:isRunning")
                val inStream: InputStream = socket.getInputStream()
                val buffer = ByteArray(1024)
                while (true) {
                    try {
                        val size = inStream.read(buffer)
                        val data = ArrayList<String>()
                        for (i in 0..<size) {
                            data.add(StringBuilder(buffer[i].toInt().toString()).toString())
                        }
                        Log.i(TAG, "read buffer: size=${size}, data=$data")
                        val msg = String(buffer, 0, size)
                        Log.d(TAG, "read from: $name, $msg")
                        handleMsg(msg)
                    } catch (e: IOException) {
                        Log.d(TAG, "read catch error:" + name + "," + e.message)
                        BluetoothSPPManager.instance.apply {
                            closeInputStream(inStream, name)
                            closeSocket(socket, name)
                            removeConnectName(name)
                            alreadyConnectedNames.remove(name)
                        }
                        return
                    }
                }
            } catch (e2: java.lang.Exception) {
                Log.e(TAG, "get input stream catch error:" + name + "," + e2.message)
                BluetoothSPPManager.instance.apply {
                    closeSocket(socket, name)
                    removeConnectName(name)
                    alreadyConnectedNames.remove(name)
                }
            }
        }

        // CM625只有"+PTT=P"和"+PTT=R"
        private fun handleMsg(str: String) {
            var event = KeyEvent(Date().time, 0, 0, KeyEvent.KEYCODE_HEADSETHOOK, 1)
            event = if (str.contains("+PTT=P") || str == "AT+P=P" || str.contains("+SPP=P")) {
                KeyEvent.changeAction(event, KeyEvent.ACTION_DOWN)
            } else if (str.contains("+PTT=R") || str == "AT+R=R" || str.contains("+SPP=R")) {
                KeyEvent.changeAction(event, KeyEvent.ACTION_UP)
            } else {
                // 未匹配到指令，不处理
                return
            }

            runOnUiThread {
                event = KeyEvent.changeTimeRepeat(event, Date().time, 1)
                ProxyService.handleMediaButtonEvent(event)
            }
        }

        private fun runOnUiThread(action: () -> Unit) {
            val mainHandler = Handler(Looper.getMainLooper())
            mainHandler.post(action)
        }
    }
}
