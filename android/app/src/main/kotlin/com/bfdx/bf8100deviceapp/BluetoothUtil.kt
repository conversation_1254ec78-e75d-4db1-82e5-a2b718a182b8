package com.bfdx.bf8100deviceapp

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothProfile
import android.content.Context
import android.media.AudioManager
import android.util.Log

class BluetoothUtil {
    companion object {
        private var WaitingOperation: Int = 0
        private var TAG: String = "${TagPrefix}BluetoothUtil"
        private var startScoCount: Int = 0
        private var currentScoState: Int = 0

        fun isScoDisConnected(): <PERSON><PERSON>an {
            return currentScoState == AudioManager.SCO_AUDIO_STATE_DISCONNECTED
        }

        fun isScoConnecting(): Boolean {
            return currentScoState == AudioManager.SCO_AUDIO_STATE_CONNECTING
        }

        fun isScoConnected(): Bo<PERSON><PERSON> {
            return currentScoState == AudioManager.SCO_AUDIO_STATE_CONNECTED
        }

        fun updateScoState(state: Int) {
            currentScoState = state
        }

        fun isBluetoothConnected(context: Context): Boolean {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val isBluetoothA2dpOn = audioManager.isBluetoothA2dpOn
            val isWiredHeadsetOn = audioManager.isWiredHeadsetOn
            val isBluetoothScoOn = audioManager.isBluetoothScoOn
            val isBluetoothScoAvailableOffCall = audioManager.isBluetoothScoAvailableOffCall
            val isHEADSETProfileConnected: Boolean = isHEADSETProfileConnected()
            Log.d(TAG, "isBluetoothA2dpOn:$isBluetoothA2dpOn")
            Log.d(TAG, "isBluetoothScoOn:$isBluetoothScoOn")
            Log.d(TAG, "isWiredHeadsetOn:$isWiredHeadsetOn")
            Log.d(TAG, "isHEADSETProfileConnected:$isHEADSETProfileConnected")
            Log.d(TAG, "isBluetoothScoAvailableOffCall:$isBluetoothScoAvailableOffCall")
            return !(!isHEADSETProfileConnected && !isBluetoothA2dpOn)
        }

        fun isBluetoothA2dpOn(context: Context): Boolean {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val isBluetoothA2dpOn = audioManager.isBluetoothA2dpOn
            Log.d(TAG, "isBluetoothA2dpOn:$isBluetoothA2dpOn")
            return isBluetoothA2dpOn
        }

        fun isBluetoothScoOn(context: Context): Boolean {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val isBluetoothScoOn = audioManager.isBluetoothScoOn
            Log.d(TAG, "isBluetoothScoOn:$isBluetoothScoOn")
            return isBluetoothScoOn
        }

        @SuppressLint("MissingPermission")
        fun isHEADSETProfileConnected(): Boolean {
            var state: Int = -1
            try {
                val adapter = BluetoothAdapter.getDefaultAdapter()
                state = adapter.getProfileConnectionState(BluetoothProfile.HEADSET)
            } catch (_: Exception) {
            }
            return state == BluetoothProfile.A2DP
        }

        @SuppressLint("MissingPermission")
        fun getConnectedDevice(): BluetoothDevice? {
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter() ?: return null
            Log.i(TAG, "getConnectedDevice:bluetoothAdapter.state=${bluetoothAdapter.state}, bondedDevices=${bluetoothAdapter.bondedDevices}")
            for (device in bluetoothAdapter.bondedDevices) {
                if (bluetoothAdapter.state == device.bondState) {
                    Log.i(TAG, "getConnectedDevice:device.bondState=${device.bondState}, name=${device.name}, mac=${device.address}")
                    return device
                }
            }
            return null
        }

        @SuppressLint("MissingPermission")
        fun getConnectedDeviceName(): String {
            val device = getConnectedDevice()
            return if (device != null) device.name else ""
        }

        fun needKeepSco(context: Context): Boolean {
            val needKeepSco: Boolean
            val connectedDeviceName: String = getConnectedDeviceName()
            needKeepSco = if ("YY-BT-01P" == connectedDeviceName) {
                false
            } else if ("NJX A8" == connectedDeviceName || connectedDeviceName.startsWith("DellKing")) {
                true
            } else if (isBluetoothA2dpOn(context)) {
                !(connectedDeviceName == "B02PTT" || connectedDeviceName == "B01")
            } else {
                true
            }
            Log.i(TAG, "needKeepSco:$needKeepSco")
            return needKeepSco
        }

        private fun isBluetoothEnabled(): Boolean {
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            if (bluetoothAdapter != null) {
                return bluetoothAdapter.isEnabled
            }
            return false
        }

        fun startBluetoothSco(context: Context, tag: String) {
            Log.d(TAG, "startBluetoothSco:tag:$tag")
            val isBluetoothEnabled: Boolean = isBluetoothEnabled()
            val isBluetoothConnected = isBluetoothConnected(context)
            Log.d(TAG, "isBluetoothEnabled:$isBluetoothEnabled")
            Log.d(TAG, "isBluetoothConnected:$isBluetoothConnected")
            if (isBluetoothEnabled && isBluetoothConnected) {
                val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                val isBluetoothScoOn = audioManager.isBluetoothScoOn
                Log.d(TAG, "isBluetoothScoOn:$isBluetoothScoOn")
                Log.d(TAG, "isScoDisConnected:${isScoDisConnected()}")
                Log.d(TAG, "startScoCount:$startScoCount")
                if (!isBluetoothScoOn || isScoDisConnected()) {
                    Log.i(TAG, "real start BluetoothSco")
                    audioManager.isBluetoothScoOn = true
                    audioManager.isSpeakerphoneOn = false
                    audioManager.startBluetoothSco()
                    startScoCount++
                    WaitingOperation = 0
                    return
                }
                if (isScoConnecting()) {
                    WaitingOperation = 1
                    Log.d(TAG, "isScoConnecting")
                } else if (isScoConnected()) {
                    WaitingOperation = 0
                    Log.d(TAG, "isScoConnected")
                }
            }
        }

        fun stopBluetoothSco(context: Context, force: Boolean) {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val isBluetoothScoOn = audioManager.isBluetoothScoOn
            Log.d(TAG, "isBluetoothScoOn:$isBluetoothScoOn")
            Log.d(TAG, "isScoConnected:${isScoConnected()}")
            Log.d(TAG, "startScoCount:$startScoCount")
            if (startScoCount > 0) {
                if (isScoConnected() || isScoDisConnected() || force) {
                    Log.e("stopBluetoothSco", "real stop BluetoothSco")
                    audioManager.isBluetoothScoOn = false
                    audioManager.isSpeakerphoneOn = true
                    for (i in 0..<startScoCount) {
                        audioManager.stopBluetoothSco()
                    }
                    startScoCount = 0
                    WaitingOperation = 0
                    return
                }
                if (isScoConnecting()) {
                    WaitingOperation = 2
                    Log.e("stopBluetoothSco", "isScoConnecting")
                }
            }
        }

        fun isPresent(name: String): Boolean {
            var isPresent = false
            try {
                Log.d(TAG, "isPresent name:$name")
                Class.forName(name)
                isPresent = true
            } catch (e: java.lang.Exception) {
                Log.e(TAG, "isPresent Exception:" + e.message)
            }
            Log.d(TAG, "isPresent ${StringBuilder(isPresent.toString()).toString()}")
            return isPresent
        }

        @SuppressLint("MissingPermission")
        fun getLastConnectedDevice(address: String): BluetoothDevice? {
            Log.i(TAG, "getLastConnectedDevice:$address")
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter() ?: return null
            for (device in bluetoothAdapter.bondedDevices) {
                if (bluetoothAdapter.state == device.bondState && address == device.address) {
                    Log.i(TAG, "getLastConnectedDevice:device.bondState=${device.bondState}, name=${device.name}, mac=${device.address}")
                    return device
                }
            }
            return null
        }

        @SuppressLint("MissingPermission")
        fun getLastConnectedDeviceByName(name: String): BluetoothDevice? {
            Log.i(TAG, "getLastConnectedDeviceByName:$name")
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter() ?: return null
            for (device in bluetoothAdapter.bondedDevices) {
                if (bluetoothAdapter.state == device.bondState && device.name.startsWith(name)) {
                    Log.i(TAG, "getLastConnectedDevice:device.bondState=${device.bondState}, name=${device.name}, mac=${device.address}")
                    return device
                }
            }
            return null
        }
    }
}
