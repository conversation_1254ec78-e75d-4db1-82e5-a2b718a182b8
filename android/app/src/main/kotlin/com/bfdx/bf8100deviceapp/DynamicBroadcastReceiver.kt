package com.bfdx.bf8100deviceapp

import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import java.lang.Thread.sleep
import kotlin.concurrent.thread

class DynamicBroadcastReceiver(proxyService: ProxyService) : BroadcastReceiver() {
    val tag: String = "${TagPrefix}DynamicReceiver"
    private val service: ProxyService = proxyService

    fun checkPlay() {
        if (BluetoothUtil.isBluetoothConnected(service)) {
            if (BluetoothUtil.needKeepSco(service)) {
                BluetoothUtil.startBluetoothSco(service, "checkPlay")
                return
            }
            return
        }
        BluetoothUtil.stopBluetoothSco(service, false)
    }

    fun onBlueToothDeviceConnected() {
        Log.i(tag, "onBlueToothDeviceConnected")
        service.isBluetoothOpened = true
        thread {
            sleep(3000)
            Log.i(tag, "onBlueToothDeviceConnected isBluetoothConnected")
            if (BluetoothUtil.isBluetoothConnected(service)) {
                checkPlay();
            }
        }
    }

    fun onBlueToothDeviceDisConnected() {
        Log.i(tag, "onBlueToothDeviceDisConnected")
        BluetoothUtil.stopBluetoothSco(service, true)
    }

    override fun onReceive(context: Context, intent: Intent) {
        Log.i(tag, "onReceive: intent=$intent")
        // 判断是否为专用蓝牙耳机事件
        when (intent.action) {
            "android.bluetooth.headset.action.VENDOR_SPECIFIC_HEADSET_EVENT" -> {
                try {
                    val args = intent.extras?.get("android.bluetooth.headset.extra.VENDOR_SPECIFIC_HEADSET_EVENT_ARGS") as Array<*>?
                    if (args != null) {
                        if (args.size >= 2) {
                            val eventName = args[0] as String
                            val eventValue = try {
                                args[1] as Int
                            } catch (e2: java.lang.Exception) {
                                (args[1] as String).toInt()
                            }
                            if (eventName == "TALK") {
                                if (eventValue == 1) {
                                    ProxyService.headsetPressedPtt("unipro.hotkey.headset.ptt.down")
                                } else {
                                    if (eventValue == 0) {
                                        ProxyService.headsetPressedPtt("unipro.hotkey.headset.ptt.up")
                                    }
                                }
                            }
//                    if (eventName == "SOS") {
//                    }
                        } else if (args.size == 1) {
                            val eventName2 = args[0] as String
                            if (eventName2 == "PTT_DOWN") {
                                ProxyService.headsetPressedPtt("unipro.hotkey.headset.ptt.down")
                            } else if (eventName2 == "PTT_UP") {
                                ProxyService.headsetPressedPtt("unipro.hotkey.headset.ptt.up")
                            }
                        }
                    }
                } catch (e3: java.lang.Exception) {
                    Log.e(tag, e3.message!!)
                }
            }

            BluetoothDevice.ACTION_ACL_CONNECTED -> {
                service.isBluetoothConnected = true
                onBlueToothDeviceConnected()
            }

            BluetoothDevice.ACTION_ACL_DISCONNECTED -> {
                onBlueToothDeviceDisConnected()
                service.isBluetoothConnected = false
            }
        }
    }
}
