package com.bfdx.bf8100deviceapp

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity

class LauncherActivity : AppCompatActivity() {
    private val tag = "${TagPrefix}Launcher"
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(tag, "onCreate")
        // 在这里启动 HomeActivity
        startHomeActivity()
        // 关闭当前 Activity
        // finish()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(tag, "onNewIntent, intent: $intent")
        startHomeActivity()
    }

    private fun startHomeActivity() {
        Log.d(tag, "startHomeActivity")
        val intent = Intent(this, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        startActivity(intent)
    }
}
