package com.bfdx.bf8100deviceapp

import android.app.Service
import android.app.admin.DevicePolicyManager
import android.content.*
import android.content.pm.PackageManager
import android.media.AudioManager
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.provider.Settings.Secure.ACCESSIBILITY_ENABLED
import android.speech.tts.TextToSpeech
import android.util.Log
import android.view.KeyEvent
import android.view.accessibility.AccessibilityManager
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import java.util.*
import kotlin.concurrent.thread
import kotlin.math.max
import kotlin.math.min

const val TagPrefix = "BFPOC."
const val ActionPrefix = "com.bfdx.bf8100deviceapp"
const val ActionStaticBroadcastReceiver = "${ActionPrefix}.StaticBroadcastReceiver"
const val AccessibilityKeyEventReceiver = "${ActionPrefix}.AccessibilityKeyEventReceiver"
const val PowerManagerWakeLockEventReceiver = "${ActionPrefix}.PowerManagerWakeLock"
const val wakeUpTimeout = 30 * 1000

class LedPttMode {
    companion object {
        const val red = "red"
        const val green = "green"
        const val turnOff = "turn_off"
    }
}

// 亮灯广播Intent
fun getLightIntent(mode: String): Intent {
    return Intent("android.led.ptt.${mode}")
}

// 语音播报的Intent
class TtsIntent(text: String) : Intent() {
    companion object {
        const val ACTION = "${ActionPrefix}.TTS_ACTION"
        const val ACTION_STOP = "${ActionPrefix}.TTS_ACTION_STOP"
        const val TEXT = "tts_text"
    }

    init {
        setAction(ACTION)
        putExtra(TEXT, text)
    }

    fun getText(): String? {
        return getStringExtra(TEXT)
    }
}

class PlatformChannelImpl(messenger: BinaryMessenger, private val activity: MainActivity) :
    MethodChannel.MethodCallHandler {
    private val tag = "${TagPrefix}PlatformChannel"
    private var methodChannel: MethodChannel
//    private var context: Context
//    private var eventChannel: EventChannel
//    private var eventSink: EventChannel.EventSink? = null

    init {
        Log.d(tag, "init")

        methodChannel = MethodChannel(messenger, "com.bfdx.bf8100deviceapp/method")
        methodChannel.setMethodCallHandler(this)

//        eventChannel = EventChannel(messenger, "com.bfdx.bf8100deviceapp/event")
//        eventChannel.setStreamHandler(object : EventChannel.StreamHandler {
//            override fun onListen(arguments: Any?, event: EventChannel.EventSink?) {
//                Log.d(tag, "eventChannel.setStreamHandler: onListen")
//                eventSink = event
//            }
//
//            override fun onCancel(arguments: Any?) {
//                Log.d(tag, "eventChannel.setStreamHandler: onCancel")
//                eventSink = null
//            }
//        })
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        Log.d(tag, "onMethodCall: method=${call.method}, arguments=${call.arguments}")

        when (call.method) {
            "setSpeechLanguage" -> {
                val language = call.argument<String>("language")
                if (language == null) {
                    result.error("setLanguage", "language is null", null)
                    return
                }
                val code = activity.getTts().setLanguage(language)
                result.success(code == TextToSpeech.LANG_AVAILABLE)
            }

            "speakText" -> {
                val text = call.argument<String>("text")
                if (text == null) {
                    result.error("empty", "text is null", null)
                    return
                }

                val tts = activity.getTts()
                val mediaIdle = call.argument<Boolean>("mediaIdle") ?: false
                if (mediaIdle && !tts.canSpeak()) {
                    result.error("mediaIdle", "media busy", null)
                    return
                }

                val queueMode = call.argument<Int>("queueMode") ?: TextToSpeech.QUEUE_ADD
                tts.speak(text, queueMode)
                result.success(true)
            }

            "ledSetPtt" -> {
                val mode = call.argument<String>("mode") ?: LedPttMode.turnOff
                activity.ledSetPtt(mode)
                result.success(true)
            }

            "ledSetFlicker" -> {
                val color = call.argument<Int>("color") ?: 0
                val flicker = call.argument<Boolean>("flicker")
                val delayOn = call.argument<Int>("delay_on")
                val delayOff = call.argument<Int>("delay_off")
                activity.ledSetFlicker(color, flicker, delayOn, delayOff)
                result.success(true)
            }

            "getAppBuildInfo" -> {
                val buildInfo = activity.getAppBuildInfo()
                result.success(buildInfo)
            }

            "checkAccessibilityEnabled" -> {
                val enabled = activity.checkAccessibilityEnabled()
                result.success(enabled)
            }

            "openAccessibilitySettings" -> {
                activity.openAccessibilitySettings()
                result.success(true)
            }

            "silentInstallApp" -> {
                val filePath = call.argument<String>("filePath")
                if (filePath == null) {
                    result.error("filePath", "filePath is null", null)
                    return
                }
                activity.silentInstallApp(filePath)
                result.success(true)
            }

            "openUniproSettings" -> {
                activity.openUniproSettings()
                result.success(true)
            }

            "wakeUp" -> {
                val timeout = call.argument<Int>("timeout") ?: wakeUpTimeout
                activity.wakeUp(timeout)
                result.success(true)
            }

            "wakeLock" -> {
                activity.wakeLock()
                result.success(true)
            }

            "playSoundEffects" -> {
                val resourceName = call.argument<String>("resourceName")
                if (resourceName == null) {
                    result.error("resourceName", "resourceName is null", null)
                    return
                }

                WavMediaAudio.playSoundEffects(resourceName, result)
            }

            else -> {
                Log.w(tag, "onMethodCall: unknown method=${call.method}")
                result.notImplemented()
            }
        }
    }

    fun invokeMethod(method: String, data: HashMap<String, Any>?) {
        methodChannel.invokeMethod(method, data)
    }

    fun dispose() {
        Log.d(tag, "dispose")
//        eventSink = null
        methodChannel.setMethodCallHandler(null)
//        eventChannel.setStreamHandler(null)
    }
}

class TtsImpl(private val activity: MainActivity) {
    private val ttsTag = "${TagPrefix}TTS"
    private var tts: TextToSpeech
    private var ttsBroadcastReceiver: TtsBroadcastReceiver

    // 0: 空闲，可以播报文本语音，1：呼叫录音中，2：呼叫放音中
    private var mediaStatus: Int = 0

    companion object {
        const val MEDIA_STATUS_ACTION = "${ActionPrefix}.MEDIA_STATUS_ACTION"
    }

    private val mediaStatusReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            // 处理广播
            Log.d(ttsTag, "mediaStatusReceiver onReceive: $intent")
            val status = intent.getIntExtra("status", 0)
            mediaStatus = status
        }
    }

    init {
        Log.d(ttsTag, "init")
        val initListener: TextToSpeech.OnInitListener = TextToSpeech.OnInitListener { status ->
            if (status == TextToSpeech.SUCCESS) {
                Log.d(ttsTag, "onInit: SUCCESS")
                // 默认设置为英语
                setLanguage(Locale.ENGLISH.language)
            } else {
                Log.d(ttsTag, "onInit: FAILURE")
            }
        }
        tts = TextToSpeech(activity, initListener, "com.iflytek.speechcloud")

        // 注册广播接收器
        ttsBroadcastReceiver = TtsBroadcastReceiver()
        val filter = IntentFilter(TtsIntent.ACTION)
        filter.addAction(TtsIntent.ACTION_STOP)
        LocalBroadcastManager.getInstance(activity).registerReceiver(ttsBroadcastReceiver, filter)
        val filter2 = IntentFilter(MEDIA_STATUS_ACTION)
        LocalBroadcastManager.getInstance(activity).registerReceiver(mediaStatusReceiver, filter2)
    }

    fun canSpeak(): Boolean {
        return mediaStatus == 0
    }

    fun setLanguage(language: String): Int {
        val r = tts.setLanguage(Locale(language))
        Log.d(ttsTag, "setLanguage: $language, $r")
        return r
    }

    fun speak(text: String, queueMode: Int = TextToSpeech.QUEUE_ADD, utteranceId: String? = null): Int {
        Log.d(ttsTag, "speak: $text")
        BluetoothSPPManager.instance.apply {
            updateBluetoothSco()
        }
        return tts.speak(text, queueMode, null, utteranceId)
    }

    fun stop(): Int {
        return tts.stop()
    }

    fun dispose() {
        tts.shutdown()
        LocalBroadcastManager.getInstance(activity).unregisterReceiver(ttsBroadcastReceiver)
        LocalBroadcastManager.getInstance(activity).unregisterReceiver(mediaStatusReceiver)
    }

    private inner class TtsBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            Log.d(ttsTag, "onReceive: $intent")
            val ttsIntent = intent as TtsIntent
            if (ttsIntent.action == TtsIntent.ACTION_STOP) {
                stop()
                return
            }

            // 正在呼叫中，不允许播报文本语音
            if (!canSpeak()) {
                return
            }

            val text = ttsIntent.getText() ?: return

            speak(text, TextToSpeech.QUEUE_FLUSH)
        }
    }
}

class MainActivity : FlutterActivity() {
    private lateinit var platformChannel: PlatformChannelImpl
    private lateinit var pm: PowerManager
    private lateinit var networkCallback: ConnectivityManager.NetworkCallback
    private lateinit var connectivityManager: ConnectivityManager
    private lateinit var tts: TtsImpl
    private val mainTag = "${TagPrefix}MainActivity"
    private lateinit var accessibilityManager: AccessibilityManager
    private lateinit var accessibilityServiceClassName: String

    private val mainHotKeyEventReceiver = object : BroadcastReceiver() {
        fun processAccessibilityKeyEvent(context: Context, intent: Intent) {
            val action = intent.getStringExtra("action")
            Log.d(mainTag, "processAccessibilityKeyEvent action: $action")
            if (action == null) return

            // 唤醒屏幕
            when (action) {
                "wakeUp" -> wakeUp(intent.getIntExtra("timeout", wakeUpTimeout))
            }
        }

        fun processPowerManagerWakeLockEvent(context: Context, intent: Intent) {
            val action = intent.getStringExtra("action")
            Log.d(mainTag, "processPowerManagerWakeLockEvent action: $action")
            if (action == null) return

            // 唤醒屏幕
            when (action) {
                "wakeUp" -> wakeUp(intent.getIntExtra("timeout", wakeUpTimeout))
                "wakeLock" -> wakeLock()
            }
        }

        fun processHotKeyEvent(context: Context, intent: Intent) {
            // 处理广播
            val action = intent.getStringExtra("originAction")
            Log.d(mainTag, "mainHotKeyEventReceiver onReceive: $intent, action: $action")
            if (action == null) return

            /*// 当前MainActivity不是在前台运行，则拦截home键，重新启动MainActivity
            if (action == "unipro.hotkey.home.down" && !self.isForeground) {
                val homeIntent = Intent(self, MainActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                startActivity(homeIntent)
                return
            }*/

            // 需要发送到Flutter端的事件参数
            val data = HashMap<String, Any>()
            data["action"] = action
            platformChannel.invokeMethod("onHotKeyEvent", data)
        }

        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (action == AccessibilityKeyEventReceiver) {
                processAccessibilityKeyEvent(context, intent)
                return
            }

            if (action == PowerManagerWakeLockEventReceiver) {
                processPowerManagerWakeLockEvent(context, intent)
                return
            }

            processHotKeyEvent(context, intent)
        }
    }

    companion object {
        // Used to load the 'native-lib' library on application startup.
        init {
            System.loadLibrary("native-lib")
        }

        private lateinit var self: MainActivity
        fun getInstance(): MainActivity {
            return self
        }
    }

    override fun onCreate(savedInstanceState: android.os.Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(mainTag, "onCreate, current activity: ${this.hashCode()}")
        Log.d(mainTag, "build info: APP_VERSION=${BuildConfig.APP_VERSION}, BUILD_TIME=${BuildConfig.BUILD_TIME}")

        // 保存当前实例
        self = this

        // 获取PowerManager实例
        pm = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        WavMediaAudio.init(this)

        // 启动ProxyService服务
        runProxyService()
        // 注册网络变更监听器
        onNetworkChanged()

        if (BuildConfig.DEBUG) {
            wakeUp(wakeUpTimeout * 2)
        } else {
            wakeUp(wakeUpTimeout)
        }

        // 注册StaticBroadcastReceiver转发的intent事件
        val filter = IntentFilter()
        filter.addAction(ActionStaticBroadcastReceiver)
        filter.addAction(AccessibilityKeyEventReceiver)
        filter.addAction(PowerManagerWakeLockEventReceiver)
        LocalBroadcastManager.getInstance(this).registerReceiver(mainHotKeyEventReceiver, filter)

        initAccessibilityServiceManager()
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        Log.d(mainTag, "configureFlutterEngine")
        super.configureFlutterEngine(flutterEngine)
        // 初始化平台消息通道
        platformChannel = PlatformChannelImpl(flutterEngine.dartExecutor.binaryMessenger, this)
        // 初始化extToSpeech实例
        tts = TtsImpl(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        platformChannel.dispose()
        tts.dispose()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mainHotKeyEventReceiver)
        connectivityManager.unregisterNetworkCallback(networkCallback)
        WavMediaAudio.dispose()
        Log.d(mainTag, "onDestroy, ${this.hashCode()}")
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(mainTag, "onNewIntent called")
        // Handle the new intent if needed
    }

    private external fun onNetworkConn()
    private external fun onNetworkLose()

    private fun onNetworkChanged() {
        connectivityManager = context.getSystemService(Service.CONNECTIVITY_SERVICE) as ConnectivityManager
        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                Log.i("-----onNetworkChanged", "Network Available")
                onNetworkConn()
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                Log.i("-----onNetworkChanged", "Connection lost")
                onNetworkLose()
            }
        }

        val builder = NetworkRequest.Builder()
        builder.addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
        builder.addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
        val networkRequest = builder.build()

        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
    }

    fun openAccessibilitySettings() {
        val intent = Intent("android.settings.ACCESSIBILITY_SETTINGS")
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        this.startActivity(intent)
    }

    fun checkAccessibilityEnabled(serviceName: String? = accessibilityServiceClassName): Boolean {
        val enabledServicesSetting = Settings.Secure.getString(
            this.getContentResolver(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
        )

        if (enabledServicesSetting == null) {
            return false
        }
        val enabledServices = enabledServicesSetting.split(":")
        for (enabledService in enabledServices) {
            if (enabledService == serviceName) {
                return true
            }
        }
        return false
    }

    // 开启后设置后，系统自动创建一个PocAccessibilityService的实例，并将其注册到系统的AccessibilityManager中
    // 等待Flutter端初始化完成后，再开启无障碍服务
    private fun initAccessibilityServiceManager() {
        accessibilityManager = this.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        accessibilityServiceClassName = "${this.packageName}/${this.packageName}.PocAccessibilityService"
        Log.d(mainTag, "accessibilityServiceClassName: $accessibilityServiceClassName")

        thread {
            var isServiceEnabled = checkAccessibilityEnabled()
            Log.d(mainTag, "checkAccessibilityEnabled: $isServiceEnabled")
            if (isServiceEnabled) {
                return@thread
            }

            // 检查是否有WRITE_SECURE_SETTINGS权限
            val requiredPermission = "android.permission.WRITE_SECURE_SETTINGS";
            val pm: PackageManager = getPackageManager()
            if (pm.checkPermission(requiredPermission, packageName) == PackageManager.PERMISSION_DENIED) {
                Log.d(mainTag, "check WRITE_SECURE_SETTINGS Permission failed")
                openAccessibilitySettings()
                return@thread
            }

            // 打开poc服务，使用这种方法打开的无障碍服务不会被accessibilityManager获取到
            isServiceEnabled = Settings.Secure.putString(
                getContentResolver(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES, accessibilityServiceClassName
            )
            if (!isServiceEnabled) {
                openAccessibilitySettings()
            }
            // 重新打开无障碍服务
            val isAccessibilityEnabled = Settings.Secure.putInt(getContentResolver(), ACCESSIBILITY_ENABLED, 1)
            if (!isAccessibilityEnabled) {
                openAccessibilitySettings()
            }
            Log.d(mainTag, "setAccessibilityServiceEnabled: $isServiceEnabled")
        }
    }

    fun getTts(): TtsImpl {
        return tts
    }

    fun getMethodCall(): PlatformChannelImpl {
        return platformChannel
    }

    fun getAppBuildInfo(): HashMap<String, Any> {
        val buildInfo = HashMap<String, Any>()
        buildInfo["version"] = BuildConfig.APP_VERSION
        buildInfo["buildTime"] = BuildConfig.BUILD_TIME

        return buildInfo
    }

    private fun runProxyService() {
        Log.d(mainTag, "runProxyService")
        val startIntent = Intent(this, ProxyService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(startIntent)
        } else {
            startService(startIntent)
        }
    }

    // 控制LED灯常亮
    fun ledSetPtt(mode: String) {
        sendBroadcast(getLightIntent(mode))
    }

    // 控制LED灯闪烁
    // 	键名：   flicker 类型：     boolean 是否必选： 否 范围：     false/true 默认值：   false	false:不闪烁   true：闪烁
    // 	键名：   delay_on 类型：     int 是否必选： 否 范围：     100~10000 默认值：   500	闪烁时灯亮的持续时间,单位毫秒
    // 	键名：   delay_off 类型：     int 是否必选： 否 范围：     100~10000 默认值：   500	闪烁时灯灭的持续时间,单位毫秒
    fun ledSetFlicker(color: Int?, flicker: Boolean?, delayOn: Int?, delayOff: Int?) {
        val intent = Intent("unipro.led.set")
        if (color != null) {
            intent.putExtra("color", color)
        }
        if (flicker != null) {
            intent.putExtra("flicker", flicker)
        }
        if (delayOn != null) {
            var delayOnFinal = min(delayOn, 10000)
            delayOnFinal = max(delayOnFinal, 100)
            intent.putExtra("delay_on", delayOnFinal)
        }
        if (delayOff != null) {
            var delayOffFinal = min(delayOff, 10000)
            delayOffFinal = max(delayOffFinal, 100)
            intent.putExtra("delay_off", delayOffFinal)
        }
        sendBroadcast(intent)
    }

    // 唤醒接口
    fun wakeUp(timeout: Int) {
        try {
            Log.i(mainTag, "wakeUp timeout: $timeout")
            val wakeLock: PowerManager.WakeLock = pm.newWakeLock(
                PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
                // ScreenOnAndOffActivity::class.java.getSimpleName()
                MainActivity::class.java.getSimpleName()
            )
            wakeLock.acquire(timeout.toLong())
            wakeLock.release()
        } catch (e: Exception) {
            Log.e(mainTag, "wakeUp error", e)
        }
    }

    // 熄屏接口
    fun wakeLock() {
        try {
            Log.i(mainTag, "wakeLock")
            val devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
            devicePolicyManager.lockNow()
        } catch (e: Exception) {
            Log.e(mainTag, "wakeLock error", e)
        }
    }

    // todo 静默升级安装app，当前按接口发送Intent, 安装失败，原因没有显示
    // filePath绝对路径
    fun silentInstallApp(filePath: String) {
        Log.i(mainTag, "silentInstallApp filePath=$filePath")
        val intent = Intent()
        intent.setAction("android.intent.action.MAIN")
        intent.setComponent(ComponentName("cc.unipro.silentinstaller", "cc.unipro.silentinstaller.Main"))
        intent.putExtra("cc.unipro.silentinstaller.action", "cc.unipro.silentinstaller.action.INSTALL")
        // 这里请修改为你自己制定的内置SD卡文件路径
        intent.putExtra("cc.unipro.silentinstaller.extra.FILE_PATH", filePath)
//        intent.addCategory("android.intent.category.LAUNCHER")
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        // mainActivity是主Activity对象
        this.startActivityForResult(intent, 0xAA)
    }

    // 打开内置的设置页面
    // CM625S内置APP，参考"北峰BF-CM625S适配接口v1.0.xlsx文档的内置APP"说明
    fun openUniproSettings() {
        val intent = Intent()
        intent.setAction("android.intent.action.MAIN")
        intent.setComponent(ComponentName("cc.unipro.usettings", "cc.unipro.usettings.MainActivity"))
        intent.addCategory("android.intent.category.LAUNCHER")
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        this.startActivity(intent)
    }

    // 主要用于阻止返回键事件退出app
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_HOME) {
            Log.d(mainTag, "onKeyDown: KEYCODE_HOME")
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            startActivity(intent)
        }
        return true
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        return true
    }

    override fun onKeyLongPress(keyCode: Int, event: KeyEvent?): Boolean {
        return true
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        Log.d(mainTag, "onBackPressed")
    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        Log.d(mainTag, "dispatchKeyEvent: keyCode=${event.keyCode}, action=${event.action}")
        val keyCode = event.keyCode
        if (keyCode == KeyEvent.KEYCODE_HEADSETHOOK
            || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY
            || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE
            || keyCode == KeyEvent.KEYCODE_MEDIA_STOP
            || keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
            || keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
            || keyCode == KeyEvent.KEYCODE_MEDIA_REWIND
            || keyCode == KeyEvent.KEYCODE_MEDIA_FAST_FORWARD
            || keyCode == KeyEvent.KEYCODE_MEDIA_PAUSE
            || keyCode == KeyEvent.KEYCODE_MEDIA_RECORD
        ) {
            val audioManager: AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
            audioManager.dispatchMediaKeyEvent(event) // 优先传递给 AudioManager
            return true // 消费事件，防止 Flutter 再次处理
        }
        return super.dispatchKeyEvent(event) // 其他按键事件交给 Flutter 处理
    }
}
