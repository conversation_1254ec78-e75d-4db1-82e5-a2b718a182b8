package com.bfdx.bf8100deviceapp

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import android.bluetooth.BluetoothDevice
import android.view.KeyEvent

class MediaButtonReceiver : BroadcastReceiver() {
    private val tag = "${TagPrefix}MediaBtnReceiver"

    override fun onReceive(context: Context, intent: Intent) {
        Log.i(tag, "received intent: ${intent.action}")
        when (intent.action) {
            // 监听耳机按钮事件
            Intent.ACTION_MEDIA_BUTTON -> {
                val keyEvent = intent.getParcelableExtra<KeyEvent>(Intent.EXTRA_KEY_EVENT)
                if (keyEvent == null) {
                    Log.i(tag, "Headset ACTION_MEDIA_BUTTON keyEvent is null")
                    return
                }

                ProxyService.handleMediaButtonEvent(keyEvent)

                // 阻止其他应用接收到该广播
                 abortBroadcast()
            }
        }
    }
}
