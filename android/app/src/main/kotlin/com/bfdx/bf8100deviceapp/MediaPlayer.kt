package com.bfdx.bf8100deviceapp

import android.content.Context
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import android.util.Log

fun byteArrayOfInts(vararg ints: Int) = ByteArray(ints.size) { pos -> ints[pos].toByte() }

var OkPcm = byteArrayOfInts(
    0xff,
    0xff,
    0xfe,
    0xff,
    0x04,
    0x00,
    0xfb,
    0xff,
    0x06,
    0x00,
    0xfb,
    0xff,
    0x02,
    0x00,
    0xff,
    0xff,
    0x02,
    0x00,
    0xfa,
    0xff,
    0x06,
    0x00,
    0xfb,
    0xff,
    0x02,
    0x00,
    0x02,
    0x00,
    0xfd,
    0xff,
    0x04,
    0x00,
    0x00,
    0x00,
    0xff,
    0xff,
    0x02,
    0x00,
    0xff,
    0xff,
    0xfe,
    0xff,
    0x04,
    0x00,
    0xfc,
    0xff,
    0x01,
    0x00,
    0xf5,
    0xff,
    0xf7,
    0xff,
    0xed,
    0xff,
    0xf1,
    0xff,
    0xe4,
    0xff,
    0xe9,
    0xff,
    0xdd,
    0xff,
    0xe2,
    0xff,
    0xd9,
    0xff,
    0xe4,
    0xff,
    0xda,
    0xff,
    0xe0,
    0xff,
    0xdd,
    0xff,
    0xdd,
    0xff,
    0xe1,
    0xff,
    0xdd,
    0xff,
    0xdc,
    0xff,
    0xdf,
    0xff,
    0xdb,
    0xff,
    0xe2,
    0xff,
    0xdf,
    0xff,
    0xe1,
    0xff,
    0xdf,
    0xff,
    0xdf,
    0xff,
    0xdd,
    0xff,
    0xdf,
    0xff,
    0xdf,
    0xff,
    0xde,
    0xff,
    0xe3,
    0xff,
    0xda,
    0xff,
    0xe5,
    0xff,
    0xdc,
    0xff,
    0xe0,
    0xff,
    0xdd,
    0xff,
    0xdd,
    0xff,
    0xe1,
    0xff,
    0xe0,
    0xff,
    0xee,
    0xff,
    0x4b,
    0x00,
    0xd4,
    0x00,
    0xe7,
    0x00,
    0xfa,
    0xff,
    0xfe,
    0xfd,
    0xf0,
    0xfb,
    0xd4,
    0xfb,
    0x66,
    0xfe,
    0x01,
    0x03,
    0x0d,
    0x07,
    0x7b,
    0x07,
    0x5e,
    0x03,
    0x55,
    0xfc,
    0x55,
    0xf6,
    0x78,
    0xf5,
    0xc6,
    0xfa,
    0xbe,
    0x03,
    0x65,
    0x0b,
    0xb1,
    0x0c,
    0x7f,
    0x06,
    0xda,
    0xfb,
    0xb2,
    0xf2,
    0xe6,
    0xf0,
    0xd2,
    0xf7,
    0xd4,
    0x03,
    0x33,
    0x0e,
    0x82,
    0x10,
    0x21,
    0x09,
    0xfd,
    0xfb,
    0x76,
    0xf0,
    0xaa,
    0xed,
    0x68,
    0xf5,
    0x7a,
    0x03,
    0xed,
    0x0f,
    0x46,
    0x13,
    0x60,
    0x0b,
    0x96,
    0xfc,
    0x35,
    0xef,
    0x65,
    0xeb,
    0x62,
    0xf3,
    0xcb,
    0x02,
    0xde,
    0x10,
    0x3f,
    0x15,
    0x4c,
    0x0d,
    0x66,
    0xfd,
    0x9e,
    0xee,
    0xbd,
    0xe9,
    0xa2,
    0xf1,
    0xd6,
    0x01,
    0x30,
    0x11,
    0x95,
    0x16,
    0xe5,
    0x0e,
    0x62,
    0xfe,
    0x7f,
    0xee,
    0x97,
    0xe8,
    0x18,
    0xf0,
    0xc7,
    0x00,
    0x15,
    0x11,
    0x8e,
    0x17,
    0x4a,
    0x10,
    0x7f,
    0xff,
    0xbc,
    0xee,
    0xcc,
    0xe7,
    0xcf,
    0xee,
    0x9d,
    0xff,
    0xc1,
    0x10,
    0x33,
    0x18,
    0x91,
    0x11,
    0xb5,
    0x00,
    0x38,
    0xef,
    0x4d,
    0xe7,
    0x97,
    0xed,
    0x63,
    0xfe,
    0x27,
    0x10,
    0x98,
    0x18,
    0xac,
    0x12,
    0xe9,
    0x01,
    0xd3,
    0xef,
    0xfd,
    0xe6,
    0x81,
    0xec,
    0x31,
    0xfd,
    0x74,
    0x0f,
    0xd0,
    0x18,
    0xaf,
    0x13,
    0x1d,
    0x03,
    0x9e,
    0xf0,
    0xdf,
    0xe6,
    0x9b,
    0xeb,
    0xfc,
    0xfb,
    0xa7,
    0x0e,
    0xda,
    0x18,
    0x90,
    0x14,
    0x4e,
    0x04,
    0x82,
    0xf1,
    0xdd,
    0xe6,
    0xce,
    0xea,
    0xbd,
    0xfa,
    0xc1,
    0x0d,
    0xc4,
    0x18,
    0x56,
    0x15,
    0x8d,
    0x05,
    0x6d,
    0xf2,
    0x0f,
    0xe7,
    0x11,
    0xea,
    0x94,
    0xf9,
    0xd3,
    0x0c,
    0x8b,
    0x18,
    0x09,
    0x16,
    0xb9,
    0x06,
    0x7a,
    0xf3,
    0x56,
    0xe7,
    0x6d,
    0xe9,
    0x60,
    0xf8,
    0xba,
    0x0b,
    0x34,
    0x18,
    0xaa,
    0x16,
    0xf1,
    0x07,
    0x97,
    0xf4,
    0xab,
    0xe7,
    0xc6,
    0xe8,
    0x27,
    0xf7,
    0x9e,
    0x0a,
    0xe3,
    0x17,
    0x4a,
    0x17,
    0x29,
    0x09,
    0xad,
    0xf5,
    0x00,
    0xe8,
    0x33,
    0xe8,
    0x03,
    0xf6,
    0x88,
    0x09,
    0x84,
    0x17,
    0xc3,
    0x17,
    0x45,
    0x0a,
    0xc6,
    0xf6,
    0x7f,
    0xe8,
    0xc9,
    0xe7,
    0xec,
    0xf4,
    0x58,
    0x08,
    0xf0,
    0x16,
    0x25,
    0x18,
    0x6b,
    0x0b,
    0x03,
    0xf8,
    0x16,
    0xe9,
    0x63,
    0xe7,
    0xc4,
    0xf3,
    0x1a,
    0x07,
    0x5b,
    0x16,
    0x8e,
    0x18,
    0x8e,
    0x0c,
    0x36,
    0xf9,
    0xa4,
    0xe9,
    0xfe,
    0xe6,
    0x9e,
    0xf2,
    0xea,
    0x05,
    0xbf,
    0x15,
    0xf0,
    0x18,
    0xa5,
    0x0d,
    0x68,
    0xfa,
    0x3e,
    0xea,
    0xa1,
    0xe6,
    0x8d,
    0xf1,
    0xab,
    0x04,
    0x1f,
    0x15,
    0x37,
    0x19,
    0xb4,
    0x0e,
    0xa7,
    0xfb,
    0xef,
    0xea,
    0x6c,
    0xe6,
    0x7f,
    0xf0,
    0x6d,
    0x03,
    0x58,
    0x14,
    0x66,
    0x19,
    0xbc,
    0x0f,
    0xec,
    0xfc,
    0xbc,
    0xeb,
    0x41,
    0xe6,
    0x7c,
    0xef,
    0x24,
    0x02,
    0x85,
    0x13,
    0x8a,
    0x19,
    0xb8,
    0x10,
    0x35,
    0xfe,
    0x8c,
    0xec,
    0x25,
    0xe6,
    0x89,
    0xee,
    0xdd,
    0x00,
    0xb7,
    0x12,
    0x9c,
    0x19,
    0xa4,
    0x11,
    0x72,
    0xff,
    0x64,
    0xed,
    0x18,
    0xe6,
    0xa3,
    0xed,
    0xa1,
    0xff,
    0xd3,
    0x11,
    0xa0,
    0x19,
    0x83,
    0x12,
    0xb8,
    0x00,
    0x4d,
    0xee,
    0x1f,
    0xe6,
    0xc5,
    0xec,
    0x5f,
    0xfe,
    0xe2,
    0x10,
    0x92,
    0x19,
    0x5b,
    0x13,
    0xf4,
    0x01,
    0x43,
    0xef,
    0x2f,
    0xe6,
    0xf9,
    0xeb,
    0x1e,
    0xfd,
    0xeb,
    0x0f,
    0x77,
    0x19,
    0x25,
    0x14,
    0x37,
    0x03,
    0x3e,
    0xf0,
    0x52,
    0xe6,
    0x2f,
    0xeb,
    0xdb,
    0xfb,
    0xf4,
    0x0e,
    0x44,
    0x19,
    0xec,
    0x14,
    0x6f,
    0x04,
    0x3e,
    0xf1,
    0x85,
    0xe6,
    0x72,
    0xea,
    0xa5,
    0xfa,
    0xe4,
    0x0d,
    0x0d,
    0x19,
    0x9a,
    0x15,
    0xa9,
    0x05,
    0x51,
    0xf2,
    0xc5,
    0xe6,
    0xcb,
    0xe9,
    0x6b,
    0xf9,
    0xd3,
    0x0c,
    0xc3,
    0x18,
    0x43,
    0x16,
    0xe8,
    0x06,
    0x6a,
    0xf3,
    0x1e,
    0xe7,
    0x2a,
    0xe9,
    0x33,
    0xf8,
    0xb5,
    0x0b,
    0x64,
    0x18,
    0xe1,
    0x16,
    0x18,
    0x08,
    0x8f,
    0xf4,
    0x79,
    0xe7,
    0x9e,
    0xe8,
    0x04,
    0xf7,
    0x97,
    0x0a,
    0x00,
    0x18,
    0x65,
    0x17,
    0x4c,
    0x09,
    0xaa,
    0xf5,
    0xee,
    0xe7,
    0x12,
    0xe8,
    0xda,
    0xf5,
    0x74,
    0x09,
    0x7e,
    0x17,
    0xe9,
    0x17,
    0x6a,
    0x0a,
    0xdd,
    0xf6,
    0x6d,
    0xe8,
    0xa2,
    0xe7,
    0xb7,
    0xf4,
    0x39,
    0x08,
    0xfc,
    0x16,
    0x47,
    0x18,
    0x99,
    0x0b,
    0x08,
    0xf8,
    0x02,
    0xe9,
    0x35,
    0xe7,
    0x91,
    0xf3,
    0x07,
    0x07,
    0x5d,
    0x16,
    0xb2,
    0x18,
    0xb0,
    0x0c,
    0x43,
    0xf9,
    0x9d,
    0xe9,
    0xdb,
    0xe6,
    0x7b,
    0xf2,
    0xc9,
    0x05,
    0xbd,
    0x15,
    0xfc,
    0x18,
    0xc8,
    0x0d,
    0x7e,
    0xfa,
    0x47,
    0xea,
    0x96,
    0xe6,
    0x6e,
    0xf1,
    0x8c,
    0x04,
    0x08,
    0x15,
    0x36,
    0x19,
    0xca,
    0x0e,
    0xba,
    0xfb,
    0x02,
    0xeb,
    0x5e,
    0xe6,
    0x6f,
    0xf0,
    0x48,
    0x03,
    0x4c,
    0x14,
    0x67,
    0x19,
    0xcf,
    0x0f,
    0x03,
    0xfd,
    0xc8,
    0xeb,
    0x39,
    0xe6,
    0x6e,
    0xef,
    0x0b,
    0x02,
    0x7e,
    0x13,
    0x8a,
    0x19,
    0xc3,
    0x10,
    0x46,
    0xfe,
    0x94,
    0xec,
    0x22,
    0xe6,
    0x7d,
    0xee,
    0xc9,
    0x00,
    0xa4,
    0x12,
    0x99,
    0x19,
    0xab,
    0x11,
    0x87,
    0xff,
    0x74,
    0xed,
    0x15,
    0xe6,
    0x96,
    0xed,
    0x83,
    0xff,
    0xc4,
    0x11,
    0x97,
    0x19,
    0x91,
    0x12,
    0xca,
    0x00,
    0x59,
    0xee,
    0x20,
    0xe6,
    0xb5,
    0xec,
    0x47,
    0xfe,
    0xd3,
    0x10,
    0x8b,
    0x19,
    0x66,
    0x13,
    0x0b,
    0x02,
    0x4f,
    0xef,
    0x33,
    0xe6,
    0xe7,
    0xeb,
    0x09,
    0xfd,
    0xdc,
    0x0f,
    0x6d,
    0x19,
    0x35,
    0x14,
    0x44,
    0x03,
    0x4b,
    0xf0,
    0x4f,
    0xe6,
    0x23,
    0xeb,
    0xc7,
    0xfb,
    0xe2,
    0x0e,
    0x3e,
    0x19,
    0xf8,
    0x14,
    0x7c,
    0x04,
    0x56,
    0xf1,
    0x82,
    0xe6,
    0x6c,
    0xea,
    0x8c,
    0xfa,
    0xd5,
    0x0d,
    0x04,
    0x19,
    0xa8,
    0x15,
    0xbd,
    0x05,
    0x64,
    0xf2,
    0xcc,
    0xe6,
    0xbe,
    0xe9,
    0x5a,
    0xf9,
    0xbc,
    0x0c,
    0xbb,
    0x18,
    0x46,
    0x16,
    0xfa,
    0x06,
    0x78,
    0xf3,
    0x24,
    0xe7,
    0x20,
    0xe9,
    0x26,
    0xf8,
    0xa4,
    0x0b,
    0x5e,
    0x18,
    0xe4,
    0x16,
    0x28,
    0x08,
    0x9e,
    0xf4,
    0x7f,
    0xe7,
    0x8f,
    0xe8,
    0xf0,
    0xf6,
    0x7c,
    0x0a,
    0xf4,
    0x17,
    0x6b,
    0x17,
    0x57,
    0x09,
    0xbd,
    0xf5,
    0xf3,
    0xe7,
    0x07,
    0xe8,
    0xc9,
    0xf5,
    0x54,
    0x09,
    0x7a,
    0x17,
    0xe0,
    0x17,
    0x81,
    0x0a,
    0xe7,
    0xf6,
    0x77,
    0xe8,
    0x9c,
    0xe7,
    0xa5,
    0xf4,
    0x2b,
    0x08,
    0xed,
    0x16,
    0x4d,
    0x18,
    0x9b,
    0x0b,
    0x20,
    0xf8,
    0x07,
    0xe9,
    0x3a,
    0xe7,
    0x88,
    0xf3,
    0xf4,
    0x06,
    0x52,
    0x16,
    0xad,
    0x18,
    0xb8,
    0x0c,
    0x56,
    0xf9,
    0xae,
    0xe9,
    0xdb,
    0xe6,
    0x73,
    0xf2,
    0xbc,
    0x05,
    0xab,
    0x15,
    0x03,
    0x19,
    0xc8,
    0x0d,
    0x94,
    0xfa,
    0x51,
    0xea,
    0x98,
    0xe6,
    0x65,
    0xf1,
    0x80,
    0x04,
    0xfd,
    0x14,
    0x39,
    0x19,
    0xd6,
    0x0e,
    0xcf,
    0xfb,
    0x0a,
    0xeb,
    0x61,
    0xe6,
    0x61,
    0xf0,
    0x40,
    0x03,
    0x3f,
    0x14,
    0x68,
    0x19,
    0xd0,
    0x0f,
    0x0c,
    0xfd,
    0xcc,
    0xeb,
    0x38,
    0xe6,
    0x68,
    0xef,
    0xff,
    0x01,
    0x75,
    0x13,
    0x86,
    0x19,
    0xc9,
    0x10,
    0x4b,
    0xfe,
    0x9b,
    0xec,
    0x1f,
    0xe6,
    0x71,
    0xee,
    0xc4,
    0x00,
    0xa3,
    0x12,
    0x9a,
    0x19,
    0xb6,
    0x11,
    0x8b,
    0xff,
    0x77,
    0xed,
    0x16,
    0xe6,
    0x8c,
    0xed,
    0x88,
    0xff,
    0xb8,
    0x11,
    0x9e,
    0x19,
    0x91,
    0x12,
    0xd0,
    0x00,
    0x5d,
    0xee,
    0x1d,
    0xe6,
    0xb1,
    0xec,
    0x43,
    0xfe,
    0xcf,
    0x10,
    0x8c,
    0x19,
    0x72,
    0x13,
    0x0e,
    0x02,
    0x59,
    0xef,
    0x33,
    0xe6,
    0xe2,
    0xeb,
    0xfa,
    0xfc,
    0xd6,
    0x0f,
    0x68,
    0x19,
    0x3c,
    0x14,
    0x54,
    0x03,
    0x59,
    0xf0,
    0x5a,
    0xe6,
    0x1a,
    0xeb,
    0xbb,
    0xfb,
    0xcc,
    0x0e,
    0x3b,
    0x19,
    0xfb,
    0x14,
    0x91,
    0x04,
    0x64,
    0xf1,
    0x8b,
    0xe6,
    0x60,
    0xea,
    0x82,
    0xfa,
    0xba,
    0x0d,
    0x07,
    0x19,
    0xaa,
    0x15,
    0xce,
    0x05,
    0x76,
    0xf2,
    0xcc,
    0xe6,
    0xb5,
    0xe9,
    0x3e,
    0xf9,
    0xac,
    0x0c,
    0xb2,
    0x18,
    0x56,
    0x16,
    0x07,
    0x07,
    0x8d,
    0xf3,
    0x26,
    0xe7,
    0x14,
    0xe9,
    0x0f,
    0xf8,
    0x8d,
    0x0b,
    0x56,
    0x18,
    0xe8,
    0x16,
    0x38,
    0x08,
    0xaf,
    0xf4,
    0x8b,
    0xe7,
    0x88,
    0xe8,
    0xe1,
    0xf6,
    0x68,
    0x0a,
    0xe6,
    0x17,
    0x76,
    0x17,
    0x66,
    0x09,
    0xda,
    0xf5,
    0xf8,
    0xe7,
    0x04,
    0xe8,
    0xad,
    0xf5,
    0x3e,
    0x09,
    0x6e,
    0x17,
    0xf0,
    0x17,
    0x92,
    0x0a,
    0x05,
    0xf7,
    0x7a,
    0xe8,
    0x95,
    0xe7,
    0x89,
    0xf4,
    0x18,
    0x08,
    0xe0,
    0x16,
    0x5e,
    0x18,
    0xb2,
    0x0b,
    0x34,
    0xf8,
    0x13,
    0xe9,
    0x2b,
    0xe7,
    0x76,
    0xf3,
    0xe0,
    0x06,
    0x4f,
    0x16,
    0xb6,
    0x18,
    0xca,
    0x0c,
    0x6c,
    0xf9,
    0xaa,
    0xe9,
    0xd7,
    0xe6,
    0x5a,
    0xf2,
    0xa2,
    0x05,
    0xa0,
    0x15,
    0xfc,
    0x18,
    0xda,
    0x0d,
    0xa1,
    0xfa,
    0x5b,
    0xea,
    0x92,
    0xe6,
    0x4d,
    0xf1,
    0x6a,
    0x04,
    0xeb,
    0x14,
    0x39,
    0x19,
    0xe6,
    0x0e,
    0xde,
    0xfb,
    0x1b,
    0xeb,
    0x57,
    0xe6,
    0x50,
    0xf0,
    0x26,
    0x03,
    0x35,
    0x14,
    0x70,
    0x19,
    0xe4,
    0x0f,
    0x2b,
    0xfd,
    0xcf,
    0xeb,
    0x37,
    0xe6,
    0x49,
    0xef,
    0xea,
    0x01,
    0x64,
    0x13,
    0x8a,
    0x19,
    0xe5,
    0x10,
    0x63,
    0xfe,
    0xb2,
    0xec,
    0x16,
    0xe6,
    0x61,
    0xee,
    0x9f,
    0x00,
    0x90,
    0x12,
    0x9c,
    0x19,
    0xd2,
    0x11,
    0xaa,
    0xff,
    0x8f,
    0xed,
    0x07,
    0xe6,
    0x75,
    0xed,
    0x59,
    0xff,
    0xaa,
    0x11,
    0xa9,
    0x19,
    0xb2,
    0x12,
    0xf5,
    0x00,
    0x71,
    0xee,
    0x12,
    0xe6,
    0x8f,
    0xec,
    0x1b,
    0xfe,
    0xb7,
    0x10,
    0x91,
    0x19,
    0x8a,
    0x13,
    0x2e,
    0x02,
    0x6b,
    0xef,
    0x28,
    0xe6,
    0xc4,
    0xeb,
    0xda,
    0xfc,
    0xb7,
    0x0f,
    0x6e,
    0x19,
    0x52,
    0x14,
    0x6f,
    0x03,
    0x6c,
    0xf0,
    0x4e,
    0xe6,
    0x06,
    0xeb,
    0x96,
    0xfb,
    0xb9,
    0x0e,
    0x3c,
    0x19,
    0x0d,
    0x15,
    0xb7,
    0x04,
    0x74,
    0xf1,
    0x87,
    0xe6,
    0x3e,
    0xea,
    0x56,
    0xfa,
    0xa4,
    0x0d,
    0x03,
    0x19,
    0xc7,
    0x15,
    0xf4,
    0x05,
    0x89,
    0xf2,
    0xd3,
    0xe6,
    0x9c,
    0xe9,
    0x20,
    0xf9,
    0x91,
    0x0c,
    0xac,
    0x18,
    0x64,
    0x16,
    0x29,
    0x07,
    0xa5,
    0xf3,
    0x2a,
    0xe7,
    0x03,
    0xe9,
    0xef,
    0xf7,
    0x76,
    0x0b,
    0x4f,
    0x18,
    0xfc,
    0x16,
    0x57,
    0x08,
    0xc1,
    0xf4,
    0x92,
    0xe7,
    0x6c,
    0xe8,
    0xc2,
    0xf6,
    0x49,
    0x0a,
    0xe5,
    0x17,
    0x81,
    0x17,
    0x86,
    0x09,
    0xeb,
    0xf5,
    0xfc,
    0xe7,
    0xf2,
    0xe7,
    0x8f,
    0xf5,
    0x2d,
    0x09,
    0x67,
    0x17,
    0xf8,
    0x17,
    0xb0,
    0x0a,
    0x12,
    0xf7,
    0x83,
    0xe8,
    0x82,
    0xe7,
    0x6a,
    0xf4,
    0xfd,
    0x07,
    0xd5,
    0x16,
    0x65,
    0x18,
    0xca,
    0x0b,
    0x47,
    0xf8,
    0x19,
    0xe9,
    0x19,
    0xe7,
    0x5b,
    0xf3,
    0xc3,
    0x06,
    0x43,
    0x16,
    0xc4,
    0x18,
    0xdf,
    0x0c,
    0x7d,
    0xf9,
    0xb3,
    0xe9,
    0xc5,
    0xe6,
    0x48,
    0xf2,
    0x8b,
    0x05,
    0xa3,
    0x15,
    0x05,
    0x19,
    0xf8,
    0x0d,
    0xb5,
    0xfa,
    0x61,
    0xea,
    0x7b,
    0xe6,
    0x3a,
    0xf1,
    0x50,
    0x04,
    0xf2,
    0x14,
    0x45,
    0x19,
    0x02,
    0x0f,
    0xea,
    0xfb,
    0x1e,
    0xeb,
    0x4c,
    0xe6,
    0x3a,
    0xf0,
    0x1c,
    0x03,
    0x2c,
    0x14,
    0x76,
    0x19,
    0xfa,
    0x0f,
    0x35,
    0xfd,
    0xde,
    0xeb,
    0x2b,
    0xe6,
    0x3d,
    0xef,
    0xd9,
    0x01,
    0x5b,
    0x13,
    0x94,
    0x19,
    0xe7,
    0x10,
    0x70,
    0xfe,
    0xad,
    0xec,
    0x12,
    0xe6,
    0x50,
    0xee,
    0x99,
    0x00,
    0x82,
    0x12,
    0xa0,
    0x19,
    0xcc,
    0x11,
    0xb2,
    0xff,
    0x91,
    0xed,
    0x14,
    0xe6,
    0x76,
    0xed,
    0x58,
    0xff,
    0x9d,
    0x11,
    0x96,
    0x19,
    0xb3,
    0x12,
    0xf5,
    0x00,
    0x7c,
    0xee,
    0x15,
    0xe6,
    0x8d,
    0xec,
    0x11,
    0xfe,
    0xb0,
    0x10,
    0x87,
    0x19,
    0x8e,
    0x13,
    0x2f,
    0x02,
    0x6c,
    0xef,
    0x2f,
    0xe6,
    0xc1,
    0xeb,
    0xd9,
    0xfc,
    0xb6,
    0x0f,
    0x6f,
    0x19,
    0x5d,
    0x14,
    0x74,
    0x03,
    0x74,
    0xf0,
    0x4c,
    0xe6,
    0x00,
    0xeb,
    0x8d,
    0xfb,
    0xba,
    0x0e,
    0x3c,
    0x19,
    0x23,
    0x15,
    0xbb,
    0x04,
    0x7c,
    0xf1,
    0x8a,
    0xe6,
    0x3c,
    0xea,
    0x50,
    0xfa,
    0x9b,
    0x0d,
    0xfb,
    0x18,
    0xc5,
    0x15,
    0xfb,
    0x05,
    0x8e,
    0xf2,
    0xd2,
    0xe6,
    0x8f,
    0xe9,
    0x0f,
    0xf9,
    0x86,
    0x0c,
    0xb4,
    0x18,
    0x76,
    0x16,
    0x37,
    0x07,
    0xa5,
    0xf3,
    0x20,
    0xe7,
    0xf7,
    0xe8,
    0xea,
    0xf7,
    0x74,
    0x0b,
    0x55,
    0x18,
    0x02,
    0x17,
    0x5c,
    0x08,
    0xc9,
    0xf4,
    0x86,
    0xe7,
    0x72,
    0xe8,
    0xbb,
    0xf6,
    0x4d,
    0x0a,
    0xe4,
    0x17,
    0x7e,
    0x17,
    0x80,
    0x09,
    0xe4,
    0xf5,
    0xf4,
    0xe7,
    0xeb,
    0xe7,
    0x8c,
    0xf5,
    0x2b,
    0x09,
    0x6c,
    0x17,
    0x08,
    0x18,
    0xb1,
    0x0a,
    0x12,
    0xf7,
    0x85,
    0xe8,
    0x77,
    0xe7,
    0x75,
    0xf4,
    0xf9,
    0x07,
    0xe4,
    0x16,
    0x6a,
    0x18,
    0xce,
    0x0b,
    0x42,
    0xf8,
    0x10,
    0xe9,
    0x10,
    0xe7,
    0x57,
    0xf3,
    0xc8,
    0x06,
    0x4f,
    0x16,
    0xd4,
    0x18,
    0xed,
    0x0c,
    0x76,
    0xf9,
    0xad,
    0xe9,
    0xb6,
    0xe6,
    0x3d,
    0xf2,
    0x8c,
    0x05,
    0x99,
    0x15,
    0x16,
    0x19,
    0xfa,
    0x0d,
    0xc4,
    0xfa,
    0x6b,
    0xea,
    0x81,
    0xe6,
    0x33,
    0xf1,
    0x46,
    0x04,
    0xe6,
    0x14,
    0x54,
    0x19,
    0x08,
    0x0f,
    0x0e,
    0xfc,
    0x29,
    0xeb,
    0x3f,
    0xe6,
    0x04,
    0xf0,
    0x9a,
    0x02,
    0x39,
    0x13,
    0x6e,
    0x18,
    0xa6,
    0x0f,
    0x9c,
    0xfe,
    0x62,
    0xef,
    0x6d,
    0xea,
    0xa9,
    0xf1,
    0x1d,
    0x00,
    0x26,
    0x0d,
    0x9b,
    0x11,
    0xbd,
    0x0b,
    0xab,
    0xff,
    0xb2,
    0xf4,
    0xda,
    0xf0,
    0xb3,
    0xf5,
    0xc3,
    0xff,
    0x08,
    0x09,
    0x64,
    0x0c,
    0x74,
    0x08,
    0x0a,
    0x00,
    0x42,
    0xf8,
    0x5b,
    0xf5,
    0xa1,
    0xf8,
    0x98,
    0xff,
    0x2c,
    0x06,
    0xa3,
    0x08,
    0x00,
    0x06,
    0x27,
    0x00,
    0x9b,
    0xfa,
    0x82,
    0xf8,
    0xa8,
    0xfa,
    0x8d,
    0xff,
    0x37,
    0x04,
    0x07,
    0x06,
    0x4a,
    0x04,
    0x30,
    0x00,
    0x43,
    0xfc,
    0xa5,
    0xfa,
    0x0f,
    0xfc,
    0x81,
    0xff,
    0xd5,
    0x02,
    0x38,
    0x04,
    0x0d,
    0x03,
    0x27,
    0x00,
    0x5a,
    0xfd,
    0x30,
    0xfc,
    0x26,
    0xfd,
    0x8a,
    0xff,
    0xe4,
    0x01,
    0xe8,
    0x02,
    0x17,
    0x02,
    0x1a,
    0x00,
    0x1e,
    0xfe,
    0x49,
    0xfd,
    0xf0,
    0xfd,
    0xa1,
    0xff,
    0x46,
    0x01,
    0xfe,
    0x01,
    0x71,
    0x01,
    0x0e,
    0x00,
    0xa9,
    0xfe,
    0x0f,
    0xfe,
    0x82,
    0xfe,
    0xa5,
    0xff,
    0xd8,
    0x00,
    0x57,
    0x01,
    0xfe,
    0x00,
    0xff,
    0xff,
    0x08,
    0xff,
    0x91,
    0xfe,
    0xe3,
    0xfe,
    0xae,
    0xff,
    0x7c,
    0x00,
    0xe2,
    0x00,
    0x9e,
    0x00,
    0xff,
    0xff,
    0x49,
    0xff,
    0xf9,
    0xfe,
    0x22,
    0xff,
    0xbc,
    0xff,
    0x51,
    0x00,
    0xa7,
    0x00,
    0x75,
    0x00,
    0xff,
    0xff,
    0x75,
    0xff,
    0x36,
    0xff,
    0x4e,
    0xff,
    0xc1,
    0xff,
    0x2b,
    0x00,
    0x5e,
    0x00,
    0x51,
    0x00,
    0xeb,
    0xff,
    0xa5,
    0xff,
    0x65,
    0xff,
    0x7d,
    0xff,
    0xc2,
    0xff,
    0x14,
    0x00,
    0x39,
    0x00,
    0x2b,
    0x00,
    0xe7,
    0xff,
    0xa5,
    0xff,
    0x81,
    0xff,
    0x8c,
    0xff,
    0xc9,
    0xff,
    0xf9,
    0xff,
    0x22,
    0x00,
    0x0e,
    0x00,
    0xe8,
    0xff,
    0xaf,
    0xff,
    0x9f,
    0xff,
    0xa5,
    0xff,
    0xd7,
    0xff,
    0xf5,
    0xff,
    0x14,
    0x00,
    0x00,
    0x00,
    0xe3,
    0xff,
    0xbe,
    0xff,
    0xaf,
    0xff,
    0xb9,
    0xff,
    0xd7,
    0xff,
    0xee,
    0xff,
    0xfa,
    0xff,
    0xef,
    0xff,
    0xe0,
    0xff,
    0xbd,
    0xff,
    0xbb,
    0xff,
    0xbd,
    0xff,
    0xd1,
    0xff,
    0xed,
    0xff,
    0xf6,
    0xff,
    0xf3,
    0xff,
    0xdd,
    0xff,
    0xcf,
    0xff,
    0xb6,
    0xff,
    0xc9,
    0xff,
    0xca,
    0xff,
    0xdf,
    0xff,
    0xe7,
    0xff,
    0xe8,
    0xff,
    0xde,
    0xff,
    0xd4,
    0xff,
    0xc9,
    0xff,
    0xcc,
    0xff,
    0xce,
    0xff,
    0xe1,
    0xff,
    0xe9,
    0xff,
    0xe5,
    0xff,
    0xe2,
    0xff,
    0xd3,
    0xff,
    0xc8,
    0xff,
    0xd4,
    0xff,
    0xcd,
    0xff,
    0xe1,
    0xff,
    0xe0,
    0xff,
    0xdf,
    0xff,
    0xdc,
    0xff,
    0xd4,
    0xff,
    0xcf,
    0xff,
    0xcd,
    0xff,
    0xd8,
    0xff,
    0xd9,
    0xff,
    0xe0,
    0xff
)
var ErrPcm = byteArrayOfInts(
    0x10,
    0x00,
    0xf0,
    0xff,
    0x0d,
    0x00,
    0xf0,
    0xff,
    0x10,
    0x00,
    0xee,
    0xff,
    0x22,
    0x00,
    0xe9,
    0xff,
    0x14,
    0x00,
    0xe9,
    0xff,
    0x19,
    0x00,
    0xee,
    0xff,
    0x22,
    0x00,
    0x9f,
    0xff,
    0x06,
    0xff,
    0x11,
    0xff,
    0xfe,
    0xfe,
    0x63,
    0xff,
    0xfd,
    0xff,
    0x1c,
    0x00,
    0xf6,
    0xfe,
    0xb4,
    0xff,
    0x31,
    0xfa,
    0x04,
    0xfb,
    0x72,
    0x03,
    0x4e,
    0x06,
    0xb9,
    0xff,
    0x94,
    0xf9,
    0x7d,
    0xfc,
    0x92,
    0x01,
    0xab,
    0x02,
    0xb7,
    0xff,
    0xae,
    0xff,
    0xfe,
    0xff,
    0x17,
    0xff,
    0xc3,
    0xfd,
    0x06,
    0xff,
    0x3e,
    0x01,
    0x49,
    0x00,
    0xeb,
    0xfd,
    0x60,
    0xfe,
    0x33,
    0x00,
    0x10,
    0x00,
    0x15,
    0xff,
    0x39,
    0xff,
    0xbf,
    0xff,
    0xe5,
    0xff,
    0x44,
    0xff,
    0x99,
    0xff,
    0x2b,
    0x00,
    0x17,
    0x00,
    0x51,
    0x00,
    0x25,
    0x00,
    0xd9,
    0x00,
    0x99,
    0xff,
    0x8c,
    0xfc,
    0x92,
    0xf9,
    0xa7,
    0xfb,
    0xea,
    0x01,
    0x16,
    0x03,
    0x1e,
    0x01,
    0x2e,
    0xfe,
    0xd1,
    0xfc,
    0x55,
    0xfe,
    0xec,
    0xff,
    0x6b,
    0x01,
    0x5a,
    0x01,
    0xb0,
    0x00,
    0x78,
    0xfd,
    0x99,
    0xfd,
    0x71,
    0xfe,
    0x5b,
    0xf9,
    0x0d,
    0xfd,
    0xf9,
    0x05,
    0xe0,
    0x04,
    0x8c,
    0xfd,
    0x89,
    0xfc,
    0x08,
    0xfe,
    0x88,
    0xfb,
    0x1e,
    0xfd,
    0x4d,
    0x03,
    0xb1,
    0x07,
    0x0a,
    0x03,
    0xb3,
    0xfa,
    0x4c,
    0xfa,
    0xbe,
    0xfd,
    0xd0,
    0xfe,
    0x04,
    0x01,
    0x43,
    0x05,
    0x84,
    0x06,
    0xe4,
    0x01,
    0x5f,
    0xfb,
    0x18,
    0xf9,
    0x7b,
    0xfc,
    0xd5,
    0x00,
    0xe8,
    0x02,
    0x4c,
    0x02,
    0xa8,
    0x00,
    0x80,
    0xff,
    0x4c,
    0xff,
    0x5d,
    0x00,
    0xb2,
    0x00,
    0xe4,
    0x00,
    0xfb,
    0xff,
    0xbe,
    0xfd,
    0x0b,
    0xfc,
    0x33,
    0xfc,
    0x53,
    0xfd,
    0x37,
    0x00,
    0x40,
    0x02,
    0xdd,
    0x01,
    0x76,
    0x01,
    0x04,
    0x03,
    0x77,
    0x02,
    0xae,
    0x02,
    0xb8,
    0xff,
    0x7f,
    0xfa,
    0xd3,
    0xfa,
    0xf3,
    0xfd,
    0x4e,
    0xfc,
    0xc8,
    0xfb,
    0xb6,
    0x00,
    0x39,
    0x06,
    0x89,
    0x08,
    0x8e,
    0x06,
    0x0d,
    0x05,
    0xc9,
    0x09,
    0x9b,
    0x0a,
    0xb6,
    0xf7,
    0xc4,
    0xe2,
    0xc5,
    0xdc,
    0x9d,
    0xfc,
    0x70,
    0x17,
    0xf9,
    0x14,
    0x99,
    0x00,
    0xf5,
    0xfd,
    0x3f,
    0x08,
    0x9d,
    0x1e,
    0x63,
    0x1d,
    0xb6,
    0xd4,
    0x28,
    0xbe,
    0x68,
    0xf2,
    0x67,
    0x2a,
    0xd2,
    0x14,
    0x0c,
    0xe7,
    0xcb,
    0xe5,
    0xe2,
    0x07,
    0x1e,
    0x1f,
    0xf7,
    0x1e,
    0x5a,
    0x26,
    0x89,
    0xf7,
    0x01,
    0xb5,
    0xa7,
    0xc5,
    0xa8,
    0x17,
    0x44,
    0x3e,
    0xd7,
    0x02,
    0x38,
    0xca,
    0x04,
    0xe0,
    0xb7,
    0x1d,
    0x32,
    0x32,
    0x44,
    0x1e,
    0xcb,
    0x1a,
    0x04,
    0xe3,
    0x79,
    0xb2,
    0xf7,
    0xd1,
    0xe3,
    0x26,
    0x2f,
    0x3a,
    0x18,
    0xf2,
    0x2b,
    0xca,
    0xcd,
    0xf2,
    0x6c,
    0x2f,
    0x26,
    0x2b,
    0xb0,
    0x19,
    0x66,
    0x01,
    0xe6,
    0xce,
    0x1e,
    0xc2,
    0xf0,
    0xf5,
    0x3c,
    0x2e,
    0xee,
    0x18,
    0x95,
    0xda,
    0x90,
    0xda,
    0xa6,
    0x12,
    0xd5,
    0x34,
    0x26,
    0x1f,
    0xdc,
    0x13,
    0x85,
    0xed,
    0x11,
    0xbf,
    0xb2,
    0xd0,
    0x9d,
    0x13,
    0xef,
    0x32,
    0x1a,
    0xfa,
    0x76,
    0xd3,
    0xab,
    0xf0,
    0x21,
    0x2a,
    0xf4,
    0x2d,
    0x2f,
    0x19,
    0xb4,
    0xfe,
    0xce,
    0xd2,
    0xaf,
    0xcc,
    0x64,
    0xf6,
    0x68,
    0x21,
    0xbc,
    0x0e,
    0xc5,
    0xe4,
    0x1c,
    0xe7,
    0x24,
    0x13,
    0x1a,
    0x2d,
    0xa3,
    0x1c,
    0xea,
    0x12,
    0xa6,
    0xe6,
    0x7f,
    0xbf,
    0xc3,
    0xdd,
    0x16,
    0x1a,
    0x1e,
    0x27,
    0x8d,
    0xee,
    0x45,
    0xdc,
    0x01,
    0x00,
    0x0d,
    0x2c,
    0x79,
    0x23,
    0x0a,
    0x16,
    0xa3,
    0xfe,
    0x95,
    0xc9,
    0x19,
    0xcc,
    0xfd,
    0xff,
    0x85,
    0x29,
    0xc0,
    0x03,
    0xb3,
    0xe0,
    0xc0,
    0xf0,
    0x95,
    0x22,
    0x76,
    0x2a,
    0x4a,
    0x18,
    0x7a,
    0x0a,
    0xe8,
    0xcf,
    0x9a,
    0xc4,
    0xec,
    0xf1,
    0x57,
    0x28,
    0xc3,
    0x12,
    0x27,
    0xe3,
    0x68,
    0xe6,
    0xfb,
    0x16,
    0x90,
    0x2d,
    0x9a,
    0x1a,
    0xcc,
    0x12,
    0xfb,
    0xdb,
    0x54,
    0xc0,
    0xf5,
    0xe4,
    0x19,
    0x20,
    0x40,
    0x1d,
    0x1e,
    0xeb,
    0xe2,
    0xe0,
    0xe4,
    0x0b,
    0x89,
    0x30,
    0x1b,
    0x20,
    0xfd,
    0x15,
    0x02,
    0xdf,
    0x3c,
    0xbd,
    0x63,
    0xe0,
    0xe6,
    0x1d,
    0x34,
    0x24,
    0x73,
    0xed,
    0x0a,
    0xdc,
    0xa1,
    0x03,
    0x13,
    0x33,
    0xf1,
    0x22,
    0x62,
    0x18,
    0x9b,
    0xe5,
    0x01,
    0xbd,
    0x96,
    0xd9,
    0xcd,
    0x16,
    0xf8,
    0x28,
    0x8f,
    0xf4,
    0x09,
    0xdb,
    0xf3,
    0xfc,
    0x6b,
    0x31,
    0x0d,
    0x24,
    0x25,
    0x1a,
    0x4a,
    0xe9,
    0xca,
    0xbd,
    0x17,
    0xd9,
    0xbe,
    0x15,
    0xc3,
    0x29,
    0xea,
    0xf4,
    0x3d,
    0xdb,
    0x56,
    0xfa,
    0x11,
    0x30,
    0x20,
    0x25,
    0xc7,
    0x1c,
    0x83,
    0xec,
    0x77,
    0xb9,
    0x15,
    0xd5,
    0x20,
    0x17,
    0xb8,
    0x30,
    0x13,
    0xf7,
    0x2b,
    0xd6,
    0x4b,
    0xf5,
    0x0a,
    0x32,
    0x82,
    0x2a,
    0xbe,
    0x18,
    0x1d,
    0xe9,
    0xba,
    0xbd,
    0xb9,
    0xd8,
    0xc6,
    0x12,
    0x54,
    0x2d,
    0x1f,
    0xfc,
    0xf4,
    0xd9,
    0xe4,
    0xf1,
    0x9c,
    0x2b,
    0x6e,
    0x2c,
    0xc3,
    0x1b,
    0x4a,
    0xe9,
    0x79,
    0xbc,
    0x04,
    0xd8,
    0xba,
    0x13,
    0xdb,
    0x2b,
    0x8a,
    0xfd,
    0xc8,
    0xdc,
    0x0a,
    0xf3,
    0xa3,
    0x27,
    0x0a,
    0x29,
    0x87,
    0x1c,
    0x28,
    0xec,
    0x1f,
    0xbf,
    0x9b,
    0xd7,
    0xf6,
    0x11,
    0xb7,
    0x2a,
    0x28,
    0xfe,
    0x3b,
    0xde,
    0x95,
    0xf3,
    0x86,
    0x26,
    0xdb,
    0x27,
    0x35,
    0x1d,
    0xd8,
    0xea,
    0x9f,
    0xbe,
    0xa2,
    0xd9,
    0x03,
    0x13,
    0x65,
    0x2a,
    0xdb,
    0xfd,
    0xab,
    0xde,
    0xf3,
    0xf3,
    0x57,
    0x25,
    0x5b,
    0x27,
    0x27,
    0x1e,
    0xcf,
    0xe8,
    0x8c,
    0xbf,
    0x96,
    0xdc,
    0x5d,
    0x13,
    0x3e,
    0x27,
    0x31,
    0xfd,
    0xda,
    0xe0,
    0xb5,
    0xf5,
    0x08,
    0x24,
    0x39,
    0x26,
    0x88,
    0x1e,
    0xa3,
    0xe5,
    0x97,
    0xbf,
    0xf6,
    0xdf,
    0xdc,
    0x16,
    0x92,
    0x25,
    0xda,
    0xf9,
    0x19,
    0xe0,
    0x60,
    0xf7,
    0xf0,
    0x24,
    0xe8,
    0x25,
    0x24,
    0x1c,
    0x6f,
    0xe5,
    0x56,
    0xc2,
    0x3b,
    0xe1,
    0xda,
    0x16,
    0x77,
    0x22,
    0xab,
    0xf8,
    0xdf,
    0xe1,
    0x1b,
    0xfb,
    0x11,
    0x24,
    0xf2,
    0x20,
    0x04,
    0x19,
    0xaa,
    0xe7,
    0x42,
    0xc6,
    0xd9,
    0xe1,
    0xf1,
    0x14,
    0xf3,
    0x20,
    0x90,
    0xf8,
    0x53,
    0xe1,
    0xc2,
    0xfa,
    0xea,
    0x23,
    0x77,
    0x20,
    0x9d,
    0x19,
    0x13,
    0xea,
    0x23,
    0xc7,
    0xcb,
    0xe0,
    0x74,
    0x12,
    0x13,
    0x21,
    0x1a,
    0xfa,
    0xb4,
    0xe2,
    0x33,
    0xfa,
    0xab,
    0x22,
    0xc8,
    0x1e,
    0xd3,
    0x15,
    0xf3,
    0xec,
    0xec,
    0xcc,
    0x60,
    0xe3,
    0x03,
    0x0f,
    0x1c,
    0x1d,
    0x55,
    0xfb,
    0xc0,
    0xe4,
    0x5c,
    0xf9,
    0x68,
    0x20,
    0xda,
    0x1c,
    0x78,
    0x15,
    0xa8,
    0xf3,
    0x1d,
    0xd1,
    0xe1,
    0xdf,
    0x61,
    0x08,
    0x63,
    0x1c,
    0xe9,
    0xff,
    0x50,
    0xe8,
    0xf0,
    0xf7,
    0xf0,
    0x1c,
    0x2a,
    0x1b,
    0x97,
    0x11,
    0xab,
    0xf8,
    0xd1,
    0xd7,
    0xed,
    0xe0,
    0xe2,
    0x02,
    0x41,
    0x19,
    0x74,
    0x02,
    0x44,
    0xea,
    0x8f,
    0xf5,
    0x3f,
    0x19,
    0x50,
    0x1c,
    0x79,
    0x0f,
    0xa2,
    0xfd,
    0xca,
    0xde,
    0xfd,
    0xe0,
    0xfe,
    0xfb,
    0xad,
    0x14,
    0x6b,
    0x07,
    0x92,
    0xef,
    0x3a,
    0xf2,
    0x0e,
    0x13,
    0xf3,
    0x1a,
    0x60,
    0x0f,
    0x0a,
    0x05,
    0xcb,
    0xe4,
    0xd3,
    0xde,
    0x1e,
    0xf5,
    0xcb,
    0x10,
    0xfc,
    0x0b,
    0x89,
    0xf3,
    0x64,
    0xf0,
    0x72,
    0x0c,
    0x38,
    0x1b,
    0x73,
    0x0f,
    0x8b,
    0x05,
    0xcf,
    0xec,
    0x5d,
    0xe1,
    0x73,
    0xf0,
    0xb8,
    0x09,
    0xe3,
    0x0d,
    0xfd,
    0xf8,
    0x05,
    0xf1,
    0x82,
    0x06,
    0x73,
    0x19,
    0x6e,
    0x0e,
    0x42,
    0x04,
    0x8c,
    0xff,
    0x06,
    0x00,
    0xed,
    0xff,
    0x69,
    0xff,
    0x0c,
    0xfe,
    0x94,
    0xfb,
    0x7e,
    0xfe,
    0xc9,
    0x02,
    0x20,
    0x04,
    0x73,
    0x00,
    0xb0,
    0xfe,
    0xdd,
    0xfe,
    0x91,
    0xff,
    0x29,
    0x00,
    0x49,
    0xff,
    0xe8,
    0xfe,
    0x38,
    0xfd,
    0x0f,
    0xfe,
    0xc5,
    0x00,
    0x1a,
    0x03,
    0x75,
    0x01,
    0x77,
    0xff,
    0x8a,
    0xfe,
    0x64,
    0xff,
    0xf6,
    0xff,
    0x25,
    0x00,
    0x5d,
    0xff,
    0x3b,
    0xfd,
    0x1b,
    0xfd,
    0x9d,
    0xff,
    0xe9,
    0x02,
    0x20,
    0x02,
    0xd0,
    0xff,
    0xcf,
    0xfe,
    0x6b,
    0xff,
    0xaa,
    0xff,
    0x14,
    0x00,
    0xf1,
    0xff,
    0x80,
    0xfe,
    0x9c,
    0xfd,
    0x69,
    0xff,
    0x60,
    0x01,
    0x25,
    0x01,
    0xd5,
    0xff,
    0xf5,
    0xfe,
    0x7a,
    0xff,
    0xfa,
    0xff,
    0x72,
    0xff,
    0xe1,
    0xfe,
    0xe5,
    0xfe,
    0x1a,
    0xfe,
    0x45,
    0xfe,
    0xd2,
    0xff,
    0x13,
    0x01,
    0xf7,
    0x00,
    0x8c,
    0xff,
    0x53,
    0xff,
    0xfd,
    0xff,
    0x83,
    0xff,
    0x4a,
    0xff,
    0x47,
    0xff,
    0xb7,
    0xff,
    0xb1,
    0xff,
    0x40,
    0x00,
    0x7c,
    0x00,
    0xc7,
    0xfe,
    0x26,
    0xfe,
    0xd6,
    0xff,
    0xbb,
    0x00,
    0x30,
    0x00,
    0x6a,
    0xfe,
    0xa3,
    0xfd,
    0x29,
    0xfe,
    0xde,
    0xfe,
    0x11,
    0xff,
    0xe5,
    0xfe,
    0x89,
    0xff,
    0x0d,
    0xff,
    0x2b,
    0xff,
    0x7e,
    0xff,
    0x06,
    0x00,
    0x23,
    0x00,
    0xcd,
    0xff,
    0x11,
    0xff,
    0x12,
    0xff,
    0xea,
    0xff,
    0x37,
    0x01,
    0x60,
    0x01,
    0x2a,
    0x00,
    0x19,
    0x00,
    0x36,
    0x00,
    0x9f,
    0xff,
    0x8c,
    0xfd,
    0x93,
    0xfd,
    0x21,
    0x00,
    0x1b,
    0x01,
    0x22,
    0xff,
    0x0d,
    0xfe,
    0x6b,
    0xfe,
    0x34,
    0x00,
    0xe6,
    0x00,
    0x21,
    0xfe,
    0x39,
    0xfc,
    0x8a,
    0xfd,
    0xc2,
    0xff,
    0x19,
    0x00,
    0xf8,
    0xff,
    0x5c,
    0xff,
    0xe7,
    0xfe,
    0xb6,
    0xff,
    0xdd,
    0x00,
    0xf0,
    0x00,
    0x3e,
    0x00,
    0xe2,
    0xff,
    0x6c,
    0x00,
    0x4d,
    0x01,
    0x0f,
    0x01,
    0x09,
    0x00,
    0xdc,
    0xfe,
    0x81,
    0xfd,
    0x6e,
    0xfd,
    0xfa,
    0xfe,
    0xcc,
    0x00,
    0xed,
    0x01,
    0xf6,
    0xff,
    0x5b,
    0xfd,
    0xfc,
    0xfd,
    0x3d,
    0x00,
    0x6f,
    0x00,
    0xe4,
    0xfc,
    0x7d,
    0xfb,
    0xba,
    0xfd,
    0x7e,
    0x00,
    0xf8,
    0x00,
    0x28,
    0x00,
    0xd0,
    0xff,
    0x1d,
    0xff,
    0x44,
    0xff,
    0xd0,
    0x00,
    0x10,
    0x02,
    0xfd,
    0xff,
    0xe5,
    0xfe,
    0x04,
    0x00,
    0xa6,
    0x01,
    0x3b,
    0x01,
    0xb5,
    0xff,
    0x65,
    0xfe,
    0xac,
    0xfd,
    0x4d,
    0xfe,
    0x9a,
    0xff,
    0x08,
    0x01,
    0xfb,
    0xff,
    0x42,
    0xfe,
    0x0a,
    0xfe,
    0x26,
    0xff,
    0x7a,
    0x00,
    0x6c,
    0x01,
    0xa6,
    0xfe,
    0x9b,
    0xfb,
    0xdd,
    0xfc,
    0x12,
    0x00,
    0x4a,
    0x01,
    0x49,
    0x00,
    0x39,
    0xff,
    0xe0,
    0xfe,
    0x74,
    0xff,
    0x9e,
    0x00,
    0x63,
    0x01,
    0x8d,
    0x01,
    0x1f,
    0x00,
    0xee,
    0xfe,
    0x8a,
    0x00,
    0xbb,
    0x00,
    0xf3,
    0xfe,
    0x4c,
    0xfd,
    0xa8,
    0xfd,
    0x06,
    0xff,
    0xb3,
    0xff,
    0x01,
    0x00,
    0x10,
    0x00,
    0x7b,
    0xff,
    0xdc,
    0xfe,
    0x61,
    0xff,
    0x09,
    0x00,
    0xd0,
    0x00,
    0x9c,
    0xfe,
    0x65,
    0xfc,
    0x0a,
    0xfe,
    0x5a,
    0x00,
    0x28,
    0x01,
    0x84,
    0x00,
    0xfb,
    0xff,
    0x12,
    0xff,
    0x08,
    0xff,
    0xa6,
    0xff,
    0xc3,
    0x00,
    0xe7,
    0x00,
    0x81,
    0xff,
    0xfc,
    0xfe,
    0x90,
    0xfe,
    0xbd,
    0xfe,
    0xa3,
    0xff,
    0x06,
    0x00,
    0x12,
    0x00,
    0x75,
    0xff,
    0xf4,
    0xfe,
    0x06,
    0xff,
    0x0f,
    0xff,
    0xf6,
    0xff,
    0xdd,
    0xff,
    0xfc,
    0xfe,
    0x01,
    0xff,
    0xfd,
    0xff,
    0xa4,
    0xff,
    0xe2,
    0xfe,
    0x6f,
    0xff,
    0xfb,
    0xff,
    0xdd,
    0xff,
    0x0a,
    0xff,
    0xf7,
    0xfe,
    0x16,
    0xff,
    0x05,
    0xff,
    0x65,
    0xff,
    0x13,
    0x00,
    0xf1,
    0xff,
    0x36,
    0xff,
    0xe9,
    0xfe,
    0xd5,
    0xff,
    0x0a,
    0x00,
    0x18,
    0xff,
    0x07,
    0xff,
    0x02,
    0xff,
    0x09,
    0xff,
    0xe5,
    0xff,
    0x23,
    0x00,
    0x03,
    0x00,
    0x06,
    0x00,
    0x18,
    0xff,
    0x05,
    0xff,
    0x11,
    0xff,
    0x21,
    0xff,
    0x00,
    0xff,
    0x03,
    0xff,
    0x14,
    0xff,
    0xf1,
    0xff,
    0xec,
    0xff,
    0x15,
    0x00,
    0xef,
    0xff,
    0x23,
    0xff,
    0xd0,
    0xff,
    0x63,
    0xff,
    0x48,
    0xff,
    0xf4,
    0xfe,
    0x0f,
    0xff,
    0xef,
    0xfe,
    0x82,
    0xff,
    0x11,
    0x00,
    0x1b,
    0x00,
    0x90,
    0xff,
    0x20,
    0xff,
    0xf5,
    0xff,
    0x16,
    0x00,
    0x9c,
    0xff,
    0xfc,
    0xfe,
    0x15,
    0xff,
    0x45,
    0xff,
    0x1c,
    0x00,
    0x12,
    0x00,
    0x07,
    0x00,
    0x15,
    0xff,
    0x3e,
    0xff,
    0x9c,
    0xff,
    0xd3,
    0xff,
    0x0f,
    0x00,
    0x24,
    0xff,
    0x15,
    0xff,
    0x09,
    0xff,
    0xec,
    0xff,
    0x07,
    0x00,
    0x2d,
    0x00,
    0xbb,
    0xff,
    0xcd,
    0xfe,
    0x92,
    0xff,
    0x0f,
    0x00,
    0xe4,
    0xff,
    0x19,
    0xff,
    0x0e,
    0xff,
    0x0d,
    0xff,
    0xb5,
    0xff,
    0x18,
    0x00,
    0xf1,
    0xff,
    0x0c,
    0x00,
    0xeb,
    0xff,
    0x1f,
    0x00,
    0xff,
    0xff,
    0x11,
    0x00,
    0xa0,
    0xff,
    0xea,
    0xfe,
    0xa6,
    0xff,
    0x12,
    0x00,
    0xfb,
    0xff,
    0x02,
    0x00,
    0x07,
    0x00,
    0x03,
    0x00,
    0x01,
    0x00,
    0x02,
    0x00,
    0x09,
    0x00,
    0xd0,
    0xff,
    0x87,
    0xff,
    0xb5,
    0xff,
    0xdb,
    0xff,
    0x0b,
    0x00,
    0x09,
    0x00,
    0xbf,
    0xff,
    0xd5,
    0xff,
    0x1e,
    0x00,
    0xf5,
    0xff,
    0xdc,
    0xff,
    0x8a,
    0xff,
    0xe7,
    0xff,
    0xfc,
    0xff,
    0x0a,
    0x00,
    0xf8,
    0xff,
    0x17,
    0x00,
    0xda,
    0xff,
    0x22,
    0xff,
    0xf0,
    0xff,
    0x00,
    0x00,
    0x02,
    0x00,
    0xff,
    0xff,
    0x0a,
    0x00,
    0xa0,
    0xff,
    0x50,
    0xff,
    0xed,
    0xff,
    0x08,
    0x00,
    0xcd,
    0xff,
    0xf2,
    0xfe,
    0x6c,
    0xff,
    0x15,
    0x00,
    0xeb,
    0xff,
    0x1a,
    0x00,
    0x95,
    0xff,
    0xff,
    0xfe,
    0xee,
    0xff,
    0x02,
    0x00,
    0xf3,
    0xff,
    0x14,
    0x00,
    0xa2,
    0xff,
    0x09,
    0x00,
    0xfe,
    0xff,
    0xff,
    0xff,
    0xda,
    0xff,
    0x52,
    0xff,
    0x79,
    0xff,
    0xf7,
    0xff,
    0xfe,
    0xff,
    0xfb,
    0xff,
    0xf2,
    0xff,
    0xfd,
    0xfe,
    0xa8,
    0xff,
    0x11,
    0x00,
    0xfa,
    0xff,
    0xee,
    0xff,
    0x3c,
    0xff,
    0x55,
    0xff,
    0xbc,
    0xff,
    0x13,
    0x00,
    0xf2,
    0xff,
    0x04,
    0x00,
    0xfe,
    0xff,
    0x00,
    0x00,
    0xfe,
    0xff,
    0xfa,
    0xff,
    0x0a,
    0x00,
    0x7d,
    0xff,
    0xbb,
    0xff,
    0x05,
    0x00,
    0xf6,
    0xff,
    0x02,
    0x00,
    0x02,
    0x00,
    0x00,
    0x00,
    0x01,
    0x00,
    0x02,
    0x00,
    0xf7,
    0xff,
    0x0e,
    0x00,
    0xa7,
    0xff,
    0x17,
    0x00,
    0xd5,
    0xff,
    0xbe,
    0xff,
    0x0c,
    0x00,
    0xff,
    0xff,
    0xff,
    0xff,
    0x05,
    0x00,
    0xfb,
    0xff,
    0x0a,
    0x00,
    0xf6,
    0xff,
    0x11,
    0x00,
    0xc9,
    0xff,
    0x00,
    0xff,
    0xbc,
    0xff,
    0x1b,
    0x00,
    0xf0,
    0xff,
    0x0e,
    0x00,
    0xf4,
    0xff,
    0x0a,
    0x00,
    0xf3,
    0xff,
    0x0d,
    0x00,
    0xef,
    0xff,
    0x15,
    0x00,
    0xde,
    0xff,
    0xbe,
    0xff,
    0xfe,
    0xff,
    0x08,
    0x00,
    0xf8,
    0xff,
    0x06,
    0x00,
    0x01,
    0x00,
    0xd4,
    0xff,
    0x5f,
    0xff,
    0xc1,
    0xff,
    0xfc,
    0xff,
    0x0d,
    0x00,
    0x9b,
    0xff,
    0x7c,
    0xff,
    0xca,
    0xff,
    0x13,
    0x00,
    0xf3,
    0xff,
    0x0c,
    0x00,
    0xdf,
    0xff,
    0x0b,
    0xff,
    0x6f,
    0xff,
    0xd0,
    0xff,
    0xa1,
    0xff,
    0x02,
    0xff,
    0x82,
    0xff,
    0x73,
    0xfd,
    0x58,
    0x00,
    0x12,
    0x04,
    0x7e,
    0xff,
    0xfa,
    0xfa,
    0xe6,
    0xfd,
    0xa0,
    0x02,
    0x24,
    0x01,
    0x49,
    0xff,
    0x0f,
    0xff,
    0xed,
    0xfe,
    0x18,
    0xfe,
    0x98,
    0xff,
    0xbf,
    0x01,
    0xc0,
    0x00,
    0x45,
    0xfe,
    0x32,
    0xfe,
    0xb5,
    0xff,
    0xf5,
    0xff,
    0x12,
    0x00,
    0xb3,
    0xff,
    0x02,
    0xff,
    0xf8,
    0xfe,
    0x66,
    0xff,
    0xfc,
    0xff,
    0x15,
    0x00,
    0x97,
    0xff,
    0xf7,
    0xfe,
    0x0b,
    0xff,
    0x8b,
    0xff,
    0x1a,
    0x00,
    0xe7,
    0xff,
    0x26,
    0xff,
    0x05,
    0xff,
    0x03,
    0xff,
    0xad,
    0xff,
    0x28,
    0x00,
    0xcd,
    0xff,
    0x1f,
    0xff,
    0x19,
    0xff,
    0x04,
    0xff,
    0x1f,
    0xff,
    0x64,
    0xff,
    0x04,
    0xff,
    0x25,
    0xff,
    0xf9,
    0xfe,
    0x87,
    0xff,
    0x1e,
    0x00,
    0x56,
    0xff,
    0x03,
    0xff,
    0x1c,
    0xff,
    0x0e,
    0xff,
    0x0e,
    0xff,
    0x07,
    0xff,
    0x14,
    0xff,
    0x13,
    0xff,
    0x0c,
    0xff,
    0x08,
    0xff,
    0x0f,
    0xff,
    0x16,
    0xff,
    0x0d,
    0xff,
    0x06,
    0xff,
    0x08,
    0xff,
    0x0f,
    0xff,
    0x1c,
    0xff,
    0x19,
    0xff,
    0x0a,
    0xff,
    0x08,
    0xff,
    0x0a,
    0xff,
    0x11,
    0xff,
    0x13,
    0xff,
    0x15,
    0xff,
    0x0c,
    0xff,
    0x01,
    0xff,
    0x0e,
    0xff,
    0x0a,
    0xff,
    0x10,
    0xff,
    0x0f,
    0xff,
    0x09,
    0xff,
    0x0c,
    0xff,
    0x0d,
    0xff,
    0x0b,
    0xff,
    0x0f,
    0xff,
    0x07,
    0xff,
    0x10,
    0xff,
    0x09,
    0xff,
    0x0f,
    0xff,
    0x08,
    0xff,
    0x0e,
    0xff,
    0x0a,
    0xff,
    0x0e,
    0xff,
    0x0b,
    0xff,
    0x0d,
    0xff,
    0x0c,
    0xff,
    0x0f,
    0xff,
    0x0c,
    0xff,
    0x0d,
    0xff,
    0x0d,
    0xff,
    0x0d,
    0xff,
    0x0e,
    0xff,
    0x0a,
    0xff,
    0x12,
    0xff,
    0x08,
    0xff,
    0x11,
    0xff,
    0x0d,
    0xff,
    0x0c,
    0xff,
    0x13,
    0xff,
    0x07,
    0xff,
    0x13,
    0xff,
    0x09,
    0xff,
    0x0e,
    0xff,
    0x0f,
    0xff,
    0x0b,
    0xff,
    0x10,
    0xff,
    0x0a,
    0xff,
    0x10,
    0xff,
    0x0b,
    0xff,
    0x12,
    0xff,
    0x08,
    0xff,
    0x11,
    0xff,
    0x06,
    0xff,
    0x17,
    0xff,
    0xfa,
    0xfe,
    0x48,
    0xff,
    0x43,
    0xff,
    0xfd,
    0xfe,
    0x16,
    0xff,
    0x09,
    0xff,
    0x0e,
    0xff,
    0x0f,
    0xff,
    0x0b,
    0xff,
    0x11,
    0xff,
    0x0b,
    0xff,
    0x11,
    0xff,
    0x0b,
    0xff,
    0x10,
    0xff,
    0x0c,
    0xff,
    0x0e,
    0xff,
    0x0d,
    0xff,
    0x0b,
    0xff,
    0x11,
    0xff,
    0x07,
    0xff,
    0x10,
    0xff,
    0x06,
    0xff,
    0x16,
    0xff,
    0xf9,
    0xfe,
    0xc2,
    0xff,
    0xd6,
    0xff,
    0xee,
    0xfe,
    0x1e,
    0xff,
    0x01,
    0xff,
    0x13,
    0xff,
    0x08,
    0xff,
    0x0f,
    0xff,
    0x0c,
    0xff,
    0x07,
    0xff,
    0x5f,
    0xff,
    0x1b,
    0xff,
    0x0b,
    0xff,
    0x0b,
    0xff,
    0x11,
    0xff,
    0x00,
    0xff,
    0x30,
    0xff,
    0x58,
    0xff,
    0xf9,
    0xfe,
    0x1d,
    0xff,
    0xfb,
    0xfe,
    0x47,
    0xff,
    0x69,
    0xff,
    0xf6,
    0xff,
    0x95,
    0xff,
    0xec,
    0xfe,
    0x30,
    0xff,
    0x70,
    0xff,
    0x95,
    0xff,
    0x82,
    0xff,
    0x88,
    0xff,
    0x0b,
    0x00,
    0x6f,
    0xff,
    0x4d,
    0xff,
    0x94,
    0xff,
    0xaa,
    0xff,
    0x60,
    0xff,
    0x2c,
    0xff,
    0xca,
    0xff,
    0x2f,
    0xff,
    0x36,
    0xff,
    0x7a,
    0xff,
    0xd6,
    0xff,
    0x7a,
    0xff,
    0x61,
    0xff,
    0x78,
    0xff,
    0x9d,
    0xff,
    0x8e,
    0xff,
    0x4c,
    0xff,
    0x88,
    0xff,
    0x00,
    0xff,
    0xc7,
    0xff,
    0xa7,
    0xff,
    0x8d,
    0xff,
    0x8d,
    0xff,
    0x8b,
    0xff,
    0x81,
    0xff,
    0x9b,
    0xff,
    0xce,
    0xff,
    0xaa,
    0xff,
    0xbb,
    0xff,
    0x51,
    0xff,
    0xf2,
    0xff,
    0xb6,
    0xff,
    0x40,
    0xff,
    0x3b,
    0xff,
    0xa7,
    0xff,
    0xfb,
    0xff,
    0xae,
    0xff,
    0xbb,
    0xff,
    0x87,
    0xff,
    0x0b,
    0xff,
    0x78,
    0xff,
    0x43,
    0xff,
    0x78,
    0xff,
    0xcb,
    0xff,
    0xf7,
    0xfe,
    0x6c,
    0xff,
    0xaa,
    0xff,
    0xd9,
    0xff,
    0x4f,
    0xff,
    0x3c,
    0xff,
    0x4a,
    0xff,
    0x36,
    0xff,
    0xa4,
    0xff,
    0x00,
    0x00,
    0xb3,
    0xff,
    0xed,
    0xfe,
    0xd0,
    0xfe,
    0xdc,
    0xff,
    0x0d,
    0x00,
    0x39,
    0xff,
    0xa7,
    0xff,
    0x1d,
    0xff,
    0xfb,
    0xfe,
    0x83,
    0xff,
    0x16,
    0x00,
    0xc3,
    0xff,
    0xe6,
    0xfe,
    0xd8,
    0xff,
    0x67,
    0xff,
    0x15,
    0xff,
    0xbd,
    0xff,
    0xf9,
    0xff,
    0x60,
    0xff,
    0xf4,
    0xfe,
    0xf1,
    0xff,
    0xc8,
    0xff,
    0x2f,
    0xff,
    0x73,
    0xff,
    0xc6,
    0xff,
    0x83,
    0xff,
    0x8c,
    0xff,
    0xe8,
    0xff,
    0x90,
    0xff,
    0xb8,
    0xff,
    0xc2,
    0xff,
    0xcd,
    0xff,
    0xb9,
    0xff,
    0x55,
    0xff,
    0xbe,
    0xff,
    0xe5,
    0xff,
    0x26,
    0x00,
    0xfc,
    0xff,
    0x61,
    0xff,
    0x90,
    0xff,
    0xbb,
    0xff,
    0xcb,
    0xff,
    0x69,
    0xff,
    0xa1,
    0xff,
    0x99,
    0xff,
    0xc3,
    0xff,
    0x46,
    0xff,
    0x78,
    0xff,
    0x9d,
    0x00,
    0xdc,
    0xff,
    0x70,
    0xff,
    0x12,
    0xff,
    0xd6,
    0xff,
    0x5d,
    0x00,
    0xb7,
    0xff,
    0xee,
    0xfe,
    0xdb,
    0xfe,
    0x87,
    0xff,
    0x01,
    0x00,
    0x61,
    0x00,
    0x3d,
    0xff,
    0xdd,
    0xfe,
    0x15,
    0x00,
    0x96,
    0x00,
    0x01,
    0xff,
    0x2c,
    0xff,
    0x03,
    0x00,
    0x8a,
    0xff,
    0xd6,
    0xfe,
    0x50,
    0xff,
    0x26,
    0x00,
    0xe0,
    0xff,
    0xaa,
    0xff,
    0x31,
    0xff,
    0xc9,
    0xff,
    0x18,
    0x00,
    0x4d,
    0xff,
    0xb5,
    0xfe,
    0xf8,
    0xff,
    0x45,
    0x00,
    0x0e,
    0xff,
    0xc0,
    0xfe,
    0xb3,
    0xff,
    0x4f,
    0x00,
    0xb7,
    0xff,
    0x3b,
    0xff,
    0x00,
    0x00,
    0x1d,
    0x00,
    0xde,
    0xfe,
    0x97,
    0xff,
    0xde,
    0xff,
    0xe2,
    0xfe,
    0x38,
    0xff,
    0xd4,
    0xff,
    0x00,
    0x00,
    0x7b,
    0xff,
    0x41,
    0x00,
    0x77,
    0x00,
    0x23,
    0xff,
    0x0d,
    0x00,
    0x8d,
    0xff,
    0x9b,
    0xff,
    0x76,
    0xff,
    0x5a,
    0xfe,
    0xa1,
    0xfe,
    0x8d,
    0xff,
    0x67,
    0x00,
    0x10,
    0x01,
    0x84,
    0x00,
    0xc5,
    0xff,
    0x5d,
    0x00,
    0x4c,
    0x00,
    0x33,
    0x00,
    0xa9,
    0xff,
    0x04,
    0xfd,
    0x1d,
    0xfc,
    0x79,
    0xfd,
    0x06,
    0x01,
    0x4b,
    0x02,
    0xe4,
    0x01,
    0xf7,
    0x00,
    0x6a,
    0xff,
    0x44,
    0x01,
    0x80,
    0x03,
    0x96,
    0x03,
    0x9e,
    0xfd,
    0x28,
    0xf6,
    0xc0,
    0xf3,
    0x6c,
    0xfa,
    0xe0,
    0x05,
    0x8b,
    0x0b,
    0xe9,
    0x06,
    0x51,
    0xff,
    0x91,
    0xfa,
    0x92,
    0x07,
    0x4a,
    0x0d,
    0x9a,
    0xf3,
    0xbe,
    0xe9,
    0x6c,
    0xf9,
    0x77,
    0x09,
    0x25,
    0x03,
    0xc9,
    0xfa,
    0x59,
    0x00,
    0x42,
    0x05,
    0x45,
    0x04,
    0x0d,
    0x00,
    0xcf,
    0x10,
    0x53,
    0x07,
    0x92,
    0xe7,
    0x5c,
    0xe7,
    0xfb,
    0x02,
    0x7b,
    0x10,
    0x33,
    0x01,
    0x2c,
    0xf6,
    0xa0,
    0xf7,
    0x84,
    0x00,
    0x86,
    0x07,
    0x17,
    0x0b,
    0xde,
    0x0c,
    0xc8,
    0x0d,
    0x86,
    0xf0,
    0x4d,
    0xe0,
    0x4b,
    0xf3,
    0x71,
    0x0e,
    0x78,
    0x10,
    0x0a,
    0xfd,
    0x86,
    0xf3,
    0x75,
    0xf5,
    0x1e,
    0x01,
    0xb7,
    0x0c,
    0x51,
    0x10,
    0x9b,
    0x0d,
    0x54,
    0x07,
    0x61,
    0xe8,
    0xe1,
    0xe1,
    0x74,
    0xf8,
    0x1b,
    0x12,
    0x2f,
    0x0e,
    0x48,
    0xfc,
    0x6f,
    0xf2,
    0x37,
    0xf3,
    0x7e,
    0xff,
    0xb2,
    0x0f,
    0xdb,
    0x14,
    0xba,
    0x0a,
    0x40,
    0x09,
    0xf3,
    0xec,
    0x28,
    0xdf,
    0xa8,
    0xf1,
    0x61,
    0x0f,
    0x5e,
    0x14,
    0x31,
    0x00,
    0xf4,
    0xee,
    0x62,
    0xeb,
    0x42,
    0xff,
    0xc0,
    0x12,
    0x91,
    0x19,
    0xd1,
    0x05,
    0x50,
    0x04,
    0x34,
    0x03,
    0x80,
    0xe6,
    0x36,
    0xe3,
    0xb6,
    0x00,
    0xb9,
    0x17,
    0xdd,
    0x09,
    0x61,
    0xf5,
    0x14,
    0xe8,
    0x1d,
    0xf0,
    0x42,
    0x09,
    0x24,
    0x1a,
    0x67,
    0x18,
    0xac,
    0xfe,
    0xf0,
    0x01,
    0x39,
    0x02,
    0x41,
    0xe4,
    0x17,
    0xe5,
    0x52,
    0x06,
    0xb1,
    0x19,
    0x85,
    0x07,
    0xc2,
    0xf1,
    0x89,
    0xe6,
    0xc6,
    0xec,
    0x7a,
    0x07,
    0x7b,
    0x1c,
    0x37,
    0x20,
    0x73,
    0x03,
    0x0a,
    0xfc,
    0x7e,
    0x03,
    0xdb,
    0xe6,
    0x06,
    0xe1,
    0xa0,
    0x04,
    0x25,
    0x1e,
    0xcf,
    0x0b,
    0x43,
    0xf1,
    0x15,
    0xe6,
    0x65,
    0xe4,
    0x2a,
    0xfd,
    0x34,
    0x1a,
    0x22,
    0x29,
    0xc6,
    0x11,
    0x6b,
    0xf9,
    0xf3,
    0x05,
    0x7b,
    0xea,
    0x6d,
    0xd5,
    0xa9,
    0xf9,
    0x8a,
    0x23,
    0xb7,
    0x1a,
    0xec,
    0xf2,
    0x34,
    0xe6,
    0x87,
    0xde,
    0x98,
    0xed,
    0x89,
    0x0e,
    0x3b,
    0x2e,
    0xa8,
    0x27,
    0x11,
    0x01,
    0xcf,
    0x03,
    0x26,
    0xee,
    0xe3,
    0xca,
    0xdd,
    0xe9,
    0x87,
    0x23,
    0xbd,
    0x2f,
    0xd5,
    0xf7,
    0xb9,
    0xd8,
    0x2e,
    0xde,
    0xdc,
    0xef,
    0xb6,
    0x07,
    0x31,
    0x1f,
    0x66,
    0x2e,
    0xb8,
    0x10,
    0xad,
    0x0c,
    0x9d,
    0xf4,
    0x85,
    0xc2,
    0x5d,
    0xd9,
    0xe3,
    0x1b,
    0x21,
    0x3a,
    0x33,
    0x0c,
    0xcf,
    0xd8,
    0xb8,
    0xd2,
    0x84,
    0xe4,
    0xc4,
    0x03,
    0x4a,
    0x1c,
    0xa9,
    0x2e,
    0xb3,
    0x1a,
    0xdc,
    0x0d,
    0x99,
    0x07,
    0xf9,
    0xc7,
    0x06,
    0xc3,
    0x61,
    0x08,
    0xe8,
    0x3d,
    0xc4,
    0x22,
    0xd3,
    0xe4,
    0x9a,
    0xcb,
    0xae,
    0xd4,
    0x3a,
    0xf9,
    0x0c,
    0x19,
    0xf8,
    0x31,
    0x44,
    0x26,
    0xd3,
    0x10,
    0x7b,
    0x0f,
    0x21,
    0xd2,
    0x61,
    0xb3,
    0x75,
    0xf5,
    0xf1,
    0x3d,
    0x99,
    0x35,
    0x80,
    0xee,
    0x6d,
    0xca,
    0x49,
    0xca,
    0xdf,
    0xed,
    0x31,
    0x15,
    0xa5,
    0x35,
    0x25,
    0x30,
    0xda,
    0x13,
    0x28,
    0x13,
    0xbb,
    0xd7,
    0x0a,
    0xaa,
    0xb7,
    0xe6,
    0xf6,
    0x3c,
    0xac,
    0x43,
    0x5b,
    0xf9,
    0xa1,
    0xc5,
    0xcd,
    0xc6,
    0x8d,
    0xe9,
    0xac,
    0x0f,
    0xfe,
    0x2c,
    0xc2,
    0x33,
    0x23,
    0x1b,
    0x22,
    0x1b,
    0x73,
    0xe8,
    0xaa,
    0xa4,
    0xb1,
    0xce,
    0x9f,
    0x2b,
    0xfc,
    0x4f,
    0xaa,
    0x0e,
    0xf6,
    0xcb,
    0xe9,
    0xbb,
    0x15,
    0xdd,
    0x09,
    0x0d,
    0xed,
    0x31,
    0x59,
    0x3a,
    0x91,
    0x20,
    0x95,
    0x1e,
    0x68,
    0xe1,
    0xf5,
    0x9c,
    0x9b,
    0xcb,
    0x46,
    0x2f,
    0xd0,
    0x55,
    0xa5,
    0x11,
    0xb4,
    0xc9,
    0x45,
    0xb4,
    0x37,
    0xdc,
    0xe2,
    0x10,
    0xa0,
    0x36,
    0x58,
    0x3a,
    0xdb,
    0x22,
    0x0b,
    0x22,
    0x1e,
    0xd9,
    0x99,
    0x96,
    0xf3,
    0xcc,
    0x9c,
    0x37,
    0x11,
    0x59,
    0xff,
    0x0e,
    0xea,
    0xc3,
    0x8f,
    0xaf,
    0x00,
    0xde,
    0xad,
    0x17,
    0x79,
    0x3e,
    0x91,
    0x39,
    0x29,
    0x26,
    0xe1,
    0x1e,
    0xb4,
    0xc4,
    0x40,
    0x92,
    0x22,
    0xda,
    0x2b,
    0x46,
    0x9f,
    0x56,
    0xa5,
    0x00,
    0x64,
    0xba,
    0x34,
    0xb3,
    0x4d,
    0xeb,
    0x06,
    0x1f,
    0x6b,
    0x42,
    0x94,
    0x34,
    0xcf,
    0x2c,
    0x29,
    0x12,
    0xf8,
    0xab,
    0x0f,
    0x95,
    0xd3,
    0xf0,
    0x03,
    0x57,
    0xc8,
    0x4a,
    0xe1,
    0xef,
    0x67,
    0xad,
    0xf9,
    0xb9,
    0xfa,
    0xfc,
    0xcb,
    0x2d,
    0x17,
    0x41,
    0x13,
    0x2e,
    0xa3,
    0x32,
    0x33,
    0xfa,
    0x81,
    0x95,
    0xc9,
    0xa3,
    0xf4,
    0x0f,
    0x1b,
    0x62,
    0x16,
    0x34,
    0x0f,
    0xdb,
    0x45,
    0xa7,
    0x96,
    0xc9,
    0xa7,
    0x0b,
    0x61,
    0x3c,
    0x8a,
    0x41,
    0x20,
    0x31,
    0xb4,
    0x28,
    0x19,
    0xc8,
    0xbc,
    0x8a,
    0xe2,
    0xc7,
    0xf9,
    0x3c,
    0x7b,
    0x5e,
    0xdd,
    0x10,
    0x8a,
    0xbc,
    0xf4,
    0xa6,
    0x65,
    0xe3,
    0xe7,
    0x25,
    0xc6,
    0x4b,
    0x58,
    0x37,
    0x06,
    0x31,
    0x88,
    0x09,
    0xe5,
    0xa0,
    0xf2,
    0x95,
    0xb9,
    0xf8,
    0x91,
    0x5b,
    0x63,
    0x47,
    0x0b,
    0xed,
    0x84,
    0xa4,
    0x5b,
    0xb5,
    0xa7,
    0xfe,
    0x2a,
    0x3e,
    0xfe,
    0x4c,
    0xaf,
    0x33,
    0x89,
    0x2d,
    0x32,
    0xd8,
    0xb1,
    0x88,
    0xc2,
    0xb3,
    0x57,
    0x2b,
    0x36,
    0x67,
    0x1d,
    0x26,
    0x82,
    0xc6,
    0x2e,
    0x9f,
    0xf0,
    0xd3,
    0x3b,
    0x1b,
    0xc5,
    0x4f,
    0xe5,
    0x41,
    0x61,
    0x36,
    0xa0,
    0x11,
    0xc6,
    0xa4,
    0x45,
    0x8c,
    0x3d,
    0xe6,
    0x3a,
    0x55,
    0xcc,
    0x53,
    0x16,
    0xfb,
    0xea,
    0xa8,
    0xc1,
    0xaf,
    0xb4,
    0xf3,
    0xf6,
    0x38,
    0x73,
    0x50,
    0x11,
    0x3b,
    0xee,
    0x33,
    0x79,
    0xdb,
    0xf5,
    0x87,
    0xbb,
    0xab,
    0x6c,
    0x21,
    0xc9,
    0x65,
    0xc8,
    0x2e,
    0xc1,
    0xcb,
    0xfa,
    0x9c,
    0x67,
    0xcf,
    0xd9,
    0x19,
    0xfe,
    0x4e,
    0x53,
    0x44,
    0xef,
    0x3b,
    0x1e,
    0x0f,
    0x7c,
    0xa3,
    0x22,
    0x8c,
    0x18,
    0xe4,
    0x36,
    0x53,
    0x71,
    0x52,
    0xe3,
    0xfb,
    0x93,
    0xa9,
    0x2e,
    0xb1,
    0xc3,
    0xf6,
    0x89,
    0x3b,
    0x3d,
    0x4d,
    0xc0,
    0x3e,
    0x0c,
    0x30,
    0xb2,
    0xcd,
    0x1d,
    0x88,
    0xef,
    0xb4,
    0x13,
    0x2a,
    0x9c,
    0x61,
    0x17,
    0x25,
    0x4d,
    0xc8,
    0x62,
    0xa5,
    0xfa,
    0xd8,
    0xa6,
    0x1d,
    0xb2,
    0x4b,
    0xdc,
    0x41,
    0x2f,
    0x3d,
    0x13,
    0x00,
    0x2e,
    0x9b,
    0x9a,
    0x95,
    0xd5,
    0xf3,
    0x19,
    0x56,
    0xac,
    0x48,
    0x84,
    0xf0,
    0x1f,
    0xa8,
    0xe2,
    0xbc,
    0xfd,
    0x03,
    0xd5,
    0x43,
    0x0e,
    0x4a,
    0xf9,
    0x3e,
    0xd5,
    0x1c,
    0x17,
    0xb8,
    0xff,
    0x8c,
    0xff,
    0xcb,
    0x6a,
    0x3b,
    0x9a,
    0x57,
    0x46,
    0x14,
    0x0c,
    0xbe,
    0x63,
    0xad,
    0x22,
    0xe4,
    0xbb,
    0x29,
    0x6b,
    0x4d,
    0xed,
    0x41,
    0x4c,
    0x37,
    0x39,
    0xe5,
    0x78,
    0x93,
    0x33,
    0xa4,
    0x98,
    0x0b,
    0x2a,
    0x58,
    0x28,
    0x38,
    0x46,
    0xe0,
    0x02,
    0xa9,
    0x96,
    0xcb,
    0x7d,
    0x0f,
    0x6d,
    0x46,
    0x6b,
    0x44,
    0x4d,
    0x3f,
    0xf0,
    0x0c,
    0x93,
    0xa9,
    0xcb,
    0x92,
    0xfc,
    0xdf,
    0xec,
    0x44,
    0xa8,
    0x4b,
    0xb4,
    0x03,
    0x3e,
    0xb8,
    0x59,
    0xbb,
    0x48,
    0xf2,
    0xfe,
    0x2f,
    0x3b,
    0x46,
    0xdc,
    0x3f,
    0xa6,
    0x2f,
    0x39,
    0xd4,
    0x2d,
    0x92,
    0xcb,
    0xb3,
    0xc9,
    0x1c,
    0xa5,
    0x54,
    0x17,
    0x28,
    0xf5,
    0xd3,
    0x7d,
    0xb1,
    0x9a,
    0xda,
    0x8e,
    0x18,
    0xb4,
    0x42,
    0x00,
    0x3f,
    0xa5,
    0x3d,
    0xcc,
    0xfe,
    0xcb,
    0xa3,
    0xac,
    0x9a,
    0xe8,
    0xee,
    0xe5,
    0x48,
    0xad,
    0x40,
    0x3f,
    0xf7,
    0x1e,
    0xb9,
    0x5a,
    0xc8,
    0x6c,
    0xfb,
    0x3f,
    0x32,
    0xef,
    0x40,
    0xe9,
    0x40,
    0x9a,
    0x28,
    0xe4,
    0xc9,
    0xec,
    0x93,
    0x52,
    0xbd,
    0x39,
    0x23,
    0x61,
    0x4c,
    0x8a,
    0x1f,
    0xd1,
    0xd4,
    0x30,
    0xbb,
    0x2a,
    0xe0,
    0x92,
    0x18,
    0xc6,
    0x3d,
    0x1e,
    0x41,
    0x40,
    0x3f,
    0x1e,
    0xf6,
    0x54,
    0xa3,
    0xd4,
    0x9e,
    0x1d,
    0xf0,
    0x44,
    0x42,
    0x62,
    0x3e,
    0x9f,
    0xfd,
    0x37,
    0xc1,
    0x4f,
    0xc7,
    0x10,
    0xf4,
    0x56,
    0x2f,
    0x3f,
    0x44,
    0xd0,
    0x49,
    0xa1,
    0x21,
    0x98,
    0xc1,
    0xf4,
    0x95,
    0xc3,
    0xbf,
    0xeb,
    0x1f,
    0xa5,
    0x48,
    0x0e,
    0x25,
    0xd9,
    0xdc,
    0xdd,
    0xbd,
    0xec,
    0xd8,
    0x18,
    0x16,
    0xb1,
    0x42,
    0x77,
    0x4b,
    0x5f,
    0x38,
    0x7e,
    0xe3,
    0xc5,
    0xa1,
    0x3a,
    0xa9,
    0x7b,
    0xf8,
    0x34,
    0x3c,
    0x25,
    0x39,
    0x51,
    0xff,
    0x6d,
    0xc7,
    0x41,
    0xc7,
    0x0e,
    0xf6,
    0x6a,
    0x33,
    0xfc,
    0x49,
    0x1f,
    0x49,
    0x5a,
    0x0d,
    0x1e,
    0xb9,
    0xb3,
    0x9e,
    0x84,
    0xcf,
    0xe4,
    0x20,
    0x88,
    0x3f,
    0xa6,
    0x1e,
    0x7a,
    0xde,
    0xb6,
    0xc2,
    0xcb,
    0xdc,
    0x3e,
    0x1a,
    0x28,
    0x44,
    0xa2,
    0x4e,
    0x87,
    0x2c,
    0x35,
    0xd7,
    0x72,
    0xa3,
    0x54,
    0xb6,
    0xfb,
    0xff,
    0x7d,
    0x36,
    0xac,
    0x31,
    0xcd,
    0xfb,
    0x69,
    0xcb,
    0x45,
    0xcc,
    0xdd,
    0xfb,
    0xff,
    0x34,
    0x33,
    0x4b,
    0xc2,
    0x43,
    0xca,
    0x00,
    0x03,
    0xb6,
    0x8d,
    0xa6,
    0x6d,
    0xda,
    0x08,
    0x21,
    0xc5,
    0x39,
    0xbd,
    0x19,
    0x5e,
    0xde,
    0x04,
    0xc6,
    0x22,
    0xe1,
    0xb3,
    0x1e,
    0x54,
    0x45,
    0x57,
    0x4d,
    0x71,
    0x22,
    0xd1,
    0xd0,
    0x50,
    0xa7,
    0xd7,
    0xbe,
    0xb1,
    0x04,
    0x2e,
    0x33,
    0xd7,
    0x2d,
    0xcc,
    0xf9,
    0x73,
    0xcc,
    0x6b,
    0xce,
    0xa0,
    0xfd,
    0x32,
    0x35,
    0xce,
    0x4a,
    0x6b,
    0x41,
    0xbb,
    0xfe,
    0x31,
    0xb7,
    0xf7,
    0xa8,
    0x67,
    0xdb,
    0xb2,
    0x1e,
    0x77,
    0x37,
    0x67,
    0x1a,
    0xbc,
    0xe0,
    0x0c,
    0xc7,
    0xa5,
    0xe0,
    0x44,
    0x1d,
    0x8d,
    0x44,
    0xc8,
    0x4b,
    0x7a,
    0x20,
    0xa0,
    0xd2,
    0xfe,
    0xab,
    0x76,
    0xc1,
    0x68,
    0x01,
    0xb7,
    0x2e,
    0xe7,
    0x2d,
    0x86,
    0xfd,
    0x5f,
    0xcf,
    0x4f,
    0xce,
    0x94,
    0xfb,
    0xd3,
    0x32,
    0x69,
    0x49,
    0x1d,
    0x41,
    0x38,
    0x00,
    0xcd,
    0xbb,
    0xfa,
    0xac,
    0x22,
    0xd9,
    0xe6,
    0x17,
    0x98,
    0x33,
    0x43,
    0x1e,
    0xd1,
    0xe7,
    0xc0,
    0xc9,
    0x6d,
    0xdc,
    0x61,
    0x16,
    0x03,
    0x40,
    0xec,
    0x4a,
    0xc0,
    0x28,
    0xbd,
    0xdd,
    0x25,
    0xb0,
    0xd5,
    0xbc,
    0xc4,
    0xf6,
    0xc6,
    0x26,
    0x46,
    0x2e,
    0x84,
    0x08,
    0xf5,
    0xd7,
    0x38,
    0xcd,
    0xd3,
    0xef,
    0xf0,
    0x27,
    0x8a,
    0x44,
    0x97,
    0x44,
    0x58,
    0x12,
    0x1b,
    0xca,
    0x28,
    0xad,
    0xe9,
    0xca,
    0x11,
    0x09,
    0x24,
    0x2e,
    0x45,
    0x27,
    0xf2,
    0xf6,
    0x33,
    0xd0,
    0x3d,
    0xd4,
    0x10,
    0x05,
    0xc4,
    0x36,
    0x0c,
    0x4a,
    0x86,
    0x35,
    0xad,
    0xf1,
    0xd2,
    0xb9,
    0x30,
    0xb6,
    0x88,
    0xe4,
    0xc9,
    0x19,
    0x63,
    0x2e,
    0xe5,
    0x16,
    0x31,
    0xe6,
    0xbd,
    0xcd,
    0x23,
    0xe1,
    0x0e,
    0x17,
    0x86,
    0x3f,
    0x2d,
    0x48,
    0xa5,
    0x25,
    0xa0,
    0xdd,
    0xe6,
    0xb2,
    0x7d,
    0xbd,
    0xf9,
    0xf4,
    0x52,
    0x24,
    0x7c,
    0x2c,
    0xe5,
    0x08,
    0x29,
    0xdb,
    0x78,
    0xd0,
    0x9f,
    0xf0,
    0x5d,
    0x27,
    0xa7,
    0x44,
    0x41,
    0x41,
    0x77,
    0x0d,
    0xc1,
    0xcb,
    0xb5,
    0xb2,
    0x98,
    0xce,
    0x68,
    0x07,
    0x83,
    0x29,
    0xa1,
    0x23,
    0x9d,
    0xf9,
    0xce,
    0xd5,
    0x17,
    0xd7,
    0x11,
    0x00,
    0x49,
    0x31,
    0x06,
    0x47,
    0x42,
    0x38,
    0x24,
    0xfa,
    0x44,
    0xc0,
    0x7f,
    0xb5,
    0x6f,
    0xdd,
    0xe7,
    0x12,
    0x9a,
    0x2a,
    0xfe,
    0x1a,
    0x35,
    0xee,
    0x6d,
    0xd4,
    0x0c,
    0xdf,
    0x56,
    0x0d,
    0xb7,
    0x38,
    0x84,
    0x47,
    0xf7,
    0x2a,
    0x96,
    0xe7,
    0x6e,
    0xb9,
    0x81,
    0xbc,
    0xa2,
    0xec,
    0x9b,
    0x1a,
    0x97,
    0x28,
    0x39,
    0x0f,
    0x03,
    0xe7,
    0x7b,
    0xd6,
    0xa5,
    0xe8,
    0x44,
    0x18,
    0x2e,
    0x3c,
    0x7d,
    0x43,
    0xc7,
    0x1c,
    0x3c,
    0xdc,
    0x75,
    0xb7,
    0xbe,
    0xc4,
    0x61,
    0xf7,
    0x8f,
    0x1e,
    0xf1,
    0x24,
    0x03,
    0x06,
    0xa4,
    0xe2,
    0xa4,
    0xd9,
    0xca,
    0xf2,
    0x00,
    0x1f,
    0x45,
    0x3e,
    0x29,
    0x40,
    0xe6,
    0x0f,
    0x3d,
    0xd1,
    0x29,
    0xb6,
    0xeb,
    0xce,
    0xbe,
    0x01,
    0x84,
    0x22,
    0x53,
    0x20,
    0xbc,
    0xfe,
    0x3d,
    0xe1,
    0x92,
    0xdd,
    0x88,
    0xf9,
    0x56,
    0x23,
    0x71,
    0x3f,
    0x9c,
    0x3a,
    0xfd,
    0x05,
    0x0f,
    0xcb,
    0xea,
    0xb7,
    0xfd,
    0xd6,
    0x05,
    0x07,
    0xc5,
    0x21,
    0x1a,
    0x1b,
    0xe1,
    0xfa,
    0x40,
    0xe1,
    0x42,
    0xe1,
    0x1b,
    0x02,
    0x26,
    0x2a,
    0xad,
    0x40,
    0x65,
    0x31,
    0xa2,
    0xf7,
    0xe4,
    0xc3,
    0xa1,
    0xbc,
    0xaf,
    0xe1,
    0x3e,
    0x0e,
    0x47,
    0x22,
    0xf2,
    0x15,
    0x41,
    0xf5,
    0xb0,
    0xde,
    0xd1,
    0xe5,
    0xb8,
    0x0a,
    0x1f,
    0x30,
    0xb2,
    0x40,
    0x52,
    0x27,
    0x21,
    0xeb,
    0x1d,
    0xbf,
    0xd9,
    0xc1,
    0x1f,
    0xeb,
    0x0e,
    0x13,
    0xef,
    0x21,
    0x1d,
    0x11,
    0x73,
    0xf0,
    0x2b,
    0xde,
    0x4b,
    0xec,
    0xcd,
    0x13,
    0x2f,
    0x37,
    0xf7,
    0x3e,
    0x47,
    0x17,
    0xd9,
    0xdb,
    0x5b,
    0xbd,
    0x50,
    0xcb,
    0x21,
    0xf5,
    0xac,
    0x17,
    0xe2,
    0x1f,
    0x0e,
    0x0a,
    0x4d,
    0xeb,
    0xaa,
    0xdf,
    0x49,
    0xf5,
    0xdc,
    0x1d,
    0x62,
    0x3b,
    0x35,
    0x37,
    0x00,
    0x07,
    0x41,
    0xd1,
    0xc4,
    0xbe,
    0x89,
    0xd5,
    0xe9,
    0xfd,
    0x4e,
    0x1a,
    0x8c,
    0x1d,
    0xd6,
    0x03,
    0x41,
    0xe5,
    0xee,
    0xe0,
    0x79,
    0x00,
    0x12,
    0x2a,
    0x94,
    0x40,
    0x33,
    0x2c,
    0x26,
    0xf2,
    0x27,
    0xc6,
    0xe8,
    0xc2,
    0x5a,
    0xe3,
    0xc1,
    0x08,
    0x03,
    0x1e,
    0x3e,
    0x18,
    0x72,
    0xf9,
    0x3d,
    0xe1,
    0x59,
    0xe8,
    0xc4,
    0x0d,
    0xac,
    0x32,
    0x8e,
    0x3d,
    0x98,
    0x1a,
    0x43,
    0xe1,
    0x7c,
    0xc1,
    0x71,
    0xcb,
    0x18,
    0xf0,
    0x69,
    0x10,
    0xd7,
    0x1e,
    0x4d,
    0x0f,
    0x5e,
    0xf0,
    0x85,
    0xe0,
    0x00,
    0xf3,
    0x7b,
    0x1b,
    0x4e,
    0x3a,
    0xb5,
    0x36,
    0xa2,
    0x06,
    0x7b,
    0xd2,
    0x38,
    0xc0,
    0xf6,
    0xd6,
    0xda,
    0xfc,
    0xde,
    0x17,
    0x71,
    0x1b,
    0xb3,
    0x04,
    0x56,
    0xea,
    0xff,
    0xe3,
    0x45,
    0xff,
    0xc9,
    0x24,
    0x32,
    0x3c,
    0xda,
    0x2b,
    0x4f,
    0xf5,
    0x27,
    0xc9,
    0x63,
    0xc3,
    0x1a,
    0xe2,
    0x15,
    0x07,
    0xa2,
    0x1c,
    0x8e,
    0x16,
    0x4e,
    0xfb,
    0x28,
    0xe4,
    0xce,
    0xe9,
    0x75,
    0x0b,
    0x72,
    0x2f,
    0x36,
    0x3d,
    0x0e,
    0x1c,
    0x16,
    0xe4,
    0x07,
    0xc2,
    0x1c,
    0xcb,
    0x25,
    0xef,
    0x8d,
    0x0f,
    0xf7,
    0x1c,
    0x50,
    0x0e,
    0xbc,
    0xf3,
    0x6b,
    0xe4,
    0x34,
    0xf3,
    0x56,
    0x16,
    0x92,
    0x35,
    0x81,
    0x37,
    0x3a,
    0x0b,
    0x5f,
    0xd6,
    0x3d,
    0xc1,
    0x9f,
    0xd5,
    0x1d,
    0xfa,
    0xcd,
    0x15,
    0xd7,
    0x1a,
    0x96,
    0x06,
    0xd5,
    0xed,
    0xda,
    0xe5,
    0x46,
    0xfd,
    0xf3,
    0x20,
    0xda,
    0x3a,
    0xa5,
    0x2d,
    0x2d,
    0xf9,
    0x2a,
    0xcb,
    0x72,
    0xc4,
    0xf9,
    0xe0,
    0x54,
    0x03,
    0x8f,
    0x19,
    0xd5,
    0x16,
    0x34,
    0x00,
    0x0d,
    0xe9,
    0x7c,
    0xe9,
    0x00,
    0x06,
    0xfb,
    0x28,
    0xc6,
    0x3c,
    0xb0,
    0x21,
    0xe1,
    0xe9,
    0x92,
    0xc4,
    0xa0,
    0xc9,
    0x94,
    0xeb,
    0xdb,
    0x0a,
    0x76,
    0x1c,
    0x7d,
    0x12,
    0xd2,
    0xf8,
    0x24,
    0xe5,
    0xa1,
    0xed,
    0x56,
    0x10,
    0x9d,
    0x33,
    0xf6,
    0x3b,
    0x13,
    0x12,
    0x6c,
    0xda,
    0xd4,
    0xc1,
    0xce,
    0xd1,
    0xd7,
    0xf4,
    0x51,
    0x11,
    0xd5,
    0x1b,
    0xa8,
    0x0c,
    0x36,
    0xf3,
    0xa5,
    0xe4,
    0x5a,
    0xf4,
    0x15,
    0x18,
    0x7c,
    0x38,
    0x84,
    0x35,
    0x82,
    0x04,
    0x30,
    0xd2,
    0x4c,
    0xc3,
    0x6d,
    0xda,
    0x8d,
    0xfb,
    0x32,
    0x14,
    0x4c,
    0x18,
    0x38,
    0x07,
    0xd2,
    0xef,
    0x8b,
    0xe7,
    0x9b,
    0xfc,
    0x58,
    0x20,
    0x88,
    0x3b,
    0xbc,
    0x2a,
    0x58,
    0xf4,
    0x8b,
    0xc9,
    0x5d,
    0xc7,
    0xa9,
    0xe5,
    0x58,
    0x04,
    0x50,
    0x16,
    0xd0,
    0x12,
    0x3e,
    0x01,
    0x61,
    0xed,
    0xba,
    0xeb,
    0xdf,
    0x04,
    0x88,
    0x27,
    0x26,
    0x3d,
    0x92,
    0x1f,
    0xbd,
    0xe6,
    0x2c,
    0xc4,
    0xcb,
    0xcc,
    0x3a,
    0xee,
    0xde,
    0x0a,
    0x2f,
    0x19,
    0xd3,
    0x0f,
    0x71,
    0xfa,
    0xd0,
    0xe8,
    0x69,
    0xef,
    0xcf,
    0x0d,
    0xcf,
    0x30,
    0xbe,
    0x3a,
    0x97,
    0x12,
    0xe1,
    0xdb,
    0x28,
    0xc3,
    0x21,
    0xd3,
    0xac,
    0xf4,
    0x29,
    0x0f,
    0x66,
    0x18,
    0x64,
    0x0b,
    0xb9,
    0xf6,
    0xa5,
    0xe9,
    0x39,
    0xf6,
    0x39,
    0x14,
    0x7a,
    0x32,
    0x0f,
    0x34,
    0x55,
    0x06,
    0x7b,
    0xd5,
    0x45,
    0xc4,
    0x4f,
    0xda,
    0xb8,
    0xfb,
    0x19,
    0x13,
    0xca,
    0x16,
    0xe8,
    0x05,
    0x1d,
    0xf2,
    0xe9,
    0xe9,
    0xc9,
    0xfc,
    0xb3,
    0x1c,
    0x18,
    0x38,
    0xc4,
    0x2b,
    0x92,
    0xf8,
    0x2a,
    0xcc,
    0xb9,
    0xc5,
    0xf0,
    0xe2,
    0x7e,
    0x03,
    0xd4,
    0x16,
    0x6c,
    0x13,
    0x51,
    0x01,
    0xc5,
    0xed,
    0x49,
    0xec,
    0xd7,
    0x04,
    0xcc,
    0x24,
    0x37,
    0x39,
    0xdc,
    0x21,
    0x67,
    0xeb,
    0x80,
    0xc7,
    0x92,
    0xcb,
    0xad,
    0xea,
    0x58,
    0x07,
    0xf6,
    0x17,
    0x95,
    0x11,
    0x99,
    0xfd,
    0x03,
    0xec,
    0xdb,
    0xee,
    0x82,
    0x09,
    0x4d,
    0x2a,
    0x26,
    0x3a,
    0x37,
    0x18,
    0x88,
    0xe2,
    0x92,
    0xc4,
    0x95,
    0xd0,
    0x17,
    0xf1,
    0x03,
    0x0d,
    0xe9,
    0x19,
    0xca,
    0x0d,
    0xb4,
    0xf7,
    0x1e,
    0xe8,
    0x23,
    0xf3,
    0xf2,
    0x12,
    0x07,
    0x32,
    0x52,
    0x36,
    0x1d,
    0x0c,
    0x0a,
    0xd8,
    0x45,
    0xc4,
    0x94,
    0xd5,
    0x2d,
    0xf6,
    0x29,
    0x11,
    0x7d,
    0x1a,
    0x10,
    0x0b,
    0xbd,
    0xf4,
    0x27,
    0xe8,
    0xfe,
    0xf6,
    0x7c,
    0x17,
    0xec,
    0x32,
    0xf3,
    0x30,
    0xe4,
    0x03,
    0xf7,
    0xd5,
    0x3f,
    0xc7,
    0x11,
    0xda,
    0x26,
    0xf8,
    0xb2,
    0x12,
    0x82,
    0x1a,
    0x18,
    0x08,
    0x16,
    0xf0,
    0xf0,
    0xe6,
    0x46,
    0xfd,
    0xbb,
    0x1e,
    0x2d,
    0x36,
    0x7d,
    0x2b,
    0x64,
    0xf9,
    0x56,
    0xcf,
    0xb8,
    0xc7,
    0x32,
    0xdf,
    0xf7,
    0xfe,
    0xaa,
    0x16,
    0x63,
    0x18,
    0x2b,
    0x03,
    0x01,
    0xee,
    0x82,
    0xe9,
    0x9a,
    0x01,
    0x6d,
    0x21,
    0x45,
    0x35,
    0xf9,
    0x24,
    0x53,
    0xf4,
    0x88,
    0xcf,
    0x29,
    0xcb,
    0x91,
    0xe2,
    0x4a,
    0x00,
    0xc5,
    0x18,
    0x0d,
    0x18,
    0x69,
    0x01,
    0x21,
    0xea,
    0x63,
    0xe9,
    0x5f,
    0x06,
    0x14,
    0x27,
    0x9e,
    0x38,
    0x73,
    0x20,
    0x0b,
    0xed,
    0xda,
    0xca,
    0x8d,
    0xcb,
    0x20,
    0xe6,
    0x8d,
    0x04,
    0x80,
    0x1a,
    0xe9,
    0x16,
    0xc6,
    0xfe,
    0x67,
    0xe9,
    0x34,
    0xeb,
    0xb7,
    0x08,
    0x21,
    0x29,
    0xcb,
    0x36,
    0xf2,
    0x1b,
    0x69,
    0xe9,
    0x20,
    0xcb,
    0x43,
    0xce,
    0xbd,
    0xe8,
    0xb6,
    0x05,
    0x7a,
    0x1a,
    0x48,
    0x15,
    0x36,
    0xfd,
    0x09,
    0xe9,
    0xe6,
    0xec,
    0x77,
    0x0c,
    0x88,
    0x2b,
    0x7d,
    0x35,
    0xbe,
    0x15,
    0x2c,
    0xe5,
    0x7c,
    0xca,
    0x87,
    0xd1,
    0x27,
    0xec,
    0x5b,
    0x08,
    0xd4,
    0x1a,
    0xf9,
    0x11,
    0x9a,
    0xf9,
    0x25,
    0xe7,
    0x55,
    0xef,
    0x1d,
    0x10,
    0xd3,
    0x2d,
    0xb6,
    0x34,
    0xe9,
    0x12,
    0x40,
    0xe2,
    0xb6,
    0xc8,
    0x94,
    0xd0,
    0xad,
    0xed,
    0x2c,
    0x0b,
    0x9e,
    0x1c,
    0x43,
    0x11,
    0x0f,
    0xf8,
    0x82,
    0xe7,
    0xfa,
    0xf1,
    0xff,
    0x10,
    0x18,
    0x2d,
    0x05,
    0x32,
    0x8c,
    0x0f,
    0xd4,
    0xe1,
    0x3d,
    0xca,
    0x25,
    0xd3,
    0x26,
    0xee,
    0xc2,
    0x0b,
    0x1b,
    0x1c,
    0x0a,
    0x10,
    0x19,
    0xf7,
    0xaa,
    0xe6,
    0xfd,
    0xf3,
    0xf2,
    0x13,
    0x2a,
    0x2f,
    0xae,
    0x31,
    0xb3,
    0x0c,
    0x7f,
    0xde,
    0x6f,
    0xc8,
    0xb5,
    0xd3,
    0x74,
    0xf1,
    0x8a,
    0x0e,
    0x9e,
    0x1b,
    0xd2,
    0x0d,
    0x97,
    0xf5,
    0x4f,
    0xe8,
    0x92,
    0xf5,
    0x9e,
    0x14,
    0x33,
    0x2e,
    0x81,
    0x30,
    0x17,
    0x0c,
    0x7f,
    0xde,
    0xb6,
    0xc8,
    0x29,
    0xd4,
    0xc4,
    0xf1,
    0x97,
    0x0e,
    0x82,
    0x1c,
    0x7a,
    0x0d,
    0xd9,
    0xf5,
    0x5f,
    0xe9,
    0x51,
    0xf6,
    0xe0,
    0x13,
    0x6f,
    0x2d,
    0xef,
    0x2f,
    0xb8,
    0x0b,
    0x65,
    0xde,
    0xbc,
    0xc8,
    0xb9,
    0xd4,
    0xc6,
    0xf2,
    0x5c,
    0x0f,
    0x84,
    0x1b,
    0x96,
    0x0c,
    0x1a,
    0xf5,
    0xe3,
    0xe8,
    0x38,
    0xf7,
    0xe8,
    0x14,
    0xeb,
    0x2c,
    0x16,
    0x2e,
    0xd3,
    0x0b,
    0x3d,
    0xdf,
    0xad,
    0xc9,
    0xf5,
    0xd3,
    0x4a,
    0xf1,
    0x78,
    0x0f,
    0x58,
    0x1c,
    0x20,
    0x0d,
    0x30,
    0xf3,
    0x7f,
    0xe7,
    0xa0,
    0xf7,
    0xfb,
    0x16,
    0x6c,
    0x2d,
    0xc8,
    0x2c,
    0xc5,
    0x0a,
    0x13,
    0xe0,
    0x6a,
    0xcb,
    0x2c,
    0xd4,
    0x2f,
    0xf0,
    0x17,
    0x0e,
    0x9a,
    0x1c,
    0xf3,
    0x0d,
    0xba,
    0xf3,
    0xd7,
    0xe7,
    0x16,
    0xf7,
    0xb6,
    0x16,
    0x82,
    0x2c,
    0x6a,
    0x2c,
    0xda,
    0x0b,
    0x9c,
    0xe1,
    0x23,
    0xcc,
    0x8a,
    0xd3,
    0x35,
    0xef,
    0xad,
    0x0c,
    0x58,
    0x1c,
    0x64,
    0x0e,
    0x6c,
    0xf5,
    0x60,
    0xe8,
    0x98,
    0xf6,
    0x6f,
    0x15,
    0xa8,
    0x2b,
    0x98,
    0x2c,
    0x8e,
    0x0d,
    0xf3,
    0xe3,
    0x47,
    0xcc,
    0x5d,
    0xd2,
    0x8b,
    0xed,
    0x2f,
    0x0d,
    0xa9,
    0x1d,
    0xc9,
    0x0f,
    0x9c,
    0xf4,
    0x3e,
    0xe6,
    0x84,
    0xf5,
    0xa1,
    0x15,
    0x69,
    0x2d,
    0x9c,
    0x2c,
    0x39,
    0x0d,
    0x3e,
    0xe4,
    0x16,
    0xce,
    0xf5,
    0xd2,
    0x06,
    0xec,
    0x5a,
    0x09,
    0x84,
    0x1b,
    0x0d,
    0x12,
    0x6b,
    0xf8,
    0x98,
    0xe8,
    0xc3,
    0xf1,
    0x0e,
    0x11,
    0x7e,
    0x2a,
    0x4f,
    0x2f,
    0x43,
    0x12,
    0x17,
    0xe7,
    0xcc,
    0xcd,
    0x3d,
    0xd1,
    0xb7,
    0xea,
    0x02,
    0x09,
    0x14,
    0x1c,
    0x4a,
    0x12,
    0x16,
    0xf9,
    0x2b,
    0xe9,
    0x41,
    0xf1,
    0x45,
    0x0e,
    0x92,
    0x28,
    0x0b,
    0x31,
    0xf0,
    0x16,
    0xed,
    0xe9,
    0xc9,
    0xcd,
    0xaf,
    0xce,
    0xe8,
    0xe7,
    0x3e,
    0x06,
    0xd9,
    0x1a,
    0xb5,
    0x14,
    0xdd,
    0xfd,
    0xe5,
    0xec,
    0x85,
    0xf0,
    0x7a,
    0x08,
    0x90,
    0x22,
    0x3c,
    0x30,
    0x0b,
    0x1d,
    0x73,
    0xf1,
    0xd3,
    0xcf,
    0x9f,
    0xcc,
    0xe2,
    0xe4,
    0x2f,
    0x03,
    0x91,
    0x18,
    0xf1,
    0x14,
    0x78,
    0x00,
    0xca,
    0xef,
    0x0b,
    0xf1,
    0x47,
    0x06,
    0xd2,
    0x1e,
    0xbe,
    0x2f,
    0x07,
    0x20,
    0x94,
    0xf4,
    0xbe,
    0xd1,
    0xfc,
    0xca,
    0x58,
    0xe2,
    0xb8,
    0x01,
    0x90,
    0x18,
    0x1a,
    0x16,
    0x65,
    0x01,
    0xed,
    0xee,
    0x33,
    0xef,
    0xe9,
    0x06,
    0x2f,
    0x22,
    0x73,
    0x33,
    0xa8,
    0x1f,
    0x80,
    0xf0,
    0x4c,
    0xcd,
    0xed,
    0xca,
    0x86,
    0xe4,
    0x15,
    0x05,
    0x41,
    0x1a,
    0xfd,
    0x15,
    0xef,
    0xff,
    0x43,
    0xee,
    0x13,
    0xee,
    0x72,
    0x04,
    0x12,
    0x1f,
    0x24,
    0x2e,
    0x45,
    0x22,
    0x29,
    0xf9,
    0xdd,
    0xd5,
    0xd7,
    0xca,
    0xf5,
    0xdc,
    0x3d,
    0xfc,
    0x79,
    0x17,
    0xf7,
    0x19,
    0x1e,
    0x05,
    0x02,
    0xef,
    0xf3,
    0xeb,
    0x12,
    0x04,
    0xa0,
    0x22,
    0xde,
    0x33,
    0x7f,
    0x20,
    0x4b,
    0xf2,
    0x4e,
    0xcf,
    0x12,
    0xcc,
    0x9f,
    0xe3,
    0x33,
    0x01,
    0xa5,
    0x16,
    0xdb,
    0x16,
    0xc0,
    0x03,
    0xa1,
    0xef,
    0xaf,
    0xeb,
    0x3e,
    0x02,
    0x43,
    0x21,
    0x0c,
    0x33,
    0xe3,
    0x22,
    0x65,
    0xf6,
    0xe4,
    0xd1,
    0xcc,
    0xca,
    0x43,
    0xe0,
    0xbc,
    0xfe,
    0x97,
    0x16,
    0xff,
    0x18,
    0xd0,
    0x05,
    0xef,
    0xf0,
    0x96,
    0xeb,
    0x45,
    0x00,
    0xc5,
    0x1e,
    0xef,
    0x31,
    0xb3,
    0x24,
    0x95,
    0xf9,
    0xa4,
    0xd3,
    0xc5,
    0xca,
    0xf3,
    0xde,
    0x7d,
    0xfd,
    0x47,
    0x16,
    0x76,
    0x19,
    0x6b,
    0x07,
    0x0c,
    0xf1,
    0x4d,
    0xea,
    0xac,
    0xfe,
    0x61,
    0x1e,
    0xb2,
    0x32,
    0x74,
    0x26,
    0xd4,
    0xfa,
    0xbe,
    0xd3,
    0x60,
    0xc9,
    0x26,
    0xdd,
    0xf7,
    0xfb,
    0xac,
    0x15,
    0x68,
    0x1a,
    0x1a,
    0x08,
    0x06,
    0xf3,
    0xa2,
    0xea,
    0x1b,
    0xfc,
    0xb5,
    0x1a,
    0xbb,
    0x31,
    0x00,
    0x29,
    0x3d,
    0xff,
    0xb6,
    0xd5,
    0x3a,
    0xc8,
    0x9a,
    0xda,
    0xfd,
    0xf9,
    0xee,
    0x14,
    0x07,
    0x1b,
    0xda,
    0x09,
    0x2c,
    0xf4,
    0xb2,
    0xea,
    0xdb,
    0xfa,
    0x9a,
    0x18,
    0xb3,
    0x30,
    0x17,
    0x2b,
    0xf4,
    0x02,
    0x98,
    0xd8,
    0xde,
    0xc7,
    0x8f,
    0xd7,
    0x7e,
    0xf7,
    0x11,
    0x14,
    0x9c,
    0x1b,
    0xe7,
    0x0a,
    0x1a,
    0xf4,
    0x46,
    0xea,
    0x87,
    0xfa,
    0x84,
    0x1a,
    0xd6,
    0x30,
    0x97,
    0x2b,
    0xd3,
    0x02,
    0x11,
    0xd9,
    0x21,
    0xc7,
    0x98,
    0xd8,
    0xe8,
    0xf6,
    0xf1,
    0x13,
    0x57,
    0x1b,
    0xb0,
    0x0c,
    0x91,
    0xf4,
    0xb3,
    0xe9,
    0x8c,
    0xf7,
    0x7b,
    0xf7,
    0x11,
    0x0f,
    0xd9,
    0x2b,
    0x2a,
    0x31,
    0xcc,
    0x11,
    0x7f,
    0xe2,
    0xea,
    0xc9
)

class MediaPlayer(private var context: Context) {
    private val TAG = "MediaPlayer"
    private var player: AudioTrack? = null
    private var playerOk: AudioTrack? = null
    private var playerErr: AudioTrack? = null
    private var isInit = false

    private var isPlaying = false
    private var sid = -1

    private fun getSid(): Int {
        return sid
    }

    fun startPlayer() {
        if (isPlaying) {
            stopPlayer()
        }

        BluetoothSPPManager.instance.apply {
            updateBluetoothSco()
        }
        isPlaying = true
        init()
        player?.play()
        sid++
        val loadPcmData = LoadPcmData()
        loadPcmData.setSid(sid)
        loadPcmData.start()
    }

    fun stopPlayer() {
        if (!isPlaying) {
            return
        }

        BluetoothSPPManager.instance.apply {
            updateBluetoothSco()
        }
        player?.stop()
        isPlaying = false
    }

    fun playOk() {
        Log.i("========playOk", "===============playOk==================")
        if (playerOk == null) {
            initOkPlayer()
        }
        if (playerOk?.state == AudioTrack.PLAYSTATE_STOPPED) {
            playerOk?.reloadStaticData()
        }
        playerOk?.play()
        var i = 0
        do {
            i++
            Thread.sleep(20)
//            Log.i("========playOk", playerOk?.playbackHeadPosition.toString())
            if (playerOk?.playbackHeadPosition!! >= 1035) {
                break
            }
        } while (i < 20);

        playerOk?.stop()
    }

    fun playErr() {
        Log.i("========playErr", "===============playErr==================")
        if (playerErr == null) {
            initErrPlayer()
        }
        if (playerErr?.state == AudioTrack.PLAYSTATE_STOPPED) {
            playerErr?.reloadStaticData()
        }
        playerErr?.play()

        //1024
        var i = 0
        do {
            i++
            Thread.sleep(20)
            if (playerErr?.playbackHeadPosition!! >= 2710) {
                break
            }
//            Log.i("========playerErr", playerErr?.playbackHeadPosition.toString())
        } while (i < 30);

        playerErr?.stop()
    }

    private fun initOkPlayer() {
        playerOk = AudioTrack(
            AudioManager.STREAM_MUSIC,
            8000,
            AudioFormat.CHANNEL_OUT_MONO,
            AudioFormat.ENCODING_PCM_16BIT,
            OkPcm.size,
            AudioTrack.MODE_STATIC
        )
        playerOk?.write(OkPcm, 0, OkPcm.size)
    }

    private fun initErrPlayer() {
        playerErr = AudioTrack(
            AudioManager.STREAM_MUSIC,
            8000,
            AudioFormat.CHANNEL_OUT_MONO,
            AudioFormat.ENCODING_PCM_16BIT,
            ErrPcm.size,
            AudioTrack.MODE_STATIC
        )
        playerErr?.write(ErrPcm, 0, ErrPcm.size)
    }

    fun init() {
        synchronized(isInit) {
            if (!isInit) {
                val bufferSizeInBytes = AudioTrack.getMinBufferSize(
                    8000,
                    AudioFormat.CHANNEL_OUT_MONO,
                    AudioFormat.ENCODING_PCM_16BIT
                )
                player = AudioTrack(
                    AudioManager.STREAM_MUSIC,
                    8000,
                    AudioFormat.CHANNEL_OUT_MONO,
                    AudioFormat.ENCODING_PCM_16BIT,
                    bufferSizeInBytes * 2,
                    AudioTrack.MODE_STREAM
                )
                isInit = true
            }
        }
    }

    external fun getOnePcmData(): ByteArray

    private inner class LoadPcmData : Thread() {
        private var sid: Int = -1

        fun setSid(_sid: Int) {
            sid = _sid
        }

        override fun run() {
            super.run()
            var FrameCount = 0
            var firstFillFrameCount = 2
            while (isPlaying) {
                if (getSid() != sid) {
                    break
                }

                while (firstFillFrameCount > 0) {
                    firstFillFrameCount--
                    val ret = getOnePcmData()

                    try {
                        if (ret.size > 0) {
                            FrameCount++

                            val ws = player?.write(ret, 0, ret.size)
//                            Log.i(
//                                TAG,
//                                "player?.write " + ws + " FrameCount:" + FrameCount + " getOnePcmData.size:" + ret.size
//                            )
                        } else {
                            Log.e(TAG, "ret.size<=0 " + ret.size)
                        }

                    } catch (e: Exception) {
                        Log.e(TAG, "write err:" + e.toString())
                    }
                }

                //call go
                val ret = getOnePcmData()

                try {
                    if (ret.size > 0) {
                        FrameCount++

                        val ws = player?.write(ret, 0, ret.size)
//                        Log.i(
//                            TAG,
//                            "player?.write " + ws + " FrameCount:" + FrameCount + " getOnePcmData.size:" + ret.size
//                        )
                    } else {
                        Log.e(TAG, "ret.size<=0 " + ret.size)
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "write err:" + e.toString())
                }

                sleep(60)
            }
        }
    }
}