package com.bfdx.bf8100deviceapp

import android.Manifest
import android.app.Service
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import androidx.core.app.ActivityCompat

class MediaRecorder(private var service: ProxyService) {
    //1.设置录音相关参数,音频采集源、采样率、声道、数据格式
    //2.计算最小录音缓存区大小
    //3.创建audioRecord对象
    //4.开始录音
    //5.创建文件用于保存PCM文件
    //6.录音完毕，关闭录音及释放相关资源
    //7.将pcm文件转换为WAV文件

    companion object {
        init {
            System.loadLibrary("native-lib")
        }

        private const val TAG: String = "MediaRecorder"
        private const val AudioSource = MediaRecorder.AudioSource.DEFAULT//声源
        private const val Channel = AudioFormat.CHANNEL_IN_MONO//单声道
        private const val EncodingType = AudioFormat.ENCODING_PCM_16BIT//数据格式
    }

    private val _8kRecorder: _8KRecorder = _8KRecorder()
    private val _48kRecorder: _48KRecorder = _48KRecorder()

    // false == _48kRecorder   true == _8kRecorder
    private var isBluetoothRecorder = false

    private fun checkRecordAudioPermission(): Boolean {
        val result = ActivityCompat.checkSelfPermission(service, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED
        if (!result) {
            Log.e(TAG, "Manifest.permission.RECORD_AUDIO denied")
        }
        return result
    }

    inner class _8KRecorder {
        private var on8kBufferSizeInByte: Int = 0//最小录音缓存区
        private var audioRecorder: AudioRecord? = null//录音对象
        private var isRecord = false
        private val SampleRate = 8000//采样率

        fun init8kRecorder() {//初始化audioRecord对象
            on8kBufferSizeInByte = AudioRecord.getMinBufferSize(SampleRate, Channel, EncodingType)
            if (!checkRecordAudioPermission()) {
                return
            }
            audioRecorder = AudioRecord(
                AudioSource, SampleRate, Channel, EncodingType, on8kBufferSizeInByte
            )
        }

        fun startRecord() {
            if (isRecord) {
                stopRecord()
                Thread.sleep(50)
            }
            if (audioRecorder == null) {
                audioRecorder ?: init8kRecorder()
            }
            if (audioRecorder?.state == AudioRecord.STATE_UNINITIALIZED) {
                Log.e(TAG, "8k audioRecorder is uninitialized, reinit it")
                audioRecorder?.release()
                audioRecorder = null
                audioRecorder ?: init8kRecorder()
            }
            audioRecorder?.startRecording()

            isRecord = true

            if (audioRecorder == null) {
                Log.e(TAG, "8k audioRecorder is null")
                isRecord = false
                return
            }

            if (audioRecorder!!.recordingState != AudioRecord.RECORDSTATE_RECORDING) {
                Log.e(TAG, "8k audioRecorder is not recording")
                isRecord = false
                return
            }

            Collect8kPcmData().start()


        }

        fun stopRecord() {
            audioRecorder?.stop()
            isRecord = false
        }

        fun releaseRecord() {
            audioRecorder?.release()
            isRecord = false
            audioRecorder = null
        }

        fun got8kPcmData() {
            while (isRecord && audioRecorder != null) {
                var audioData = ByteArray(on8kBufferSizeInByte)
                //block func
                var length = audioRecorder!!.read(audioData, 0, on8kBufferSizeInByte)//获取音频数据
                if (AudioRecord.ERROR_INVALID_OPERATION != length) {
                    onGot8kPcmData(audioData, length)
                }
            }
            stopRecord()
        }

        private inner class Collect8kPcmData : Thread() {
            override fun run() {
                super.run()

                got8kPcmData()
            }
        }
    }

    inner class _48KRecorder {
        private var on48kBufferSizeInByte: Int = 0//最小录音缓存区
        private var audioRecorder: AudioRecord? = null//录音对象
        private var isRecord = false
        private val SampleRate = 48000//采样率

        fun init48kRecorder() {//初始化audioRecord对象
            on48kBufferSizeInByte = AudioRecord.getMinBufferSize(SampleRate, Channel, EncodingType)
            if (!checkRecordAudioPermission()) {
                return
            }
            audioRecorder = AudioRecord(
                AudioSource, SampleRate, Channel, EncodingType, on48kBufferSizeInByte
            )
        }

        fun startRecord() {
            if (isRecord) {
                stopRecord()
                Thread.sleep(50)
            }
            if (audioRecorder == null) {
                audioRecorder ?: init48kRecorder()
            }
            if (audioRecorder?.state == AudioRecord.STATE_UNINITIALIZED) {
                Log.e(TAG, "48k audioRecorder is uninitialized, reinit it")
                audioRecorder?.release()
                audioRecorder = null
                audioRecorder ?: init48kRecorder()
            }
            audioRecorder?.startRecording()
            isRecord = true

            if (audioRecorder == null) {
                Log.e(TAG, "48k audioRecorder is null")
                isRecord = false
                return
            }

            if (audioRecorder!!.recordingState != AudioRecord.RECORDSTATE_RECORDING) {
                Log.e(TAG, "48k audioRecorder is not recording")
                isRecord = false
                return
            }

            Collect48kPcmData().start()


        }


        fun stopRecord() {
            audioRecorder?.stop()
            isRecord = false
        }

        fun releaseRecord() {
            audioRecorder?.release()
            isRecord = false
            audioRecorder = null
        }

        private fun got48kPcmData() {
            while (isRecord && audioRecorder != null) {
                var audioData = ByteArray(on48kBufferSizeInByte)
                //block func
                var length = audioRecorder!!.read(audioData, 0, on48kBufferSizeInByte)//获取音频数据
                if (AudioRecord.ERROR_INVALID_OPERATION != length) {
                    onGot48kPcmData(audioData, length)
                }
            }
            stopRecord()
        }

        private inner class Collect48kPcmData : Thread() {
            override fun run() {
                super.run()

                got48kPcmData()
            }
        }
    }

    fun start8kRecord() {
        Log.i(TAG, " ============ start8kRecord")
        synchronized(isBluetoothRecorder) {
            if (!isBluetoothRecorder) {
                _48kRecorder.stopRecord()
            }
            isBluetoothRecorder = true
            _8kRecorder.startRecord()
        }
    }

    fun start48kRecord() {
        Log.i(TAG, " ============ start48kRecord")
        synchronized(isBluetoothRecorder) {
            if (isBluetoothRecorder) {
                _8kRecorder.stopRecord()
            }
            isBluetoothRecorder = false
            _48kRecorder.startRecord()
        }
    }

    fun stopRecord() {
        Log.i(TAG, " ============ stopRecord")
        synchronized(isBluetoothRecorder) {
            if (isBluetoothRecorder) {
                _8kRecorder.stopRecord()
            } else {
                _48kRecorder.stopRecord()
            }
        }
    }

    fun releaseRecord() {
        _8kRecorder.releaseRecord()
        _48kRecorder.releaseRecord()
    }

    private external fun onGot8kPcmData(data: ByteArray, len: Int)
    private external fun onGot48kPcmData(data: ByteArray, len: Int)
}