package com.bfdx.bf8100deviceapp

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.os.PowerManager
import android.util.Log
import android.view.KeyEvent
import android.view.accessibility.AccessibilityEvent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.Thread.sleep
import java.util.concurrent.atomic.AtomicLong

class PocAccessibilityService : AccessibilityService() {
    private val tag = "${TagPrefix}PocAccessibility"
    private lateinit var pm: PowerManager

    companion object {
        private lateinit var appContext: Context
        private var instance: PocAccessibilityService? = null

        fun getInstance(): PocAccessibilityService {
            if (instance == null) {
                instance = PocAccessibilityService()
            }
            return instance as PocAccessibilityService
        }
    }

    init {
        Log.i(tag, "PocAccessibilityService init")
    }

    // com.corget.BaseAccessibilityService, android.accessibilityservice.AccessibilityService
    override fun onInterrupt() {
        Log.i(tag, "onInterrupt")
    }

    // com.corget.BaseAccessibilityService, android.accessibilityservice.AccessibilityService
    override fun onAccessibilityEvent(event: AccessibilityEvent) {
//        Log.i(tag, "onAccessibilityEvent, $event")
//        Log.i(tag, "onAccessibilityEvent, ${event.packageName}")
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        appContext = applicationContext
        instance = this
        pm = applicationContext.getSystemService(Context.POWER_SERVICE) as PowerManager
    }

    private inner class KeyPressDetector(timeout: Long = 600) {
        private val job = Job()
        private val scope = CoroutineScope(Dispatchers.Main + job)
        private val lastKeyPressTime = AtomicLong()
        private val pressInterval = timeout

        fun onKeyPress(onKeyEvent: () -> Unit) {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastKeyPressTime.get() <= pressInterval) {
                // 在600毫秒内按下两次
                onKeyEvent()
            }
            lastKeyPressTime.set(currentTime)

            // 每600毫秒重置一次
            scope.launch {
                delay(pressInterval)
                lastKeyPressTime.set(0)
            }
        }

        fun stopListening() {
            job.cancel()
        }
    }

    private var sosKeyPressDetector: KeyPressDetector? = null
    private fun monitorSosKeyEvent(event: KeyEvent): Boolean {
        // 暂时处理按下事件
        val action = event.action;
        if (action == KeyEvent.ACTION_UP) {
            return false
        }

        val ctx = this
        if (sosKeyPressDetector == null) {
            sosKeyPressDetector = KeyPressDetector()
        }
        sosKeyPressDetector?.onKeyPress {
            // 唤醒屏幕
            val wakeLockIntent = Intent()
            wakeLockIntent.action = AccessibilityKeyEventReceiver
            wakeLockIntent.putExtra("action", "wakeUp")
            wakeLockIntent.putExtra("timeout", wakeUpTimeout)
            LocalBroadcastManager.getInstance(ctx).sendBroadcast(wakeLockIntent)
            sleep(60)

            // 发送两次按下事件
            val keyDownIntent = Intent()
            keyDownIntent.action = "unipro.hotkey.sos.down"
            ctx.sendBroadcast(keyDownIntent)
            sleep(20)
            val keyUpIntent = Intent()
            keyUpIntent.action = "unipro.hotkey.sos.up"
            ctx.sendBroadcast(keyUpIntent)

            sleep(100)
            ctx.sendBroadcast(keyDownIntent)
            sleep(20)
            ctx.sendBroadcast(keyUpIntent)

            // 停止监听
            sosKeyPressDetector?.stopListening()
            sosKeyPressDetector = null
        }

        return true
    }

    // android.accessibilityservice.AccessibilityService
    override fun onKeyEvent(event: KeyEvent): Boolean {
        // 亮屏时不处理
        if (pm.isInteractive) {
            return false
        }

        val keyCode = event.keyCode;
        val action = event.action;
        Log.i(tag, "onKeyEvent, keyCode=$keyCode, action=$action")

        // 监听报警键keydown事件
        if (keyCode == 260) {
            val handled = monitorSosKeyEvent(event);
            if (handled) {
                return true
            }
        }

        // 熄屏时，处理蓝牙耳机按键事件
        if (keyCode == KeyEvent.KEYCODE_HEADSETHOOK
            || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY
            || keyCode == KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE
            || keyCode == KeyEvent.KEYCODE_MEDIA_STOP
            || keyCode == KeyEvent.KEYCODE_MEDIA_NEXT
            || keyCode == KeyEvent.KEYCODE_MEDIA_PREVIOUS
            || keyCode == KeyEvent.KEYCODE_MEDIA_REWIND
            || keyCode == KeyEvent.KEYCODE_MEDIA_FAST_FORWARD
            || keyCode == KeyEvent.KEYCODE_MEDIA_PAUSE
            || keyCode == KeyEvent.KEYCODE_MEDIA_RECORD
        ) {
            ProxyService.handleMediaButtonEvent(event)
            return true
        }

        return super.onKeyEvent(event);
    }
}
