package com.bfdx.bf8100deviceapp

import android.Manifest
import android.annotation.SuppressLint
import android.app.Service
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothProfile
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.location.Criteria
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.media.AudioManager
import android.os.Bundle

import android.os.IBinder
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.KeyEvent
import androidx.annotation.Keep
import androidx.core.app.ActivityCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.location.*
import com.google.android.gms.location.Priority.PRIORITY_HIGH_ACCURACY
import com.google.android.gms.tasks.CancellationToken
import com.google.android.gms.tasks.CancellationTokenSource
import com.google.android.gms.tasks.OnTokenCanceledListener
import org.json.JSONObject
import java.lang.Thread.sleep
import kotlin.concurrent.thread
import kotlin.system.exitProcess

@Keep
class ProxyService : Service() {
    private val tag = "${TagPrefix}ProxyService"

    private lateinit var fusedLocationClient: FusedLocationProviderClient
    private lateinit var locationManager: LocationManager
    private var locationManagerProvider: String = LocationManager.GPS_PROVIDER
    private var isGooglePlayServicesAvailable: Boolean = false
    private var locationInvalidTimeInterval: Long = 5 * 60 * 1000 // 5分钟，用于判断当前定位是否需要重新定位
    private var locationInterval: Long = 3000
    private var minDistanceM: Float = 0F
    private var mediaButtonEventReceiverComponentName: ComponentName? = null
    private lateinit var audioManager: AudioManager
    private lateinit var mediaPlayer: MediaPlayer
    private lateinit var mediaRecorder: MediaRecorder
    private lateinit var bluetoothAdapter: BluetoothAdapter
    private lateinit var dynamicBroadcastReceiver: DynamicBroadcastReceiver
    private lateinit var bleSppManager: BluetoothSPPManager
    var isBluetoothConnected = false
    var isBluetoothOpened = true

    companion object {
//        init {
//            System.loadLibrary("native-lib")
//        }

        lateinit var self: ProxyService

        fun sendLocalBroadcastIntent(intent: Intent) {
            LocalBroadcastManager.getInstance(self).sendBroadcast(intent)
        }

        private fun stopTtsSpeak() {
            val stopTts = TtsIntent("")
            stopTts.action = TtsIntent.ACTION_STOP
            LocalBroadcastManager.getInstance(self).sendBroadcast(stopTts)
        }

        private fun runStartRecorder() {
            val denoiseSettingVal = denoiseSetting()
            Log.i(self.tag, "runStartRecorder denoiseSettingVal: $denoiseSettingVal")
            if (denoiseSettingVal == 0 || denoiseSettingVal == 1) {
                self.mediaRecorder.start8kRecord()
                return
            }
            self.mediaRecorder.start48kRecord()
        }

        var _isRecording = false
        fun getIsRecording(): Boolean {
            return _isRecording
        }

        var _isPlaying = false
        fun getIsPlaying(): Boolean {
            return _isPlaying
        }

        @SuppressLint("MissingPermission")
        @JvmStatic
        fun StartRecorder() {
            _isRecording = true
            _isPlaying = false
            sendLocalBroadcastIntent(Intent(TtsImpl.MEDIA_STATUS_ACTION).putExtra("status", 1))
            self.sendBroadcast(getLightIntent(LedPttMode.red))
            stopTtsSpeak()
            Log.i(self.tag, "StartRecorder self.bluetoothAdapter.state: ${self.bluetoothAdapter.state}")

            //blueTooth off
            if (self.bluetoothAdapter.state == BluetoothAdapter.STATE_OFF) {
                runStartRecorder()
                return
            }

            //检查蓝牙权限
            //android 5.1.1不需要运行时申请BLUETOOTH_CONNECT，永远为false
//            if (ActivityCompat.checkSelfPermission(self, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
//                // here to request the missing permissions, and then overriding
//                //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
//                //                                          int[] grantResults)
//                // to handle the case where the user grants the permission. See the documentation
//                // for ActivityCompat#requestPermissions for more details.
//                Log.e(self.tag, "no bluetooth connect permission")
//                runStartRecorder()
//                return
//            }

            //检查蓝牙耳机是否连接
            if (self.bluetoothAdapter.getProfileConnectionState(BluetoothProfile.HEADSET) != BluetoothAdapter.STATE_CONNECTED) {
                Log.e(self.tag, "bluetooth not connect")
                runStartRecorder()
                return
            }

            Log.i(self.tag, "StartRecorder use bluetooth headset recording")
            // 开始使用蓝牙耳机录音
            var checkTimes = 0
            //try less than 5 times. total time = 5*20 = 100ms
            while (!self.audioManager.isBluetoothScoOn && checkTimes < 5) {
                self.audioManager.startBluetoothSco()
                sleep(20)
                checkTimes++
            }
            if (self.audioManager.isBluetoothScoOn) {
                self.mediaRecorder.start8kRecord()
                return
            }

            //无法使用蓝牙耳机录音，使用默认录音
            runStartRecorder()
        }

        @JvmStatic
        fun StopRecorder() {
            _isRecording = false
            sendLocalBroadcastIntent(Intent(TtsImpl.MEDIA_STATUS_ACTION).putExtra("status", 0))
            self.sendBroadcast(getLightIntent(LedPttMode.turnOff))
            self.mediaRecorder.stopRecord()
        }

        @JvmStatic
        fun StartPlayer() {
            _isPlaying = true
            _isRecording = false
            sendLocalBroadcastIntent(Intent(TtsImpl.MEDIA_STATUS_ACTION).putExtra("status", 2))
            self.sendBroadcast(getLightIntent(LedPttMode.green))
            stopTtsSpeak()
            self.mediaPlayer.startPlayer()
        }

        @JvmStatic
        fun StopPlayer() {
            _isPlaying = false
            sendLocalBroadcastIntent(Intent(TtsImpl.MEDIA_STATUS_ACTION).putExtra("status", 0))
            self.sendBroadcast(getLightIntent(LedPttMode.turnOff))
            self.mediaPlayer.stopPlayer()
        }

        @JvmStatic
        fun PlayOk() {
            self.mediaPlayer.playOk()
        }

        @JvmStatic
        fun PlayErr() {
            self.sendBroadcast(getLightIntent(LedPttMode.turnOff))
            self.mediaPlayer.playErr()
        }

        @JvmStatic
        external fun denoiseSetting(): Int

        @JvmStatic
        private fun setAudioSpeaker() {
//            self.audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
//            //设置音量，解决有些机型切换后没声音或者声音突然变大的问题
//            self.audioManager.getStreamVolume(AudioManager.STREAM_VOICE_CALL)?.let {
//                self.audioManager.setStreamVolume(
//                    AudioManager.STREAM_VOICE_CALL,
//                    it,
//                    AudioManager.FX_KEY_CLICK
//                )
//            }

            //扬声器开启，此方法调用之前需要先设置好mode

//            Log.i(self.tag, "setAudioSpeaker isBluetoothA2dpOn:" + self.audioManager.mode)
//            Log.i(self.tag, "setAudioSpeaker isBluetoothA2dpOn:" + self.audioManager.isBluetoothA2dpOn)
//            Log.i(self.tag, "setAudioSpeaker isBluetoothScoOn:" + self.audioManager.isBluetoothScoOn)
//            Log.i(self.tag, "setAudioSpeaker isWiredHeadsetOn:" + self.audioManager.isWiredHeadsetOn)
//            Log.i(self.tag, "setAudioSpeaker isBluetoothScoAvailableOffCall:" + self.audioManager.isBluetoothScoAvailableOffCall)
//            Log.i(self.tag, "setAudioSpeaker isHEADSETProfileConnected:" + BluetoothUtil.isHEADSETProfileConnected())
//            BluetoothUtil.isBluetoothA2dpOn(self)
//            self.audioManager.mode = AudioManager.MODE_NORMAL
//            self.audioManager.isSpeakerphoneOn = true
        }

        @JvmStatic
        private fun updateNotificationStateMediaPlayerStart() {
            setAudioSpeaker()
//            updateNotification()
        }

        @JvmStatic
        fun StartLocation() {
            self.stopLocation()
            sleep(100)
            self.startLocationLoop()
        }

        @JvmStatic
        fun LocationOnce(isForce: Int) {
            self.getLastLocation(isForce)
            //locationHandle?.getLastLocation()
        }

        @JvmStatic
        fun StopLocation() {
            self.stopLocation()
        }

        @JvmStatic
        private fun holdWakeLock() {
            val wakeLockIntent = Intent()
            wakeLockIntent.action = PowerManagerWakeLockEventReceiver
            wakeLockIntent.putExtra("action", "wakeUp")
            wakeLockIntent.putExtra("timeout", wakeUpTimeout)
            LocalBroadcastManager.getInstance(self).sendBroadcast(wakeLockIntent)
        }

        @JvmStatic
        private fun releaseWakeLock() {
            val wakeLockIntent = Intent()
            wakeLockIntent.action = PowerManagerWakeLockEventReceiver
            wakeLockIntent.putExtra("action", "wakeLock")
            LocalBroadcastManager.getInstance(self).sendBroadcast(wakeLockIntent)
        }

        @JvmStatic
        private fun getAppBuildInfo(): String {
            val buildInfo = HashMap<String, Any>()
            buildInfo["version"] = BuildConfig.APP_VERSION
            buildInfo["buildTime"] = BuildConfig.BUILD_TIME
            val jsonObject = JSONObject(buildInfo.toMap())
            var str = jsonObject.toString()
            return str
        }

        /**
         * gpsMode  0: 关闭 1：仅限设备(GPS) 2：耗电量低(WLAN/MOBILE) 3：准确度高（GPS/WLAN/MOBILE)
         * gpsType: 0：北斗卫星和GPS 1：仅GPS 2：仅北斗卫星
         */
        private fun uniproGpsSet(gpsMode: Int, gpsType: Int) {
            Log.d(self.tag, "uniproGpsSet: gpsMode=$gpsMode, gpsType=$gpsType")
            val gpsIntent = Intent()
            gpsIntent.action = "unipro.gps.set"
            gpsIntent.putExtra("gps_mode", gpsMode)
            gpsIntent.putExtra("gps_type", gpsType)
            LocalBroadcastManager.getInstance(self).sendBroadcast(gpsIntent)
        }

        /**
         * value: "+gps" or "-gps"
         */
        private fun setLocationSecureValue(value: String) {
            // 检查是否有WRITE_SECURE_SETTINGS权限
            val requiredPermission = "android.permission.WRITE_SECURE_SETTINGS";
            val pm: PackageManager = self.getPackageManager()
            if (pm.checkPermission(requiredPermission, self.packageName) == PackageManager.PERMISSION_DENIED) {
                Log.d(self.tag, "check WRITE_SECURE_SETTINGS Permission failed")
                return
            }

            val isServiceEnabled = Settings.Secure.putString(
                self.getContentResolver(), Settings.Secure.LOCATION_PROVIDERS_ALLOWED, value
            )
            Log.d(
                self.tag,
                "Settings.Secure.putString: ${Settings.Secure.LOCATION_PROVIDERS_ALLOWED} $value, isServiceEnabled=$isServiceEnabled"
            )
        }

        @JvmStatic
        private fun enableGps() {
            setLocationSecureValue("+gps")
            uniproGpsSet(3, 0)
        }

        @JvmStatic
        private fun disableGps() {
            uniproGpsSet(0, 0)
            setLocationSecureValue("-gps")
        }

        fun headsetPressedPtt(action: String) {
            Log.i(self.tag, "headsetPressedPtt: $action")
//            val headsetIntent = Intent()
//            headsetIntent.setAction(action)
//            self.sendBroadcast(headsetIntent)

            val mainActivity = MainActivity.getInstance()
            // 需要发送到Flutter端的事件参数
            val data = java.util.HashMap<String, Any>()
            data["action"] = action
            mainActivity.getMethodCall().invokeMethod("onHotKeyEvent", data)
        }

        private var mediaButtonEventTime: Long = 0
        fun handleMediaButtonEvent(event: KeyEvent) {
            // 检查两次按键事件的时间间隔是否小于360ms
            if (mediaButtonEventTime > 0 && event.eventTime - mediaButtonEventTime < 360) {
                return
            }
            mediaButtonEventTime = event.eventTime

            val keyCode = event.keyCode
            val action = event.action
            Log.i(self.tag, "handleMediaButtonEvent: keyCode=$keyCode, action=$action")

            when (keyCode) {
                KeyEvent.KEYCODE_HEADSETHOOK, KeyEvent.KEYCODE_MEDIA_PLAY, KeyEvent.KEYCODE_MEDIA_RECORD -> {
                    when (action) {
                        KeyEvent.ACTION_DOWN -> {
                            // 如果当前处于录音，则结束呼叫
                            if (_isRecording) {
                                headsetPressedPtt("unipro.hotkey.headset.ptt.up")
                            } else {
                                headsetPressedPtt("unipro.hotkey.headset.ptt.down")
                            }
                        }

                        KeyEvent.ACTION_UP -> {
                            // 处理释放事件
                            headsetPressedPtt("unipro.hotkey.headset.ptt.up")
                        }
                    }
                }

                KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE, KeyEvent.KEYCODE_MEDIA_PAUSE, KeyEvent.KEYCODE_MEDIA_STOP -> {
                    // 处理播放/暂停按键
                    headsetPressedPtt("unipro.hotkey.headset.ptt.up")
                }

                KeyEvent.KEYCODE_MEDIA_NEXT -> {
                    // 处理下一曲按键
                    Log.i(self.tag, "Headset button KEYCODE_MEDIA_NEXT")
                }

                KeyEvent.KEYCODE_MEDIA_PREVIOUS -> {
                    // 处理上一曲按键
                    Log.i(self.tag, "Headset button KEYCODE_MEDIA_PREVIOUS")
                }
            }
        }
    }

    // 必须实现该方法
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()
        Log.i(tag, "onCreate")

        self = this
        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        mediaPlayer = MediaPlayer(this)
        mediaRecorder = MediaRecorder(this)

        // 启动Goproxy
        runGoproxyWithThread()

        initLocator()
        regCallBackFuncs()
        registerMediaButtonEventReceiver()
        // registerDynamicBroadcastReceiver()
        bleSppManager = BluetoothSPPManager(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(tag, "onDestroy")
        stopProxy()

        stopLocation()

        mediaRecorder.releaseRecord()
        mediaPlayer.stopPlayer()
        unregisterMediaButtonEventReceiver()
        // unregisterReceiver(this.dynamicBroadcastReceiver)

        exitProcess(0)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        runInitSqliteDataBaseWithThread()
        // Use absolutePath
        return START_NOT_STICKY
    }

    private fun registerMediaButtonEventReceiver() {
        unregisterMediaButtonEventReceiver()
        if (mediaButtonEventReceiverComponentName == null) {
            mediaButtonEventReceiverComponentName = ComponentName(packageName, MediaButtonReceiver::class.java.name)
            audioManager.registerMediaButtonEventReceiver(mediaButtonEventReceiverComponentName)
            Log.i(tag, "registerMediaButtonEventReceiver, $mediaButtonEventReceiverComponentName")
        }
    }

    private fun unregisterMediaButtonEventReceiver() {
        if (mediaButtonEventReceiverComponentName != null) {
            audioManager.unregisterMediaButtonEventReceiver(mediaButtonEventReceiverComponentName)
            mediaButtonEventReceiverComponentName = null
            Log.i(tag, "unregisterMediaButtonEventReceiver")
        }
    }

    private fun registerDynamicBroadcastReceiver() {
        this.dynamicBroadcastReceiver = DynamicBroadcastReceiver(this);
        val filter = IntentFilter()
        filter.addAction("android.bluetooth.headset.action.VENDOR_SPECIFIC_HEADSET_EVENT")
        filter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED)
        filter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
        filter.priority = Integer.MAX_VALUE
        registerReceiver(this.dynamicBroadcastReceiver, filter)
    }

    // 向go动态库设置app的文件夹路径
    private external fun setAppFilesDirPath(filesDirPath: String)
    private external fun initSqliteDataBase()
    private fun runInitSqliteDataBaseWithThread() {
        lateinit var t: Thread
        t = thread {
            val absolutePath = applicationContext.filesDir.absolutePath
            Log.i(tag, "applicationContext.filesDir.absolutePath: $absolutePath")
            setAppFilesDirPath(absolutePath)
            initSqliteDataBase()
            sleep(200)
            t.interrupt()
        }
    }

    private external fun stopProxy()
    private external fun startProxy()
    private fun runGoproxyWithThread() {
        lateinit var t: Thread
        t = thread {
            Log.i(tag, "runGoproxy")
            startProxy()
            sleep(200)
            t.interrupt()
        }
    }

    external fun regCallBackFuncs()

    external fun gotValidLocation(time: Long, lon: Double, lat: Double, direction: Float, speed: Float, altitude: Double)
    private fun initLocator() {
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this)

        try {
            val a = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this)
            isGooglePlayServicesAvailable = a == ConnectionResult.SUCCESS
        } catch (e: Exception) {
            isGooglePlayServicesAvailable = false
        }

        Log.i(tag, "isGooglePlayServicesAvailable: $isGooglePlayServicesAvailable")
        if (isGooglePlayServicesAvailable) {
            return
        }

        locationManager = getSystemService(LOCATION_SERVICE) as LocationManager
        // Create persistent LocationManager reference
        val criteria = Criteria()
        criteria.accuracy = Criteria.ACCURACY_COARSE
        criteria.isAltitudeRequired = true
        criteria.isBearingRequired = true
        criteria.isCostAllowed = true
        criteria.powerRequirement = Criteria.POWER_HIGH //低功耗
        val provider = locationManager.getBestProvider(criteria, true)
        // 可能为null
        if (provider != null) {
            locationManagerProvider = provider
        }
        Log.i(tag, "LocationManager PROVIDER: $locationManagerProvider")
    }

    private val locationCallback = object : LocationCallback() {
        override fun onLocationResult(locationResult: LocationResult) {
            super.onLocationResult(locationResult)
            Log.d(tag, "onLocationResult")
            if (locationResult.locations.isEmpty()) {
                Log.e(tag, "Got Empty Location data")
                return
            }
            val location = locationResult.locations[0]
            onGetLocation(location)
        }
    }

    private val locationListener = object : LocationListener {
        override fun onLocationChanged(location: Location) {
            Log.d(tag, "onLocationChanged")
            onGetLocation(location)
        }

        @Deprecated("Deprecated in Java")
        override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {
            Log.d(tag, "onStatusChanged")
        }

        override fun onProviderEnabled(provider: String) {
            Log.d(tag, "onProviderEnabled")
        }

        override fun onProviderDisabled(provider: String) {
            Log.d(tag, "onProviderDisabled")
        }
    }

    private fun stopLocation() {
        Log.d(tag, " stopLocation Called")
        fusedLocationClient.removeLocationUpdates(locationCallback)
        if (isGooglePlayServicesAvailable) {
            return
        }
        locationManager.removeUpdates(locationListener)
    }

    private fun startLocationLoop() {
        Log.d(tag, " startLocationLoop Called")
        if (!checkPermission()) {
            return
        }

        if (isGooglePlayServicesAvailable) {
            val locationRequest = LocationRequest.create()
            locationRequest.interval = locationInterval
            locationRequest.priority = PRIORITY_HIGH_ACCURACY
            locationRequest.fastestInterval = locationInterval / 2

            fusedLocationClient.requestLocationUpdates(
                locationRequest, locationCallback, Looper.getMainLooper()
            )
            return
        }

        Log.e(
            tag, "Google Play Service Not Available, can not get last location use fusedLocationClient"
        )
        locationManager.requestLocationUpdates(
            locationManagerProvider, locationInterval, minDistanceM, locationListener, Looper.getMainLooper()
        )
    }

    private fun onGetLocation(location: Location) {
        Log.d(
            tag, """
                onGetLocation gps Time: ${location.time}
                lon: ${location.longitude}
                lat: ${location.latitude}
                speed: ${location.speed}
                bearing: ${location.bearing}
                altitude: ${location.altitude}
            """.trimIndent()
        )
        gotValidLocation(location.time / 1000, location.longitude, location.latitude, location.bearing, location.speed, location.altitude)
    }

    private fun checkPermission(): Boolean {
        return !(ActivityCompat.checkSelfPermission(
            this, Manifest.permission.ACCESS_FINE_LOCATION
        ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
            this, Manifest.permission.ACCESS_COARSE_LOCATION
        ) != PackageManager.PERMISSION_GRANTED)
    }

    // 判断是否需要重新定位
    private fun checkGpsIsInvalid(location: Location): Boolean {
        return System.currentTimeMillis() - location.time >= locationInvalidTimeInterval /*|| (location.longitude.absoluteValue + location.latitude.absoluteValue) < 0.0000001*/
    }

    private var locationListenerOnce = object : LocationListener {
        override fun onLocationChanged(location: Location) {
            Log.d(
                tag,
                "LocationManager locationListenerOnce onLocationChanged:" + location.time + " lon:" + location.longitude + " lat:" + location.latitude
            )
            onGetLocation(location)
            removeListenerOnce()
        }

        // 必须以下事件，否则定位调用报错
        @Deprecated("Deprecated in Java")
        override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {
            Log.d(
                tag, "LocationManager locationListenerOnce onStatusChanged provider: " + provider + ", status: " + status
            )
        }

        override fun onFlushComplete(requestCode: Int) {
            Log.d(tag, "LocationManager locationListenerOnce onFlushComplete: $requestCode")
        }

        override fun onProviderEnabled(provider: String) {
            Log.d(tag, "LocationManager locationListenerOnce onProviderEnabled: $provider")
        }

        override fun onProviderDisabled(provider: String) {
            Log.d(tag, "LocationManager locationListenerOnce onProviderDisabled: $provider")
        }
    }

    private fun removeListenerOnce() {
        locationManager.removeUpdates(locationListenerOnce)
    }

    private fun getLastLocation(isForce: Int) {
        if (!checkPermission()) {
            Log.e(tag, "No Permission For Location")
            return
        }

        if (isGooglePlayServicesAvailable) {
            fun getCurrentLocation() {
                fusedLocationClient.getCurrentLocation(PRIORITY_HIGH_ACCURACY, object : CancellationToken() {
                    override fun onCanceledRequested(p0: OnTokenCanceledListener) = CancellationTokenSource().token

                    override fun isCancellationRequested() = false
                }).addOnSuccessListener { location2: Location? ->
                    if (location2 == null) {
                        Log.e(tag, "Got Null Location data on getCurrentLocation")
                    } else {
                        onGetLocation(location2)
                    }
                }
            }

            fusedLocationClient.lastLocation.addOnSuccessListener { location: Location? ->
                if (location != null) {
                    // 判断是否需要重新定位
                    if (checkGpsIsInvalid(location)) {
                        Log.e(tag, "Got Zero Location data in lastLocation, try getCurrentLocation")
                        //get current location
                        getCurrentLocation()
                    } else {
                        onGetLocation(location)
                    }
                } else {
                    Log.i(tag, "Got Null Location data, try getCurrentLocation")
                    //get current location
                    getCurrentLocation()
                }
            }
            return
        }


        Log.e(
            tag, "Google Play Service Not Available, can not get last location use fusedLocationClient"
        )
        val location = locationManager.getLastKnownLocation(locationManagerProvider)
        // 上次定位没有，则获取当前的定位
        if (location == null) {
            Log.d(tag, "LocationManager getLastKnownLocation is null")
            locationManager.requestLocationUpdates(
                locationManagerProvider, locationInterval, minDistanceM, locationListenerOnce, Looper.getMainLooper()
            )
            return
        }

        // 判断是否需要重新定位
        if (checkGpsIsInvalid(location) || isForce == 1) {
            locationManager.requestLocationUpdates(
                locationManagerProvider, locationInterval, minDistanceM, locationListenerOnce, Looper.getMainLooper()
            )
            return
        }

        Log.d(
            tag, "LocationManager location:" + location.time + " lon:" + location.longitude + " lat:" + location.latitude
        )
        onGetLocation(location)
    }
}