package com.bfdx.bf8100deviceapp

import android.app.KeyguardManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import android.bluetooth.BluetoothDevice
import android.media.AudioDeviceInfo

class StaticBroadcastReceiver : BroadcastReceiver() {
    private val tag = "${TagPrefix}BroadcastReceiver"

    private fun isDeviceLocked(context: Context): Boolean {
        val keyguardManager = context.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        return keyguardManager.inKeyguardRestrictedInputMode()
    }

    private fun onPttDown(context: Context, intent: Intent, isLocked: Boolean) {
        if (!isLocked) {
            context.sendBroadcast(getLightIntent(LedPttMode.red))
        }
        /*// 必须停止当前播报的语音，以准备呼叫
        val stopTts = TtsIntent("")
        stopTts.action = TtsIntent.ACTION_STOP
        LocalBroadcastManager.getInstance(context).sendBroadcast(stopTts)*/
    }

    private fun onPttUp(context: Context, intent: Intent, isLocked: Boolean) {
        context.sendBroadcast(getLightIntent(LedPttMode.turnOff))
    }

    override fun onReceive(context: Context, intent: Intent) {
        var action = intent.action
        Log.i(tag, "received intent: $action, ${context.applicationContext}")

        // 判断是否为专用蓝牙耳机事件
        if (action == "android.bluetooth.headset.action.VENDOR_SPECIFIC_HEADSET_EVENT") {
            try {
                val eventExtra = intent.extras
                var eventValue: Int = -1
                val args = eventExtra?.get("android.bluetooth.headset.extra.VENDOR_SPECIFIC_HEADSET_EVENT_ARGS") as Array<*>?
                if (args != null) {
                    if (args.size >= 2) {
                        val eventName = args[0] as String
                        try {
                            eventValue = args[1] as Int
                        } catch (e2: java.lang.Exception) {
                            eventValue = (args[1] as String).toInt()
                        }
                        if (eventName == "TALK") {
                            if (eventValue == 1) {
                                action = "unipro.hotkey.headset.ptt.down"
                            } else {
                                if (eventValue == 0) {
                                    action = "unipro.hotkey.headset.ptt.up"
                                }
                            }
                        }
//                    if (eventName == "SOS") {
//                    }
                    } else if (args.size == 1) {
                        val eventName2 = args[0] as String
                        if (eventName2 == "PTT_DOWN") {
                            action = "unipro.hotkey.headset.ptt.down"
                        } else if (eventName2 == "PTT_UP") {
                            action = "unipro.hotkey.headset.ptt.up"
                        }
                    }
                }
            } catch (e3: java.lang.Exception) {
                Log.e(tag, e3.message!!)
            }
        }

        val lock = isDeviceLocked(context)
        // 处理特殊的广播
        when (action) {
            "unipro.hotkey.ptt.down" -> {
                onPttDown(context, intent, lock)
            }

            "unipro.hotkey.headset.ptt.down" -> {
                onPttDown(context, intent, lock)
            }

            "unipro.hotkey.ptt.up" -> {
                onPttUp(context, intent, lock)
            }

            "unipro.hotkey.headset.ptt.up" -> {
                onPttUp(context, intent, lock)
            }

            "android.media.VOLUME_CHANGED_ACTION" -> {
                val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                val streamVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
                val streamType = intent.getIntExtra("android.media.EXTRA_VOLUME_STREAM_TYPE", -1)
                val volume = intent.getIntExtra("android.media.EXTRA_VOLUME_STREAM_VALUE", -1)
                val prevVolume = intent.getIntExtra("android.media.EXTRA_PREV_VOLUME_STREAM_VALUE", -1)
                Log.d(tag, "Volume changed for stream: $streamType, new volume: $volume, previous volume: $prevVolume, streamVolume: $streamVolume")

                if (volume == prevVolume) {
                    return
                }

                if (volume > 0) {
                    val ttsIntent = TtsIntent(context.getString(R.string.tts_volume_level, volume))
                    LocalBroadcastManager.getInstance(context).sendBroadcast(ttsIntent)
                }
            }
        }
        if (lock) {
            Log.d(tag, "block event due to locked state")
            return
        }

        // 转发到MainActivity，以取代原有的onKeyDown等方法
        val mainIntent = Intent()
        mainIntent.action = ActionStaticBroadcastReceiver
        mainIntent.putExtra("originAction", action)
        LocalBroadcastManager.getInstance(context).sendBroadcast(mainIntent)
    }
}
