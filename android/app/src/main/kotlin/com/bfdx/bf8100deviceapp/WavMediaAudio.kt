package com.bfdx.bf8100deviceapp

import android.annotation.SuppressLint
import android.content.Context
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import android.util.Log
import androidx.annotation.GuardedBy
import io.flutter.plugin.common.MethodChannel
import java.io.IOException
import java.io.InputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * WAV文件头结构共44字节:
 * 偏移量  大小	数据类型     描述                                                                             值/示例
 * 0        4	char[4]	    Chunk ID (也称为 RIFF ID)	                                                    "RIFF" (0x52494646)
 * 4	    4	int	        Chunk Size (文件大小 - 8 字节)	                                                文件大小 - 8 (小端序)
 * 8	    4	char[4]     Format (RIFF 类型)	                                                            "WAVE" (0x57415645)
 * 12	    4	char[4]	    Subchunk1 ID (Format Chunk ID)	                                                "fmt " (0x666d7420)
 * 16	    4	int	        Subchunk1 Size (Format Chunk Size)	                                            16 (对于 PCM 格式) (小端序)
 * 20	    2	short	    Audio Format (音频格式)	                                                        1 (PCM - 未压缩) (小端序)
 * 22	    2	short	    Num Channels (声道数)	                                                        1 (单声道), 2 (立体声) (小端序)
 * 24	    4	int	        Sample Rate (采样率)	                                                            44100 (Hz), 48000 (Hz) (小端序)
 * 28	    4	int	        Byte Rate (传输速率) = SampleRate * NumChannels * BitsPerSample/8	            采样率 * 声道数 * 位深度 / 8 (小端序)
 * 32	    2	short	    Block Align (块对齐) = NumChannels * BitsPerSample/8	                            声道数 * 位深度 / 8 (小端序)
 * 34	    2	short	    Bits Per Sample (位深度)	                                                        8 (8 位), 16 (16 位) (小端序)
 * 36	    4	char[4]	    Subchunk2 ID (Data Chunk ID)	                                                "data" (0x64617461)
 * 40	    4	int	        Subchunk2 Size (Data Chunk Size) = NumSamples * NumChannels * BitsPerSample/8	采样点数 * 声道数 * 位深度 / 8 (小端序)
 */

class WavHeader {
    var sampleRate: Int = 0
    var channels: Int = 0
    var bitsPerSample: Int = 0
    var dataOffset: Int = 0
    var dataSize: Int = 0

    companion object {
        private val TAG = "${TagPrefix}WavHeader"
        fun parseHeader(inputStream: InputStream): WavHeader {
            // Read the first 44 bytes to parse the header (assuming standard WAV format)
            val headerBytes = ByteArray(44)
            val bytesRead = inputStream.read(headerBytes)
            if (bytesRead != 44) {
                throw Exception("Invalid WAV file: Header is shorter than 44 bytes")
            }

            val buffer = ByteBuffer.wrap(headerBytes).order(ByteOrder.LITTLE_ENDIAN)
            val chunkId = String(headerBytes, 0, 4) // "RIFF"
            val chunkSize = buffer.getInt(4)
            val format = String(headerBytes, 8, 4) // "WAVE"
            val subChunk1Id = String(headerBytes, 12, 4) // "fmt "
            val subChunk1Size = buffer.getInt(16)
            val audioFormat = buffer.getShort(20).toInt()
            val channels = buffer.getShort(22).toInt()
            val sampleRate = buffer.getInt(24)
            val byteRate = buffer.getInt(28)
            val blockAlign = buffer.getShort(32).toInt()
            val bitsPerSample = buffer.getShort(34).toInt()
            val subChunk2Id = String(headerBytes, 36, 4) // "data"
            val dataSize = buffer.getInt(40)

            // Validate ChunkID, Format and subChunk1ID
            if (chunkId != "RIFF") throw Exception("Invalid Wav File: Missing RIFF ChunkID")
            if (format != "WAVE") throw Exception("Invalid Wav File: Missing WAVE Format")
            if (subChunk1Id != "fmt ") throw Exception("Invalid Wav File: Missing fmt ChunkID")

            Log.d(TAG, "chunkId: $chunkId")
            Log.d(TAG, "chunkSize: $chunkSize")
            Log.d(TAG, "format: $format")
            Log.d(TAG, "subChunk1Id: $subChunk1Id")
            Log.d(TAG, "subChunk1Size: $subChunk1Size")
            Log.d(TAG, "audioFormat: $audioFormat")
            Log.d(TAG, "channels: $channels")
            Log.d(TAG, "sampleRate: $sampleRate")
            Log.d(TAG, "byteRate: $byteRate")
            Log.d(TAG, "blockAlign: $blockAlign")
            Log.d(TAG, "bitsPerSample: $bitsPerSample")
            Log.d(TAG, "subChunk2Id: $subChunk2Id")
            Log.d(TAG, "dataSize: $dataSize")

            val header = WavHeader()
            header.sampleRate = sampleRate
            header.channels = channels
            header.bitsPerSample = bitsPerSample
            header.dataOffset = 44 // Standard WAV data offset
            header.dataSize = dataSize

            return header
        }
    }
}

class WavMediaAudio {
    companion object {
        private val TAG = "${TagPrefix}WavMediaAudio"

        @SuppressLint("StaticFieldLeak")
        private lateinit var context: Context
        fun init(context: Context) {
            this.context = context
        }

        // 缓存的音频数据
        private data class CachedAudio(
            val sampleRate: Int,
            val channels: Int,
            val bitsPerSample: Int,
            val pcmData: ByteArray
        )

        private val audioCache = mutableMapOf<String, CachedAudio>()
        private val audioCacheLock = Any() // 用于同步audioCache的锁

        // 单例的 AudioTrack 播放器
        @GuardedBy("audioTrackLock")
        private var audioTrack: AudioTrack? = null
        private val audioTrackLock = Any() // 用于同步 audioTrack的锁

        /**
         * 播放res/raw目录指定文件的音频
         * @param resourceName 音频资源名称，不包含扩展名，例如"sms_receive"
         * @param result Flutter的MethodChannel.Result对象，用于返回结果
         */
        fun playSoundEffects(resourceName: String, result: MethodChannel.Result) {
            synchronized(audioCacheLock) {
                val cachedAudio = audioCache[resourceName]
                if (cachedAudio != null) {
                    // 播放缓存的音频
                    Log.d(TAG, "Playing cached audio: $resourceName")
                    playCachedAudio(cachedAudio, result)
                    return
                }

                // 加载音频并缓存
                Log.d(TAG, "Loading and caching audio: $resourceName")
                loadAndCacheAudio(resourceName, result)
            }
        }

        private fun loadAndCacheAudio(resourceName: String, result: MethodChannel.Result) {
            val resourceId = context.resources.getIdentifier(resourceName, "raw", context.packageName)
            if (resourceId == 0) {
                result.error("RESOURCE_NOT_FOUND", "Audio resource not found: $resourceName", null)
                return
            }

            var inputStream: InputStream? = null
            try {
                inputStream = context.resources.openRawResource(resourceId)
                val header = WavHeader.parseHeader(inputStream)

                // 读取 PCM 数据
                val pcmData = ByteArray(header.dataSize)
                inputStream.skip(header.dataOffset.toLong())
                inputStream.read(pcmData, 0, header.dataSize)

                // 创建缓存的音频数据
                val cachedAudio = CachedAudio(
                    header.sampleRate,
                    header.channels,
                    header.bitsPerSample,
                    pcmData
                )

                synchronized(audioCacheLock) {
                    audioCache[resourceName] = cachedAudio
                }

                // 播放缓存的音频
                playCachedAudio(cachedAudio, result)

            } catch (e: Exception) {
                result.error("AUDIO_PLAYBACK_ERROR", "Error playing audio: ${e.message}", null)
            } finally {
                try {
                    inputStream?.close()
                } catch (ioException: IOException) {
                    Log.e(TAG, "Error closing input stream: ${ioException.message}")
                }
            }
        }

        private fun playCachedAudio(cachedAudio: CachedAudio, result: MethodChannel.Result) {
            synchronized(audioTrackLock) {
                try {
                    // 检查 audioTrack 是否已经初始化, 未初始化则进行初始化
                    if (audioTrack == null) {
                        val channelConfig = if (cachedAudio.channels == 1) AudioFormat.CHANNEL_OUT_MONO else AudioFormat.CHANNEL_OUT_STEREO
                        val audioFormat = if (cachedAudio.bitsPerSample == 8) AudioFormat.ENCODING_PCM_8BIT else AudioFormat.ENCODING_PCM_16BIT
                        val bufferSize = AudioTrack.getMinBufferSize(cachedAudio.sampleRate, channelConfig, audioFormat)
                        if (bufferSize == AudioTrack.ERROR_BAD_VALUE || bufferSize == AudioTrack.ERROR) {
                            result.error("AUDIO_TRACK_ERROR", "Failed to get minimum buffer size", null)
                            return
                        }

                        audioTrack = AudioTrack(
                            AudioManager.STREAM_MUSIC,
                            cachedAudio.sampleRate,
                            channelConfig,
                            audioFormat,
                            bufferSize * 2,
                            AudioTrack.MODE_STREAM
                        )
                    }

                    //  确保 audioTrack 处于停止状态
                    if (audioTrack?.playState == AudioTrack.PLAYSTATE_PLAYING) {
                        audioTrack?.stop()
                        audioTrack?.flush()
                    }

                    // 写入 PCM 数据并播放
                    audioTrack?.let {
                        it.play()
                        it.write(cachedAudio.pcmData, 0, cachedAudio.pcmData.size)
                    }

                    result.success(true)
                } catch (e: Exception) {
                    result.error("AUDIO_PLAYBACK_ERROR", "Error playing audio: ${e.message}", null)
                }
            }
        }

        fun dispose() {
            synchronized(audioTrackLock) {
                audioTrack?.release()
                audioTrack = null
            }
        }
    }
}
