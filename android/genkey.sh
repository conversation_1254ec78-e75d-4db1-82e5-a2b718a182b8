#!/usr/bin/env bash

cd "$(dirname "$0")"

#输入密钥库口令: bf8100deviceapp
 #再次输入新口令: bf8100deviceapp
 #您的名字与姓氏是什么?
 #  [Unknown]:  <PERSON><PERSON>
 #您的组织单位名称是什么?
 #  [Unknown]:  <PERSON><PERSON>
 #您的组织名称是什么?
 #  [Unknown]:  BFDX
 #您所在的城市或区域名称是什么?
 #  [Unknown]:  QuanZhou
 #您所在的省/市/自治区名称是什么?
 #  [Unknown]:  FuJian
 #该单位的双字母国家/地区代码是什么?
 #  [Unknown]:  CN
 #CN=Be<PERSON>, OU=Be<PERSON> Feng, O=BFDX, L=Quanzhou, ST=FuJian, C=CN是否正确?
 #  [否]:  是
 #
 # 输入源密钥库口令:  bf8100deviceapp
keytool -genkey -v -keystore bf8100deviceapp-release.jks \
  -keyalg RSA \
  -keysize 2048 -validity 36500 \
  -alias bf8100deviceapp

# JKS 密钥库使用专用格式 迁移到行业标准格式
keytool -importkeystore -srckeystore bf8100deviceapp-release.jks -destkeystore bf8100deviceapp-release.jks -deststoretype pkcs12

rm bf8100deviceapp-release.jks.old