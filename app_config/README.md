# 账号设置工具

## i18n 国际化

使用方法，以 "zh-CN" 为例，在 `app_config` 目录下：

1. 全局安装 `gotext` 工具: `go install golang.org/x/text/cmd/gotext@latest`,修改 `i18n/i18n.go` 中 `go generate` 注释，在 `-lang` 中添加 `zh-CN`。
2. 运行 `go generate ./i18n/i18n.go`，此时 `gotext` 会遍历 appconfig/gui 这个包内所有`message.Printer.Printf()`，`Fprintf()` 和 `Sprintf()` 方法，根据参数字符串生成 `locales/catalog.gen.go` 和 `locales/zh-CN/out.gotext.json`。
3. 复制 `locales/zh-CN/out.gotext.json` 到 `locales/zh-CN/messages.gotext.json`，在 `locales/zh-CN/messages.gotext.json` 中添加翻译。
4. 完成翻译后, 运行 `go generate ./i18n/i18n.go` 更新，翻译结果会同步到 `locales/catalog.gen.go` 和 `locales/zh-CN/out.gotext.json` 中。
5. 在 `i18n/i18n.go` 中修改 `locales` 数组，添加新的语言支持，此步骤只在添加新语言时进行。添加新翻译字符串时，重复 2-4 步骤。
6. 使用时，调用 `i18n.Get("zh-CN").Translate(key, args...)` 方法。

## 目录结构

```
.
├── adb - 设备管理
├── app_proto - protobuf 生成文件
├── embed - 嵌入的文件
├── env - 环境变量
├── gui - Tk9.0
├── i18n - 国际化
```