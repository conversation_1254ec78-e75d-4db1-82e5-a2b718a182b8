package adb

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"appconfig/env"

	"github.com/go-cmd/cmd"
)

const cmdTimeout = 10 * time.Second

type Device struct {
	Id string
	// Device name
	manufacturer string
	model        string
	// 本地端口，通过这个端口连接到远程端口
	Port        int
	IsForwarded atomic.Bool
}

func NewDevice(id string) *Device {
	d := &Device{
		Id: id,
	}

	d.QueryModel()
	d.QueryManufacturer()

	return d
}

func (d *Device) DisplayName() string {
	sb := &strings.Builder{}
	if d.manufacturer != "" {
		sb.WriteString(d.manufacturer)
		sb.WriteString(" ")
	}

	if d.model != "" {
		sb.WriteString(d.model)
		sb.WriteString(" ")
	}

	if sb.Len() > 0 {
		sb.WriteString("(")
		sb.WriteString(d.Id)
		sb.WriteString(")")
	} else {
		sb.WriteString(d.Id)
	}
	return sb.String()
}

func (d *Device) QueryManufacturer() {
	args := []string{"-s", d.Id, "shell", "getprop", "ro.product.manufacturer"}

	statusChan := NewHiddenCmd(GAdbManager.Path, args...).Start()

	timer := time.NewTimer(cmdTimeout)
	select {
	case status := <-statusChan:
		if !status.Complete {
			return
		}

		if len(status.Stdout) > 0 {
			d.manufacturer = strings.TrimSpace(status.Stdout[0])
		}
	case <-timer.C:
		log.Printf("Timeout: could not find %s manufacturer name", d.Id)
		return
	}
}

func (d *Device) QueryModel() {
	args := []string{"-s", d.Id, "shell", "getprop", "ro.product.model"}

	statusChan := NewHiddenCmd(GAdbManager.Path, args...).Start()

	timer := time.NewTimer(cmdTimeout)
	select {
	case status := <-statusChan:
		if !status.Complete {
			return
		}

		if len(status.Stdout) > 0 {
			d.model = strings.TrimSpace(status.Stdout[0])
		}
	case <-timer.C:
		log.Printf("Timeout: could not find %s model name", d.Id)
		return
	}
}

// 将设备的REMOTE端口映射到本地的LOCAL端口
func (d *Device) ForwardPort(Local, Remote int) error {
	_, err := RunCmd(cmdTimeout, GAdbManager.Path,
		"-s",
		d.Id,
		"forward",
		"tcp:"+strconv.Itoa(Local),
		"tcp:"+strconv.Itoa(Remote),
	)
	if err != nil {
		return err
	}
	d.Port = Local
	d.IsForwarded.Store(true)
	return nil
}

func (d *Device) CloseForwardPort(port int) error {
	if port == 0 {
		return fmt.Errorf("port not forward")
	}

	_, err := RunCmd(cmdTimeout, GAdbManager.Path,
		"-s",
		d.Id,
		"forward",
		"--remove",
		"tcp:"+strconv.Itoa(port),
	)
	if err != nil {
		return err
	}

	d.IsForwarded.Store(false)
	d.Port = 0
	return nil
}

// 关闭设备所有转发的端口
func (d *Device) CloseAllForwardPort() error {
	_, err := RunCmd(cmdTimeout, GAdbManager.Path,
		"-s",
		d.Id,
		"forward",
		"--remove-all",
	)
	if err != nil {
		return err
	}
	d.IsForwarded.Store(false)
	return nil
}

// 查询已转发的端口
func (d *Device) QueryForwardPord() int {
	args := []string{
		"-s",
		d.Id,
		"forward",
		"--list",
	}

	statusChan := NewHiddenCmd(GAdbManager.Path, args...).Start()

	timer := time.NewTimer(cmdTimeout)
	select {
	case status := <-statusChan:
		if !status.Complete {
			return 0
		}

		for _, line := range status.Stdout {
			fields := strings.Fields(line)
			if len(fields) < 3 {
				continue
			}
			if !strings.HasPrefix(fields[2], "tcp:") {
				continue
			}
			remotePort, err := strconv.Atoi(strings.TrimPrefix(fields[2], "tcp:"))
			if err != nil {
				continue
			}

			if remotePort < env.ProxyHttpPort || remotePort > env.ProxyHttpPort+2 {
				continue
			}
			// parse local port
			localPort, err := strconv.Atoi(strings.TrimPrefix(fields[1], "tcp:"))
			if err != nil {
				continue
			}
			return localPort
		}
	case <-timer.C:
		log.Printf("Timeout: could not query %s forward port", d.Id)
		return 0
	}
	return 0
}

func (d *Device) RunCmd(args []string, timeout time.Duration) (*cmd.Status, error) {
	arguments := append([]string{"-s", d.Id}, args...)
	return RunCmd(timeout, GAdbManager.Path, arguments...)
}

func (d *Device) DumpsysApk() (*cmd.Status, error) {
	args := []string{"shell", "dumpsys", "package", env.ApkPackageName}
	return d.RunCmd(args, 5*time.Second)
}
