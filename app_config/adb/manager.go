package adb

import (
	"errors"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"appconfig/env"

	"github.com/go-cmd/cmd"
)

type AdbManager struct {
	Path string
	// id -> device
	DeviceMap map[string]*Device
}

var GAdbManager = &AdbManager{
	DeviceMap: make(map[string]*Device),
}

// 查找路径:  pwd,adb/,PATH, ANDROID_HOME/platform-tools/
func (m *AdbManager) FindAdb() error {
	currentDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("get current dir error: %w", err)
	}

	exe := "adb"
	if runtime.GOOS == "windows" {
		exe += ".exe"
	}
	search := []string{
		env.DefaultAdbPath,
		filepath.Join(currentDir, "adb"),
		filepath.Join(currentDir, "adb", exe),
		exe,
	}

	if home := os.Getenv("ANDROID_HOME"); home != "" {
		search = append(search, filepath.Join(home, "platform-tools", exe))
	}

	for _, path := range search {
		if p, err := exec.LookPath(path); err == nil {
			m.Path = p
			return nil
		}
	}

	return fmt.Errorf("adb could not be found from ANDROID_HOME or PATH\n"+
		"search: %v",
		search)
}

func (m *AdbManager) ChangeAdbPath(path string) error {
	_, err := exec.LookPath(path)
	if err != nil {
		return err
	}

	m.Path = path
	return nil
}

func (m *AdbManager) FindDevices() error {
	m.DeviceMap = make(map[string]*Device)
	if m.Path == "" {
		err := m.FindAdb()
		if err != nil {
			return fmt.Errorf("could not find adb: %w", err)
		}
	}

	status, err := RunCmd(cmdTimeout, m.Path, "devices")
	if err != nil {
		return err
	}
	ids, err := parseDevices(status.Stdout)
	if err != nil {
		return err
	}
	if len(ids) == 0 {
		return fmt.Errorf("no device found")
	}
	for _, id := range ids {
		device := NewDevice(id)
		m.DeviceMap[id] = device
	}

	return nil
}

func parseDevices(out []string) ([]string, error) {
	if len(out) < 2 {
		return nil, fmt.Errorf("no device list")
	}
	deviceIds := make([]string, 0)
	for _, line := range out {
		if strings.HasPrefix(line, "List of devices attached") {
			continue
		}
		if strings.HasPrefix(line, "adb server version") && strings.HasSuffix(line, "killing...") {
			continue // adb server version (36) doesn't match this client (35); killing...
		}
		if strings.HasPrefix(line, "*") {
			continue // For example, "* daemon started successfully *"
		}
		fields := strings.Fields(line)
		switch len(fields) {
		case 0:
			continue
		case 2:
			serial, status := fields[0], fields[1]
			switch status {
			case "device":
				deviceIds = append(deviceIds, serial)
			default:
				log.Println("device ", serial, " not add, status: ", status)
			}
		default:
			return nil, fmt.Errorf("invalid status string")
		}
	}
	return deviceIds, nil
}

// NewHiddenCmd 创建一个隐藏窗口的命令
func NewHiddenCmd(name string, args ...string) *cmd.Cmd {
	return cmd.NewCmdOptions(
		cmd.Options{
			BeforeExec: []func(cmd *exec.Cmd){
				configCmd,
			},
			Buffered: true,
		},
		name, args...)
}

func RunCmd(timeout time.Duration, name string, args ...string) (*cmd.Status, error) {
	statusChan := NewHiddenCmd(name, args...).Start()

	timer := time.NewTimer(timeout)
	select {
	case status := <-statusChan:
		if status.Error != nil {
			return &status, status.Error
		}

		if !status.Complete {
			return &status, fmt.Errorf("command not complete, error: %s", status.Error)
		}

		if status.Exit != 0 {
			b := strings.Builder{}
			for _, line := range status.Stderr {
				b.WriteString(line)
				b.WriteString("\n")
			}
			return &status, fmt.Errorf("exit code %d: %s", status.Exit, b.String())
		}
		return &status, nil

	case <-timer.C:
		return nil, errors.New("Timeout")
	}
}
