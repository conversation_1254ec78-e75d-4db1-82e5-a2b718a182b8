// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: app_config_only.proto

package app_proto

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type RpcCmd struct {
	// sequence no
	SeqNo int32 `protobuf:"varint,2,opt,name=seq_no,json=seqNo,proto3" json:"seq_no,omitempty"`
	// session id
	Sid int64 `protobuf:"varint,3,opt,name=sid,proto3" json:"sid,omitempty"`
	// rpc command code
	Cmd int32 `protobuf:"varint,5,opt,name=cmd,proto3" json:"cmd,omitempty"`
	// response code
	Res int32 `protobuf:"varint,8,opt,name=res,proto3" json:"res,omitempty"`
	// command body
	Body []byte `protobuf:"bytes,10,opt,name=body,proto3" json:"body,omitempty"`
	// optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
	ParaStr string `protobuf:"bytes,11,opt,name=para_str,json=paraStr,proto3" json:"para_str,omitempty"`
	// optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
	ParaBin []byte `protobuf:"bytes,12,opt,name=para_bin,json=paraBin,proto3" json:"para_bin,omitempty"`
	// optional int64 parameter
	ParaInt int64 `protobuf:"varint,13,opt,name=para_int,json=paraInt,proto3" json:"para_int,omitempty"`
}

func (m *RpcCmd) Reset()         { *m = RpcCmd{} }
func (m *RpcCmd) String() string { return proto.CompactTextString(m) }
func (*RpcCmd) ProtoMessage()    {}
func (*RpcCmd) Descriptor() ([]byte, []int) {
	return fileDescriptor_2fa47570db0c4dca, []int{0}
}
func (m *RpcCmd) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RpcCmd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RpcCmd.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RpcCmd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RpcCmd.Merge(m, src)
}
func (m *RpcCmd) XXX_Size() int {
	return m.Size()
}
func (m *RpcCmd) XXX_DiscardUnknown() {
	xxx_messageInfo_RpcCmd.DiscardUnknown(m)
}

var xxx_messageInfo_RpcCmd proto.InternalMessageInfo

func (m *RpcCmd) GetSeqNo() int32 {
	if m != nil {
		return m.SeqNo
	}
	return 0
}

func (m *RpcCmd) GetSid() int64 {
	if m != nil {
		return m.Sid
	}
	return 0
}

func (m *RpcCmd) GetCmd() int32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *RpcCmd) GetRes() int32 {
	if m != nil {
		return m.Res
	}
	return 0
}

func (m *RpcCmd) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *RpcCmd) GetParaStr() string {
	if m != nil {
		return m.ParaStr
	}
	return ""
}

func (m *RpcCmd) GetParaBin() []byte {
	if m != nil {
		return m.ParaBin
	}
	return nil
}

func (m *RpcCmd) GetParaInt() int64 {
	if m != nil {
		return m.ParaInt
	}
	return 0
}

func init() {
	proto.RegisterType((*RpcCmd)(nil), "app_proto.rpc_cmd")
}

func init() { proto.RegisterFile("app_config_only.proto", fileDescriptor_2fa47570db0c4dca) }

var fileDescriptor_2fa47570db0c4dca = []byte{
	// 233 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x4d, 0x2c, 0x28, 0x88,
	0x4f, 0xce, 0xcf, 0x4b, 0xcb, 0x4c, 0x8f, 0xcf, 0xcf, 0xcb, 0xa9, 0xd4, 0x2b, 0x28, 0xca, 0x2f,
	0xc9, 0x17, 0xe2, 0x04, 0x09, 0x83, 0x99, 0x4a, 0xbb, 0x19, 0xb9, 0xd8, 0x8b, 0x0a, 0x92, 0xe3,
	0x93, 0x73, 0x53, 0x84, 0x44, 0xb9, 0xd8, 0x8a, 0x53, 0x0b, 0xe3, 0xf3, 0xf2, 0x25, 0x98, 0x14,
	0x18, 0x35, 0x58, 0x83, 0x58, 0x8b, 0x53, 0x0b, 0xfd, 0xf2, 0x85, 0x04, 0xb8, 0x98, 0x8b, 0x33,
	0x53, 0x24, 0x98, 0x15, 0x18, 0x35, 0x98, 0x83, 0x40, 0x4c, 0x90, 0x48, 0x72, 0x6e, 0x8a, 0x04,
	0x2b, 0x58, 0x15, 0x88, 0x09, 0x12, 0x29, 0x4a, 0x2d, 0x96, 0xe0, 0x80, 0x88, 0x14, 0xa5, 0x16,
	0x0b, 0x09, 0x71, 0xb1, 0x24, 0xe5, 0xa7, 0x54, 0x4a, 0x70, 0x29, 0x30, 0x6a, 0xf0, 0x04, 0x81,
	0xd9, 0x42, 0x92, 0x5c, 0x1c, 0x05, 0x89, 0x45, 0x89, 0xf1, 0xc5, 0x25, 0x45, 0x12, 0xdc, 0x0a,
	0x8c, 0x1a, 0x9c, 0x41, 0xec, 0x20, 0x7e, 0x70, 0x49, 0x11, 0x5c, 0x2a, 0x29, 0x33, 0x4f, 0x82,
	0x07, 0xac, 0x05, 0x2c, 0xe5, 0x94, 0x99, 0x07, 0x97, 0xca, 0xcc, 0x2b, 0x91, 0xe0, 0x05, 0x3b,
	0x02, 0x2c, 0xe5, 0x99, 0x57, 0xe2, 0xa4, 0x7a, 0xe2, 0x91, 0x1c, 0xe3, 0x85, 0x47, 0x72, 0x8c,
	0x0f, 0x1e, 0xc9, 0x31, 0x4e, 0x78, 0x2c, 0xc7, 0x70, 0xe1, 0xb1, 0x1c, 0xc3, 0x8d, 0xc7, 0x72,
	0x0c, 0x1e, 0xcc, 0x51, 0x08, 0x4f, 0x26, 0xb1, 0x81, 0x29, 0x63, 0x40, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x2e, 0x55, 0x3f, 0xe3, 0x0f, 0x01, 0x00, 0x00,
}

func (m *RpcCmd) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RpcCmd) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RpcCmd) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ParaInt != 0 {
		i = encodeVarintAppConfigOnly(dAtA, i, uint64(m.ParaInt))
		i--
		dAtA[i] = 0x68
	}
	if len(m.ParaBin) > 0 {
		i -= len(m.ParaBin)
		copy(dAtA[i:], m.ParaBin)
		i = encodeVarintAppConfigOnly(dAtA, i, uint64(len(m.ParaBin)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.ParaStr) > 0 {
		i -= len(m.ParaStr)
		copy(dAtA[i:], m.ParaStr)
		i = encodeVarintAppConfigOnly(dAtA, i, uint64(len(m.ParaStr)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintAppConfigOnly(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x52
	}
	if m.Res != 0 {
		i = encodeVarintAppConfigOnly(dAtA, i, uint64(m.Res))
		i--
		dAtA[i] = 0x40
	}
	if m.Cmd != 0 {
		i = encodeVarintAppConfigOnly(dAtA, i, uint64(m.Cmd))
		i--
		dAtA[i] = 0x28
	}
	if m.Sid != 0 {
		i = encodeVarintAppConfigOnly(dAtA, i, uint64(m.Sid))
		i--
		dAtA[i] = 0x18
	}
	if m.SeqNo != 0 {
		i = encodeVarintAppConfigOnly(dAtA, i, uint64(m.SeqNo))
		i--
		dAtA[i] = 0x10
	}
	return len(dAtA) - i, nil
}

func encodeVarintAppConfigOnly(dAtA []byte, offset int, v uint64) int {
	offset -= sovAppConfigOnly(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *RpcCmd) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SeqNo != 0 {
		n += 1 + sovAppConfigOnly(uint64(m.SeqNo))
	}
	if m.Sid != 0 {
		n += 1 + sovAppConfigOnly(uint64(m.Sid))
	}
	if m.Cmd != 0 {
		n += 1 + sovAppConfigOnly(uint64(m.Cmd))
	}
	if m.Res != 0 {
		n += 1 + sovAppConfigOnly(uint64(m.Res))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovAppConfigOnly(uint64(l))
	}
	l = len(m.ParaStr)
	if l > 0 {
		n += 1 + l + sovAppConfigOnly(uint64(l))
	}
	l = len(m.ParaBin)
	if l > 0 {
		n += 1 + l + sovAppConfigOnly(uint64(l))
	}
	if m.ParaInt != 0 {
		n += 1 + sovAppConfigOnly(uint64(m.ParaInt))
	}
	return n
}

func sovAppConfigOnly(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozAppConfigOnly(x uint64) (n int) {
	return sovAppConfigOnly(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RpcCmd) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppConfigOnly
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: rpc_cmd: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: rpc_cmd: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SeqNo", wireType)
			}
			m.SeqNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sid", wireType)
			}
			m.Sid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			m.Cmd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cmd |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Res", wireType)
			}
			m.Res = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Res |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAppConfigOnly
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthAppConfigOnly
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = append(m.Body[:0], dAtA[iNdEx:postIndex]...)
			if m.Body == nil {
				m.Body = []byte{}
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppConfigOnly
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppConfigOnly
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParaStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAppConfigOnly
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthAppConfigOnly
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParaBin = append(m.ParaBin[:0], dAtA[iNdEx:postIndex]...)
			if m.ParaBin == nil {
				m.ParaBin = []byte{}
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaInt", wireType)
			}
			m.ParaInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParaInt |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppConfigOnly(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppConfigOnly
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipAppConfigOnly(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAppConfigOnly
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppConfigOnly
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthAppConfigOnly
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupAppConfigOnly
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthAppConfigOnly
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthAppConfigOnly        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAppConfigOnly          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupAppConfigOnly = fmt.Errorf("proto: unexpected end of group")
)
