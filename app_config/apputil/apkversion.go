package apputil

import (
	"archive/zip"
	"bufio"
	"fmt"
	"io"
	"strings"

	"github.com/hashicorp/go-version"
)

func GetApkFileVersion(apkPath string) (*version.Version, error) {
	reader, err := zip.OpenReader(apkPath)
	if err != nil {
		return nil, fmt.Errorf("error opening APK: %v", err)
	}
	defer reader.Close()

	for _, file := range reader.File {
		if strings.Contains(file.Name, "version.properties") {
			propertiesFile, err := file.Open()
			if err != nil {
				return nil, fmt.Errorf("opening properties file error: %v", err)
			}

			properties, err := ReadPropertiesFile(propertiesFile)
			propertiesFile.Close()
			if err != nil {
				return nil, err
			}

			versionName, ok := properties["versionName"]
			if !ok {
				return nil, fmt.Errorf("versionName not found in version.properties")
			}

			return version.NewVersion(strings.TrimSpace(versionName))
		}
	}

	return nil, fmt.<PERSON><PERSON><PERSON>("version.properties not found in APK")
}

func ReadPropertiesFile(propertiesFile io.ReadCloser) (map[string]string, error) {
	properties := make(map[string]string)

	scanner := bufio.NewScanner(propertiesFile)
	for scanner.Scan() {
		line := scanner.Text()
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			properties[key] = value
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return properties, nil
}
