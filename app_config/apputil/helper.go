package apputil

import (
	"net"
	"strconv"
	"strings"
)

// GetFreePort 获取一个空闲的端口
func GetFreePort() (port int, err error) {
	var a *net.TCPAddr
	if a, err = net.ResolveTCPAddr("tcp", "localhost:0"); err == nil {
		var l *net.TCPListener
		if l, err = net.ListenTCP("tcp", a); err == nil {
			defer l.Close()
			return l.Addr().(*net.TCPAddr).Port, nil
		}
	}
	return
}

func PadStrWithLeadingZero(str string, str_min_len int) string {
	result := str
	for len(result) < str_min_len {
		result = "0" + result
	}

	return result
}

func Dmrid2Hex(dmrid uint32) string {
	hex := strconv.FormatInt(int64(dmrid), 16)
	return PadStrWithLeadingZero(strings.ToUpper(hex), 8)
}

func HexDmrid2Uint32(hexDmrid string) uint32 {
	id, err := strconv.ParseUint(hexDmrid, 16, 32)
	if err != nil {
		return 0
	}

	return uint32(id)
}
