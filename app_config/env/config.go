package env

const (
	ApkPackageName                    = "com.bfdx.bf8100deviceapp"
	WriteSecureSettingsPermissionName = "android.permission.WRITE_SECURE_SETTINGS"
)

var IsDebug bool

var Version string = "unknown"

// App go proxy 的监听地址
var ProxyHttpHost string = "127.0.0.1"

// App go proxy 的监听端口
var ProxyHttpPort int = 12255

/**默认值**/
var (
	DefaultAdbPath    string = "./adb"
	DefaultLanguage   string = "zh-CN"
	DefaultServerHost string = "t2.bfdx.net"
	DefaultServerPort int    = 2235
	DefaultApkPath    string = "./BF8100Device.apk"
)
