module appconfig

go 1.21

require (
	github.com/Unknwon/goconfig v1.0.0
	github.com/gogo/protobuf v1.3.2
	github.com/jeandeaual/go-locale v0.0.0-20240223122105-ce5225dcaa49
	github.com/joho/godotenv v1.5.1
	golang.org/x/text v0.20.0
	modernc.org/tk9.0 v0.56.3
)

require (
	github.com/hashicorp/go-version v1.7.0 // indirect
	github.com/smartystreets/goconvey v1.8.1 // indirect
	modernc.org/fileutil v1.3.0 // indirect
	modernc.org/fsm v1.2.1 // indirect
	modernc.org/gc/v3 v3.0.0-20230512134359-466b49aa80e0 // indirect
	modernc.org/knuth v0.5.3 // indirect
	modernc.org/libX11 v0.11.2 // indirect
	modernc.org/libXau v0.9.2 // indirect
	modernc.org/libXdmcp v0.12.0 // indirect
	modernc.org/libXft v0.10.0 // indirect
	modernc.org/libXrender v0.9.0 // indirect
	modernc.org/libbsd v0.11.1 // indirect
	modernc.org/libc v1.61.6 // indirect
	modernc.org/libexpat v0.10.2 // indirect
	modernc.org/libfontconfig v0.8.2 // indirect
	modernc.org/libfreetype v0.9.1 // indirect
	modernc.org/libmd v0.12.0 // indirect
	modernc.org/libtcl9.0 v0.14.0 // indirect
	modernc.org/libtk9.0 v0.13.0 // indirect
	modernc.org/libxcb v0.11.0 // indirect
	modernc.org/libz v0.16.4 // indirect
	modernc.org/mathutil v1.7.1 // indirect
	modernc.org/memory v1.8.0 // indirect
	modernc.org/ngrab v0.1.0 // indirect
	modernc.org/rec v0.2.0 // indirect
	modernc.org/regexp v1.7.3 // indirect
	modernc.org/sortutil v1.2.0 // indirect
	modernc.org/strutil v1.2.0 // indirect
	modernc.org/tcl9.0 v0.15.0 // indirect
	modernc.org/token v1.1.0 // indirect
)

require (
	github.com/adrg/xdg v0.5.0 // indirect
	github.com/disintegration/imaging v1.6.2 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/ebitengine/purego v0.8.0 // indirect
	github.com/evilsocket/islazy v1.11.0 // indirect
	github.com/go-cmd/cmd v1.4.3
	github.com/google/uuid v1.6.0 // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.1 // indirect
	github.com/mat/besticon/v3 v3.18.0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/ncruces/go-strftime v0.1.9 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	golang.org/x/exp v0.0.0-20230315142452-642cacee5cc0 // indirect
	golang.org/x/image v0.20.0 // indirect
	golang.org/x/net v0.29.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
)
