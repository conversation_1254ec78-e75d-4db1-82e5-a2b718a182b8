package gui

import (
	"errors"
	"net"
	"regexp"
	"strconv"
	"strings"

	"appconfig/apputil"
	"appconfig/env"
	"appconfig/i18n"

	. "modernc.org/tk9.0"
)

type accountConfigView struct {
	root *GuiApp
	// form widget
	frame *TLabelframeWidget

	hostLabel      *TLabelWidget
	hostInput      *TEntryWidget
	hostErrorLabel *LabelWidget

	portLabel      *TLabelWidget
	portInput      *TEntryWidget
	portErrorLabel *LabelWidget

	dmrIDLabel      *TLabelWidget
	dmrIDInput      *TEntryWidget
	dmrIDComboBox   *TComboboxWidget
	dmrIDErrorLabel *LabelWidget
	IsDmrIDHex      bool

	passwordLabel      *TLabelWidget
	passwordInput      *TEntryWidget
	passwordErrorLabel *LabelWidget

	// user can edit login params checkbox
	canUserEditLoginParamsLabel    *TLabelWidget
	canUserEditLoginParamsCheckBox *TCheckbuttonWidget

	IsDmrIdAutoIncrement       bool
	dmrIdAutoIncrementLabel    *TLabelWidget
	dmrIdAutoIncrementCheckBox *TCheckbuttonWidget

	readButton  *TButtonWidget
	writeButton *TButtonWidget

	isNeedUpdateInput bool

	readBtnClicked  bool
	writeBtnClicked bool
}

var domainMatch, _ = regexp.Compile(DomainReg)

func newAccountConfig(app *GuiApp) *accountConfigView {
	i := i18n.Get(app.Lang)
	view := &accountConfigView{
		root: app,
	}

	view.frame = TLabelframe(Txt(i.Translate(AccountConfigKey)))

	app.appConfig.Host = env.DefaultServerHost
	hostFrame := view.frame.TFrame()
	view.hostLabel = hostFrame.TLabel(Txt(i.Translate(HostKey)))
	view.hostErrorLabel = hostFrame.Label(Txt(""), Foreground("red"))
	view.hostInput = hostFrame.TEntry(
		Textvariable(env.DefaultServerHost),
		Validate("focusout"),
		Validatecommand(func(e *Event) {
			value := strings.TrimSpace(view.hostInput.Textvariable())
			var ok bool
			ip := net.ParseIP(value)
			if ip == nil {
				ok = domainMatch.MatchString(value)
			} else {
				ok = true
			}
			e.Result = strconv.FormatBool(ok)
			if ok {
				app.appConfig.Host = value
				view.hostErrorLabel.Configure(Txt(""))
			}
		}),
		Invalidcommand(func() {
			view.hostErrorLabel.Configure(Txt(i18n.Get(app.Lang).Translate(InvalidHostKey)))
			app.appConfig.Host = ""
		}),
	)

	Grid(view.hostLabel, Column(0), Row(0), Sticky("news"))
	Grid(view.hostInput, Column(1), Row(0), Sticky("news"))
	Grid(view.hostErrorLabel, Column(1), Row(1), Sticky("news"))
	GridColumnConfigure(hostFrame, 1, Minsize("200"), Weight(1))

	app.appConfig.Port = int32(env.DefaultServerPort)
	portFrame := view.frame.TFrame()
	view.portLabel = portFrame.TLabel(Txt(i.Translate(PortKey)))
	view.portErrorLabel = portFrame.Label(Txt(""), Foreground("red"))

	view.portInput = portFrame.TEntry(
		Textvariable(strconv.Itoa(env.DefaultServerPort)),
		Validate("focusout"),
		Validatecommand(func(e *Event) {
			value := strings.TrimSpace(view.portInput.Textvariable())
			var ok bool
			i, err := strconv.ParseInt(value, 10, 32)
			if err != nil {
				ok = false
			} else {
				ok = 1 <= i && i <= 65535
			}
			e.Result = strconv.FormatBool(ok)
			if ok {
				app.appConfig.Port = int32(i)
				view.portErrorLabel.Configure(Txt(""))
			}
		}),
		Invalidcommand(func() {
			view.portErrorLabel.Configure(Txt(i18n.Get(app.Lang).Translate(InvalidPortKey)))
			app.appConfig.Port = 0
		}),
	)

	GridColumnConfigure(portFrame, 1, Minsize("200"), Weight(1))
	Grid(view.portLabel, Column(0), Row(0), Sticky("news"))
	Grid(view.portInput, Column(1), Row(0), Sticky("news"))
	Grid(view.portErrorLabel, Column(1), Row(1), Sticky("news"))

	dmrIDFrame := view.frame.TFrame()
	view.dmrIDLabel = dmrIDFrame.TLabel(Txt(i.Translate(TerminalDMRIDKey)))
	view.dmrIDErrorLabel = dmrIDFrame.Label(Txt(""), Foreground("red"))

	view.dmrIDInput = dmrIDFrame.TEntry(
		Textvariable(""),
		Validate("focusout"),
		Validatecommand(view.validateDmrID),
		Invalidcommand(func() {
			if view.IsDmrIDHex {
				view.dmrIDErrorLabel.Configure(Txt(i18n.Get(app.Lang).Translate(InvalidHexDMRIDKey)))
			} else {
				view.dmrIDErrorLabel.Configure(Txt(i18n.Get(app.Lang).Translate(InvalidDecDMRIDKey)))
			}
			app.appConfig.Dmrid = ""
		}),
	)

	view.dmrIDComboBox = dmrIDFrame.TCombobox(
		Values(view.DmrIDComboboxValues()),
		State("readonly"),
		Textvariable(view.DmrIDComboboxDefaultValue()),
	)
	view.IsDmrIDHex = false

	Bind(view.dmrIDComboBox, "<<ComboboxSelected>>", Command(func() {
		base := view.dmrIDComboBox.Textvariable()
		if base == i18n.Get(app.Lang).Translate(Hexadecimalkey) {
			view.IsDmrIDHex = true
		} else {
			view.IsDmrIDHex = false
		}
		e := &Event{}
		view.validateDmrID(e)
		if e.Result == "false" {
			if view.IsDmrIDHex {
				view.dmrIDErrorLabel.Configure(Txt(i18n.Get(app.Lang).Translate(InvalidHexDMRIDKey)))
			} else {
				view.dmrIDErrorLabel.Configure(Txt(i18n.Get(app.Lang).Translate(InvalidDecDMRIDKey)))
			}
			app.appConfig.Dmrid = ""
		}
	}))

	Grid(view.dmrIDLabel, Column(0), Row(0), Sticky("news"))
	Grid(view.dmrIDInput, Column(1), Row(0), Sticky("news"))
	Grid(view.dmrIDComboBox, Column(2), Row(0), Sticky("news"))
	Grid(view.dmrIDErrorLabel, Column(1), Row(1), Columnspan(2), Sticky("news"))
	GridColumnConfigure(dmrIDFrame, 1, Minsize("200"), Weight(1))

	passwordFrame := view.frame.TFrame()
	view.passwordLabel = passwordFrame.TLabel(Txt(i.Translate(PasswordKey)))
	view.passwordErrorLabel = passwordFrame.Label(Txt(""), Foreground("red"))
	view.passwordInput = passwordFrame.TEntry(
		Textvariable(""),
		Show("*"),
		Validate("focusout"),
		Validatecommand(func(e *Event) {
			value := strings.TrimSpace(view.passwordInput.Textvariable())
			if len(value) == 0 {
				e.Result = "false"
				app.appConfig.Password = ""
				return
			}
			app.appConfig.Password = value
			view.passwordErrorLabel.Configure(Txt(""))
			e.Result = "true"
		}),
		Invalidcommand(func() {
			view.passwordErrorLabel.Configure(Txt(i18n.Get(app.Lang).Translate(PasswordIsRequiredKey)))
			app.appConfig.Password = ""
		}),
	)

	GridColumnConfigure(passwordFrame, 1, Minsize("200"), Weight(1))
	Grid(view.passwordLabel, Column(0), Row(0), Sticky("news"))
	Grid(view.passwordInput, Column(1), Row(0), Sticky("news"))
	Grid(view.passwordErrorLabel, Column(1), Row(1), Sticky("news"))

	autoIncrementFrame := view.frame.TFrame()
	view.dmrIdAutoIncrementLabel = autoIncrementFrame.TLabel(Txt(i.Translate(DmrIdIncrementAfterAccountConfigSuccess)))
	view.dmrIdAutoIncrementCheckBox = autoIncrementFrame.TCheckbutton(
		Variable("false"),
		Onvalue("true"),
		Offvalue("false"),
		Command(func() {
			parseBool, err := strconv.ParseBool(view.dmrIdAutoIncrementCheckBox.Variable())
			view.IsDmrIdAutoIncrement = parseBool
			if err != nil {
				view.root.Log("parse dmrid auto increment checkbox bool error: " + err.Error())
				return
			}
		}),
	)

	GridColumnConfigure(autoIncrementFrame, 1, Minsize("200"), Weight(1))
	Grid(view.dmrIdAutoIncrementLabel, Column(0), Row(0), Sticky("news"))
	Grid(view.dmrIdAutoIncrementCheckBox, Column(1), Row(0), Sticky("news"))

	canUserEditLoginParamsFrame := view.frame.TFrame()
	view.canUserEditLoginParamsLabel = canUserEditLoginParamsFrame.TLabel(Txt(i.Translate(CanUserEditLoginParamsKey)))
	view.canUserEditLoginParamsCheckBox = canUserEditLoginParamsFrame.TCheckbutton(
		Variable("false"),
		Onvalue("true"),
		Offvalue("false"),
		Command(func() {
			parseBool, err := strconv.ParseBool(view.canUserEditLoginParamsCheckBox.Variable())
			view.root.appConfig.CanEditLoginParam = parseBool
			if err != nil {
				view.root.Log("parse can edit login Params checkbox bool error: " + err.Error())
				return
			}
		}),
	)

	GridColumnConfigure(canUserEditLoginParamsFrame, 1, Minsize("200"), Weight(1))
	Grid(view.canUserEditLoginParamsLabel, Column(0), Row(0), Sticky("news"))
	Grid(view.canUserEditLoginParamsCheckBox, Column(1), Row(0), Sticky("news"))

	Grid(
		hostFrame,
		Column(0),
		Row(0),
		Padx("10"),
		Pady("10"),
		Sticky("news"),
	)
	Grid(
		portFrame,
		Column(0),
		Row(1),
		Padx("10"),
		Pady("10"),
		Sticky("news"),
	)

	Grid(
		dmrIDFrame,
		Column(0),
		Row(2),
		Padx("10"),
		Pady("10"),
		Sticky("news"),
	)

	Grid(
		passwordFrame,
		Column(0),
		Row(3),
		Padx("10"),
		Pady("10"),
		Sticky("news"),
	)

	Grid(
		autoIncrementFrame,
		Column(0),
		Row(4),
		Padx("10"),
		Sticky("news"),
	)
	Grid(
		canUserEditLoginParamsFrame,
		Column(0),
		Row(5),
		Padx("10"),
		Sticky("news"),
	)
	emptyRow := view.frame.TFrame()
	Grid(emptyRow, Column(0), Row(6), Sticky("news"))
	GridRowConfigure(view.frame, 6, Weight(1))

	/*** buttons ***/
	buttonFrame := view.frame.TFrame()

	view.readButton = buttonFrame.TButton(
		Txt(i.Translate(ReadKey)),
		Command(func() {
			if view.readBtnClicked {
				return
			}
			view.readBtnClicked = true
			go view.root.QueryAppConfig()
		}),
	)
	view.writeButton = buttonFrame.TButton(
		Txt(i.Translate(WriteKey)),
		Command(func() {
			if view.writeBtnClicked {
				return
			}
			view.writeBtnClicked = true
			go view.root.setAppConfig()
		}),
	)
	emptyleft := buttonFrame.TFrame()
	emptyRight := buttonFrame.TFrame()

	Grid(emptyleft, Column(0), Row(0), Sticky("news"))
	Grid(view.readButton, Column(1), Row(0), Padx("10"), Pady("10"), Sticky("news"))
	Grid(view.writeButton, Column(2), Row(0), Padx("10"), Pady("10"), Sticky("news"))
	Grid(emptyRight, Column(3), Row(0), Sticky("news"))
	Grid(buttonFrame, Column(0), Row(7), Sticky("news"))
	GridColumnConfigure(buttonFrame, 0, Weight(1))
	GridColumnConfigure(buttonFrame, 3, Weight(1))

	GridColumnConfigure(view.frame, 0, Weight(1))

	return view
}

func (s *accountConfigView) validateDmrID(e *Event) {
	defer func() {
		if e.Result == "true" {
			s.dmrIDErrorLabel.Configure(Txt(""))
		}
	}()

	e.Result = "false"
	value := strings.TrimSpace(s.dmrIDInput.Textvariable())

	err := s.parseDmrID(value)
	if err == nil {
		e.Result = "true"
	}
}

func (s *accountConfigView) parseDmrID(value string) error {
	if !s.IsDmrIDHex {
		// 尝试以十进制解析
		u, err := strconv.ParseInt(value, 10, 32)
		if err == nil && u > 0 {
			s.root.appConfig.Dmrid = apputil.Dmrid2Hex(uint32(u))
			return nil
		}
		return errors.New("cannot parse dec dmrID:" + value)
	}

	// 尝试以十六进制解析
	u, err := strconv.ParseUint(value, 16, 32)
	if err == nil && u > 0 {
		s.root.appConfig.Dmrid = apputil.Dmrid2Hex(uint32(u))
		return nil
	}

	return errors.New("cannot parse hex dmrID:" + value)
}

func (s *accountConfigView) DmrIDComboboxValues() string {
	i := i18n.Get(s.root.Lang)
	return i.Translate(DecimalKey) + " " + i.Translate(Hexadecimalkey)
}

func (s *accountConfigView) DmrIDComboboxDefaultValue() string {
	return i18n.Get(s.root.Lang).Translate(DecimalKey)
}

func (s *accountConfigView) render() {
	i := i18n.Get(s.root.Lang)
	s.frame.Configure(Txt(i.Translate(AccountConfigKey)))
	s.hostLabel.Configure(Txt(i.Translate(HostKey)))
	s.hostErrorLabel.Configure(Txt(""))
	s.portLabel.Configure(Txt(i.Translate(PortKey)))
	s.portErrorLabel.Configure(Txt(""))
	s.dmrIDLabel.Configure(Txt(i.Translate(TerminalDMRIDKey)))
	s.dmrIDErrorLabel.Configure(Txt(""))
	s.passwordLabel.Configure(Txt(i.Translate(PasswordKey)))
	s.passwordErrorLabel.Configure(Txt(""))
	s.canUserEditLoginParamsLabel.Configure(Txt(i.Translate(CanUserEditLoginParamsKey)))
	s.dmrIdAutoIncrementLabel.Configure(Txt(i.Translate(DmrIdIncrementAfterAccountConfigSuccess)))
	s.readButton.Configure(Txt(i.Translate(ReadKey)))
	s.writeButton.Configure(Txt(i.Translate(WriteKey)))

	s.dmrIDComboBox.Configure(
		Values(s.DmrIDComboboxValues()),
		State("readonly"),
	)

	if s.IsDmrIDHex {
		s.dmrIDComboBox.Configure(Textvariable(i.Translate(Hexadecimalkey)))
	} else {
		s.dmrIDComboBox.Configure(Textvariable(i.Translate(DecimalKey)))
	}

	if s.isNeedUpdateInput {
		config := s.root.appConfig
		s.hostInput.Configure(Textvariable(config.Host))
		s.portInput.Configure(Textvariable(strconv.Itoa(int(config.Port))))
		s.dmrIDInput.Configure(Textvariable(config.Dmrid))
		s.dmrIDComboBox.Configure(Textvariable(i.Translate(Hexadecimalkey)))
		s.IsDmrIDHex = true
		s.passwordInput.Configure(Textvariable(config.Password))
		s.canUserEditLoginParamsCheckBox.Configure(Variable(strconv.FormatBool(config.CanEditLoginParam)))
		s.isNeedUpdateInput = false
	}
}
