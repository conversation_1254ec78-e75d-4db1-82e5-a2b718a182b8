package gui

import (
	"appconfig/apputil"
	"appconfig/env"
	"appconfig/i18n"
	"strings"

	"github.com/hashicorp/go-version"
	. "modernc.org/tk9.0"
)

type apkManageView struct {
	root            *GuiApp
	frame           *TLabelframeWidget
	pathLabel       *TLabelWidget
	pathInput       *TEntryWidget
	getFileBtn      *ButtonWidget
	apkPath         string
	apkVersionLabel *TLabelWidget
	apkVersion      *version.Version

	// uninstallBtn        *TButtonWidget
	uninstallBtnClicked bool
	installBtn          *TButtonWidget
	installBtnClicked   bool
}

func newApkManage(app *GuiApp) *apkManageView {
	i := i18n.Get(app.Lang)

	view := &apkManageView{
		root: app,
	}

	view.frame = TLabelframe(Txt(i.Translate(UpgradeApkKey)))

	defaultApkPath := env.DefaultApkPath
	fileChooseFrame := view.frame.TFrame()
	view.pathLabel = fileChooseFrame.TLabel(Txt(i.Translate(ApkFileKey)))
	// 设置默认值
	view.apkPath = defaultApkPath
	view.pathInput = fileChooseFrame.TEntry(
		Textvariable(defaultApkPath),
		Placeholder("file path"),
		Validate("focusout"),
		Validatecommand(func(e *Event) {
			view.apkPath = strings.TrimSpace(view.pathInput.Textvariable())
			view.parserApkVersion(view.apkPath)
			e.Result = "true"
		}),
	)

	view.getFileBtn = fileChooseFrame.Button(
		Width("5"),
		Txt(i.Translate(OpenKey)),
		Command(func() {
			s := GetOpenFile(
				Parent(App),
				Title(i18n.Get(view.root.Lang).Translate(OpenApkFileKey)),
				Defaultextension(".apk"),
				Multiple(false),
				Filetypes([]FileType{
					{TypeName: "APK", Extensions: []string{".apk"}},
					{TypeName: "Any file", Extensions: []string{".*"}},
				}))
			switch len(s) {
			case 0:
				// nop
			case 1:
				fn := s[0]
				if fn == "" {
					break
				}

				view.pathInput.Configure(Textvariable(fn))
				view.apkPath = fn
				view.parserApkVersion(fn)
			default:
				MessageBox(
					Icon("error"),
					Title("Error"),
					Msg(i.Translate(OnlyOneFileKey)),
					Parent(App), Type("ok"),
				)
			}
		}),
	)

	Grid(view.pathLabel, Column(0), Row(0), Sticky("news"))
	Grid(view.pathInput, Column(1), Row(0), Sticky("news"))
	Grid(view.getFileBtn, Column(2), Row(0), Sticky("news"))
	GridColumnConfigure(fileChooseFrame, 1, Minsize("200"), Weight(1))

	versionName := ""
	view.apkVersionLabel = fileChooseFrame.TLabel(Txt(i.Translate(ApkVersionKey, versionName)))
	view.parserApkVersion(defaultApkPath)
	Grid(view.apkVersionLabel, Column(0), Row(1), Columnspan(2), Sticky("news"))
	GridRowConfigure(fileChooseFrame, 1, Weight(1))

	buttonFrame := view.frame.TFrame()
	/*
		view.uninstallBtn = buttonFrame.TButton(
			Txt(i.Translate(UninstallKey)),
			Command(func() {
				if view.uninstallBtnClicked {
					return
				}
				view.uninstallBtnClicked = true
				view.root.onUninstallApp()
			}),
		)
	*/
	view.installBtn = buttonFrame.TButton(
		Txt(i.Translate(installKey)),
		Command(view.onInstallBtnClicked),
	)
	emptyleft := buttonFrame.TFrame()
	emptyRight := buttonFrame.TFrame()

	Grid(emptyleft, Column(0), Row(0), Sticky("news"))
	// Grid(view.uninstallBtn, Column(1), Row(0), Padx("10"), Sticky("news"))
	Grid(view.installBtn, Column(2), Row(0), Padx("10"), Sticky("news"))
	Grid(emptyRight, Column(3), Row(0), Sticky("news"))
	Grid(buttonFrame, Column(0), Row(5), Sticky("news"))
	GridColumnConfigure(buttonFrame, 0, Weight(1))
	GridColumnConfigure(buttonFrame, 3, Weight(1))

	Grid(
		fileChooseFrame,
		Column(0),
		Row(0),
		Padx("10"),
		Pady("10"),
		Sticky("news"),
	)

	Grid(
		buttonFrame,
		Column(0),
		Row(1),
		Padx("10"),
		Pady("10"),
		Sticky("news"),
	)

	GridColumnConfigure(view.frame, 0, Weight(1))

	return view
}

func (s *apkManageView) parserApkVersion(apkPath string) {
	i := i18n.Get(s.root.Lang)
	version, err := apputil.GetApkFileVersion(apkPath)
	versionName := "unknown"
	if err == nil {
		s.apkVersion = version
		versionName = s.apkVersion.String()
	} else {
		s.apkVersion = nil
	}
	s.apkVersionLabel.Configure(Txt(i.Translate(ApkVersionKey, versionName)))
	s.root.refreshCh <- struct{}{}
}

func (s *apkManageView) onInstallBtnClicked() {
	if s.installBtnClicked {
		return
	}
	s.installBtnClicked = true
	i := i18n.Get(s.root.Lang)
	if s.apkVersion == nil {
		value := MessageBox(
			Icon("warning"),
			Title(i.Translate(WarningKey)),
			Msg(i.Translate(ApkFileVersionIsUnknownKey)),
			Parent(App), Type("yesno"),
		)
		if value == "yes" {
			go s.root.onInstallApk(s.apkPath)
		} else {
			s.installBtnClicked = false
		}
		return
	}

	appVersion, err := s.root.getAppVersion()
	if err != nil {
		go s.root.onInstallApk(s.apkPath)
		return
	}

	compared := appVersion.Compare(s.apkVersion)
	if compared == 0 {
		// 版本号相同
		value := MessageBox(
			Icon("warning"),
			Title(i.Translate(WarningKey)),
			Msg(i.Translate(AppAndFileVersionIsSameKey)),
			Parent(App), Type("yesno"),
		)
		if value == "yes" {
			go s.root.onInstallApk(s.apkPath)
		} else {
			s.installBtnClicked = false
		}
		return
	}

	if compared > 0 {
		value := MessageBox(
			Icon("warning"),
			Title(i.Translate(WarningKey)),
			Msg(i.Translate(ApkFileVersionIsSmallerThanAppKey)),
			Parent(App), Type("yesno"),
		)
		if value == "yes" {
			go s.root.onInstallApk(s.apkPath)
		} else {
			s.installBtnClicked = false
		}
		return
	}

	go s.root.onInstallApk(s.apkPath)
}
func (s *apkManageView) render() {
	i := i18n.Get(s.root.Lang)
	s.frame.Configure(Txt(i.Translate(UpgradeApkKey)))
	s.pathLabel.Configure(Txt(i.Translate(ApkFileKey)))

	versionName := "unknown"
	if s.apkVersion != nil {
		versionName = s.apkVersion.String()
	}
	s.apkVersionLabel.Configure(Txt(i.Translate(ApkVersionKey, versionName)))

	s.getFileBtn.Configure(Txt(i.Translate(OpenKey)))
	// s.uninstallBtn.Configure(Txt(i.Translate(UninstallKey)))
	s.installBtn.Configure(Txt(i.Translate(installKey)))
}
