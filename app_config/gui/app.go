package gui

import (
	"bytes"
	"errors"
	"io"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"appconfig/app_proto"
	"appconfig/apputil"
	"appconfig/env"
	"appconfig/i18n"

	"github.com/Unknwon/goconfig"
	"github.com/hashicorp/go-version"
	"github.com/jeandeaual/go-locale"
	. "modernc.org/tk9.0"

	"appconfig/adb"
)

const (
	// 最大日志行数
	logRotate = 1000
)

// nolint
const DomainReg = `^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$`

type GuiApp struct {
	NavView           *navMenuView
	DeviceFindView    *deviceFindView
	AccountConfigView *accountConfigView
	BuildInfoView     *buildInfoView
	LogTextView       *TextWidget
	ApkManageView     *apkManageView

	sync.Mutex

	logs []string

	// 语言
	Lang string

	refreshCh chan struct{}

	// 选中的设备,device id
	selected       string
	isAppConnected atomic.Bool
	// 选中设备的编译信息
	buildInfo *app_proto.AppBuildInfo
	appConfig *app_proto.AppConfig
}

func getInitLang() string {
	lang := env.DefaultLanguage
	i := i18n.Get(env.DefaultLanguage)
	if i == nil {
		var err error
		lang, err = locale.GetLocale()
		if err != nil {
			lang = i18n.ZH_CN
		}
	}
	return lang
}

// gui界面主函数，需在main中最后一个调用
func NewApp() *GuiApp {
	lang := getInitLang()

	a := &GuiApp{
		Lang:           lang,
		refreshCh:      make(chan struct{}, 10),
		appConfig:      &app_proto.AppConfig{},
		isAppConnected: atomic.Bool{},
	}

	App.WmTitle(i18n.Get(a.Lang).Translate(AppTitleKey) + " - " + env.Version)

	a.CreateDeviceFindView()
	a.CreateBuildInfoView()
	a.CreateAccountConfigView()
	a.CreateLogView()
	a.CreateApkManageView()

	err := adb.GAdbManager.FindAdb()
	// 没有查找到adb，则提示用户手动设置
	if err != nil {
		value := MessageBox(
			Icon("warning"),
			Title(i18n.Get(a.Lang).Translate(WarningKey)),
			Msg(i18n.Get(a.Lang).Translate(AdbNotFoundKey)),
			Detail(i18n.Get(a.Lang).Translate(OpenAdbProgramByYourself)),
			Parent(App), Type("yesno"),
		)
		if value == "no" {
			Destroy(App)
			os.Exit(1)
		}
		a.onChangeAdbPath()
	}

	go a.findAdbDevice()

	_, err = NewTicker(100*time.Millisecond, a.RenderTick)
	if err != nil {
		a.Gpanic(err)
	}

	App.Configure(Mnu(a.CreatMenu()), Padx("10"), Pady("10")).Center()
	WmProtocol(App, "WM_DELETE_WINDOW", a.ExitHandler)

	return a
}

func (a *GuiApp) ExitHandler() {
	Destroy(App)

	// 检查config.ini是否存在
	_, err := os.Stat("config.ini")
	if err != nil {
		_, err = os.Create("config.ini")
		if err != nil {
			log.Println("exit GuiApp: create config file err:", err)
			return
		}
	}

	config, err := goconfig.LoadConfigFile("config.ini")
	if err != nil {
		log.Println("exit GuiApp: load config file err:", err)
		return
	}

	config.SetValue("default", "adb_path", adb.GAdbManager.Path)
	config.SetValue("default", "language", a.Lang)
	config.SetValue("default", "server_host", a.AccountConfigView.hostInput.Textvariable())
	config.SetValue("default", "server_port", a.AccountConfigView.portInput.Textvariable())
	config.SetValue("default", "apk_path", a.ApkManageView.pathInput.Textvariable())

	_ = goconfig.SaveConfigFile(config, "config.ini")
}

// 捕获异常,退出
func (a *GuiApp) Gpanic(err error) {
	i := i18n.Get(a.Lang)
	if err != nil && MessageBox(
		Icon("error"),
		Title(i.Translate(ErrorKey)),
		Msg(i.Translate(ProgramErrorKey)),
		Detail(err.Error()),
		Parent(App), Type("ok"),
	) == "ok" {
		a.ExitHandler()
		os.Exit(1)
	}
}

func (a *GuiApp) Refresh() {
	a.refreshCh <- struct{}{}
}

// RenderTick,回调函数，传入Tk9.0，用于定时刷新界面
func (a *GuiApp) RenderTick() {
	// 刷新ui
	select {
	case <-a.refreshCh:
		a.render()
		if a.DeviceFindView.needUpdateSelectedDevice {
			a.DeviceFindView.RefreshList()
		}
	default:
	}

	if len(a.logs) == 0 {
		return
	}

	var logs []string
	a.Lock()
	logs, a.logs = a.logs, nil
	a.Unlock()
	for _, v := range logs {
		a.log2View(v)
	}
}

// 加载app界面
func (a *GuiApp) render() {
	// 重新加载标题
	App.WmTitle(i18n.Get(a.Lang).Translate(AppTitleKey) + " - " + env.Version)
	// 重新加载菜单
	if a.NavView != nil {
		a.NavView.render()
	}
	// 重新加载设备发现
	if a.DeviceFindView != nil {
		a.DeviceFindView.render()
	}
	if a.BuildInfoView != nil {
		a.BuildInfoView.render()
	}
	if a.AccountConfigView != nil {
		a.AccountConfigView.render()
	}
	if a.ApkManageView != nil {
		a.ApkManageView.render()
	}
}

// 菜单ui
func (a *GuiApp) CreatMenu() (r *MenuWidget) {
	n := newNavMenu(a)
	a.NavView = n

	return n.top
}

// 设备发现ui
func (a *GuiApp) CreateDeviceFindView() {
	d := newDeviceFind(a)
	a.DeviceFindView = d

	Grid(
		d.frame,
		Column(0),
		Row(0),
		Pady("10"),
		Sticky("news"),
	)
	GridColumnConfigure(App, 0, Minsize("500"), Weight(1))
	GridRowConfigure(App, 0, Minsize("50"), Weight(0))
}

func (a *GuiApp) CreateAccountConfigView() {
	si := newAccountConfig(a)
	a.AccountConfigView = si

	Grid(
		si.frame,
		Column(0),
		Row(2),
		Sticky("news"),
	)
	GridRowConfigure(App, 2, Weight(1))
}

func (a *GuiApp) CreateBuildInfoView() {
	vi := newBuildInfo(a)
	a.BuildInfoView = vi

	Grid(
		vi.frame,
		Column(0),
		Row(1),
		Pady("10"),
		Sticky("news"),
	)
}

func (a *GuiApp) CreateLogView() {
	logFrame := TFrame()
	a.LogTextView = logFrame.Text()
	a.LogTextView.Configure(State("normal"))
	scroll := logFrame.TScrollbar(Command(func(e *Event) { e.Yview(a.LogTextView) }))
	Grid(a.LogTextView, Row(0), Column(0), Sticky("news"))
	Grid(scroll, Row(0), Column(1), Sticky("nes"))
	GridRowConfigure(logFrame, 0, Weight(1))
	GridColumnConfigure(logFrame, 0, Weight(1))

	Grid(
		logFrame,
		Column(1),
		Row(0),
		Rowspan(6),
		Padx("10"),
		Pady("10"),
		Sticky("news"),
	)
	GridColumnConfigure(App, 1, Weight(1))
}

func (a *GuiApp) CreateApkManageView() {
	si := newApkManage(a)
	a.ApkManageView = si

	Grid(
		si.frame,
		Column(0),
		Row(4),
		Pady("10"),
		Sticky("news"),
	)
	// GridRowConfigure(App, 3, Weight(1))
}

func (a *GuiApp) log2View(msg string) {
	a.LogTextView.Insert("end", msg)
	lines := a.LogTextView.Count(Lines(), "1.0", "end")
	if len(lines) == 1 {
		if n, err := strconv.ParseInt(lines[0], 10, 32); err == nil {
			if n > logRotate {
				f := strconv.FormatFloat(float64(n-logRotate), 'f', 1, 64)
				a.LogTextView.Delete("1.0", f)
			}
		}
	}
	a.LogTextView.See("end")
}

func (a *GuiApp) Log(msg string) {
	t := time.Now().Format("2006-01-02 15:04:05.000")
	a.logs = append(a.logs, t+" "+msg+"\n")
}

func (a *GuiApp) findAdbDevice() {
	defer func() {
		a.Lock()
		a.DeviceFindView.needUpdateSelectedDevice = true
		a.DeviceFindView.isFindingDevice = false
		a.Unlock()
		if len(adb.GAdbManager.DeviceMap) == 0 && a.buildInfo != nil {
			a.buildInfo = nil
		}
		a.Refresh()
	}()
	i := i18n.Get(a.Lang)
	a.Log(i.Translate(TryFindingDeviceKey))

	if adb.GAdbManager.Path == "" {
		err := adb.GAdbManager.FindAdb()
		if err != nil {
			a.Log(i.Translate(FindAdbErrorKey, err.Error()))
			return
		}
	}

	err := adb.GAdbManager.FindDevices()
	if err != nil {
		a.Log(i.Translate(FindDeviceErrorKey, err.Error()))
		return
	}
	a.Log(i.Translate(FindDeviceSuccessKey))
}

// 选中设备的回调
func (a *GuiApp) onSelected() {
	// destroy old device
	if a.selected != "" {
		oldDevice := adb.GAdbManager.DeviceMap[a.selected]
		if oldDevice != nil {
			_ = oldDevice.CloseForwardPort(oldDevice.Port)
		}
	}

	a.selected = parseSelected2DeviceID(a.DeviceFindView.list.Textvariable())
	a.Log(i18n.Get(a.Lang).Translate(SelectedDeviceKey, a.selected))
	a.tryConnectDevice()
}

func (a *GuiApp) forwardAppPort() error {
	device := adb.GAdbManager.DeviceMap[a.selected]
	if device == nil {
		return errors.New("no device selected")
	}

	device.Port = 0

	port := device.QueryForwardPord()
	if port != 0 {
		_ = device.CloseForwardPort(port)
	}
	c := &http.Client{
		Timeout: time.Second * 5,
	}

	if env.IsDebug && env.ProxyHttpHost != "127.0.0.1" {
		// in debug mode, no need to forward port,use env port
		port = env.ProxyHttpPort
		for i := 0; i < 3; i++ {
			hostport := net.JoinHostPort(env.ProxyHttpHost, strconv.Itoa(port))
			u := url.URL{Scheme: "http", Host: hostport, Path: "/"}
			resp, _ := c.Get(u.String())
			if resp != nil {
				// 当前端口是可用的
				device.Port = port
				break
			}
			port++
		}
		if device.Port == 0 {
			return errors.New("cannot connect to GuiApp proxy")
		}
		return nil
	}

	if port == 0 {
		p, err := apputil.GetFreePort()
		if err != nil {
			return errors.New("no free port")
		}
		port = p
	}

	remotePort := env.ProxyHttpPort
	for i := 0; i < 3; i++ {
		_ = device.ForwardPort(port, remotePort)
		hostport := net.JoinHostPort(env.ProxyHttpHost, strconv.Itoa(port))
		u := url.URL{Scheme: "http", Host: hostport}
		resp, _ := c.Get(u.String())
		if resp != nil {
			// 当前端口是可用的
			device.Port = port
			break
		}
		_ = device.CloseForwardPort(port)
		remotePort++
	}

	if device.Port == 0 {
		return errors.New("cannot connect to GuiApp proxy")
	}
	return nil
}

func (a *GuiApp) tryConnectDevice() {
	defer a.Refresh()

	a.buildInfo = nil
	i := i18n.Get(a.Lang)
	a.Log(i.Translate(TryConnectDeviceKey))

	device := adb.GAdbManager.DeviceMap[a.selected]

	a.isAppConnected.Store(false)

	if device == nil {
		errorMsg := i.Translate(NoDeviceSelectedKey)
		a.Log(i.Translate(
			ConnectDeviceErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}

	isApKInstalled := a.isApKInstalled()
	if !isApKInstalled {
		apkPath := env.DefaultApkPath
		if a.ApkManageView.apkPath != "" {
			apkPath = a.ApkManageView.apkPath
		}
		_, err := os.Stat(apkPath)
		if err == nil {
			value := MessageBox(
				Icon("info"),
				Title(i18n.Get(a.Lang).Translate(WarningKey)),
				Msg(i18n.Get(a.Lang).Translate(AskIfInstallApkKey)),
				Detail(i18n.Get(a.Lang).Translate(ApkPathKey, apkPath)),
				Parent(App), Type("yesno"),
			)
			if value == "yes" {
				go a.onInstallApk(apkPath)
			}
		}
		return
	}

	appVersion, err := a.getAppVersion()
	if err == nil {
		apkPath := env.DefaultApkPath
		if a.ApkManageView.apkPath != "" {
			apkPath = a.ApkManageView.apkPath
		}
		_, err = os.Stat(apkPath)
		if err == nil {
			apkVersion, err := apputil.GetApkFileVersion(apkPath)
			if err == nil {
				if apkVersion.Compare(appVersion) > 0 {
					value := MessageBox(
						Icon("info"),
						Title(i18n.Get(a.Lang).Translate(WarningKey)),
						Msg(i18n.Get(a.Lang).Translate(AppVersionIsSmallerThanApkKey)),
						Detail(i18n.Get(a.Lang).Translate(ApkPathKey, apkPath)),
						Parent(App), Type("yesno"),
					)
					if value == "yes" {
						go a.onInstallApk(apkPath)
						return
					}
				}
			}
		}
	}

	err = a.grantWriteSecureSettings()
	if err != nil {
		log.Printf("grantWriteSecureSettings error: %v", err)
	}

	isAppRunning := a.isAppRunning()
	if !isAppRunning {
		_, _ = device.RunCmd([]string{"shell", "am", "start", env.ApkPackageName}, 30*time.Second)
	}

	err = a.forwardAppPort()
	if err != nil {
		errorMsg := err.Error()
		a.Log(i.Translate(ConnectDeviceErrorKey, a.selected, errorMsg))
		return
	}

	a.Log(i.Translate(ConnectDeviceSuccessKey, a.selected))
	a.isAppConnected.Store(true)
	a.QueryingBuildInfo()
}

// 查询app构建信息
func (a *GuiApp) QueryingBuildInfo() {
	defer func() {
		a.BuildInfoView.isQuerying = false
		a.refreshCh <- struct{}{}
	}()
	i := i18n.Get(a.Lang)
	if !a.isAppConnected.Load() {
		errorMsg := i.Translate(DeviceNotConnectedKey)
		a.Log(i.Translate(QueryBuildInfoErrorKey, a.selected, errorMsg))
		return
	}

	a.Log(i.Translate(TryQueryBuildInfoKey, a.selected))
	a.buildInfo = nil
	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		errorMsg := i.Translate(NoDeviceSelectedKey)
		a.Log(i.Translate(QueryBuildInfoErrorKey, a.selected, errorMsg))
		return
	}
	hostport := net.JoinHostPort(env.ProxyHttpHost, strconv.Itoa(currentDevice.Port))
	u := url.URL{Scheme: "http", Host: hostport, Path: "/appBuildInfo"}

	c := &http.Client{
		Timeout: time.Second * 10,
	}

	resp, err := c.Get(u.String())
	if err != nil {
		errorMsg := i.Translate(SendRequestFailKey, err.Error())
		a.Log(i.Translate(
			QueryBuildInfoErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}

	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		errorMsg := resp.Status + " " + string(body)
		a.Log(i.Translate(
			QueryBuildInfoErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		errorMsg := "read body err: " + err.Error()
		a.Log(i.Translate(
			QueryBuildInfoErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}

	info := &app_proto.AppBuildInfo{}
	err = info.Unmarshal(body)
	if err != nil {
		errorMsg := "unmarshal err: " + err.Error()
		a.Log(i.Translate(
			QueryBuildInfoErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}
	a.buildInfo = info
	a.Log(i.Translate(QueryBuildInfoSuccessKey, a.selected))
}

func (a *GuiApp) QueryAppConfig() {
	defer func() {
		a.Lock()
		a.AccountConfigView.readBtnClicked = false
		a.Unlock()
		a.refreshCh <- struct{}{}
	}()

	i := i18n.Get(a.Lang)
	if !a.isAppConnected.Load() {
		errorMsg := i.Translate(DeviceNotConnectedKey)
		a.Log(i.Translate(QueryAppConfigErrorKey, a.selected, errorMsg))
		return
	}

	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		errorMsg := i.Translate(NoDeviceSelectedKey)
		a.Log(i.Translate(QueryAppConfigErrorKey, a.selected, errorMsg))
		return
	}
	hostport := net.JoinHostPort(env.ProxyHttpHost, strconv.Itoa(currentDevice.Port))
	u := url.URL{Scheme: "http", Host: hostport, Path: "/appConfig"}

	c := &http.Client{
		Timeout: time.Second * 15,
	}
	resp, err := c.Get(u.String())
	if err != nil {
		errorMsg := i.Translate(SendRequestFailKey, err.Error())
		a.Log(i.Translate(
			QueryAppConfigErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}

	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		errorMsg := resp.Status + " " + string(body)
		a.Log(i.Translate(
			QueryAppConfigErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		errorMsg := "read body err: " + err.Error()
		a.Log(i.Translate(
			QueryAppConfigErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}

	config := &app_proto.AppConfig{}
	err = config.Unmarshal(body)
	if err != nil {
		errorMsg := "unmarshal err: " + err.Error()
		a.Log(i.Translate(
			QueryAppConfigErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}

	a.appConfig = config
	a.AccountConfigView.isNeedUpdateInput = true
	a.Log(i.Translate(QueryAppConfigSuccessKey, a.selected))
}

func (a *GuiApp) setAppConfig() {
	defer func() {
		a.Lock()
		a.AccountConfigView.writeBtnClicked = false
		a.Unlock()
	}()

	i := i18n.Get(a.Lang)
	if !a.isAppConnected.Load() {
		errorMsg := i.Translate(DeviceNotConnectedKey)
		a.Log(i.Translate(SetAppConfigErrorKey, a.selected, errorMsg))
		return
	}

	if a.appConfig == nil {
		errorMsg := "no account config"
		a.Log(i.Translate(SetAppConfigErrorKey, a.selected, errorMsg))
		return
	}

	if a.appConfig.Host == "" || a.appConfig.Port == 0 || a.appConfig.Dmrid == "" || a.appConfig.Password == "" {
		errorMsg := "invalid account config"
		a.Log(i.Translate(SetAppConfigErrorKey, a.selected, errorMsg))
		return
	}

	a.Log(i.Translate(TrySetAppConfigKey, a.selected))

	dAtA, err := a.appConfig.Marshal()
	if err != nil {
		errorMsg := "marshal config err: " + err.Error()
		a.Log(i.Translate(SetAppConfigErrorKey, a.selected, errorMsg))
		return
	}

	c := &http.Client{
		Timeout: time.Second * 15,
	}

	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		errorMsg := i.Translate(NoDeviceSelectedKey)
		a.Log(i.Translate(SetAppConfigErrorKey, a.selected, errorMsg))
		return
	}
	hostport := net.JoinHostPort(env.ProxyHttpHost, strconv.Itoa(currentDevice.Port))
	u := url.URL{Scheme: "http", Host: hostport, Path: "/appConfig"}
	resp, err := c.Post(u.String(),
		"application/octet-stream", bytes.NewReader(dAtA))
	if err != nil {
		errorMsg := i.Translate(SendRequestFailKey, err.Error())
		a.Log(i.Translate(
			SetAppConfigErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}

	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		errorMsg := resp.Status + " " + string(body)
		a.Log(i.Translate(
			SetAppConfigErrorKey,
			a.selected,
			errorMsg,
		))
		return
	}

	a.Log(i.Translate(SetAppConfigSuccessKey, a.selected))

	if a.AccountConfigView.IsDmrIdAutoIncrement {
		dmrid2Uint32 := apputil.HexDmrid2Uint32(a.appConfig.Dmrid)
		a.appConfig.Dmrid = apputil.Dmrid2Hex(dmrid2Uint32 + 1)
		a.AccountConfigView.isNeedUpdateInput = true
		a.refreshCh <- struct{}{}
	}
}

func (a *GuiApp) installApkByIntent(device *adb.Device, path string) error {
	i := i18n.Get(a.Lang)
	a.Log(i.Translate(PushingApkKey))

	fileName := filepath.Base(path)
	_, err := device.RunCmd(
		[]string{"push", path, "/data/local/tmp/"},
		3*time.Minute)
	if err != nil {
		a.Log(i.Translate(PushApkErrorKey, err.Error()))
		return err
	}

	a.Log(i.Translate(PushApkSuccessKey, fileName))

	_, err = device.RunCmd([]string{
		"shell", " am", "broadcast", "-a", "unipro.install.pack", "-e", "package_path",
		"/data/local/tmp/" + fileName, "-e", "package_name", env.ApkPackageName,
	}, 3*time.Minute)
	if err != nil {
		return err
	}

	if !a.isApKInstalled() {
		return errors.New("unknown error")
	}
	return nil
}

func (a *GuiApp) installApkByShell(device *adb.Device, path string) error {
	_, err := device.RunCmd([]string{
		"install", "-r", path,
	}, 3*time.Minute)
	if err != nil {
		return err
	}

	if !a.isApKInstalled() {
		return errors.New("unknown error")
	}
	return nil
}

func (a *GuiApp) onInstallApk(apkPath string) {
	defer func() {
		a.Lock()
		a.ApkManageView.installBtnClicked = false
		a.Unlock()
		a.Refresh()
	}()

	// 防止重复点击
	a.ApkManageView.installBtnClicked = true

	i := i18n.Get(a.Lang)
	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		errorMsg := i.Translate(NoDeviceSelectedKey)
		a.Log(i.Translate(InstallApkErrorKey, a.selected, errorMsg))
		return
	}

	path := strings.TrimSpace(apkPath)
	if apkPath == "" {
		errorMsg := i.Translate(NoApkFileSelectedKey)
		a.Log(i.Translate(InstallApkErrorKey, a.selected, errorMsg))
		return
	}

	_, err := os.Stat(path)
	if err != nil {
		a.Log(i.Translate(InstallApkErrorKey, a.selected, "file not exist"))
		return
	}

	_, _ = currentDevice.RunCmd([]string{
		"shell", "am", "broadcast", "-a",
		"unipro.install.enable",
	}, 30*time.Second)

	a.Log(i.Translate(TryInstallApkKey, a.selected, path))

	err = a.installApkByIntent(currentDevice, path)
	if err != nil {
		errorMsg := "install apk error: " + err.Error()
		a.Log(i.Translate(InstallApkErrorKey, a.selected, errorMsg))
		a.Log(i.Translate(ReTryInstallApkKey))
		err = a.installApkByShell(currentDevice, path)
		if err != nil {
			errorMsg := "install apk error: " + err.Error()
			a.Log(i.Translate(InstallApkErrorKey, a.selected, errorMsg))
			return
		}
	}

	fileName := filepath.Base(path)
	_, _ = currentDevice.RunCmd([]string{"shell", "rm", "-f", "/data/local/tmp/" + fileName}, 30*time.Second)
	_ = a.grantWriteSecureSettings()

	// 重启app，保证app打开无障碍服务
	_, _ = currentDevice.RunCmd([]string{"shell", "am", "force-stop", env.ApkPackageName}, 30*time.Second)
	_, _ = currentDevice.RunCmd([]string{
		"shell",
		"am",
		"start",
		"-n",
		env.ApkPackageName + "/.LauncherActivity",
		"-a",
		"android.intent.action.MAIN",
		"-c",
		"android.intent.category.LAUNCHER",
		"-f",
		"0x10008000",
	}, 30*time.Second)

	// check app version
	appVersion, err := a.queryAppVersionByAdb()
	if err != nil {
		errorMsg := "get app version fail:" + err.Error()
		a.Log(i.Translate(InstallApkErrorKey, a.selected, errorMsg))
		return
	}
	if a.ApkManageView.apkVersion != nil {
		compare := a.ApkManageView.apkVersion.Compare(appVersion)
		if compare != 0 {
			errorMsg := "apk version is " + a.ApkManageView.apkVersion.String() + ", while current installed app version is " + appVersion.String()
			a.Log(i.Translate(InstallApkErrorKey, a.selected, errorMsg))
			return
		}
	} else {
		a.Log(i.Translate(NotCheckUnknownApkVersionKey))
	}

	a.Log(i.Translate(InstallApkSuccessKey, a.selected))
	// 重新连接设备
	a.Log(i.Translate(TryConnectDeviceKey))

	// try to connect device 3 times
	a.isAppConnected.Store(false)
	for j := 0; j < 3; j++ {
		err = a.forwardAppPort()
		if err == nil {
			a.isAppConnected.Store(true)
			break
		}

		time.Sleep(3 * time.Second)
	}

	if !a.isAppConnected.Load() {
		errorMsg := "try connect device 3 times, but failed, error: " + err.Error()
		a.Log(i.Translate(ConnectDeviceErrorKey, a.selected, errorMsg))
		return
	}

	a.QueryingBuildInfo()
	a.Log(i.Translate(ConnectDeviceSuccessKey, a.selected))
}

func (a *GuiApp) onUninstallApp() {
	defer func() {
		a.ApkManageView.uninstallBtnClicked = false
	}()

	i := i18n.Get(a.Lang)
	if !a.isAppConnected.Load() {
		errorMsg := i.Translate(DeviceNotConnectedKey)
		a.Log(i.Translate(UninstallAppErrorKey, a.selected, errorMsg))
		return
	}

	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		errorMsg := i.Translate(NoDeviceSelectedKey)
		a.Log(i.Translate(UninstallAppErrorKey, a.selected, errorMsg))
		return
	}

	a.Log(i.Translate(TryUninstallAppKey, a.selected))

	status, err := currentDevice.RunCmd(
		[]string{"uninstall", env.ApkPackageName},
		1*time.Minute,
	)
	if err != nil {
		errorMsg := "adb cmd uninstall error: " + err.Error()
		a.Log(i.Translate(UninstallAppErrorKey, errorMsg))
		return
	}

	for _, line := range status.Stdout {
		if strings.Contains(line, "Success") {
			a.Log(i.Translate(UninstallAppSuccessKey, a.selected))
			return
		}
		if strings.Contains(line, "Failure") {
			errorMsg := "adb cmd uninstall error: failure"
			a.Log(i.Translate(UninstallAppErrorKey, a.selected, errorMsg))
			return
		}
	}
}

func (a *GuiApp) onChangeAdbPath() {
	i := i18n.Get(a.Lang)
	s := GetOpenFile(
		Parent(App),
		Title(i18n.Get(a.Lang).Translate(OpenAdbProgramKey)),
		Multiple(false),
		Filetypes([]FileType{
			{TypeName: "Any file", Extensions: []string{"*"}},
		}))

	switch len(s) {
	case 0:
		// nop
	case 1:
		fn := s[0]
		if fn == "" {
			break
		}
		err := adb.GAdbManager.ChangeAdbPath(fn)
		if err != nil {
			a.Log(i.Translate(ChangeAdbPathErrorKey, err.Error()))
			return
		}
		a.Log(i.Translate(ChangeAdbPathSuccessKey))
		a.Refresh()
	default:
		MessageBox(
			Icon("error"),
			Title("Error"),
			Msg(i.Translate(OnlyOneFileKey)),
			Parent(App), Type("ok"),
		)
	}
}

func (a *GuiApp) isApKInstalled() bool {
	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		return false
	}

	status, err := currentDevice.DumpsysApk()
	if err != nil {
		return false
	}

	if len(status.Stdout) == 0 {
		return false
	}
	isAppInstalled := true
	for _, v := range status.Stdout {
		if strings.Contains(v, "Unable to find package") {
			isAppInstalled = false
			break
		}
	}
	return isAppInstalled
}

func (a *GuiApp) grantWriteSecureSettings() error {
	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		return errors.New("current device is nil")
	}

	status, err := currentDevice.DumpsysApk()
	if err != nil {
		return errors.New("dumpsys error: " + err.Error())
	}

	var isGranted bool
	for _, v := range status.Stdout {
		if strings.Contains(v, env.WriteSecureSettingsPermissionName) {
			isGranted = true
			break
		}
	}

	if isGranted {
		return nil
	}

	_, err = currentDevice.RunCmd([]string{
		"shell", "pm", "grant", env.ApkPackageName,
		env.WriteSecureSettingsPermissionName,
	}, 15*time.Second)
	if err != nil {
		return errors.New("grant WRITE_SECURE_SETTINGS permission error: " + err.Error())
	}
	return nil
}

func (a *GuiApp) isAppRunning() bool {
	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		return false
	}

	status, err := currentDevice.RunCmd([]string{"shell", "ps"}, 10*time.Second)
	if err != nil {
		return false
	}

	for _, v := range status.Stdout {
		if strings.Contains(v, env.ApkPackageName) {
			return true
		}
	}
	return false
}

func (a *GuiApp) queryAppVersionByAdb() (*version.Version, error) {
	currentDevice := adb.GAdbManager.DeviceMap[a.selected]
	if currentDevice == nil {
		return nil, errors.New("current device is nil")
	}

	status, err := currentDevice.DumpsysApk()
	if err != nil {
		return nil, errors.New("dumpsys error: " + err.Error())
	}

	for _, v := range status.Stdout {
		if strings.Contains(v, "versionName") {
			parts := strings.SplitN(v, "=", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])
				if key != "versionName" {
					continue
				}
				return version.NewVersion(strings.TrimSpace(value))
			}
		}
	}

	return nil, errors.New("versionName not found")
}

func (a *GuiApp) getAppVersion() (*version.Version, error) {
	if a.buildInfo != nil && a.buildInfo.Version != "" {
		return version.NewVersion(strings.TrimSpace(a.buildInfo.Version))
	}
	return a.queryAppVersionByAdb()
}
