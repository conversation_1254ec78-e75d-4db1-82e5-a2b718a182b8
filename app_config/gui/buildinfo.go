package gui

import (
	"appconfig/i18n"

	. "modernc.org/tk9.0"
)

type buildInfoView struct {
	root         *GuiApp
	frame        *TLabelframeWidget // top frame
	versionLabel *TLabelWidget

	buildTimeLabel *TLabelWidget
	isQuerying     bool
}

func newBuildInfo(app *GuiApp) *buildInfoView {
	i := i18n.Get(app.Lang)
	f := TLabelframe(Txt(i.Translate(BuildInfoKey)))

	view := &buildInfoView{
		root:  app,
		frame: f,
	}

	view.render()

	return view
}

func (m *buildInfoView) render() {
	i := i18n.Get(m.root.Lang)
	m.frame.Configure(Txt(i.Translate(BuildInfoKey)))

	if m.isQuerying {
		m.setVersion(Txt(i18n.Get(m.root.Lang).Translate(QueryingBuildInfoKey)), Foreground("black"))
		if m.buildTimeLabel != nil {
			Destroy(m.buildTimeLabel)
			m.buildTimeLabel = nil
		}
		return
	}

	info := m.root.buildInfo
	if info == nil {
		m.setVersion(Txt(i.Translate(AppNotConnectKey)), Foreground("red"))
		if m.buildTimeLabel != nil {
			Destroy(m.buildTimeLabel)
			m.buildTimeLabel = nil
		}
		return
	}

	if info.Version != "" {
		m.setVersion(Txt(i.Translate(VersionKey)+info.Version), Foreground("black"))
	} else {
		m.setVersion(Txt(i.Translate(VersionKey)+i.Translate(UnknownKey)), Foreground("red"))
	}

	if info.BuildTime != "" {
		m.setBuildTime(Txt(i.Translate(BuildTimeKey) + info.BuildTime))
	} else {
		// m.setBuildTime(Txt(i.Translate(BuildTimeKey) + " " + i.Translate(UnknownKey))))
		if m.buildTimeLabel != nil {
			Destroy(m.buildTimeLabel)
			m.buildTimeLabel = nil
		}
	}
}

// 设置版本, txt: Txt Option
func (m *buildInfoView) setVersion(options ...Opt) {
	i := i18n.Get(m.root.Lang)
	if m.versionLabel == nil {
		m.versionLabel = m.frame.TLabel(
			Txt(i.Translate(VersionKey)),
		)

		Grid(m.versionLabel, In(m.frame), Column(0), Row(0), Padx("10"),
			Pady("10"), Sticky("news"))
		GridColumnConfigure(m.frame, 0, Weight(1))
	}

	m.versionLabel.Configure(options...)
}

func (m *buildInfoView) setBuildTime(options ...Opt) {
	i := i18n.Get(m.root.Lang)
	if m.buildTimeLabel == nil {
		m.buildTimeLabel = m.frame.TLabel(
			Txt(i.Translate(BuildTimeKey)),
		)
		Grid(m.buildTimeLabel, In(m.frame), Column(1), Row(0), Padx("10"),
			Pady("10"), Sticky("news"))
		GridColumnConfigure(m.frame, 1, Weight(1))
	}

	m.buildTimeLabel.Configure(options...)
}
