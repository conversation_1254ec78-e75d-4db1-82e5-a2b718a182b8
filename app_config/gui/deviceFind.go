package gui

import (
	"strings"
	"time"

	"appconfig/adb"
	"appconfig/i18n"

	. "modernc.org/tk9.0"
)

type deviceFindView struct {
	root  *GuiApp
	frame *TFrameWidget
	// 当前连接设备状态
	msg *TLabelWidget

	// 连接设备列表
	list *TComboboxWidget

	// 连接设备按钮
	btn             *TButtonWidget
	btnClicked      bool
	isFindingDevice bool
	btnTimer        *time.Timer

	needUpdateSelectedDevice bool
}

func newDeviceFind(app *GuiApp) *deviceFindView {
	i := i18n.Get(app.Lang)
	f := TFrame()

	view := &deviceFindView{
		frame: f,
		root:  app,
	}

	view.renderMsg()
	view.CreateList()
	GridColumnConfigure(f, 0, Weight(1))

	empty := f.TFrame()
	Grid(empty, In(f), Column(3), Row(0), <PERSON>y("news"))

	// 连接设备按钮
	b := f.TButton(
		Txt(i.Translate(ConnectKey)),
		Command(func() {
			if view.btnClicked || view.isFindingDevice {
				return
			}

			if view.btnTimer == nil {
				view.btnTimer = time.AfterFunc(500*time.Millisecond, func() {
					view.btnClicked = false
				})
			} else {
				view.btnTimer.Reset(500 * time.Millisecond)
			}

			view.btnClicked = true
			view.isFindingDevice = true
			go app.findAdbDevice()
		}),
	)
	Grid(b,
		In(f),
		Padx("10"),
		Column(4),
		Row(0),
		Sticky("ne"),
	)
	view.btn = b

	return view
}

func (m *deviceFindView) render() {
	i := i18n.Get(m.root.Lang)
	m.btn.Configure(Txt(i.Translate(ConnectKey)))
	m.renderMsg()
}

func (m *deviceFindView) renderMsg() {
	if m.msg == nil {
		m.msg = m.frame.TLabel(Txt(""))
		Grid(m.msg, In(m.frame), Column(0), Row(0), Columnspan(3), Sticky("news"))
		GridColumnConfigure(m.frame, 0, Weight(1))
	}

	i := i18n.Get(m.root.Lang)
	m.msg.Configure(Txt(i.Translate(DeviceKey)), Foreground("black"))
}

func (m *deviceFindView) RefreshList() {
	defer func() {
		m.needUpdateSelectedDevice = false
	}()
	if m.list == nil {
		m.CreateList()
	}

	defaultValue := m.defaultValue()

	// 检查当前选中的设备是否在列表中，如果在，则设为默认值
	if d, ok := adb.GAdbManager.DeviceMap[m.root.selected]; ok {
		defaultValue = d.DisplayName()
	}

	m.list.Configure(
		Values(m.listValues()),
		State("readonly"),
		Textvariable(defaultValue),
	)

	// 列表为空，则清空选中的设备
	if defaultValue == "" {
		m.root.selected = ""
		return
	}

	m.root.onSelected()
}

func (m *deviceFindView) CreateList() {
	if m.list != nil {
		Destroy(m.list)
	}
	defaultValue := m.defaultValue()
	m.list = m.frame.TCombobox(
		Values(m.listValues()),
		State("readonly"),
		Textvariable(defaultValue),
	)

	// 默认选中
	if defaultValue != "" {
		m.root.onSelected()
	}

	Grid(m.list, In(m.frame), Column(1), Row(0), Sticky("news"))
	GridColumnConfigure(m.frame, 1, Minsize("250"), Weight(1))

	GridColumnConfigure(m.frame, 0, Minsize("100"), Weight(0))

	Bind(m.list, "<<ComboboxSelected>>", Command(m.root.onSelected))

	GridColumnConfigure(m.frame, 2, Minsize(1), Weight(5))
}

// 生成列表选项
func (m *deviceFindView) listValues() string {
	b := strings.Builder{}

	for _, v := range adb.GAdbManager.DeviceMap {
		b.WriteString(strings.ReplaceAll(v.DisplayName(), " ", "\\ "))
		b.WriteString(" ")
	}

	return b.String()
}

// 设置 selected 的默认值
func (m *deviceFindView) defaultValue() string {
	for _, v := range adb.GAdbManager.DeviceMap {
		// return first
		return v.DisplayName()
	}
	return ""
}

func parseSelected2DeviceID(selected string) string {
	fields := strings.Split(selected, " ")

	l := len(fields)
	if l == 0 {
		return ""
	}

	return strings.Trim(fields[l-1], "()")
}
