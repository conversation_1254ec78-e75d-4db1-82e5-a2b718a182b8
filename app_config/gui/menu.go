package gui

import (
	"appconfig/adb"
	"appconfig/i18n"

	. "modernc.org/tk9.0"
)

// 顶部菜单栏
/**
 * 结构：
 * 顶部菜单栏
 *  - 文件菜单
 *	- 语言菜单
 */
type navMenuView struct {
	root *GuiApp
	// 顶层菜单
	top *MenuWidget
	// 文件菜单
	file *MenuWidget
	// 语言菜单
	lang *MenuWidget
	// adb菜单
	adbProgram *MenuWidget
}

func newNavMenu(app *GuiApp) *navMenuView {
	top := Menu()

	i := i18n.Get(app.Lang)

	file := top.Menu()
	file.AddCommand(Lbl(i.Translate(ExitKey)), Underline(1), Accelerator("Ctrl+Q"), Command(func() { app.ExitHandler() }))
	Bind(App, "<Control-q>", Command(func() { file.Invoke(0) }))

	lang := top.Menu()
	for i := 0; i < len(i18n.Locales); i++ {
		v := i18n.Locales[i]
		lang.AddCommand(Lbl(v.Desc), Underline(0), Command(func() {
			app.Lang = v.ID
			app.Refresh()
		}))
	}

	adbProgram := top.Menu()
	adbProgram.AddCommand(Lbl(i.Translate(CurrentAdbPathKey)+adb.GAdbManager.Path), Underline(0))
	adbProgram.AddCommand(Lbl(i.Translate(ChangeAdbPathKey)), Underline(0), Command(app.onChangeAdbPath))

	top.AddCascade(Lbl(i.Translate(FileKey)), Underline(0), Mnu(file))
	top.AddCascade(Lbl(i.Translate(LanguageKey)), Mnu(lang))
	top.AddCascade(Lbl(i.Translate(AdbProgramKey)), Mnu(adbProgram))

	return &navMenuView{
		root:       app,
		top:        top,
		file:       file,
		lang:       lang,
		adbProgram: adbProgram,
	}
}

// 加载语言
func (a *navMenuView) render() {
	i := i18n.Get(a.root.Lang)
	a.file.EntryConfigure(0, Lbl(i.Translate(ExitKey)))
	a.top.EntryConfigure(0, Lbl(i.Translate(FileKey)))
	a.top.EntryConfigure(1, Lbl(i.Translate(LanguageKey)))
	a.top.EntryConfigure(2, Lbl(i.Translate(AdbProgramKey)))
	a.adbProgram.EntryConfigure(0, Lbl(i.Translate(CurrentAdbPathKey)+adb.GAdbManager.Path))
	a.adbProgram.EntryConfigure(1, Lbl(i.Translate(ChangeAdbPathKey)))
}
