package gui

// translation keys
var (
	// Common
	ExitKey         = "Exit"
	FileKey         = "File"
	WriteKey        = "Write"
	ReadKey         = "Read"
	ErrorKey        = "Error"
	WarningKey      = "Warning"
	UnknownKey      = "Unknown"
	TimeoutKey      = "Timeout"
	QueryKey        = "Query"
	OpenKey         = "Open"
	DecimalKey      = "Decimal"
	Hexadecimalkey  = "Hexadecimal"
	ProgramErrorKey = "Program error, please contact the support team"

	// menu
	LanguageKey       = "Language"
	AppTitleKey       = "POC Config Tool"
	AdbProgramKey     = "Adb Program"
	ChangeAdbPathKey  = "Change Adb Path"
	CurrentAdbPathKey = "Current Adb Path: "
	OpenAdbProgramKey = "Open Adb Program"

	// device
	ConnectKey            = "Connect device"
	DeviceKey             = "Poc Devices"
	DeviceNotConnectedKey = "Device not connected"
	AccountConfigKey      = "Account Config"

	// server setting
	HostKey        = "Server Host: "
	PortKey        = "Server Port: "
	InvalidHostKey = "Invalid host, it should be a hostname or ip address"
	InvalidPortKey = "Invalid port, it should be a number between 1 and 65535"

	// account setting
	TerminalDMRIDKey                        = "Terminal DMRID: "
	PasswordKey                             = "Password: "
	InvalidDecDMRIDKey                      = "Invalid DMRID, it should be a decimal number"
	InvalidHexDMRIDKey                      = "Invalid DMRID, it should be a hexadecimal number"
	PasswordIsRequiredKey                   = "password is required"
	CanUserEditLoginParamsKey               = "Can user edit login parameters?"
	DmrIdIncrementAfterAccountConfigSuccess = "Auto-increment DMRID after successful account config write?"

	// build info
	BuildInfoKey         = "Current Device App Build Info"
	AppNotConnectKey     = "App not connect,check if the device install the app"
	VersionKey           = "Version: "
	QueryingBuildInfoKey = "Querying Build Info..."
	BuildTimeKey         = "Build Time: "

	// apk install
	UpgradeApkKey        = "Upgrade APK"
	ApkFileKey           = "APK File: "
	OnlyOneFileKey       = "Only one file can be specified"
	installKey           = "Install"
	UninstallKey         = "Uninstall"
	NoApkFileSelectedKey = "No APK file selected"
	OpenApkFileKey       = "Open APK File"
	ApkVersionKey        = "APK Version: %s"

	// log
	TryFindingDeviceKey               = "Trying finding device..."
	FindDeviceSuccessKey              = "Find device success"
	FindAdbErrorKey                   = "Find adb error: %s"
	FindDeviceErrorKey                = "Find device error: %s"
	SelectedDeviceKey                 = "Current selected device: %s"
	NoDeviceSelectedKey               = "No device selected"
	TryConnectDeviceKey               = "Trying connect device..."
	ConnectDeviceErrorKey             = "Connect device %s error: %s"
	ConnectDeviceFailKey              = "Connect device %s failed, please check if the app is open."
	ConnectDeviceSuccessKey           = "Connect device %s success"
	TryQueryBuildInfoKey              = "Trying query %s build info..."
	QueryBuildInfoErrorKey            = "Query %s build info error: %s"
	QueryBuildInfoSuccessKey          = "Query %s build info success"
	SendRequestFailKey                = "Send request failed: %s"
	QueryAppConfigErrorKey            = "Query %s app config error: %s"
	QueryAppConfigSuccessKey          = "Query %s app config success"
	TrySetAppConfigKey                = "Trying set %s app config..."
	SetAppConfigErrorKey              = "Set %s app config error: %s"
	SetAppConfigSuccessKey            = "Set %s app config success"
	TryUninstallAppKey                = "Trying uninstall %s app..."
	UninstallAppErrorKey              = "Uninstall %s app error: %s"
	UninstallAppSuccessKey            = "Uninstall %s app success"
	ApkFileVersionIsUnknownKey        = "Apk file version is unknown, are you sure to install?"
	AppAndFileVersionIsSameKey        = "Current app and apk file version is same, are you sure to install?"
	ApkFileVersionIsSmallerThanAppKey = "Apk file Version is smaller than current app, are you sure to downgrade?"
	AppVersionIsSmallerThanApkKey     = "Current app version is smaller than apk file, do you want to upgrade?"
	TryInstallApkKey                  = "Trying install new apk to %s, apk path: %s"
	ReTryInstallApkKey                = "Re-Trying install apk ..."
	InstallApkErrorKey                = "Install apk to %s error: %s"
	InstallApkSuccessKey              = "Install apk to %s success"
	PushingApkKey                     = "Pushing APK..."
	PushApkErrorKey                   = "Push APK error: %s"
	PushApkSuccessKey                 = "Push APK success"
	ChangeAdbPathErrorKey             = "Change Adb Path error: %s"
	ChangeAdbPathSuccessKey           = "Change Adb Path success"
	AdbNotFoundKey                    = "Adb not found"
	OpenAdbProgramByYourself          = "In order to continue, please manually select the location of the adb program"
	AskIfInstallApkKey                = "Apk is not installed, do you want to install it?"
	ApkPathKey                        = "Apk Path: %s"
	NotCheckUnknownApkVersionKey      = "Apk version is unknown, not check"
)
