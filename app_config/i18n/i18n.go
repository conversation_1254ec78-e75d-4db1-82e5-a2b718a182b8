package i18n

import (
	_ "appconfig/i18n/locales"

	"golang.org/x/text/language"
	"golang.org/x/text/message"
)

//go:generate gotext -srclang=en-US update -out=./locales/catalog.gen.go -lang=en-US,zh-CN appconfig/gui

type Localizer struct {
	ID      string
	Desc    string
	Printer *message.Printer
}

const (
	ZH_CN = "zh-CN"
	EN_US = "en-US"
)

// 支持的本地化语言, 第一个为默认语言
var Locales = []Localizer{
	{
		ID:      ZH_CN,
		Desc:    "简体中文",
		Printer: message.NewPrinter(language.MustParse(ZH_CN)),
	},
	{
		ID:      EN_US,
		Desc:    "English",
		Printer: message.NewPrinter(language.MustParse(EN_US)),
	},
}

// 获取本地化语言, 如果语言不存在, 则返回nil
func Get(id string) *Localizer {
	for _, locale := range Locales {
		if id == locale.ID {
			return &locale
		}
	}

	return nil
}

func (l Localizer) Translate(key message.Reference, args ...interface{}) string {
	return l.Printer.Sprintf(key, args...)
}
