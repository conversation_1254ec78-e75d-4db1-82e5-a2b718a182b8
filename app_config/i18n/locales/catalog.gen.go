// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

package i18n

import (
	"golang.org/x/text/language"
	"golang.org/x/text/message"
	"golang.org/x/text/message/catalog"
)

type dictionary struct {
	index []uint32
	data  string
}

func (d *dictionary) Lookup(key string) (data string, ok bool) {
	p, ok := messageKeyToIndex[key]
	if !ok {
		return "", false
	}
	start, end := d.index[p], d.index[p+1]
	if start == end {
		return "", false
	}
	return d.data[start:end], true
}

func init() {
	dict := map[string]catalog.Dictionary{
		"en_US": &dictionary{index: en_USIndex, data: en_USData},
		"zh_CN": &dictionary{index: zh_CNIndex, data: zh_CNData},
	}
	fallback := language.MustParse("en-US")
	cat, err := catalog.NewFromMap(dict, catalog.Fallback(fallback))
	if err != nil {
		panic(err)
	}
	message.DefaultCatalog = cat
}

var messageKeyToIndex = map[string]int{
	"APK File: ":      38,
	"APK Version: %s": 43,
	"Account Config":  20,
	"Adb Program":     13,
	"Adb not found":   78,
	"Apk Path: %s":    81,
	"Apk file Version is smaller than current app, are you sure to downgrade?": 67,
	"Apk file version is unknown, are you sure to install?":                    65,
	"Apk is not installed, do you want to install it?":                         80,
	"Apk version is unknown, not check":                                        82,
	"App not connect,check if the device install the app":                      33,
	"Auto-increment DMRID after successful account config write?":              31,
	"Build Time: ":                    36,
	"Can user edit login parameters?": 30,
	"Change Adb Path":                 14,
	"Change Adb Path error: %s":       76,
	"Change Adb Path success":         77,
	"Connect device":                  17,
	"Connect device %s error: %s":     51,
	"Connect device %s success":       52,
	"Current Adb Path: ":              15,
	"Current Device App Build Info":   32,
	"Current app and apk file version is same, are you sure to install?":    66,
	"Current app version is smaller than apk file, do you want to upgrade?": 68,
	"Current selected device: %s":                                           48,
	"Decimal":                                                               8,
	"Device not connected":                                                  19,
	"Error":                                                                 4,
	"Exit":                                                                  0,
	"File":                                                                  1,
	"Find adb error: %s":                                                    46,
	"Find device error: %s":                                                 47,
	"Find device success":                                                   45,
	"Hexadecimal":                                                           9,
	"In order to continue, please manually select the location of the adb program": 79,
	"Install":                                                 40,
	"Install apk to %s error: %s":                             71,
	"Install apk to %s success":                               72,
	"Invalid DMRID, it should be a decimal number":            27,
	"Invalid DMRID, it should be a hexadecimal number":        28,
	"Invalid host, it should be a hostname or ip address":     23,
	"Invalid port, it should be a number between 1 and 65535": 24,
	"Language":                       11,
	"No APK file selected":           41,
	"No device selected":             49,
	"Only one file can be specified": 39,
	"Open":                           7,
	"Open APK File":                  42,
	"Open Adb Program":               16,
	"POC Config Tool":                12,
	"Password: ":                     26,
	"Poc Devices":                    18,
	"Program error, please contact the support team": 10,
	"Push APK error: %s":                             74,
	"Push APK success":                               75,
	"Pushing APK...":                                 73,
	"Query %s app config error: %s":                  57,
	"Query %s app config success":                    58,
	"Query %s build info error: %s":                  54,
	"Query %s build info success":                    55,
	"Querying Build Info...":                         35,
	"Re-Trying install apk ...":                      70,
	"Read":                                           3,
	"Send request failed: %s":                        56,
	"Server Host: ":                                  21,
	"Server Port: ":                                  22,
	"Set %s app config error: %s":                    60,
	"Set %s app config success":                      61,
	"Terminal DMRID: ":                               25,
	"Trying connect device...":                       50,
	"Trying finding device...":                       44,
	"Trying install new apk to %s, apk path: %s":     69,
	"Trying query %s build info...":                  53,
	"Trying set %s app config...":                    59,
	"Trying uninstall %s app...":                     62,
	"Uninstall %s app error: %s":                     63,
	"Uninstall %s app success":                       64,
	"Unknown":                                        6,
	"Upgrade APK":                                    37,
	"Version: ":                                      34,
	"Warning":                                        5,
	"Write":                                          2,
	"password is required":                           29,
}

var en_USIndex = []uint32{ // 84 elements
	// Entry 0 - 1F
	0x00000000, 0x00000005, 0x0000000a, 0x00000010,
	0x00000015, 0x0000001b, 0x00000023, 0x0000002b,
	0x00000030, 0x00000038, 0x00000044, 0x00000073,
	0x0000007c, 0x0000008c, 0x00000098, 0x000000a8,
	0x000000bf, 0x000000d0, 0x000000df, 0x000000eb,
	0x00000100, 0x0000010f, 0x00000121, 0x00000133,
	0x00000167, 0x0000019f, 0x000001b4, 0x000001c3,
	0x000001f0, 0x00000221, 0x00000236, 0x00000256,
	// Entry 20 - 3F
	0x00000292, 0x000002b0, 0x000002e4, 0x000002f2,
	0x00000309, 0x0000031a, 0x00000326, 0x00000335,
	0x00000354, 0x0000035c, 0x00000371, 0x0000037f,
	0x00000392, 0x000003ab, 0x000003bf, 0x000003d5,
	0x000003ee, 0x0000040d, 0x00000420, 0x00000439,
	0x0000045b, 0x00000478, 0x00000499, 0x000004bd,
	0x000004dc, 0x000004f7, 0x0000051b, 0x0000053a,
	0x00000559, 0x0000057b, 0x00000598, 0x000005b6,
	// Entry 40 - 5F
	0x000005d7, 0x000005f3, 0x00000629, 0x0000066c,
	0x000006b5, 0x000006fb, 0x0000072c, 0x00000746,
	0x00000768, 0x00000785, 0x00000794, 0x000007aa,
	0x000007bb, 0x000007d8, 0x000007f0, 0x000007fe,
	0x0000084b, 0x0000087c, 0x0000088c, 0x000008ae,
} // Size: 360 bytes

const en_USData string = "" + // Size: 2222 bytes
	"\x02Exit\x02File\x02Write\x02Read\x02Error\x02Warning\x02Unknown\x02Open" +
	"\x02Decimal\x02Hexadecimal\x02Program error, please contact the support " +
	"team\x02Language\x02POC Config Tool\x02Adb Program\x02Change Adb Path" +
	"\x04\x00\x01 \x12\x02Current Adb Path:\x02Open Adb Program\x02Connect de" +
	"vice\x02Poc Devices\x02Device not connected\x02Account Config\x04\x00" +
	"\x01 \x0d\x02Server Host:\x04\x00\x01 \x0d\x02Server Port:\x02Invalid ho" +
	"st, it should be a hostname or ip address\x02Invalid port, it should be " +
	"a number between 1 and 65535\x04\x00\x01 \x10\x02Terminal DMRID:\x04\x00" +
	"\x01 \x0a\x02Password:\x02Invalid DMRID, it should be a decimal number" +
	"\x02Invalid DMRID, it should be a hexadecimal number\x02password is requ" +
	"ired\x02Can user edit login parameters?\x02Auto-increment DMRID after su" +
	"ccessful account config write?\x02Current Device App Build Info\x02App n" +
	"ot connect,check if the device install the app\x04\x00\x01 \x09\x02Versi" +
	"on:\x02Querying Build Info...\x04\x00\x01 \x0c\x02Build Time:\x02Upgrade" +
	" APK\x04\x00\x01 \x0a\x02APK File:\x02Only one file can be specified\x02" +
	"Install\x02No APK file selected\x02Open APK File\x02APK Version: %[1]s" +
	"\x02Trying finding device...\x02Find device success\x02Find adb error: %" +
	"[1]s\x02Find device error: %[1]s\x02Current selected device: %[1]s\x02No" +
	" device selected\x02Trying connect device...\x02Connect device %[1]s err" +
	"or: %[2]s\x02Connect device %[1]s success\x02Trying query %[1]s build in" +
	"fo...\x02Query %[1]s build info error: %[2]s\x02Query %[1]s build info s" +
	"uccess\x02Send request failed: %[1]s\x02Query %[1]s app config error: %[" +
	"2]s\x02Query %[1]s app config success\x02Trying set %[1]s app config..." +
	"\x02Set %[1]s app config error: %[2]s\x02Set %[1]s app config success" +
	"\x02Trying uninstall %[1]s app...\x02Uninstall %[1]s app error: %[2]s" +
	"\x02Uninstall %[1]s app success\x02Apk file version is unknown, are you " +
	"sure to install?\x02Current app and apk file version is same, are you su" +
	"re to install?\x02Apk file Version is smaller than current app, are you " +
	"sure to downgrade?\x02Current app version is smaller than apk file, do y" +
	"ou want to upgrade?\x02Trying install new apk to %[1]s, apk path: %[2]s" +
	"\x02Re-Trying install apk ...\x02Install apk to %[1]s error: %[2]s\x02In" +
	"stall apk to %[1]s success\x02Pushing APK...\x02Push APK error: %[1]s" +
	"\x02Push APK success\x02Change Adb Path error: %[1]s\x02Change Adb Path " +
	"success\x02Adb not found\x02In order to continue, please manually select" +
	" the location of the adb program\x02Apk is not installed, do you want to" +
	" install it?\x02Apk Path: %[1]s\x02Apk version is unknown, not check"

var zh_CNIndex = []uint32{ // 84 elements
	// Entry 0 - 1F
	0x00000000, 0x00000007, 0x0000000e, 0x00000015,
	0x0000001c, 0x00000023, 0x0000002a, 0x00000031,
	0x00000038, 0x00000042, 0x0000004f, 0x00000073,
	0x0000007a, 0x0000008a, 0x00000095, 0x000000a7,
	0x000000bf, 0x000000d1, 0x000000de, 0x000000e9,
	0x000000f9, 0x00000106, 0x0000011d, 0x00000134,
	0x00000164, 0x00000196, 0x000001a9, 0x000001b7,
	0x000001e1, 0x0000020e, 0x00000221, 0x00000247,
	// Entry 20 - 3F
	0x00000287, 0x000002a5, 0x000002d5, 0x000002e3,
	0x000002ff, 0x00000313, 0x00000320, 0x00000330,
	0x00000349, 0x00000350, 0x00000365, 0x00000375,
	0x00000387, 0x0000039d, 0x000003b0, 0x000003c9,
	0x000003e3, 0x00000400, 0x00000413, 0x00000429,
	0x0000044a, 0x00000464, 0x00000487, 0x000004ae,
	0x000004ce, 0x000004e8, 0x0000050d, 0x0000052b,
	0x0000054c, 0x00000571, 0x0000058f, 0x000005a9,
	// Entry 40 - 5F
	0x000005c8, 0x000005e0, 0x00000606, 0x00000647,
	0x0000068c, 0x000006d0, 0x00000704, 0x0000071f,
	0x00000742, 0x0000075e, 0x00000771, 0x00000788,
	0x00000798, 0x000007b7, 0x000007cf, 0x000007e3,
	0x0000081d, 0x00000844, 0x00000856, 0x00000872,
} // Size: 360 bytes

const zh_CNData string = "" + // Size: 2162 bytes
	"\x02退出\x02文件\x02写入\x02读取\x02错误\x02警告\x02未知\x02打开\x02十进制\x02十六进制\x02程序错误," +
	" 请联系支持团队\x02语言\x02POC配置工具\x02Adb 程序\x02修改 Adb 路径\x04\x00\x01 \x13\x02当前 " +
	"Adb 路径:\x02打开 Adb 程序\x02连接设备\x02Poc 设备\x02设备未连接\x02账号配置\x04\x00\x01 \x12" +
	"\x02服务器地址: \x04\x00\x01 \x12\x02服务器端口: \x02无效的地址，要求为主机名或IP地址\x02无效的端口，要求" +
	"为1-65535之间的数字\x04\x00\x01 \x0e\x02终端DMRID: \x04\x00\x01 \x09\x02密码: " +
	"\x02无效的DMRID，要求为十进制数字\x02无效的DMRID，要求为十六进制数字\x02密码不能为空\x02用户是否可以编辑登录参数?" +
	"\x02是否在成功写入账号配置之后，为DMRID自动加一?\x02当前设备 App 构建信息\x02App 未连接,请检查设备是否安装应用" +
	"\x04\x00\x01 \x09\x02版本: \x02正在查询构建信息...\x04\x00\x01 \x0f\x02构建时间: \x02升" +
	"级软件\x04\x00\x01 \x0b\x02APK 文件\x02文件只能指定一个\x02安装\x02未选择 APK 文件\x02打开AP" +
	"K文件\x02APK 版本: %[1]s\x02正在查找设备...\x02查找设备成功\x02查找 Adb 错误: %[1]s\x02查找设备错" +
	"误: %[1]s\x02当前选中的设备: %[1]s\x02没有选中设备\x02正在连接设备...\x02连接设备 %[1]s 错误: %[" +
	"2]s\x02连接设备 %[1]s 成功\x02正在查询 %[1]s 构建信息...\x02查询 %[1]s 构建信息错误: %[2]s\x02" +
	"查询 %[1]s 构建信息成功\x02发送请求失败: %[1]s\x02查询 %[1]s App 配置错误: %[2]s\x02查询 %[1" +
	"]s App 配置成功\x02正在设置 %[1]s App 配置...\x02设置 %[1]s App 配置错误: %[2]s\x02设置 %[" +
	"1]s App 配置成功\x02正在卸载 %[1]s App...\x02卸载 %[1]s App 错误: %[2]s\x02卸载 %[1]s " +
	"App 成功\x02Apk 文件版本未知, 是否安装?\x02当前已安装 App 和 Apk 文件版本相同, 确定安装吗?\x02apk 文件版" +
	"本小于当前已安装 App的版本, 确定降级吗?\x02当前已安装 App 版本小于 apk 文件的版本, 是否升级?\x02正在安装新的 a" +
	"pk 到 %[1]s, apk 路径: %[2]s\x02正在重试安装 apk ...\x02安装 apk 到 %[1]s 错误: %[2]s" +
	"\x02安装 apk 到 %[1]s 成功\x02正在推送APK...\x02推送APK错误: %[1]s\x02推送APK成功\x02修改 A" +
	"db 路径错误: %[1]s\x02修改 Adb 路径成功\x02Adb 程序未找到\x02为了继续操作，请手动选择adb程序所在位置\x02A" +
	"pk 程序未安装，是否要安装?\x02Apk 路径: %[1]s\x02Apk版本未知，不检查"

	// Total table size 5104 bytes (4KiB); checksum: EE339060
