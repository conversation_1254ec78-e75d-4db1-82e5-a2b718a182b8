{"language": "en-US", "messages": [{"id": ["ExitKey", "Exit"], "message": "Exit", "translation": "Exit", "comment": "Common", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["<PERSON><PERSON>ey", "File"], "message": "File", "translation": "File", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["WriteKey", "Write"], "message": "Write", "translation": "Write", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["<PERSON><PERSON><PERSON>", "Read"], "message": "Read", "translation": "Read", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Error"], "message": "Error", "translation": "Error", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["Warning<PERSON>ey", "Warning"], "message": "Warning", "translation": "Warning", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["<PERSON><PERSON><PERSON>", "Unknown"], "message": "Unknown", "translation": "Unknown", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["Open<PERSON>ey", "Open"], "message": "Open", "translation": "Open", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["DecimalKey", "Decimal"], "message": "Decimal", "translation": "Decimal", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["Hexadecimalkey", "Hexadecimal"], "message": "Hexadecimal", "translation": "Hexadecimal", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["ProgramError<PERSON><PERSON>", "Program error, please contact the support team"], "message": "Program error, please contact the support team", "translation": "Program error, please contact the support team", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["LanguageKey", "Language"], "message": "Language", "translation": "Language", "comment": "menu", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["AppTitleKey", "POC Config Tool"], "message": "POC Config Tool", "translation": "POC Config Tool", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["AdbProgramKey", "Adb Program"], "message": "Adb Program", "translation": "Adb Program", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["ChangeAdbPathKey", "Change Adb Path"], "message": "Change Adb Path", "translation": "Change Adb Path", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["CurrentAdbPathKey", "Current Adb Path:"], "message": "Current Adb Path:", "translation": "Current Adb Path:", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["OpenAdbProgramKey", "Open Adb Program"], "message": "Open Adb Program", "translation": "Open Adb Program", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["ConnectKey", "Connect device"], "message": "Connect device", "translation": "Connect device", "comment": "device", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["<PERSON><PERSON><PERSON><PERSON>", "Poc Devices"], "message": "Poc Devices", "translation": "Poc Devices", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["DeviceNotConnectedKey", "Device not connected"], "message": "Device not connected", "translation": "Device not connected", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["AccountConfigKey", "Account Config"], "message": "Account Config", "translation": "Account Config", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["<PERSON><PERSON><PERSON>", "Server Host:"], "message": "Server Host:", "translation": "Server Host:", "comment": "server setting", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["PortKey", "Server Port:"], "message": "Server Port:", "translation": "Server Port:", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["InvalidHostKey", "Invalid host, it should be a hostname or ip address"], "message": "Invalid host, it should be a hostname or ip address", "translation": "Invalid host, it should be a hostname or ip address", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["InvalidPortKey", "Invalid port, it should be a number between 1 and 65535"], "message": "Invalid port, it should be a number between 1 and 65535", "translation": "Invalid port, it should be a number between 1 and 65535", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["TerminalDMRIDKey", "Terminal DMRID:"], "message": "Terminal DMRID:", "translation": "Terminal DMRID:", "comment": "account setting", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["PasswordKey", "Password:"], "message": "Password:", "translation": "Password:", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["InvalidDecDMRIDKey", "Invalid DMRID, it should be a decimal number"], "message": "Invalid DMRID, it should be a decimal number", "translation": "Invalid DMRID, it should be a decimal number", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["InvalidHexDMRIDKey", "Invalid DMRID, it should be a hexadecimal number"], "message": "Invalid DMRID, it should be a hexadecimal number", "translation": "Invalid DMRID, it should be a hexadecimal number", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["PasswordIsRequiredKey", "password is required"], "message": "password is required", "translation": "password is required", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["CanUserEditLoginParamsKey", "Can user edit login parameters?"], "message": "Can user edit login parameters?", "translation": "Can user edit login parameters?", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["DmrIdIncrementAfterAccountConfigSuccess", "Auto-increment DMRID after successful account config write?"], "message": "Auto-increment DMRID after successful account config write?", "translation": "Auto-increment DMRID after successful account config write?", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["BuildInfoKey", "Current Device App Build Info"], "message": "Current Device App Build Info", "translation": "Current Device App Build Info", "comment": "build info", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["AppNotConnectKey", "App not connect,check if the device install the app"], "message": "App not connect,check if the device install the app", "translation": "App not connect,check if the device install the app", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["VersionKey", "Version:"], "message": "Version:", "translation": "Version:", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["QueryingBuildInfoKey", "Querying Build Info..."], "message": "Querying Build Info...", "translation": "Querying Build Info...", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["BuildTimeKey", "Build Time:"], "message": "Build Time:", "translation": "Build Time:", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["UpgradeApkKey", "Upgrade APK"], "message": "Upgrade APK", "translation": "Upgrade APK", "comment": "apk install", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["ApkFileKey", "APK File:"], "message": "APK File:", "translation": "APK File:", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["OnlyOneFile<PERSON>ey", "Only one file can be specified"], "message": "Only one file can be specified", "translation": "Only one file can be specified", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["<PERSON><PERSON>ey", "Install"], "message": "Install", "translation": "Install", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["NoApkFileSelectedKey", "No APK file selected"], "message": "No APK file selected", "translation": "No APK file selected", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["OpenApkFileKey", "Open APK File"], "message": "Open APK File", "translation": "Open APK File", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["ApkVersionKey", "APK Version: {VersionName}"], "message": "APK Version: {VersionName}", "translation": "APK Version: {VersionName}", "translatorComment": "Copied from source.", "placeholders": [{"id": "VersionName", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "versionName"}], "fuzzy": true}, {"id": ["TryFindingDeviceKey", "Trying finding device..."], "message": "Trying finding device...", "translation": "Trying finding device...", "comment": "log", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["FindDeviceSuccessKey", "Find device success"], "message": "Find device success", "translation": "Find device success", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["FindAdbErrorKey", "Find adb error: {Error}"], "message": "Find adb error: {Error}", "translation": "Find adb error: {Error}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}], "fuzzy": true}, {"id": ["FindDeviceError<PERSON>ey", "Find device error: {Error}"], "message": "Find device error: {Error}", "translation": "Find device error: {Error}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}], "fuzzy": true}, {"id": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Current selected device: {Selected}"], "message": "Current selected device: {Selected}", "translation": "Current selected device: {Selected}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["NoDeviceSelectedKey", "No device selected"], "message": "No device selected", "translation": "No device selected", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["TryConnectDeviceKey", "Trying connect device..."], "message": "Trying connect device...", "translation": "Trying connect device...", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["ConnectDeviceErrorKey", "Connect device {Selected} error: {ErrorMsg}"], "message": "Connect device {Selected} error: {ErrorMsg}", "translation": "Connect device {Selected} error: {ErrorMsg}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}], "fuzzy": true}, {"id": ["ConnectDeviceSuccessKey", "Connect device {Selected} success"], "message": "Connect device {Selected} success", "translation": "Connect device {Selected} success", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["TryQueryBuildInfoKey", "Trying query {Selected} build info..."], "message": "Trying query {Selected} build info...", "translation": "Trying query {Selected} build info...", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["QueryBuildInfoErrorKey", "Query {Selected} build info error: {ErrorMsg}"], "message": "Query {Selected} build info error: {ErrorMsg}", "translation": "Query {Selected} build info error: {ErrorMsg}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}], "fuzzy": true}, {"id": ["QueryBuildInfoSuccessKey", "Query {Selected} build info success"], "message": "Query {Selected} build info success", "translation": "Query {Selected} build info success", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["SendRequestFailKey", "Send request failed: {Error}"], "message": "Send request failed: {Error}", "translation": "Send request failed: {Error}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}], "fuzzy": true}, {"id": ["QueryAppConfigErrorKey", "Query {Selected} app config error: {ErrorMsg}"], "message": "Query {Selected} app config error: {ErrorMsg}", "translation": "Query {Selected} app config error: {ErrorMsg}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}], "fuzzy": true}, {"id": ["QueryAppConfigSuccessKey", "Query {Selected} app config success"], "message": "Query {Selected} app config success", "translation": "Query {Selected} app config success", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["TrySetAppConfigKey", "Trying set {Selected} app config..."], "message": "Trying set {Selected} app config...", "translation": "Trying set {Selected} app config...", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["SetAppConfigErrorKey", "Set {Selected} app config error: {ErrorMsg}"], "message": "Set {Selected} app config error: {ErrorMsg}", "translation": "Set {Selected} app config error: {ErrorMsg}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}], "fuzzy": true}, {"id": ["SetAppConfigSuccessKey", "Set {Selected} app config success"], "message": "Set {Selected} app config success", "translation": "Set {Selected} app config success", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["TryUninstallAppKey", "Trying uninstall {Selected} app..."], "message": "Trying uninstall {Selected} app...", "translation": "Trying uninstall {Selected} app...", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["UninstallAppErrorKey", "Uninstall {Selected} app error: {ErrorMsg}"], "message": "Uninstall {Selected} app error: {ErrorMsg}", "translation": "Uninstall {Selected} app error: {ErrorMsg}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}], "fuzzy": true}, {"id": ["UninstallAppSuccessKey", "Uninstall {Selected} app success"], "message": "Uninstall {Selected} app success", "translation": "Uninstall {Selected} app success", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["ApkFileVersionIsUnknownKey", "Apk file version is unknown, are you sure to install?"], "message": "Apk file version is unknown, are you sure to install?", "translation": "Apk file version is unknown, are you sure to install?", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["AppAndFileVersionIsSameKey", "Current app and apk file version is same, are you sure to install?"], "message": "Current app and apk file version is same, are you sure to install?", "translation": "Current app and apk file version is same, are you sure to install?", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["ApkFileVersionIsSmallerThanAppKey", "Apk file Version is smaller than current app, are you sure to downgrade?"], "message": "Apk file Version is smaller than current app, are you sure to downgrade?", "translation": "Apk file Version is smaller than current app, are you sure to downgrade?", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["AppVersionIsSmallerThanApkKey", "Current app version is smaller than apk file, do you want to upgrade?"], "message": "Current app version is smaller than apk file, do you want to upgrade?", "translation": "Current app version is smaller than apk file, do you want to upgrade?", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["TryInstallApkKey", "Trying install new apk to {Selected}, apk path: {Path}"], "message": "Trying install new apk to {Selected}, apk path: {Path}", "translation": "Trying install new apk to {Selected}, apk path: {Path}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Path", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "path"}], "fuzzy": true}, {"id": ["ReTryInstallApkKey", "Re-Trying install apk ..."], "message": "Re-Trying install apk ...", "translation": "Re-Trying install apk ...", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["InstallApkErrorKey", "Install apk to {Selected} error: {ErrorMsg}"], "message": "Install apk to {Selected} error: {ErrorMsg}", "translation": "Install apk to {Selected} error: {ErrorMsg}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}], "fuzzy": true}, {"id": ["InstallApkSuccessKey", "Install apk to {Selected} success"], "message": "Install apk to {Selected} success", "translation": "Install apk to {Selected} success", "translatorComment": "Copied from source.", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}], "fuzzy": true}, {"id": ["PushingApkKey", "Pushing APK..."], "message": "Pushing APK...", "translation": "Pushing APK...", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["PushApkErrorKey", "Push APK error: {Error}"], "message": "Push APK error: {Error}", "translation": "Push APK error: {Error}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}], "fuzzy": true}, {"id": ["PushApkSuccessKey", "Push APK success"], "message": "Push APK success", "translation": "Push APK success", "translatorComment": "Copied from source.", "placeholders": [{"id": "FileName", "string": "%[1]v", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "fileName"}], "fuzzy": true}, {"id": ["ChangeAdbPathErrorKey", "Change Adb Path error: {Error}"], "message": "Change Adb Path error: {Error}", "translation": "Change Adb Path error: {Error}", "translatorComment": "Copied from source.", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}], "fuzzy": true}, {"id": ["ChangeAdbPathSuccessKey", "Change Adb Path success"], "message": "Change Adb Path success", "translation": "Change Adb Path success", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["AdbNotFoundKey", "Adb not found"], "message": "Adb not found", "translation": "Adb not found", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["OpenAdbProgramByYourself", "In order to continue, please manually select the location of the adb program"], "message": "In order to continue, please manually select the location of the adb program", "translation": "In order to continue, please manually select the location of the adb program", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["AskIfInstallApkKey", "Apk is not installed, do you want to install it?"], "message": "Apk is not installed, do you want to install it?", "translation": "Apk is not installed, do you want to install it?", "translatorComment": "Copied from source.", "fuzzy": true}, {"id": ["ApkPathKey", "Apk Path: {ApkPath}"], "message": "Apk Path: {ApkPath}", "translation": "Apk Path: {ApkPath}", "translatorComment": "Copied from source.", "placeholders": [{"id": "ApkPath", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "apkPath"}], "fuzzy": true}, {"id": ["NotCheckUnknownApkVersionKey", "Apk version is unknown, not check"], "message": "Apk version is unknown, not check", "translation": "Apk version is unknown, not check", "translatorComment": "Copied from source.", "fuzzy": true}]}