{"language": "zh-CN", "messages": [{"id": ["ExitKey", "Exit"], "message": "Exit", "translation": "退出", "comment": "Common"}, {"id": ["<PERSON><PERSON>ey", "File"], "message": "File", "translation": "文件"}, {"id": ["WriteKey", "Write"], "message": "Write", "translation": "写入"}, {"id": ["<PERSON><PERSON><PERSON>", "Read"], "message": "Read", "translation": "读取"}, {"id": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Error"], "message": "Error", "translation": "错误"}, {"id": ["Warning<PERSON>ey", "Warning"], "message": "Warning", "translation": "警告"}, {"id": ["<PERSON><PERSON><PERSON>", "Unknown"], "message": "Unknown", "translation": "未知"}, {"id": ["Open<PERSON>ey", "Open"], "message": "Open", "translation": "打开"}, {"id": ["DecimalKey", "Decimal"], "message": "Decimal", "translation": "十进制"}, {"id": ["Hexadecimalkey", "Hexadecimal"], "message": "Hexadecimal", "translation": "十六进制"}, {"id": ["ProgramError<PERSON><PERSON>", "Program error, please contact the support team"], "message": "Program error, please contact the support team", "translation": "程序错误, 请联系支持团队"}, {"id": ["LanguageKey", "Language"], "message": "Language", "translation": "语言", "comment": "menu"}, {"id": ["AppTitleKey", "POC Config Tool"], "message": "POC Config Tool", "translation": "POC配置工具"}, {"id": ["AdbProgramKey", "Adb Program"], "message": "Adb Program", "translation": "Adb 程序"}, {"id": ["ChangeAdbPathKey", "Change Adb Path"], "message": "Change Adb Path", "translation": "修改 Adb 路径"}, {"id": ["CurrentAdbPathKey", "Current Adb Path:"], "message": "Current Adb Path:", "translation": "当前 Adb 路径:"}, {"id": ["OpenAdbProgramKey", "Open Adb Program"], "message": "Open Adb Program", "translation": "打开 Adb 程序"}, {"id": ["ConnectKey", "Connect device"], "message": "Connect device", "translation": "连接设备", "comment": "device"}, {"id": ["<PERSON><PERSON><PERSON><PERSON>", "Poc Devices"], "message": "Poc Devices", "translation": "Poc 设备"}, {"id": ["DeviceNotConnectedKey", "Device not connected"], "message": "Device not connected", "translation": "设备未连接"}, {"id": ["AccountConfigKey", "Account Config"], "message": "Account Config", "translation": "账号配置"}, {"id": ["<PERSON><PERSON><PERSON>", "Server Host:"], "message": "Server Host:", "translation": "服务器地址: ", "comment": "server setting"}, {"id": ["PortKey", "Server Port:"], "message": "Server Port:", "translation": "服务器端口: "}, {"id": ["InvalidHostKey", "Invalid host, it should be a hostname or ip address"], "message": "Invalid host, it should be a hostname or ip address", "translation": "无效的地址，要求为主机名或IP地址"}, {"id": ["InvalidPortKey", "Invalid port, it should be a number between 1 and 65535"], "message": "Invalid port, it should be a number between 1 and 65535", "translation": "无效的端口，要求为1-65535之间的数字"}, {"id": ["TerminalDMRIDKey", "Terminal DMRID:"], "message": "Terminal DMRID:", "translation": "终端DMRID: ", "comment": "account setting"}, {"id": ["PasswordKey", "Password:"], "message": "Password:", "translation": "密码: "}, {"id": ["InvalidDecDMRIDKey", "Invalid DMRID, it should be a decimal number"], "message": "Invalid DMRID, it should be a decimal number", "translation": "无效的DMRID，要求为十进制数字"}, {"id": ["InvalidHexDMRIDKey", "Invalid DMRID, it should be a hexadecimal number"], "message": "Invalid DMRID, it should be a hexadecimal number", "translation": "无效的DMRID，要求为十六进制数字"}, {"id": ["PasswordIsRequiredKey", "password is required"], "message": "password is required", "translation": "密码不能为空"}, {"id": ["CanUserEditLoginParamsKey", "Can user edit login parameters?"], "message": "Can user edit login parameters?", "translation": "用户是否可以编辑登录参数?"}, {"id": ["DmrIdIncrementAfterAccountConfigSuccess", "Auto-increment DMRID after successful account config write?"], "message": "Auto-increment DMRID after successful account config write?", "translation": "是否在成功写入账号配置之后，为DMRID自动加一?"}, {"id": ["BuildInfoKey", "Current Device App Build Info"], "message": "Current Device App Build Info", "translation": "当前设备 App 构建信息", "comment": "build info"}, {"id": ["AppNotConnectKey", "App not connect,check if the device install the app"], "message": "App not connect,check if the device install the app", "translation": "App 未连接,请检查设备是否安装应用"}, {"id": ["VersionKey", "Version:"], "message": "Version:", "translation": "版本: "}, {"id": ["QueryingBuildInfoKey", "Querying Build Info..."], "message": "Querying Build Info...", "translation": "正在查询构建信息..."}, {"id": ["BuildTimeKey", "Build Time:"], "message": "Build Time:", "translation": "构建时间: "}, {"id": ["UpgradeApkKey", "Upgrade APK"], "message": "Upgrade APK", "translation": "升级软件", "comment": "apk install"}, {"id": ["ApkFileKey", "APK File:"], "message": "APK File:", "translation": "APK 文件"}, {"id": ["OnlyOneFile<PERSON>ey", "Only one file can be specified"], "message": "Only one file can be specified", "translation": "文件只能指定一个"}, {"id": ["<PERSON><PERSON>ey", "Install"], "message": "Install", "translation": "安装"}, {"id": ["NoApkFileSelectedKey", "No APK file selected"], "message": "No APK file selected", "translation": "未选择 APK 文件"}, {"id": ["OpenApkFileKey", "Open APK File"], "message": "Open APK File", "translation": "打开APK文件"}, {"id": ["ApkVersionKey", "APK Version: {VersionName}"], "message": "APK Version: {VersionName}", "translation": "APK 版本: {VersionName}", "placeholders": [{"id": "VersionName", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "versionName"}]}, {"id": ["TryFindingDeviceKey", "Trying finding device..."], "message": "Trying finding device...", "translation": "正在查找设备...", "comment": "log"}, {"id": ["FindDeviceSuccessKey", "Find device success"], "message": "Find device success", "translation": "查找设备成功"}, {"id": ["FindAdbErrorKey", "Find adb error: {Error}"], "message": "Find adb error: {Error}", "translation": "查找 Adb 错误: {Error}", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}]}, {"id": ["FindDeviceError<PERSON>ey", "Find device error: {Error}"], "message": "Find device error: {Error}", "translation": "查找设备错误: {Error}", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}]}, {"id": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Current selected device: {Selected}"], "message": "Current selected device: {Selected}", "translation": "当前选中的设备: {Selected}", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["NoDeviceSelectedKey", "No device selected"], "message": "No device selected", "translation": "没有选中设备"}, {"id": ["TryConnectDeviceKey", "Trying connect device..."], "message": "Trying connect device...", "translation": "正在连接设备..."}, {"id": ["ConnectDeviceErrorKey", "Connect device {Selected} error: {ErrorMsg}"], "message": "Connect device {Selected} error: {ErrorMsg}", "translation": "连接设备 {Selected} 错误: {ErrorMsg}", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}]}, {"id": ["ConnectDeviceSuccessKey", "Connect device {Selected} success"], "message": "Connect device {Selected} success", "translation": "连接设备 {Selected} 成功", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["TryQueryBuildInfoKey", "Trying query {Selected} build info..."], "message": "Trying query {Selected} build info...", "translation": "正在查询 {Selected} 构建信息...", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["QueryBuildInfoErrorKey", "Query {Selected} build info error: {ErrorMsg}"], "message": "Query {Selected} build info error: {ErrorMsg}", "translation": "查询 {Selected} 构建信息错误: {ErrorMsg}", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}]}, {"id": ["QueryBuildInfoSuccessKey", "Query {Selected} build info success"], "message": "Query {Selected} build info success", "translation": "查询 {Selected} 构建信息成功", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["SendRequestFailKey", "Send request failed: {Error}"], "message": "Send request failed: {Error}", "translation": "发送请求失败: {Error}", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}]}, {"id": ["QueryAppConfigErrorKey", "Query {Selected} app config error: {ErrorMsg}"], "message": "Query {Selected} app config error: {ErrorMsg}", "translation": "查询 {Selected} App 配置错误: {ErrorMsg}", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}]}, {"id": ["QueryAppConfigSuccessKey", "Query {Selected} app config success"], "message": "Query {Selected} app config success", "translation": "查询 {Selected} App 配置成功", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["TrySetAppConfigKey", "Trying set {Selected} app config..."], "message": "Trying set {Selected} app config...", "translation": "正在设置 {Selected} App 配置...", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["SetAppConfigErrorKey", "Set {Selected} app config error: {ErrorMsg}"], "message": "Set {Selected} app config error: {ErrorMsg}", "translation": "设置 {Selected} App 配置错误: {ErrorMsg}", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}]}, {"id": ["SetAppConfigSuccessKey", "Set {Selected} app config success"], "message": "Set {Selected} app config success", "translation": "设置 {Selected} App 配置成功", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["TryUninstallAppKey", "Trying uninstall {Selected} app..."], "message": "Trying uninstall {Selected} app...", "translation": "正在卸载 {Selected} App...", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["UninstallAppErrorKey", "Uninstall {Selected} app error: {ErrorMsg}"], "message": "Uninstall {Selected} app error: {ErrorMsg}", "translation": "卸载 {Selected} App 错误: {ErrorMsg}", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}]}, {"id": ["UninstallAppSuccessKey", "Uninstall {Selected} app success"], "message": "Uninstall {Selected} app success", "translation": "卸载 {Selected} App 成功", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["ApkFileVersionIsUnknownKey", "Apk file version is unknown, are you sure to install?"], "message": "Apk file version is unknown, are you sure to install?", "translation": "Apk 文件版本未知, 是否安装?"}, {"id": ["AppAndFileVersionIsSameKey", "Current app and apk file version is same, are you sure to install?"], "message": "Current app and apk file version is same, are you sure to install?", "translation": "当前已安装 App 和 Apk 文件版本相同, 确定安装吗?"}, {"id": ["ApkFileVersionIsSmallerThanAppKey", "Apk file Version is smaller than current app, are you sure to downgrade?"], "message": "Apk file Version is smaller than current app, are you sure to downgrade?", "translation": "apk 文件版本小于当前已安装 App的版本, 确定降级吗?"}, {"id": ["AppVersionIsSmallerThanApkKey", "Current app version is smaller than apk file, do you want to upgrade?"], "message": "Current app version is smaller than apk file, do you want to upgrade?", "translation": "当前已安装 App 版本小于 apk 文件的版本, 是否升级?"}, {"id": ["TryInstallApkKey", "Trying install new apk to {Selected}, apk path: {Path}"], "message": "Trying install new apk to {Selected}, apk path: {Path}", "translation": "正在安装新的 apk 到 {Selected}, apk 路径: {Path}", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Path", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "path"}]}, {"id": ["ReTryInstallApkKey", "Re-Trying install apk ..."], "message": "Re-Trying install apk ...", "translation": "正在重试安装 apk ..."}, {"id": ["InstallApkErrorKey", "Install apk to {Selected} error: {ErrorMsg}"], "message": "Install apk to {Selected} error: {ErrorMsg}", "translation": "安装 apk 到 {Selected} 错误: {ErrorMsg}", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}, {"id": "Error<PERSON><PERSON>", "string": "%[2]s", "type": "string", "underlyingType": "string", "argNum": 2, "expr": "errorMsg"}]}, {"id": ["InstallApkSuccessKey", "Install apk to {Selected} success"], "message": "Install apk to {Selected} success", "translation": "安装 apk 到 {Selected} 成功", "placeholders": [{"id": "Selected", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "a.selected"}]}, {"id": ["PushingApkKey", "Pushing APK..."], "message": "Pushing APK...", "translation": "正在推送APK..."}, {"id": ["PushApkErrorKey", "Push APK error: {Error}"], "message": "Push APK error: {Error}", "translation": "推送APK错误: {Error}", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}]}, {"id": ["PushApkSuccessKey", "Push APK success"], "message": "Push APK success", "translation": "推送APK成功", "placeholders": [{"id": "FileName", "string": "%[1]v", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "fileName"}]}, {"id": ["ChangeAdbPathErrorKey", "Change Adb Path error: {Error}"], "message": "Change Adb Path error: {Error}", "translation": "修改 Adb 路径错误: {Error}", "placeholders": [{"id": "Error", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "err.<PERSON><PERSON><PERSON>()"}]}, {"id": ["ChangeAdbPathSuccessKey", "Change Adb Path success"], "message": "Change Adb Path success", "translation": "修改 Adb 路径成功"}, {"id": ["AdbNotFoundKey", "Adb not found"], "message": "Adb not found", "translation": "Adb 程序未找到"}, {"id": ["OpenAdbProgramByYourself", "In order to continue, please manually select the location of the adb program"], "message": "In order to continue, please manually select the location of the adb program", "translation": "为了继续操作，请手动选择adb程序所在位置"}, {"id": ["AskIfInstallApkKey", "Apk is not installed, do you want to install it?"], "message": "Apk is not installed, do you want to install it?", "translation": "Apk 程序未安装，是否要安装?"}, {"id": ["ApkPathKey", "Apk Path: {ApkPath}"], "message": "Apk Path: {ApkPath}", "translation": "Apk 路径: {ApkPath}", "placeholders": [{"id": "ApkPath", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "apkPath"}]}, {"id": ["NotCheckUnknownApkVersionKey", "Apk version is unknown, not check"], "message": "Apk version is unknown, not check", "translation": "Apk版本未知，不检查"}]}