package main

import (
	_ "embed"
	"flag"
	"log"
	"log/slog"
	"strconv"

	"appconfig/env"
	"appconfig/gui"

	"github.com/Unknwon/goconfig"
	"github.com/joho/godotenv"
	. "modernc.org/tk9.0"
	_ "modernc.org/tk9.0/themes/azure"
)

//go:embed bfdx-icon.png
var ico []byte

//go:embed version.txt
var version string

func main() {
	debugPtr := flag.Bool("debug", false, "debug program,read .env form bf8100deviceapp")
	flag.Parse()

	if *debugPtr {
		env.IsDebug = true

		envMap, err := godotenv.Read("../.env")
		if err != nil {
			slog.Debug("load env file err:", err)
		}
		host := envMap["SERVER_HOST"]
		if host != "" {
			env.ProxyHttpHost = host
		}
		port, _ := strconv.Atoi(envMap["SERVER_PORT"])
		if port > 0 {
			env.ProxyHttpPort = port
		}
	}

	env.Version = version
	loadConfig()

	// 设置图标
	App.IconPhoto(NewPhoto(Data(ico)))

	_ = ActivateTheme("azure light")

	WmGeometry(App, "1200x770")
	WmMinSize(App, 520, 770)

	app := gui.NewApp()

	app.Log("poc config version: " + version)

	App.Wait()
}

func loadConfig() {
	ini, err := goconfig.LoadConfigFile("config.ini")
	if err != nil {
		log.Println("load config file err:", err)
		return
	}

	adbPath, err := ini.GetValue("default", "adb_path")
	if err != nil {
		log.Println("load DefaultAdbPath err:", err)
		return
	}
	env.DefaultAdbPath = adbPath

	lang, err := ini.GetValue("default", "language")
	if err != nil {
		log.Println("load DefaultLanguage err:", err)
		return
	}
	env.DefaultLanguage = lang

	serverHost, err := ini.GetValue("default", "server_host")
	if err != nil {
		log.Println("load DefaultServerHost err:", err)
		return
	}
	env.DefaultServerHost = serverHost

	serverPort, err := ini.Int("default", "server_port")
	if err != nil {
		log.Println("load DefaultServerPort err:", err)
		return
	}
	env.DefaultServerPort = serverPort

	apkPath, err := ini.GetValue("default", "apk_path")
	if err != nil {
		log.Println("load DefaultApkPath err:", err)
		return
	}
	env.DefaultApkPath = apkPath
}
