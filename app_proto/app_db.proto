syntax = "proto3";
package app_proto;

option go_package = "app_proto";

message db_org_list {
  repeated db_org rows=1;
}

message db_device_list {
  repeated db_device rows=1;
}

//组织架构表
message db_org {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table varchar(64) unique not null
  //组织机构编号
  string org_self_id = 3;


  //@table varchar(32) unique not null
  //机构名称,缩写
  string org_short_name = 5;


  //@table int default 2
  //2:真实机构,1:虚拟机构 虚拟机构没有真实设备和用户,只是引用真实机构的数据,相当于额外的分组
  int32 org_is_virtual = 8;

  //@table varchar(8) unique
  //DMR ID,可用作组呼的ID
  string dmr_id = 9;

  //@table uuid not null default '11111111-1111-1111-1111-111111111111'
  //此组织的上级机构device
  string parent_org_id = 11;
}

//对讲机设备表
message db_device {
  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  //设备所属的群组
  string org_id = 3;

  //@table varchar(16) not null unique
  //设备名称
  string self_id = 4;

  //@table varchar(16) not null unique
  //设备DMR-ID
  string dmr_id = 5;

  //@table int not null default 0
  //设备类型 0:对讲机手台 1：车台 3:电话网关设备 4:中继虚拟终端 5:互联网关终端 6:模拟网关终端 7:数字网关终端
  int32 device_type = 9;

  //@table int
  //优先级
  int32 priority = 12;
}
