syntax = "proto3";

package app_proto;

import "app_db.proto";

option optimize_for = LITE_RUNTIME;

option go_package = "app_proto";

//option go_package = "git.kicad99.com/bfdx/bf8100deviceapp/goproxy/app_proto";

message req_login {
  string sys_id = 1;
  string user_name = 2;
  //用户密码 / session
  string user_pass = 3;
  //登录系统方法,0:使用密码登录,1:使用sid登录
  int32 login_method = 4;
  //can display map
  bool can_display_map = 5;
  // 0:ambe, 1:opus
  int32 prefer_codec = 6;
}

message resp_login {
  bool is_server_support_map = 1;
  //服务器版本号
  string server_version = 2;
  //配置最后更新时间,utc时间
  //目前只有poc终端有此字段
  string setting_last_update_time = 3;
  // 当前登录的终端设备
  app_proto.db_device device = 4;
}

//apply for device gps info permission
message req_gps_permission {
  //target device dmrid
  uint32 dmrid = 1;
  //applier device dmrid
  uint32 apply_dmrid = 2;
}

//got apply response for device gps info permission
message res_gps_permission {
  //response code
  //0:ok 4:reject
  int32 code = 1;
  //device dmrid
  uint32 dmrid = 2;
  //who grant the permission, user rid
  string grant_user_rid = 3;
  //grant user name
  string grant_user_name = 4;
  //permission expire time
  string expire_time = 5;
}

//一个通用的gps84定位数据
message gps84 {
  string gps_time = 1;
  int32 av = 2; //invalid=0,gps valid =1,last_gps_data=2
  double lat = 3;
  double lon = 4;
  double speed = 5; //km/h
  int32 direction = 6; //north=0
  int32 altitude = 7;
}

//got device gps info
message gps_info {
  //device dmrid
  string dmrid = 1;

  //device gps info
  gps84 gps_info = 3;

  // 1为用户主动定位
  int32 active_status = 4;
}

message req_update_listen_group_list {
  repeated string listen_group_list = 1;
}

message req_update_speak_target {
  string speak_target = 1;
}

//包含 单位 动态组 终端设备
message address_book {
  //上级单位的dmrid，如果对应是单位则不需要层层嵌套
  string parent_dmrid = 1;

  string dmrid = 2;
  //设备名称
  string name = 3;
  //单位类型 111:虚拟机构  112:真实机构 100:临时组  101：任务组  102:自动失效的临时组
  //设备类型 0:对讲机手台 1：车台 2:指挥座席 3:电话网关设备 4:中继虚拟终端 5:互联网关终端 6:模拟网关终端 7:数字网关终端 21:基地台  22:android模拟终端
  int32 devType = 4;
  //单位排序值
  int32 org_sort_value = 5;
}

message req_address_book_result {
  repeated address_book success_list = 1;
  //失败的dmrid
  repeated string failed_list = 2;
}

message address_book_list {
  repeated address_book addr_book_list = 1;
}

message server_addr {
  string host = 1;
  int32 port = 2;
}

message short_messages {
  //发起者dmrid
  string sender_dmrid = 4;

  //接收方dmrid
  string target_dmrid = 5;

  //短信内容 utf16 编码
  string sms_content = 7;

  //短信序号
  int32 sms_no = 8;

  //编码格式 2.utf16(目前手台只支持此编码)
  int32 codec = 10;

  //短信类型 0:普通短信 1:自动播报短信
  int32 sms_type = 11;

  //接受时间
  int64 time = 12;
}

message Notify {
  //1.打开麦克风失败 2.kcp断开  5,kcp链接服务器失败
  //6.播放语音历史开始(paraStr为当前播放历史记录"repeaterIdHex-sourceHex-timeStampHex")  7.播放语音历史结束
  //9.录音历史下载失败
  // 0xcb36.加入临时组  0xcb37.退出临时组 0xcb38.加入任务组  0xcb39.退出任务组
  //333.sync online devices,body = bfkcp.ex_oneline_devices
  //8.收到他人讲话通知。非bc15.body = bfkcp.dev_data_info
  //11.正在播放他人语音 paramStr=sourceHex-targetHex 12.正在播放语音历史。paramStr=repeaterIdHex-sourceHex-timeStampHex
  //20.收到gps定位信息, body = gps_info
  int32 code = 1;

  //额外的附加信息
  //0xcb37/0xcb39 groupDmrid
  string paramStr = 2;

  //code = 0xcb38,body = AddressBook
  //code = 0xcb36,body = AddressBook
  bytes body = 3;
}

message voice_config {
  //声音放大参数 //基准为1000  [500,10000]测试一下
  int32 gain = 3;

  //播放媒体缓冲区（*个包） 一个包 60ms
  //范围 [8,25]  即 约[0.5,1.5]秒
  //超过这个大小，自动开始 播放
  int32 mediaAutoStartSize = 4;

  //媒体总缓冲区大小（*个包） 一个包 60ms
  //范围 [250,500] 即[15,30]秒
  int32 mediaBufferSize = 5;

  //讲话超时 *秒
  int32 speakTimeout = 6;

  //parser = sn. eg: 0x1123,0x0012,0xCCFF -> 11220012CCFF
  //string sn = 7;

  //denoise setting
  //0: off, 1: ambe denoise 2:rnnoise
  int32 denoiseSetting = 8;

  //debug recorder pcm_data
  //0: off 1:on
  int32 debugRecorderPcm = 9;

  //recorder pcm gain
  //xxx% 10-1000
  int32 recorderPcmGain = 10;

}

enum media_status {
  start = 0;
  stoped = 1;
}

enum res_code {
  success = 0;
  fialed = 1;
}

//rpc 结构见 bf8100_project 的 bf8100.proto 中的 rpc_cmd
//默认req 使用的 message对应enum cmd_后面的部分
//如：cmd_req_ping的body 对应message req_login
//回应对body和res有对应介绍
//回应rpc.res = res_code.fialed 时候 para_str 携带错误原因
enum cmd_code {
  cmd_req_ping = 0;
  //rpc.res = 1
  cmd_resp_ping = 1;

  //登录
  cmd_req_login = 2;
  //rpc.res = 登录回应值 0:登录成功， 1:重复登录 10:登录失败,密码/session校验错误 303: 用户没有指定设备
  //rpc.para_str 携带 sid
  //rpc.para_int 0:正常登录  1:自动重新登录
  //rpc.para_bin 携带登录额外信息。对应bf8100_project/bf8100.proto 中 res_login_para_bin
  cmd_resp_login = 3;

  //更新收听组
  //@Deprecated - use cmd_req_delete_listen_group/cmd_req_add_listen_group replace this cmd
  cmd_req_update_listen_group_list = 4;
  //rpc.res = res_code
  cmd_resp_update_listen_group_list = 5;

  //查询收听组
  cmd_req_query_listen_group_list = 6;
  //rpc.body = req_update_listen_group_list    亦可由proxy主动推送
  cmd_resp_query_listen_group_list = 7;

  //更新发射目标
  cmd_req_update_speak_target = 8;
  //rpc.res = res_code
  cmd_resp_update_speak_target = 9;

  //查询发射目标
  cmd_req_query_speak_target = 10;
  //rpc.body = req_update_speak_target  亦可由proxy主动推送
  cmd_resp_query_speak_target = 11;

  //查询最新通讯录,不需要填body
  cmd_req_query_address_book = 12;
  //rpc.body = address_book_list  亦可由proxy主动推送
  //rpc.res = 1,则为推送未知用户的数据信息（不可存放至通讯录,仅用于展示）
  cmd_resp_query_address_book = 13;

  //开始通话,不需要填body
  // rpc.para_str = dmrid
  cmd_req_speak_start = 14;
  //rpc.body = bfkcp.cb71
  //res = 1.无效的通话目标
  //如果当前已经开始录音，则回应 res=2,rpc.body = bfkcp.cb71，cb71中仅source/target两个字段有效
  //如果未设置sn，则 res=3
  // res=4,当前被禁发
  cmd_resp_speak_start = 15;
  //停止通话,不需要填body
  cmd_req_speak_stop = 16;
  //rpc.res = res_code
  cmd_resp_speak_stop = 17;
  //查询通话状态,不需要填body
  cmd_req_speak_status = 18;
  //rpc.res = media_status
  cmd_resp_speak_status = 19;

  //查询是否处于放音状态,不需要填body
  cmd_req_media_play_status = 20;
  //rpc.res = media_status,不需要填body 亦可由proxy通知处于放音状态
  //rpc.body = bc15
  cmd_resp_media_play_status = 21;

  //由proxy 主动发送，携带通知客户端的事件
  //body = Notify
  cmd_notify = 22;

  //由proxy通知有新的短信
  cmd_short_messages = 24;
  //确认收到短信  rpc.body = short_messages. 只需要填写sender_dmrid、target_dmrid、sms_no
  cmd_resp_confirm_short_messages = 25;

  //发送短信消息  rpc.body = short_messages
  cmd_send_short_messages = 26;
  //rpc.res = smsNo //后台发送成功回应
  cmd_resp_send_short_messages = 27;

  //查询是否登录 不需要body
  cmd_req_is_login = 28;
  //rpc.res = 0.未登录  1.已经登录  2.已经登录但kcp断开
  cmd_resp_is_login = 29;

  //查询kcp是否链接服务器 不需要body
  cmd_req_is_conn_server = 30;
  //rpc.res = res_code
  cmd_resp_is_conn_server = 31;

  //更新kcp链接服务器的ipAddr
  cmd_req_update_server_addr = 32;
  //rpc.res = res_code
  cmd_resp_update_server_addr = 33;

  //不需要body
  cmd_req_query_login_user = 34;
  //rpc.body req_login
  cmd_resp_query_login_user = 35;

  //不需要body
  cmd_req_query_login_dev = 36;
  //rpc.Res = DevicePriority  rpc.ParaStr = DevDMRID
  cmd_resp_query_login_dev = 37;

  //不需要body  设置单次讲话超时  rpc.res=n 秒  [0,300]
  cmd_req_set_speak_time_out_duration = 38;
  //rpc.res = res_code
  cmd_resp_set_speak_time_out_duration = 39;

  //不需要body  获取单次讲话超时
  cmd_req_get_speak_time_out_duration = 40;
  //rpc.res = n 秒
  cmd_resp_get_speak_time_out_duration = 41;

  //body = voice_config
  cmd_req_update_voice_config = 42;
  cmd_resp_update_voice_config = 43;

  //部分删除收听组  一次请求 listenGroup个数不能超过过20个
  cmd_req_delete_listen_group = 52;
  //rpc.res = res_code | 后台服务器 -1000=ErrUnmarshal
  cmd_resp_delete_listen_group = 53;

  //部分增加收听组  一次请求 listenGroup个数不能超过过20个
  cmd_req_add_listen_group = 54;
  //rpc.res = res_code | 后台服务器 -1000=ErrUnmarshal
  cmd_resp_add_listen_group = 55;

  //退出登录
  cmd_req_login_quit = 56;
  //rpc.res = res_code
  cmd_resp_login_quit = 57;

  //查询默认发射目标
  cmd_req_query_default_speak_target = 58;
  //rpc.body = req_update_speak_target  亦可由proxy主动推送
  cmd_resp_query_default_speak_target = 59;

  //同步后台dev数据
  cmd_req_query_default_dev_config = 60;
  //proxy的响应
  //rpc.res = res_code
  //parmStr:错误原因
  cmd_resp_query_default_dev_config = 61;

  //申请播放指定的缓存语音消息
  //ParaStr="repeaterIdHex-sourceHex-timeStampHex" .e.g CCCCCCCC-MMMMMMMM-HHMMSSddmmyyyy
  //时间戳不需要前补0
  cmd_req_play_local_cache_media = 62;
  //rpc.res = 0.本地拥有数据，即将播放 1.本地没有数据，需要下载 只是回应，具体的播放需要notify通知  2.拒绝播放，当前正处于录音或放音状态  3.无效的sn
  cmd_resp_play_local_cache_media = 63;

  //请求回呼目标
  cmd_req_query_call_back_target = 64;
  //res = call_back_target，亦可由proxy主动通知,回呼目标为""表示没有回呼目标
  cmd_resp_query_call_back_target = 65;

  //请求清空回呼目标
  cmd_req_clear_call_back_target = 66;
  //rpc.res = res_code
  cmd_resp_clear_call_back_target = 67;

  //停止播放缓存语音数据
  cmd_req_stop_play_local_cache_media = 68;
  //rpc.res = res_code 只是回应，具体的播放结束需要notify通知. 若res=2 则表示当前未播放
  cmd_resp_stop_play_local_cache_media = 69;

  //设置媒体处理软体：  res= 1.NDK  2.JDK
  //默认设置为NDK
  cmd_req_set_media_software = 70;
  //rpc.res = res_code
  cmd_resp_set_media_software = 71;

  //rpc.paramStr = "dmriHex,dmridHex,..."
  cmd_req_query_addr_book_by_dmrid = 72;
  //rpc.body = app_proto.req_address_book_result
  //rpc.res=-1 req_address_book_result marshal出错
  //客户端查询结果
  cmd_resp_query_addr_book_by_dmrid = 73;
  //rpc.body = app_proto.req_address_book_result
  //rpc.res=-1 req_address_book_result marshal出错
  //proxy主动查询结果
  cmd_resp_query_addr_book_by_dmrid_proxy = 74;

  //客户端请求map token
  cmd_req_map_token = 75;
  //客户端响应map token
  //rpc_cmd.para_int = map token
  //rpc_cmd.res = res_code
  //rpc_cmd.para_str = error message
  cmd_resp_map_token = 76;

  //req gps location once
  cmd_req_gps_location_once = 77;

  //req gps location on
  cmd_req_gps_location_on = 78;

  //req gps location off
  cmd_req_gps_location_off = 79;

  //175命令 对应 bf8100.proto 中的cb75

  //broadcast pcm data
  //rpc_cmd.para_int=samplerate
  //rpc_cmd.body=pcm_data
  cmd_got_pcm_data = 80;

  // request gps location broadcast of a specific device
  // rpc_cmd.body = req_gps_permission
  cmd_req_device_gps_location_permission = 81;
  // response gps location broadcast of a specific device
  // rpc_cmd.body = res_gps_permission
  cmd_resp_device_gps_location_permission = 82;
  //query gps permission
  // rpc_cmd.body = req_gps_permission
  cmd_req_query_device_gps_location_permission = 83;
  //response query gps permission
  // rpc_cmd.body = res_gps_permission
  cmd_resp_query_device_gps_location_permission = 84;

  //退出程序
  cmd_exit = 1001;
  //被强制下线
  cmd_force_exit = 444;
  //转发服务器bc15指令，body=bc15
  cmd_bc15 = 445;

  /**
   * 以下为poc终端专用命令
   */

  // 请求poc通讯录
  cmd_req_query_contact = 1100;
  // 回应poc通讯录
  // rpc_cmd.res = res_code
  // rpc_cmd.para_int = 1, rpc_cmd.body = db_org_list
  // rpc_cmd.para_int = 2, rpc_cmd.body = db_device_list
  cmd_resp_query_contact = 1101;

  // 查询默认发射组和收听组
  cmd_req_query_poc_default_group = 1102;
  // 回应默认发射组和收听组
  // rpc_cmd.res = res_code
  // rpc_cmd.para_int = 3, rpc_cmd.body = PocDefaultGroup
  cmd_resp_query_poc_default_group = 1103;

  // poc更新收听组
  // rpc_cmd.para_bin = PocSubscribleUpdateOption
  // rpc_cmd.body = PocDefaultGroup
  cmd_req_update_poc_listen_group = 1104;
  // rpc_cmd.res = res_code
  cmd_resp_update_poc_listen_group = 1105;

  // poc查询当前收听组
  cmd_req_query_poc_listen_group = 1106;
  // rpc_cmd.res = res_code
  // rpc_cmd.body = PocDefaultGroup
  cmd_resp_query_poc_listen_group = 1107;

  // 请求权限外的通讯录数据，只作通话消息显示
  // rpc_cmd.ParaStr = hex dmrId
  cmd_req_query_outside_permission_contact = 1108;
  // rpc_cmd.res = res_code
  // rpc_cmd.ParaStr = hex dmrId
  // rpc_cmd.body = db_org or db_device
  cmd_resp_query_outside_permission_contact = 1109;

  // 收到更新通讯录通知(调度台修改了通讯录)
  // 收到此通知后，应该更新通讯录 cmd_req_query_contact
  cmd_notify_poc_setting_changed = 1111;

  // 发送报警
  // rpc_cmd.ParaStr = device Org hex dmrId
  cmd_req_send_alarm = 1113;
  // rpc_cmd.res = res_code
  cmd_resp_send_alarm = 1114;

  // 转发服务器cb10指令 用于解除报警
  cmd_cb10 = 1115;

  // 通知设备状态变更
  // rpc_cmd.ParaBin = []byte 6个字节
  cmd_notify_device_status = 1116;
  // 系统下发遥开、遥闭命令操作，锁机状态参数
  // YN: 00=开机；01=锁机；02=查询参数；其它无效
  // ST: 00=开机；01=禁听锁机,02=禁发锁机,03=禁发禁听锁机。
  // rpc_cmd.ParaBin = [YN, ST]
  cmd_notify_lock_device_status = 1117;
  // 请求poc配置
  cmd_req_query_poc_config = 1118;
  // 返回poc配置
  cmd_resp_query_poc_config = 1119;

  //rpc.res = res_code
  // seq_no:请求序列号,响应和请求一一对应
  //parmStr:错误原因
  cmd_req_query_app_config = 10001;
  cmd_resp_query_app_config = 10002;
  cmd_req_set_app_config = 10003;
  cmd_resp_set_app_config = 10004;

  // 查询在线终端
  cmd_req_query_online_contact = 1120;
  // 查询在线终端 / 终端列表
  // rpc_cmd.res = res_code
  // rpc_cmd.para_int = 11, rpc_cmd.res=1, rpc_cmd.body=cc183, cc183.action_code=12, cc183.dmrids为在线终端
  // 如果没有在线终端，rpccmd.body=空
  cmd_resp_query_online_contact = 1121;
  // rpc_cmd.Body = PocConfig
  cmd_sync_poc_config_to_proxy = 1122;

  // 登录超时指令，通知到goproxy
  // rpc_cmd.ParaStr = hex dmrId
  cmd_notify_login_timeout = 1124;
  // 登录成功，初始化数据完成，通知到goproxy
  cmd_notify_init_data_finish = 1125;
}
