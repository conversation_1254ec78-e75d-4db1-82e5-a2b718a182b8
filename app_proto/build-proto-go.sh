#!/bin/sh

cd "$(dirname "$0")"

# build go app_proto package
appProtoOutDir=../goproxy/app_proto/
mkdir -p $appProtoOutDir
#go gen msg
protoc --experimental_allow_proto3_optional -I=. --gogofaster_out=$appProtoOutDir app_proto.proto app_db.proto app_config.proto

#build go bfkcp package
bfkcpOutDir=../goproxy/bfkcp
mkdir -p $bfkcpOutDir
#go gen msg
protoc --experimental_allow_proto3_optional -I=. --gogofaster_out=$bfkcpOutDir bf8100.proto

#build go bfdx_proto package
bfdxProtoOutDir=../goproxy/bfdx_proto
mkdir -p $bfdxProtoOutDir
#go gen msg
protoc --experimental_allow_proto3_optional -I=. --gogofaster_out=$bfdxProtoOutDir bf_radio.proto db.proto db.pb.list.proto
