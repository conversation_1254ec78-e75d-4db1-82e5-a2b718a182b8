syntax = "proto3";
package bfdx_proto;

import "db.proto";

//gen by /tmp/db.pb.go
//DO NOT EDIT THIS FILE!!!

// 系统设置表
message db_sys_config_list {
repeated db_sys_config rows=1; 
}

// 表各种操作时间// 客户端有时需要查询后台是否已经更新了数据,可以通过此表来得到初步的信息
message db_table_operate_time_list {
repeated db_table_operate_time rows=1; 
}

// 组织架构表
message db_org_list {
repeated db_org rows=1; 
}

// 用户的一些图片数据,地图点icon等
message db_image_list {
repeated db_image rows=1; 
}

// 基站列表
message db_base_station_list {
repeated db_base_station rows=1; 
}

// 控制器设备表
message db_controller_list {
repeated db_controller rows=1; 
}

// 控制器状态
message db_controller_last_info_list {
repeated db_controller_last_info rows=1; 
}

// 控制器上线历史表,按月分表
message db_controller_online_history_list {
repeated db_controller_online_history rows=1; 
}

// 电话网关黑白名单
message db_phone_gateway_filter_list {
repeated db_phone_gateway_filter rows=1; 
}

// 对讲机设备表
message db_device_list {
repeated db_device rows=1; 
}

// 对讲机最后的数据信息
message db_device_last_info_list {
repeated db_device_last_info rows=1; 
}

// 用户职称表
message db_user_title_list {
repeated db_user_title rows=1; 
}

// 用户数据表
message db_user_list {
repeated db_user rows=1; 
}

// 用户群组权限表
message db_user_privelege_list {
repeated db_user_privelege rows=1; 
}

// 用户登录的session id表
message db_user_session_id_list {
repeated db_user_session_id rows=1; 
}

// 虚拟群组信息表
message db_virtual_org_list {
repeated db_virtual_org rows=1; 
}

// 用户自己的一些地图标志
message db_map_point_list {
repeated db_map_point rows=1; 
}

// 巡查线路点
message db_line_point_list {
repeated db_line_point rows=1; 
}

// 巡查点最新信息
message db_line_point_latest_info_list {
repeated db_line_point_latest_info rows=1; 
}

// 巡查线路主表
message db_line_master_list {
repeated db_line_master rows=1; 
}

// 巡查线路细表
message db_line_detail_list {
repeated db_line_detail rows=1; 
}

// 巡查规则表
message db_rfid_rule_master_list {
repeated db_rfid_rule_master rows=1; 
}

// 开关机数据表,按月分表
message db_device_power_onoff_list {
repeated db_device_power_onoff rows=1; 
}

// 上班下班打卡数据表,按月分表
message db_user_check_in_history_list {
repeated db_user_check_in_history rows=1; 
}

// rfid巡查历史表,按月分表
message db_rfid_history_list {
repeated db_rfid_history rows=1; 
}

// gps位置历史表,按月分表
message db_gps_history_list {
repeated db_gps_history rows=1; 
}

// 报警历史,要分表了,因为报警可能会非常多,客户端需要编辑此表,分表客户端处理需要特殊处理
message db_alarm_history_list {
repeated db_alarm_history rows=1; 
}

// 对讲机通话历史,按月分表
message db_sound_history_list {
repeated db_sound_history rows=1; 
}

// 还没发送的命令
message db_not_send_cmd_list {
repeated db_not_send_cmd rows=1; 
}

// 已经发送的命令列表
message db_sent_cmd_history_list {
repeated db_sent_cmd_history rows=1; 
}

// 对讲机注册信息
message db_device_register_info_list {
repeated db_device_register_info rows=1; 
}

// 通话调度/切换信道历史表,按月分表
message db_call_dispatch_history_list {
repeated db_call_dispatch_history rows=1; 
}

// 基站调度历史
message db_conf_dispatch_history_list {
repeated db_conf_dispatch_history rows=1; 
}

// 未确认短信表
message db_not_confirm_sms_list {
repeated db_not_confirm_sms rows=1; 
}

// 短信历史表,短信一般很少,不分表处理了
message db_sms_history_list {
repeated db_sms_history rows=1; 
}

// 频道物理数据// todo 此处信息还需要商议下才能确定
message db_ch_rf_setting_list {
repeated db_ch_rf_setting rows=1; 
}

// 写频配置文件
message db_device_setting_conf_list {
repeated db_device_setting_conf rows=1; 
}

// 电话网关短号
message db_phone_short_no_list {
repeated db_phone_short_no rows=1; 
}

// 电话网关使用授权
message db_phone_gateway_permission_list {
repeated db_phone_gateway_permission rows=1; 
}

// 电话网关设备关系管理
message db_controller_gateway_manage_list {
repeated db_controller_gateway_manage rows=1; 
}

// 预定义电话号码本
message db_phone_no_list_list {
repeated db_phone_no_list rows=1; 
}

// 有源点报警历史
message db_linepoint_alarm_history_list {
repeated db_linepoint_alarm_history rows=1; 
}


message db_device_channel_zone_list {
repeated db_device_channel_zone rows=1; 
}

// 用户crud log表
message db_crud_log_list {
repeated db_crud_log rows=1; 
}

// 动态组成员详细信息
message db_dynamic_group_detail_list {
repeated db_dynamic_group_detail rows=1; 
}

// 物联网终端
message db_iot_device_list {
repeated db_iot_device rows=1; 
}

// iot限制
message db_iot_restriction_list {
repeated db_iot_restriction rows=1; 
}

// 物联终端最后的数据信息
message db_iot_device_last_info_list {
repeated db_iot_device_last_info rows=1; 
}

// iot_data历史表,按月分表
message db_iot_data_history_list {
repeated db_iot_data_history rows=1; 
}

// 设备固定订阅/静态收听表
message db_static_subscribes_list {
repeated db_static_subscribes rows=1; 
}

// app用户地图显示中特别许可的其它终端列表
message db_app_map_privilege_device_list {
repeated db_app_map_privilege_device rows=1; 
}

// poc session id表
message db_poc_session_list {
repeated db_poc_session rows=1; 
}

