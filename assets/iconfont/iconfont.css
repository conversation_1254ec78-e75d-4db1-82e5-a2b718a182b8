@font-face {
  font-family: "aliyunIconfont"; /* Project id 4794540 */
  src: url('iconfont.woff2?t=1737170239816') format('woff2'),
       url('iconfont.woff?t=1737170239816') format('woff'),
       url('iconfont.ttf?t=1737170239816') format('truetype');
}

.aliyunIconfont {
  font-family: "aliyunIconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-disconnect2:before {
  content: "\e645";
}

.icon-dynamic-group:before {
  content: "\e6bc";
}

