#!/usr/bin/env bash

cd "$(dirname "$0")"

# 解析参数
while getopts "dh" flag
do
    case "${flag}" in
        # d) debug=${OPTARG};;
        d) debug=1;;
        h) help=1;;
    esac
done

# 打印帮助信息
if [ "$help" == "1" ];then
  echo "Usage: ./build-android.sh [options]; Default to build release app"
  echo "Options:"
  echo "-d        Build debug app"
  echo "-h        Print help info"

  exit 0;
fi

./update_version.sh

# flutter env
if [[ ! -f ".env" ]]; then
  echo "not found .env file!!! generate .env with default value"
  echo "SERVER_PROTOCOL=ws
SERVER_HOST=127.0.0.1
SERVER_PORT=12255
SERVER_PROXY=proxy" > .env

fi

# 编译android abi依赖
cd ./goproxy/libproxy
./build-android.sh
cd -

flutter pub get

# 编译android apk
#cd ./android
#if [ "$debug" == "1" ];then
#  echo "start gradlew assembleDebug"
#  ./gradlew assembleDebug
#else
#  echo "start gradlew assembleRelease"
#  ./gradlew assembleRelease
#fi

if [ "$debug" == "1" ];then
  echo "start flutter build apk"
  flutter build apk --debug
else
  echo "start flutter build apk --release"
  flutter build apk --release
fi
