const fs = require('fs');
const path = require('path');

const assets = path.resolve(__dirname, './assets/iconfont');
const inputFile = `${assets}/iconfont.json`;
const outputPath = path.resolve(__dirname, './lib/iconfont');
let fontFamily = 'iconfont';
let dartFontFamily = 'Iconfont';
let description = '';
let dartFileName = 'iconfont';
let className = 'Iconfont';

function wrapperName(name) {
  let list = name.split('-');
  const firstWord = list.shift();
  list = list.map((s) => {
    const f = s[0];
    return f.toUpperCase() + s.slice(1, s.length);
  });
  list.unshift(firstWord);
  return list.join('');
}

function writeToFile(content) {
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath);
  }
  const outputFullPath = `${outputPath}/${dartFileName}.dart`;

  fs.writeFileSync(outputFullPath, content);
  console.log('writeToFile:', outputFullPath);
}

function generate(content) {
  const descriptionComment = description ? `// ${description}` : '';
  const tpl = `/** 
* Generated code: do not hand-edit!!!
* ${new Date().toString()}
* @auth Linfl
*/

import 'package:flutter/material.dart' show IconData;

// iconfont project address: https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2592144&keyword=&project_type=&page=
${descriptionComment}
class ${className} {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  ${className}._();
  
  ${content.join('\n')}
}
`;

  writeToFile(tpl);
}

function main() {
  try {
    const file = fs.readFileSync(inputFile);
    const jsonData = JSON.parse(file);

    fontFamily = jsonData.font_family;
    dartFontFamily = wrapperName(fontFamily);
    dartFontFamily = dartFontFamily[0].toUpperCase() + dartFontFamily.slice(1, dartFontFamily.length);
    description = jsonData.description;

    const cssPrefixText = jsonData.css_prefix_text;
    const glyphs = jsonData.glyphs;
    const content = [];

    for (let i = 0; i < glyphs.length; i++) {
      const icon = glyphs[i];
      const fontClass = icon.font_class;
      const unicode = icon.unicode;
      const name = wrapperName(fontClass);
      const iconData = `
  /// <i class="${fontFamily} ${cssPrefixText}${fontClass}"></i>;
  /// icon named "${fontClass}".
  static const IconData ${name} = IconData(0x${unicode}, fontFamily: '${dartFontFamily}');`;
      content.push(iconData);
    }

    generate(content);
  } catch (err) {
    console.error('generate iconfont error:', err);
  }
}

main();
