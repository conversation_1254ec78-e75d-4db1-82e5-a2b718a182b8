// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: app_config.proto

package app_proto

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type AppConfig struct {
	Host string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	// hex 8位，不足8位用0填充
	Dmrid             string `protobuf:"bytes,3,opt,name=dmrid,proto3" json:"dmrid,omitempty"`
	Password          string `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	CanEditLoginParam bool   `protobuf:"varint,5,opt,name=can_edit_login_param,json=canEditLoginParam,proto3" json:"can_edit_login_param,omitempty"`
}

func (m *AppConfig) Reset()         { *m = AppConfig{} }
func (m *AppConfig) String() string { return proto.CompactTextString(m) }
func (*AppConfig) ProtoMessage()    {}
func (*AppConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_16ffc0bbf1ea3dff, []int{0}
}
func (m *AppConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AppConfig.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AppConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppConfig.Merge(m, src)
}
func (m *AppConfig) XXX_Size() int {
	return m.Size()
}
func (m *AppConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_AppConfig.DiscardUnknown(m)
}

var xxx_messageInfo_AppConfig proto.InternalMessageInfo

func (m *AppConfig) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

func (m *AppConfig) GetPort() int32 {
	if m != nil {
		return m.Port
	}
	return 0
}

func (m *AppConfig) GetDmrid() string {
	if m != nil {
		return m.Dmrid
	}
	return ""
}

func (m *AppConfig) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *AppConfig) GetCanEditLoginParam() bool {
	if m != nil {
		return m.CanEditLoginParam
	}
	return false
}

type AppBuildInfo struct {
	Version   string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	BuildTime string `protobuf:"bytes,2,opt,name=build_time,json=buildTime,proto3" json:"build_time,omitempty"`
}

func (m *AppBuildInfo) Reset()         { *m = AppBuildInfo{} }
func (m *AppBuildInfo) String() string { return proto.CompactTextString(m) }
func (*AppBuildInfo) ProtoMessage()    {}
func (*AppBuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_16ffc0bbf1ea3dff, []int{1}
}
func (m *AppBuildInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AppBuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AppBuildInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AppBuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppBuildInfo.Merge(m, src)
}
func (m *AppBuildInfo) XXX_Size() int {
	return m.Size()
}
func (m *AppBuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AppBuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AppBuildInfo proto.InternalMessageInfo

func (m *AppBuildInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *AppBuildInfo) GetBuildTime() string {
	if m != nil {
		return m.BuildTime
	}
	return ""
}

func init() {
	proto.RegisterType((*AppConfig)(nil), "app_proto.app_config")
	proto.RegisterType((*AppBuildInfo)(nil), "app_proto.app_build_info")
}

func init() { proto.RegisterFile("app_config.proto", fileDescriptor_16ffc0bbf1ea3dff) }

var fileDescriptor_16ffc0bbf1ea3dff = []byte{
	// 253 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x48, 0x2c, 0x28, 0x88,
	0x4f, 0xce, 0xcf, 0x4b, 0xcb, 0x4c, 0xd7, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0xe2, 0x04, 0x89,
	0x80, 0x99, 0x4a, 0xd3, 0x19, 0xb9, 0xb8, 0x10, 0xf2, 0x42, 0x42, 0x5c, 0x2c, 0x19, 0xf9, 0xc5,
	0x25, 0x12, 0x8c, 0x0a, 0x8c, 0x1a, 0x9c, 0x41, 0x60, 0x36, 0x48, 0xac, 0x20, 0xbf, 0xa8, 0x44,
	0x82, 0x49, 0x81, 0x51, 0x83, 0x35, 0x08, 0xcc, 0x16, 0x12, 0xe1, 0x62, 0x4d, 0xc9, 0x2d, 0xca,
	0x4c, 0x91, 0x60, 0x06, 0x2b, 0x84, 0x70, 0x84, 0xa4, 0xb8, 0x38, 0x0a, 0x12, 0x8b, 0x8b, 0xcb,
	0xf3, 0x8b, 0x52, 0x24, 0x58, 0xc0, 0x12, 0x70, 0xbe, 0x90, 0x3e, 0x97, 0x48, 0x72, 0x62, 0x5e,
	0x7c, 0x6a, 0x4a, 0x66, 0x49, 0x7c, 0x4e, 0x7e, 0x7a, 0x66, 0x5e, 0x7c, 0x41, 0x62, 0x51, 0x62,
	0xae, 0x04, 0xab, 0x02, 0xa3, 0x06, 0x47, 0x90, 0x60, 0x72, 0x62, 0x9e, 0x6b, 0x4a, 0x66, 0x89,
	0x0f, 0x48, 0x26, 0x00, 0x24, 0xa1, 0xe4, 0xc9, 0xc5, 0x07, 0x72, 0x58, 0x52, 0x69, 0x66, 0x4e,
	0x4a, 0x7c, 0x66, 0x5e, 0x5a, 0xbe, 0x90, 0x04, 0x17, 0x7b, 0x59, 0x6a, 0x51, 0x71, 0x66, 0x7e,
	0x1e, 0xd4, 0x7d, 0x30, 0xae, 0x90, 0x2c, 0x17, 0x17, 0x44, 0x5d, 0x49, 0x66, 0x6e, 0x2a, 0xd8,
	0xa1, 0x9c, 0x41, 0x9c, 0x60, 0x91, 0x90, 0xcc, 0xdc, 0x54, 0x27, 0xd5, 0x13, 0x8f, 0xe4, 0x18,
	0x2f, 0x3c, 0x92, 0x63, 0x7c, 0xf0, 0x48, 0x8e, 0x71, 0xc2, 0x63, 0x39, 0x86, 0x0b, 0x8f, 0xe5,
	0x18, 0x6e, 0x3c, 0x96, 0x63, 0xf0, 0x60, 0x8e, 0x42, 0x84, 0x45, 0x12, 0x1b, 0x98, 0x32, 0x06,
	0x04, 0x00, 0x00, 0xff, 0xff, 0x9a, 0xb5, 0x0e, 0x36, 0x31, 0x01, 0x00, 0x00,
}

func (m *AppConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CanEditLoginParam {
		i--
		if m.CanEditLoginParam {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if len(m.Password) > 0 {
		i -= len(m.Password)
		copy(dAtA[i:], m.Password)
		i = encodeVarintAppConfig(dAtA, i, uint64(len(m.Password)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Dmrid) > 0 {
		i -= len(m.Dmrid)
		copy(dAtA[i:], m.Dmrid)
		i = encodeVarintAppConfig(dAtA, i, uint64(len(m.Dmrid)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Port != 0 {
		i = encodeVarintAppConfig(dAtA, i, uint64(m.Port))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Host) > 0 {
		i -= len(m.Host)
		copy(dAtA[i:], m.Host)
		i = encodeVarintAppConfig(dAtA, i, uint64(len(m.Host)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AppBuildInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppBuildInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AppBuildInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BuildTime) > 0 {
		i -= len(m.BuildTime)
		copy(dAtA[i:], m.BuildTime)
		i = encodeVarintAppConfig(dAtA, i, uint64(len(m.BuildTime)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Version) > 0 {
		i -= len(m.Version)
		copy(dAtA[i:], m.Version)
		i = encodeVarintAppConfig(dAtA, i, uint64(len(m.Version)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintAppConfig(dAtA []byte, offset int, v uint64) int {
	offset -= sovAppConfig(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *AppConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Host)
	if l > 0 {
		n += 1 + l + sovAppConfig(uint64(l))
	}
	if m.Port != 0 {
		n += 1 + sovAppConfig(uint64(m.Port))
	}
	l = len(m.Dmrid)
	if l > 0 {
		n += 1 + l + sovAppConfig(uint64(l))
	}
	l = len(m.Password)
	if l > 0 {
		n += 1 + l + sovAppConfig(uint64(l))
	}
	if m.CanEditLoginParam {
		n += 2
	}
	return n
}

func (m *AppBuildInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Version)
	if l > 0 {
		n += 1 + l + sovAppConfig(uint64(l))
	}
	l = len(m.BuildTime)
	if l > 0 {
		n += 1 + l + sovAppConfig(uint64(l))
	}
	return n
}

func sovAppConfig(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozAppConfig(x uint64) (n int) {
	return sovAppConfig(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *AppConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppConfig
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: app_config: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: app_config: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Host", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Host = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			m.Port = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Port |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dmrid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dmrid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CanEditLoginParam", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanEditLoginParam = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAppConfig(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppConfig
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppBuildInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppConfig
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: app_build_info: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: app_build_info: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Version = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BuildTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BuildTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppConfig(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppConfig
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipAppConfig(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAppConfig
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppConfig
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthAppConfig
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupAppConfig
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthAppConfig
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthAppConfig        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAppConfig          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupAppConfig = fmt.Errorf("proto: unexpected end of group")
)
