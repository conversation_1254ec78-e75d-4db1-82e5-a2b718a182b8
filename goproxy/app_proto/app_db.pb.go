// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: app_db.proto

package app_proto

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type DbOrgList struct {
	Rows []*DbOrg `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbOrgList) Reset()         { *m = DbOrgList{} }
func (m *DbOrgList) String() string { return proto.CompactTextString(m) }
func (*DbOrgList) ProtoMessage()    {}
func (*DbOrgList) Descriptor() ([]byte, []int) {
	return fileDescriptor_c32abfe5ff642b5a, []int{0}
}
func (m *DbOrgList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbOrgList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbOrgList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbOrgList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbOrgList.Merge(m, src)
}
func (m *DbOrgList) XXX_Size() int {
	return m.Size()
}
func (m *DbOrgList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbOrgList.DiscardUnknown(m)
}

var xxx_messageInfo_DbOrgList proto.InternalMessageInfo

func (m *DbOrgList) GetRows() []*DbOrg {
	if m != nil {
		return m.Rows
	}
	return nil
}

type DbDeviceList struct {
	Rows []*DbDevice `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbDeviceList) Reset()         { *m = DbDeviceList{} }
func (m *DbDeviceList) String() string { return proto.CompactTextString(m) }
func (*DbDeviceList) ProtoMessage()    {}
func (*DbDeviceList) Descriptor() ([]byte, []int) {
	return fileDescriptor_c32abfe5ff642b5a, []int{1}
}
func (m *DbDeviceList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDeviceList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDeviceList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDeviceList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDeviceList.Merge(m, src)
}
func (m *DbDeviceList) XXX_Size() int {
	return m.Size()
}
func (m *DbDeviceList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDeviceList.DiscardUnknown(m)
}

var xxx_messageInfo_DbDeviceList proto.InternalMessageInfo

func (m *DbDeviceList) GetRows() []*DbDevice {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 组织架构表
type DbOrg struct {
	// @table uuid primary key
	// 行ID
	Rid string `protobuf:"bytes,1,opt,name=rid,proto3" json:"rid,omitempty"`
	// @table varchar(64) unique not null
	// 组织机构编号
	OrgSelfId string `protobuf:"bytes,3,opt,name=org_self_id,json=orgSelfId,proto3" json:"org_self_id,omitempty"`
	// @table varchar(32) unique not null
	// 机构名称,缩写
	OrgShortName string `protobuf:"bytes,5,opt,name=org_short_name,json=orgShortName,proto3" json:"org_short_name,omitempty"`
	// @table int default 2
	// 2:真实机构,1:虚拟机构 虚拟机构没有真实设备和用户,只是引用真实机构的数据,相当于额外的分组
	OrgIsVirtual int32 `protobuf:"varint,8,opt,name=org_is_virtual,json=orgIsVirtual,proto3" json:"org_is_virtual,omitempty"`
	// @table varchar(8) unique
	// DMR ID,可用作组呼的ID
	DmrId string `protobuf:"bytes,9,opt,name=dmr_id,json=dmrId,proto3" json:"dmr_id,omitempty"`
	// @table uuid not null default '11111111-1111-1111-1111-111111111111'
	// 此组织的上级机构device
	ParentOrgId string `protobuf:"bytes,11,opt,name=parent_org_id,json=parentOrgId,proto3" json:"parent_org_id,omitempty"`
}

func (m *DbOrg) Reset()         { *m = DbOrg{} }
func (m *DbOrg) String() string { return proto.CompactTextString(m) }
func (*DbOrg) ProtoMessage()    {}
func (*DbOrg) Descriptor() ([]byte, []int) {
	return fileDescriptor_c32abfe5ff642b5a, []int{2}
}
func (m *DbOrg) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbOrg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbOrg.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbOrg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbOrg.Merge(m, src)
}
func (m *DbOrg) XXX_Size() int {
	return m.Size()
}
func (m *DbOrg) XXX_DiscardUnknown() {
	xxx_messageInfo_DbOrg.DiscardUnknown(m)
}

var xxx_messageInfo_DbOrg proto.InternalMessageInfo

func (m *DbOrg) GetRid() string {
	if m != nil {
		return m.Rid
	}
	return ""
}

func (m *DbOrg) GetOrgSelfId() string {
	if m != nil {
		return m.OrgSelfId
	}
	return ""
}

func (m *DbOrg) GetOrgShortName() string {
	if m != nil {
		return m.OrgShortName
	}
	return ""
}

func (m *DbOrg) GetOrgIsVirtual() int32 {
	if m != nil {
		return m.OrgIsVirtual
	}
	return 0
}

func (m *DbOrg) GetDmrId() string {
	if m != nil {
		return m.DmrId
	}
	return ""
}

func (m *DbOrg) GetParentOrgId() string {
	if m != nil {
		return m.ParentOrgId
	}
	return ""
}

// 对讲机设备表
type DbDevice struct {
	// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
	// 设备所属的群组
	OrgId string `protobuf:"bytes,3,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	// @table varchar(16) not null unique
	// 设备名称
	SelfId string `protobuf:"bytes,4,opt,name=self_id,json=selfId,proto3" json:"self_id,omitempty"`
	// @table varchar(16) not null unique
	// 设备DMR-ID
	DmrId string `protobuf:"bytes,5,opt,name=dmr_id,json=dmrId,proto3" json:"dmr_id,omitempty"`
	// @table int not null default 0
	// 设备类型 0:对讲机手台 1：车台 3:电话网关设备 4:中继虚拟终端 5:互联网关终端 6:模拟网关终端 7:数字网关终端
	DeviceType int32 `protobuf:"varint,9,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	// @table int
	// 优先级
	Priority int32 `protobuf:"varint,12,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (m *DbDevice) Reset()         { *m = DbDevice{} }
func (m *DbDevice) String() string { return proto.CompactTextString(m) }
func (*DbDevice) ProtoMessage()    {}
func (*DbDevice) Descriptor() ([]byte, []int) {
	return fileDescriptor_c32abfe5ff642b5a, []int{3}
}
func (m *DbDevice) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDevice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDevice.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDevice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDevice.Merge(m, src)
}
func (m *DbDevice) XXX_Size() int {
	return m.Size()
}
func (m *DbDevice) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDevice.DiscardUnknown(m)
}

var xxx_messageInfo_DbDevice proto.InternalMessageInfo

func (m *DbDevice) GetOrgId() string {
	if m != nil {
		return m.OrgId
	}
	return ""
}

func (m *DbDevice) GetSelfId() string {
	if m != nil {
		return m.SelfId
	}
	return ""
}

func (m *DbDevice) GetDmrId() string {
	if m != nil {
		return m.DmrId
	}
	return ""
}

func (m *DbDevice) GetDeviceType() int32 {
	if m != nil {
		return m.DeviceType
	}
	return 0
}

func (m *DbDevice) GetPriority() int32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func init() {
	proto.RegisterType((*DbOrgList)(nil), "app_proto.db_org_list")
	proto.RegisterType((*DbDeviceList)(nil), "app_proto.db_device_list")
	proto.RegisterType((*DbOrg)(nil), "app_proto.db_org")
	proto.RegisterType((*DbDevice)(nil), "app_proto.db_device")
}

func init() { proto.RegisterFile("app_db.proto", fileDescriptor_c32abfe5ff642b5a) }

var fileDescriptor_c32abfe5ff642b5a = []byte{
	// 351 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x54, 0x91, 0xbf, 0x4e, 0xeb, 0x30,
	0x14, 0xc6, 0x6b, 0xb5, 0xc9, 0x6d, 0x4e, 0x7a, 0xab, 0x7b, 0xad, 0x7b, 0x45, 0xc4, 0x60, 0xaa,
	0x00, 0x52, 0xa6, 0x0e, 0xc0, 0xc4, 0xc8, 0x96, 0x05, 0xa4, 0x82, 0x18, 0x58, 0xac, 0x04, 0xbb,
	0xc5, 0x52, 0x52, 0x5b, 0x8e, 0x29, 0xea, 0x53, 0xc0, 0x2b, 0xb1, 0x31, 0x76, 0x64, 0x44, 0xed,
	0x8b, 0x20, 0xdb, 0xfd, 0x43, 0xa7, 0xd8, 0xdf, 0xf7, 0x3b, 0xe7, 0x3b, 0xc7, 0x81, 0x5e, 0xa1,
	0x14, 0x65, 0xe5, 0x50, 0x69, 0x69, 0x24, 0x8e, 0xec, 0xcd, 0x1d, 0xd3, 0x0b, 0x88, 0x59, 0x49,
	0xa5, 0x9e, 0xd0, 0x4a, 0x34, 0x06, 0x9f, 0x42, 0x47, 0xcb, 0x97, 0x26, 0x41, 0x83, 0x76, 0x16,
	0x9f, 0xfd, 0x1d, 0x6e, 0xc1, 0xa1, 0xa7, 0x46, 0xce, 0x4e, 0x2f, 0xa1, 0xcf, 0x4a, 0xca, 0xf8,
	0x4c, 0x3c, 0x72, 0x5f, 0x98, 0xed, 0x15, 0xfe, 0xdb, 0x2f, 0xf4, 0xe0, 0xba, 0xf6, 0x1d, 0x41,
	0xe8, 0x9b, 0xe1, 0x3f, 0xd0, 0xd6, 0x82, 0x25, 0x68, 0x80, 0xb2, 0x68, 0x64, 0x8f, 0x98, 0x40,
	0x6c, 0x67, 0x69, 0x78, 0x35, 0xa6, 0x82, 0x25, 0x6d, 0xe7, 0x44, 0x52, 0x4f, 0x6e, 0x79, 0x35,
	0xce, 0x19, 0x3e, 0x81, 0xbe, 0xf3, 0x9f, 0xa4, 0x36, 0x74, 0x5a, 0xd4, 0x3c, 0x09, 0x1c, 0xd2,
	0xb3, 0x88, 0x15, 0xaf, 0x8b, 0x9a, 0x6f, 0x28, 0xd1, 0xd0, 0x99, 0xd0, 0xe6, 0xb9, 0xa8, 0x92,
	0xee, 0x00, 0x65, 0x81, 0xa3, 0xf2, 0xe6, 0xde, 0x6b, 0xf8, 0x3f, 0x84, 0xac, 0xd6, 0x36, 0x26,
	0x72, 0x3d, 0x02, 0x56, 0xeb, 0x9c, 0xe1, 0x14, 0x7e, 0xab, 0x42, 0xf3, 0xa9, 0x71, 0xaf, 0x22,
	0x58, 0x12, 0x3b, 0x37, 0xf6, 0xe2, 0x8d, 0x9e, 0xe4, 0x2c, 0x7d, 0x45, 0x10, 0x6d, 0xf7, 0xb2,
	0x8d, 0xd6, 0xa8, 0x9f, 0x37, 0xb0, 0x31, 0x0c, 0x1f, 0xc0, 0xaf, 0xcd, 0x1e, 0x1d, 0xa7, 0x87,
	0x8d, 0x5f, 0x62, 0x17, 0x1c, 0xfc, 0x0c, 0x3e, 0x82, 0x78, 0xfd, 0xa2, 0x66, 0xae, 0xb8, 0x1b,
	0x2a, 0x18, 0x81, 0x97, 0xee, 0xe6, 0x8a, 0xe3, 0x43, 0xe8, 0x2a, 0x2d, 0xa4, 0x16, 0x66, 0x9e,
	0xf4, 0x9c, 0xbb, 0xbd, 0x5f, 0x1d, 0x7f, 0x2c, 0x09, 0x5a, 0x2c, 0x09, 0xfa, 0x5a, 0x12, 0xf4,
	0xb6, 0x22, 0xad, 0xc5, 0x8a, 0xb4, 0x3e, 0x57, 0xa4, 0xf5, 0xb0, 0xfb, 0xd9, 0x65, 0xe8, 0x3e,
	0xe7, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x8b, 0x2f, 0x56, 0xf4, 0x0e, 0x02, 0x00, 0x00,
}

func (m *DbOrgList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbOrgList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbOrgList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAppDb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbDeviceList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDeviceList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDeviceList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAppDb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbOrg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbOrg) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbOrg) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ParentOrgId) > 0 {
		i -= len(m.ParentOrgId)
		copy(dAtA[i:], m.ParentOrgId)
		i = encodeVarintAppDb(dAtA, i, uint64(len(m.ParentOrgId)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.DmrId) > 0 {
		i -= len(m.DmrId)
		copy(dAtA[i:], m.DmrId)
		i = encodeVarintAppDb(dAtA, i, uint64(len(m.DmrId)))
		i--
		dAtA[i] = 0x4a
	}
	if m.OrgIsVirtual != 0 {
		i = encodeVarintAppDb(dAtA, i, uint64(m.OrgIsVirtual))
		i--
		dAtA[i] = 0x40
	}
	if len(m.OrgShortName) > 0 {
		i -= len(m.OrgShortName)
		copy(dAtA[i:], m.OrgShortName)
		i = encodeVarintAppDb(dAtA, i, uint64(len(m.OrgShortName)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.OrgSelfId) > 0 {
		i -= len(m.OrgSelfId)
		copy(dAtA[i:], m.OrgSelfId)
		i = encodeVarintAppDb(dAtA, i, uint64(len(m.OrgSelfId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Rid) > 0 {
		i -= len(m.Rid)
		copy(dAtA[i:], m.Rid)
		i = encodeVarintAppDb(dAtA, i, uint64(len(m.Rid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbDevice) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDevice) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDevice) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Priority != 0 {
		i = encodeVarintAppDb(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x60
	}
	if m.DeviceType != 0 {
		i = encodeVarintAppDb(dAtA, i, uint64(m.DeviceType))
		i--
		dAtA[i] = 0x48
	}
	if len(m.DmrId) > 0 {
		i -= len(m.DmrId)
		copy(dAtA[i:], m.DmrId)
		i = encodeVarintAppDb(dAtA, i, uint64(len(m.DmrId)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.SelfId) > 0 {
		i -= len(m.SelfId)
		copy(dAtA[i:], m.SelfId)
		i = encodeVarintAppDb(dAtA, i, uint64(len(m.SelfId)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.OrgId) > 0 {
		i -= len(m.OrgId)
		copy(dAtA[i:], m.OrgId)
		i = encodeVarintAppDb(dAtA, i, uint64(len(m.OrgId)))
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}

func encodeVarintAppDb(dAtA []byte, offset int, v uint64) int {
	offset -= sovAppDb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbOrgList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovAppDb(uint64(l))
		}
	}
	return n
}

func (m *DbDeviceList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovAppDb(uint64(l))
		}
	}
	return n
}

func (m *DbOrg) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Rid)
	if l > 0 {
		n += 1 + l + sovAppDb(uint64(l))
	}
	l = len(m.OrgSelfId)
	if l > 0 {
		n += 1 + l + sovAppDb(uint64(l))
	}
	l = len(m.OrgShortName)
	if l > 0 {
		n += 1 + l + sovAppDb(uint64(l))
	}
	if m.OrgIsVirtual != 0 {
		n += 1 + sovAppDb(uint64(m.OrgIsVirtual))
	}
	l = len(m.DmrId)
	if l > 0 {
		n += 1 + l + sovAppDb(uint64(l))
	}
	l = len(m.ParentOrgId)
	if l > 0 {
		n += 1 + l + sovAppDb(uint64(l))
	}
	return n
}

func (m *DbDevice) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.OrgId)
	if l > 0 {
		n += 1 + l + sovAppDb(uint64(l))
	}
	l = len(m.SelfId)
	if l > 0 {
		n += 1 + l + sovAppDb(uint64(l))
	}
	l = len(m.DmrId)
	if l > 0 {
		n += 1 + l + sovAppDb(uint64(l))
	}
	if m.DeviceType != 0 {
		n += 1 + sovAppDb(uint64(m.DeviceType))
	}
	if m.Priority != 0 {
		n += 1 + sovAppDb(uint64(m.Priority))
	}
	return n
}

func sovAppDb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozAppDb(x uint64) (n int) {
	return sovAppDb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbOrgList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppDb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_org_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_org_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbOrg{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppDb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppDb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDeviceList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppDb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_device_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_device_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbDevice{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppDb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppDb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbOrg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppDb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_org: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_org: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgSelfId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgSelfId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgShortName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgShortName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgIsVirtual", wireType)
			}
			m.OrgIsVirtual = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OrgIsVirtual |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DmrId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DmrId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParentOrgId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParentOrgId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppDb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppDb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDevice) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppDb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_device: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_device: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SelfId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SelfId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DmrId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppDb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppDb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DmrId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppDb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppDb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipAppDb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAppDb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppDb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthAppDb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupAppDb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthAppDb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthAppDb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAppDb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupAppDb = fmt.Errorf("proto: unexpected end of group")
)
