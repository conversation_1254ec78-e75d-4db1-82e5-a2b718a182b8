// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: app_proto.proto

package app_proto

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type MediaStatus int32

const (
	MediaStatus_start  MediaStatus = 0
	MediaStatus_stoped MediaStatus = 1
)

var MediaStatus_name = map[int32]string{
	0: "start",
	1: "stoped",
}

var MediaStatus_value = map[string]int32{
	"start":  0,
	"stoped": 1,
}

func (x MediaStatus) String() string {
	return proto.EnumName(MediaStatus_name, int32(x))
}

func (MediaStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{0}
}

type ResCode int32

const (
	ResCode_success ResCode = 0
	ResCode_fialed  ResCode = 1
)

var ResCode_name = map[int32]string{
	0: "success",
	1: "fialed",
}

var ResCode_value = map[string]int32{
	"success": 0,
	"fialed":  1,
}

func (x ResCode) String() string {
	return proto.EnumName(ResCode_name, int32(x))
}

func (ResCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{1}
}

// rpc 结构见 bf8100_project 的 bf8100.proto 中的 rpc_cmd
// 默认req 使用的 message对应enum cmd_后面的部分
// 如：cmd_req_ping的body 对应message req_login
// 回应对body和res有对应介绍
// 回应rpc.res = res_code.fialed 时候 para_str 携带错误原因
type CmdCode int32

const (
	CmdCode_cmd_req_ping CmdCode = 0
	//rpc.res = 1
	CmdCode_cmd_resp_ping CmdCode = 1
	//登录
	CmdCode_cmd_req_login CmdCode = 2
	//rpc.res = 登录回应值 0:登录成功， 1:重复登录 10:登录失败,密码/session校验错误 303: 用户没有指定设备
	//rpc.para_str 携带 sid
	//rpc.para_int 0:正常登录  1:自动重新登录
	//rpc.para_bin 携带登录额外信息。对应bf8100_project/bf8100.proto 中 res_login_para_bin
	CmdCode_cmd_resp_login CmdCode = 3
	//更新收听组
	//@Deprecated - use cmd_req_delete_listen_group/cmd_req_add_listen_group replace this cmd
	CmdCode_cmd_req_update_listen_group_list CmdCode = 4
	//rpc.res = res_code
	CmdCode_cmd_resp_update_listen_group_list CmdCode = 5
	//查询收听组
	CmdCode_cmd_req_query_listen_group_list CmdCode = 6
	//rpc.body = req_update_listen_group_list    亦可由proxy主动推送
	CmdCode_cmd_resp_query_listen_group_list CmdCode = 7
	//更新发射目标
	CmdCode_cmd_req_update_speak_target CmdCode = 8
	//rpc.res = res_code
	CmdCode_cmd_resp_update_speak_target CmdCode = 9
	//查询发射目标
	CmdCode_cmd_req_query_speak_target CmdCode = 10
	//rpc.body = req_update_speak_target  亦可由proxy主动推送
	CmdCode_cmd_resp_query_speak_target CmdCode = 11
	//查询最新通讯录,不需要填body
	CmdCode_cmd_req_query_address_book CmdCode = 12
	//rpc.body = address_book_list  亦可由proxy主动推送
	//rpc.res = 1,则为推送未知用户的数据信息（不可存放至通讯录,仅用于展示）
	CmdCode_cmd_resp_query_address_book CmdCode = 13
	//开始通话,不需要填body
	// rpc.para_str = dmrid
	CmdCode_cmd_req_speak_start CmdCode = 14
	//rpc.body = bfkcp.cb71
	//res = 1.无效的通话目标
	//如果当前已经开始录音，则回应 res=2,rpc.body = bfkcp.cb71，cb71中仅source/target两个字段有效
	//如果未设置sn，则 res=3
	// res=4,当前被禁发
	CmdCode_cmd_resp_speak_start CmdCode = 15
	//停止通话,不需要填body
	CmdCode_cmd_req_speak_stop CmdCode = 16
	//rpc.res = res_code
	CmdCode_cmd_resp_speak_stop CmdCode = 17
	//查询通话状态,不需要填body
	CmdCode_cmd_req_speak_status CmdCode = 18
	//rpc.res = media_status
	CmdCode_cmd_resp_speak_status CmdCode = 19
	//查询是否处于放音状态,不需要填body
	CmdCode_cmd_req_media_play_status CmdCode = 20
	//rpc.res = media_status,不需要填body 亦可由proxy通知处于放音状态
	//rpc.body = bc15
	CmdCode_cmd_resp_media_play_status CmdCode = 21
	//由proxy 主动发送，携带通知客户端的事件
	//body = Notify
	CmdCode_cmd_notify CmdCode = 22
	//由proxy通知有新的短信
	CmdCode_cmd_short_messages CmdCode = 24
	//确认收到短信  rpc.body = short_messages. 只需要填写sender_dmrid、target_dmrid、sms_no
	CmdCode_cmd_resp_confirm_short_messages CmdCode = 25
	//发送短信消息  rpc.body = short_messages
	CmdCode_cmd_send_short_messages CmdCode = 26
	//rpc.res = smsNo //后台发送成功回应
	CmdCode_cmd_resp_send_short_messages CmdCode = 27
	//查询是否登录 不需要body
	CmdCode_cmd_req_is_login CmdCode = 28
	//rpc.res = 0.未登录  1.已经登录  2.已经登录但kcp断开
	CmdCode_cmd_resp_is_login CmdCode = 29
	//查询kcp是否链接服务器 不需要body
	CmdCode_cmd_req_is_conn_server CmdCode = 30
	//rpc.res = res_code
	CmdCode_cmd_resp_is_conn_server CmdCode = 31
	//更新kcp链接服务器的ipAddr
	CmdCode_cmd_req_update_server_addr CmdCode = 32
	//rpc.res = res_code
	CmdCode_cmd_resp_update_server_addr CmdCode = 33
	//不需要body
	CmdCode_cmd_req_query_login_user CmdCode = 34
	//rpc.body req_login
	CmdCode_cmd_resp_query_login_user CmdCode = 35
	//不需要body
	CmdCode_cmd_req_query_login_dev CmdCode = 36
	//rpc.Res = DevicePriority  rpc.ParaStr = DevDMRID
	CmdCode_cmd_resp_query_login_dev CmdCode = 37
	//不需要body  设置单次讲话超时  rpc.res=n 秒  [0,300]
	CmdCode_cmd_req_set_speak_time_out_duration CmdCode = 38
	//rpc.res = res_code
	CmdCode_cmd_resp_set_speak_time_out_duration CmdCode = 39
	//不需要body  获取单次讲话超时
	CmdCode_cmd_req_get_speak_time_out_duration CmdCode = 40
	//rpc.res = n 秒
	CmdCode_cmd_resp_get_speak_time_out_duration CmdCode = 41
	//body = voice_config
	CmdCode_cmd_req_update_voice_config  CmdCode = 42
	CmdCode_cmd_resp_update_voice_config CmdCode = 43
	//部分删除收听组  一次请求 listenGroup个数不能超过过20个
	CmdCode_cmd_req_delete_listen_group CmdCode = 52
	//rpc.res = res_code | 后台服务器 -1000=ErrUnmarshal
	CmdCode_cmd_resp_delete_listen_group CmdCode = 53
	//部分增加收听组  一次请求 listenGroup个数不能超过过20个
	CmdCode_cmd_req_add_listen_group CmdCode = 54
	//rpc.res = res_code | 后台服务器 -1000=ErrUnmarshal
	CmdCode_cmd_resp_add_listen_group CmdCode = 55
	//退出登录
	CmdCode_cmd_req_login_quit CmdCode = 56
	//rpc.res = res_code
	CmdCode_cmd_resp_login_quit CmdCode = 57
	//查询默认发射目标
	CmdCode_cmd_req_query_default_speak_target CmdCode = 58
	//rpc.body = req_update_speak_target  亦可由proxy主动推送
	CmdCode_cmd_resp_query_default_speak_target CmdCode = 59
	//同步后台dev数据
	CmdCode_cmd_req_query_default_dev_config CmdCode = 60
	//proxy的响应
	//rpc.res = res_code
	//parmStr:错误原因
	CmdCode_cmd_resp_query_default_dev_config CmdCode = 61
	//申请播放指定的缓存语音消息
	//ParaStr="repeaterIdHex-sourceHex-timeStampHex" .e.g CCCCCCCC-MMMMMMMM-HHMMSSddmmyyyy
	//时间戳不需要前补0
	CmdCode_cmd_req_play_local_cache_media CmdCode = 62
	//rpc.res = 0.本地拥有数据，即将播放 1.本地没有数据，需要下载 只是回应，具体的播放需要notify通知  2.拒绝播放，当前正处于录音或放音状态  3.无效的sn
	CmdCode_cmd_resp_play_local_cache_media CmdCode = 63
	//请求回呼目标
	CmdCode_cmd_req_query_call_back_target CmdCode = 64
	//res = call_back_target，亦可由proxy主动通知,回呼目标为""表示没有回呼目标
	CmdCode_cmd_resp_query_call_back_target CmdCode = 65
	//请求清空回呼目标
	CmdCode_cmd_req_clear_call_back_target CmdCode = 66
	//rpc.res = res_code
	CmdCode_cmd_resp_clear_call_back_target CmdCode = 67
	//停止播放缓存语音数据
	CmdCode_cmd_req_stop_play_local_cache_media CmdCode = 68
	//rpc.res = res_code 只是回应，具体的播放结束需要notify通知. 若res=2 则表示当前未播放
	CmdCode_cmd_resp_stop_play_local_cache_media CmdCode = 69
	//设置媒体处理软体：  res= 1.NDK  2.JDK
	//默认设置为NDK
	CmdCode_cmd_req_set_media_software CmdCode = 70
	//rpc.res = res_code
	CmdCode_cmd_resp_set_media_software CmdCode = 71
	//rpc.paramStr = "dmriHex,dmridHex,..."
	CmdCode_cmd_req_query_addr_book_by_dmrid CmdCode = 72
	//rpc.body = app_proto.req_address_book_result
	//rpc.res=-1 req_address_book_result marshal出错
	//客户端查询结果
	CmdCode_cmd_resp_query_addr_book_by_dmrid CmdCode = 73
	//rpc.body = app_proto.req_address_book_result
	//rpc.res=-1 req_address_book_result marshal出错
	//proxy主动查询结果
	CmdCode_cmd_resp_query_addr_book_by_dmrid_proxy CmdCode = 74
	//客户端请求map token
	CmdCode_cmd_req_map_token CmdCode = 75
	//客户端响应map token
	//rpc_cmd.para_int = map token
	//rpc_cmd.res = res_code
	//rpc_cmd.para_str = error message
	CmdCode_cmd_resp_map_token CmdCode = 76
	//req gps location once
	CmdCode_cmd_req_gps_location_once CmdCode = 77
	//req gps location on
	CmdCode_cmd_req_gps_location_on CmdCode = 78
	//req gps location off
	CmdCode_cmd_req_gps_location_off CmdCode = 79
	//broadcast pcm data
	//rpc_cmd.para_int=samplerate
	//rpc_cmd.body=pcm_data
	CmdCode_cmd_got_pcm_data CmdCode = 80
	// request gps location broadcast of a specific device
	// rpc_cmd.body = req_gps_permission
	CmdCode_cmd_req_device_gps_location_permission CmdCode = 81
	// response gps location broadcast of a specific device
	// rpc_cmd.body = res_gps_permission
	CmdCode_cmd_resp_device_gps_location_permission CmdCode = 82
	//query gps permission
	// rpc_cmd.body = req_gps_permission
	CmdCode_cmd_req_query_device_gps_location_permission CmdCode = 83
	//response query gps permission
	// rpc_cmd.body = res_gps_permission
	CmdCode_cmd_resp_query_device_gps_location_permission CmdCode = 84
	//退出程序
	CmdCode_cmd_exit CmdCode = 1001
	//被强制下线
	CmdCode_cmd_force_exit CmdCode = 444
	//转发服务器bc15指令，body=bc15
	CmdCode_cmd_bc15 CmdCode = 445
	// 请求poc通讯录
	CmdCode_cmd_req_query_contact CmdCode = 1100
	// 回应poc通讯录
	// rpc_cmd.res = res_code
	// rpc_cmd.para_int = 1, rpc_cmd.body = db_org_list
	// rpc_cmd.para_int = 2, rpc_cmd.body = db_device_list
	CmdCode_cmd_resp_query_contact CmdCode = 1101
	// 查询默认发射组和收听组
	CmdCode_cmd_req_query_poc_default_group CmdCode = 1102
	// 回应默认发射组和收听组
	// rpc_cmd.res = res_code
	// rpc_cmd.para_int = 3, rpc_cmd.body = PocDefaultGroup
	CmdCode_cmd_resp_query_poc_default_group CmdCode = 1103
	// poc更新收听组
	// rpc_cmd.para_bin = PocSubscribleUpdateOption
	// rpc_cmd.body = PocDefaultGroup
	CmdCode_cmd_req_update_poc_listen_group CmdCode = 1104
	// rpc_cmd.res = res_code
	CmdCode_cmd_resp_update_poc_listen_group CmdCode = 1105
	// poc查询当前收听组
	CmdCode_cmd_req_query_poc_listen_group CmdCode = 1106
	// rpc_cmd.res = res_code
	// rpc_cmd.body = PocDefaultGroup
	CmdCode_cmd_resp_query_poc_listen_group CmdCode = 1107
	// 请求权限外的通讯录数据，只作通话消息显示
	// rpc_cmd.ParaStr = hex dmrId
	CmdCode_cmd_req_query_outside_permission_contact CmdCode = 1108
	// rpc_cmd.res = res_code
	// rpc_cmd.ParaStr = hex dmrId
	// rpc_cmd.body = db_org or db_device
	CmdCode_cmd_resp_query_outside_permission_contact CmdCode = 1109
	// 收到更新通讯录通知(调度台修改了通讯录)
	// 收到此通知后，应该更新通讯录 cmd_req_query_contact
	CmdCode_cmd_notify_poc_setting_changed CmdCode = 1111
	// 发送报警
	// rpc_cmd.ParaStr = device Org hex dmrId
	CmdCode_cmd_req_send_alarm CmdCode = 1113
	// rpc_cmd.res = res_code
	CmdCode_cmd_resp_send_alarm CmdCode = 1114
	// 转发服务器cb10指令 用于解除报警
	CmdCode_cmd_cb10 CmdCode = 1115
	// 通知设备状态变更
	// rpc_cmd.ParaBin = []byte 6个字节
	CmdCode_cmd_notify_device_status CmdCode = 1116
	// 系统下发遥开、遥闭命令操作，锁机状态参数
	// YN: 00=开机；01=锁机；02=查询参数；其它无效
	// ST: 00=开机；01=禁听锁机,02=禁发锁机,03=禁发禁听锁机。
	// rpc_cmd.ParaBin = [YN, ST]
	CmdCode_cmd_notify_lock_device_status CmdCode = 1117
	// 请求poc配置
	CmdCode_cmd_req_query_poc_config CmdCode = 1118
	// 返回poc配置
	CmdCode_cmd_resp_query_poc_config CmdCode = 1119
	//rpc.res = res_code
	// seq_no:请求序列号,响应和请求一一对应
	//parmStr:错误原因
	CmdCode_cmd_req_query_app_config  CmdCode = 10001
	CmdCode_cmd_resp_query_app_config CmdCode = 10002
	CmdCode_cmd_req_set_app_config    CmdCode = 10003
	CmdCode_cmd_resp_set_app_config   CmdCode = 10004
	// 查询在线终端
	CmdCode_cmd_req_query_online_contact CmdCode = 1120
	// 查询在线终端 / 终端列表
	// rpc_cmd.res = res_code
	// rpc_cmd.para_int = 11, rpc_cmd.res=1, rpc_cmd.body=cc183, cc183.action_code=12, cc183.dmrids为在线终端
	// 如果没有在线终端，rpccmd.body=空
	CmdCode_cmd_resp_query_online_contact CmdCode = 1121
	// rpc_cmd.Body = PocConfig
	CmdCode_cmd_sync_poc_config_to_proxy CmdCode = 1122
	// 登录超时指令，通知到goproxy
	// rpc_cmd.ParaStr = hex dmrId
	CmdCode_cmd_notify_login_timeout CmdCode = 1124
	// 登录成功，初始化数据完成，通知到goproxy
	CmdCode_cmd_notify_init_data_finish CmdCode = 1125
)

var CmdCode_name = map[int32]string{
	0:     "cmd_req_ping",
	1:     "cmd_resp_ping",
	2:     "cmd_req_login",
	3:     "cmd_resp_login",
	4:     "cmd_req_update_listen_group_list",
	5:     "cmd_resp_update_listen_group_list",
	6:     "cmd_req_query_listen_group_list",
	7:     "cmd_resp_query_listen_group_list",
	8:     "cmd_req_update_speak_target",
	9:     "cmd_resp_update_speak_target",
	10:    "cmd_req_query_speak_target",
	11:    "cmd_resp_query_speak_target",
	12:    "cmd_req_query_address_book",
	13:    "cmd_resp_query_address_book",
	14:    "cmd_req_speak_start",
	15:    "cmd_resp_speak_start",
	16:    "cmd_req_speak_stop",
	17:    "cmd_resp_speak_stop",
	18:    "cmd_req_speak_status",
	19:    "cmd_resp_speak_status",
	20:    "cmd_req_media_play_status",
	21:    "cmd_resp_media_play_status",
	22:    "cmd_notify",
	24:    "cmd_short_messages",
	25:    "cmd_resp_confirm_short_messages",
	26:    "cmd_send_short_messages",
	27:    "cmd_resp_send_short_messages",
	28:    "cmd_req_is_login",
	29:    "cmd_resp_is_login",
	30:    "cmd_req_is_conn_server",
	31:    "cmd_resp_is_conn_server",
	32:    "cmd_req_update_server_addr",
	33:    "cmd_resp_update_server_addr",
	34:    "cmd_req_query_login_user",
	35:    "cmd_resp_query_login_user",
	36:    "cmd_req_query_login_dev",
	37:    "cmd_resp_query_login_dev",
	38:    "cmd_req_set_speak_time_out_duration",
	39:    "cmd_resp_set_speak_time_out_duration",
	40:    "cmd_req_get_speak_time_out_duration",
	41:    "cmd_resp_get_speak_time_out_duration",
	42:    "cmd_req_update_voice_config",
	43:    "cmd_resp_update_voice_config",
	52:    "cmd_req_delete_listen_group",
	53:    "cmd_resp_delete_listen_group",
	54:    "cmd_req_add_listen_group",
	55:    "cmd_resp_add_listen_group",
	56:    "cmd_req_login_quit",
	57:    "cmd_resp_login_quit",
	58:    "cmd_req_query_default_speak_target",
	59:    "cmd_resp_query_default_speak_target",
	60:    "cmd_req_query_default_dev_config",
	61:    "cmd_resp_query_default_dev_config",
	62:    "cmd_req_play_local_cache_media",
	63:    "cmd_resp_play_local_cache_media",
	64:    "cmd_req_query_call_back_target",
	65:    "cmd_resp_query_call_back_target",
	66:    "cmd_req_clear_call_back_target",
	67:    "cmd_resp_clear_call_back_target",
	68:    "cmd_req_stop_play_local_cache_media",
	69:    "cmd_resp_stop_play_local_cache_media",
	70:    "cmd_req_set_media_software",
	71:    "cmd_resp_set_media_software",
	72:    "cmd_req_query_addr_book_by_dmrid",
	73:    "cmd_resp_query_addr_book_by_dmrid",
	74:    "cmd_resp_query_addr_book_by_dmrid_proxy",
	75:    "cmd_req_map_token",
	76:    "cmd_resp_map_token",
	77:    "cmd_req_gps_location_once",
	78:    "cmd_req_gps_location_on",
	79:    "cmd_req_gps_location_off",
	80:    "cmd_got_pcm_data",
	81:    "cmd_req_device_gps_location_permission",
	82:    "cmd_resp_device_gps_location_permission",
	83:    "cmd_req_query_device_gps_location_permission",
	84:    "cmd_resp_query_device_gps_location_permission",
	1001:  "cmd_exit",
	444:   "cmd_force_exit",
	445:   "cmd_bc15",
	1100:  "cmd_req_query_contact",
	1101:  "cmd_resp_query_contact",
	1102:  "cmd_req_query_poc_default_group",
	1103:  "cmd_resp_query_poc_default_group",
	1104:  "cmd_req_update_poc_listen_group",
	1105:  "cmd_resp_update_poc_listen_group",
	1106:  "cmd_req_query_poc_listen_group",
	1107:  "cmd_resp_query_poc_listen_group",
	1108:  "cmd_req_query_outside_permission_contact",
	1109:  "cmd_resp_query_outside_permission_contact",
	1111:  "cmd_notify_poc_setting_changed",
	1113:  "cmd_req_send_alarm",
	1114:  "cmd_resp_send_alarm",
	1115:  "cmd_cb10",
	1116:  "cmd_notify_device_status",
	1117:  "cmd_notify_lock_device_status",
	1118:  "cmd_req_query_poc_config",
	1119:  "cmd_resp_query_poc_config",
	10001: "cmd_req_query_app_config",
	10002: "cmd_resp_query_app_config",
	10003: "cmd_req_set_app_config",
	10004: "cmd_resp_set_app_config",
	1120:  "cmd_req_query_online_contact",
	1121:  "cmd_resp_query_online_contact",
	1122:  "cmd_sync_poc_config_to_proxy",
	1124:  "cmd_notify_login_timeout",
	1125:  "cmd_notify_init_data_finish",
}

var CmdCode_value = map[string]int32{
	"cmd_req_ping":                                  0,
	"cmd_resp_ping":                                 1,
	"cmd_req_login":                                 2,
	"cmd_resp_login":                                3,
	"cmd_req_update_listen_group_list":              4,
	"cmd_resp_update_listen_group_list":             5,
	"cmd_req_query_listen_group_list":               6,
	"cmd_resp_query_listen_group_list":              7,
	"cmd_req_update_speak_target":                   8,
	"cmd_resp_update_speak_target":                  9,
	"cmd_req_query_speak_target":                    10,
	"cmd_resp_query_speak_target":                   11,
	"cmd_req_query_address_book":                    12,
	"cmd_resp_query_address_book":                   13,
	"cmd_req_speak_start":                           14,
	"cmd_resp_speak_start":                          15,
	"cmd_req_speak_stop":                            16,
	"cmd_resp_speak_stop":                           17,
	"cmd_req_speak_status":                          18,
	"cmd_resp_speak_status":                         19,
	"cmd_req_media_play_status":                     20,
	"cmd_resp_media_play_status":                    21,
	"cmd_notify":                                    22,
	"cmd_short_messages":                            24,
	"cmd_resp_confirm_short_messages":               25,
	"cmd_send_short_messages":                       26,
	"cmd_resp_send_short_messages":                  27,
	"cmd_req_is_login":                              28,
	"cmd_resp_is_login":                             29,
	"cmd_req_is_conn_server":                        30,
	"cmd_resp_is_conn_server":                       31,
	"cmd_req_update_server_addr":                    32,
	"cmd_resp_update_server_addr":                   33,
	"cmd_req_query_login_user":                      34,
	"cmd_resp_query_login_user":                     35,
	"cmd_req_query_login_dev":                       36,
	"cmd_resp_query_login_dev":                      37,
	"cmd_req_set_speak_time_out_duration":           38,
	"cmd_resp_set_speak_time_out_duration":          39,
	"cmd_req_get_speak_time_out_duration":           40,
	"cmd_resp_get_speak_time_out_duration":          41,
	"cmd_req_update_voice_config":                   42,
	"cmd_resp_update_voice_config":                  43,
	"cmd_req_delete_listen_group":                   52,
	"cmd_resp_delete_listen_group":                  53,
	"cmd_req_add_listen_group":                      54,
	"cmd_resp_add_listen_group":                     55,
	"cmd_req_login_quit":                            56,
	"cmd_resp_login_quit":                           57,
	"cmd_req_query_default_speak_target":            58,
	"cmd_resp_query_default_speak_target":           59,
	"cmd_req_query_default_dev_config":              60,
	"cmd_resp_query_default_dev_config":             61,
	"cmd_req_play_local_cache_media":                62,
	"cmd_resp_play_local_cache_media":               63,
	"cmd_req_query_call_back_target":                64,
	"cmd_resp_query_call_back_target":               65,
	"cmd_req_clear_call_back_target":                66,
	"cmd_resp_clear_call_back_target":               67,
	"cmd_req_stop_play_local_cache_media":           68,
	"cmd_resp_stop_play_local_cache_media":          69,
	"cmd_req_set_media_software":                    70,
	"cmd_resp_set_media_software":                   71,
	"cmd_req_query_addr_book_by_dmrid":              72,
	"cmd_resp_query_addr_book_by_dmrid":             73,
	"cmd_resp_query_addr_book_by_dmrid_proxy":       74,
	"cmd_req_map_token":                             75,
	"cmd_resp_map_token":                            76,
	"cmd_req_gps_location_once":                     77,
	"cmd_req_gps_location_on":                       78,
	"cmd_req_gps_location_off":                      79,
	"cmd_got_pcm_data":                              80,
	"cmd_req_device_gps_location_permission":        81,
	"cmd_resp_device_gps_location_permission":       82,
	"cmd_req_query_device_gps_location_permission":  83,
	"cmd_resp_query_device_gps_location_permission": 84,
	"cmd_exit":                                  1001,
	"cmd_force_exit":                            444,
	"cmd_bc15":                                  445,
	"cmd_req_query_contact":                     1100,
	"cmd_resp_query_contact":                    1101,
	"cmd_req_query_poc_default_group":           1102,
	"cmd_resp_query_poc_default_group":          1103,
	"cmd_req_update_poc_listen_group":           1104,
	"cmd_resp_update_poc_listen_group":          1105,
	"cmd_req_query_poc_listen_group":            1106,
	"cmd_resp_query_poc_listen_group":           1107,
	"cmd_req_query_outside_permission_contact":  1108,
	"cmd_resp_query_outside_permission_contact": 1109,
	"cmd_notify_poc_setting_changed":            1111,
	"cmd_req_send_alarm":                        1113,
	"cmd_resp_send_alarm":                       1114,
	"cmd_cb10":                                  1115,
	"cmd_notify_device_status":                  1116,
	"cmd_notify_lock_device_status":             1117,
	"cmd_req_query_poc_config":                  1118,
	"cmd_resp_query_poc_config":                 1119,
	"cmd_req_query_app_config":                  10001,
	"cmd_resp_query_app_config":                 10002,
	"cmd_req_set_app_config":                    10003,
	"cmd_resp_set_app_config":                   10004,
	"cmd_req_query_online_contact":              1120,
	"cmd_resp_query_online_contact":             1121,
	"cmd_sync_poc_config_to_proxy":              1122,
	"cmd_notify_login_timeout":                  1124,
	"cmd_notify_init_data_finish":               1125,
}

func (x CmdCode) String() string {
	return proto.EnumName(CmdCode_name, int32(x))
}

func (CmdCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{2}
}

type ReqLogin struct {
	SysId    string `protobuf:"bytes,1,opt,name=sys_id,json=sysId,proto3" json:"sys_id,omitempty"`
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	//用户密码 / session
	UserPass string `protobuf:"bytes,3,opt,name=user_pass,json=userPass,proto3" json:"user_pass,omitempty"`
	//登录系统方法,0:使用密码登录,1:使用sid登录
	LoginMethod int32 `protobuf:"varint,4,opt,name=login_method,json=loginMethod,proto3" json:"login_method,omitempty"`
	//can display map
	CanDisplayMap bool `protobuf:"varint,5,opt,name=can_display_map,json=canDisplayMap,proto3" json:"can_display_map,omitempty"`
	// 0:ambe, 1:opus
	PreferCodec int32 `protobuf:"varint,6,opt,name=prefer_codec,json=preferCodec,proto3" json:"prefer_codec,omitempty"`
}

func (m *ReqLogin) Reset()         { *m = ReqLogin{} }
func (m *ReqLogin) String() string { return proto.CompactTextString(m) }
func (*ReqLogin) ProtoMessage()    {}
func (*ReqLogin) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{0}
}
func (m *ReqLogin) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReqLogin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReqLogin.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReqLogin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqLogin.Merge(m, src)
}
func (m *ReqLogin) XXX_Size() int {
	return m.Size()
}
func (m *ReqLogin) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqLogin.DiscardUnknown(m)
}

var xxx_messageInfo_ReqLogin proto.InternalMessageInfo

func (m *ReqLogin) GetSysId() string {
	if m != nil {
		return m.SysId
	}
	return ""
}

func (m *ReqLogin) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ReqLogin) GetUserPass() string {
	if m != nil {
		return m.UserPass
	}
	return ""
}

func (m *ReqLogin) GetLoginMethod() int32 {
	if m != nil {
		return m.LoginMethod
	}
	return 0
}

func (m *ReqLogin) GetCanDisplayMap() bool {
	if m != nil {
		return m.CanDisplayMap
	}
	return false
}

func (m *ReqLogin) GetPreferCodec() int32 {
	if m != nil {
		return m.PreferCodec
	}
	return 0
}

type RespLogin struct {
	IsServerSupportMap bool `protobuf:"varint,1,opt,name=is_server_support_map,json=isServerSupportMap,proto3" json:"is_server_support_map,omitempty"`
	//服务器版本号
	ServerVersion string `protobuf:"bytes,2,opt,name=server_version,json=serverVersion,proto3" json:"server_version,omitempty"`
	//配置最后更新时间,utc时间
	//目前只有poc终端有此字段
	SettingLastUpdateTime string `protobuf:"bytes,3,opt,name=setting_last_update_time,json=settingLastUpdateTime,proto3" json:"setting_last_update_time,omitempty"`
	// 当前登录的终端设备
	Device *DbDevice `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
}

func (m *RespLogin) Reset()         { *m = RespLogin{} }
func (m *RespLogin) String() string { return proto.CompactTextString(m) }
func (*RespLogin) ProtoMessage()    {}
func (*RespLogin) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{1}
}
func (m *RespLogin) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RespLogin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RespLogin.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RespLogin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RespLogin.Merge(m, src)
}
func (m *RespLogin) XXX_Size() int {
	return m.Size()
}
func (m *RespLogin) XXX_DiscardUnknown() {
	xxx_messageInfo_RespLogin.DiscardUnknown(m)
}

var xxx_messageInfo_RespLogin proto.InternalMessageInfo

func (m *RespLogin) GetIsServerSupportMap() bool {
	if m != nil {
		return m.IsServerSupportMap
	}
	return false
}

func (m *RespLogin) GetServerVersion() string {
	if m != nil {
		return m.ServerVersion
	}
	return ""
}

func (m *RespLogin) GetSettingLastUpdateTime() string {
	if m != nil {
		return m.SettingLastUpdateTime
	}
	return ""
}

func (m *RespLogin) GetDevice() *DbDevice {
	if m != nil {
		return m.Device
	}
	return nil
}

// apply for device gps info permission
type ReqGpsPermission struct {
	// target device dmrid
	Dmrid uint32 `protobuf:"varint,1,opt,name=dmrid,proto3" json:"dmrid,omitempty"`
	// applier device dmrid
	ApplyDmrid uint32 `protobuf:"varint,2,opt,name=apply_dmrid,json=applyDmrid,proto3" json:"apply_dmrid,omitempty"`
}

func (m *ReqGpsPermission) Reset()         { *m = ReqGpsPermission{} }
func (m *ReqGpsPermission) String() string { return proto.CompactTextString(m) }
func (*ReqGpsPermission) ProtoMessage()    {}
func (*ReqGpsPermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{2}
}
func (m *ReqGpsPermission) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReqGpsPermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReqGpsPermission.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReqGpsPermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqGpsPermission.Merge(m, src)
}
func (m *ReqGpsPermission) XXX_Size() int {
	return m.Size()
}
func (m *ReqGpsPermission) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqGpsPermission.DiscardUnknown(m)
}

var xxx_messageInfo_ReqGpsPermission proto.InternalMessageInfo

func (m *ReqGpsPermission) GetDmrid() uint32 {
	if m != nil {
		return m.Dmrid
	}
	return 0
}

func (m *ReqGpsPermission) GetApplyDmrid() uint32 {
	if m != nil {
		return m.ApplyDmrid
	}
	return 0
}

// got apply response for device gps info permission
type ResGpsPermission struct {
	// response code
	//
	//0:ok 4:reject
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// device dmrid
	Dmrid uint32 `protobuf:"varint,2,opt,name=dmrid,proto3" json:"dmrid,omitempty"`
	// who grant the permission, user rid
	GrantUserRid string `protobuf:"bytes,3,opt,name=grant_user_rid,json=grantUserRid,proto3" json:"grant_user_rid,omitempty"`
	// grant user name
	GrantUserName string `protobuf:"bytes,4,opt,name=grant_user_name,json=grantUserName,proto3" json:"grant_user_name,omitempty"`
	// permission expire time
	ExpireTime string `protobuf:"bytes,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
}

func (m *ResGpsPermission) Reset()         { *m = ResGpsPermission{} }
func (m *ResGpsPermission) String() string { return proto.CompactTextString(m) }
func (*ResGpsPermission) ProtoMessage()    {}
func (*ResGpsPermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{3}
}
func (m *ResGpsPermission) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResGpsPermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResGpsPermission.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResGpsPermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResGpsPermission.Merge(m, src)
}
func (m *ResGpsPermission) XXX_Size() int {
	return m.Size()
}
func (m *ResGpsPermission) XXX_DiscardUnknown() {
	xxx_messageInfo_ResGpsPermission.DiscardUnknown(m)
}

var xxx_messageInfo_ResGpsPermission proto.InternalMessageInfo

func (m *ResGpsPermission) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ResGpsPermission) GetDmrid() uint32 {
	if m != nil {
		return m.Dmrid
	}
	return 0
}

func (m *ResGpsPermission) GetGrantUserRid() string {
	if m != nil {
		return m.GrantUserRid
	}
	return ""
}

func (m *ResGpsPermission) GetGrantUserName() string {
	if m != nil {
		return m.GrantUserName
	}
	return ""
}

func (m *ResGpsPermission) GetExpireTime() string {
	if m != nil {
		return m.ExpireTime
	}
	return ""
}

// 一个通用的gps84定位数据
type Gps84 struct {
	GpsTime   string  `protobuf:"bytes,1,opt,name=gps_time,json=gpsTime,proto3" json:"gps_time,omitempty"`
	Av        int32   `protobuf:"varint,2,opt,name=av,proto3" json:"av,omitempty"`
	Lat       float64 `protobuf:"fixed64,3,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon       float64 `protobuf:"fixed64,4,opt,name=lon,proto3" json:"lon,omitempty"`
	Speed     float64 `protobuf:"fixed64,5,opt,name=speed,proto3" json:"speed,omitempty"`
	Direction int32   `protobuf:"varint,6,opt,name=direction,proto3" json:"direction,omitempty"`
	Altitude  int32   `protobuf:"varint,7,opt,name=altitude,proto3" json:"altitude,omitempty"`
}

func (m *Gps84) Reset()         { *m = Gps84{} }
func (m *Gps84) String() string { return proto.CompactTextString(m) }
func (*Gps84) ProtoMessage()    {}
func (*Gps84) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{4}
}
func (m *Gps84) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Gps84) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Gps84.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Gps84) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Gps84.Merge(m, src)
}
func (m *Gps84) XXX_Size() int {
	return m.Size()
}
func (m *Gps84) XXX_DiscardUnknown() {
	xxx_messageInfo_Gps84.DiscardUnknown(m)
}

var xxx_messageInfo_Gps84 proto.InternalMessageInfo

func (m *Gps84) GetGpsTime() string {
	if m != nil {
		return m.GpsTime
	}
	return ""
}

func (m *Gps84) GetAv() int32 {
	if m != nil {
		return m.Av
	}
	return 0
}

func (m *Gps84) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *Gps84) GetLon() float64 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *Gps84) GetSpeed() float64 {
	if m != nil {
		return m.Speed
	}
	return 0
}

func (m *Gps84) GetDirection() int32 {
	if m != nil {
		return m.Direction
	}
	return 0
}

func (m *Gps84) GetAltitude() int32 {
	if m != nil {
		return m.Altitude
	}
	return 0
}

// got device gps info
type GpsInfo struct {
	// device dmrid
	Dmrid string `protobuf:"bytes,1,opt,name=dmrid,proto3" json:"dmrid,omitempty"`
	// device gps info
	GpsInfo *Gps84 `protobuf:"bytes,3,opt,name=gps_info,json=gpsInfo,proto3" json:"gps_info,omitempty"`
	// 1为用户主动定位
	ActiveStatus int32 `protobuf:"varint,4,opt,name=active_status,json=activeStatus,proto3" json:"active_status,omitempty"`
}

func (m *GpsInfo) Reset()         { *m = GpsInfo{} }
func (m *GpsInfo) String() string { return proto.CompactTextString(m) }
func (*GpsInfo) ProtoMessage()    {}
func (*GpsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{5}
}
func (m *GpsInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GpsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GpsInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GpsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GpsInfo.Merge(m, src)
}
func (m *GpsInfo) XXX_Size() int {
	return m.Size()
}
func (m *GpsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GpsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GpsInfo proto.InternalMessageInfo

func (m *GpsInfo) GetDmrid() string {
	if m != nil {
		return m.Dmrid
	}
	return ""
}

func (m *GpsInfo) GetGpsInfo() *Gps84 {
	if m != nil {
		return m.GpsInfo
	}
	return nil
}

func (m *GpsInfo) GetActiveStatus() int32 {
	if m != nil {
		return m.ActiveStatus
	}
	return 0
}

type ReqUpdateListenGroupList struct {
	ListenGroupList []string `protobuf:"bytes,1,rep,name=listen_group_list,json=listenGroupList,proto3" json:"listen_group_list,omitempty"`
}

func (m *ReqUpdateListenGroupList) Reset()         { *m = ReqUpdateListenGroupList{} }
func (m *ReqUpdateListenGroupList) String() string { return proto.CompactTextString(m) }
func (*ReqUpdateListenGroupList) ProtoMessage()    {}
func (*ReqUpdateListenGroupList) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{6}
}
func (m *ReqUpdateListenGroupList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReqUpdateListenGroupList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReqUpdateListenGroupList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReqUpdateListenGroupList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqUpdateListenGroupList.Merge(m, src)
}
func (m *ReqUpdateListenGroupList) XXX_Size() int {
	return m.Size()
}
func (m *ReqUpdateListenGroupList) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqUpdateListenGroupList.DiscardUnknown(m)
}

var xxx_messageInfo_ReqUpdateListenGroupList proto.InternalMessageInfo

func (m *ReqUpdateListenGroupList) GetListenGroupList() []string {
	if m != nil {
		return m.ListenGroupList
	}
	return nil
}

type ReqUpdateSpeakTarget struct {
	SpeakTarget string `protobuf:"bytes,1,opt,name=speak_target,json=speakTarget,proto3" json:"speak_target,omitempty"`
}

func (m *ReqUpdateSpeakTarget) Reset()         { *m = ReqUpdateSpeakTarget{} }
func (m *ReqUpdateSpeakTarget) String() string { return proto.CompactTextString(m) }
func (*ReqUpdateSpeakTarget) ProtoMessage()    {}
func (*ReqUpdateSpeakTarget) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{7}
}
func (m *ReqUpdateSpeakTarget) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReqUpdateSpeakTarget) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReqUpdateSpeakTarget.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReqUpdateSpeakTarget) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqUpdateSpeakTarget.Merge(m, src)
}
func (m *ReqUpdateSpeakTarget) XXX_Size() int {
	return m.Size()
}
func (m *ReqUpdateSpeakTarget) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqUpdateSpeakTarget.DiscardUnknown(m)
}

var xxx_messageInfo_ReqUpdateSpeakTarget proto.InternalMessageInfo

func (m *ReqUpdateSpeakTarget) GetSpeakTarget() string {
	if m != nil {
		return m.SpeakTarget
	}
	return ""
}

// 包含 单位 动态组 终端设备
type AddressBook struct {
	// 上级单位的dmrid，如果对应是单位则不需要层层嵌套
	ParentDmrid string `protobuf:"bytes,1,opt,name=parent_dmrid,json=parentDmrid,proto3" json:"parent_dmrid,omitempty"`
	Dmrid       string `protobuf:"bytes,2,opt,name=dmrid,proto3" json:"dmrid,omitempty"`
	// 设备名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 单位类型 111:虚拟机构  112:真实机构 100:临时组  101：任务组  102:自动失效的临时组
	// 设备类型 0:对讲机手台 1：车台 2:指挥座席 3:电话网关设备 4:中继虚拟终端 5:互联网关终端 6:模拟网关终端 7:数字网关终端 21:基地台  22:android模拟终端
	DevType int32 `protobuf:"varint,4,opt,name=devType,proto3" json:"devType,omitempty"`
	// 单位排序值
	OrgSortValue int32 `protobuf:"varint,5,opt,name=org_sort_value,json=orgSortValue,proto3" json:"org_sort_value,omitempty"`
}

func (m *AddressBook) Reset()         { *m = AddressBook{} }
func (m *AddressBook) String() string { return proto.CompactTextString(m) }
func (*AddressBook) ProtoMessage()    {}
func (*AddressBook) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{8}
}
func (m *AddressBook) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AddressBook) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AddressBook.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AddressBook) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddressBook.Merge(m, src)
}
func (m *AddressBook) XXX_Size() int {
	return m.Size()
}
func (m *AddressBook) XXX_DiscardUnknown() {
	xxx_messageInfo_AddressBook.DiscardUnknown(m)
}

var xxx_messageInfo_AddressBook proto.InternalMessageInfo

func (m *AddressBook) GetParentDmrid() string {
	if m != nil {
		return m.ParentDmrid
	}
	return ""
}

func (m *AddressBook) GetDmrid() string {
	if m != nil {
		return m.Dmrid
	}
	return ""
}

func (m *AddressBook) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddressBook) GetDevType() int32 {
	if m != nil {
		return m.DevType
	}
	return 0
}

func (m *AddressBook) GetOrgSortValue() int32 {
	if m != nil {
		return m.OrgSortValue
	}
	return 0
}

type ReqAddressBookResult struct {
	SuccessList []*AddressBook `protobuf:"bytes,1,rep,name=success_list,json=successList,proto3" json:"success_list,omitempty"`
	// 失败的dmrid
	FailedList []string `protobuf:"bytes,2,rep,name=failed_list,json=failedList,proto3" json:"failed_list,omitempty"`
}

func (m *ReqAddressBookResult) Reset()         { *m = ReqAddressBookResult{} }
func (m *ReqAddressBookResult) String() string { return proto.CompactTextString(m) }
func (*ReqAddressBookResult) ProtoMessage()    {}
func (*ReqAddressBookResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{9}
}
func (m *ReqAddressBookResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReqAddressBookResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReqAddressBookResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReqAddressBookResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqAddressBookResult.Merge(m, src)
}
func (m *ReqAddressBookResult) XXX_Size() int {
	return m.Size()
}
func (m *ReqAddressBookResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqAddressBookResult.DiscardUnknown(m)
}

var xxx_messageInfo_ReqAddressBookResult proto.InternalMessageInfo

func (m *ReqAddressBookResult) GetSuccessList() []*AddressBook {
	if m != nil {
		return m.SuccessList
	}
	return nil
}

func (m *ReqAddressBookResult) GetFailedList() []string {
	if m != nil {
		return m.FailedList
	}
	return nil
}

type AddressBookList struct {
	AddrBookList []*AddressBook `protobuf:"bytes,1,rep,name=addr_book_list,json=addrBookList,proto3" json:"addr_book_list,omitempty"`
}

func (m *AddressBookList) Reset()         { *m = AddressBookList{} }
func (m *AddressBookList) String() string { return proto.CompactTextString(m) }
func (*AddressBookList) ProtoMessage()    {}
func (*AddressBookList) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{10}
}
func (m *AddressBookList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AddressBookList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AddressBookList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AddressBookList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddressBookList.Merge(m, src)
}
func (m *AddressBookList) XXX_Size() int {
	return m.Size()
}
func (m *AddressBookList) XXX_DiscardUnknown() {
	xxx_messageInfo_AddressBookList.DiscardUnknown(m)
}

var xxx_messageInfo_AddressBookList proto.InternalMessageInfo

func (m *AddressBookList) GetAddrBookList() []*AddressBook {
	if m != nil {
		return m.AddrBookList
	}
	return nil
}

type ServerAddr struct {
	Host string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
}

func (m *ServerAddr) Reset()         { *m = ServerAddr{} }
func (m *ServerAddr) String() string { return proto.CompactTextString(m) }
func (*ServerAddr) ProtoMessage()    {}
func (*ServerAddr) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{11}
}
func (m *ServerAddr) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServerAddr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ServerAddr.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ServerAddr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerAddr.Merge(m, src)
}
func (m *ServerAddr) XXX_Size() int {
	return m.Size()
}
func (m *ServerAddr) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerAddr.DiscardUnknown(m)
}

var xxx_messageInfo_ServerAddr proto.InternalMessageInfo

func (m *ServerAddr) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

func (m *ServerAddr) GetPort() int32 {
	if m != nil {
		return m.Port
	}
	return 0
}

type ShortMessages struct {
	// 发起者dmrid
	SenderDmrid string `protobuf:"bytes,4,opt,name=sender_dmrid,json=senderDmrid,proto3" json:"sender_dmrid,omitempty"`
	// 接收方dmrid
	TargetDmrid string `protobuf:"bytes,5,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 短信内容 utf16 编码
	SmsContent string `protobuf:"bytes,7,opt,name=sms_content,json=smsContent,proto3" json:"sms_content,omitempty"`
	// 短信序号
	SmsNo int32 `protobuf:"varint,8,opt,name=sms_no,json=smsNo,proto3" json:"sms_no,omitempty"`
	// 编码格式 2.utf16(目前手台只支持此编码)
	Codec int32 `protobuf:"varint,10,opt,name=codec,proto3" json:"codec,omitempty"`
	// 短信类型 0:普通短信 1:自动播报短信
	SmsType int32 `protobuf:"varint,11,opt,name=sms_type,json=smsType,proto3" json:"sms_type,omitempty"`
	// 接受时间
	Time int64 `protobuf:"varint,12,opt,name=time,proto3" json:"time,omitempty"`
}

func (m *ShortMessages) Reset()         { *m = ShortMessages{} }
func (m *ShortMessages) String() string { return proto.CompactTextString(m) }
func (*ShortMessages) ProtoMessage()    {}
func (*ShortMessages) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{12}
}
func (m *ShortMessages) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ShortMessages) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ShortMessages.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ShortMessages) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShortMessages.Merge(m, src)
}
func (m *ShortMessages) XXX_Size() int {
	return m.Size()
}
func (m *ShortMessages) XXX_DiscardUnknown() {
	xxx_messageInfo_ShortMessages.DiscardUnknown(m)
}

var xxx_messageInfo_ShortMessages proto.InternalMessageInfo

func (m *ShortMessages) GetSenderDmrid() string {
	if m != nil {
		return m.SenderDmrid
	}
	return ""
}

func (m *ShortMessages) GetTargetDmrid() string {
	if m != nil {
		return m.TargetDmrid
	}
	return ""
}

func (m *ShortMessages) GetSmsContent() string {
	if m != nil {
		return m.SmsContent
	}
	return ""
}

func (m *ShortMessages) GetSmsNo() int32 {
	if m != nil {
		return m.SmsNo
	}
	return 0
}

func (m *ShortMessages) GetCodec() int32 {
	if m != nil {
		return m.Codec
	}
	return 0
}

func (m *ShortMessages) GetSmsType() int32 {
	if m != nil {
		return m.SmsType
	}
	return 0
}

func (m *ShortMessages) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

type Notify struct {
	// 1.打开麦克风失败 2.kcp断开  5,kcp链接服务器失败
	// 6.播放语音历史开始(paraStr为当前播放历史记录"repeaterIdHex-sourceHex-timeStampHex")  7.播放语音历史结束
	// 9.录音历史下载失败
	// 0xcb36.加入临时组  0xcb37.退出临时组 0xcb38.加入任务组  0xcb39.退出任务组
	// 333.sync online devices,body = bfkcp.ex_oneline_devices
	// 8.收到他人讲话通知。非bc15.body = bfkcp.dev_data_info
	// 11.正在播放他人语音 paramStr=sourceHex-targetHex 12.正在播放语音历史。paramStr=repeaterIdHex-sourceHex-timeStampHex
	// 20.收到gps定位信息, body = gps_info
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 额外的附加信息
	// 0xcb37/0xcb39 groupDmrid
	ParamStr string `protobuf:"bytes,2,opt,name=paramStr,proto3" json:"paramStr,omitempty"`
	// code = 0xcb38,body = AddressBook
	// code = 0xcb36,body = AddressBook
	Body []byte `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
}

func (m *Notify) Reset()         { *m = Notify{} }
func (m *Notify) String() string { return proto.CompactTextString(m) }
func (*Notify) ProtoMessage()    {}
func (*Notify) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{13}
}
func (m *Notify) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Notify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Notify.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Notify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Notify.Merge(m, src)
}
func (m *Notify) XXX_Size() int {
	return m.Size()
}
func (m *Notify) XXX_DiscardUnknown() {
	xxx_messageInfo_Notify.DiscardUnknown(m)
}

var xxx_messageInfo_Notify proto.InternalMessageInfo

func (m *Notify) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *Notify) GetParamStr() string {
	if m != nil {
		return m.ParamStr
	}
	return ""
}

func (m *Notify) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

type VoiceConfig struct {
	// 声音放大参数 //基准为1000  [500,10000]测试一下
	Gain int32 `protobuf:"varint,3,opt,name=gain,proto3" json:"gain,omitempty"`
	// 播放媒体缓冲区（*个包） 一个包 60ms
	// 范围 [8,25]  即 约[0.5,1.5]秒
	// 超过这个大小，自动开始 播放
	MediaAutoStartSize int32 `protobuf:"varint,4,opt,name=mediaAutoStartSize,proto3" json:"mediaAutoStartSize,omitempty"`
	// 媒体总缓冲区大小（*个包） 一个包 60ms
	// 范围 [250,500] 即[15,30]秒
	MediaBufferSize int32 `protobuf:"varint,5,opt,name=mediaBufferSize,proto3" json:"mediaBufferSize,omitempty"`
	// 讲话超时 *秒
	SpeakTimeout int32 `protobuf:"varint,6,opt,name=speakTimeout,proto3" json:"speakTimeout,omitempty"`
	// denoise setting
	// 0: off, 1: ambe denoise 2:rnnoise
	DenoiseSetting int32 `protobuf:"varint,8,opt,name=denoiseSetting,proto3" json:"denoiseSetting,omitempty"`
	// debug recorder pcm_data
	// 0: off 1:on
	DebugRecorderPcm int32 `protobuf:"varint,9,opt,name=debugRecorderPcm,proto3" json:"debugRecorderPcm,omitempty"`
	// recorder pcm gain
	// xxx% 10-1000
	RecorderPcmGain int32 `protobuf:"varint,10,opt,name=recorderPcmGain,proto3" json:"recorderPcmGain,omitempty"`
}

func (m *VoiceConfig) Reset()         { *m = VoiceConfig{} }
func (m *VoiceConfig) String() string { return proto.CompactTextString(m) }
func (*VoiceConfig) ProtoMessage()    {}
func (*VoiceConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_575058d622b9a3cc, []int{14}
}
func (m *VoiceConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *VoiceConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_VoiceConfig.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *VoiceConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoiceConfig.Merge(m, src)
}
func (m *VoiceConfig) XXX_Size() int {
	return m.Size()
}
func (m *VoiceConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_VoiceConfig.DiscardUnknown(m)
}

var xxx_messageInfo_VoiceConfig proto.InternalMessageInfo

func (m *VoiceConfig) GetGain() int32 {
	if m != nil {
		return m.Gain
	}
	return 0
}

func (m *VoiceConfig) GetMediaAutoStartSize() int32 {
	if m != nil {
		return m.MediaAutoStartSize
	}
	return 0
}

func (m *VoiceConfig) GetMediaBufferSize() int32 {
	if m != nil {
		return m.MediaBufferSize
	}
	return 0
}

func (m *VoiceConfig) GetSpeakTimeout() int32 {
	if m != nil {
		return m.SpeakTimeout
	}
	return 0
}

func (m *VoiceConfig) GetDenoiseSetting() int32 {
	if m != nil {
		return m.DenoiseSetting
	}
	return 0
}

func (m *VoiceConfig) GetDebugRecorderPcm() int32 {
	if m != nil {
		return m.DebugRecorderPcm
	}
	return 0
}

func (m *VoiceConfig) GetRecorderPcmGain() int32 {
	if m != nil {
		return m.RecorderPcmGain
	}
	return 0
}

func init() {
	proto.RegisterEnum("app_proto.MediaStatus", MediaStatus_name, MediaStatus_value)
	proto.RegisterEnum("app_proto.ResCode", ResCode_name, ResCode_value)
	proto.RegisterEnum("app_proto.CmdCode", CmdCode_name, CmdCode_value)
	proto.RegisterType((*ReqLogin)(nil), "app_proto.req_login")
	proto.RegisterType((*RespLogin)(nil), "app_proto.resp_login")
	proto.RegisterType((*ReqGpsPermission)(nil), "app_proto.req_gps_permission")
	proto.RegisterType((*ResGpsPermission)(nil), "app_proto.res_gps_permission")
	proto.RegisterType((*Gps84)(nil), "app_proto.gps84")
	proto.RegisterType((*GpsInfo)(nil), "app_proto.gps_info")
	proto.RegisterType((*ReqUpdateListenGroupList)(nil), "app_proto.req_update_listen_group_list")
	proto.RegisterType((*ReqUpdateSpeakTarget)(nil), "app_proto.req_update_speak_target")
	proto.RegisterType((*AddressBook)(nil), "app_proto.address_book")
	proto.RegisterType((*ReqAddressBookResult)(nil), "app_proto.req_address_book_result")
	proto.RegisterType((*AddressBookList)(nil), "app_proto.address_book_list")
	proto.RegisterType((*ServerAddr)(nil), "app_proto.server_addr")
	proto.RegisterType((*ShortMessages)(nil), "app_proto.short_messages")
	proto.RegisterType((*Notify)(nil), "app_proto.Notify")
	proto.RegisterType((*VoiceConfig)(nil), "app_proto.voice_config")
}

func init() { proto.RegisterFile("app_proto.proto", fileDescriptor_575058d622b9a3cc) }

var fileDescriptor_575058d622b9a3cc = []byte{
	// 2147 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x58, 0xdd, 0x77, 0x1c, 0x37,
	0x15, 0xcf, 0xda, 0x5e, 0x7b, 0xf6, 0xee, 0xda, 0x51, 0x94, 0x38, 0xde, 0xd8, 0x89, 0xe3, 0xac,
	0xe3, 0xc4, 0x75, 0xda, 0x9c, 0x26, 0x34, 0xb4, 0x94, 0x16, 0x68, 0x12, 0x48, 0xd3, 0x26, 0x26,
	0xac, 0xd3, 0x3e, 0xf0, 0x32, 0x47, 0x9e, 0xd1, 0xae, 0xe7, 0x78, 0x66, 0x34, 0x91, 0xb4, 0x4b,
	0x96, 0xbf, 0x82, 0xaf, 0x37, 0xce, 0xe1, 0x91, 0x37, 0xfe, 0x03, 0x78, 0xe7, 0x70, 0x28, 0x04,
	0x0a, 0x87, 0x6f, 0x28, 0x09, 0x3c, 0xf0, 0xca, 0x5f, 0xd0, 0x23, 0x69, 0x3e, 0xa4, 0xdd, 0xb5,
	0xf3, 0x62, 0x4b, 0xbf, 0xfb, 0xd3, 0xd5, 0xd5, 0xfd, 0x92, 0x66, 0xe1, 0x24, 0xc9, 0x32, 0x3f,
	0xe3, 0x4c, 0xb2, 0xeb, 0xfa, 0x2f, 0x6e, 0x94, 0xc0, 0x6a, 0x4b, 0x0d, 0xc3, 0x7d, 0x23, 0xe8,
	0xfc, 0xaa, 0x06, 0x0d, 0x4e, 0x9f, 0xf8, 0x31, 0xeb, 0x47, 0x29, 0x5e, 0x86, 0x79, 0x31, 0x12,
	0x7e, 0x14, 0xb6, 0x6b, 0x1b, 0xb5, 0xed, 0x46, 0xb7, 0x2e, 0x46, 0xe2, 0x7e, 0x88, 0xd7, 0xa0,
	0x31, 0x10, 0x94, 0xfb, 0x29, 0x49, 0x68, 0x7b, 0x46, 0x4b, 0x3c, 0x05, 0xec, 0x92, 0x84, 0x96,
	0xc2, 0x8c, 0x08, 0xd1, 0x9e, 0xad, 0x84, 0x8f, 0x88, 0x10, 0xf8, 0x12, 0xb4, 0xb4, 0x66, 0x3f,
	0xa1, 0xf2, 0x80, 0x85, 0xed, 0xb9, 0x8d, 0xda, 0x76, 0xbd, 0xdb, 0xd4, 0xd8, 0x43, 0x0d, 0xe1,
	0x2b, 0x70, 0x32, 0x20, 0xa9, 0x1f, 0x46, 0x22, 0x8b, 0xc9, 0xc8, 0x4f, 0x48, 0xd6, 0xae, 0x6f,
	0xd4, 0xb6, 0xbd, 0xee, 0x62, 0x40, 0xd2, 0xbb, 0x06, 0x7d, 0x48, 0x32, 0xa5, 0x2a, 0xe3, 0xb4,
	0x47, 0xb9, 0x1f, 0xb0, 0x90, 0x06, 0xed, 0x79, 0xa3, 0xca, 0x60, 0x77, 0x14, 0xd4, 0xf9, 0xa4,
	0x06, 0xc0, 0xa9, 0xc8, 0xf2, 0xd3, 0xdc, 0x80, 0xe5, 0x48, 0xf8, 0x82, 0xf2, 0x21, 0xe5, 0xbe,
	0x18, 0x64, 0x19, 0xe3, 0x52, 0xeb, 0xaf, 0x69, 0xfd, 0x38, 0x12, 0x7b, 0x5a, 0xb6, 0x67, 0x44,
	0x6a, 0x93, 0x2d, 0x58, 0xca, 0xf9, 0x43, 0xca, 0x45, 0xc4, 0xd2, 0xfc, 0xb8, 0x8b, 0x06, 0xfd,
	0xd8, 0x80, 0xf8, 0x4d, 0x68, 0x0b, 0x2a, 0x65, 0x94, 0xf6, 0xfd, 0x98, 0x08, 0xe9, 0x0f, 0xb2,
	0x90, 0x48, 0xea, 0xcb, 0x28, 0xa1, 0xb9, 0x0b, 0x96, 0x73, 0xf9, 0x03, 0x22, 0xe4, 0x47, 0x5a,
	0xfa, 0x38, 0x4a, 0x28, 0x7e, 0x15, 0xe6, 0x43, 0x3a, 0x8c, 0x02, 0xaa, 0x3d, 0xd1, 0xbc, 0x79,
	0xe6, 0x7a, 0x15, 0xa9, 0x70, 0xdf, 0x37, 0xb2, 0x6e, 0xce, 0xe9, 0x7c, 0x08, 0x58, 0xc5, 0xa6,
	0x9f, 0x09, 0x3f, 0xa3, 0x3c, 0x89, 0x84, 0xde, 0xfc, 0x0c, 0xd4, 0xc3, 0x84, 0xe7, 0x31, 0x5a,
	0xec, 0x9a, 0x09, 0xbe, 0x08, 0x4d, 0x92, 0x65, 0xf1, 0xc8, 0x37, 0xb2, 0x19, 0x2d, 0x03, 0x0d,
	0xdd, 0x55, 0x48, 0xe7, 0x67, 0x35, 0xa5, 0x4d, 0x8c, 0x6b, 0xc3, 0x30, 0xa7, 0xfc, 0xa9, 0x95,
	0xd5, 0xbb, 0x7a, 0x5c, 0xed, 0x30, 0x63, 0xef, 0x70, 0x19, 0x96, 0xfa, 0x9c, 0xa4, 0xd2, 0xd7,
	0xe1, 0x56, 0x62, 0x73, 0xd4, 0x96, 0x46, 0x3f, 0x12, 0x94, 0x77, 0x23, 0x1d, 0x4e, 0x8b, 0xa5,
	0x33, 0x66, 0xce, 0xb8, 0xb0, 0xa4, 0xe9, 0xb4, 0xb9, 0x08, 0x4d, 0xfa, 0x34, 0x8b, 0x78, 0xee,
	0xb5, 0xba, 0xe6, 0x80, 0x81, 0x94, 0xab, 0x3a, 0x3f, 0xad, 0x41, 0xbd, 0x9f, 0x89, 0xb7, 0xde,
	0xc0, 0xe7, 0xc0, 0x53, 0x46, 0x6b, 0x9e, 0xc9, 0xcb, 0x85, 0x7e, 0x26, 0xb4, 0x3f, 0x97, 0x60,
	0x86, 0x0c, 0xb5, 0x99, 0xf5, 0xee, 0x0c, 0x19, 0x62, 0x04, 0xb3, 0x31, 0x91, 0xda, 0xb0, 0x5a,
	0x57, 0x0d, 0x35, 0xc2, 0x52, 0x6d, 0x83, 0x42, 0x8c, 0xff, 0x44, 0x46, 0x69, 0xa8, 0xf7, 0xac,
	0x75, 0xcd, 0x04, 0x9f, 0x87, 0x46, 0x18, 0x71, 0x1a, 0x48, 0x15, 0x74, 0x93, 0x5b, 0x15, 0x80,
	0x57, 0xc1, 0x23, 0xb1, 0x8c, 0xe4, 0x20, 0xa4, 0xed, 0x05, 0x2d, 0x2c, 0xe7, 0x1d, 0x6e, 0xcc,
	0x8b, 0xd2, 0x1e, 0x73, 0x63, 0xd3, 0x28, 0x3c, 0x77, 0xad, 0x62, 0x68, 0xd3, 0x9a, 0x37, 0x91,
	0x15, 0x77, 0x7d, 0x48, 0x7d, 0xa4, 0xfb, 0x4a, 0xc5, 0x26, 0x2c, 0x92, 0x40, 0x46, 0x43, 0xea,
	0x0b, 0x49, 0xe4, 0x40, 0xe4, 0x35, 0xd3, 0x32, 0xe0, 0x9e, 0xc6, 0x3a, 0x1f, 0xc0, 0x79, 0x95,
	0x19, 0x79, 0xde, 0xc5, 0x91, 0x90, 0x34, 0xf5, 0xfb, 0x9c, 0x0d, 0x32, 0x3d, 0xc1, 0x3b, 0x70,
	0x6a, 0x02, 0x6c, 0xd7, 0x36, 0x66, 0xb7, 0x1b, 0xdd, 0x93, 0x46, 0x70, 0x4f, 0xe1, 0x0f, 0x22,
	0x21, 0x3b, 0xef, 0xc0, 0x8a, 0xa5, 0x4b, 0x64, 0x94, 0x1c, 0xfa, 0x92, 0xf0, 0x3e, 0x95, 0xaa,
	0xe6, 0xec, 0x79, 0x7e, 0xaa, 0xa6, 0xc6, 0x1e, 0x6b, 0xa8, 0xf3, 0xe3, 0x1a, 0xb4, 0x48, 0x18,
	0x72, 0x2a, 0x84, 0xbf, 0xcf, 0xd8, 0xa1, 0xae, 0x53, 0xc2, 0x69, 0x2a, 0x7d, 0xdb, 0x13, 0x4d,
	0x83, 0xe9, 0x54, 0x74, 0xf3, 0xab, 0xf4, 0x12, 0x86, 0x39, 0x9d, 0x2e, 0x26, 0xab, 0xf4, 0x18,
	0xb7, 0x61, 0x21, 0xa4, 0xc3, 0xc7, 0xa3, 0x8c, 0xe6, 0x6e, 0x28, 0xa6, 0x2a, 0x1b, 0x19, 0xef,
	0xfb, 0x42, 0xd5, 0xf4, 0x90, 0xc4, 0x03, 0x93, 0x42, 0xf5, 0x6e, 0x8b, 0xf1, 0xfe, 0x1e, 0xe3,
	0xf2, 0x63, 0x85, 0x75, 0x86, 0xe6, 0x6c, 0xb6, 0x81, 0x3e, 0xa7, 0x62, 0x10, 0x4b, 0xfc, 0x36,
	0xb4, 0xc4, 0x20, 0x08, 0x14, 0x5c, 0x7a, 0xa7, 0x79, 0x73, 0xc5, 0x0a, 0x8c, 0xbd, 0xaa, 0xdb,
	0xcc, 0xc9, 0xca, 0x65, 0x2a, 0x79, 0x7b, 0x24, 0x8a, 0x69, 0x68, 0x96, 0xce, 0x68, 0xc7, 0x82,
	0x81, 0xb4, 0x4f, 0xbb, 0x70, 0xca, 0xd9, 0x53, 0x07, 0xe5, 0x5d, 0x58, 0x52, 0x60, 0x85, 0xbc,
	0x6c, 0x4f, 0xed, 0xd8, 0xdb, 0x8c, 0x1d, 0x6a, 0x9d, 0xb7, 0xa0, 0x99, 0xf7, 0x26, 0x05, 0x2b,
	0x77, 0x1d, 0x30, 0x51, 0xc4, 0x44, 0x8f, 0x15, 0xa6, 0x3a, 0x59, 0x5e, 0x10, 0x7a, 0xdc, 0xf9,
	0xb4, 0x06, 0x4b, 0xe2, 0x40, 0xb7, 0x3e, 0x2a, 0x04, 0xe9, 0x53, 0xdd, 0x95, 0x05, 0x4d, 0x43,
	0xca, 0xf3, 0x10, 0xcd, 0xe5, 0x61, 0xd5, 0x98, 0x09, 0xd1, 0x25, 0x68, 0x99, 0x98, 0xe7, 0x14,
	0x53, 0x9f, 0x4d, 0x83, 0xdd, 0x2d, 0x3a, 0x8e, 0x48, 0x84, 0x1f, 0xb0, 0x54, 0xd2, 0x54, 0xea,
	0xb2, 0x68, 0x74, 0x41, 0x24, 0xe2, 0x8e, 0x41, 0xf4, 0x6d, 0x92, 0x08, 0x3f, 0x65, 0x6d, 0x4f,
	0xdb, 0x53, 0x17, 0x89, 0xd8, 0xd5, 0x35, 0x62, 0x3a, 0x38, 0x18, 0x54, 0x4f, 0x54, 0x91, 0x2b,
	0xb2, 0x54, 0xa1, 0x6e, 0x9a, 0x50, 0x8b, 0x44, 0xe8, 0x50, 0x63, 0x98, 0xd3, 0xb5, 0xdf, 0xda,
	0xa8, 0x6d, 0xcf, 0x76, 0xf5, 0xb8, 0xf3, 0x00, 0xe6, 0x77, 0x99, 0x8c, 0x7a, 0xa3, 0xa9, 0x0d,
	0x6c, 0x15, 0xbc, 0x8c, 0x70, 0x92, 0xec, 0x49, 0x5e, 0xdc, 0x57, 0xc5, 0x5c, 0xf1, 0xf7, 0x59,
	0x38, 0xd2, 0x69, 0xd6, 0xea, 0xea, 0x71, 0xe7, 0x27, 0x33, 0xd0, 0x1a, 0xb2, 0x28, 0xa0, 0xea,
	0x34, 0xbd, 0xa8, 0xaf, 0x48, 0x7d, 0x12, 0xa5, 0x9a, 0x54, 0xef, 0xea, 0x31, 0xbe, 0x0e, 0x38,
	0xa1, 0x61, 0x44, 0xde, 0x1b, 0x48, 0xb6, 0x27, 0x09, 0x97, 0x7b, 0xd1, 0x77, 0x8b, 0xb4, 0x9c,
	0x22, 0xc1, 0xdb, 0x70, 0x52, 0xa3, 0xb7, 0x07, 0xbd, 0x1e, 0xe5, 0x9a, 0x6c, 0x52, 0x74, 0x1c,
	0xc6, 0x9d, 0xbc, 0xcc, 0x54, 0x4b, 0x63, 0x03, 0x99, 0xb7, 0x1f, 0x07, 0xc3, 0x57, 0x60, 0x29,
	0xa4, 0x29, 0x8b, 0x04, 0xdd, 0x33, 0x37, 0x4b, 0xee, 0xd4, 0x31, 0x14, 0xef, 0x00, 0x0a, 0xe9,
	0xfe, 0xa0, 0xdf, 0xa5, 0x01, 0xe3, 0x21, 0xe5, 0x8f, 0x82, 0xa4, 0xdd, 0xd0, 0xcc, 0x09, 0x5c,
	0x59, 0xc8, 0xab, 0xe9, 0x3d, 0x75, 0x60, 0x13, 0x93, 0x71, 0x78, 0x67, 0x0b, 0x5a, 0xda, 0xe8,
	0xbc, 0x27, 0xe1, 0x06, 0xd4, 0x85, 0x3a, 0x28, 0x3a, 0x81, 0x01, 0xe6, 0x85, 0x64, 0x19, 0x0d,
	0x51, 0x6d, 0x67, 0x13, 0x3c, 0x75, 0xc5, 0xe8, 0x18, 0x34, 0x61, 0x21, 0x2f, 0x19, 0x43, 0xea,
	0x45, 0x24, 0xd6, 0xa4, 0xff, 0xaf, 0x82, 0x17, 0x24, 0xa1, 0x61, 0x21, 0x68, 0xa9, 0xb1, 0x2a,
	0xd2, 0x2c, 0x4a, 0xfb, 0xe8, 0x04, 0x3e, 0x05, 0x8b, 0x06, 0x11, 0x99, 0x81, 0x6a, 0x15, 0x94,
	0xbf, 0x53, 0xd0, 0x0c, 0xc6, 0xb0, 0x54, 0xb2, 0x0c, 0x36, 0x8b, 0x2f, 0xc3, 0x46, 0x41, 0x3b,
	0xaa, 0x31, 0xa2, 0x39, 0xbc, 0x05, 0x97, 0xca, 0x95, 0x47, 0xd2, 0xea, 0x78, 0x13, 0x2e, 0x16,
	0xca, 0x9e, 0x0c, 0x28, 0x1f, 0x4d, 0x21, 0xcd, 0x57, 0x3b, 0x8a, 0xec, 0x48, 0xd6, 0x02, 0xbe,
	0x08, 0x6b, 0x63, 0x76, 0xd9, 0x4d, 0x15, 0x79, 0x78, 0x03, 0xce, 0x8f, 0x9b, 0xe4, 0x30, 0x1a,
	0x78, 0x1d, 0x56, 0x5d, 0x6b, 0x1c, 0x39, 0x54, 0x5b, 0x94, 0x86, 0x38, 0x84, 0xe6, 0xa4, 0x02,
	0xbb, 0xd1, 0xa0, 0xd6, 0x14, 0x05, 0x0e, 0x61, 0x11, 0xaf, 0xc0, 0xe9, 0x42, 0x81, 0x51, 0x6d,
	0xe2, 0xbf, 0x84, 0xdb, 0x70, 0xa6, 0x5c, 0x69, 0x4b, 0x4e, 0xe2, 0xb3, 0x80, 0xc7, 0x97, 0xb0,
	0x0c, 0xa1, 0x4a, 0x95, 0xb5, 0x82, 0x65, 0xe8, 0x54, 0xa5, 0xca, 0xda, 0x43, 0x0e, 0x04, 0xc2,
	0xf8, 0x1c, 0x2c, 0x4f, 0x6e, 0xa2, 0x44, 0xa7, 0xf1, 0x05, 0x38, 0x57, 0x2c, 0x32, 0x29, 0xaa,
	0x9f, 0x91, 0xb9, 0xf8, 0x4c, 0x75, 0x70, 0x91, 0x4d, 0x91, 0x2f, 0xe3, 0x25, 0x00, 0x25, 0x4f,
	0x75, 0x33, 0x41, 0x67, 0x0b, 0xa3, 0xdd, 0x8e, 0x89, 0xda, 0x55, 0x3e, 0x88, 0xcc, 0x34, 0x09,
	0x9e, 0x8c, 0x93, 0xce, 0xe1, 0x35, 0x58, 0xd1, 0x8b, 0x69, 0x3a, 0xa1, 0x61, 0xd5, 0x89, 0xf2,
	0x34, 0xc6, 0x1a, 0x3e, 0x03, 0xa8, 0x38, 0x4a, 0x24, 0xf2, 0xb4, 0x3e, 0x8f, 0x97, 0xe1, 0x54,
	0xb9, 0xae, 0x84, 0x2f, 0xe0, 0x55, 0x38, 0x6b, 0x91, 0x03, 0x96, 0xa6, 0xf9, 0x53, 0x17, 0xad,
	0x17, 0x76, 0x14, 0x4b, 0x6c, 0xe1, 0x45, 0x3b, 0x15, 0x8a, 0x64, 0xab, 0xae, 0x15, 0xb4, 0xe1,
	0xa4, 0xc2, 0x14, 0xc2, 0x25, 0x7c, 0x1e, 0xda, 0x63, 0xa5, 0xa1, 0x9f, 0xf8, 0xea, 0xc1, 0x87,
	0x3a, 0x55, 0x3c, 0xaa, 0x9a, 0xa8, 0xc4, 0x9b, 0x95, 0x69, 0xee, 0xe2, 0x90, 0x0e, 0xd1, 0xe5,
	0x4a, 0xf3, 0xd8, 0x5a, 0x25, 0xdd, 0xc2, 0x57, 0x61, 0xb3, 0x4c, 0x0f, 0x2a, 0x8b, 0x0c, 0x8f,
	0x12, 0xea, 0xb3, 0x81, 0xf4, 0xc3, 0x01, 0x27, 0xea, 0xad, 0x86, 0xae, 0xe0, 0x6d, 0xb8, 0x6c,
	0x79, 0xfa, 0x68, 0xe6, 0x55, 0x5b, 0x65, 0xff, 0x18, 0xe2, 0xb6, 0xa3, 0xf2, 0x38, 0xe6, 0x2b,
	0x53, 0xaa, 0xdd, 0xbe, 0x59, 0xd0, 0xce, 0xb4, 0x6a, 0x77, 0x18, 0xd7, 0x6c, 0x15, 0x21, 0x8d,
	0xe9, 0x58, 0x87, 0x42, 0x6f, 0x38, 0x2a, 0xa6, 0x31, 0x6e, 0xd9, 0x31, 0x22, 0x61, 0xe8, 0x4a,
	0xbf, 0xe8, 0xc4, 0x68, 0x42, 0xfc, 0xa6, 0x5d, 0xb8, 0xc6, 0xff, 0x4f, 0x06, 0x91, 0x44, 0x6f,
	0x39, 0x85, 0x6b, 0x09, 0xbe, 0x84, 0xaf, 0x40, 0xc7, 0x0d, 0x6a, 0x48, 0x7b, 0x64, 0x10, 0x4b,
	0xb7, 0x0b, 0xbd, 0x5d, 0xb9, 0xbb, 0x8c, 0xef, 0x54, 0xe2, 0x97, 0xed, 0x56, 0xee, 0xf2, 0x42,
	0x3a, 0x2c, 0xfc, 0xf4, 0x8e, 0xd3, 0xca, 0x8f, 0xa4, 0xbd, 0x8b, 0x3b, 0xb0, 0x5e, 0xde, 0x31,
	0xaa, 0xf6, 0x63, 0x16, 0x90, 0xd8, 0x0f, 0x48, 0x70, 0x40, 0x4d, 0x4b, 0x40, 0x5f, 0x71, 0xca,
	0xfb, 0x08, 0xd2, 0x57, 0x6d, 0x45, 0x66, 0xbb, 0x80, 0xc4, 0xb1, 0xbf, 0x4f, 0x82, 0xd2, 0xf2,
	0xaf, 0x39, 0x8a, 0x8e, 0x20, 0xbd, 0x67, 0x2b, 0x0a, 0x62, 0x4a, 0xf8, 0x24, 0xe7, 0xb6, 0xdb,
	0x70, 0xa6, 0x93, 0xee, 0x38, 0x25, 0x21, 0xd9, 0x91, 0xa6, 0xdf, 0x75, 0x4b, 0xe2, 0x18, 0xe6,
	0xd7, 0xed, 0xf6, 0xa0, 0x6a, 0x27, 0xbf, 0xf6, 0x59, 0x4f, 0x7e, 0x87, 0x70, 0x8a, 0xbe, 0xe1,
	0xb4, 0x87, 0x29, 0x84, 0x7b, 0x93, 0xb1, 0xab, 0x1e, 0xbd, 0xfb, 0xf9, 0xe7, 0x29, 0x7a, 0x7f,
	0x4a, 0xec, 0xa6, 0xd0, 0xee, 0xe3, 0x6b, 0x70, 0xf5, 0xa5, 0x34, 0xf5, 0x74, 0x7e, 0x3a, 0x42,
	0x1f, 0x54, 0x9d, 0xf2, 0x89, 0xfa, 0xcc, 0xf7, 0x25, 0x3b, 0xa4, 0x29, 0xfa, 0xb0, 0x4a, 0x67,
	0x75, 0x05, 0x94, 0xf8, 0x03, 0xfb, 0xe6, 0x50, 0x9f, 0x67, 0xca, 0x19, 0xaa, 0x86, 0x7d, 0x96,
	0x06, 0x14, 0x3d, 0xb4, 0x3b, 0xd5, 0x98, 0x18, 0xed, 0xda, 0xf5, 0xe5, 0x0a, 0x7b, 0x3d, 0xf4,
	0xcd, 0xa2, 0x91, 0xf7, 0x99, 0xf4, 0xb3, 0x20, 0xf1, 0x43, 0x22, 0x09, 0x7a, 0x84, 0x77, 0xe0,
	0x4a, 0x55, 0xd6, 0xea, 0x03, 0xdf, 0x5d, 0x5a, 0x7d, 0x94, 0xa3, 0x6f, 0x39, 0xe7, 0x7e, 0x09,
	0xb9, 0x8b, 0x5f, 0x87, 0x57, 0xc7, 0xab, 0xe5, 0xd8, 0x15, 0x7b, 0xf8, 0x06, 0xbc, 0x36, 0x51,
	0x39, 0xc7, 0x2e, 0x79, 0x8c, 0x17, 0xcd, 0xab, 0x8d, 0x3e, 0x8d, 0x24, 0xfa, 0xdf, 0x02, 0x3e,
	0x6d, 0x1e, 0x60, 0x3d, 0xc6, 0x03, 0x6a, 0xc0, 0x9f, 0xcf, 0x16, 0x9c, 0xfd, 0xe0, 0xc6, 0x2d,
	0xf4, 0x8b, 0x59, 0xbc, 0x5a, 0xdc, 0xda, 0x65, 0xbd, 0xb0, 0x54, 0x92, 0x40, 0xa2, 0x5f, 0x7b,
	0x78, 0xad, 0xb8, 0xbe, 0xaa, 0x3a, 0xc9, 0x85, 0x9f, 0x78, 0xf8, 0xf2, 0xf8, 0xe3, 0x2b, 0x63,
	0x41, 0x59, 0xdb, 0xa6, 0x4b, 0xfd, 0xc6, 0xc3, 0x5b, 0x13, 0xaf, 0xaf, 0x49, 0xda, 0x6f, 0x1d,
	0x65, 0x79, 0xbb, 0x55, 0x34, 0xa7, 0xe5, 0x3d, 0x73, 0x95, 0x1d, 0x45, 0xfb, 0x9d, 0x87, 0x37,
	0xc7, 0x5b, 0xc0, 0x04, 0xe9, 0xf7, 0xd6, 0x8e, 0x8e, 0x61, 0x0e, 0xeb, 0x53, 0x0f, 0xbf, 0x06,
	0xdb, 0xae, 0x2a, 0x36, 0x90, 0x22, 0x0a, 0xa9, 0xe5, 0xf6, 0xd2, 0x27, 0x7f, 0xf0, 0xf0, 0x75,
	0x78, 0x65, 0x4c, 0xe9, 0x31, 0xfc, 0x3f, 0x96, 0x96, 0x9a, 0x87, 0x8d, 0x36, 0xa0, 0xf8, 0xc9,
	0x2a, 0x38, 0x20, 0x69, 0x9f, 0x86, 0xe8, 0x4f, 0x1e, 0x5e, 0xb1, 0x9e, 0x68, 0xea, 0x49, 0x42,
	0x62, 0xc2, 0x13, 0xf4, 0x17, 0x0f, 0xb7, 0xed, 0x37, 0x5a, 0x25, 0xf9, 0xab, 0x57, 0xc4, 0x38,
	0xd8, 0xbf, 0xf1, 0x3a, 0xfa, 0x9b, 0x87, 0x2f, 0x98, 0x42, 0xc8, 0xb7, 0xc9, 0xb3, 0x28, 0x7f,
	0x5d, 0xfd, 0xdd, 0xc3, 0x1d, 0xb8, 0x60, 0x89, 0x63, 0x16, 0x1c, 0x8e, 0x71, 0xfe, 0x51, 0xaa,
	0x70, 0x7d, 0x9a, 0x77, 0xef, 0x7f, 0x7a, 0x78, 0x7d, 0xe2, 0x41, 0x61, 0xc9, 0xff, 0x35, 0x65,
	0xb9, 0xfa, 0x9a, 0xce, 0xc5, 0xdf, 0xdf, 0x9d, 0xb2, 0xdc, 0x92, 0xff, 0x60, 0xb7, 0x4a, 0x44,
	0xd3, 0xef, 0x2c, 0xe1, 0x0f, 0x55, 0x99, 0xaf, 0x38, 0xcd, 0xce, 0x92, 0xfe, 0x68, 0x17, 0x5f,
	0x2a, 0xae, 0xe1, 0x32, 0x82, 0x69, 0x1c, 0xa5, 0xb4, 0x8c, 0xc2, 0x67, 0xe5, 0xf9, 0xed, 0xa8,
	0xb9, 0x9c, 0x7f, 0x7b, 0x85, 0x1a, 0x31, 0x4a, 0x03, 0xeb, 0x68, 0xbe, 0x64, 0x79, 0x63, 0x7b,
	0x3e, 0xee, 0x65, 0x73, 0xf7, 0x4a, 0xf3, 0x65, 0x88, 0xfe, 0xa3, 0x3e, 0x20, 0xd6, 0x2c, 0x71,
	0x94, 0x46, 0x52, 0xf7, 0x1c, 0xbf, 0x17, 0xa5, 0x91, 0x38, 0x40, 0xff, 0xf5, 0x6e, 0x6f, 0xfd,
	0xf2, 0xf9, 0x7a, 0xed, 0xd9, 0xf3, 0xf5, 0xda, 0x67, 0xcf, 0xd7, 0x6b, 0xdf, 0x7b, 0xb1, 0x7e,
	0xe2, 0xd9, 0x8b, 0xf5, 0x13, 0x7f, 0x7e, 0xb1, 0x7e, 0xe2, 0xfd, 0xd9, 0x6f, 0x57, 0x3f, 0x0e,
	0xef, 0xcf, 0xeb, 0x7f, 0x5f, 0xf8, 0x3c, 0x00, 0x00, 0xff, 0xff, 0x95, 0xc4, 0xb9, 0x1d, 0x41,
	0x16, 0x00, 0x00,
}

func (m *ReqLogin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReqLogin) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReqLogin) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.PreferCodec != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.PreferCodec))
		i--
		dAtA[i] = 0x30
	}
	if m.CanDisplayMap {
		i--
		if m.CanDisplayMap {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if m.LoginMethod != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.LoginMethod))
		i--
		dAtA[i] = 0x20
	}
	if len(m.UserPass) > 0 {
		i -= len(m.UserPass)
		copy(dAtA[i:], m.UserPass)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.UserPass)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.UserName) > 0 {
		i -= len(m.UserName)
		copy(dAtA[i:], m.UserName)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.UserName)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.SysId) > 0 {
		i -= len(m.SysId)
		copy(dAtA[i:], m.SysId)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.SysId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RespLogin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RespLogin) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RespLogin) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Device != nil {
		{
			size, err := m.Device.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintAppProto(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.SettingLastUpdateTime) > 0 {
		i -= len(m.SettingLastUpdateTime)
		copy(dAtA[i:], m.SettingLastUpdateTime)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.SettingLastUpdateTime)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ServerVersion) > 0 {
		i -= len(m.ServerVersion)
		copy(dAtA[i:], m.ServerVersion)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.ServerVersion)))
		i--
		dAtA[i] = 0x12
	}
	if m.IsServerSupportMap {
		i--
		if m.IsServerSupportMap {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ReqGpsPermission) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReqGpsPermission) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReqGpsPermission) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ApplyDmrid != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.ApplyDmrid))
		i--
		dAtA[i] = 0x10
	}
	if m.Dmrid != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Dmrid))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ResGpsPermission) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResGpsPermission) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResGpsPermission) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ExpireTime) > 0 {
		i -= len(m.ExpireTime)
		copy(dAtA[i:], m.ExpireTime)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.ExpireTime)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.GrantUserName) > 0 {
		i -= len(m.GrantUserName)
		copy(dAtA[i:], m.GrantUserName)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.GrantUserName)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.GrantUserRid) > 0 {
		i -= len(m.GrantUserRid)
		copy(dAtA[i:], m.GrantUserRid)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.GrantUserRid)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Dmrid != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Dmrid))
		i--
		dAtA[i] = 0x10
	}
	if m.Code != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Gps84) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Gps84) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Gps84) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Altitude != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Altitude))
		i--
		dAtA[i] = 0x38
	}
	if m.Direction != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Direction))
		i--
		dAtA[i] = 0x30
	}
	if m.Speed != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Speed))))
		i--
		dAtA[i] = 0x29
	}
	if m.Lon != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lon))))
		i--
		dAtA[i] = 0x21
	}
	if m.Lat != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lat))))
		i--
		dAtA[i] = 0x19
	}
	if m.Av != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Av))
		i--
		dAtA[i] = 0x10
	}
	if len(m.GpsTime) > 0 {
		i -= len(m.GpsTime)
		copy(dAtA[i:], m.GpsTime)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.GpsTime)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GpsInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GpsInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GpsInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ActiveStatus != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.ActiveStatus))
		i--
		dAtA[i] = 0x20
	}
	if m.GpsInfo != nil {
		{
			size, err := m.GpsInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintAppProto(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Dmrid) > 0 {
		i -= len(m.Dmrid)
		copy(dAtA[i:], m.Dmrid)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.Dmrid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReqUpdateListenGroupList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReqUpdateListenGroupList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReqUpdateListenGroupList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ListenGroupList) > 0 {
		for iNdEx := len(m.ListenGroupList) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ListenGroupList[iNdEx])
			copy(dAtA[i:], m.ListenGroupList[iNdEx])
			i = encodeVarintAppProto(dAtA, i, uint64(len(m.ListenGroupList[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ReqUpdateSpeakTarget) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReqUpdateSpeakTarget) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReqUpdateSpeakTarget) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SpeakTarget) > 0 {
		i -= len(m.SpeakTarget)
		copy(dAtA[i:], m.SpeakTarget)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.SpeakTarget)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AddressBook) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddressBook) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AddressBook) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.OrgSortValue != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.OrgSortValue))
		i--
		dAtA[i] = 0x28
	}
	if m.DevType != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.DevType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Dmrid) > 0 {
		i -= len(m.Dmrid)
		copy(dAtA[i:], m.Dmrid)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.Dmrid)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ParentDmrid) > 0 {
		i -= len(m.ParentDmrid)
		copy(dAtA[i:], m.ParentDmrid)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.ParentDmrid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReqAddressBookResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReqAddressBookResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReqAddressBookResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.FailedList) > 0 {
		for iNdEx := len(m.FailedList) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.FailedList[iNdEx])
			copy(dAtA[i:], m.FailedList[iNdEx])
			i = encodeVarintAppProto(dAtA, i, uint64(len(m.FailedList[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.SuccessList) > 0 {
		for iNdEx := len(m.SuccessList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.SuccessList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAppProto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *AddressBookList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddressBookList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AddressBookList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.AddrBookList) > 0 {
		for iNdEx := len(m.AddrBookList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AddrBookList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintAppProto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ServerAddr) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServerAddr) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServerAddr) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Port != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Port))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Host) > 0 {
		i -= len(m.Host)
		copy(dAtA[i:], m.Host)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.Host)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ShortMessages) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShortMessages) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ShortMessages) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Time != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Time))
		i--
		dAtA[i] = 0x60
	}
	if m.SmsType != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.SmsType))
		i--
		dAtA[i] = 0x58
	}
	if m.Codec != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Codec))
		i--
		dAtA[i] = 0x50
	}
	if m.SmsNo != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.SmsNo))
		i--
		dAtA[i] = 0x40
	}
	if len(m.SmsContent) > 0 {
		i -= len(m.SmsContent)
		copy(dAtA[i:], m.SmsContent)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.SmsContent)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.TargetDmrid) > 0 {
		i -= len(m.TargetDmrid)
		copy(dAtA[i:], m.TargetDmrid)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.TargetDmrid)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.SenderDmrid) > 0 {
		i -= len(m.SenderDmrid)
		copy(dAtA[i:], m.SenderDmrid)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.SenderDmrid)))
		i--
		dAtA[i] = 0x22
	}
	return len(dAtA) - i, nil
}

func (m *Notify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Notify) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Notify) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ParamStr) > 0 {
		i -= len(m.ParamStr)
		copy(dAtA[i:], m.ParamStr)
		i = encodeVarintAppProto(dAtA, i, uint64(len(m.ParamStr)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *VoiceConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoiceConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *VoiceConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RecorderPcmGain != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.RecorderPcmGain))
		i--
		dAtA[i] = 0x50
	}
	if m.DebugRecorderPcm != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.DebugRecorderPcm))
		i--
		dAtA[i] = 0x48
	}
	if m.DenoiseSetting != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.DenoiseSetting))
		i--
		dAtA[i] = 0x40
	}
	if m.SpeakTimeout != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.SpeakTimeout))
		i--
		dAtA[i] = 0x30
	}
	if m.MediaBufferSize != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.MediaBufferSize))
		i--
		dAtA[i] = 0x28
	}
	if m.MediaAutoStartSize != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.MediaAutoStartSize))
		i--
		dAtA[i] = 0x20
	}
	if m.Gain != 0 {
		i = encodeVarintAppProto(dAtA, i, uint64(m.Gain))
		i--
		dAtA[i] = 0x18
	}
	return len(dAtA) - i, nil
}

func encodeVarintAppProto(dAtA []byte, offset int, v uint64) int {
	offset -= sovAppProto(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ReqLogin) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SysId)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.UserName)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.UserPass)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	if m.LoginMethod != 0 {
		n += 1 + sovAppProto(uint64(m.LoginMethod))
	}
	if m.CanDisplayMap {
		n += 2
	}
	if m.PreferCodec != 0 {
		n += 1 + sovAppProto(uint64(m.PreferCodec))
	}
	return n
}

func (m *RespLogin) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.IsServerSupportMap {
		n += 2
	}
	l = len(m.ServerVersion)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.SettingLastUpdateTime)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	if m.Device != nil {
		l = m.Device.Size()
		n += 1 + l + sovAppProto(uint64(l))
	}
	return n
}

func (m *ReqGpsPermission) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Dmrid != 0 {
		n += 1 + sovAppProto(uint64(m.Dmrid))
	}
	if m.ApplyDmrid != 0 {
		n += 1 + sovAppProto(uint64(m.ApplyDmrid))
	}
	return n
}

func (m *ResGpsPermission) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovAppProto(uint64(m.Code))
	}
	if m.Dmrid != 0 {
		n += 1 + sovAppProto(uint64(m.Dmrid))
	}
	l = len(m.GrantUserRid)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.GrantUserName)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.ExpireTime)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	return n
}

func (m *Gps84) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.GpsTime)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	if m.Av != 0 {
		n += 1 + sovAppProto(uint64(m.Av))
	}
	if m.Lat != 0 {
		n += 9
	}
	if m.Lon != 0 {
		n += 9
	}
	if m.Speed != 0 {
		n += 9
	}
	if m.Direction != 0 {
		n += 1 + sovAppProto(uint64(m.Direction))
	}
	if m.Altitude != 0 {
		n += 1 + sovAppProto(uint64(m.Altitude))
	}
	return n
}

func (m *GpsInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Dmrid)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	if m.GpsInfo != nil {
		l = m.GpsInfo.Size()
		n += 1 + l + sovAppProto(uint64(l))
	}
	if m.ActiveStatus != 0 {
		n += 1 + sovAppProto(uint64(m.ActiveStatus))
	}
	return n
}

func (m *ReqUpdateListenGroupList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ListenGroupList) > 0 {
		for _, s := range m.ListenGroupList {
			l = len(s)
			n += 1 + l + sovAppProto(uint64(l))
		}
	}
	return n
}

func (m *ReqUpdateSpeakTarget) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SpeakTarget)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	return n
}

func (m *AddressBook) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ParentDmrid)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.Dmrid)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	if m.DevType != 0 {
		n += 1 + sovAppProto(uint64(m.DevType))
	}
	if m.OrgSortValue != 0 {
		n += 1 + sovAppProto(uint64(m.OrgSortValue))
	}
	return n
}

func (m *ReqAddressBookResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.SuccessList) > 0 {
		for _, e := range m.SuccessList {
			l = e.Size()
			n += 1 + l + sovAppProto(uint64(l))
		}
	}
	if len(m.FailedList) > 0 {
		for _, s := range m.FailedList {
			l = len(s)
			n += 1 + l + sovAppProto(uint64(l))
		}
	}
	return n
}

func (m *AddressBookList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.AddrBookList) > 0 {
		for _, e := range m.AddrBookList {
			l = e.Size()
			n += 1 + l + sovAppProto(uint64(l))
		}
	}
	return n
}

func (m *ServerAddr) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Host)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	if m.Port != 0 {
		n += 1 + sovAppProto(uint64(m.Port))
	}
	return n
}

func (m *ShortMessages) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SenderDmrid)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.TargetDmrid)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.SmsContent)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	if m.SmsNo != 0 {
		n += 1 + sovAppProto(uint64(m.SmsNo))
	}
	if m.Codec != 0 {
		n += 1 + sovAppProto(uint64(m.Codec))
	}
	if m.SmsType != 0 {
		n += 1 + sovAppProto(uint64(m.SmsType))
	}
	if m.Time != 0 {
		n += 1 + sovAppProto(uint64(m.Time))
	}
	return n
}

func (m *Notify) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovAppProto(uint64(m.Code))
	}
	l = len(m.ParamStr)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovAppProto(uint64(l))
	}
	return n
}

func (m *VoiceConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Gain != 0 {
		n += 1 + sovAppProto(uint64(m.Gain))
	}
	if m.MediaAutoStartSize != 0 {
		n += 1 + sovAppProto(uint64(m.MediaAutoStartSize))
	}
	if m.MediaBufferSize != 0 {
		n += 1 + sovAppProto(uint64(m.MediaBufferSize))
	}
	if m.SpeakTimeout != 0 {
		n += 1 + sovAppProto(uint64(m.SpeakTimeout))
	}
	if m.DenoiseSetting != 0 {
		n += 1 + sovAppProto(uint64(m.DenoiseSetting))
	}
	if m.DebugRecorderPcm != 0 {
		n += 1 + sovAppProto(uint64(m.DebugRecorderPcm))
	}
	if m.RecorderPcmGain != 0 {
		n += 1 + sovAppProto(uint64(m.RecorderPcmGain))
	}
	return n
}

func sovAppProto(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozAppProto(x uint64) (n int) {
	return sovAppProto(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ReqLogin) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: req_login: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: req_login: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SysId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SysId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserPass", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserPass = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginMethod", wireType)
			}
			m.LoginMethod = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginMethod |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CanDisplayMap", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanDisplayMap = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PreferCodec", wireType)
			}
			m.PreferCodec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PreferCodec |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RespLogin) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: resp_login: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: resp_login: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsServerSupportMap", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsServerSupportMap = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServerVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ServerVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SettingLastUpdateTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SettingLastUpdateTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Device", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Device == nil {
				m.Device = &DbDevice{}
			}
			if err := m.Device.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReqGpsPermission) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: req_gps_permission: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: req_gps_permission: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dmrid", wireType)
			}
			m.Dmrid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Dmrid |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApplyDmrid", wireType)
			}
			m.ApplyDmrid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyDmrid |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResGpsPermission) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: res_gps_permission: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: res_gps_permission: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dmrid", wireType)
			}
			m.Dmrid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Dmrid |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrantUserRid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GrantUserRid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrantUserName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GrantUserName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ExpireTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Gps84) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: gps84: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: gps84: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GpsTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GpsTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Av", wireType)
			}
			m.Av = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Av |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lat = float64(math.Float64frombits(v))
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lon", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lon = float64(math.Float64frombits(v))
		case 5:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Speed", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Speed = float64(math.Float64frombits(v))
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Direction", wireType)
			}
			m.Direction = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Direction |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Altitude", wireType)
			}
			m.Altitude = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Altitude |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GpsInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: gps_info: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: gps_info: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dmrid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dmrid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GpsInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GpsInfo == nil {
				m.GpsInfo = &Gps84{}
			}
			if err := m.GpsInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActiveStatus", wireType)
			}
			m.ActiveStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveStatus |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReqUpdateListenGroupList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: req_update_listen_group_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: req_update_listen_group_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListenGroupList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ListenGroupList = append(m.ListenGroupList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReqUpdateSpeakTarget) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: req_update_speak_target: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: req_update_speak_target: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpeakTarget", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpeakTarget = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddressBook) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: address_book: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: address_book: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParentDmrid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParentDmrid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dmrid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dmrid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevType", wireType)
			}
			m.DevType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DevType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgSortValue", wireType)
			}
			m.OrgSortValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OrgSortValue |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReqAddressBookResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: req_address_book_result: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: req_address_book_result: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SuccessList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SuccessList = append(m.SuccessList, &AddressBook{})
			if err := m.SuccessList[len(m.SuccessList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FailedList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FailedList = append(m.FailedList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddressBookList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: address_book_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: address_book_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AddrBookList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AddrBookList = append(m.AddrBookList, &AddressBook{})
			if err := m.AddrBookList[len(m.AddrBookList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServerAddr) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: server_addr: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: server_addr: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Host", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Host = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			m.Port = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Port |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShortMessages) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: short_messages: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: short_messages: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SenderDmrid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SenderDmrid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SmsContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SmsContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SmsNo", wireType)
			}
			m.SmsNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SmsNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Codec", wireType)
			}
			m.Codec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Codec |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SmsType", wireType)
			}
			m.SmsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SmsType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Time", wireType)
			}
			m.Time = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Time |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Notify) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Notify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Notify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParamStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParamStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAppProto
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthAppProto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = append(m.Body[:0], dAtA[iNdEx:postIndex]...)
			if m.Body == nil {
				m.Body = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoiceConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: voice_config: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: voice_config: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Gain", wireType)
			}
			m.Gain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Gain |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MediaAutoStartSize", wireType)
			}
			m.MediaAutoStartSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MediaAutoStartSize |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MediaBufferSize", wireType)
			}
			m.MediaBufferSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MediaBufferSize |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpeakTimeout", wireType)
			}
			m.SpeakTimeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpeakTimeout |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DenoiseSetting", wireType)
			}
			m.DenoiseSetting = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DenoiseSetting |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DebugRecorderPcm", wireType)
			}
			m.DebugRecorderPcm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DebugRecorderPcm |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RecorderPcmGain", wireType)
			}
			m.RecorderPcmGain = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecorderPcmGain |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAppProto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAppProto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipAppProto(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAppProto
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAppProto
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthAppProto
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupAppProto
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthAppProto
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthAppProto        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAppProto          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupAppProto = fmt.Errorf("proto: unexpected end of group")
)
