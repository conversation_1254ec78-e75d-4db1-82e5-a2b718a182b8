// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: db.pb.list.proto

package bfdx_proto

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// 系统设置表
type DbSysConfigList struct {
	Rows []*DbSysConfig `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbSysConfigList) Reset()         { *m = DbSysConfigList{} }
func (m *DbSysConfigList) String() string { return proto.CompactTextString(m) }
func (*DbSysConfigList) ProtoMessage()    {}
func (*DbSysConfigList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{0}
}
func (m *DbSysConfigList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbSysConfigList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbSysConfigList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbSysConfigList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbSysConfigList.Merge(m, src)
}
func (m *DbSysConfigList) XXX_Size() int {
	return m.Size()
}
func (m *DbSysConfigList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbSysConfigList.DiscardUnknown(m)
}

var xxx_messageInfo_DbSysConfigList proto.InternalMessageInfo

func (m *DbSysConfigList) GetRows() []*DbSysConfig {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 表各种操作时间// 客户端有时需要查询后台是否已经更新了数据,可以通过此表来得到初步的信息
type DbTableOperateTimeList struct {
	Rows []*DbTableOperateTime `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbTableOperateTimeList) Reset()         { *m = DbTableOperateTimeList{} }
func (m *DbTableOperateTimeList) String() string { return proto.CompactTextString(m) }
func (*DbTableOperateTimeList) ProtoMessage()    {}
func (*DbTableOperateTimeList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{1}
}
func (m *DbTableOperateTimeList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbTableOperateTimeList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbTableOperateTimeList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbTableOperateTimeList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbTableOperateTimeList.Merge(m, src)
}
func (m *DbTableOperateTimeList) XXX_Size() int {
	return m.Size()
}
func (m *DbTableOperateTimeList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbTableOperateTimeList.DiscardUnknown(m)
}

var xxx_messageInfo_DbTableOperateTimeList proto.InternalMessageInfo

func (m *DbTableOperateTimeList) GetRows() []*DbTableOperateTime {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 组织架构表
type DbOrgList struct {
	Rows []*DbOrg `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbOrgList) Reset()         { *m = DbOrgList{} }
func (m *DbOrgList) String() string { return proto.CompactTextString(m) }
func (*DbOrgList) ProtoMessage()    {}
func (*DbOrgList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{2}
}
func (m *DbOrgList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbOrgList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbOrgList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbOrgList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbOrgList.Merge(m, src)
}
func (m *DbOrgList) XXX_Size() int {
	return m.Size()
}
func (m *DbOrgList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbOrgList.DiscardUnknown(m)
}

var xxx_messageInfo_DbOrgList proto.InternalMessageInfo

func (m *DbOrgList) GetRows() []*DbOrg {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 用户的一些图片数据,地图点icon等
type DbImageList struct {
	Rows []*DbImage `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbImageList) Reset()         { *m = DbImageList{} }
func (m *DbImageList) String() string { return proto.CompactTextString(m) }
func (*DbImageList) ProtoMessage()    {}
func (*DbImageList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{3}
}
func (m *DbImageList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbImageList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbImageList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbImageList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbImageList.Merge(m, src)
}
func (m *DbImageList) XXX_Size() int {
	return m.Size()
}
func (m *DbImageList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbImageList.DiscardUnknown(m)
}

var xxx_messageInfo_DbImageList proto.InternalMessageInfo

func (m *DbImageList) GetRows() []*DbImage {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 基站列表
type DbBaseStationList struct {
	Rows []*DbBaseStation `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbBaseStationList) Reset()         { *m = DbBaseStationList{} }
func (m *DbBaseStationList) String() string { return proto.CompactTextString(m) }
func (*DbBaseStationList) ProtoMessage()    {}
func (*DbBaseStationList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{4}
}
func (m *DbBaseStationList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbBaseStationList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbBaseStationList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbBaseStationList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbBaseStationList.Merge(m, src)
}
func (m *DbBaseStationList) XXX_Size() int {
	return m.Size()
}
func (m *DbBaseStationList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbBaseStationList.DiscardUnknown(m)
}

var xxx_messageInfo_DbBaseStationList proto.InternalMessageInfo

func (m *DbBaseStationList) GetRows() []*DbBaseStation {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 控制器设备表
type DbControllerList struct {
	Rows []*DbController `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbControllerList) Reset()         { *m = DbControllerList{} }
func (m *DbControllerList) String() string { return proto.CompactTextString(m) }
func (*DbControllerList) ProtoMessage()    {}
func (*DbControllerList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{5}
}
func (m *DbControllerList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbControllerList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbControllerList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbControllerList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbControllerList.Merge(m, src)
}
func (m *DbControllerList) XXX_Size() int {
	return m.Size()
}
func (m *DbControllerList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbControllerList.DiscardUnknown(m)
}

var xxx_messageInfo_DbControllerList proto.InternalMessageInfo

func (m *DbControllerList) GetRows() []*DbController {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 控制器状态
type DbControllerLastInfoList struct {
	Rows []*DbControllerLastInfo `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbControllerLastInfoList) Reset()         { *m = DbControllerLastInfoList{} }
func (m *DbControllerLastInfoList) String() string { return proto.CompactTextString(m) }
func (*DbControllerLastInfoList) ProtoMessage()    {}
func (*DbControllerLastInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{6}
}
func (m *DbControllerLastInfoList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbControllerLastInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbControllerLastInfoList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbControllerLastInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbControllerLastInfoList.Merge(m, src)
}
func (m *DbControllerLastInfoList) XXX_Size() int {
	return m.Size()
}
func (m *DbControllerLastInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbControllerLastInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_DbControllerLastInfoList proto.InternalMessageInfo

func (m *DbControllerLastInfoList) GetRows() []*DbControllerLastInfo {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 控制器上线历史表,按月分表
type DbControllerOnlineHistoryList struct {
	Rows []*DbControllerOnlineHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbControllerOnlineHistoryList) Reset()         { *m = DbControllerOnlineHistoryList{} }
func (m *DbControllerOnlineHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbControllerOnlineHistoryList) ProtoMessage()    {}
func (*DbControllerOnlineHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{7}
}
func (m *DbControllerOnlineHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbControllerOnlineHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbControllerOnlineHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbControllerOnlineHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbControllerOnlineHistoryList.Merge(m, src)
}
func (m *DbControllerOnlineHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbControllerOnlineHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbControllerOnlineHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbControllerOnlineHistoryList proto.InternalMessageInfo

func (m *DbControllerOnlineHistoryList) GetRows() []*DbControllerOnlineHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 电话网关黑白名单
type DbPhoneGatewayFilterList struct {
	Rows []*DbPhoneGatewayFilter `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbPhoneGatewayFilterList) Reset()         { *m = DbPhoneGatewayFilterList{} }
func (m *DbPhoneGatewayFilterList) String() string { return proto.CompactTextString(m) }
func (*DbPhoneGatewayFilterList) ProtoMessage()    {}
func (*DbPhoneGatewayFilterList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{8}
}
func (m *DbPhoneGatewayFilterList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbPhoneGatewayFilterList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbPhoneGatewayFilterList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbPhoneGatewayFilterList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbPhoneGatewayFilterList.Merge(m, src)
}
func (m *DbPhoneGatewayFilterList) XXX_Size() int {
	return m.Size()
}
func (m *DbPhoneGatewayFilterList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbPhoneGatewayFilterList.DiscardUnknown(m)
}

var xxx_messageInfo_DbPhoneGatewayFilterList proto.InternalMessageInfo

func (m *DbPhoneGatewayFilterList) GetRows() []*DbPhoneGatewayFilter {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 对讲机设备表
type DbDeviceList struct {
	Rows []*DbDevice `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbDeviceList) Reset()         { *m = DbDeviceList{} }
func (m *DbDeviceList) String() string { return proto.CompactTextString(m) }
func (*DbDeviceList) ProtoMessage()    {}
func (*DbDeviceList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{9}
}
func (m *DbDeviceList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDeviceList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDeviceList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDeviceList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDeviceList.Merge(m, src)
}
func (m *DbDeviceList) XXX_Size() int {
	return m.Size()
}
func (m *DbDeviceList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDeviceList.DiscardUnknown(m)
}

var xxx_messageInfo_DbDeviceList proto.InternalMessageInfo

func (m *DbDeviceList) GetRows() []*DbDevice {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 对讲机最后的数据信息
type DbDeviceLastInfoList struct {
	Rows []*DbDeviceLastInfo `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbDeviceLastInfoList) Reset()         { *m = DbDeviceLastInfoList{} }
func (m *DbDeviceLastInfoList) String() string { return proto.CompactTextString(m) }
func (*DbDeviceLastInfoList) ProtoMessage()    {}
func (*DbDeviceLastInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{10}
}
func (m *DbDeviceLastInfoList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDeviceLastInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDeviceLastInfoList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDeviceLastInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDeviceLastInfoList.Merge(m, src)
}
func (m *DbDeviceLastInfoList) XXX_Size() int {
	return m.Size()
}
func (m *DbDeviceLastInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDeviceLastInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_DbDeviceLastInfoList proto.InternalMessageInfo

func (m *DbDeviceLastInfoList) GetRows() []*DbDeviceLastInfo {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 用户职称表
type DbUserTitleList struct {
	Rows []*DbUserTitle `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbUserTitleList) Reset()         { *m = DbUserTitleList{} }
func (m *DbUserTitleList) String() string { return proto.CompactTextString(m) }
func (*DbUserTitleList) ProtoMessage()    {}
func (*DbUserTitleList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{11}
}
func (m *DbUserTitleList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserTitleList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserTitleList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserTitleList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserTitleList.Merge(m, src)
}
func (m *DbUserTitleList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserTitleList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserTitleList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserTitleList proto.InternalMessageInfo

func (m *DbUserTitleList) GetRows() []*DbUserTitle {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 用户数据表
type DbUserList struct {
	Rows []*DbUser `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbUserList) Reset()         { *m = DbUserList{} }
func (m *DbUserList) String() string { return proto.CompactTextString(m) }
func (*DbUserList) ProtoMessage()    {}
func (*DbUserList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{12}
}
func (m *DbUserList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserList.Merge(m, src)
}
func (m *DbUserList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserList proto.InternalMessageInfo

func (m *DbUserList) GetRows() []*DbUser {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 用户群组权限表
type DbUserPrivelegeList struct {
	Rows []*DbUserPrivelege `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbUserPrivelegeList) Reset()         { *m = DbUserPrivelegeList{} }
func (m *DbUserPrivelegeList) String() string { return proto.CompactTextString(m) }
func (*DbUserPrivelegeList) ProtoMessage()    {}
func (*DbUserPrivelegeList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{13}
}
func (m *DbUserPrivelegeList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserPrivelegeList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserPrivelegeList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserPrivelegeList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserPrivelegeList.Merge(m, src)
}
func (m *DbUserPrivelegeList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserPrivelegeList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserPrivelegeList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserPrivelegeList proto.InternalMessageInfo

func (m *DbUserPrivelegeList) GetRows() []*DbUserPrivelege {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 用户登录的session id表
type DbUserSessionIdList struct {
	Rows []*DbUserSessionId `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbUserSessionIdList) Reset()         { *m = DbUserSessionIdList{} }
func (m *DbUserSessionIdList) String() string { return proto.CompactTextString(m) }
func (*DbUserSessionIdList) ProtoMessage()    {}
func (*DbUserSessionIdList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{14}
}
func (m *DbUserSessionIdList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserSessionIdList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserSessionIdList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserSessionIdList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserSessionIdList.Merge(m, src)
}
func (m *DbUserSessionIdList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserSessionIdList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserSessionIdList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserSessionIdList proto.InternalMessageInfo

func (m *DbUserSessionIdList) GetRows() []*DbUserSessionId {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 虚拟群组信息表
type DbVirtualOrgList struct {
	Rows []*DbVirtualOrg `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbVirtualOrgList) Reset()         { *m = DbVirtualOrgList{} }
func (m *DbVirtualOrgList) String() string { return proto.CompactTextString(m) }
func (*DbVirtualOrgList) ProtoMessage()    {}
func (*DbVirtualOrgList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{15}
}
func (m *DbVirtualOrgList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbVirtualOrgList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbVirtualOrgList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbVirtualOrgList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbVirtualOrgList.Merge(m, src)
}
func (m *DbVirtualOrgList) XXX_Size() int {
	return m.Size()
}
func (m *DbVirtualOrgList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbVirtualOrgList.DiscardUnknown(m)
}

var xxx_messageInfo_DbVirtualOrgList proto.InternalMessageInfo

func (m *DbVirtualOrgList) GetRows() []*DbVirtualOrg {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 用户自己的一些地图标志
type DbMapPointList struct {
	Rows []*DbMapPoint `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbMapPointList) Reset()         { *m = DbMapPointList{} }
func (m *DbMapPointList) String() string { return proto.CompactTextString(m) }
func (*DbMapPointList) ProtoMessage()    {}
func (*DbMapPointList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{16}
}
func (m *DbMapPointList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbMapPointList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbMapPointList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbMapPointList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbMapPointList.Merge(m, src)
}
func (m *DbMapPointList) XXX_Size() int {
	return m.Size()
}
func (m *DbMapPointList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbMapPointList.DiscardUnknown(m)
}

var xxx_messageInfo_DbMapPointList proto.InternalMessageInfo

func (m *DbMapPointList) GetRows() []*DbMapPoint {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 巡查线路点
type DbLinePointList struct {
	Rows []*DbLinePoint `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbLinePointList) Reset()         { *m = DbLinePointList{} }
func (m *DbLinePointList) String() string { return proto.CompactTextString(m) }
func (*DbLinePointList) ProtoMessage()    {}
func (*DbLinePointList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{17}
}
func (m *DbLinePointList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbLinePointList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbLinePointList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbLinePointList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbLinePointList.Merge(m, src)
}
func (m *DbLinePointList) XXX_Size() int {
	return m.Size()
}
func (m *DbLinePointList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbLinePointList.DiscardUnknown(m)
}

var xxx_messageInfo_DbLinePointList proto.InternalMessageInfo

func (m *DbLinePointList) GetRows() []*DbLinePoint {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 巡查点最新信息
type DbLinePointLatestInfoList struct {
	Rows []*DbLinePointLatestInfo `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbLinePointLatestInfoList) Reset()         { *m = DbLinePointLatestInfoList{} }
func (m *DbLinePointLatestInfoList) String() string { return proto.CompactTextString(m) }
func (*DbLinePointLatestInfoList) ProtoMessage()    {}
func (*DbLinePointLatestInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{18}
}
func (m *DbLinePointLatestInfoList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbLinePointLatestInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbLinePointLatestInfoList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbLinePointLatestInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbLinePointLatestInfoList.Merge(m, src)
}
func (m *DbLinePointLatestInfoList) XXX_Size() int {
	return m.Size()
}
func (m *DbLinePointLatestInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbLinePointLatestInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_DbLinePointLatestInfoList proto.InternalMessageInfo

func (m *DbLinePointLatestInfoList) GetRows() []*DbLinePointLatestInfo {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 巡查线路主表
type DbLineMasterList struct {
	Rows []*DbLineMaster `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbLineMasterList) Reset()         { *m = DbLineMasterList{} }
func (m *DbLineMasterList) String() string { return proto.CompactTextString(m) }
func (*DbLineMasterList) ProtoMessage()    {}
func (*DbLineMasterList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{19}
}
func (m *DbLineMasterList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbLineMasterList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbLineMasterList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbLineMasterList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbLineMasterList.Merge(m, src)
}
func (m *DbLineMasterList) XXX_Size() int {
	return m.Size()
}
func (m *DbLineMasterList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbLineMasterList.DiscardUnknown(m)
}

var xxx_messageInfo_DbLineMasterList proto.InternalMessageInfo

func (m *DbLineMasterList) GetRows() []*DbLineMaster {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 巡查线路细表
type DbLineDetailList struct {
	Rows []*DbLineDetail `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbLineDetailList) Reset()         { *m = DbLineDetailList{} }
func (m *DbLineDetailList) String() string { return proto.CompactTextString(m) }
func (*DbLineDetailList) ProtoMessage()    {}
func (*DbLineDetailList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{20}
}
func (m *DbLineDetailList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbLineDetailList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbLineDetailList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbLineDetailList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbLineDetailList.Merge(m, src)
}
func (m *DbLineDetailList) XXX_Size() int {
	return m.Size()
}
func (m *DbLineDetailList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbLineDetailList.DiscardUnknown(m)
}

var xxx_messageInfo_DbLineDetailList proto.InternalMessageInfo

func (m *DbLineDetailList) GetRows() []*DbLineDetail {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 巡查规则表
type DbRfidRuleMasterList struct {
	Rows []*DbRfidRuleMaster `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbRfidRuleMasterList) Reset()         { *m = DbRfidRuleMasterList{} }
func (m *DbRfidRuleMasterList) String() string { return proto.CompactTextString(m) }
func (*DbRfidRuleMasterList) ProtoMessage()    {}
func (*DbRfidRuleMasterList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{21}
}
func (m *DbRfidRuleMasterList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbRfidRuleMasterList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbRfidRuleMasterList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbRfidRuleMasterList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbRfidRuleMasterList.Merge(m, src)
}
func (m *DbRfidRuleMasterList) XXX_Size() int {
	return m.Size()
}
func (m *DbRfidRuleMasterList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbRfidRuleMasterList.DiscardUnknown(m)
}

var xxx_messageInfo_DbRfidRuleMasterList proto.InternalMessageInfo

func (m *DbRfidRuleMasterList) GetRows() []*DbRfidRuleMaster {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 开关机数据表,按月分表
type DbDevicePowerOnoffList struct {
	Rows []*DbDevicePowerOnoff `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbDevicePowerOnoffList) Reset()         { *m = DbDevicePowerOnoffList{} }
func (m *DbDevicePowerOnoffList) String() string { return proto.CompactTextString(m) }
func (*DbDevicePowerOnoffList) ProtoMessage()    {}
func (*DbDevicePowerOnoffList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{22}
}
func (m *DbDevicePowerOnoffList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDevicePowerOnoffList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDevicePowerOnoffList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDevicePowerOnoffList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDevicePowerOnoffList.Merge(m, src)
}
func (m *DbDevicePowerOnoffList) XXX_Size() int {
	return m.Size()
}
func (m *DbDevicePowerOnoffList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDevicePowerOnoffList.DiscardUnknown(m)
}

var xxx_messageInfo_DbDevicePowerOnoffList proto.InternalMessageInfo

func (m *DbDevicePowerOnoffList) GetRows() []*DbDevicePowerOnoff {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 上班下班打卡数据表,按月分表
type DbUserCheckInHistoryList struct {
	Rows []*DbUserCheckInHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbUserCheckInHistoryList) Reset()         { *m = DbUserCheckInHistoryList{} }
func (m *DbUserCheckInHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbUserCheckInHistoryList) ProtoMessage()    {}
func (*DbUserCheckInHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{23}
}
func (m *DbUserCheckInHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserCheckInHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserCheckInHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserCheckInHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserCheckInHistoryList.Merge(m, src)
}
func (m *DbUserCheckInHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserCheckInHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserCheckInHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserCheckInHistoryList proto.InternalMessageInfo

func (m *DbUserCheckInHistoryList) GetRows() []*DbUserCheckInHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// rfid巡查历史表,按月分表
type DbRfidHistoryList struct {
	Rows []*DbRfidHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbRfidHistoryList) Reset()         { *m = DbRfidHistoryList{} }
func (m *DbRfidHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbRfidHistoryList) ProtoMessage()    {}
func (*DbRfidHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{24}
}
func (m *DbRfidHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbRfidHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbRfidHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbRfidHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbRfidHistoryList.Merge(m, src)
}
func (m *DbRfidHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbRfidHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbRfidHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbRfidHistoryList proto.InternalMessageInfo

func (m *DbRfidHistoryList) GetRows() []*DbRfidHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// gps位置历史表,按月分表
type DbGpsHistoryList struct {
	Rows []*DbGpsHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbGpsHistoryList) Reset()         { *m = DbGpsHistoryList{} }
func (m *DbGpsHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbGpsHistoryList) ProtoMessage()    {}
func (*DbGpsHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{25}
}
func (m *DbGpsHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbGpsHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbGpsHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbGpsHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbGpsHistoryList.Merge(m, src)
}
func (m *DbGpsHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbGpsHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbGpsHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbGpsHistoryList proto.InternalMessageInfo

func (m *DbGpsHistoryList) GetRows() []*DbGpsHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 报警历史,要分表了,因为报警可能会非常多,客户端需要编辑此表,分表客户端处理需要特殊处理
type DbAlarmHistoryList struct {
	Rows []*DbAlarmHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbAlarmHistoryList) Reset()         { *m = DbAlarmHistoryList{} }
func (m *DbAlarmHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbAlarmHistoryList) ProtoMessage()    {}
func (*DbAlarmHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{26}
}
func (m *DbAlarmHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbAlarmHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbAlarmHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbAlarmHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbAlarmHistoryList.Merge(m, src)
}
func (m *DbAlarmHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbAlarmHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbAlarmHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbAlarmHistoryList proto.InternalMessageInfo

func (m *DbAlarmHistoryList) GetRows() []*DbAlarmHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 对讲机通话历史,按月分表
type DbSoundHistoryList struct {
	Rows []*DbSoundHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbSoundHistoryList) Reset()         { *m = DbSoundHistoryList{} }
func (m *DbSoundHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbSoundHistoryList) ProtoMessage()    {}
func (*DbSoundHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{27}
}
func (m *DbSoundHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbSoundHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbSoundHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbSoundHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbSoundHistoryList.Merge(m, src)
}
func (m *DbSoundHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbSoundHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbSoundHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbSoundHistoryList proto.InternalMessageInfo

func (m *DbSoundHistoryList) GetRows() []*DbSoundHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 还没发送的命令
type DbNotSendCmdList struct {
	Rows []*DbNotSendCmd `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbNotSendCmdList) Reset()         { *m = DbNotSendCmdList{} }
func (m *DbNotSendCmdList) String() string { return proto.CompactTextString(m) }
func (*DbNotSendCmdList) ProtoMessage()    {}
func (*DbNotSendCmdList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{28}
}
func (m *DbNotSendCmdList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbNotSendCmdList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbNotSendCmdList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbNotSendCmdList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbNotSendCmdList.Merge(m, src)
}
func (m *DbNotSendCmdList) XXX_Size() int {
	return m.Size()
}
func (m *DbNotSendCmdList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbNotSendCmdList.DiscardUnknown(m)
}

var xxx_messageInfo_DbNotSendCmdList proto.InternalMessageInfo

func (m *DbNotSendCmdList) GetRows() []*DbNotSendCmd {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 已经发送的命令列表
type DbSentCmdHistoryList struct {
	Rows []*DbSentCmdHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbSentCmdHistoryList) Reset()         { *m = DbSentCmdHistoryList{} }
func (m *DbSentCmdHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbSentCmdHistoryList) ProtoMessage()    {}
func (*DbSentCmdHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{29}
}
func (m *DbSentCmdHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbSentCmdHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbSentCmdHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbSentCmdHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbSentCmdHistoryList.Merge(m, src)
}
func (m *DbSentCmdHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbSentCmdHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbSentCmdHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbSentCmdHistoryList proto.InternalMessageInfo

func (m *DbSentCmdHistoryList) GetRows() []*DbSentCmdHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 对讲机注册信息
type DbDeviceRegisterInfoList struct {
	Rows []*DbDeviceRegisterInfo `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbDeviceRegisterInfoList) Reset()         { *m = DbDeviceRegisterInfoList{} }
func (m *DbDeviceRegisterInfoList) String() string { return proto.CompactTextString(m) }
func (*DbDeviceRegisterInfoList) ProtoMessage()    {}
func (*DbDeviceRegisterInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{30}
}
func (m *DbDeviceRegisterInfoList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDeviceRegisterInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDeviceRegisterInfoList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDeviceRegisterInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDeviceRegisterInfoList.Merge(m, src)
}
func (m *DbDeviceRegisterInfoList) XXX_Size() int {
	return m.Size()
}
func (m *DbDeviceRegisterInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDeviceRegisterInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_DbDeviceRegisterInfoList proto.InternalMessageInfo

func (m *DbDeviceRegisterInfoList) GetRows() []*DbDeviceRegisterInfo {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 通话调度/切换信道历史表,按月分表
type DbCallDispatchHistoryList struct {
	Rows []*DbCallDispatchHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbCallDispatchHistoryList) Reset()         { *m = DbCallDispatchHistoryList{} }
func (m *DbCallDispatchHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbCallDispatchHistoryList) ProtoMessage()    {}
func (*DbCallDispatchHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{31}
}
func (m *DbCallDispatchHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbCallDispatchHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbCallDispatchHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbCallDispatchHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbCallDispatchHistoryList.Merge(m, src)
}
func (m *DbCallDispatchHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbCallDispatchHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbCallDispatchHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbCallDispatchHistoryList proto.InternalMessageInfo

func (m *DbCallDispatchHistoryList) GetRows() []*DbCallDispatchHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 基站调度历史
type DbConfDispatchHistoryList struct {
	Rows []*DbConfDispatchHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbConfDispatchHistoryList) Reset()         { *m = DbConfDispatchHistoryList{} }
func (m *DbConfDispatchHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbConfDispatchHistoryList) ProtoMessage()    {}
func (*DbConfDispatchHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{32}
}
func (m *DbConfDispatchHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbConfDispatchHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbConfDispatchHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbConfDispatchHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbConfDispatchHistoryList.Merge(m, src)
}
func (m *DbConfDispatchHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbConfDispatchHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbConfDispatchHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbConfDispatchHistoryList proto.InternalMessageInfo

func (m *DbConfDispatchHistoryList) GetRows() []*DbConfDispatchHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 未确认短信表
type DbNotConfirmSmsList struct {
	Rows []*DbNotConfirmSms `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbNotConfirmSmsList) Reset()         { *m = DbNotConfirmSmsList{} }
func (m *DbNotConfirmSmsList) String() string { return proto.CompactTextString(m) }
func (*DbNotConfirmSmsList) ProtoMessage()    {}
func (*DbNotConfirmSmsList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{33}
}
func (m *DbNotConfirmSmsList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbNotConfirmSmsList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbNotConfirmSmsList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbNotConfirmSmsList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbNotConfirmSmsList.Merge(m, src)
}
func (m *DbNotConfirmSmsList) XXX_Size() int {
	return m.Size()
}
func (m *DbNotConfirmSmsList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbNotConfirmSmsList.DiscardUnknown(m)
}

var xxx_messageInfo_DbNotConfirmSmsList proto.InternalMessageInfo

func (m *DbNotConfirmSmsList) GetRows() []*DbNotConfirmSms {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 短信历史表,短信一般很少,不分表处理了
type DbSmsHistoryList struct {
	Rows []*DbSmsHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbSmsHistoryList) Reset()         { *m = DbSmsHistoryList{} }
func (m *DbSmsHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbSmsHistoryList) ProtoMessage()    {}
func (*DbSmsHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{34}
}
func (m *DbSmsHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbSmsHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbSmsHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbSmsHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbSmsHistoryList.Merge(m, src)
}
func (m *DbSmsHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbSmsHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbSmsHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbSmsHistoryList proto.InternalMessageInfo

func (m *DbSmsHistoryList) GetRows() []*DbSmsHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 频道物理数据// todo 此处信息还需要商议下才能确定
type DbChRfSettingList struct {
	Rows []*DbChRfSetting `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbChRfSettingList) Reset()         { *m = DbChRfSettingList{} }
func (m *DbChRfSettingList) String() string { return proto.CompactTextString(m) }
func (*DbChRfSettingList) ProtoMessage()    {}
func (*DbChRfSettingList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{35}
}
func (m *DbChRfSettingList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbChRfSettingList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbChRfSettingList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbChRfSettingList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbChRfSettingList.Merge(m, src)
}
func (m *DbChRfSettingList) XXX_Size() int {
	return m.Size()
}
func (m *DbChRfSettingList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbChRfSettingList.DiscardUnknown(m)
}

var xxx_messageInfo_DbChRfSettingList proto.InternalMessageInfo

func (m *DbChRfSettingList) GetRows() []*DbChRfSetting {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 写频配置文件
type DbDeviceSettingConfList struct {
	Rows []*DbDeviceSettingConf `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbDeviceSettingConfList) Reset()         { *m = DbDeviceSettingConfList{} }
func (m *DbDeviceSettingConfList) String() string { return proto.CompactTextString(m) }
func (*DbDeviceSettingConfList) ProtoMessage()    {}
func (*DbDeviceSettingConfList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{36}
}
func (m *DbDeviceSettingConfList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDeviceSettingConfList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDeviceSettingConfList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDeviceSettingConfList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDeviceSettingConfList.Merge(m, src)
}
func (m *DbDeviceSettingConfList) XXX_Size() int {
	return m.Size()
}
func (m *DbDeviceSettingConfList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDeviceSettingConfList.DiscardUnknown(m)
}

var xxx_messageInfo_DbDeviceSettingConfList proto.InternalMessageInfo

func (m *DbDeviceSettingConfList) GetRows() []*DbDeviceSettingConf {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 电话网关短号
type DbPhoneShortNoList struct {
	Rows []*DbPhoneShortNo `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbPhoneShortNoList) Reset()         { *m = DbPhoneShortNoList{} }
func (m *DbPhoneShortNoList) String() string { return proto.CompactTextString(m) }
func (*DbPhoneShortNoList) ProtoMessage()    {}
func (*DbPhoneShortNoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{37}
}
func (m *DbPhoneShortNoList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbPhoneShortNoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbPhoneShortNoList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbPhoneShortNoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbPhoneShortNoList.Merge(m, src)
}
func (m *DbPhoneShortNoList) XXX_Size() int {
	return m.Size()
}
func (m *DbPhoneShortNoList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbPhoneShortNoList.DiscardUnknown(m)
}

var xxx_messageInfo_DbPhoneShortNoList proto.InternalMessageInfo

func (m *DbPhoneShortNoList) GetRows() []*DbPhoneShortNo {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 电话网关使用授权
type DbPhoneGatewayPermissionList struct {
	Rows []*DbPhoneGatewayPermission `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbPhoneGatewayPermissionList) Reset()         { *m = DbPhoneGatewayPermissionList{} }
func (m *DbPhoneGatewayPermissionList) String() string { return proto.CompactTextString(m) }
func (*DbPhoneGatewayPermissionList) ProtoMessage()    {}
func (*DbPhoneGatewayPermissionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{38}
}
func (m *DbPhoneGatewayPermissionList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbPhoneGatewayPermissionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbPhoneGatewayPermissionList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbPhoneGatewayPermissionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbPhoneGatewayPermissionList.Merge(m, src)
}
func (m *DbPhoneGatewayPermissionList) XXX_Size() int {
	return m.Size()
}
func (m *DbPhoneGatewayPermissionList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbPhoneGatewayPermissionList.DiscardUnknown(m)
}

var xxx_messageInfo_DbPhoneGatewayPermissionList proto.InternalMessageInfo

func (m *DbPhoneGatewayPermissionList) GetRows() []*DbPhoneGatewayPermission {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 电话网关设备关系管理
type DbControllerGatewayManageList struct {
	Rows []*DbControllerGatewayManage `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbControllerGatewayManageList) Reset()         { *m = DbControllerGatewayManageList{} }
func (m *DbControllerGatewayManageList) String() string { return proto.CompactTextString(m) }
func (*DbControllerGatewayManageList) ProtoMessage()    {}
func (*DbControllerGatewayManageList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{39}
}
func (m *DbControllerGatewayManageList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbControllerGatewayManageList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbControllerGatewayManageList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbControllerGatewayManageList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbControllerGatewayManageList.Merge(m, src)
}
func (m *DbControllerGatewayManageList) XXX_Size() int {
	return m.Size()
}
func (m *DbControllerGatewayManageList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbControllerGatewayManageList.DiscardUnknown(m)
}

var xxx_messageInfo_DbControllerGatewayManageList proto.InternalMessageInfo

func (m *DbControllerGatewayManageList) GetRows() []*DbControllerGatewayManage {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 预定义电话号码本
type DbPhoneNoListList struct {
	Rows []*DbPhoneNoList `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbPhoneNoListList) Reset()         { *m = DbPhoneNoListList{} }
func (m *DbPhoneNoListList) String() string { return proto.CompactTextString(m) }
func (*DbPhoneNoListList) ProtoMessage()    {}
func (*DbPhoneNoListList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{40}
}
func (m *DbPhoneNoListList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbPhoneNoListList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbPhoneNoListList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbPhoneNoListList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbPhoneNoListList.Merge(m, src)
}
func (m *DbPhoneNoListList) XXX_Size() int {
	return m.Size()
}
func (m *DbPhoneNoListList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbPhoneNoListList.DiscardUnknown(m)
}

var xxx_messageInfo_DbPhoneNoListList proto.InternalMessageInfo

func (m *DbPhoneNoListList) GetRows() []*DbPhoneNoList {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 有源点报警历史
type DbLinepointAlarmHistoryList struct {
	Rows []*DbLinepointAlarmHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbLinepointAlarmHistoryList) Reset()         { *m = DbLinepointAlarmHistoryList{} }
func (m *DbLinepointAlarmHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbLinepointAlarmHistoryList) ProtoMessage()    {}
func (*DbLinepointAlarmHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{41}
}
func (m *DbLinepointAlarmHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbLinepointAlarmHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbLinepointAlarmHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbLinepointAlarmHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbLinepointAlarmHistoryList.Merge(m, src)
}
func (m *DbLinepointAlarmHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbLinepointAlarmHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbLinepointAlarmHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbLinepointAlarmHistoryList proto.InternalMessageInfo

func (m *DbLinepointAlarmHistoryList) GetRows() []*DbLinepointAlarmHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

type DbDeviceChannelZoneList struct {
	Rows []*DbDeviceChannelZone `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbDeviceChannelZoneList) Reset()         { *m = DbDeviceChannelZoneList{} }
func (m *DbDeviceChannelZoneList) String() string { return proto.CompactTextString(m) }
func (*DbDeviceChannelZoneList) ProtoMessage()    {}
func (*DbDeviceChannelZoneList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{42}
}
func (m *DbDeviceChannelZoneList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDeviceChannelZoneList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDeviceChannelZoneList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDeviceChannelZoneList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDeviceChannelZoneList.Merge(m, src)
}
func (m *DbDeviceChannelZoneList) XXX_Size() int {
	return m.Size()
}
func (m *DbDeviceChannelZoneList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDeviceChannelZoneList.DiscardUnknown(m)
}

var xxx_messageInfo_DbDeviceChannelZoneList proto.InternalMessageInfo

func (m *DbDeviceChannelZoneList) GetRows() []*DbDeviceChannelZone {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 用户crud log表
type DbCrudLogList struct {
	Rows []*DbCrudLog `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbCrudLogList) Reset()         { *m = DbCrudLogList{} }
func (m *DbCrudLogList) String() string { return proto.CompactTextString(m) }
func (*DbCrudLogList) ProtoMessage()    {}
func (*DbCrudLogList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{43}
}
func (m *DbCrudLogList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbCrudLogList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbCrudLogList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbCrudLogList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbCrudLogList.Merge(m, src)
}
func (m *DbCrudLogList) XXX_Size() int {
	return m.Size()
}
func (m *DbCrudLogList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbCrudLogList.DiscardUnknown(m)
}

var xxx_messageInfo_DbCrudLogList proto.InternalMessageInfo

func (m *DbCrudLogList) GetRows() []*DbCrudLog {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 动态组成员详细信息
type DbDynamicGroupDetailList struct {
	Rows []*DbDynamicGroupDetail `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbDynamicGroupDetailList) Reset()         { *m = DbDynamicGroupDetailList{} }
func (m *DbDynamicGroupDetailList) String() string { return proto.CompactTextString(m) }
func (*DbDynamicGroupDetailList) ProtoMessage()    {}
func (*DbDynamicGroupDetailList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{44}
}
func (m *DbDynamicGroupDetailList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbDynamicGroupDetailList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbDynamicGroupDetailList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbDynamicGroupDetailList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbDynamicGroupDetailList.Merge(m, src)
}
func (m *DbDynamicGroupDetailList) XXX_Size() int {
	return m.Size()
}
func (m *DbDynamicGroupDetailList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbDynamicGroupDetailList.DiscardUnknown(m)
}

var xxx_messageInfo_DbDynamicGroupDetailList proto.InternalMessageInfo

func (m *DbDynamicGroupDetailList) GetRows() []*DbDynamicGroupDetail {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 物联网终端
type DbIotDeviceList struct {
	Rows []*DbIotDevice `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbIotDeviceList) Reset()         { *m = DbIotDeviceList{} }
func (m *DbIotDeviceList) String() string { return proto.CompactTextString(m) }
func (*DbIotDeviceList) ProtoMessage()    {}
func (*DbIotDeviceList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{45}
}
func (m *DbIotDeviceList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbIotDeviceList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbIotDeviceList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbIotDeviceList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbIotDeviceList.Merge(m, src)
}
func (m *DbIotDeviceList) XXX_Size() int {
	return m.Size()
}
func (m *DbIotDeviceList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbIotDeviceList.DiscardUnknown(m)
}

var xxx_messageInfo_DbIotDeviceList proto.InternalMessageInfo

func (m *DbIotDeviceList) GetRows() []*DbIotDevice {
	if m != nil {
		return m.Rows
	}
	return nil
}

// iot限制
type DbIotRestrictionList struct {
	Rows []*DbIotRestriction `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbIotRestrictionList) Reset()         { *m = DbIotRestrictionList{} }
func (m *DbIotRestrictionList) String() string { return proto.CompactTextString(m) }
func (*DbIotRestrictionList) ProtoMessage()    {}
func (*DbIotRestrictionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{46}
}
func (m *DbIotRestrictionList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbIotRestrictionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbIotRestrictionList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbIotRestrictionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbIotRestrictionList.Merge(m, src)
}
func (m *DbIotRestrictionList) XXX_Size() int {
	return m.Size()
}
func (m *DbIotRestrictionList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbIotRestrictionList.DiscardUnknown(m)
}

var xxx_messageInfo_DbIotRestrictionList proto.InternalMessageInfo

func (m *DbIotRestrictionList) GetRows() []*DbIotRestriction {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 物联终端最后的数据信息
type DbIotDeviceLastInfoList struct {
	Rows []*DbIotDeviceLastInfo `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbIotDeviceLastInfoList) Reset()         { *m = DbIotDeviceLastInfoList{} }
func (m *DbIotDeviceLastInfoList) String() string { return proto.CompactTextString(m) }
func (*DbIotDeviceLastInfoList) ProtoMessage()    {}
func (*DbIotDeviceLastInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{47}
}
func (m *DbIotDeviceLastInfoList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbIotDeviceLastInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbIotDeviceLastInfoList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbIotDeviceLastInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbIotDeviceLastInfoList.Merge(m, src)
}
func (m *DbIotDeviceLastInfoList) XXX_Size() int {
	return m.Size()
}
func (m *DbIotDeviceLastInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbIotDeviceLastInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_DbIotDeviceLastInfoList proto.InternalMessageInfo

func (m *DbIotDeviceLastInfoList) GetRows() []*DbIotDeviceLastInfo {
	if m != nil {
		return m.Rows
	}
	return nil
}

// iot_data历史表,按月分表
type DbIotDataHistoryList struct {
	Rows []*DbIotDataHistory `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbIotDataHistoryList) Reset()         { *m = DbIotDataHistoryList{} }
func (m *DbIotDataHistoryList) String() string { return proto.CompactTextString(m) }
func (*DbIotDataHistoryList) ProtoMessage()    {}
func (*DbIotDataHistoryList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{48}
}
func (m *DbIotDataHistoryList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbIotDataHistoryList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbIotDataHistoryList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbIotDataHistoryList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbIotDataHistoryList.Merge(m, src)
}
func (m *DbIotDataHistoryList) XXX_Size() int {
	return m.Size()
}
func (m *DbIotDataHistoryList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbIotDataHistoryList.DiscardUnknown(m)
}

var xxx_messageInfo_DbIotDataHistoryList proto.InternalMessageInfo

func (m *DbIotDataHistoryList) GetRows() []*DbIotDataHistory {
	if m != nil {
		return m.Rows
	}
	return nil
}

// 设备固定订阅/静态收听表
type DbStaticSubscribesList struct {
	Rows []*DbStaticSubscribes `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbStaticSubscribesList) Reset()         { *m = DbStaticSubscribesList{} }
func (m *DbStaticSubscribesList) String() string { return proto.CompactTextString(m) }
func (*DbStaticSubscribesList) ProtoMessage()    {}
func (*DbStaticSubscribesList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{49}
}
func (m *DbStaticSubscribesList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbStaticSubscribesList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbStaticSubscribesList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbStaticSubscribesList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbStaticSubscribesList.Merge(m, src)
}
func (m *DbStaticSubscribesList) XXX_Size() int {
	return m.Size()
}
func (m *DbStaticSubscribesList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbStaticSubscribesList.DiscardUnknown(m)
}

var xxx_messageInfo_DbStaticSubscribesList proto.InternalMessageInfo

func (m *DbStaticSubscribesList) GetRows() []*DbStaticSubscribes {
	if m != nil {
		return m.Rows
	}
	return nil
}

// app用户地图显示中特别许可的其它终端列表
type DbAppMapPrivilegeDeviceList struct {
	Rows []*DbAppMapPrivilegeDevice `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbAppMapPrivilegeDeviceList) Reset()         { *m = DbAppMapPrivilegeDeviceList{} }
func (m *DbAppMapPrivilegeDeviceList) String() string { return proto.CompactTextString(m) }
func (*DbAppMapPrivilegeDeviceList) ProtoMessage()    {}
func (*DbAppMapPrivilegeDeviceList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{50}
}
func (m *DbAppMapPrivilegeDeviceList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbAppMapPrivilegeDeviceList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbAppMapPrivilegeDeviceList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbAppMapPrivilegeDeviceList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbAppMapPrivilegeDeviceList.Merge(m, src)
}
func (m *DbAppMapPrivilegeDeviceList) XXX_Size() int {
	return m.Size()
}
func (m *DbAppMapPrivilegeDeviceList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbAppMapPrivilegeDeviceList.DiscardUnknown(m)
}

var xxx_messageInfo_DbAppMapPrivilegeDeviceList proto.InternalMessageInfo

func (m *DbAppMapPrivilegeDeviceList) GetRows() []*DbAppMapPrivilegeDevice {
	if m != nil {
		return m.Rows
	}
	return nil
}

// poc session id表
type DbPocSessionList struct {
	Rows []*DbPocSession `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (m *DbPocSessionList) Reset()         { *m = DbPocSessionList{} }
func (m *DbPocSessionList) String() string { return proto.CompactTextString(m) }
func (*DbPocSessionList) ProtoMessage()    {}
func (*DbPocSessionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_6f9c49608ef2e9a9, []int{51}
}
func (m *DbPocSessionList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbPocSessionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbPocSessionList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbPocSessionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbPocSessionList.Merge(m, src)
}
func (m *DbPocSessionList) XXX_Size() int {
	return m.Size()
}
func (m *DbPocSessionList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbPocSessionList.DiscardUnknown(m)
}

var xxx_messageInfo_DbPocSessionList proto.InternalMessageInfo

func (m *DbPocSessionList) GetRows() []*DbPocSession {
	if m != nil {
		return m.Rows
	}
	return nil
}

func init() {
	proto.RegisterType((*DbSysConfigList)(nil), "bfdx_proto.db_sys_config_list")
	proto.RegisterType((*DbTableOperateTimeList)(nil), "bfdx_proto.db_table_operate_time_list")
	proto.RegisterType((*DbOrgList)(nil), "bfdx_proto.db_org_list")
	proto.RegisterType((*DbImageList)(nil), "bfdx_proto.db_image_list")
	proto.RegisterType((*DbBaseStationList)(nil), "bfdx_proto.db_base_station_list")
	proto.RegisterType((*DbControllerList)(nil), "bfdx_proto.db_controller_list")
	proto.RegisterType((*DbControllerLastInfoList)(nil), "bfdx_proto.db_controller_last_info_list")
	proto.RegisterType((*DbControllerOnlineHistoryList)(nil), "bfdx_proto.db_controller_online_history_list")
	proto.RegisterType((*DbPhoneGatewayFilterList)(nil), "bfdx_proto.db_phone_gateway_filter_list")
	proto.RegisterType((*DbDeviceList)(nil), "bfdx_proto.db_device_list")
	proto.RegisterType((*DbDeviceLastInfoList)(nil), "bfdx_proto.db_device_last_info_list")
	proto.RegisterType((*DbUserTitleList)(nil), "bfdx_proto.db_user_title_list")
	proto.RegisterType((*DbUserList)(nil), "bfdx_proto.db_user_list")
	proto.RegisterType((*DbUserPrivelegeList)(nil), "bfdx_proto.db_user_privelege_list")
	proto.RegisterType((*DbUserSessionIdList)(nil), "bfdx_proto.db_user_session_id_list")
	proto.RegisterType((*DbVirtualOrgList)(nil), "bfdx_proto.db_virtual_org_list")
	proto.RegisterType((*DbMapPointList)(nil), "bfdx_proto.db_map_point_list")
	proto.RegisterType((*DbLinePointList)(nil), "bfdx_proto.db_line_point_list")
	proto.RegisterType((*DbLinePointLatestInfoList)(nil), "bfdx_proto.db_line_point_latest_info_list")
	proto.RegisterType((*DbLineMasterList)(nil), "bfdx_proto.db_line_master_list")
	proto.RegisterType((*DbLineDetailList)(nil), "bfdx_proto.db_line_detail_list")
	proto.RegisterType((*DbRfidRuleMasterList)(nil), "bfdx_proto.db_rfid_rule_master_list")
	proto.RegisterType((*DbDevicePowerOnoffList)(nil), "bfdx_proto.db_device_power_onoff_list")
	proto.RegisterType((*DbUserCheckInHistoryList)(nil), "bfdx_proto.db_user_check_in_history_list")
	proto.RegisterType((*DbRfidHistoryList)(nil), "bfdx_proto.db_rfid_history_list")
	proto.RegisterType((*DbGpsHistoryList)(nil), "bfdx_proto.db_gps_history_list")
	proto.RegisterType((*DbAlarmHistoryList)(nil), "bfdx_proto.db_alarm_history_list")
	proto.RegisterType((*DbSoundHistoryList)(nil), "bfdx_proto.db_sound_history_list")
	proto.RegisterType((*DbNotSendCmdList)(nil), "bfdx_proto.db_not_send_cmd_list")
	proto.RegisterType((*DbSentCmdHistoryList)(nil), "bfdx_proto.db_sent_cmd_history_list")
	proto.RegisterType((*DbDeviceRegisterInfoList)(nil), "bfdx_proto.db_device_register_info_list")
	proto.RegisterType((*DbCallDispatchHistoryList)(nil), "bfdx_proto.db_call_dispatch_history_list")
	proto.RegisterType((*DbConfDispatchHistoryList)(nil), "bfdx_proto.db_conf_dispatch_history_list")
	proto.RegisterType((*DbNotConfirmSmsList)(nil), "bfdx_proto.db_not_confirm_sms_list")
	proto.RegisterType((*DbSmsHistoryList)(nil), "bfdx_proto.db_sms_history_list")
	proto.RegisterType((*DbChRfSettingList)(nil), "bfdx_proto.db_ch_rf_setting_list")
	proto.RegisterType((*DbDeviceSettingConfList)(nil), "bfdx_proto.db_device_setting_conf_list")
	proto.RegisterType((*DbPhoneShortNoList)(nil), "bfdx_proto.db_phone_short_no_list")
	proto.RegisterType((*DbPhoneGatewayPermissionList)(nil), "bfdx_proto.db_phone_gateway_permission_list")
	proto.RegisterType((*DbControllerGatewayManageList)(nil), "bfdx_proto.db_controller_gateway_manage_list")
	proto.RegisterType((*DbPhoneNoListList)(nil), "bfdx_proto.db_phone_no_list_list")
	proto.RegisterType((*DbLinepointAlarmHistoryList)(nil), "bfdx_proto.db_linepoint_alarm_history_list")
	proto.RegisterType((*DbDeviceChannelZoneList)(nil), "bfdx_proto.db_device_channel_zone_list")
	proto.RegisterType((*DbCrudLogList)(nil), "bfdx_proto.db_crud_log_list")
	proto.RegisterType((*DbDynamicGroupDetailList)(nil), "bfdx_proto.db_dynamic_group_detail_list")
	proto.RegisterType((*DbIotDeviceList)(nil), "bfdx_proto.db_iot_device_list")
	proto.RegisterType((*DbIotRestrictionList)(nil), "bfdx_proto.db_iot_restriction_list")
	proto.RegisterType((*DbIotDeviceLastInfoList)(nil), "bfdx_proto.db_iot_device_last_info_list")
	proto.RegisterType((*DbIotDataHistoryList)(nil), "bfdx_proto.db_iot_data_history_list")
	proto.RegisterType((*DbStaticSubscribesList)(nil), "bfdx_proto.db_static_subscribes_list")
	proto.RegisterType((*DbAppMapPrivilegeDeviceList)(nil), "bfdx_proto.db_app_map_privilege_device_list")
	proto.RegisterType((*DbPocSessionList)(nil), "bfdx_proto.db_poc_session_list")
}

func init() { proto.RegisterFile("db.pb.list.proto", fileDescriptor_6f9c49608ef2e9a9) }

var fileDescriptor_6f9c49608ef2e9a9 = []byte{
	// 1013 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x97, 0x51, 0x8f, 0x1b, 0x35,
	0x10, 0xc7, 0xef, 0x04, 0x42, 0xc8, 0x05, 0x04, 0xd7, 0x96, 0x5e, 0xaf, 0x6d, 0x7a, 0x3d, 0xa0,
	0x3d, 0x04, 0x04, 0x68, 0x29, 0xa5, 0x14, 0x09, 0x01, 0x42, 0x08, 0x21, 0x84, 0x00, 0x21, 0x84,
	0x10, 0x1a, 0xd9, 0x6b, 0x6f, 0x62, 0xb1, 0x6b, 0xaf, 0x6c, 0xe7, 0x8e, 0xf0, 0x29, 0xf8, 0x58,
	0x3c, 0xde, 0x23, 0x8f, 0xe8, 0xee, 0x8b, 0xa0, 0xf5, 0x7a, 0xb3, 0x3b, 0x8e, 0xb3, 0x6c, 0xfa,
	0x16, 0x25, 0xff, 0xff, 0x2f, 0xde, 0x99, 0xf1, 0xcc, 0x2c, 0x79, 0x99, 0xb3, 0x69, 0xc5, 0xa6,
	0x85, 0xb4, 0x6e, 0x5a, 0x19, 0xed, 0xf4, 0x1e, 0x61, 0x39, 0xff, 0x03, 0xfc, 0xe7, 0x83, 0xe7,
	0xeb, 0x5f, 0xeb, 0x4f, 0x47, 0x5f, 0x90, 0x3d, 0xce, 0xc0, 0x2e, 0x2d, 0x64, 0x5a, 0xe5, 0x72,
	0x06, 0xb5, 0x63, 0xef, 0x1d, 0xf2, 0xac, 0xd1, 0xa7, 0x76, 0x7f, 0xf7, 0xf0, 0x99, 0xe3, 0x4b,
	0xf7, 0xaf, 0x4f, 0x3b, 0xeb, 0x14, 0xa9, 0x7f, 0xf0, 0xb2, 0xa3, 0x1f, 0xc9, 0x01, 0x67, 0xe0,
	0x28, 0x2b, 0x04, 0xe8, 0x4a, 0x18, 0xea, 0x04, 0x38, 0x59, 0x8a, 0x06, 0xf6, 0x10, 0xc1, 0xee,
	0x44, 0xb0, 0x75, 0x57, 0x80, 0x3e, 0x24, 0x97, 0x38, 0x03, 0x6d, 0xc2, 0x91, 0xee, 0x22, 0xca,
	0x5e, 0x44, 0xd1, 0xa6, 0x3d, 0xcb, 0x63, 0xf2, 0x22, 0x67, 0x20, 0x4b, 0x3a, 0x0b, 0x7f, 0x7f,
	0x8c, 0x8c, 0x57, 0x22, 0xa3, 0x17, 0x06, 0xeb, 0x57, 0xe4, 0x0a, 0x67, 0xc0, 0xa8, 0x15, 0x60,
	0x1d, 0x75, 0x52, 0xab, 0x86, 0xf0, 0x2e, 0x22, 0xdc, 0x88, 0x08, 0x7d, 0x7d, 0x00, 0x35, 0x41,
	0xcd, 0xb4, 0x72, 0x46, 0x17, 0x85, 0x30, 0x63, 0x82, 0xda, 0xa9, 0x03, 0xe4, 0x67, 0x72, 0x33,
	0x82, 0x50, 0xeb, 0x40, 0xaa, 0x5c, 0x37, 0xb8, 0x47, 0x08, 0xf7, 0xda, 0x46, 0x5c, 0xe7, 0x0b,
	0x60, 0x4a, 0xee, 0x60, 0x81, 0x56, 0x85, 0x54, 0x02, 0xe6, 0xd2, 0x3a, 0x6d, 0x96, 0x0d, 0xfd,
	0x13, 0x44, 0x3f, 0xde, 0x4c, 0xc7, 0x66, 0x74, 0xf6, 0x6a, 0xae, 0x95, 0x80, 0x19, 0x75, 0xe2,
	0x94, 0x2e, 0x21, 0x97, 0x85, 0x6b, 0x43, 0x31, 0x7c, 0xf6, 0x94, 0x2f, 0x80, 0x9f, 0x90, 0x97,
	0x38, 0x03, 0x2e, 0x4e, 0x64, 0x16, 0xd2, 0xfb, 0x26, 0x42, 0x5d, 0x8d, 0x50, 0x8d, 0x32, 0x98,
	0xbf, 0x23, 0xfb, 0x3d, 0x33, 0x8e, 0xe6, 0x03, 0x84, 0xb9, 0x9d, 0xc4, 0xac, 0x45, 0xb2, 0xc9,
	0xf3, 0xc2, 0x0a, 0x03, 0x4e, 0xba, 0x42, 0x8c, 0xc9, 0x73, 0xa7, 0x0e, 0x90, 0x47, 0xe4, 0x85,
	0xf6, 0x6b, 0x6f, 0xbf, 0x87, 0xec, 0x97, 0x13, 0xf6, 0x60, 0xfc, 0x86, 0xbc, 0xda, 0x1a, 0x2b,
	0x23, 0x4f, 0x44, 0x21, 0xda, 0x92, 0x7f, 0x1f, 0x21, 0x6e, 0xa5, 0x4e, 0xb0, 0x72, 0x04, 0xd8,
	0xb7, 0xe4, 0x5a, 0xfb, 0x93, 0x15, 0xd6, 0xd6, 0xb5, 0x2f, 0x79, 0x43, 0xbb, 0x8f, 0x68, 0x93,
	0x14, 0xad, 0xb3, 0x04, 0xdc, 0x97, 0xe4, 0x32, 0x67, 0x70, 0x22, 0x8d, 0x5b, 0xd0, 0xa2, 0xbb,
	0xc4, 0x53, 0x84, 0x3a, 0x88, 0x50, 0x3d, 0x79, 0xc0, 0x7c, 0x46, 0x5e, 0xe1, 0x0c, 0x4a, 0x5a,
	0x41, 0xa5, 0xa5, 0x72, 0x0d, 0xe4, 0x6d, 0x04, 0xd9, 0x8f, 0x20, 0x2b, 0x31, 0xca, 0x91, 0xaf,
	0xd1, 0x1e, 0x63, 0x38, 0x47, 0x9d, 0x3a, 0x40, 0x7e, 0x25, 0x93, 0x08, 0x42, 0x9d, 0x40, 0xf5,
	0xf3, 0x18, 0x01, 0xdf, 0xd8, 0x08, 0xec, 0x3b, 0x51, 0xac, 0xbc, 0xa4, 0xa4, 0x76, 0x75, 0x47,
	0x86, 0x63, 0xd5, 0x93, 0xaf, 0x63, 0xb8, 0x70, 0x54, 0x16, 0xa3, 0x31, 0x8d, 0x1c, 0x5d, 0x12,
	0x93, 0x4b, 0x0e, 0x66, 0x51, 0xe0, 0x23, 0x0d, 0x5f, 0x92, 0xd8, 0x83, 0x86, 0x43, 0xb8, 0x41,
	0x95, 0x3e, 0xf5, 0x3d, 0x43, 0xe7, 0xf9, 0x98, 0xe1, 0xb0, 0xee, 0x0a, 0xd0, 0x5f, 0xc8, 0xad,
	0xb6, 0xf6, 0xb2, 0xb9, 0xc8, 0x7e, 0x07, 0xa9, 0x70, 0xff, 0xfa, 0x08, 0x71, 0x5f, 0x4f, 0x15,
	0x6d, 0x6c, 0x44, 0x53, 0xc0, 0x3f, 0x0c, 0x22, 0x0e, 0x4f, 0x81, 0xbe, 0x1e, 0x25, 0x64, 0x56,
	0x59, 0xcc, 0x19, 0x4e, 0x48, 0x4f, 0x1e, 0x30, 0x5f, 0x93, 0xab, 0x9c, 0x01, 0x2d, 0xa8, 0x29,
	0x31, 0xe8, 0x3d, 0x04, 0xba, 0x19, 0x81, 0x90, 0x01, 0xa1, 0xac, 0x5e, 0x28, 0xbe, 0x0d, 0x0a,
	0x19, 0x50, 0x94, 0x94, 0x76, 0x60, 0x85, 0xe2, 0x90, 0x95, 0x7c, 0x4c, 0x94, 0xfa, 0x7a, 0x54,
	0x6f, 0x56, 0x28, 0xe7, 0x21, 0xe8, 0x58, 0xc3, 0xf5, 0x16, 0x7b, 0xd0, 0xec, 0x09, 0x95, 0x63,
	0xc4, 0x4c, 0xfa, 0xfa, 0x1d, 0x3b, 0x37, 0x53, 0x3e, 0x54, 0x73, 0x19, 0x2d, 0x0a, 0xe0, 0xd2,
	0x56, 0xd4, 0x65, 0xf3, 0x6d, 0x6a, 0x2e, 0x69, 0xc4, 0x68, 0xad, 0xf2, 0xa7, 0x43, 0xa7, 0x8c,
	0xa8, 0xb1, 0xd7, 0x81, 0xf7, 0x2b, 0x9b, 0x29, 0xc1, 0x96, 0x76, 0x4c, 0x63, 0x8f, 0x2c, 0xa8,
	0xa8, 0x6b, 0xc4, 0x16, 0x45, 0xdd, 0x93, 0xa3, 0x4a, 0xcc, 0xe6, 0x60, 0x72, 0xb0, 0xc2, 0x39,
	0xa9, 0x66, 0x63, 0x2a, 0x11, 0x19, 0x02, 0xea, 0x27, 0x72, 0xa3, 0xcb, 0x5b, 0xcb, 0xf2, 0x11,
	0xf1, 0xc0, 0x0f, 0x11, 0xf0, 0x28, 0x9d, 0xee, 0xbe, 0x0d, 0x4d, 0xd7, 0x66, 0x15, 0xb1, 0x73,
	0x6d, 0x1c, 0x28, 0x3d, 0x66, 0xba, 0x62, 0x47, 0x80, 0x01, 0x39, 0x5c, 0xdb, 0x6b, 0x2a, 0x61,
	0x4a, 0xd9, 0x8c, 0x4d, 0x8f, 0x7d, 0x82, 0xb0, 0xf7, 0x06, 0x77, 0xa2, 0xce, 0xbb, 0x69, 0xa7,
	0x6b, 0x95, 0x25, 0x55, 0xab, 0x4d, 0x78, 0xf4, 0x4e, 0x87, 0xcd, 0x28, 0x65, 0xcd, 0x39, 0x42,
	0x28, 0xc6, 0xa4, 0x0c, 0x19, 0x02, 0xea, 0x37, 0x72, 0x3b, 0xcc, 0x9e, 0x66, 0x26, 0x26, 0x9a,
	0xdb, 0xc7, 0x08, 0x7a, 0x37, 0x31, 0xb6, 0x12, 0xd6, 0x54, 0x45, 0x64, 0x73, 0xaa, 0x94, 0x28,
	0xe0, 0xcf, 0xfa, 0x18, 0xe3, 0x2b, 0xa2, 0x6f, 0x0b, 0xd8, 0x4f, 0xeb, 0x97, 0x2a, 0xc8, 0xcc,
	0x82, 0x43, 0xa1, 0x43, 0xb9, 0xbe, 0x85, 0x58, 0xd7, 0xe2, 0x90, 0x06, 0x2d, 0xee, 0x4c, 0x4b,
	0x45, 0x4b, 0x99, 0xc1, 0xcc, 0xe8, 0x45, 0x85, 0x46, 0xf5, 0xff, 0x74, 0xa6, 0x84, 0x0f, 0xed,
	0x38, 0x52, 0x3b, 0xb4, 0x19, 0x0f, 0xef, 0x38, 0x9d, 0x1a, 0x35, 0x8a, 0xfa, 0x6b, 0x23, 0xac,
	0x33, 0x32, 0xeb, 0x5e, 0x80, 0x86, 0x1b, 0x45, 0x64, 0x41, 0x0f, 0xdb, 0x3f, 0xd3, 0x36, 0xaf,
	0x2f, 0x29, 0x1f, 0x1a, 0x18, 0x5e, 0x40, 0x1d, 0xdd, 0x66, 0x60, 0xc4, 0x9e, 0x00, 0xfc, 0x9e,
	0x5c, 0xaf, 0x7b, 0x54, 0xfd, 0x06, 0x97, 0x81, 0x5d, 0x30, 0x9b, 0x19, 0xc9, 0x44, 0xe8, 0x91,
	0x1f, 0x20, 0xe2, 0x61, 0xdc, 0xd8, 0x62, 0x13, 0xba, 0xef, 0xb4, 0xaa, 0x9a, 0x75, 0xd4, 0xc8,
	0x13, 0xe9, 0xb7, 0xf3, 0x7e, 0x7a, 0x86, 0xef, 0xfb, 0x26, 0x2f, 0x6a, 0xc3, 0x95, 0xce, 0x56,
	0xab, 0xf7, 0x88, 0x36, 0xdc, 0x93, 0x37, 0x98, 0xcf, 0xf7, 0xff, 0x3e, 0x9f, 0xec, 0x9e, 0x9d,
	0x4f, 0x76, 0xff, 0x3d, 0x9f, 0xec, 0xfe, 0x75, 0x31, 0xd9, 0x39, 0xbb, 0x98, 0xec, 0xfc, 0x73,
	0x31, 0xd9, 0x61, 0xcf, 0x79, 0xd7, 0x83, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x6f, 0x1a, 0x8c,
	0x08, 0x48, 0x10, 0x00, 0x00,
}

func (m *DbSysConfigList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbSysConfigList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbSysConfigList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbTableOperateTimeList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbTableOperateTimeList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbTableOperateTimeList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbOrgList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbOrgList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbOrgList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbImageList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbImageList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbImageList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbBaseStationList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbBaseStationList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbBaseStationList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbControllerList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbControllerList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbControllerList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbControllerLastInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbControllerLastInfoList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbControllerLastInfoList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbControllerOnlineHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbControllerOnlineHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbControllerOnlineHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbPhoneGatewayFilterList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbPhoneGatewayFilterList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbPhoneGatewayFilterList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbDeviceList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDeviceList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDeviceList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbDeviceLastInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDeviceLastInfoList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDeviceLastInfoList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbUserTitleList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserTitleList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserTitleList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbUserList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbUserPrivelegeList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserPrivelegeList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserPrivelegeList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbUserSessionIdList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserSessionIdList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserSessionIdList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbVirtualOrgList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbVirtualOrgList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbVirtualOrgList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbMapPointList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbMapPointList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbMapPointList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbLinePointList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbLinePointList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbLinePointList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbLinePointLatestInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbLinePointLatestInfoList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbLinePointLatestInfoList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbLineMasterList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbLineMasterList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbLineMasterList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbLineDetailList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbLineDetailList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbLineDetailList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbRfidRuleMasterList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbRfidRuleMasterList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbRfidRuleMasterList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbDevicePowerOnoffList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDevicePowerOnoffList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDevicePowerOnoffList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbUserCheckInHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserCheckInHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserCheckInHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbRfidHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbRfidHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbRfidHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbGpsHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbGpsHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbGpsHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbAlarmHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbAlarmHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbAlarmHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbSoundHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbSoundHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbSoundHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbNotSendCmdList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbNotSendCmdList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbNotSendCmdList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbSentCmdHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbSentCmdHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbSentCmdHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbDeviceRegisterInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDeviceRegisterInfoList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDeviceRegisterInfoList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbCallDispatchHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbCallDispatchHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbCallDispatchHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbConfDispatchHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbConfDispatchHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbConfDispatchHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbNotConfirmSmsList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbNotConfirmSmsList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbNotConfirmSmsList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbSmsHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbSmsHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbSmsHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbChRfSettingList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbChRfSettingList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbChRfSettingList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbDeviceSettingConfList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDeviceSettingConfList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDeviceSettingConfList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbPhoneShortNoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbPhoneShortNoList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbPhoneShortNoList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbPhoneGatewayPermissionList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbPhoneGatewayPermissionList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbPhoneGatewayPermissionList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbControllerGatewayManageList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbControllerGatewayManageList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbControllerGatewayManageList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbPhoneNoListList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbPhoneNoListList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbPhoneNoListList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbLinepointAlarmHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbLinepointAlarmHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbLinepointAlarmHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbDeviceChannelZoneList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDeviceChannelZoneList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDeviceChannelZoneList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbCrudLogList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbCrudLogList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbCrudLogList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbDynamicGroupDetailList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbDynamicGroupDetailList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbDynamicGroupDetailList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbIotDeviceList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbIotDeviceList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbIotDeviceList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbIotRestrictionList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbIotRestrictionList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbIotRestrictionList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbIotDeviceLastInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbIotDeviceLastInfoList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbIotDeviceLastInfoList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbIotDataHistoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbIotDataHistoryList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbIotDataHistoryList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbStaticSubscribesList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbStaticSubscribesList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbStaticSubscribesList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbAppMapPrivilegeDeviceList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbAppMapPrivilegeDeviceList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbAppMapPrivilegeDeviceList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DbPocSessionList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbPocSessionList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbPocSessionList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDbPbList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintDbPbList(dAtA []byte, offset int, v uint64) int {
	offset -= sovDbPbList(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbSysConfigList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbTableOperateTimeList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbOrgList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbImageList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbBaseStationList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbControllerList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbControllerLastInfoList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbControllerOnlineHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbPhoneGatewayFilterList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbDeviceList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbDeviceLastInfoList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbUserTitleList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbUserList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbUserPrivelegeList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbUserSessionIdList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbVirtualOrgList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbMapPointList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbLinePointList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbLinePointLatestInfoList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbLineMasterList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbLineDetailList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbRfidRuleMasterList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbDevicePowerOnoffList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbUserCheckInHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbRfidHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbGpsHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbAlarmHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbSoundHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbNotSendCmdList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbSentCmdHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbDeviceRegisterInfoList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbCallDispatchHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbConfDispatchHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbNotConfirmSmsList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbSmsHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbChRfSettingList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbDeviceSettingConfList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbPhoneShortNoList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbPhoneGatewayPermissionList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbControllerGatewayManageList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbPhoneNoListList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbLinepointAlarmHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbDeviceChannelZoneList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbCrudLogList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbDynamicGroupDetailList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbIotDeviceList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbIotRestrictionList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbIotDeviceLastInfoList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbIotDataHistoryList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbStaticSubscribesList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbAppMapPrivilegeDeviceList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func (m *DbPocSessionList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovDbPbList(uint64(l))
		}
	}
	return n
}

func sovDbPbList(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDbPbList(x uint64) (n int) {
	return sovDbPbList(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbSysConfigList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_sys_config_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_sys_config_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbSysConfig{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbTableOperateTimeList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_table_operate_time_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_table_operate_time_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbTableOperateTime{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbOrgList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_org_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_org_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbOrg{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbImageList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_image_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_image_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbImage{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbBaseStationList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_base_station_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_base_station_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbBaseStation{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbControllerList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_controller_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_controller_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbController{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbControllerLastInfoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_controller_last_info_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_controller_last_info_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbControllerLastInfo{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbControllerOnlineHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_controller_online_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_controller_online_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbControllerOnlineHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbPhoneGatewayFilterList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_phone_gateway_filter_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_phone_gateway_filter_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbPhoneGatewayFilter{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDeviceList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_device_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_device_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbDevice{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDeviceLastInfoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_device_last_info_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_device_last_info_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbDeviceLastInfo{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbUserTitleList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_user_title_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_user_title_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUserTitle{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbUserList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_user_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_user_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUser{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbUserPrivelegeList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_user_privelege_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_user_privelege_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUserPrivelege{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbUserSessionIdList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_user_session_id_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_user_session_id_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUserSessionId{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbVirtualOrgList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_virtual_org_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_virtual_org_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbVirtualOrg{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbMapPointList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_map_point_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_map_point_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbMapPoint{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbLinePointList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_line_point_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_line_point_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbLinePoint{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbLinePointLatestInfoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_line_point_latest_info_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_line_point_latest_info_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbLinePointLatestInfo{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbLineMasterList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_line_master_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_line_master_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbLineMaster{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbLineDetailList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_line_detail_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_line_detail_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbLineDetail{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbRfidRuleMasterList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_rfid_rule_master_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_rfid_rule_master_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbRfidRuleMaster{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDevicePowerOnoffList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_device_power_onoff_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_device_power_onoff_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbDevicePowerOnoff{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbUserCheckInHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_user_check_in_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_user_check_in_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUserCheckInHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbRfidHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_rfid_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_rfid_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbRfidHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbGpsHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_gps_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_gps_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbGpsHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbAlarmHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_alarm_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_alarm_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbAlarmHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbSoundHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_sound_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_sound_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbSoundHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbNotSendCmdList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_not_send_cmd_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_not_send_cmd_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbNotSendCmd{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbSentCmdHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_sent_cmd_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_sent_cmd_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbSentCmdHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDeviceRegisterInfoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_device_register_info_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_device_register_info_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbDeviceRegisterInfo{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbCallDispatchHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_call_dispatch_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_call_dispatch_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbCallDispatchHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbConfDispatchHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_conf_dispatch_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_conf_dispatch_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbConfDispatchHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbNotConfirmSmsList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_not_confirm_sms_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_not_confirm_sms_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbNotConfirmSms{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbSmsHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_sms_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_sms_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbSmsHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbChRfSettingList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_ch_rf_setting_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_ch_rf_setting_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbChRfSetting{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDeviceSettingConfList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_device_setting_conf_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_device_setting_conf_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbDeviceSettingConf{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbPhoneShortNoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_phone_short_no_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_phone_short_no_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbPhoneShortNo{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbPhoneGatewayPermissionList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_phone_gateway_permission_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_phone_gateway_permission_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbPhoneGatewayPermission{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbControllerGatewayManageList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_controller_gateway_manage_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_controller_gateway_manage_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbControllerGatewayManage{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbPhoneNoListList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_phone_no_list_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_phone_no_list_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbPhoneNoList{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbLinepointAlarmHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_linepoint_alarm_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_linepoint_alarm_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbLinepointAlarmHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDeviceChannelZoneList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_device_channel_zone_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_device_channel_zone_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbDeviceChannelZone{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbCrudLogList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_crud_log_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_crud_log_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbCrudLog{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbDynamicGroupDetailList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_dynamic_group_detail_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_dynamic_group_detail_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbDynamicGroupDetail{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbIotDeviceList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_iot_device_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_iot_device_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbIotDevice{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbIotRestrictionList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_iot_restriction_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_iot_restriction_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbIotRestriction{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbIotDeviceLastInfoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_iot_device_last_info_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_iot_device_last_info_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbIotDeviceLastInfo{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbIotDataHistoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_iot_data_history_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_iot_data_history_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbIotDataHistory{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbStaticSubscribesList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_static_subscribes_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_static_subscribes_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbStaticSubscribes{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbAppMapPrivilegeDeviceList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_app_map_privilege_device_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_app_map_privilege_device_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbAppMapPrivilegeDevice{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbPocSessionList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: db_poc_session_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: db_poc_session_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDbPbList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDbPbList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbPocSession{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDbPbList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDbPbList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDbPbList(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDbPbList
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDbPbList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDbPbList
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDbPbList
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDbPbList
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDbPbList        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDbPbList          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDbPbList = fmt.Errorf("proto: unexpected end of group")
)
