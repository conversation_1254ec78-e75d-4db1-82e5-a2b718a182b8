// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: bf8100.proto

package bfkcp

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// * 卫星定位数据类型 *
type MESH_GPS_DATA_TYPE int32

const (
	//*0:无*
	MESH_GPS_DATA_TYPE_ST_NONE MESH_GPS_DATA_TYPE = 0
	//*1:功能键*
	MESH_GPS_DATA_TYPE_ST_FUNCTION_KEY MESH_GPS_DATA_TYPE = 1
	//*2:开机*
	MESH_GPS_DATA_TYPE_ST_POWER_ON MESH_GPS_DATA_TYPE = 2
	//*3:关机*
	MESH_GPS_DATA_TYPE_ST_POWER_OFF MESH_GPS_DATA_TYPE = 3
	//*4:定时*
	MESH_GPS_DATA_TYPE_ST_TIME MESH_GPS_DATA_TYPE = 4
	//*5:距离超出*
	MESH_GPS_DATA_TYPE_ST_DISTANCE MESH_GPS_DATA_TYPE = 5
	//*6:通过菜单发送*
	MESH_GPS_DATA_TYPE_ST_FUNCTION_MENU MESH_GPS_DATA_TYPE = 6
	//*7:开机密码错误自毙*
	MESH_GPS_DATA_TYPE_ST_PWDERR MESH_GPS_DATA_TYPE = 7
	//*8:收到对讲机遥毙*
	MESH_GPS_DATA_TYPE_ST_DEVICE_DISABLE MESH_GPS_DATA_TYPE = 8
	//*9:收到远程监听*
	MESH_GPS_DATA_TYPE_ST_REMOTE_MONITOR MESH_GPS_DATA_TYPE = 9
	//*10:超出PTT次数*
	MESH_GPS_DATA_TYPE_ST_PTT_BEYOND MESH_GPS_DATA_TYPE = 10
	//*11:超出连接次数*
	MESH_GPS_DATA_TYPE_ST_LINK_BEYOND MESH_GPS_DATA_TYPE = 11
	//*12:被GPS查询发送*
	MESH_GPS_DATA_TYPE_ST_GPS_QUERY MESH_GPS_DATA_TYPE = 12
	//*13:发起警报发送*
	MESH_GPS_DATA_TYPE_ST_TX_ALARM MESH_GPS_DATA_TYPE = 13
)

var MESH_GPS_DATA_TYPE_name = map[int32]string{
	0:  "ST_NONE",
	1:  "ST_FUNCTION_KEY",
	2:  "ST_POWER_ON",
	3:  "ST_POWER_OFF",
	4:  "ST_TIME",
	5:  "ST_DISTANCE",
	6:  "ST_FUNCTION_MENU",
	7:  "ST_PWDERR",
	8:  "ST_DEVICE_DISABLE",
	9:  "ST_REMOTE_MONITOR",
	10: "ST_PTT_BEYOND",
	11: "ST_LINK_BEYOND",
	12: "ST_GPS_QUERY",
	13: "ST_TX_ALARM",
}

var MESH_GPS_DATA_TYPE_value = map[string]int32{
	"ST_NONE":           0,
	"ST_FUNCTION_KEY":   1,
	"ST_POWER_ON":       2,
	"ST_POWER_OFF":      3,
	"ST_TIME":           4,
	"ST_DISTANCE":       5,
	"ST_FUNCTION_MENU":  6,
	"ST_PWDERR":         7,
	"ST_DEVICE_DISABLE": 8,
	"ST_REMOTE_MONITOR": 9,
	"ST_PTT_BEYOND":     10,
	"ST_LINK_BEYOND":    11,
	"ST_GPS_QUERY":      12,
	"ST_TX_ALARM":       13,
}

func (x MESH_GPS_DATA_TYPE) String() string {
	return proto.EnumName(MESH_GPS_DATA_TYPE_name, int32(x))
}

func (MESH_GPS_DATA_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{0}
}

// 系统中所有的消息交互都以此为包装
type RpcCmd struct {
	// sequence no
	SeqNo int32 `protobuf:"varint,2,opt,name=seq_no,json=seqNo,proto3" json:"seq_no,omitempty"`
	// session id
	Sid int64 `protobuf:"varint,3,opt,name=sid,proto3" json:"sid,omitempty"`
	// rpc command code
	Cmd int32 `protobuf:"varint,5,opt,name=cmd,proto3" json:"cmd,omitempty"`
	// response code
	Res int32 `protobuf:"varint,8,opt,name=res,proto3" json:"res,omitempty"`
	// command body
	Body []byte `protobuf:"bytes,10,opt,name=body,proto3" json:"body,omitempty"`
	// optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
	ParaStr string `protobuf:"bytes,11,opt,name=para_str,json=paraStr,proto3" json:"para_str,omitempty"`
	// optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
	ParaBin []byte `protobuf:"bytes,12,opt,name=para_bin,json=paraBin,proto3" json:"para_bin,omitempty"`
	// optional int64 parameter
	ParaInt int64 `protobuf:"varint,13,opt,name=para_int,json=paraInt,proto3" json:"para_int,omitempty"`
}

func (m *RpcCmd) Reset()         { *m = RpcCmd{} }
func (m *RpcCmd) String() string { return proto.CompactTextString(m) }
func (*RpcCmd) ProtoMessage()    {}
func (*RpcCmd) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{0}
}
func (m *RpcCmd) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RpcCmd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RpcCmd.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RpcCmd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RpcCmd.Merge(m, src)
}
func (m *RpcCmd) XXX_Size() int {
	return m.Size()
}
func (m *RpcCmd) XXX_DiscardUnknown() {
	xxx_messageInfo_RpcCmd.DiscardUnknown(m)
}

var xxx_messageInfo_RpcCmd proto.InternalMessageInfo

func (m *RpcCmd) GetSeqNo() int32 {
	if m != nil {
		return m.SeqNo
	}
	return 0
}

func (m *RpcCmd) GetSid() int64 {
	if m != nil {
		return m.Sid
	}
	return 0
}

func (m *RpcCmd) GetCmd() int32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *RpcCmd) GetRes() int32 {
	if m != nil {
		return m.Res
	}
	return 0
}

func (m *RpcCmd) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *RpcCmd) GetParaStr() string {
	if m != nil {
		return m.ParaStr
	}
	return ""
}

func (m *RpcCmd) GetParaBin() []byte {
	if m != nil {
		return m.ParaBin
	}
	return nil
}

func (m *RpcCmd) GetParaInt() int64 {
	if m != nil {
		return m.ParaInt
	}
	return 0
}

// 登录请求   rpc.cmd=100
type Login struct {
	// 设备dmrid  android端可不填
	DeviceDmrid uint32 `protobuf:"fixed32,1,opt,name=device_dmrid,json=deviceDmrid,proto3" json:"device_dmrid,omitempty"`
	// 设备名字  android端填写username
	DeviceName string `protobuf:"bytes,2,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	// 登录类型,0:中继台　1:控制台程序 2:控制台程序/网关终端  3:android端登录 23:公网poc登录
	LoginType int32 `protobuf:"varint,3,opt,name=login_type,json=loginType,proto3" json:"login_type,omitempty"`
	// 设备型号    android端可不填
	// BF8100项目:TR805005,双频中继，2个时隙
	// BF-TR925项目:TR092500,双频中继，2个时隙
	// BF-TR925R项目:TR092501，单频中继，1个时隙
	// BF-TR925D项目:TR09250M，单频中继，1个时隙
	DeviceModel string `protobuf:"bytes,4,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`
	// 密码检验值   android端填写password/sid
	Password string `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	// andoid端，公网poc端：
	// 11:使用密码登录,password=base64(sha256(time_str+base64(sha256(user_name+user_pass))))
	// 12:使用sid登录,首次登录
	// 13:使用sid登录，断线重连登录
	PasswordMethod int32 `protobuf:"varint,6,opt,name=password_method,json=passwordMethod,proto3" json:"password_method,omitempty"`
	// password_method=10/11 时需要的参数
	// 取当前时间(utc)格式为: yyyy-mm-dd hh:mm:ss
	TimeStr string `protobuf:"bytes,7,opt,name=time_str,json=timeStr,proto3" json:"time_str,omitempty"`
	// 系统号,00-63
	SysId string `protobuf:"bytes,8,opt,name=sys_id,json=sysId,proto3" json:"sys_id,omitempty"`
	// 额外要求的功能,每个额外的功能有相应的功能号
	// 8: 中继支持监控功能
	// 107：要求曾经在此中继下注册过的终端发送终端已在其它地方登录的提示，功能号为107
	// 175: 控制器要求发送所有的会话结束信息，功能号为175
	// 375: app 要求转发gps信息，它有地图显示功能
	ExtraOption []int32 `protobuf:"varint,9,rep,packed,name=extra_option,json=extraOption,proto3" json:"extra_option,omitempty"`
	// 支持的codec编码器，用于后台判断是否支持该编码器以便转发相关数据
	// 目前支持的codec编码器：
	// 0: dmr ambe  -> bc30
	// 1: opus  -> bc10
	// 默认不填写，即只支持标准 dmr ambe，null=[0]
	// poc端一般=[1]
	// cm625=[0,1]
	Codec []int32 `protobuf:"varint,10,rep,packed,name=codec,proto3" json:"codec,omitempty"`
}

func (m *Login) Reset()         { *m = Login{} }
func (m *Login) String() string { return proto.CompactTextString(m) }
func (*Login) ProtoMessage()    {}
func (*Login) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{1}
}
func (m *Login) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Login) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Login.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Login) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Login.Merge(m, src)
}
func (m *Login) XXX_Size() int {
	return m.Size()
}
func (m *Login) XXX_DiscardUnknown() {
	xxx_messageInfo_Login.DiscardUnknown(m)
}

var xxx_messageInfo_Login proto.InternalMessageInfo

func (m *Login) GetDeviceDmrid() uint32 {
	if m != nil {
		return m.DeviceDmrid
	}
	return 0
}

func (m *Login) GetDeviceName() string {
	if m != nil {
		return m.DeviceName
	}
	return ""
}

func (m *Login) GetLoginType() int32 {
	if m != nil {
		return m.LoginType
	}
	return 0
}

func (m *Login) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *Login) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *Login) GetPasswordMethod() int32 {
	if m != nil {
		return m.PasswordMethod
	}
	return 0
}

func (m *Login) GetTimeStr() string {
	if m != nil {
		return m.TimeStr
	}
	return ""
}

func (m *Login) GetSysId() string {
	if m != nil {
		return m.SysId
	}
	return ""
}

func (m *Login) GetExtraOption() []int32 {
	if m != nil {
		return m.ExtraOption
	}
	return nil
}

func (m *Login) GetCodec() []int32 {
	if m != nil {
		return m.Codec
	}
	return nil
}

// 登录回应的额外信息。放入rpc_cmd.para_bin
type ResLoginParaBin struct {
	ValidSnCode []byte `protobuf:"bytes,1,opt,name=validSnCode,proto3" json:"validSnCode,omitempty"`
	ImbeSn      string `protobuf:"bytes,2,opt,name=imbeSn,proto3" json:"imbeSn,omitempty"`
	// 0.没有权限  1.具有全呼权限
	IsHaveFullCallPerm int32 `protobuf:"varint,3,opt,name=isHaveFullCallPerm,proto3" json:"isHaveFullCallPerm,omitempty"`
}

func (m *ResLoginParaBin) Reset()         { *m = ResLoginParaBin{} }
func (m *ResLoginParaBin) String() string { return proto.CompactTextString(m) }
func (*ResLoginParaBin) ProtoMessage()    {}
func (*ResLoginParaBin) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{2}
}
func (m *ResLoginParaBin) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResLoginParaBin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResLoginParaBin.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResLoginParaBin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResLoginParaBin.Merge(m, src)
}
func (m *ResLoginParaBin) XXX_Size() int {
	return m.Size()
}
func (m *ResLoginParaBin) XXX_DiscardUnknown() {
	xxx_messageInfo_ResLoginParaBin.DiscardUnknown(m)
}

var xxx_messageInfo_ResLoginParaBin proto.InternalMessageInfo

func (m *ResLoginParaBin) GetValidSnCode() []byte {
	if m != nil {
		return m.ValidSnCode
	}
	return nil
}

func (m *ResLoginParaBin) GetImbeSn() string {
	if m != nil {
		return m.ImbeSn
	}
	return ""
}

func (m *ResLoginParaBin) GetIsHaveFullCallPerm() int32 {
	if m != nil {
		return m.IsHaveFullCallPerm
	}
	return 0
}

// 登录回应
// 特别的，andoid端 需要主动返回 channels等信息
type ResLogin struct {
	// 登录回应值
	// 0:登录成功，
	// 1:重复登录
	// 4:密码不对
	// 5:没有指定密码登录
	// 10:登录失败,不存在此设备
	// 44:bad param
	// 303: 用户没有指定设备
	// 404: 此session id不存在,需要换用户名密码登录
	// 500: 服务器内部错误
	ResCode int32 `protobuf:"varint,1,opt,name=res_code,json=resCode,proto3" json:"res_code,omitempty"`
	// last session id
	Sid int64 `protobuf:"varint,3,opt,name=sid,proto3" json:"sid,omitempty"`
	// 服务器配置的挂起时间，单位毫秒,0为无效值
	HangupTime int32 `protobuf:"varint,4,opt,name=hangup_time,json=hangupTime,proto3" json:"hangup_time,omitempty"`
	// 服务器http端口
	HttpPort int32 `protobuf:"varint,5,opt,name=http_port,json=httpPort,proto3" json:"http_port,omitempty"`
	// 服务器版本号
	ServerVersion string `protobuf:"bytes,6,opt,name=server_version,json=serverVersion,proto3" json:"server_version,omitempty"`
	// 配置最后更新时间,utc时间
	// 目前只有poc终端有此字段
	SettingLastUpdateTime string `protobuf:"bytes,7,opt,name=setting_last_update_time,json=settingLastUpdateTime,proto3" json:"setting_last_update_time,omitempty"`
}

func (m *ResLogin) Reset()         { *m = ResLogin{} }
func (m *ResLogin) String() string { return proto.CompactTextString(m) }
func (*ResLogin) ProtoMessage()    {}
func (*ResLogin) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{3}
}
func (m *ResLogin) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResLogin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResLogin.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResLogin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResLogin.Merge(m, src)
}
func (m *ResLogin) XXX_Size() int {
	return m.Size()
}
func (m *ResLogin) XXX_DiscardUnknown() {
	xxx_messageInfo_ResLogin.DiscardUnknown(m)
}

var xxx_messageInfo_ResLogin proto.InternalMessageInfo

func (m *ResLogin) GetResCode() int32 {
	if m != nil {
		return m.ResCode
	}
	return 0
}

func (m *ResLogin) GetSid() int64 {
	if m != nil {
		return m.Sid
	}
	return 0
}

func (m *ResLogin) GetHangupTime() int32 {
	if m != nil {
		return m.HangupTime
	}
	return 0
}

func (m *ResLogin) GetHttpPort() int32 {
	if m != nil {
		return m.HttpPort
	}
	return 0
}

func (m *ResLogin) GetServerVersion() string {
	if m != nil {
		return m.ServerVersion
	}
	return ""
}

func (m *ResLogin) GetSettingLastUpdateTime() string {
	if m != nil {
		return m.SettingLastUpdateTime
	}
	return ""
}

// 中继状态获取的应答
// rpc.cmd=185
// rpc.res=1
// rpc.body=res_repeater_state
type ResRepeaterState struct {
	// 设备dmrid
	DeviceDmrid uint32 `protobuf:"fixed32,1,opt,name=device_dmrid,json=deviceDmrid,proto3" json:"device_dmrid,omitempty"`
	// 信道ID
	ChannelId int32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 接收频率,Mhz
	RxFrequency uint32 `protobuf:"fixed32,3,opt,name=rx_frequency,json=rxFrequency,proto3" json:"rx_frequency,omitempty"`
	// 发射频率,Mhz
	TxFrequency uint32 `protobuf:"fixed32,4,opt,name=tx_frequency,json=txFrequency,proto3" json:"tx_frequency,omitempty"`
	// 功率值,W
	PowerValue int32 `protobuf:"varint,5,opt,name=power_value,json=powerValue,proto3" json:"power_value,omitempty"`
	// 中继本地IP地址
	IpAddr uint32 `protobuf:"fixed32,6,opt,name=ip_addr,json=ipAddr,proto3" json:"ip_addr,omitempty"`
	// 电压值,mV
	VolValue int32 `protobuf:"varint,7,opt,name=vol_value,json=volValue,proto3" json:"vol_value,omitempty"`
	// 温度值,单位 0.1摄氏度
	TmpValue int32 `protobuf:"varint,8,opt,name=tmp_value,json=tmpValue,proto3" json:"tmp_value,omitempty"`
	// 温度状态
	// 0:正常
	// 1:温度过高
	TmpErr int32 `protobuf:"varint,9,opt,name=tmp_err,json=tmpErr,proto3" json:"tmp_err,omitempty"`
	// 天线(驻波)状态
	// 0:正常
	// 1:异常
	AntErr int32 `protobuf:"varint,10,opt,name=ant_err,json=antErr,proto3" json:"ant_err,omitempty"`
	// GPS同步状态
	// 0:未安装
	// 1:未同步
	// 2:已同步
	GpsErr int32 `protobuf:"varint,11,opt,name=gps_err,json=gpsErr,proto3" json:"gps_err,omitempty"`
	// 电压状态
	// 0:正常
	// 1:电压过高
	// 2:电压过低
	VolErr int32 `protobuf:"varint,12,opt,name=vol_err,json=volErr,proto3" json:"vol_err,omitempty"`
	// 接收pll异常
	// 0:正常
	// 1:异常
	RxPllErr int32 `protobuf:"varint,13,opt,name=rx_pll_err,json=rxPllErr,proto3" json:"rx_pll_err,omitempty"`
	// 发射pll异常
	// 0:正常
	// 1:异常
	TxPllErr int32 `protobuf:"varint,14,opt,name=tx_pll_err,json=txPllErr,proto3" json:"tx_pll_err,omitempty"`
	// 风扇异常
	// 0:正常
	// 1:异常
	FanErr int32 `protobuf:"varint,15,opt,name=fan_err,json=fanErr,proto3" json:"fan_err,omitempty"`
	// 信号干扰
	// 0:无信号干扰
	// 1:有信号干扰
	Signal int32 `protobuf:"varint,16,opt,name=signal,proto3" json:"signal,omitempty"`
	// 驻波值,单位 0.1
	AntValue int32 `protobuf:"varint,17,opt,name=ant_value,json=antValue,proto3" json:"ant_value,omitempty"`
}

func (m *ResRepeaterState) Reset()         { *m = ResRepeaterState{} }
func (m *ResRepeaterState) String() string { return proto.CompactTextString(m) }
func (*ResRepeaterState) ProtoMessage()    {}
func (*ResRepeaterState) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{4}
}
func (m *ResRepeaterState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResRepeaterState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResRepeaterState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResRepeaterState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResRepeaterState.Merge(m, src)
}
func (m *ResRepeaterState) XXX_Size() int {
	return m.Size()
}
func (m *ResRepeaterState) XXX_DiscardUnknown() {
	xxx_messageInfo_ResRepeaterState.DiscardUnknown(m)
}

var xxx_messageInfo_ResRepeaterState proto.InternalMessageInfo

func (m *ResRepeaterState) GetDeviceDmrid() uint32 {
	if m != nil {
		return m.DeviceDmrid
	}
	return 0
}

func (m *ResRepeaterState) GetChannelId() int32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ResRepeaterState) GetRxFrequency() uint32 {
	if m != nil {
		return m.RxFrequency
	}
	return 0
}

func (m *ResRepeaterState) GetTxFrequency() uint32 {
	if m != nil {
		return m.TxFrequency
	}
	return 0
}

func (m *ResRepeaterState) GetPowerValue() int32 {
	if m != nil {
		return m.PowerValue
	}
	return 0
}

func (m *ResRepeaterState) GetIpAddr() uint32 {
	if m != nil {
		return m.IpAddr
	}
	return 0
}

func (m *ResRepeaterState) GetVolValue() int32 {
	if m != nil {
		return m.VolValue
	}
	return 0
}

func (m *ResRepeaterState) GetTmpValue() int32 {
	if m != nil {
		return m.TmpValue
	}
	return 0
}

func (m *ResRepeaterState) GetTmpErr() int32 {
	if m != nil {
		return m.TmpErr
	}
	return 0
}

func (m *ResRepeaterState) GetAntErr() int32 {
	if m != nil {
		return m.AntErr
	}
	return 0
}

func (m *ResRepeaterState) GetGpsErr() int32 {
	if m != nil {
		return m.GpsErr
	}
	return 0
}

func (m *ResRepeaterState) GetVolErr() int32 {
	if m != nil {
		return m.VolErr
	}
	return 0
}

func (m *ResRepeaterState) GetRxPllErr() int32 {
	if m != nil {
		return m.RxPllErr
	}
	return 0
}

func (m *ResRepeaterState) GetTxPllErr() int32 {
	if m != nil {
		return m.TxPllErr
	}
	return 0
}

func (m *ResRepeaterState) GetFanErr() int32 {
	if m != nil {
		return m.FanErr
	}
	return 0
}

func (m *ResRepeaterState) GetSignal() int32 {
	if m != nil {
		return m.Signal
	}
	return 0
}

func (m *ResRepeaterState) GetAntValue() int32 {
	if m != nil {
		return m.AntValue
	}
	return 0
}

// 中继异常状态上报
// rpc.cmd=188
// rpc.res=0
// rpc.body=repeater_err_status
type RepeaterErrStatus struct {
	// 设备dmrid
	DeviceDmrid uint32 `protobuf:"fixed32,1,opt,name=device_dmrid,json=deviceDmrid,proto3" json:"device_dmrid,omitempty"`
	// 温度状态
	// 0:正常
	// 1:温度过高
	TmpErr int32 `protobuf:"varint,2,opt,name=tmp_err,json=tmpErr,proto3" json:"tmp_err,omitempty"`
	// 天线(驻波)状态
	// 0:正常
	// 1:异常
	AntErr int32 `protobuf:"varint,3,opt,name=ant_err,json=antErr,proto3" json:"ant_err,omitempty"`
	// GPS同步状态
	// 0:未安装
	// 1:未同步
	// 2:已同步
	GpsErr int32 `protobuf:"varint,4,opt,name=gps_err,json=gpsErr,proto3" json:"gps_err,omitempty"`
	// 电压状态
	// 0:正常
	// 1:电压过高
	// 2:电压过低
	VolErr int32 `protobuf:"varint,5,opt,name=vol_err,json=volErr,proto3" json:"vol_err,omitempty"`
	// 接收pll异常
	// 0:正常
	// 1:异常
	RxPllErr int32 `protobuf:"varint,6,opt,name=rx_pll_err,json=rxPllErr,proto3" json:"rx_pll_err,omitempty"`
	// 发射pll异常
	// 0:正常
	// 1:异常
	TxPllErr int32 `protobuf:"varint,7,opt,name=tx_pll_err,json=txPllErr,proto3" json:"tx_pll_err,omitempty"`
	// 风扇异常
	// 0:正常
	// 1:异常
	FanErr int32 `protobuf:"varint,8,opt,name=fan_err,json=fanErr,proto3" json:"fan_err,omitempty"`
	// 信号干扰
	// 0:无信号干扰
	// 1:有信号干扰
	Signal int32 `protobuf:"varint,9,opt,name=signal,proto3" json:"signal,omitempty"`
}

func (m *RepeaterErrStatus) Reset()         { *m = RepeaterErrStatus{} }
func (m *RepeaterErrStatus) String() string { return proto.CompactTextString(m) }
func (*RepeaterErrStatus) ProtoMessage()    {}
func (*RepeaterErrStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{5}
}
func (m *RepeaterErrStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RepeaterErrStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RepeaterErrStatus.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RepeaterErrStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RepeaterErrStatus.Merge(m, src)
}
func (m *RepeaterErrStatus) XXX_Size() int {
	return m.Size()
}
func (m *RepeaterErrStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_RepeaterErrStatus.DiscardUnknown(m)
}

var xxx_messageInfo_RepeaterErrStatus proto.InternalMessageInfo

func (m *RepeaterErrStatus) GetDeviceDmrid() uint32 {
	if m != nil {
		return m.DeviceDmrid
	}
	return 0
}

func (m *RepeaterErrStatus) GetTmpErr() int32 {
	if m != nil {
		return m.TmpErr
	}
	return 0
}

func (m *RepeaterErrStatus) GetAntErr() int32 {
	if m != nil {
		return m.AntErr
	}
	return 0
}

func (m *RepeaterErrStatus) GetGpsErr() int32 {
	if m != nil {
		return m.GpsErr
	}
	return 0
}

func (m *RepeaterErrStatus) GetVolErr() int32 {
	if m != nil {
		return m.VolErr
	}
	return 0
}

func (m *RepeaterErrStatus) GetRxPllErr() int32 {
	if m != nil {
		return m.RxPllErr
	}
	return 0
}

func (m *RepeaterErrStatus) GetTxPllErr() int32 {
	if m != nil {
		return m.TxPllErr
	}
	return 0
}

func (m *RepeaterErrStatus) GetFanErr() int32 {
	if m != nil {
		return m.FanErr
	}
	return 0
}

func (m *RepeaterErrStatus) GetSignal() int32 {
	if m != nil {
		return m.Signal
	}
	return 0
}

// 手台上传命令，bcxx,cdxx等
type DeviceSend struct {
	// 发起方 中继设备ID
	RepeaterDmrid uint32 `protobuf:"fixed32,1,opt,name=repeater_dmrid,json=repeaterDmrid,proto3" json:"repeater_dmrid,omitempty"`
	// 手台上传的fsk 内容
	Fsk []byte `protobuf:"bytes,3,opt,name=fsk,proto3" json:"fsk,omitempty"`
	// 从机中继ID(针对同播控制器)
	SourceRepeaterDmrid uint32 `protobuf:"fixed32,4,opt,name=source_repeater_dmrid,json=sourceRepeaterDmrid,proto3" json:"source_repeater_dmrid,omitempty"`
}

func (m *DeviceSend) Reset()         { *m = DeviceSend{} }
func (m *DeviceSend) String() string { return proto.CompactTextString(m) }
func (*DeviceSend) ProtoMessage()    {}
func (*DeviceSend) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{6}
}
func (m *DeviceSend) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeviceSend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeviceSend.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeviceSend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeviceSend.Merge(m, src)
}
func (m *DeviceSend) XXX_Size() int {
	return m.Size()
}
func (m *DeviceSend) XXX_DiscardUnknown() {
	xxx_messageInfo_DeviceSend.DiscardUnknown(m)
}

var xxx_messageInfo_DeviceSend proto.InternalMessageInfo

func (m *DeviceSend) GetRepeaterDmrid() uint32 {
	if m != nil {
		return m.RepeaterDmrid
	}
	return 0
}

func (m *DeviceSend) GetFsk() []byte {
	if m != nil {
		return m.Fsk
	}
	return nil
}

func (m *DeviceSend) GetSourceRepeaterDmrid() uint32 {
	if m != nil {
		return m.SourceRepeaterDmrid
	}
	return 0
}

// 服务器下发的cbxx,dcxx等命令
type ServerSend struct {
	// 下发的fsk 内容
	Fsk []byte `protobuf:"bytes,3,opt,name=fsk,proto3" json:"fsk,omitempty"`
}

func (m *ServerSend) Reset()         { *m = ServerSend{} }
func (m *ServerSend) String() string { return proto.CompactTextString(m) }
func (*ServerSend) ProtoMessage()    {}
func (*ServerSend) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{7}
}
func (m *ServerSend) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServerSend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ServerSend.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ServerSend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerSend.Merge(m, src)
}
func (m *ServerSend) XXX_Size() int {
	return m.Size()
}
func (m *ServerSend) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerSend.DiscardUnknown(m)
}

var xxx_messageInfo_ServerSend proto.InternalMessageInfo

func (m *ServerSend) GetFsk() []byte {
	if m != nil {
		return m.Fsk
	}
	return nil
}

// rpc.cmd=71,72
// 手台申请话权
type Bc71 struct {
	// 发起方 中继设备ID
	RepeaterDmrid uint32 `protobuf:"fixed32,1,opt,name=repeater_dmrid,json=repeaterDmrid,proto3" json:"repeater_dmrid,omitempty"`
	// 目标id
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// 场强
	FieldIntensity int32 `protobuf:"varint,4,opt,name=field_intensity,json=fieldIntensity,proto3" json:"field_intensity,omitempty"`
	// 语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
	SupportDigital int32 `protobuf:"varint,5,opt,name=support_digital,json=supportDigital,proto3" json:"support_digital,omitempty"`
	// 语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
	SupportAnalog int32 `protobuf:"varint,6,opt,name=support_analog,json=supportAnalog,proto3" json:"support_analog,omitempty"`
	// 时隙 0：时隙1，　1：时隙2
	TimeSlotNo int32 `protobuf:"varint,7,opt,name=time_slot_no,json=timeSlotNo,proto3" json:"time_slot_no,omitempty"`
	// 通话优先级
	// 0：普通通话， 1：优先级1
	// 2: 优先级2，  3: 优先级3
	// 4: 紧急通话
	Priority int32 `protobuf:"varint,8,opt,name=priority,proto3" json:"priority,omitempty"`
	// 话音申请类型
	// 0:默认的正常手台语音申请
	// 1:手台打电话申请
	// 2:电话网关外线语音申请(被动)
	// 3:手台报警后的自动语音申请
	// 4:后台发监听,手台自动监听的语音申请
	// 5:电话网关收到手台请求的电话后的语音申请
	SoundType int32 `protobuf:"varint,9,opt,name=sound_type,json=soundType,proto3" json:"sound_type,omitempty"`
	// 电话号码,电话相关业务使用
	PhoneNo string `protobuf:"bytes,10,opt,name=phone_no,json=phoneNo,proto3" json:"phone_no,omitempty"`
	// 双工,半双工通话类型
	// 0: 半双工
	// 1: 全双工
	CallDuplex int32 `protobuf:"varint,14,opt,name=call_duplex,json=callDuplex,proto3" json:"call_duplex,omitempty"`
	// 从机中继ID(针对同播控制器)
	SourceRepeaterDmrid uint32 `protobuf:"fixed32,15,opt,name=source_repeater_dmrid,json=sourceRepeaterDmrid,proto3" json:"source_repeater_dmrid,omitempty"`
	// 如果涉及到抢占，控制器希望打断自己上面特定的会话时，可以通过此字段指定要打断的会话目标dmrid,默认不需要此功能
	// 虚拟集群里面，如果时隙已经占满，则希望抢占当前时隙的会话
	PreferInterruptTargetDmrid uint32 `protobuf:"fixed32,16,opt,name=prefer_interrupt_target_dmrid,json=preferInterruptTargetDmrid,proto3" json:"prefer_interrupt_target_dmrid,omitempty"`
}

func (m *Bc71) Reset()         { *m = Bc71{} }
func (m *Bc71) String() string { return proto.CompactTextString(m) }
func (*Bc71) ProtoMessage()    {}
func (*Bc71) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{8}
}
func (m *Bc71) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bc71) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bc71.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bc71) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bc71.Merge(m, src)
}
func (m *Bc71) XXX_Size() int {
	return m.Size()
}
func (m *Bc71) XXX_DiscardUnknown() {
	xxx_messageInfo_Bc71.DiscardUnknown(m)
}

var xxx_messageInfo_Bc71 proto.InternalMessageInfo

func (m *Bc71) GetRepeaterDmrid() uint32 {
	if m != nil {
		return m.RepeaterDmrid
	}
	return 0
}

func (m *Bc71) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *Bc71) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *Bc71) GetFieldIntensity() int32 {
	if m != nil {
		return m.FieldIntensity
	}
	return 0
}

func (m *Bc71) GetSupportDigital() int32 {
	if m != nil {
		return m.SupportDigital
	}
	return 0
}

func (m *Bc71) GetSupportAnalog() int32 {
	if m != nil {
		return m.SupportAnalog
	}
	return 0
}

func (m *Bc71) GetTimeSlotNo() int32 {
	if m != nil {
		return m.TimeSlotNo
	}
	return 0
}

func (m *Bc71) GetPriority() int32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *Bc71) GetSoundType() int32 {
	if m != nil {
		return m.SoundType
	}
	return 0
}

func (m *Bc71) GetPhoneNo() string {
	if m != nil {
		return m.PhoneNo
	}
	return ""
}

func (m *Bc71) GetCallDuplex() int32 {
	if m != nil {
		return m.CallDuplex
	}
	return 0
}

func (m *Bc71) GetSourceRepeaterDmrid() uint32 {
	if m != nil {
		return m.SourceRepeaterDmrid
	}
	return 0
}

func (m *Bc71) GetPreferInterruptTargetDmrid() uint32 {
	if m != nil {
		return m.PreferInterruptTargetDmrid
	}
	return 0
}

// rpc.cmd=171
// 申请话权回应
type Cb71 struct {
	// 发起方 中继设备ID
	RepeaterDmrid uint32 `protobuf:"fixed32,1,opt,name=repeater_dmrid,json=repeaterDmrid,proto3" json:"repeater_dmrid,omitempty"`
	// 目标id
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// 应答的请求指令号
	ReqNo int32 `protobuf:"varint,4,opt,name=req_no,json=reqNo,proto3" json:"req_no,omitempty"`
	// 语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
	SupportDigital int32 `protobuf:"varint,5,opt,name=support_digital,json=supportDigital,proto3" json:"support_digital,omitempty"`
	// 语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
	SupportAnalog int32 `protobuf:"varint,6,opt,name=support_analog,json=supportAnalog,proto3" json:"support_analog,omitempty"`
	// 时隙 0：时隙1，　1：时隙2
	TimeSlotNo int32 `protobuf:"varint,7,opt,name=time_slot_no,json=timeSlotNo,proto3" json:"time_slot_no,omitempty"`
	// 通话优先级
	Priority int32 `protobuf:"varint,8,opt,name=priority,proto3" json:"priority,omitempty"`
	// 应答结果
	// 0x00：时隙占有，准备接收语音呼叫
	// 0x01：允许联网呼叫
	// 0x02：允许呼叫，中继与服务器断开
	// 0x03: 手台电话申请成功
	// 0x10: 组呼并入成功，集群模式下才有
	// 0x80=128：拒绝呼叫，对方不在线
	// 0x81=129：拒绝呼叫，对方在通话中
	// 0x82=130：拒绝呼叫，中继信道忙
	// 0x83=131：拒绝呼叫，被优先级更高手台抢占时隙资源
	// 0x84=132: 拒绝呼叫，当前有更高优先级手台在通话中
	// 0x85=133: 拒绝呼叫,后台已经释放了此手台的通话资源
	// 0x86=134: 未登录,请先登录
	// 0x87=135: 无电话网关可用,电话申请失败
	// 0x88=136: 电话网关忙,电话申请失败
	// 0x89=137: 电话黑名单,电话申请失败
	// 0x8A=138: 系统授权已经过期，呼叫失败
	// 0x90=144:
	// 0x91=145: 后台数据库查询错误
	// 0x92=146: 电话号码错误
	// 0x93=147: 控制台登录号码和申请话权号码不一致,申请失败
	// 0x94=148: 拒绝呼叫，临时组或任务组已失效
	// 0xA0=160: 拒绝呼叫, 归属组无其它成员在线，集群模式下才有
	// 0xA1=161: 拒绝呼叫, 手台所属中继错误，集群模式下才有
	// 0xA2=162: 拒绝呼叫, 手台所属中继错误，集群模式下才有
	Result int32 `protobuf:"varint,9,opt,name=result,proto3" json:"result,omitempty"`
	// 被更高优先级抢了语音的设备dmrid
	// 当一个人A在通话中,但是有更高优先级的用户B发了bc71,
	// 则会先给A发cb71.result=0x83,interrupt_dmrid=A的dmrid
	// 然后给B发cb71.result=0x01
	InterruptDmrid uint32 `protobuf:"fixed32,10,opt,name=interrupt_dmrid,json=interruptDmrid,proto3" json:"interrupt_dmrid,omitempty"`
	// 话音申请类型
	// 0:默认的正常手台语音申请
	// 1:手台打电话申请
	// 2:电话网关外线语音申请(被动)
	// 3:手台报警后的自动语音申请
	// 4:后台发监听,手台自动监听的语音申请
	// 5:电话网关收到手台请求的电话后的语音申请
	SoundType int32 `protobuf:"varint,11,opt,name=sound_type,json=soundType,proto3" json:"sound_type,omitempty"`
	// 电话号码,电话相关业务使用
	PhoneNo string `protobuf:"bytes,12,opt,name=phone_no,json=phoneNo,proto3" json:"phone_no,omitempty"`
	// 双工,半双工通话类型
	// 0: 半双工
	// 1: 全双工
	CallDuplex int32 `protobuf:"varint,13,opt,name=call_duplex,json=callDuplex,proto3" json:"call_duplex,omitempty"`
	// 从机中继ID(针对同播控制器)
	SourceRepeaterDmrid uint32 `protobuf:"fixed32,14,opt,name=source_repeater_dmrid,json=sourceRepeaterDmrid,proto3" json:"source_repeater_dmrid,omitempty"`
}

func (m *Cb71) Reset()         { *m = Cb71{} }
func (m *Cb71) String() string { return proto.CompactTextString(m) }
func (*Cb71) ProtoMessage()    {}
func (*Cb71) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{9}
}
func (m *Cb71) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Cb71) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Cb71.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Cb71) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Cb71.Merge(m, src)
}
func (m *Cb71) XXX_Size() int {
	return m.Size()
}
func (m *Cb71) XXX_DiscardUnknown() {
	xxx_messageInfo_Cb71.DiscardUnknown(m)
}

var xxx_messageInfo_Cb71 proto.InternalMessageInfo

func (m *Cb71) GetRepeaterDmrid() uint32 {
	if m != nil {
		return m.RepeaterDmrid
	}
	return 0
}

func (m *Cb71) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *Cb71) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *Cb71) GetReqNo() int32 {
	if m != nil {
		return m.ReqNo
	}
	return 0
}

func (m *Cb71) GetSupportDigital() int32 {
	if m != nil {
		return m.SupportDigital
	}
	return 0
}

func (m *Cb71) GetSupportAnalog() int32 {
	if m != nil {
		return m.SupportAnalog
	}
	return 0
}

func (m *Cb71) GetTimeSlotNo() int32 {
	if m != nil {
		return m.TimeSlotNo
	}
	return 0
}

func (m *Cb71) GetPriority() int32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *Cb71) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *Cb71) GetInterruptDmrid() uint32 {
	if m != nil {
		return m.InterruptDmrid
	}
	return 0
}

func (m *Cb71) GetSoundType() int32 {
	if m != nil {
		return m.SoundType
	}
	return 0
}

func (m *Cb71) GetPhoneNo() string {
	if m != nil {
		return m.PhoneNo
	}
	return ""
}

func (m *Cb71) GetCallDuplex() int32 {
	if m != nil {
		return m.CallDuplex
	}
	return 0
}

func (m *Cb71) GetSourceRepeaterDmrid() uint32 {
	if m != nil {
		return m.SourceRepeaterDmrid
	}
	return 0
}

// rpc.cmd=73
// 抢断广播命令
type Bc73 struct {
	// 发起方 中继设备ID
	RepeaterDmrid uint32 `protobuf:"fixed32,1,opt,name=repeater_dmrid,json=repeaterDmrid,proto3" json:"repeater_dmrid,omitempty"`
	// 目标id,被打断语音的发起人
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// 时隙 0：时隙1，　1：时隙2
	TimeSlotNo int32 `protobuf:"varint,4,opt,name=time_slot_no,json=timeSlotNo,proto3" json:"time_slot_no,omitempty"`
	// 从机中继ID(针对同播控制器)
	SourceRepeaterDmrid uint32 `protobuf:"fixed32,5,opt,name=source_repeater_dmrid,json=sourceRepeaterDmrid,proto3" json:"source_repeater_dmrid,omitempty"`
}

func (m *Bc73) Reset()         { *m = Bc73{} }
func (m *Bc73) String() string { return proto.CompactTextString(m) }
func (*Bc73) ProtoMessage()    {}
func (*Bc73) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{10}
}
func (m *Bc73) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bc73) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bc73.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bc73) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bc73.Merge(m, src)
}
func (m *Bc73) XXX_Size() int {
	return m.Size()
}
func (m *Bc73) XXX_DiscardUnknown() {
	xxx_messageInfo_Bc73.DiscardUnknown(m)
}

var xxx_messageInfo_Bc73 proto.InternalMessageInfo

func (m *Bc73) GetRepeaterDmrid() uint32 {
	if m != nil {
		return m.RepeaterDmrid
	}
	return 0
}

func (m *Bc73) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *Bc73) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *Bc73) GetTimeSlotNo() int32 {
	if m != nil {
		return m.TimeSlotNo
	}
	return 0
}

func (m *Bc73) GetSourceRepeaterDmrid() uint32 {
	if m != nil {
		return m.SourceRepeaterDmrid
	}
	return 0
}

// rpc.cmd=175
// 后台通知会话需要立即结束
// 不管是发起方还是接收方,此会话都必须立即结束
type Cb75 struct {
	// 会话目标id
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 会话源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// 会话结束原因
	// 0:会话正在被销毁,原因未知
	// 1:被抢占
	// 2:开始后指定时间内无语音
	// 3:语音会话中无语音超时
	// 4:会话已经销毁
	// 5:BC15没抢到话权
	Reason int32 `protobuf:"varint,5,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (m *Cb75) Reset()         { *m = Cb75{} }
func (m *Cb75) String() string { return proto.CompactTextString(m) }
func (*Cb75) ProtoMessage()    {}
func (*Cb75) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{11}
}
func (m *Cb75) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Cb75) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Cb75.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Cb75) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Cb75.Merge(m, src)
}
func (m *Cb75) XXX_Size() int {
	return m.Size()
}
func (m *Cb75) XXX_DiscardUnknown() {
	xxx_messageInfo_Cb75.DiscardUnknown(m)
}

var xxx_messageInfo_Cb75 proto.InternalMessageInfo

func (m *Cb75) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *Cb75) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *Cb75) GetReason() int32 {
	if m != nil {
		return m.Reason
	}
	return 0
}

// rpc.cmd=15
type Bc15 struct {
	// 发起方 中继设备ID
	RepeaterDmrid uint32 `protobuf:"fixed32,1,opt,name=repeater_dmrid,json=repeaterDmrid,proto3" json:"repeater_dmrid,omitempty"`
	// 目标id
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// 场强
	FieldIntensity int32 `protobuf:"varint,4,opt,name=field_intensity,json=fieldIntensity,proto3" json:"field_intensity,omitempty"`
	// 语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
	SupportDigital int32 `protobuf:"varint,5,opt,name=support_digital,json=supportDigital,proto3" json:"support_digital,omitempty"`
	// 语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
	SupportAnalog int32 `protobuf:"varint,6,opt,name=support_analog,json=supportAnalog,proto3" json:"support_analog,omitempty"`
	// 时隙 0：时隙1，　1：时隙2
	TimeSlotNo int32 `protobuf:"varint,7,opt,name=time_slot_no,json=timeSlotNo,proto3" json:"time_slot_no,omitempty"`
	// 通话类型
	// 0：本地通话, 1：联网通话
	CallType int32 `protobuf:"varint,9,opt,name=call_type,json=callType,proto3" json:"call_type,omitempty"`
	// 通话优先级
	// 0：普通通话， 1：优先级1
	// 2: 优先级2，  3: 优先级3
	// 4: 紧急通话
	Priority int32 `protobuf:"varint,8,opt,name=priority,proto3" json:"priority,omitempty"`
	// 优先打断标志
	// 0：不支持优先打断，1支持优先打断
	SupportInterrupt int32 `protobuf:"varint,10,opt,name=support_interrupt,json=supportInterrupt,proto3" json:"support_interrupt,omitempty"`
	// Call Status，通话状态
	// 0：语音结束, 1：语音开始
	CallStatus int32 `protobuf:"varint,11,opt,name=call_status,json=callStatus,proto3" json:"call_status,omitempty"`
	// 话音类型
	// 0:默认的正常手台语音申请
	// 1:手台电话会话语音
	// 2:电话网关过来的开始结束指令
	SoundType int32 `protobuf:"varint,12,opt,name=sound_type,json=soundType,proto3" json:"sound_type,omitempty"`
	// 电话号码,电话相关业务使用,手台上来的bc15是没有此字段的
	PhoneNo string `protobuf:"bytes,13,opt,name=phone_no,json=phoneNo,proto3" json:"phone_no,omitempty"`
	// 通话类型
	// 0: 半双工
	// 1: 全双工
	CallDuplex int32 `protobuf:"varint,14,opt,name=call_duplex,json=callDuplex,proto3" json:"call_duplex,omitempty"`
	// 从机中继ID(针对同播控制器)
	SourceRepeaterDmrid uint32 `protobuf:"fixed32,15,opt,name=source_repeater_dmrid,json=sourceRepeaterDmrid,proto3" json:"source_repeater_dmrid,omitempty"`
	// 通话开始时间，服务器填写,目前只有app会用到
	// Unix time, the number of seconds elapsed since January 1, 1970 UTC
	StartTime uint64 `protobuf:"fixed64,16,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
}

func (m *Bc15) Reset()         { *m = Bc15{} }
func (m *Bc15) String() string { return proto.CompactTextString(m) }
func (*Bc15) ProtoMessage()    {}
func (*Bc15) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{12}
}
func (m *Bc15) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bc15) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bc15.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bc15) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bc15.Merge(m, src)
}
func (m *Bc15) XXX_Size() int {
	return m.Size()
}
func (m *Bc15) XXX_DiscardUnknown() {
	xxx_messageInfo_Bc15.DiscardUnknown(m)
}

var xxx_messageInfo_Bc15 proto.InternalMessageInfo

func (m *Bc15) GetRepeaterDmrid() uint32 {
	if m != nil {
		return m.RepeaterDmrid
	}
	return 0
}

func (m *Bc15) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *Bc15) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *Bc15) GetFieldIntensity() int32 {
	if m != nil {
		return m.FieldIntensity
	}
	return 0
}

func (m *Bc15) GetSupportDigital() int32 {
	if m != nil {
		return m.SupportDigital
	}
	return 0
}

func (m *Bc15) GetSupportAnalog() int32 {
	if m != nil {
		return m.SupportAnalog
	}
	return 0
}

func (m *Bc15) GetTimeSlotNo() int32 {
	if m != nil {
		return m.TimeSlotNo
	}
	return 0
}

func (m *Bc15) GetCallType() int32 {
	if m != nil {
		return m.CallType
	}
	return 0
}

func (m *Bc15) GetPriority() int32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *Bc15) GetSupportInterrupt() int32 {
	if m != nil {
		return m.SupportInterrupt
	}
	return 0
}

func (m *Bc15) GetCallStatus() int32 {
	if m != nil {
		return m.CallStatus
	}
	return 0
}

func (m *Bc15) GetSoundType() int32 {
	if m != nil {
		return m.SoundType
	}
	return 0
}

func (m *Bc15) GetPhoneNo() string {
	if m != nil {
		return m.PhoneNo
	}
	return ""
}

func (m *Bc15) GetCallDuplex() int32 {
	if m != nil {
		return m.CallDuplex
	}
	return 0
}

func (m *Bc15) GetSourceRepeaterDmrid() uint32 {
	if m != nil {
		return m.SourceRepeaterDmrid
	}
	return 0
}

func (m *Bc15) GetStartTime() uint64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

// rpc.cmd=10
type Bc10 struct {
	// 目标id
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// 通话类型
	// 0：本地通话, 1：联网通话
	CallType int32 `protobuf:"varint,5,opt,name=call_type,json=callType,proto3" json:"call_type,omitempty"`
	// 通话优先级
	// 0：普通通话， 1：优先级1
	// 2: 优先级2，  3: 优先级3
	// 4: 紧急通话
	Priority int32 `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	// 优先打断标志
	// 0：不支持优先打断，1支持优先打断
	SupportInterrupt int32 `protobuf:"varint,7,opt,name=support_interrupt,json=supportInterrupt,proto3" json:"support_interrupt,omitempty"`
	// Pld Seq,语音数据帧序列号
	FrameNo uint32 `protobuf:"fixed32,8,opt,name=frame_no,json=frameNo,proto3" json:"frame_no,omitempty"`
	// 60ms语音帧数据, 一般为60ms发送一次，只有opus_data_1有效
	OpusData_1 []byte `protobuf:"bytes,9,opt,name=opus_data_1,json=opusData1,proto3" json:"opus_data_1,omitempty"`
	// 网络不好时，可以一次发两个60ms的语音帧数据，opus_data_1和opus_data_2可以同时存在
	OpusData_2 []byte `protobuf:"bytes,10,opt,name=opus_data_2,json=opusData2,proto3" json:"opus_data_2,omitempty"`
}

func (m *Bc10) Reset()         { *m = Bc10{} }
func (m *Bc10) String() string { return proto.CompactTextString(m) }
func (*Bc10) ProtoMessage()    {}
func (*Bc10) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{13}
}
func (m *Bc10) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bc10) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bc10.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bc10) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bc10.Merge(m, src)
}
func (m *Bc10) XXX_Size() int {
	return m.Size()
}
func (m *Bc10) XXX_DiscardUnknown() {
	xxx_messageInfo_Bc10.DiscardUnknown(m)
}

var xxx_messageInfo_Bc10 proto.InternalMessageInfo

func (m *Bc10) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *Bc10) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *Bc10) GetCallType() int32 {
	if m != nil {
		return m.CallType
	}
	return 0
}

func (m *Bc10) GetPriority() int32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *Bc10) GetSupportInterrupt() int32 {
	if m != nil {
		return m.SupportInterrupt
	}
	return 0
}

func (m *Bc10) GetFrameNo() uint32 {
	if m != nil {
		return m.FrameNo
	}
	return 0
}

func (m *Bc10) GetOpusData_1() []byte {
	if m != nil {
		return m.OpusData_1
	}
	return nil
}

func (m *Bc10) GetOpusData_2() []byte {
	if m != nil {
		return m.OpusData_2
	}
	return nil
}

// rpc.cmd=30
type Bc30 struct {
	// 发起方 中继设备ID
	RepeaterDmrid uint32 `protobuf:"fixed32,1,opt,name=repeater_dmrid,json=repeaterDmrid,proto3" json:"repeater_dmrid,omitempty"`
	// 目标id
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// 时隙 0：时隙1，　1：时隙2
	TimeSlotNo int32 `protobuf:"varint,4,opt,name=time_slot_no,json=timeSlotNo,proto3" json:"time_slot_no,omitempty"`
	// 通话类型
	// 0：本地通话, 1：联网通话
	CallType int32 `protobuf:"varint,5,opt,name=call_type,json=callType,proto3" json:"call_type,omitempty"`
	// 通话优先级
	// 0：普通通话， 1：优先级1
	// 2: 优先级2，  3: 优先级3
	// 4: 紧急通话
	Priority int32 `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	// 优先打断标志
	// 0：不支持优先打断，1支持优先打断
	SupportInterrupt int32 `protobuf:"varint,7,opt,name=support_interrupt,json=supportInterrupt,proto3" json:"support_interrupt,omitempty"`
	// Pld Seq,语音数据帧序列号
	FrameNo uint32 `protobuf:"fixed32,8,opt,name=frame_no,json=frameNo,proto3" json:"frame_no,omitempty"`
	// 语音帧数据
	AmbeData []byte `protobuf:"bytes,9,opt,name=ambe_data,json=ambeData,proto3" json:"ambe_data,omitempty"`
	// 语音类型
	// 0:手台产生的语音
	// 2:电话网关送过来的语音
	SoundType int32 `protobuf:"varint,10,opt,name=sound_type,json=soundType,proto3" json:"sound_type,omitempty"`
	// 从机中继ID(针对同播控制器)
	SourceRepeaterDmrid uint32 `protobuf:"fixed32,11,opt,name=source_repeater_dmrid,json=sourceRepeaterDmrid,proto3" json:"source_repeater_dmrid,omitempty"`
}

func (m *Bc30) Reset()         { *m = Bc30{} }
func (m *Bc30) String() string { return proto.CompactTextString(m) }
func (*Bc30) ProtoMessage()    {}
func (*Bc30) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{14}
}
func (m *Bc30) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bc30) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bc30.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bc30) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bc30.Merge(m, src)
}
func (m *Bc30) XXX_Size() int {
	return m.Size()
}
func (m *Bc30) XXX_DiscardUnknown() {
	xxx_messageInfo_Bc30.DiscardUnknown(m)
}

var xxx_messageInfo_Bc30 proto.InternalMessageInfo

func (m *Bc30) GetRepeaterDmrid() uint32 {
	if m != nil {
		return m.RepeaterDmrid
	}
	return 0
}

func (m *Bc30) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *Bc30) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *Bc30) GetTimeSlotNo() int32 {
	if m != nil {
		return m.TimeSlotNo
	}
	return 0
}

func (m *Bc30) GetCallType() int32 {
	if m != nil {
		return m.CallType
	}
	return 0
}

func (m *Bc30) GetPriority() int32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *Bc30) GetSupportInterrupt() int32 {
	if m != nil {
		return m.SupportInterrupt
	}
	return 0
}

func (m *Bc30) GetFrameNo() uint32 {
	if m != nil {
		return m.FrameNo
	}
	return 0
}

func (m *Bc30) GetAmbeData() []byte {
	if m != nil {
		return m.AmbeData
	}
	return nil
}

func (m *Bc30) GetSoundType() int32 {
	if m != nil {
		return m.SoundType
	}
	return 0
}

func (m *Bc30) GetSourceRepeaterDmrid() uint32 {
	if m != nil {
		return m.SourceRepeaterDmrid
	}
	return 0
}

// rpc.cmd=33
// 手台输入dtmf
type Dtmf struct {
	// 发起方 中继设备ID
	RepeaterDmrid uint32 `protobuf:"fixed32,1,opt,name=repeater_dmrid,json=repeaterDmrid,proto3" json:"repeater_dmrid,omitempty"`
	// 目标id
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// dtmf 字符串
	DtmfStr string `protobuf:"bytes,6,opt,name=dtmf_str,json=dtmfStr,proto3" json:"dtmf_str,omitempty"`
	// 从机中继ID(针对同播控制器)
	SourceRepeaterDmrid uint32 `protobuf:"fixed32,7,opt,name=source_repeater_dmrid,json=sourceRepeaterDmrid,proto3" json:"source_repeater_dmrid,omitempty"`
}

func (m *Dtmf) Reset()         { *m = Dtmf{} }
func (m *Dtmf) String() string { return proto.CompactTextString(m) }
func (*Dtmf) ProtoMessage()    {}
func (*Dtmf) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{15}
}
func (m *Dtmf) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Dtmf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Dtmf.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Dtmf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Dtmf.Merge(m, src)
}
func (m *Dtmf) XXX_Size() int {
	return m.Size()
}
func (m *Dtmf) XXX_DiscardUnknown() {
	xxx_messageInfo_Dtmf.DiscardUnknown(m)
}

var xxx_messageInfo_Dtmf proto.InternalMessageInfo

func (m *Dtmf) GetRepeaterDmrid() uint32 {
	if m != nil {
		return m.RepeaterDmrid
	}
	return 0
}

func (m *Dtmf) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *Dtmf) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *Dtmf) GetDtmfStr() string {
	if m != nil {
		return m.DtmfStr
	}
	return ""
}

func (m *Dtmf) GetSourceRepeaterDmrid() uint32 {
	if m != nil {
		return m.SourceRepeaterDmrid
	}
	return 0
}

// rpc.cmd=34
// 结束电话通话
type EndCall struct {
	// 发起方 中继设备ID
	RepeaterDmrid uint32 `protobuf:"fixed32,1,opt,name=repeater_dmrid,json=repeaterDmrid,proto3" json:"repeater_dmrid,omitempty"`
	// 目标id
	TargetDmrid uint32 `protobuf:"fixed32,2,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	// 源id
	SourceDmrid uint32 `protobuf:"fixed32,3,opt,name=source_dmrid,json=sourceDmrid,proto3" json:"source_dmrid,omitempty"`
	// 话音类型
	// 1:手台电话会话过来的
	// 2:电话网关过来的
	SoundType int32 `protobuf:"varint,12,opt,name=sound_type,json=soundType,proto3" json:"sound_type,omitempty"`
	// 电话号码,电话相关业务使用
	PhoneNo string `protobuf:"bytes,13,opt,name=phone_no,json=phoneNo,proto3" json:"phone_no,omitempty"`
	// 从机中继ID(针对同播控制器)
	SourceRepeaterDmrid uint32 `protobuf:"fixed32,14,opt,name=source_repeater_dmrid,json=sourceRepeaterDmrid,proto3" json:"source_repeater_dmrid,omitempty"`
}

func (m *EndCall) Reset()         { *m = EndCall{} }
func (m *EndCall) String() string { return proto.CompactTextString(m) }
func (*EndCall) ProtoMessage()    {}
func (*EndCall) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{16}
}
func (m *EndCall) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EndCall) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EndCall.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EndCall) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndCall.Merge(m, src)
}
func (m *EndCall) XXX_Size() int {
	return m.Size()
}
func (m *EndCall) XXX_DiscardUnknown() {
	xxx_messageInfo_EndCall.DiscardUnknown(m)
}

var xxx_messageInfo_EndCall proto.InternalMessageInfo

func (m *EndCall) GetRepeaterDmrid() uint32 {
	if m != nil {
		return m.RepeaterDmrid
	}
	return 0
}

func (m *EndCall) GetTargetDmrid() uint32 {
	if m != nil {
		return m.TargetDmrid
	}
	return 0
}

func (m *EndCall) GetSourceDmrid() uint32 {
	if m != nil {
		return m.SourceDmrid
	}
	return 0
}

func (m *EndCall) GetSoundType() int32 {
	if m != nil {
		return m.SoundType
	}
	return 0
}

func (m *EndCall) GetPhoneNo() string {
	if m != nil {
		return m.PhoneNo
	}
	return ""
}

func (m *EndCall) GetSourceRepeaterDmrid() uint32 {
	if m != nil {
		return m.SourceRepeaterDmrid
	}
	return 0
}

// rpc.cmd=35
// 电话转接命令,发送给电话网关
type PhoneTransfer struct {
	// 电话的dmrid(当前的讲话者)
	PhoneDmrid uint32 `protobuf:"fixed32,2,opt,name=phone_dmrid,json=phoneDmrid,proto3" json:"phone_dmrid,omitempty"`
	// 当前的讲话目标
	NowTarget uint32 `protobuf:"fixed32,3,opt,name=now_target,json=nowTarget,proto3" json:"now_target,omitempty"`
	// 要转接到的目标dmrid
	TransferTarget uint32 `protobuf:"fixed32,4,opt,name=transfer_target,json=transferTarget,proto3" json:"transfer_target,omitempty"`
}

func (m *PhoneTransfer) Reset()         { *m = PhoneTransfer{} }
func (m *PhoneTransfer) String() string { return proto.CompactTextString(m) }
func (*PhoneTransfer) ProtoMessage()    {}
func (*PhoneTransfer) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{17}
}
func (m *PhoneTransfer) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PhoneTransfer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PhoneTransfer.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PhoneTransfer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhoneTransfer.Merge(m, src)
}
func (m *PhoneTransfer) XXX_Size() int {
	return m.Size()
}
func (m *PhoneTransfer) XXX_DiscardUnknown() {
	xxx_messageInfo_PhoneTransfer.DiscardUnknown(m)
}

var xxx_messageInfo_PhoneTransfer proto.InternalMessageInfo

func (m *PhoneTransfer) GetPhoneDmrid() uint32 {
	if m != nil {
		return m.PhoneDmrid
	}
	return 0
}

func (m *PhoneTransfer) GetNowTarget() uint32 {
	if m != nil {
		return m.NowTarget
	}
	return 0
}

func (m *PhoneTransfer) GetTransferTarget() uint32 {
	if m != nil {
		return m.TransferTarget
	}
	return 0
}

// 手台频道数据
type OneChannelItem struct {
	// 频道号
	No int32 `protobuf:"varint,1,opt,name=no,proto3" json:"no,omitempty"`
	// 默认发射组dmrid
	SendGroup string `protobuf:"bytes,2,opt,name=sendGroup,proto3" json:"sendGroup,omitempty"`
	// 接收组dmrid列表
	ListenGroup []string `protobuf:"bytes,3,rep,name=listenGroup,proto3" json:"listenGroup,omitempty"`
	// 频道所在区域rid
	ZoneRid string `protobuf:"bytes,4,opt,name=zoneRid,proto3" json:"zoneRid,omitempty"`
}

func (m *OneChannelItem) Reset()         { *m = OneChannelItem{} }
func (m *OneChannelItem) String() string { return proto.CompactTextString(m) }
func (*OneChannelItem) ProtoMessage()    {}
func (*OneChannelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{18}
}
func (m *OneChannelItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OneChannelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OneChannelItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OneChannelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneChannelItem.Merge(m, src)
}
func (m *OneChannelItem) XXX_Size() int {
	return m.Size()
}
func (m *OneChannelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_OneChannelItem.DiscardUnknown(m)
}

var xxx_messageInfo_OneChannelItem proto.InternalMessageInfo

func (m *OneChannelItem) GetNo() int32 {
	if m != nil {
		return m.No
	}
	return 0
}

func (m *OneChannelItem) GetSendGroup() string {
	if m != nil {
		return m.SendGroup
	}
	return ""
}

func (m *OneChannelItem) GetListenGroup() []string {
	if m != nil {
		return m.ListenGroup
	}
	return nil
}

func (m *OneChannelItem) GetZoneRid() string {
	if m != nil {
		return m.ZoneRid
	}
	return ""
}

// 手台的频道相关数据
// rpc.cmd=300
type Channels struct {
	Channels       []*OneChannelItem `protobuf:"bytes,1,rep,name=channels,proto3" json:"channels,omitempty"`
	DeviceDmrid    string            `protobuf:"bytes,2,opt,name=device_dmrid,json=deviceDmrid,proto3" json:"device_dmrid,omitempty"`
	DevicePriority int32             `protobuf:"varint,3,opt,name=device_priority,json=devicePriority,proto3" json:"device_priority,omitempty"`
}

func (m *Channels) Reset()         { *m = Channels{} }
func (m *Channels) String() string { return proto.CompactTextString(m) }
func (*Channels) ProtoMessage()    {}
func (*Channels) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{19}
}
func (m *Channels) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Channels) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Channels.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Channels) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Channels.Merge(m, src)
}
func (m *Channels) XXX_Size() int {
	return m.Size()
}
func (m *Channels) XXX_DiscardUnknown() {
	xxx_messageInfo_Channels.DiscardUnknown(m)
}

var xxx_messageInfo_Channels proto.InternalMessageInfo

func (m *Channels) GetChannels() []*OneChannelItem {
	if m != nil {
		return m.Channels
	}
	return nil
}

func (m *Channels) GetDeviceDmrid() string {
	if m != nil {
		return m.DeviceDmrid
	}
	return ""
}

func (m *Channels) GetDevicePriority() int32 {
	if m != nil {
		return m.DevicePriority
	}
	return 0
}

// rpc.cmd=201
// 电话网关向后台查询短号对应的dmrid
type PhoneAdapterShortNo2DmridReq struct {
	ShortNo string `protobuf:"bytes,1,opt,name=short_no,json=shortNo,proto3" json:"short_no,omitempty"`
	Opt     int32  `protobuf:"varint,2,opt,name=opt,proto3" json:"opt,omitempty"`
	Dmrid   uint32 `protobuf:"fixed32,3,opt,name=dmrid,proto3" json:"dmrid,omitempty"`
}

func (m *PhoneAdapterShortNo2DmridReq) Reset()         { *m = PhoneAdapterShortNo2DmridReq{} }
func (m *PhoneAdapterShortNo2DmridReq) String() string { return proto.CompactTextString(m) }
func (*PhoneAdapterShortNo2DmridReq) ProtoMessage()    {}
func (*PhoneAdapterShortNo2DmridReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{20}
}
func (m *PhoneAdapterShortNo2DmridReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PhoneAdapterShortNo2DmridReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PhoneAdapterShortNo2DmridReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PhoneAdapterShortNo2DmridReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhoneAdapterShortNo2DmridReq.Merge(m, src)
}
func (m *PhoneAdapterShortNo2DmridReq) XXX_Size() int {
	return m.Size()
}
func (m *PhoneAdapterShortNo2DmridReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PhoneAdapterShortNo2DmridReq.DiscardUnknown(m)
}

var xxx_messageInfo_PhoneAdapterShortNo2DmridReq proto.InternalMessageInfo

func (m *PhoneAdapterShortNo2DmridReq) GetShortNo() string {
	if m != nil {
		return m.ShortNo
	}
	return ""
}

func (m *PhoneAdapterShortNo2DmridReq) GetOpt() int32 {
	if m != nil {
		return m.Opt
	}
	return 0
}

func (m *PhoneAdapterShortNo2DmridReq) GetDmrid() uint32 {
	if m != nil {
		return m.Dmrid
	}
	return 0
}

// 后台回应电话网关查询短号
type PhoneAdapterShortNo2DmridRes struct {
	// 结果码
	// 0:成功获得对应dmrid
	// 1:数据库没有此短号
	// 2:数据库出错,无法查询
	// 3:marshal/unmarshal error
	Result int32  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	Dmrid  uint32 `protobuf:"fixed32,2,opt,name=dmrid,proto3" json:"dmrid,omitempty"`
	// err info
	Info string `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
}

func (m *PhoneAdapterShortNo2DmridRes) Reset()         { *m = PhoneAdapterShortNo2DmridRes{} }
func (m *PhoneAdapterShortNo2DmridRes) String() string { return proto.CompactTextString(m) }
func (*PhoneAdapterShortNo2DmridRes) ProtoMessage()    {}
func (*PhoneAdapterShortNo2DmridRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{21}
}
func (m *PhoneAdapterShortNo2DmridRes) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PhoneAdapterShortNo2DmridRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PhoneAdapterShortNo2DmridRes.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PhoneAdapterShortNo2DmridRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhoneAdapterShortNo2DmridRes.Merge(m, src)
}
func (m *PhoneAdapterShortNo2DmridRes) XXX_Size() int {
	return m.Size()
}
func (m *PhoneAdapterShortNo2DmridRes) XXX_DiscardUnknown() {
	xxx_messageInfo_PhoneAdapterShortNo2DmridRes.DiscardUnknown(m)
}

var xxx_messageInfo_PhoneAdapterShortNo2DmridRes proto.InternalMessageInfo

func (m *PhoneAdapterShortNo2DmridRes) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *PhoneAdapterShortNo2DmridRes) GetDmrid() uint32 {
	if m != nil {
		return m.Dmrid
	}
	return 0
}

func (m *PhoneAdapterShortNo2DmridRes) GetInfo() string {
	if m != nil {
		return m.Info
	}
	return ""
}

// rpc.cmd=205
// 查询与返回电话网关中电话配置信息
type PhoneLineSetting struct {
	// 动作码
	// 1: 网关查询电话配置
	// 2: 后台回应配置信息
	// <0:后台出错了
	ActionCode int32 `protobuf:"varint,1,opt,name=action_code,json=actionCode,proto3" json:"action_code,omitempty"`
	// 以后三个为电话线配置信息,数组一一对应
	// 电话线位置,
	LinePos []int32 `protobuf:"varint,2,rep,packed,name=line_pos,json=linePos,proto3" json:"line_pos,omitempty"`
	// 电话号码
	LinePhoneNo []string `protobuf:"bytes,3,rep,name=line_phone_no,json=linePhoneNo,proto3" json:"line_phone_no,omitempty"`
	// 电话对应的dmrid
	LineDmrid []uint32 `protobuf:"fixed32,4,rep,packed,name=line_dmrid,json=lineDmrid,proto3" json:"line_dmrid,omitempty"`
}

func (m *PhoneLineSetting) Reset()         { *m = PhoneLineSetting{} }
func (m *PhoneLineSetting) String() string { return proto.CompactTextString(m) }
func (*PhoneLineSetting) ProtoMessage()    {}
func (*PhoneLineSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{22}
}
func (m *PhoneLineSetting) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PhoneLineSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PhoneLineSetting.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PhoneLineSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhoneLineSetting.Merge(m, src)
}
func (m *PhoneLineSetting) XXX_Size() int {
	return m.Size()
}
func (m *PhoneLineSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_PhoneLineSetting.DiscardUnknown(m)
}

var xxx_messageInfo_PhoneLineSetting proto.InternalMessageInfo

func (m *PhoneLineSetting) GetActionCode() int32 {
	if m != nil {
		return m.ActionCode
	}
	return 0
}

func (m *PhoneLineSetting) GetLinePos() []int32 {
	if m != nil {
		return m.LinePos
	}
	return nil
}

func (m *PhoneLineSetting) GetLinePhoneNo() []string {
	if m != nil {
		return m.LinePhoneNo
	}
	return nil
}

func (m *PhoneLineSetting) GetLineDmrid() []uint32 {
	if m != nil {
		return m.LineDmrid
	}
	return nil
}

// rpc.cmd=320
// 网关终端请求群组(收听组)数据回应
type ExOneOrg struct {
	// @table uuid primary key
	// 行ID
	Rid string `protobuf:"bytes,1,opt,name=rid,proto3" json:"rid,omitempty"`
	// @table varchar(64) unique not null
	// 组织机构编号
	OrgSelfId string `protobuf:"bytes,3,opt,name=org_self_id,json=orgSelfId,proto3" json:"org_self_id,omitempty"`
	// @table varchar(32) unique not null
	// 机构名称,缩写
	OrgShortName string `protobuf:"bytes,5,opt,name=org_short_name,json=orgShortName,proto3" json:"org_short_name,omitempty"`
	// @table varchar(256)
	// 机构名称,全称
	OrgFullName string `protobuf:"bytes,6,opt,name=org_full_name,json=orgFullName,proto3" json:"org_full_name,omitempty"`
	// @table text
	// 机构详细描述
	Note string `protobuf:"bytes,7,opt,name=note,proto3" json:"note,omitempty"`
	// @table int default 2
	// 2:真实机构,1:虚拟机构 虚拟机构没有真实设备和用户,只是引用真实机构的数据,相当于额外的分组
	OrgIsVirtual int32 `protobuf:"varint,8,opt,name=org_is_virtual,json=orgIsVirtual,proto3" json:"org_is_virtual,omitempty"`
	// @table varchar(8) unique
	// DMR ID,可用作组呼的ID
	DmrId string `protobuf:"bytes,9,opt,name=dmr_id,json=dmrId,proto3" json:"dmr_id,omitempty"`
	// @table uuid not null default '11111111-1111-1111-1111-111111111111'
	// 此组织的上级机构
	ParentOrgId string `protobuf:"bytes,11,opt,name=parent_org_id,json=parentOrgId,proto3" json:"parent_org_id,omitempty"`
}

func (m *ExOneOrg) Reset()         { *m = ExOneOrg{} }
func (m *ExOneOrg) String() string { return proto.CompactTextString(m) }
func (*ExOneOrg) ProtoMessage()    {}
func (*ExOneOrg) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{23}
}
func (m *ExOneOrg) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExOneOrg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExOneOrg.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExOneOrg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExOneOrg.Merge(m, src)
}
func (m *ExOneOrg) XXX_Size() int {
	return m.Size()
}
func (m *ExOneOrg) XXX_DiscardUnknown() {
	xxx_messageInfo_ExOneOrg.DiscardUnknown(m)
}

var xxx_messageInfo_ExOneOrg proto.InternalMessageInfo

func (m *ExOneOrg) GetRid() string {
	if m != nil {
		return m.Rid
	}
	return ""
}

func (m *ExOneOrg) GetOrgSelfId() string {
	if m != nil {
		return m.OrgSelfId
	}
	return ""
}

func (m *ExOneOrg) GetOrgShortName() string {
	if m != nil {
		return m.OrgShortName
	}
	return ""
}

func (m *ExOneOrg) GetOrgFullName() string {
	if m != nil {
		return m.OrgFullName
	}
	return ""
}

func (m *ExOneOrg) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *ExOneOrg) GetOrgIsVirtual() int32 {
	if m != nil {
		return m.OrgIsVirtual
	}
	return 0
}

func (m *ExOneOrg) GetDmrId() string {
	if m != nil {
		return m.DmrId
	}
	return ""
}

func (m *ExOneOrg) GetParentOrgId() string {
	if m != nil {
		return m.ParentOrgId
	}
	return ""
}

// rpc.cmd=330
// 网关终端请求所有终端数据回应
type ExOneDevice struct {
	// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
	// 设备所属的群组
	OrgId string `protobuf:"bytes,3,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	// @table varchar(16) not null unique
	// 设备名称
	SelfId string `protobuf:"bytes,4,opt,name=self_id,json=selfId,proto3" json:"self_id,omitempty"`
	// @table varchar(16) not null unique
	// 设备DMR-ID
	DmrId string `protobuf:"bytes,5,opt,name=dmr_id,json=dmrId,proto3" json:"dmr_id,omitempty"`
	// @table text default ”
	// 对讲机所属的虚拟群组,逗号分隔的群组rid
	VirOrgs string `protobuf:"bytes,6,opt,name=vir_orgs,json=virOrgs,proto3" json:"vir_orgs,omitempty"`
	// @table text default ”
	// 设备备注信息
	Note string `protobuf:"bytes,8,opt,name=note,proto3" json:"note,omitempty"`
	// @table int not null default 0
	// 设备类型 0:对讲机手台 1：车台 3:电话网关设备 4:中继虚拟终端 5:互联网关终端
	DeviceType int32 `protobuf:"varint,9,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	// @table int
	// 优先级
	Priority int32 `protobuf:"varint,12,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (m *ExOneDevice) Reset()         { *m = ExOneDevice{} }
func (m *ExOneDevice) String() string { return proto.CompactTextString(m) }
func (*ExOneDevice) ProtoMessage()    {}
func (*ExOneDevice) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{24}
}
func (m *ExOneDevice) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExOneDevice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExOneDevice.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExOneDevice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExOneDevice.Merge(m, src)
}
func (m *ExOneDevice) XXX_Size() int {
	return m.Size()
}
func (m *ExOneDevice) XXX_DiscardUnknown() {
	xxx_messageInfo_ExOneDevice.DiscardUnknown(m)
}

var xxx_messageInfo_ExOneDevice proto.InternalMessageInfo

func (m *ExOneDevice) GetOrgId() string {
	if m != nil {
		return m.OrgId
	}
	return ""
}

func (m *ExOneDevice) GetSelfId() string {
	if m != nil {
		return m.SelfId
	}
	return ""
}

func (m *ExOneDevice) GetDmrId() string {
	if m != nil {
		return m.DmrId
	}
	return ""
}

func (m *ExOneDevice) GetVirOrgs() string {
	if m != nil {
		return m.VirOrgs
	}
	return ""
}

func (m *ExOneDevice) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *ExOneDevice) GetDeviceType() int32 {
	if m != nil {
		return m.DeviceType
	}
	return 0
}

func (m *ExOneDevice) GetPriority() int32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

// rpc.cmd=180
// 查询ambe 序列号
type AmbeSerialCode struct {
	Code []int32 `protobuf:"varint,1,rep,packed,name=code,proto3" json:"code,omitempty"`
}

func (m *AmbeSerialCode) Reset()         { *m = AmbeSerialCode{} }
func (m *AmbeSerialCode) String() string { return proto.CompactTextString(m) }
func (*AmbeSerialCode) ProtoMessage()    {}
func (*AmbeSerialCode) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{25}
}
func (m *AmbeSerialCode) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AmbeSerialCode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AmbeSerialCode.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AmbeSerialCode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmbeSerialCode.Merge(m, src)
}
func (m *AmbeSerialCode) XXX_Size() int {
	return m.Size()
}
func (m *AmbeSerialCode) XXX_DiscardUnknown() {
	xxx_messageInfo_AmbeSerialCode.DiscardUnknown(m)
}

var xxx_messageInfo_AmbeSerialCode proto.InternalMessageInfo

func (m *AmbeSerialCode) GetCode() []int32 {
	if m != nil {
		return m.Code
	}
	return nil
}

// rpc.cmd=333
// 在线终端信息
type ExOnelineDevices struct {
	// 在线的终端dmrid
	DmrId []int32 `protobuf:"varint,1,rep,packed,name=dmr_id,json=dmrId,proto3" json:"dmr_id,omitempty"`
	// 终端对应的最后数据时间time.unix(单位:s)
	LastDataTime []int64 `protobuf:"varint,2,rep,packed,name=last_data_time,json=lastDataTime,proto3" json:"last_data_time,omitempty"`
}

func (m *ExOnelineDevices) Reset()         { *m = ExOnelineDevices{} }
func (m *ExOnelineDevices) String() string { return proto.CompactTextString(m) }
func (*ExOnelineDevices) ProtoMessage()    {}
func (*ExOnelineDevices) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{26}
}
func (m *ExOnelineDevices) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExOnelineDevices) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExOnelineDevices.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExOnelineDevices) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExOnelineDevices.Merge(m, src)
}
func (m *ExOnelineDevices) XXX_Size() int {
	return m.Size()
}
func (m *ExOnelineDevices) XXX_DiscardUnknown() {
	xxx_messageInfo_ExOnelineDevices.DiscardUnknown(m)
}

var xxx_messageInfo_ExOnelineDevices proto.InternalMessageInfo

func (m *ExOnelineDevices) GetDmrId() []int32 {
	if m != nil {
		return m.DmrId
	}
	return nil
}

func (m *ExOnelineDevices) GetLastDataTime() []int64 {
	if m != nil {
		return m.LastDataTime
	}
	return nil
}

// rpc.cmd=3
// 只能上行是3,下行的cmd=3已经被用于系统后台发短信
// 物联网数据
type IotData struct {
	// 指令
	Cmd int32 `protobuf:"zigzag32,1,opt,name=cmd,proto3" json:"cmd,omitempty"`
	// 设备类型
	DevType int32 `protobuf:"zigzag32,2,opt,name=dev_type,json=devType,proto3" json:"dev_type,omitempty"`
	// 终端设备ID,iot发射终端
	DevId []byte `protobuf:"bytes,3,opt,name=dev_id,json=devId,proto3" json:"dev_id,omitempty"`
	// 指令参数
	CmdParam []byte `protobuf:"bytes,4,opt,name=cmd_param,json=cmdParam,proto3" json:"cmd_param,omitempty"`
	// iot接收设备ID
	RecvDevId []byte `protobuf:"bytes,9,opt,name=recv_dev_id,json=recvDevId,proto3" json:"recv_dev_id,omitempty"`
	// 接收时间,unix epoch时间(秒)
	RecvTime int64 `protobuf:"zigzag64,10,opt,name=recv_time,json=recvTime,proto3" json:"recv_time,omitempty"`
	// 后台接收此数据的控制器ID，(控制器上传时不需要填写此字段)
	KcpRecvDevId string `protobuf:"bytes,11,opt,name=kcp_recv_dev_id,json=kcpRecvDevId,proto3" json:"kcp_recv_dev_id,omitempty"`
}

func (m *IotData) Reset()         { *m = IotData{} }
func (m *IotData) String() string { return proto.CompactTextString(m) }
func (*IotData) ProtoMessage()    {}
func (*IotData) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{27}
}
func (m *IotData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IotData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IotData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IotData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IotData.Merge(m, src)
}
func (m *IotData) XXX_Size() int {
	return m.Size()
}
func (m *IotData) XXX_DiscardUnknown() {
	xxx_messageInfo_IotData.DiscardUnknown(m)
}

var xxx_messageInfo_IotData proto.InternalMessageInfo

func (m *IotData) GetCmd() int32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *IotData) GetDevType() int32 {
	if m != nil {
		return m.DevType
	}
	return 0
}

func (m *IotData) GetDevId() []byte {
	if m != nil {
		return m.DevId
	}
	return nil
}

func (m *IotData) GetCmdParam() []byte {
	if m != nil {
		return m.CmdParam
	}
	return nil
}

func (m *IotData) GetRecvDevId() []byte {
	if m != nil {
		return m.RecvDevId
	}
	return nil
}

func (m *IotData) GetRecvTime() int64 {
	if m != nil {
		return m.RecvTime
	}
	return 0
}

func (m *IotData) GetKcpRecvDevId() string {
	if m != nil {
		return m.KcpRecvDevId
	}
	return ""
}

// rpc.cmd=8
// 广播给app终端
type DevDataInfo struct {
	// 广播代码
	// 0:主动上线
	// 1:主动下线
	// 3:超时下线
	// 14:断线下线
	// 8：讲话
	// 10: gps data data=bfdx_proto.Gps84
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 源id
	SrcDmrid uint32 `protobuf:"varint,2,opt,name=src_dmrid,json=srcDmrid,proto3" json:"src_dmrid,omitempty"`
	// 目标id
	DstDmrid uint32 `protobuf:"varint,3,opt,name=dst_dmrid,json=dstDmrid,proto3" json:"dst_dmrid,omitempty"`
	// 数据
	Data []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (m *DevDataInfo) Reset()         { *m = DevDataInfo{} }
func (m *DevDataInfo) String() string { return proto.CompactTextString(m) }
func (*DevDataInfo) ProtoMessage()    {}
func (*DevDataInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{28}
}
func (m *DevDataInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DevDataInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DevDataInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DevDataInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DevDataInfo.Merge(m, src)
}
func (m *DevDataInfo) XXX_Size() int {
	return m.Size()
}
func (m *DevDataInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DevDataInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DevDataInfo proto.InternalMessageInfo

func (m *DevDataInfo) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DevDataInfo) GetSrcDmrid() uint32 {
	if m != nil {
		return m.SrcDmrid
	}
	return 0
}

func (m *DevDataInfo) GetDstDmrid() uint32 {
	if m != nil {
		return m.DstDmrid
	}
	return 0
}

func (m *DevDataInfo) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type AddrBook struct {
	// 1.组呼  2.单呼
	Type int32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// 0.ok  -1.query err  -2.marshal err
	Code int32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	// code=1,body=db_org
	// code=2,body=db_device
	Body []byte `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	// para_str=dmridHex
	ParaStr string `protobuf:"bytes,4,opt,name=para_str,json=paraStr,proto3" json:"para_str,omitempty"`
}

func (m *AddrBook) Reset()         { *m = AddrBook{} }
func (m *AddrBook) String() string { return proto.CompactTextString(m) }
func (*AddrBook) ProtoMessage()    {}
func (*AddrBook) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{29}
}
func (m *AddrBook) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AddrBook) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AddrBook.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AddrBook) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddrBook.Merge(m, src)
}
func (m *AddrBook) XXX_Size() int {
	return m.Size()
}
func (m *AddrBook) XXX_DiscardUnknown() {
	xxx_messageInfo_AddrBook.DiscardUnknown(m)
}

var xxx_messageInfo_AddrBook proto.InternalMessageInfo

func (m *AddrBook) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddrBook) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AddrBook) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *AddrBook) GetParaStr() string {
	if m != nil {
		return m.ParaStr
	}
	return ""
}

// 返回查询通讯录结果
// rpc.cmd = 315
// rpc.res  0.ok  -1.db err  -2.marshal err   -3.需要查询的dmrid为空
type AddrBookList struct {
	AddrBookList []*AddrBook `protobuf:"bytes,1,rep,name=addr_book_list,json=addrBookList,proto3" json:"addr_book_list,omitempty"`
}

func (m *AddrBookList) Reset()         { *m = AddrBookList{} }
func (m *AddrBookList) String() string { return proto.CompactTextString(m) }
func (*AddrBookList) ProtoMessage()    {}
func (*AddrBookList) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{30}
}
func (m *AddrBookList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AddrBookList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AddrBookList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AddrBookList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddrBookList.Merge(m, src)
}
func (m *AddrBookList) XXX_Size() int {
	return m.Size()
}
func (m *AddrBookList) XXX_DiscardUnknown() {
	xxx_messageInfo_AddrBookList.DiscardUnknown(m)
}

var xxx_messageInfo_AddrBookList proto.InternalMessageInfo

func (m *AddrBookList) GetAddrBookList() []*AddrBook {
	if m != nil {
		return m.AddrBookList
	}
	return nil
}

// 虚拟集群手台注册请求
// rpc_cmd.cmd = 108
// rpc_cmd.res = 0
// rpc_cmd.body=bc40_req
type Bc40Req struct {
	// *手台ID*
	DevDmrid uint32 `protobuf:"fixed32,1,opt,name=dev_dmrid,json=devDmrid,proto3" json:"dev_dmrid,omitempty"`
	// *手台归属组ID*
	GroupDmrid uint32 `protobuf:"fixed32,2,opt,name=group_dmrid,json=groupDmrid,proto3" json:"group_dmrid,omitempty"`
	// *无线终端的场强值,0~4*
	Riss int32 `protobuf:"varint,3,opt,name=riss,proto3" json:"riss,omitempty"`
	// *手台状态信息 6字节*
	Status []byte `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// *本次开关机事件,0:关机,1:正常开机,2:电池开机,3:低压复位开机 8：信道切换注册*
	PowerEvent int32 `protobuf:"varint,5,opt,name=power_event,json=powerEvent,proto3" json:"power_event,omitempty"`
	// *上次开关机事件,1:正常开关机,2:异常开关机,3:低压关机/复位开机*
	LastPowerEvent int32 `protobuf:"varint,6,opt,name=last_power_event,json=lastPowerEvent,proto3" json:"last_power_event,omitempty"`
	// *0:主信道即选定发起注册,1:漫游信道发起注册*
	Roaming int32 `protobuf:"varint,7,opt,name=roaming,proto3" json:"roaming,omitempty"`
	// *虚拟集群下主中继在基站内的序列号*
	BsIndex int32 `protobuf:"varint,8,opt,name=bs_index,json=bsIndex,proto3" json:"bs_index,omitempty"`
}

func (m *Bc40Req) Reset()         { *m = Bc40Req{} }
func (m *Bc40Req) String() string { return proto.CompactTextString(m) }
func (*Bc40Req) ProtoMessage()    {}
func (*Bc40Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{31}
}
func (m *Bc40Req) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bc40Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bc40Req.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bc40Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bc40Req.Merge(m, src)
}
func (m *Bc40Req) XXX_Size() int {
	return m.Size()
}
func (m *Bc40Req) XXX_DiscardUnknown() {
	xxx_messageInfo_Bc40Req.DiscardUnknown(m)
}

var xxx_messageInfo_Bc40Req proto.InternalMessageInfo

func (m *Bc40Req) GetDevDmrid() uint32 {
	if m != nil {
		return m.DevDmrid
	}
	return 0
}

func (m *Bc40Req) GetGroupDmrid() uint32 {
	if m != nil {
		return m.GroupDmrid
	}
	return 0
}

func (m *Bc40Req) GetRiss() int32 {
	if m != nil {
		return m.Riss
	}
	return 0
}

func (m *Bc40Req) GetStatus() []byte {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *Bc40Req) GetPowerEvent() int32 {
	if m != nil {
		return m.PowerEvent
	}
	return 0
}

func (m *Bc40Req) GetLastPowerEvent() int32 {
	if m != nil {
		return m.LastPowerEvent
	}
	return 0
}

func (m *Bc40Req) GetRoaming() int32 {
	if m != nil {
		return m.Roaming
	}
	return 0
}

func (m *Bc40Req) GetBsIndex() int32 {
	if m != nil {
		return m.BsIndex
	}
	return 0
}

// 虚拟集群手台注册应答
// rpc_cmd.cmd = 108
// rpc_cmd.res = 1
// rpc_cmd.body=bc40_resp
type Bc40Resp struct {
	// *手台ID*
	DevDmrid uint32 `protobuf:"fixed32,1,opt,name=dev_dmrid,json=devDmrid,proto3" json:"dev_dmrid,omitempty"`
	// *应答结果码 0: 注册成功 1：系统无此手台信息 2:归属组错误 3:终端类型配置错误 4:未知错误 10:关机成功*
	ResCode int32 `protobuf:"varint,2,opt,name=res_code,json=resCode,proto3" json:"res_code,omitempty"`
	// 当前系统时间，unix时间，单位为秒
	// the number of seconds elapsed since January 1, 1970 UTC
	SysTime int64 `protobuf:"varint,3,opt,name=sys_time,json=sysTime,proto3" json:"sys_time,omitempty"`
}

func (m *Bc40Resp) Reset()         { *m = Bc40Resp{} }
func (m *Bc40Resp) String() string { return proto.CompactTextString(m) }
func (*Bc40Resp) ProtoMessage()    {}
func (*Bc40Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{32}
}
func (m *Bc40Resp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bc40Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bc40Resp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bc40Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bc40Resp.Merge(m, src)
}
func (m *Bc40Resp) XXX_Size() int {
	return m.Size()
}
func (m *Bc40Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_Bc40Resp.DiscardUnknown(m)
}

var xxx_messageInfo_Bc40Resp proto.InternalMessageInfo

func (m *Bc40Resp) GetDevDmrid() uint32 {
	if m != nil {
		return m.DevDmrid
	}
	return 0
}

func (m *Bc40Resp) GetResCode() int32 {
	if m != nil {
		return m.ResCode
	}
	return 0
}

func (m *Bc40Resp) GetSysTime() int64 {
	if m != nil {
		return m.SysTime
	}
	return 0
}

// msg for mesh gateway
// rpc_cmd.cmd = 500=Query8100DmridInfo
// rpc_cmd.para_int = dmrid
// rcp_cmd.cmd = 501=QueryAll8100DmridInfo
// rpc_cmd.para_int = 0: all group and radio info 1:query group info,2:query radio info
type Bf8100DmridInfo struct {
	// dmrid
	DmrID uint32 `protobuf:"fixed32,1,opt,name=DmrID,proto3" json:"DmrID,omitempty"`
	// dmrid name
	Name string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	// parent org uuid, 所属单位的uuid
	OrgUUID string `protobuf:"bytes,4,opt,name=OrgUUID,proto3" json:"OrgUUID,omitempty"`
	// self uuid
	MyUUID string `protobuf:"bytes,5,opt,name=MyUUID,proto3" json:"MyUUID,omitempty"`
}

func (m *Bf8100DmridInfo) Reset()         { *m = Bf8100DmridInfo{} }
func (m *Bf8100DmridInfo) String() string { return proto.CompactTextString(m) }
func (*Bf8100DmridInfo) ProtoMessage()    {}
func (*Bf8100DmridInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{33}
}
func (m *Bf8100DmridInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bf8100DmridInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bf8100DmridInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bf8100DmridInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bf8100DmridInfo.Merge(m, src)
}
func (m *Bf8100DmridInfo) XXX_Size() int {
	return m.Size()
}
func (m *Bf8100DmridInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_Bf8100DmridInfo.DiscardUnknown(m)
}

var xxx_messageInfo_Bf8100DmridInfo proto.InternalMessageInfo

func (m *Bf8100DmridInfo) GetDmrID() uint32 {
	if m != nil {
		return m.DmrID
	}
	return 0
}

func (m *Bf8100DmridInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Bf8100DmridInfo) GetOrgUUID() string {
	if m != nil {
		return m.OrgUUID
	}
	return ""
}

func (m *Bf8100DmridInfo) GetMyUUID() string {
	if m != nil {
		return m.MyUUID
	}
	return ""
}

// * 定位数据 *
type MeshGpsInfoT struct {
	// *数据源ID*
	SourceId uint32 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// *目标ID*
	TargetId uint32 `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// *GPS数据*
	Hour         uint32 `protobuf:"varint,3,opt,name=hour,proto3" json:"hour,omitempty"`
	Minute       uint32 `protobuf:"varint,4,opt,name=minute,proto3" json:"minute,omitempty"`
	Second       uint32 `protobuf:"varint,5,opt,name=second,proto3" json:"second,omitempty"`
	Day          uint32 `protobuf:"varint,6,opt,name=day,proto3" json:"day,omitempty"`
	Month        uint32 `protobuf:"varint,7,opt,name=month,proto3" json:"month,omitempty"`
	Year         uint32 `protobuf:"varint,8,opt,name=year,proto3" json:"year,omitempty"`
	Available    uint32 `protobuf:"varint,9,opt,name=available,proto3" json:"available,omitempty"`
	Latitude     uint32 `protobuf:"varint,10,opt,name=latitude,proto3" json:"latitude,omitempty"`
	NorthOrSouth uint32 `protobuf:"varint,11,opt,name=northOrSouth,proto3" json:"northOrSouth,omitempty"`
	Longitude    uint32 `protobuf:"varint,12,opt,name=longitude,proto3" json:"longitude,omitempty"`
	EastOrWest   uint32 `protobuf:"varint,13,opt,name=eastOrWest,proto3" json:"eastOrWest,omitempty"`
	Speed        uint32 `protobuf:"varint,14,opt,name=speed,proto3" json:"speed,omitempty"`
	Direction    uint32 `protobuf:"varint,15,opt,name=direction,proto3" json:"direction,omitempty"`
	Altitude     int32  `protobuf:"varint,16,opt,name=altitude,proto3" json:"altitude,omitempty"`
	// * (预留)GPS数据类型 *
	GpsDataType MESH_GPS_DATA_TYPE `protobuf:"varint,17,opt,name=gps_data_type,json=gpsDataType,proto3,enum=bfkcp.MESH_GPS_DATA_TYPE" json:"gps_data_type,omitempty"`
}

func (m *MeshGpsInfoT) Reset()         { *m = MeshGpsInfoT{} }
func (m *MeshGpsInfoT) String() string { return proto.CompactTextString(m) }
func (*MeshGpsInfoT) ProtoMessage()    {}
func (*MeshGpsInfoT) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{34}
}
func (m *MeshGpsInfoT) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MeshGpsInfoT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MeshGpsInfoT.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MeshGpsInfoT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeshGpsInfoT.Merge(m, src)
}
func (m *MeshGpsInfoT) XXX_Size() int {
	return m.Size()
}
func (m *MeshGpsInfoT) XXX_DiscardUnknown() {
	xxx_messageInfo_MeshGpsInfoT.DiscardUnknown(m)
}

var xxx_messageInfo_MeshGpsInfoT proto.InternalMessageInfo

func (m *MeshGpsInfoT) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *MeshGpsInfoT) GetTargetId() uint32 {
	if m != nil {
		return m.TargetId
	}
	return 0
}

func (m *MeshGpsInfoT) GetHour() uint32 {
	if m != nil {
		return m.Hour
	}
	return 0
}

func (m *MeshGpsInfoT) GetMinute() uint32 {
	if m != nil {
		return m.Minute
	}
	return 0
}

func (m *MeshGpsInfoT) GetSecond() uint32 {
	if m != nil {
		return m.Second
	}
	return 0
}

func (m *MeshGpsInfoT) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *MeshGpsInfoT) GetMonth() uint32 {
	if m != nil {
		return m.Month
	}
	return 0
}

func (m *MeshGpsInfoT) GetYear() uint32 {
	if m != nil {
		return m.Year
	}
	return 0
}

func (m *MeshGpsInfoT) GetAvailable() uint32 {
	if m != nil {
		return m.Available
	}
	return 0
}

func (m *MeshGpsInfoT) GetLatitude() uint32 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

func (m *MeshGpsInfoT) GetNorthOrSouth() uint32 {
	if m != nil {
		return m.NorthOrSouth
	}
	return 0
}

func (m *MeshGpsInfoT) GetLongitude() uint32 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *MeshGpsInfoT) GetEastOrWest() uint32 {
	if m != nil {
		return m.EastOrWest
	}
	return 0
}

func (m *MeshGpsInfoT) GetSpeed() uint32 {
	if m != nil {
		return m.Speed
	}
	return 0
}

func (m *MeshGpsInfoT) GetDirection() uint32 {
	if m != nil {
		return m.Direction
	}
	return 0
}

func (m *MeshGpsInfoT) GetAltitude() int32 {
	if m != nil {
		return m.Altitude
	}
	return 0
}

func (m *MeshGpsInfoT) GetGpsDataType() MESH_GPS_DATA_TYPE {
	if m != nil {
		return m.GpsDataType
	}
	return MESH_GPS_DATA_TYPE_ST_NONE
}

// mesh send gps info to server
// rpc_cmd.cmd = 502=MeshSendGpsInfo
// rpc_cmd.body = MeshGpsInfo
type MeshGpsInfo struct {
	// dev dmrid in 8100
	DmrID uint32 `protobuf:"fixed32,1,opt,name=DmrID,proto3" json:"DmrID,omitempty"`
	// gps info
	GpsInfo *MeshGpsInfoT `protobuf:"bytes,2,opt,name=GpsInfo,proto3" json:"GpsInfo,omitempty"`
}

func (m *MeshGpsInfo) Reset()         { *m = MeshGpsInfo{} }
func (m *MeshGpsInfo) String() string { return proto.CompactTextString(m) }
func (*MeshGpsInfo) ProtoMessage()    {}
func (*MeshGpsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{35}
}
func (m *MeshGpsInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MeshGpsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MeshGpsInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MeshGpsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeshGpsInfo.Merge(m, src)
}
func (m *MeshGpsInfo) XXX_Size() int {
	return m.Size()
}
func (m *MeshGpsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MeshGpsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MeshGpsInfo proto.InternalMessageInfo

func (m *MeshGpsInfo) GetDmrID() uint32 {
	if m != nil {
		return m.DmrID
	}
	return 0
}

func (m *MeshGpsInfo) GetGpsInfo() *MeshGpsInfoT {
	if m != nil {
		return m.GpsInfo
	}
	return nil
}

type PocCommand struct {
	Cmd int32 `protobuf:"varint,1,opt,name=cmd,proto3" json:"cmd,omitempty"`
	// seq_no
	SeqNo int64  `protobuf:"varint,2,opt,name=seq_no,json=seqNo,proto3" json:"seq_no,omitempty"`
	Body  []byte `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	// para_str
	ParaStr string `protobuf:"bytes,4,opt,name=para_str,json=paraStr,proto3" json:"para_str,omitempty"`
	// para_bin
	ParaBin []byte `protobuf:"bytes,5,opt,name=para_bin,json=paraBin,proto3" json:"para_bin,omitempty"`
	// para_int
	ParaInt int64 `protobuf:"varint,6,opt,name=para_int,json=paraInt,proto3" json:"para_int,omitempty"`
}

func (m *PocCommand) Reset()         { *m = PocCommand{} }
func (m *PocCommand) String() string { return proto.CompactTextString(m) }
func (*PocCommand) ProtoMessage()    {}
func (*PocCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{36}
}
func (m *PocCommand) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PocCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PocCommand.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PocCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PocCommand.Merge(m, src)
}
func (m *PocCommand) XXX_Size() int {
	return m.Size()
}
func (m *PocCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_PocCommand.DiscardUnknown(m)
}

var xxx_messageInfo_PocCommand proto.InternalMessageInfo

func (m *PocCommand) GetCmd() int32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *PocCommand) GetSeqNo() int64 {
	if m != nil {
		return m.SeqNo
	}
	return 0
}

func (m *PocCommand) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *PocCommand) GetParaStr() string {
	if m != nil {
		return m.ParaStr
	}
	return ""
}

func (m *PocCommand) GetParaBin() []byte {
	if m != nil {
		return m.ParaBin
	}
	return nil
}

func (m *PocCommand) GetParaInt() int64 {
	if m != nil {
		return m.ParaInt
	}
	return 0
}

type PocDefaultGroup struct {
	DefaultSendGroupDmrid uint32 `protobuf:"fixed32,1,opt,name=default_send_group_dmrid,json=defaultSendGroupDmrid,proto3" json:"default_send_group_dmrid,omitempty"`
	// 最多256个，如果多于256个，则应该分包发送多次
	DefaultListenGroupDmrids []uint32 `protobuf:"fixed32,2,rep,packed,name=default_listen_group_dmrids,json=defaultListenGroupDmrids,proto3" json:"default_listen_group_dmrids,omitempty"`
}

func (m *PocDefaultGroup) Reset()         { *m = PocDefaultGroup{} }
func (m *PocDefaultGroup) String() string { return proto.CompactTextString(m) }
func (*PocDefaultGroup) ProtoMessage()    {}
func (*PocDefaultGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{37}
}
func (m *PocDefaultGroup) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PocDefaultGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PocDefaultGroup.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PocDefaultGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PocDefaultGroup.Merge(m, src)
}
func (m *PocDefaultGroup) XXX_Size() int {
	return m.Size()
}
func (m *PocDefaultGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_PocDefaultGroup.DiscardUnknown(m)
}

var xxx_messageInfo_PocDefaultGroup proto.InternalMessageInfo

func (m *PocDefaultGroup) GetDefaultSendGroupDmrid() uint32 {
	if m != nil {
		return m.DefaultSendGroupDmrid
	}
	return 0
}

func (m *PocDefaultGroup) GetDefaultListenGroupDmrids() []uint32 {
	if m != nil {
		return m.DefaultListenGroupDmrids
	}
	return nil
}

type PocSubscribleUpdateOption struct {
	// 序号
	FrameNo int32 `protobuf:"varint,1,opt,name=frame_no,json=frameNo,proto3" json:"frame_no,omitempty"`
	// 帧类型
	// 0: 一次性更新
	// 1: 开始批量更新
	// 2: 处于批量更新中
	// 3: 批量更新结束
	FrameType int32 `protobuf:"varint,2,opt,name=frame_type,json=frameType,proto3" json:"frame_type,omitempty"`
}

func (m *PocSubscribleUpdateOption) Reset()         { *m = PocSubscribleUpdateOption{} }
func (m *PocSubscribleUpdateOption) String() string { return proto.CompactTextString(m) }
func (*PocSubscribleUpdateOption) ProtoMessage()    {}
func (*PocSubscribleUpdateOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{38}
}
func (m *PocSubscribleUpdateOption) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PocSubscribleUpdateOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PocSubscribleUpdateOption.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PocSubscribleUpdateOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PocSubscribleUpdateOption.Merge(m, src)
}
func (m *PocSubscribleUpdateOption) XXX_Size() int {
	return m.Size()
}
func (m *PocSubscribleUpdateOption) XXX_DiscardUnknown() {
	xxx_messageInfo_PocSubscribleUpdateOption.DiscardUnknown(m)
}

var xxx_messageInfo_PocSubscribleUpdateOption proto.InternalMessageInfo

func (m *PocSubscribleUpdateOption) GetFrameNo() int32 {
	if m != nil {
		return m.FrameNo
	}
	return 0
}

func (m *PocSubscribleUpdateOption) GetFrameType() int32 {
	if m != nil {
		return m.FrameType
	}
	return 0
}

type PocConfig struct {
	// can edit subscription local
	// 终端是否可以本地编辑收听组,0:不可以,1:可以
	CanEditSubscriptionLocal int32 `protobuf:"varint,1,opt,name=can_edit_subscription_local,json=canEditSubscriptionLocal,proto3" json:"can_edit_subscription_local,omitempty"`
	// reliable gps transfer
	// 是否启用可靠的gps传输,0:不启用,1:启用
	Rgps int32 `protobuf:"varint,2,opt,name=rgps,proto3" json:"rgps,omitempty"`
}

func (m *PocConfig) Reset()         { *m = PocConfig{} }
func (m *PocConfig) String() string { return proto.CompactTextString(m) }
func (*PocConfig) ProtoMessage()    {}
func (*PocConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_f28c1d377d09b8d3, []int{39}
}
func (m *PocConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PocConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PocConfig.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PocConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PocConfig.Merge(m, src)
}
func (m *PocConfig) XXX_Size() int {
	return m.Size()
}
func (m *PocConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PocConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PocConfig proto.InternalMessageInfo

func (m *PocConfig) GetCanEditSubscriptionLocal() int32 {
	if m != nil {
		return m.CanEditSubscriptionLocal
	}
	return 0
}

func (m *PocConfig) GetRgps() int32 {
	if m != nil {
		return m.Rgps
	}
	return 0
}

func init() {
	proto.RegisterEnum("bfkcp.MESH_GPS_DATA_TYPE", MESH_GPS_DATA_TYPE_name, MESH_GPS_DATA_TYPE_value)
	proto.RegisterType((*RpcCmd)(nil), "bfkcp.rpc_cmd")
	proto.RegisterType((*Login)(nil), "bfkcp.login")
	proto.RegisterType((*ResLoginParaBin)(nil), "bfkcp.res_login_para_bin")
	proto.RegisterType((*ResLogin)(nil), "bfkcp.res_login")
	proto.RegisterType((*ResRepeaterState)(nil), "bfkcp.res_repeater_state")
	proto.RegisterType((*RepeaterErrStatus)(nil), "bfkcp.repeater_err_status")
	proto.RegisterType((*DeviceSend)(nil), "bfkcp.device_send")
	proto.RegisterType((*ServerSend)(nil), "bfkcp.server_send")
	proto.RegisterType((*Bc71)(nil), "bfkcp.bc71")
	proto.RegisterType((*Cb71)(nil), "bfkcp.cb71")
	proto.RegisterType((*Bc73)(nil), "bfkcp.bc73")
	proto.RegisterType((*Cb75)(nil), "bfkcp.cb75")
	proto.RegisterType((*Bc15)(nil), "bfkcp.bc15")
	proto.RegisterType((*Bc10)(nil), "bfkcp.bc10")
	proto.RegisterType((*Bc30)(nil), "bfkcp.bc30")
	proto.RegisterType((*Dtmf)(nil), "bfkcp.dtmf")
	proto.RegisterType((*EndCall)(nil), "bfkcp.end_call")
	proto.RegisterType((*PhoneTransfer)(nil), "bfkcp.phone_transfer")
	proto.RegisterType((*OneChannelItem)(nil), "bfkcp.OneChannelItem")
	proto.RegisterType((*Channels)(nil), "bfkcp.Channels")
	proto.RegisterType((*PhoneAdapterShortNo2DmridReq)(nil), "bfkcp.PhoneAdapterShortNo2DmridReq")
	proto.RegisterType((*PhoneAdapterShortNo2DmridRes)(nil), "bfkcp.PhoneAdapterShortNo2DmridRes")
	proto.RegisterType((*PhoneLineSetting)(nil), "bfkcp.PhoneLineSetting")
	proto.RegisterType((*ExOneOrg)(nil), "bfkcp.ex_one_org")
	proto.RegisterType((*ExOneDevice)(nil), "bfkcp.ex_one_device")
	proto.RegisterType((*AmbeSerialCode)(nil), "bfkcp.ambe_serial_code")
	proto.RegisterType((*ExOnelineDevices)(nil), "bfkcp.ex_oneline_devices")
	proto.RegisterType((*IotData)(nil), "bfkcp.iot_data")
	proto.RegisterType((*DevDataInfo)(nil), "bfkcp.dev_data_info")
	proto.RegisterType((*AddrBook)(nil), "bfkcp.addr_book")
	proto.RegisterType((*AddrBookList)(nil), "bfkcp.addr_book_list")
	proto.RegisterType((*Bc40Req)(nil), "bfkcp.bc40_req")
	proto.RegisterType((*Bc40Resp)(nil), "bfkcp.bc40_resp")
	proto.RegisterType((*Bf8100DmridInfo)(nil), "bfkcp.Bf8100DmridInfo")
	proto.RegisterType((*MeshGpsInfoT)(nil), "bfkcp.mesh_gps_info_t")
	proto.RegisterType((*MeshGpsInfo)(nil), "bfkcp.MeshGpsInfo")
	proto.RegisterType((*PocCommand)(nil), "bfkcp.PocCommand")
	proto.RegisterType((*PocDefaultGroup)(nil), "bfkcp.PocDefaultGroup")
	proto.RegisterType((*PocSubscribleUpdateOption)(nil), "bfkcp.PocSubscribleUpdateOption")
	proto.RegisterType((*PocConfig)(nil), "bfkcp.PocConfig")
}

func init() { proto.RegisterFile("bf8100.proto", fileDescriptor_f28c1d377d09b8d3) }

var fileDescriptor_f28c1d377d09b8d3 = []byte{
	// 3039 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0x4f, 0x8f, 0x1b, 0xc7,
	0x95, 0x17, 0xc9, 0xe1, 0x90, 0xfd, 0xf8, 0x67, 0xa8, 0xb6, 0x24, 0x53, 0x96, 0x35, 0x9a, 0x6d,
	0xd8, 0xeb, 0xc1, 0x2e, 0x20, 0x48, 0xf2, 0x7a, 0xbd, 0x17, 0x1f, 0x46, 0x1a, 0xca, 0x22, 0x3c,
	0xc3, 0x19, 0x37, 0x39, 0xf2, 0xea, 0xe2, 0xde, 0x66, 0x77, 0x91, 0xd3, 0x50, 0x77, 0x57, 0xab,
	0xaa, 0x48, 0xcd, 0xf8, 0xb0, 0xc7, 0x05, 0x7c, 0x58, 0x20, 0x01, 0x72, 0xca, 0x57, 0x09, 0x72,
	0x49, 0x2e, 0xc9, 0x29, 0x3e, 0x05, 0x39, 0x06, 0x36, 0x02, 0xe4, 0x03, 0x04, 0x39, 0x25, 0x41,
	0xf0, 0x5e, 0x55, 0xf7, 0xf4, 0x48, 0xe1, 0x40, 0x31, 0x0c, 0xc5, 0x87, 0xdc, 0xaa, 0x7e, 0xef,
	0xf5, 0xab, 0x7a, 0x7f, 0xea, 0xd5, 0xab, 0xaa, 0x86, 0xf6, 0x74, 0xf6, 0x5f, 0x77, 0xef, 0xdc,
	0xb9, 0x9d, 0x09, 0xae, 0xb8, 0x5d, 0x9f, 0xce, 0x9e, 0x06, 0x99, 0xf3, 0x93, 0x0a, 0x34, 0x44,
	0x16, 0x78, 0x41, 0x12, 0xda, 0x57, 0x61, 0x5d, 0xb2, 0x67, 0x5e, 0xca, 0xfb, 0xd5, 0xad, 0xca,
	0x76, 0xdd, 0xad, 0x4b, 0xf6, 0x6c, 0xc4, 0xed, 0x1e, 0xd4, 0x64, 0x14, 0xf6, 0x6b, 0x5b, 0x95,
	0xed, 0x9a, 0x8b, 0x4d, 0x44, 0x82, 0x24, 0xec, 0xd7, 0x89, 0x0b, 0x9b, 0x88, 0x08, 0x26, 0xfb,
	0x4d, 0x8d, 0x08, 0x26, 0x6d, 0x1b, 0xd6, 0xa6, 0x3c, 0x3c, 0xed, 0xc3, 0x56, 0x65, 0xbb, 0xed,
	0x52, 0xdb, 0xbe, 0x0e, 0xcd, 0xcc, 0x17, 0xbe, 0x27, 0x95, 0xe8, 0xb7, 0xb6, 0x2a, 0xdb, 0x96,
	0xdb, 0xc0, 0xfe, 0x58, 0x89, 0x82, 0x34, 0x8d, 0xd2, 0x7e, 0x9b, 0x3e, 0x21, 0xd2, 0xfd, 0x28,
	0x2d, 0x48, 0x51, 0xaa, 0xfa, 0x1d, 0x9a, 0x04, 0x91, 0x86, 0xa9, 0x72, 0x7e, 0x5a, 0x85, 0x7a,
	0xcc, 0xe7, 0x51, 0x6a, 0xff, 0x0b, 0xb4, 0x43, 0xb6, 0x8c, 0x02, 0xe6, 0x85, 0x89, 0x88, 0xc2,
	0x7e, 0x65, 0xab, 0xb2, 0xdd, 0x70, 0x5b, 0x1a, 0xdb, 0x45, 0xc8, 0xbe, 0x05, 0xa6, 0xeb, 0xa5,
	0x7e, 0xc2, 0x48, 0x47, 0xcb, 0x05, 0x0d, 0x8d, 0xfc, 0x84, 0xd9, 0x37, 0x01, 0x48, 0x98, 0xa7,
	0x4e, 0x33, 0x46, 0xfa, 0xd6, 0x5d, 0x8b, 0x90, 0xc9, 0x69, 0xc6, 0x4a, 0x43, 0x24, 0x3c, 0x64,
	0x71, 0x7f, 0x8d, 0x04, 0x18, 0x99, 0xfb, 0x08, 0xd9, 0x6f, 0xe1, 0x54, 0xa5, 0x7c, 0xce, 0x85,
	0xb6, 0x8e, 0xe5, 0x16, 0x7d, 0xfb, 0x3d, 0xd8, 0xc8, 0xdb, 0x5e, 0xc2, 0xd4, 0x31, 0x0f, 0xfb,
	0xeb, 0x34, 0x44, 0x37, 0x87, 0xf7, 0x09, 0x45, 0x7d, 0x55, 0x94, 0x30, 0xb2, 0x52, 0x43, 0x5b,
	0x09, 0xfb, 0x68, 0x25, 0xf4, 0xd0, 0xa9, 0xf4, 0xa2, 0x90, 0x2c, 0x6d, 0xb9, 0x75, 0x79, 0x2a,
	0x87, 0x21, 0xce, 0x8c, 0x9d, 0x28, 0xe1, 0x7b, 0x3c, 0x53, 0x11, 0x4f, 0xfb, 0xd6, 0x56, 0x6d,
	0xbb, 0xee, 0xb6, 0x08, 0x3b, 0x20, 0xc8, 0xbe, 0x02, 0xf5, 0x80, 0x87, 0x2c, 0xe8, 0x03, 0xd1,
	0x74, 0xc7, 0xf9, 0x5f, 0xb0, 0x05, 0x93, 0x9e, 0xd6, 0x3a, 0xb7, 0xbf, 0xbd, 0x05, 0xad, 0xa5,
	0x1f, 0x47, 0xe1, 0x38, 0x7d, 0xc0, 0x43, 0x46, 0xa6, 0x6c, 0xbb, 0x65, 0xc8, 0xbe, 0x06, 0xeb,
	0x51, 0x32, 0x65, 0xe3, 0xd4, 0x58, 0xd1, 0xf4, 0xec, 0xdb, 0x60, 0x47, 0xf2, 0x91, 0xbf, 0x64,
	0x0f, 0x17, 0x71, 0xfc, 0xc0, 0x8f, 0xe3, 0x43, 0x26, 0x12, 0x63, 0xc9, 0xbf, 0x41, 0x71, 0x7e,
	0x5d, 0x01, 0xab, 0x98, 0x00, 0x2a, 0x8e, 0x9d, 0x20, 0x1f, 0xb4, 0xee, 0x36, 0x04, 0x93, 0x34,
	0xe0, 0xcb, 0x31, 0x78, 0x0b, 0x5a, 0xc7, 0x7e, 0x3a, 0x5f, 0x64, 0x1e, 0x1a, 0x87, 0x9c, 0x51,
	0x77, 0x41, 0x43, 0x93, 0x28, 0x61, 0xf6, 0x0d, 0xb0, 0x8e, 0x95, 0xca, 0xbc, 0x8c, 0x0b, 0x65,
	0x42, 0xb5, 0x89, 0xc0, 0x21, 0x17, 0xca, 0x7e, 0x17, 0xba, 0x92, 0x89, 0x25, 0x13, 0xde, 0x92,
	0x09, 0x89, 0x36, 0x5b, 0x27, 0x45, 0x3a, 0x1a, 0x7d, 0xac, 0x41, 0xfb, 0x43, 0xe8, 0x4b, 0xa6,
	0x54, 0x94, 0xce, 0xbd, 0xd8, 0x97, 0xca, 0x5b, 0x64, 0xa1, 0xaf, 0x98, 0x1e, 0x51, 0xbb, 0xe6,
	0xaa, 0xa1, 0xef, 0xf9, 0x52, 0x1d, 0x11, 0x15, 0x07, 0x77, 0xfe, 0x52, 0xd3, 0x96, 0x15, 0x2c,
	0x63, 0xbe, 0x62, 0xc2, 0x93, 0xca, 0x57, 0xec, 0x55, 0xa2, 0xf4, 0x26, 0x40, 0x70, 0xec, 0xa7,
	0x29, 0x8b, 0xd1, 0xcd, 0x7a, 0x21, 0x5a, 0x06, 0xd1, 0xae, 0x16, 0x27, 0xde, 0x4c, 0xb0, 0x67,
	0x0b, 0x96, 0x06, 0xa7, 0x64, 0x91, 0x86, 0xdb, 0x12, 0x27, 0x0f, 0x73, 0x08, 0x59, 0x54, 0x99,
	0x65, 0x4d, 0xb3, 0xa8, 0x12, 0xcb, 0x2d, 0x68, 0x65, 0xfc, 0x39, 0x6a, 0xef, 0xc7, 0x0b, 0x66,
	0xac, 0x03, 0x04, 0x3d, 0x46, 0xc4, 0x7e, 0x13, 0x1a, 0x51, 0xe6, 0xf9, 0x61, 0x28, 0xc8, 0x30,
	0x0d, 0x77, 0x3d, 0xca, 0x76, 0xc2, 0x50, 0xa0, 0x55, 0x97, 0x3c, 0x36, 0xdf, 0x35, 0xb4, 0x55,
	0x97, 0x3c, 0xd6, 0x5f, 0xdd, 0x00, 0x4b, 0x25, 0x99, 0x21, 0xea, 0x5c, 0xd0, 0x54, 0x49, 0x56,
	0x88, 0x44, 0x22, 0x13, 0xa2, 0x6f, 0x11, 0x69, 0x5d, 0x25, 0xd9, 0x40, 0x08, 0x24, 0xf8, 0xa9,
	0x22, 0x02, 0x68, 0x82, 0x9f, 0x2a, 0x43, 0x98, 0x67, 0x92, 0x08, 0x2d, 0x4d, 0x98, 0x67, 0xd2,
	0x10, 0x70, 0x12, 0x48, 0x68, 0x6b, 0xc2, 0x92, 0xc7, 0x48, 0x78, 0x1b, 0x40, 0x9c, 0x78, 0x59,
	0xac, 0x69, 0x1d, 0x3d, 0x03, 0x71, 0x72, 0x18, 0xe7, 0x54, 0x75, 0x46, 0xed, 0x9a, 0xf9, 0xe5,
	0xd4, 0x37, 0xa1, 0x31, 0xf3, 0x53, 0x22, 0x6d, 0x68, 0xa1, 0x33, 0x3f, 0x45, 0xc2, 0x35, 0x58,
	0x97, 0xd1, 0x3c, 0xf5, 0xe3, 0x7e, 0x4f, 0xe3, 0xba, 0x87, 0xda, 0xe2, 0xbc, 0xb5, 0xb6, 0x97,
	0xb5, 0x34, 0x3f, 0x55, 0xa4, 0xad, 0xf3, 0x65, 0x15, 0xde, 0x28, 0x9c, 0xcf, 0x84, 0x0e, 0x80,
	0x85, 0x7c, 0x95, 0x08, 0x28, 0x19, 0xaa, 0xba, 0xca, 0x50, 0xb5, 0x55, 0x86, 0x5a, 0x5b, 0x65,
	0xa8, 0xfa, 0x05, 0x86, 0x5a, 0xbf, 0xd0, 0x50, 0x8d, 0xd5, 0x86, 0x6a, 0xae, 0x30, 0x94, 0x55,
	0x36, 0x94, 0xf3, 0x45, 0x91, 0x78, 0x25, 0x4b, 0x43, 0x5c, 0x7b, 0x85, 0x65, 0xca, 0x46, 0xe8,
	0xe4, 0xa8, 0x36, 0x43, 0x0f, 0x6a, 0x33, 0xf9, 0x94, 0x34, 0x6d, 0xbb, 0xd8, 0xb4, 0xef, 0xc1,
	0x55, 0xc9, 0x17, 0x22, 0x60, 0xde, 0x0b, 0xdf, 0xeb, 0x08, 0x7f, 0x43, 0x13, 0xdd, 0xb2, 0x14,
	0xe7, 0x16, 0xb4, 0xcc, 0x42, 0xa7, 0xb1, 0x5f, 0x12, 0xea, 0xfc, 0xa9, 0x06, 0x6b, 0xd3, 0xe0,
	0xc3, 0xbb, 0xaf, 0x3a, 0x2d, 0x5c, 0x5d, 0xbe, 0x98, 0x33, 0x65, 0x98, 0xaa, 0x66, 0x75, 0x11,
	0x56, 0xb0, 0x98, 0x79, 0x6a, 0x16, 0xb3, 0x46, 0x35, 0xa6, 0x59, 0xde, 0x83, 0x8d, 0x59, 0xc4,
	0xe2, 0x10, 0x37, 0x35, 0x96, 0xca, 0x48, 0x9d, 0x1a, 0xcf, 0x75, 0x09, 0x1e, 0xe6, 0x28, 0x32,
	0xca, 0x45, 0x86, 0x39, 0xcc, 0x0b, 0xa3, 0x79, 0xa4, 0xfc, 0xd8, 0x78, 0xb2, 0x6b, 0xe0, 0x5d,
	0x8d, 0x52, 0x46, 0x33, 0x8c, 0x7e, 0xea, 0xc7, 0x7c, 0x6e, 0xbc, 0xda, 0x31, 0xe8, 0x0e, 0x81,
	0xf6, 0x16, 0xb4, 0xf5, 0xe6, 0x12, 0x73, 0x85, 0x3b, 0xbd, 0x76, 0x2e, 0xd0, 0x06, 0x13, 0x73,
	0x35, 0xe2, 0xb4, 0x87, 0x89, 0x88, 0x0b, 0x9c, 0x93, 0x59, 0xc3, 0x79, 0x1f, 0x93, 0x93, 0xe4,
	0x8b, 0x34, 0xd4, 0x3b, 0xa4, 0xf6, 0xb2, 0x45, 0x08, 0xed, 0x90, 0xb8, 0x53, 0x1f, 0xf3, 0x94,
	0xa1, 0x60, 0x30, 0xfb, 0x3b, 0xf6, 0x47, 0x1c, 0x33, 0x4e, 0xe0, 0xc7, 0xb1, 0x17, 0x2e, 0xb2,
	0x98, 0x9d, 0x98, 0xc5, 0x07, 0x08, 0xed, 0x12, 0xb2, 0xda, 0xb9, 0x1b, 0x2b, 0x9d, 0x6b, 0xef,
	0xc0, 0xcd, 0x4c, 0xb0, 0x19, 0x13, 0x64, 0x46, 0x21, 0x16, 0x99, 0xf2, 0xce, 0x39, 0xa7, 0x47,
	0xdf, 0xbe, 0xa5, 0x99, 0x86, 0x39, 0xcf, 0xe4, 0xcc, 0x57, 0xce, 0xef, 0x6b, 0xb0, 0x16, 0x4c,
	0x5f, 0xb7, 0xfb, 0xaf, 0xc2, 0xba, 0xd0, 0x95, 0x96, 0xf6, 0x7a, 0x5d, 0x50, 0xa5, 0xf5, 0xfd,
	0x72, 0xf6, 0x35, 0x9c, 0xa4, 0x5c, 0xc4, 0x2a, 0x5f, 0xce, 0xba, 0x87, 0xb3, 0x3c, 0x33, 0xb7,
	0x56, 0x11, 0x48, 0xc5, 0x6e, 0x01, 0x17, 0x5b, 0x59, 0x29, 0x5a, 0x5a, 0x17, 0x45, 0x4b, 0xfb,
	0xc2, 0x68, 0xe9, 0xbc, 0x7a, 0xb4, 0x74, 0x57, 0xa7, 0x82, 0x5f, 0x56, 0x68, 0xa5, 0xbf, 0xff,
	0x7a, 0x5d, 0xfd, 0xa2, 0x0f, 0xd6, 0x5e, 0xf2, 0xc1, 0x4a, 0x5d, 0xea, 0xab, 0x75, 0x09, 0x29,
	0x6a, 0x3f, 0xf8, 0x8e, 0xe6, 0x48, 0x9e, 0xf6, 0x25, 0x4f, 0xf3, 0x5d, 0x42, 0xf7, 0x9c, 0x9f,
	0xaf, 0xa1, 0xc5, 0xee, 0x7e, 0xf0, 0xcf, 0xdc, 0x68, 0x5c, 0x75, 0x03, 0x2c, 0x8a, 0xcb, 0x52,
	0xfa, 0x6b, 0x22, 0x40, 0xf1, 0x7c, 0xd1, 0x5a, 0xfa, 0x77, 0xb8, 0x9c, 0xcf, 0xa0, 0x58, 0x24,
	0xa6, 0xda, 0xe9, 0x19, 0x42, 0x91, 0x9e, 0x8a, 0xe8, 0xd7, 0x25, 0x83, 0x59, 0x38, 0x14, 0xfd,
	0x63, 0x5d, 0x44, 0x9c, 0x5f, 0x58, 0xed, 0x8b, 0x16, 0x56, 0xe7, 0x35, 0xa4, 0x61, 0x9c, 0x8e,
	0xf2, 0x85, 0xd2, 0x75, 0x31, 0xe6, 0xdc, 0x75, 0xd7, 0x22, 0x84, 0x6a, 0xe1, 0xff, 0xaf, 0x52,
	0x14, 0xdd, 0xf9, 0x8e, 0xc2, 0xe3, 0x9c, 0x0f, 0xea, 0x17, 0xf8, 0x60, 0xfd, 0x55, 0x7c, 0xd0,
	0x58, 0xe1, 0x83, 0xeb, 0xd0, 0x9c, 0x09, 0x3f, 0x21, 0x1b, 0x36, 0x69, 0x12, 0x0d, 0xea, 0x8f,
	0xb8, 0xbd, 0x09, 0x2d, 0x9e, 0x2d, 0xa4, 0x17, 0xfa, 0xca, 0xf7, 0xee, 0x52, 0x18, 0xb4, 0x5d,
	0x0b, 0xa1, 0x5d, 0x5f, 0xf9, 0x77, 0xcf, 0xd3, 0xef, 0x99, 0x03, 0x70, 0x41, 0xbf, 0xe7, 0xfc,
	0x1f, 0x55, 0x1c, 0xef, 0xdf, 0xf9, 0xbe, 0xe5, 0xa1, 0x7f, 0xb8, 0x61, 0xb1, 0xa0, 0x4e, 0xa6,
	0x8c, 0x0c, 0x67, 0xcc, 0xda, 0x44, 0x00, 0xcd, 0xf6, 0x42, 0xcc, 0xc3, 0x8b, 0x31, 0xbf, 0x32,
	0x6e, 0x5b, 0xab, 0x93, 0xe8, 0xcf, 0x2a, 0xb0, 0x16, 0xaa, 0x64, 0xf6, 0x7a, 0x1d, 0x71, 0x1d,
	0x9a, 0x38, 0x28, 0x1d, 0xef, 0xf5, 0xa1, 0xb3, 0x81, 0x7d, 0x3c, 0xde, 0xaf, 0x54, 0xa2, 0xb1,
	0x5a, 0x89, 0xdf, 0x55, 0xa0, 0xc9, 0xd2, 0xd0, 0x43, 0x4f, 0xbd, 0x5e, 0x45, 0xbe, 0x7d, 0x16,
	0xfa, 0x36, 0xbb, 0xf7, 0x29, 0x74, 0xb5, 0x38, 0x25, 0xfc, 0x54, 0xce, 0x98, 0xa0, 0x43, 0x2c,
	0x21, 0x65, 0x25, 0x80, 0xa0, 0x62, 0x82, 0x29, 0x7f, 0x6e, 0x2a, 0x42, 0xa3, 0x81, 0x95, 0xf2,
	0xe7, 0xba, 0xfe, 0xc3, 0xed, 0x23, 0x97, 0x95, 0xf3, 0xe8, 0x83, 0x44, 0x37, 0x87, 0x35, 0xa3,
	0xf3, 0x05, 0x74, 0x0f, 0x52, 0xf6, 0xc0, 0x9c, 0xc1, 0x15, 0x4b, 0xec, 0x2e, 0x54, 0x53, 0x6e,
	0xee, 0x28, 0xaa, 0x29, 0xb7, 0xdf, 0x06, 0x0b, 0x8f, 0x17, 0x1f, 0x0b, 0xbe, 0xc8, 0xcc, 0x95,
	0xc8, 0x19, 0x60, 0x6f, 0x41, 0x2b, 0x8e, 0xa4, 0x62, 0xa9, 0xa6, 0xd7, 0xb6, 0x6a, 0xdb, 0x96,
	0x5b, 0x86, 0xec, 0x3e, 0x34, 0xbe, 0xe0, 0x29, 0x73, 0xcd, 0x59, 0xc6, 0x72, 0xf3, 0xae, 0xf3,
	0x65, 0x05, 0x9a, 0x66, 0x64, 0x69, 0xdf, 0x85, 0xa6, 0xb9, 0x09, 0x90, 0xfd, 0xca, 0x56, 0x6d,
	0xbb, 0x75, 0xef, 0xea, 0x6d, 0xba, 0xc6, 0xbb, 0x7d, 0x7e, 0x7e, 0x6e, 0xc1, 0xf6, 0xd2, 0x79,
	0xb3, 0x5a, 0xbe, 0xb4, 0x2a, 0xf6, 0x5b, 0xc3, 0x52, 0xac, 0x70, 0x7d, 0xbc, 0xec, 0x6a, 0xf8,
	0xd0, 0xa0, 0x8e, 0x0f, 0x6f, 0x1f, 0xa2, 0x75, 0x77, 0x42, 0x3f, 0x53, 0x4c, 0x8c, 0x8f, 0xb9,
	0x50, 0x23, 0x7e, 0x8f, 0xa4, 0xb8, 0xec, 0x19, 0x7a, 0x5c, 0x22, 0xe6, 0x19, 0xdb, 0x58, 0x6e,
	0x43, 0x6a, 0x1e, 0x3c, 0x77, 0xf1, 0x4c, 0x99, 0xf3, 0x2c, 0x36, 0xed, 0x2b, 0x50, 0x2f, 0x47,
	0x96, 0xee, 0x38, 0xff, 0x73, 0xe1, 0x10, 0xb2, 0x54, 0x93, 0x56, 0xce, 0xd5, 0xa4, 0x85, 0xb4,
	0x6a, 0x49, 0x9a, 0x6d, 0xc3, 0x5a, 0x94, 0xce, 0x38, 0x0d, 0x61, 0xb9, 0xd4, 0x76, 0x7e, 0x58,
	0x81, 0x1e, 0x0d, 0xb1, 0x17, 0xa5, 0x6c, 0xac, 0x2f, 0x6f, 0x30, 0x94, 0xfc, 0x40, 0x45, 0x3c,
	0x2d, 0x5f, 0x3e, 0x81, 0x86, 0xe8, 0xfe, 0xe9, 0x3a, 0x34, 0xe3, 0x28, 0x65, 0x5e, 0xc6, 0x65,
	0xbf, 0x4a, 0x37, 0x68, 0x0d, 0xec, 0x1f, 0x72, 0x69, 0x3b, 0xd0, 0xd1, 0xa4, 0x3c, 0xd8, 0x0b,
	0xff, 0xa6, 0xec, 0xd0, 0x04, 0xfc, 0x4d, 0x00, 0xe2, 0xc9, 0x8f, 0xab, 0x35, 0x8c, 0x44, 0x44,
	0x74, 0x6c, 0xff, 0xb9, 0x02, 0xc0, 0x4e, 0x3c, 0xfc, 0x9e, 0x8b, 0x39, 0x5d, 0xa6, 0x9a, 0xa5,
	0x6b, 0xb9, 0xd8, 0xa4, 0x2d, 0x45, 0xcc, 0x3d, 0xc9, 0xe2, 0x99, 0x67, 0x4c, 0x66, 0xb9, 0x16,
	0x17, 0xf3, 0x31, 0x8b, 0x67, 0xc3, 0xd0, 0x7e, 0x07, 0xba, 0x44, 0xd7, 0xd6, 0xf7, 0x13, 0x66,
	0x6e, 0x1f, 0xdb, 0xc8, 0x42, 0x36, 0xf4, 0x13, 0x86, 0x33, 0x45, 0xae, 0xd9, 0x22, 0x8e, 0x35,
	0x93, 0x4e, 0x3f, 0x28, 0xfa, 0xe1, 0x22, 0x8e, 0x89, 0xc7, 0x86, 0xb5, 0x94, 0xab, 0xfc, 0x76,
	0x8b, 0xda, 0xb9, 0xf4, 0x48, 0x7a, 0xcb, 0x48, 0xa8, 0x85, 0x1f, 0x9b, 0xf2, 0x06, 0xa5, 0x0f,
	0xe5, 0x63, 0x8d, 0xe1, 0x99, 0x26, 0x4c, 0x04, 0x4e, 0xcf, 0xd2, 0x77, 0x93, 0x61, 0x22, 0x86,
	0x21, 0x0e, 0x9a, 0xf9, 0x82, 0xa5, 0xca, 0x23, 0x19, 0xa1, 0xb9, 0xf8, 0x6d, 0x69, 0xf0, 0x40,
	0xcc, 0x87, 0x94, 0x88, 0x3b, 0x46, 0x7f, 0x1d, 0x72, 0x28, 0xcc, 0xb0, 0x6b, 0x5d, 0xeb, 0x38,
	0x14, 0x5d, 0x8d, 0xe4, 0x36, 0xd0, 0xeb, 0x64, 0x5d, 0x6a, 0x03, 0x9c, 0x0d, 0x5e, 0x2f, 0x0f,
	0x7e, 0x1d, 0x9a, 0xcb, 0x48, 0xe0, 0xc8, 0x32, 0xcf, 0xb5, 0xcb, 0x48, 0x1c, 0x88, 0xb9, 0x2c,
	0x14, 0x6d, 0x96, 0x14, 0x3d, 0xbb, 0x21, 0x2e, 0x15, 0x78, 0xe6, 0x86, 0xf8, 0xa5, 0x5d, 0xb0,
	0x7d, 0x7e, 0x17, 0x74, 0xfe, 0x15, 0x7a, 0xb4, 0x7b, 0x49, 0x26, 0x22, 0x3f, 0xa6, 0x40, 0xc2,
	0x41, 0x4c, 0x40, 0x61, 0xc8, 0x50, 0xdb, 0xf9, 0x14, 0x6c, 0xad, 0xab, 0x8e, 0x08, 0x12, 0x2e,
	0x4b, 0x0a, 0x68, 0x5e, 0xa3, 0xc0, 0x3b, 0xd0, 0xa5, 0x8b, 0x47, 0xaa, 0x25, 0xa8, 0xbc, 0xc2,
	0xe8, 0xab, 0xb9, 0x6d, 0x44, 0x71, 0x5f, 0xa4, 0x0a, 0xeb, 0x57, 0x15, 0x68, 0x46, 0x5c, 0x73,
	0xe5, 0x97, 0xf3, 0x18, 0x3d, 0x97, 0xf5, 0xe5, 0x3c, 0xee, 0x38, 0x6c, 0xa9, 0x75, 0xaa, 0x12,
	0xdc, 0x08, 0xd9, 0x92, 0x14, 0xc2, 0x61, 0xd9, 0x32, 0xb7, 0x73, 0xdb, 0xad, 0x87, 0x6c, 0x39,
	0xd4, 0x35, 0x56, 0x12, 0xd2, 0x8d, 0x70, 0x42, 0x96, 0x6e, 0xbb, 0xcd, 0x20, 0x09, 0x0f, 0xb1,
	0x8f, 0xc1, 0x28, 0x58, 0xb0, 0xf4, 0xcc, 0x87, 0xa6, 0xfe, 0x41, 0x68, 0x37, 0xff, 0x98, 0xe8,
	0x34, 0x5d, 0xdc, 0xa8, 0x6d, 0xb7, 0x89, 0x00, 0xdd, 0xca, 0xbe, 0x0b, 0x1b, 0x4f, 0x83, 0xcc,
	0x2b, 0x0b, 0xd0, 0x01, 0xd1, 0x7e, 0x1a, 0x64, 0x6e, 0x2e, 0xc3, 0x79, 0x06, 0x1d, 0xa4, 0x92,
	0xda, 0xb8, 0x6c, 0x4b, 0x96, 0xac, 0xe4, 0x96, 0xc4, 0x81, 0xa4, 0x08, 0x4a, 0x89, 0xad, 0xe3,
	0x36, 0xa5, 0x08, 0x8a, 0x32, 0x31, 0x94, 0xaa, 0xb4, 0x7b, 0x75, 0xdc, 0x66, 0x28, 0xcd, 0xee,
	0x66, 0xc3, 0x1a, 0x15, 0x19, 0x5a, 0x35, 0x6a, 0x3b, 0x53, 0xb0, 0xfc, 0x30, 0x14, 0xde, 0x94,
	0xf3, 0xa7, 0xc8, 0x40, 0xe6, 0x32, 0xc3, 0x61, 0xbb, 0x98, 0x42, 0xb5, 0x34, 0x85, 0xfc, 0x95,
	0xa3, 0xb6, 0xe2, 0x95, 0x63, 0xed, 0xdc, 0x2b, 0x87, 0xf3, 0x08, 0xba, 0xc5, 0x18, 0x1e, 0x6e,
	0x00, 0xf6, 0x7f, 0xbe, 0x88, 0x98, 0xc4, 0xde, 0x33, 0x89, 0xbd, 0x20, 0xba, 0x6d, 0x6c, 0xde,
	0xe7, 0xfc, 0xe9, 0x5e, 0x24, 0x95, 0xf3, 0x87, 0x0a, 0x34, 0xa7, 0xc1, 0x7f, 0xdc, 0xf1, 0x04,
	0x7b, 0x46, 0xba, 0xa2, 0xb5, 0x4a, 0x3b, 0x3e, 0x7a, 0xbc, 0x78, 0xf6, 0x98, 0xe3, 0x26, 0x73,
	0x7e, 0x9b, 0x24, 0xa8, 0x30, 0x86, 0x88, 0xa4, 0x34, 0x49, 0x9f, 0xda, 0x74, 0x95, 0xa7, 0x4f,
	0x1f, 0xda, 0x44, 0xa6, 0x77, 0x76, 0x71, 0xcc, 0x96, 0x2c, 0x55, 0xe7, 0x2e, 0x8e, 0x07, 0x88,
	0xd8, 0xdb, 0xd0, 0xa3, 0x80, 0x2d, 0x73, 0x99, 0x67, 0x0e, 0xc4, 0x0f, 0xcf, 0x38, 0xfb, 0xd0,
	0x10, 0xdc, 0x4f, 0xa2, 0x74, 0x6e, 0x6a, 0xc5, 0xbc, 0x8b, 0x06, 0x9c, 0x4a, 0x2f, 0x4a, 0x43,
	0x76, 0x62, 0x32, 0x4d, 0x63, 0x2a, 0x87, 0xd8, 0x75, 0x3e, 0x07, 0xcb, 0x68, 0x2d, 0xb3, 0x8b,
	0xd5, 0x2e, 0x3f, 0x26, 0x54, 0xcf, 0x3f, 0x26, 0xe0, 0x3e, 0x75, 0x2a, 0x75, 0x7c, 0xea, 0x17,
	0x85, 0x86, 0x3c, 0x95, 0xb4, 0x92, 0x12, 0xd8, 0xb8, 0x4f, 0xaf, 0x64, 0x24, 0x64, 0x88, 0x91,
	0x77, 0x05, 0xea, 0xbb, 0x89, 0x18, 0xee, 0x9a, 0x11, 0x74, 0x07, 0x8d, 0x36, 0x3a, 0x7b, 0x45,
	0xa2, 0x36, 0x6a, 0x74, 0x20, 0xe6, 0x47, 0x47, 0xc3, 0xdd, 0xdc, 0xef, 0xa6, 0x8b, 0xe6, 0xdc,
	0x3f, 0x25, 0x82, 0x4e, 0x4f, 0xa6, 0xe7, 0xfc, 0xb1, 0x06, 0x1b, 0x09, 0x93, 0xc7, 0xde, 0x3c,
	0x93, 0x14, 0xe7, 0x9e, 0xa2, 0xa8, 0xd6, 0xc5, 0x91, 0xd1, 0x0a, 0xa3, 0x9a, 0x00, 0xbd, 0xb6,
	0x4c, 0xe5, 0x76, 0x16, 0xf2, 0x1a, 0x18, 0x92, 0x23, 0x8f, 0xf9, 0x42, 0x98, 0x68, 0xa7, 0x36,
	0x8e, 0x9c, 0x44, 0xe9, 0x42, 0xe9, 0x17, 0x92, 0x8e, 0x6b, 0x7a, 0xe4, 0x60, 0x16, 0xf0, 0x54,
	0x27, 0xcc, 0x8e, 0x6b, 0x7a, 0x98, 0x3d, 0x42, 0x5f, 0x97, 0xf8, 0x1d, 0x17, 0x9b, 0xa8, 0x7f,
	0xc2, 0x53, 0x75, 0x4c, 0x5e, 0xea, 0xb8, 0xba, 0x83, 0x63, 0x9d, 0x32, 0x5f, 0xdf, 0x00, 0x77,
	0x5c, 0x6a, 0x63, 0x15, 0xe4, 0x2f, 0xfd, 0x28, 0xf6, 0xa7, 0xb1, 0x4e, 0x9e, 0x1d, 0xf7, 0x0c,
	0xc0, 0xdc, 0x19, 0xfb, 0x2a, 0x52, 0x8b, 0x50, 0x67, 0x85, 0x8e, 0x5b, 0xf4, 0x6d, 0x07, 0xda,
	0x29, 0x17, 0xea, 0xf8, 0x40, 0x8c, 0xf9, 0x42, 0x1d, 0x53, 0x4a, 0xe8, 0xb8, 0xe7, 0x30, 0x94,
	0x1e, 0xf3, 0x74, 0xae, 0x05, 0xb4, 0xb5, 0xf4, 0x02, 0xb0, 0x37, 0x01, 0x98, 0x2f, 0xd5, 0x81,
	0xf8, 0x8c, 0x49, 0xfd, 0x4c, 0xd8, 0x71, 0x4b, 0x08, 0x6a, 0x21, 0x33, 0xc6, 0x74, 0x89, 0xd9,
	0x71, 0x75, 0x07, 0x65, 0x86, 0x91, 0x60, 0xb4, 0xcf, 0xd3, 0x09, 0xb7, 0xe3, 0x9e, 0x01, 0x38,
	0x63, 0x3f, 0x36, 0x33, 0xee, 0x99, 0xfb, 0x7d, 0xd3, 0xb7, 0x3f, 0x82, 0x0e, 0xfa, 0x4c, 0xe7,
	0x65, 0xcc, 0x14, 0x97, 0xb7, 0x2a, 0xdb, 0xdd, 0x7b, 0xd7, 0xcd, 0xb2, 0xdd, 0x1f, 0x8c, 0x1f,
	0x79, 0x1f, 0x1f, 0x8e, 0xbd, 0xdd, 0x9d, 0xc9, 0x8e, 0x37, 0x79, 0x72, 0x38, 0x70, 0x5b, 0xf3,
	0x8c, 0x0e, 0x80, 0x98, 0x77, 0x9d, 0x23, 0x68, 0xed, 0x33, 0x79, 0xfc, 0x71, 0x26, 0x2f, 0x88,
	0xb1, 0x3b, 0xd0, 0x30, 0x0c, 0xe4, 0xea, 0xd6, 0xbd, 0x6b, 0x46, 0xfa, 0x0b, 0x21, 0xe3, 0xe6,
	0x6c, 0xce, 0x8f, 0x2b, 0x00, 0x87, 0x3c, 0x78, 0xc0, 0x93, 0xc4, 0x4f, 0xc3, 0xf2, 0x56, 0x60,
	0xde, 0x69, 0xcf, 0x3f, 0xf1, 0xd6, 0xf2, 0x27, 0xde, 0xbf, 0x2f, 0x8d, 0x9d, 0x7b, 0xac, 0xad,
	0xaf, 0x7e, 0xac, 0x5d, 0x3f, 0xff, 0x58, 0xfb, 0x65, 0x05, 0x36, 0x0e, 0x79, 0xb0, 0xcb, 0x66,
	0xfe, 0x22, 0x56, 0xba, 0xf0, 0xfd, 0x10, 0xfa, 0xa1, 0xee, 0xd3, 0xfd, 0xbc, 0x57, 0xce, 0x54,
	0xda, 0x16, 0x57, 0x0d, 0x7d, 0x9c, 0x97, 0xd3, 0x7a, 0x79, 0x7f, 0x04, 0x37, 0xf2, 0x0f, 0x75,
	0x21, 0x5d, 0xfe, 0x54, 0xd7, 0x68, 0x0d, 0x37, 0x97, 0xbd, 0x77, 0x56, 0x6a, 0xd3, 0xd7, 0xd2,
	0x39, 0x82, 0xeb, 0x87, 0x3c, 0x18, 0x2f, 0xa6, 0x32, 0x10, 0xd1, 0x34, 0x66, 0xfa, 0xe9, 0xce,
	0xbc, 0x95, 0x96, 0x8f, 0xa8, 0xe6, 0x1d, 0x32, 0x3f, 0xa2, 0xde, 0x04, 0xd0, 0xa4, 0x62, 0x33,
	0xad, 0xbb, 0x16, 0x21, 0xe4, 0xd6, 0xcf, 0xc1, 0x22, 0xf3, 0xa7, 0xb3, 0x68, 0x8e, 0x53, 0x0c,
	0xfc, 0xd4, 0x63, 0x61, 0xa4, 0x3c, 0xa9, 0x47, 0x22, 0xf9, 0x5e, 0xcc, 0x03, 0x3f, 0x36, 0x92,
	0xfb, 0x81, 0x9f, 0x0e, 0xc2, 0x48, 0x8d, 0x4b, 0x0c, 0x7b, 0x48, 0xa7, 0xb4, 0x3c, 0xcf, 0x64,
	0xbe, 0xdd, 0x60, 0xfb, 0xdf, 0x7e, 0x54, 0x05, 0xfb, 0xe5, 0xd0, 0xb2, 0x5b, 0xd0, 0x18, 0x4f,
	0xbc, 0xd1, 0xc1, 0x68, 0xd0, 0xbb, 0x64, 0xbf, 0x01, 0x1b, 0xe3, 0x89, 0xf7, 0xf0, 0x68, 0xf4,
	0x60, 0x32, 0x3c, 0x18, 0x79, 0x9f, 0x0c, 0x9e, 0xf4, 0x2a, 0xf6, 0x06, 0xb4, 0xc6, 0x13, 0xef,
	0xf0, 0xe0, 0xb3, 0x81, 0xeb, 0x1d, 0x8c, 0x7a, 0x55, 0xbb, 0x07, 0xed, 0x33, 0xe0, 0xe1, 0xc3,
	0x5e, 0xcd, 0x08, 0x99, 0x0c, 0xf7, 0x07, 0xbd, 0x35, 0xc3, 0xbf, 0x3b, 0x1c, 0x4f, 0x76, 0x46,
	0x0f, 0x06, 0xbd, 0xba, 0x7d, 0x05, 0x7a, 0x65, 0xa9, 0xfb, 0x83, 0xd1, 0x51, 0x6f, 0xdd, 0xee,
	0x80, 0x85, 0x52, 0x3e, 0xdb, 0x1d, 0xb8, 0x6e, 0xaf, 0x61, 0x5f, 0x85, 0xcb, 0xf8, 0xd5, 0xe0,
	0xf1, 0xf0, 0xc1, 0x00, 0x3f, 0xde, 0xb9, 0xbf, 0x37, 0xe8, 0x35, 0x0d, 0xec, 0x0e, 0xf6, 0x0f,
	0x26, 0x03, 0x6f, 0xff, 0x60, 0x34, 0x9c, 0x1c, 0xb8, 0x3d, 0xcb, 0xbe, 0x0c, 0x1d, 0xfc, 0x78,
	0x32, 0xf1, 0xee, 0x0f, 0x9e, 0x1c, 0x8c, 0x76, 0x7b, 0x60, 0xdb, 0xd0, 0x1d, 0x4f, 0xbc, 0xbd,
	0xe1, 0xe8, 0x93, 0x1c, 0x6b, 0x99, 0x99, 0xa2, 0xc2, 0x9f, 0x1e, 0x0d, 0xdc, 0x27, 0xbd, 0xb6,
	0x99, 0xdc, 0xe4, 0xbf, 0xbd, 0x9d, 0xbd, 0x1d, 0x77, 0xbf, 0xd7, 0xb9, 0xff, 0xd6, 0x2f, 0xbe,
	0xde, 0xac, 0x7c, 0xf5, 0xf5, 0x66, 0xe5, 0xb7, 0x5f, 0x6f, 0x56, 0x7e, 0xf0, 0xcd, 0xe6, 0xa5,
	0xaf, 0xbe, 0xd9, 0xbc, 0xf4, 0x9b, 0x6f, 0x36, 0x2f, 0x3d, 0xaa, 0x4d, 0xd7, 0xe9, 0x77, 0x87,
	0xf7, 0xff, 0x1a, 0x00, 0x00, 0xff, 0xff, 0xa0, 0xc4, 0x1c, 0xd5, 0xfe, 0x20, 0x00, 0x00,
}

func (m *RpcCmd) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RpcCmd) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RpcCmd) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ParaInt != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.ParaInt))
		i--
		dAtA[i] = 0x68
	}
	if len(m.ParaBin) > 0 {
		i -= len(m.ParaBin)
		copy(dAtA[i:], m.ParaBin)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ParaBin)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.ParaStr) > 0 {
		i -= len(m.ParaStr)
		copy(dAtA[i:], m.ParaStr)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ParaStr)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x52
	}
	if m.Res != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Res))
		i--
		dAtA[i] = 0x40
	}
	if m.Cmd != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Cmd))
		i--
		dAtA[i] = 0x28
	}
	if m.Sid != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Sid))
		i--
		dAtA[i] = 0x18
	}
	if m.SeqNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SeqNo))
		i--
		dAtA[i] = 0x10
	}
	return len(dAtA) - i, nil
}

func (m *Login) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Login) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Login) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Codec) > 0 {
		dAtA2 := make([]byte, len(m.Codec)*10)
		var j1 int
		for _, num1 := range m.Codec {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		i -= j1
		copy(dAtA[i:], dAtA2[:j1])
		i = encodeVarintBf8100(dAtA, i, uint64(j1))
		i--
		dAtA[i] = 0x52
	}
	if len(m.ExtraOption) > 0 {
		dAtA4 := make([]byte, len(m.ExtraOption)*10)
		var j3 int
		for _, num1 := range m.ExtraOption {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA4[j3] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j3++
			}
			dAtA4[j3] = uint8(num)
			j3++
		}
		i -= j3
		copy(dAtA[i:], dAtA4[:j3])
		i = encodeVarintBf8100(dAtA, i, uint64(j3))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.SysId) > 0 {
		i -= len(m.SysId)
		copy(dAtA[i:], m.SysId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.SysId)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.TimeStr) > 0 {
		i -= len(m.TimeStr)
		copy(dAtA[i:], m.TimeStr)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.TimeStr)))
		i--
		dAtA[i] = 0x3a
	}
	if m.PasswordMethod != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.PasswordMethod))
		i--
		dAtA[i] = 0x30
	}
	if len(m.Password) > 0 {
		i -= len(m.Password)
		copy(dAtA[i:], m.Password)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Password)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.DeviceModel) > 0 {
		i -= len(m.DeviceModel)
		copy(dAtA[i:], m.DeviceModel)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.DeviceModel)))
		i--
		dAtA[i] = 0x22
	}
	if m.LoginType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.LoginType))
		i--
		dAtA[i] = 0x18
	}
	if len(m.DeviceName) > 0 {
		i -= len(m.DeviceName)
		copy(dAtA[i:], m.DeviceName)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.DeviceName)))
		i--
		dAtA[i] = 0x12
	}
	if m.DeviceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DeviceDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *ResLoginParaBin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResLoginParaBin) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResLoginParaBin) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsHaveFullCallPerm != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.IsHaveFullCallPerm))
		i--
		dAtA[i] = 0x18
	}
	if len(m.ImbeSn) > 0 {
		i -= len(m.ImbeSn)
		copy(dAtA[i:], m.ImbeSn)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ImbeSn)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ValidSnCode) > 0 {
		i -= len(m.ValidSnCode)
		copy(dAtA[i:], m.ValidSnCode)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ValidSnCode)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ResLogin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResLogin) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResLogin) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SettingLastUpdateTime) > 0 {
		i -= len(m.SettingLastUpdateTime)
		copy(dAtA[i:], m.SettingLastUpdateTime)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.SettingLastUpdateTime)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.ServerVersion) > 0 {
		i -= len(m.ServerVersion)
		copy(dAtA[i:], m.ServerVersion)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ServerVersion)))
		i--
		dAtA[i] = 0x32
	}
	if m.HttpPort != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.HttpPort))
		i--
		dAtA[i] = 0x28
	}
	if m.HangupTime != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.HangupTime))
		i--
		dAtA[i] = 0x20
	}
	if m.Sid != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Sid))
		i--
		dAtA[i] = 0x18
	}
	if m.ResCode != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.ResCode))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ResRepeaterState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResRepeaterState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResRepeaterState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AntValue != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.AntValue))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if m.Signal != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Signal))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if m.FanErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.FanErr))
		i--
		dAtA[i] = 0x78
	}
	if m.TxPllErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TxPllErr))
		i--
		dAtA[i] = 0x70
	}
	if m.RxPllErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.RxPllErr))
		i--
		dAtA[i] = 0x68
	}
	if m.VolErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.VolErr))
		i--
		dAtA[i] = 0x60
	}
	if m.GpsErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.GpsErr))
		i--
		dAtA[i] = 0x58
	}
	if m.AntErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.AntErr))
		i--
		dAtA[i] = 0x50
	}
	if m.TmpErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TmpErr))
		i--
		dAtA[i] = 0x48
	}
	if m.TmpValue != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TmpValue))
		i--
		dAtA[i] = 0x40
	}
	if m.VolValue != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.VolValue))
		i--
		dAtA[i] = 0x38
	}
	if m.IpAddr != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.IpAddr))
		i--
		dAtA[i] = 0x35
	}
	if m.PowerValue != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.PowerValue))
		i--
		dAtA[i] = 0x28
	}
	if m.TxFrequency != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TxFrequency))
		i--
		dAtA[i] = 0x25
	}
	if m.RxFrequency != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RxFrequency))
		i--
		dAtA[i] = 0x1d
	}
	if m.ChannelId != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.ChannelId))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DeviceDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *RepeaterErrStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RepeaterErrStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RepeaterErrStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Signal != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Signal))
		i--
		dAtA[i] = 0x48
	}
	if m.FanErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.FanErr))
		i--
		dAtA[i] = 0x40
	}
	if m.TxPllErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TxPllErr))
		i--
		dAtA[i] = 0x38
	}
	if m.RxPllErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.RxPllErr))
		i--
		dAtA[i] = 0x30
	}
	if m.VolErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.VolErr))
		i--
		dAtA[i] = 0x28
	}
	if m.GpsErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.GpsErr))
		i--
		dAtA[i] = 0x20
	}
	if m.AntErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.AntErr))
		i--
		dAtA[i] = 0x18
	}
	if m.TmpErr != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TmpErr))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DeviceDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *DeviceSend) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeviceSend) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeviceSend) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SourceRepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceRepeaterDmrid))
		i--
		dAtA[i] = 0x25
	}
	if len(m.Fsk) > 0 {
		i -= len(m.Fsk)
		copy(dAtA[i:], m.Fsk)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Fsk)))
		i--
		dAtA[i] = 0x1a
	}
	if m.RepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RepeaterDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *ServerSend) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServerSend) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServerSend) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Fsk) > 0 {
		i -= len(m.Fsk)
		copy(dAtA[i:], m.Fsk)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Fsk)))
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}

func (m *Bc71) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bc71) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bc71) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.PreferInterruptTargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.PreferInterruptTargetDmrid))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x85
	}
	if m.SourceRepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceRepeaterDmrid))
		i--
		dAtA[i] = 0x7d
	}
	if m.CallDuplex != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.CallDuplex))
		i--
		dAtA[i] = 0x70
	}
	if len(m.PhoneNo) > 0 {
		i -= len(m.PhoneNo)
		copy(dAtA[i:], m.PhoneNo)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.PhoneNo)))
		i--
		dAtA[i] = 0x52
	}
	if m.SoundType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SoundType))
		i--
		dAtA[i] = 0x48
	}
	if m.Priority != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x40
	}
	if m.TimeSlotNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TimeSlotNo))
		i--
		dAtA[i] = 0x38
	}
	if m.SupportAnalog != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportAnalog))
		i--
		dAtA[i] = 0x30
	}
	if m.SupportDigital != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportDigital))
		i--
		dAtA[i] = 0x28
	}
	if m.FieldIntensity != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.FieldIntensity))
		i--
		dAtA[i] = 0x20
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.RepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RepeaterDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *Cb71) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Cb71) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Cb71) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SourceRepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceRepeaterDmrid))
		i--
		dAtA[i] = 0x75
	}
	if m.CallDuplex != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.CallDuplex))
		i--
		dAtA[i] = 0x68
	}
	if len(m.PhoneNo) > 0 {
		i -= len(m.PhoneNo)
		copy(dAtA[i:], m.PhoneNo)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.PhoneNo)))
		i--
		dAtA[i] = 0x62
	}
	if m.SoundType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SoundType))
		i--
		dAtA[i] = 0x58
	}
	if m.InterruptDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.InterruptDmrid))
		i--
		dAtA[i] = 0x55
	}
	if m.Result != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Result))
		i--
		dAtA[i] = 0x48
	}
	if m.Priority != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x40
	}
	if m.TimeSlotNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TimeSlotNo))
		i--
		dAtA[i] = 0x38
	}
	if m.SupportAnalog != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportAnalog))
		i--
		dAtA[i] = 0x30
	}
	if m.SupportDigital != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportDigital))
		i--
		dAtA[i] = 0x28
	}
	if m.ReqNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.ReqNo))
		i--
		dAtA[i] = 0x20
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.RepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RepeaterDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *Bc73) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bc73) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bc73) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SourceRepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceRepeaterDmrid))
		i--
		dAtA[i] = 0x2d
	}
	if m.TimeSlotNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TimeSlotNo))
		i--
		dAtA[i] = 0x20
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.RepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RepeaterDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *Cb75) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Cb75) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Cb75) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Reason != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Reason))
		i--
		dAtA[i] = 0x28
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	return len(dAtA) - i, nil
}

func (m *Bc15) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bc15) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bc15) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StartTime != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.StartTime))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x81
	}
	if m.SourceRepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceRepeaterDmrid))
		i--
		dAtA[i] = 0x7d
	}
	if m.CallDuplex != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.CallDuplex))
		i--
		dAtA[i] = 0x70
	}
	if len(m.PhoneNo) > 0 {
		i -= len(m.PhoneNo)
		copy(dAtA[i:], m.PhoneNo)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.PhoneNo)))
		i--
		dAtA[i] = 0x6a
	}
	if m.SoundType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SoundType))
		i--
		dAtA[i] = 0x60
	}
	if m.CallStatus != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.CallStatus))
		i--
		dAtA[i] = 0x58
	}
	if m.SupportInterrupt != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportInterrupt))
		i--
		dAtA[i] = 0x50
	}
	if m.CallType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.CallType))
		i--
		dAtA[i] = 0x48
	}
	if m.Priority != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x40
	}
	if m.TimeSlotNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TimeSlotNo))
		i--
		dAtA[i] = 0x38
	}
	if m.SupportAnalog != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportAnalog))
		i--
		dAtA[i] = 0x30
	}
	if m.SupportDigital != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportDigital))
		i--
		dAtA[i] = 0x28
	}
	if m.FieldIntensity != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.FieldIntensity))
		i--
		dAtA[i] = 0x20
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.RepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RepeaterDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *Bc10) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bc10) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bc10) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.OpusData_2) > 0 {
		i -= len(m.OpusData_2)
		copy(dAtA[i:], m.OpusData_2)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.OpusData_2)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.OpusData_1) > 0 {
		i -= len(m.OpusData_1)
		copy(dAtA[i:], m.OpusData_1)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.OpusData_1)))
		i--
		dAtA[i] = 0x4a
	}
	if m.FrameNo != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.FrameNo))
		i--
		dAtA[i] = 0x45
	}
	if m.SupportInterrupt != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportInterrupt))
		i--
		dAtA[i] = 0x38
	}
	if m.Priority != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x30
	}
	if m.CallType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.CallType))
		i--
		dAtA[i] = 0x28
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	return len(dAtA) - i, nil
}

func (m *Bc30) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bc30) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bc30) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SourceRepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceRepeaterDmrid))
		i--
		dAtA[i] = 0x5d
	}
	if m.SoundType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SoundType))
		i--
		dAtA[i] = 0x50
	}
	if len(m.AmbeData) > 0 {
		i -= len(m.AmbeData)
		copy(dAtA[i:], m.AmbeData)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.AmbeData)))
		i--
		dAtA[i] = 0x4a
	}
	if m.FrameNo != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.FrameNo))
		i--
		dAtA[i] = 0x45
	}
	if m.SupportInterrupt != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SupportInterrupt))
		i--
		dAtA[i] = 0x38
	}
	if m.Priority != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x30
	}
	if m.CallType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.CallType))
		i--
		dAtA[i] = 0x28
	}
	if m.TimeSlotNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TimeSlotNo))
		i--
		dAtA[i] = 0x20
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.RepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RepeaterDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *Dtmf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Dtmf) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Dtmf) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SourceRepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceRepeaterDmrid))
		i--
		dAtA[i] = 0x3d
	}
	if len(m.DtmfStr) > 0 {
		i -= len(m.DtmfStr)
		copy(dAtA[i:], m.DtmfStr)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.DtmfStr)))
		i--
		dAtA[i] = 0x32
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.RepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RepeaterDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *EndCall) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EndCall) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EndCall) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SourceRepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceRepeaterDmrid))
		i--
		dAtA[i] = 0x75
	}
	if len(m.PhoneNo) > 0 {
		i -= len(m.PhoneNo)
		copy(dAtA[i:], m.PhoneNo)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.PhoneNo)))
		i--
		dAtA[i] = 0x6a
	}
	if m.SoundType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SoundType))
		i--
		dAtA[i] = 0x60
	}
	if m.SourceDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.SourceDmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.TargetDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TargetDmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.RepeaterDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.RepeaterDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *PhoneTransfer) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhoneTransfer) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PhoneTransfer) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TransferTarget != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.TransferTarget))
		i--
		dAtA[i] = 0x25
	}
	if m.NowTarget != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.NowTarget))
		i--
		dAtA[i] = 0x1d
	}
	if m.PhoneDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.PhoneDmrid))
		i--
		dAtA[i] = 0x15
	}
	return len(dAtA) - i, nil
}

func (m *OneChannelItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OneChannelItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OneChannelItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ZoneRid) > 0 {
		i -= len(m.ZoneRid)
		copy(dAtA[i:], m.ZoneRid)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ZoneRid)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ListenGroup) > 0 {
		for iNdEx := len(m.ListenGroup) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ListenGroup[iNdEx])
			copy(dAtA[i:], m.ListenGroup[iNdEx])
			i = encodeVarintBf8100(dAtA, i, uint64(len(m.ListenGroup[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.SendGroup) > 0 {
		i -= len(m.SendGroup)
		copy(dAtA[i:], m.SendGroup)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.SendGroup)))
		i--
		dAtA[i] = 0x12
	}
	if m.No != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.No))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Channels) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Channels) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Channels) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DevicePriority != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.DevicePriority))
		i--
		dAtA[i] = 0x18
	}
	if len(m.DeviceDmrid) > 0 {
		i -= len(m.DeviceDmrid)
		copy(dAtA[i:], m.DeviceDmrid)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.DeviceDmrid)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Channels) > 0 {
		for iNdEx := len(m.Channels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Channels[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBf8100(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *PhoneAdapterShortNo2DmridReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhoneAdapterShortNo2DmridReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PhoneAdapterShortNo2DmridReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Dmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.Dmrid))
		i--
		dAtA[i] = 0x1d
	}
	if m.Opt != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Opt))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ShortNo) > 0 {
		i -= len(m.ShortNo)
		copy(dAtA[i:], m.ShortNo)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ShortNo)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PhoneAdapterShortNo2DmridRes) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhoneAdapterShortNo2DmridRes) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PhoneAdapterShortNo2DmridRes) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Info) > 0 {
		i -= len(m.Info)
		copy(dAtA[i:], m.Info)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Info)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Dmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.Dmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.Result != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Result))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PhoneLineSetting) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhoneLineSetting) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PhoneLineSetting) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LineDmrid) > 0 {
		for iNdEx := len(m.LineDmrid) - 1; iNdEx >= 0; iNdEx-- {
			i -= 4
			encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.LineDmrid[iNdEx]))
		}
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.LineDmrid)*4))
		i--
		dAtA[i] = 0x22
	}
	if len(m.LinePhoneNo) > 0 {
		for iNdEx := len(m.LinePhoneNo) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.LinePhoneNo[iNdEx])
			copy(dAtA[i:], m.LinePhoneNo[iNdEx])
			i = encodeVarintBf8100(dAtA, i, uint64(len(m.LinePhoneNo[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.LinePos) > 0 {
		dAtA6 := make([]byte, len(m.LinePos)*10)
		var j5 int
		for _, num1 := range m.LinePos {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA6[j5] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j5++
			}
			dAtA6[j5] = uint8(num)
			j5++
		}
		i -= j5
		copy(dAtA[i:], dAtA6[:j5])
		i = encodeVarintBf8100(dAtA, i, uint64(j5))
		i--
		dAtA[i] = 0x12
	}
	if m.ActionCode != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.ActionCode))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ExOneOrg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExOneOrg) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExOneOrg) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ParentOrgId) > 0 {
		i -= len(m.ParentOrgId)
		copy(dAtA[i:], m.ParentOrgId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ParentOrgId)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.DmrId) > 0 {
		i -= len(m.DmrId)
		copy(dAtA[i:], m.DmrId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.DmrId)))
		i--
		dAtA[i] = 0x4a
	}
	if m.OrgIsVirtual != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.OrgIsVirtual))
		i--
		dAtA[i] = 0x40
	}
	if len(m.Note) > 0 {
		i -= len(m.Note)
		copy(dAtA[i:], m.Note)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Note)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.OrgFullName) > 0 {
		i -= len(m.OrgFullName)
		copy(dAtA[i:], m.OrgFullName)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.OrgFullName)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.OrgShortName) > 0 {
		i -= len(m.OrgShortName)
		copy(dAtA[i:], m.OrgShortName)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.OrgShortName)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.OrgSelfId) > 0 {
		i -= len(m.OrgSelfId)
		copy(dAtA[i:], m.OrgSelfId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.OrgSelfId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Rid) > 0 {
		i -= len(m.Rid)
		copy(dAtA[i:], m.Rid)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Rid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExOneDevice) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExOneDevice) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExOneDevice) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Priority != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x60
	}
	if m.DeviceType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.DeviceType))
		i--
		dAtA[i] = 0x48
	}
	if len(m.Note) > 0 {
		i -= len(m.Note)
		copy(dAtA[i:], m.Note)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Note)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.VirOrgs) > 0 {
		i -= len(m.VirOrgs)
		copy(dAtA[i:], m.VirOrgs)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.VirOrgs)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.DmrId) > 0 {
		i -= len(m.DmrId)
		copy(dAtA[i:], m.DmrId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.DmrId)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.SelfId) > 0 {
		i -= len(m.SelfId)
		copy(dAtA[i:], m.SelfId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.SelfId)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.OrgId) > 0 {
		i -= len(m.OrgId)
		copy(dAtA[i:], m.OrgId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.OrgId)))
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}

func (m *AmbeSerialCode) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AmbeSerialCode) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AmbeSerialCode) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Code) > 0 {
		dAtA8 := make([]byte, len(m.Code)*10)
		var j7 int
		for _, num1 := range m.Code {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA8[j7] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j7++
			}
			dAtA8[j7] = uint8(num)
			j7++
		}
		i -= j7
		copy(dAtA[i:], dAtA8[:j7])
		i = encodeVarintBf8100(dAtA, i, uint64(j7))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExOnelineDevices) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExOnelineDevices) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExOnelineDevices) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LastDataTime) > 0 {
		dAtA10 := make([]byte, len(m.LastDataTime)*10)
		var j9 int
		for _, num1 := range m.LastDataTime {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA10[j9] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j9++
			}
			dAtA10[j9] = uint8(num)
			j9++
		}
		i -= j9
		copy(dAtA[i:], dAtA10[:j9])
		i = encodeVarintBf8100(dAtA, i, uint64(j9))
		i--
		dAtA[i] = 0x12
	}
	if len(m.DmrId) > 0 {
		dAtA12 := make([]byte, len(m.DmrId)*10)
		var j11 int
		for _, num1 := range m.DmrId {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA12[j11] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j11++
			}
			dAtA12[j11] = uint8(num)
			j11++
		}
		i -= j11
		copy(dAtA[i:], dAtA12[:j11])
		i = encodeVarintBf8100(dAtA, i, uint64(j11))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *IotData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IotData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IotData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.KcpRecvDevId) > 0 {
		i -= len(m.KcpRecvDevId)
		copy(dAtA[i:], m.KcpRecvDevId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.KcpRecvDevId)))
		i--
		dAtA[i] = 0x5a
	}
	if m.RecvTime != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64((uint64(m.RecvTime)<<1)^uint64((m.RecvTime>>63))))
		i--
		dAtA[i] = 0x50
	}
	if len(m.RecvDevId) > 0 {
		i -= len(m.RecvDevId)
		copy(dAtA[i:], m.RecvDevId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.RecvDevId)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.CmdParam) > 0 {
		i -= len(m.CmdParam)
		copy(dAtA[i:], m.CmdParam)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.CmdParam)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.DevId) > 0 {
		i -= len(m.DevId)
		copy(dAtA[i:], m.DevId)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.DevId)))
		i--
		dAtA[i] = 0x1a
	}
	if m.DevType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64((uint32(m.DevType)<<1)^uint32((m.DevType>>31))))
		i--
		dAtA[i] = 0x10
	}
	if m.Cmd != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64((uint32(m.Cmd)<<1)^uint32((m.Cmd>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DevDataInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DevDataInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DevDataInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x22
	}
	if m.DstDmrid != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.DstDmrid))
		i--
		dAtA[i] = 0x18
	}
	if m.SrcDmrid != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SrcDmrid))
		i--
		dAtA[i] = 0x10
	}
	if m.Code != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AddrBook) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddrBook) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AddrBook) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ParaStr) > 0 {
		i -= len(m.ParaStr)
		copy(dAtA[i:], m.ParaStr)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ParaStr)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Code != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x10
	}
	if m.Type != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AddrBookList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddrBookList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AddrBookList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.AddrBookList) > 0 {
		for iNdEx := len(m.AddrBookList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.AddrBookList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBf8100(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Bc40Req) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bc40Req) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bc40Req) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.BsIndex != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.BsIndex))
		i--
		dAtA[i] = 0x40
	}
	if m.Roaming != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Roaming))
		i--
		dAtA[i] = 0x38
	}
	if m.LastPowerEvent != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.LastPowerEvent))
		i--
		dAtA[i] = 0x30
	}
	if m.PowerEvent != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.PowerEvent))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0x22
	}
	if m.Riss != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Riss))
		i--
		dAtA[i] = 0x18
	}
	if m.GroupDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.GroupDmrid))
		i--
		dAtA[i] = 0x15
	}
	if m.DevDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DevDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *Bc40Resp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bc40Resp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bc40Resp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SysTime != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SysTime))
		i--
		dAtA[i] = 0x18
	}
	if m.ResCode != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.ResCode))
		i--
		dAtA[i] = 0x10
	}
	if m.DevDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DevDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *Bf8100DmridInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bf8100DmridInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bf8100DmridInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.MyUUID) > 0 {
		i -= len(m.MyUUID)
		copy(dAtA[i:], m.MyUUID)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.MyUUID)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.OrgUUID) > 0 {
		i -= len(m.OrgUUID)
		copy(dAtA[i:], m.OrgUUID)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.OrgUUID)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if m.DmrID != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DmrID))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *MeshGpsInfoT) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MeshGpsInfoT) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MeshGpsInfoT) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.GpsDataType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.GpsDataType))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if m.Altitude != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Altitude))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if m.Direction != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Direction))
		i--
		dAtA[i] = 0x78
	}
	if m.Speed != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Speed))
		i--
		dAtA[i] = 0x70
	}
	if m.EastOrWest != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.EastOrWest))
		i--
		dAtA[i] = 0x68
	}
	if m.Longitude != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Longitude))
		i--
		dAtA[i] = 0x60
	}
	if m.NorthOrSouth != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.NorthOrSouth))
		i--
		dAtA[i] = 0x58
	}
	if m.Latitude != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Latitude))
		i--
		dAtA[i] = 0x50
	}
	if m.Available != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Available))
		i--
		dAtA[i] = 0x48
	}
	if m.Year != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Year))
		i--
		dAtA[i] = 0x40
	}
	if m.Month != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Month))
		i--
		dAtA[i] = 0x38
	}
	if m.Day != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Day))
		i--
		dAtA[i] = 0x30
	}
	if m.Second != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Second))
		i--
		dAtA[i] = 0x28
	}
	if m.Minute != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Minute))
		i--
		dAtA[i] = 0x20
	}
	if m.Hour != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Hour))
		i--
		dAtA[i] = 0x18
	}
	if m.TargetId != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.TargetId))
		i--
		dAtA[i] = 0x10
	}
	if m.SourceId != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SourceId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MeshGpsInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MeshGpsInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MeshGpsInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.GpsInfo != nil {
		{
			size, err := m.GpsInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBf8100(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.DmrID != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DmrID))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *PocCommand) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PocCommand) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PocCommand) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ParaInt != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.ParaInt))
		i--
		dAtA[i] = 0x30
	}
	if len(m.ParaBin) > 0 {
		i -= len(m.ParaBin)
		copy(dAtA[i:], m.ParaBin)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ParaBin)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.ParaStr) > 0 {
		i -= len(m.ParaStr)
		copy(dAtA[i:], m.ParaStr)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.ParaStr)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x1a
	}
	if m.SeqNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.SeqNo))
		i--
		dAtA[i] = 0x10
	}
	if m.Cmd != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Cmd))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PocDefaultGroup) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PocDefaultGroup) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PocDefaultGroup) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DefaultListenGroupDmrids) > 0 {
		for iNdEx := len(m.DefaultListenGroupDmrids) - 1; iNdEx >= 0; iNdEx-- {
			i -= 4
			encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DefaultListenGroupDmrids[iNdEx]))
		}
		i = encodeVarintBf8100(dAtA, i, uint64(len(m.DefaultListenGroupDmrids)*4))
		i--
		dAtA[i] = 0x12
	}
	if m.DefaultSendGroupDmrid != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.DefaultSendGroupDmrid))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *PocSubscribleUpdateOption) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PocSubscribleUpdateOption) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PocSubscribleUpdateOption) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.FrameType != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.FrameType))
		i--
		dAtA[i] = 0x10
	}
	if m.FrameNo != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.FrameNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PocConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PocConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PocConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Rgps != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.Rgps))
		i--
		dAtA[i] = 0x10
	}
	if m.CanEditSubscriptionLocal != 0 {
		i = encodeVarintBf8100(dAtA, i, uint64(m.CanEditSubscriptionLocal))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintBf8100(dAtA []byte, offset int, v uint64) int {
	offset -= sovBf8100(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *RpcCmd) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SeqNo != 0 {
		n += 1 + sovBf8100(uint64(m.SeqNo))
	}
	if m.Sid != 0 {
		n += 1 + sovBf8100(uint64(m.Sid))
	}
	if m.Cmd != 0 {
		n += 1 + sovBf8100(uint64(m.Cmd))
	}
	if m.Res != 0 {
		n += 1 + sovBf8100(uint64(m.Res))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.ParaStr)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.ParaBin)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.ParaInt != 0 {
		n += 1 + sovBf8100(uint64(m.ParaInt))
	}
	return n
}

func (m *Login) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceDmrid != 0 {
		n += 5
	}
	l = len(m.DeviceName)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.LoginType != 0 {
		n += 1 + sovBf8100(uint64(m.LoginType))
	}
	l = len(m.DeviceModel)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.Password)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.PasswordMethod != 0 {
		n += 1 + sovBf8100(uint64(m.PasswordMethod))
	}
	l = len(m.TimeStr)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.SysId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if len(m.ExtraOption) > 0 {
		l = 0
		for _, e := range m.ExtraOption {
			l += sovBf8100(uint64(e))
		}
		n += 1 + sovBf8100(uint64(l)) + l
	}
	if len(m.Codec) > 0 {
		l = 0
		for _, e := range m.Codec {
			l += sovBf8100(uint64(e))
		}
		n += 1 + sovBf8100(uint64(l)) + l
	}
	return n
}

func (m *ResLoginParaBin) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ValidSnCode)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.ImbeSn)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.IsHaveFullCallPerm != 0 {
		n += 1 + sovBf8100(uint64(m.IsHaveFullCallPerm))
	}
	return n
}

func (m *ResLogin) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ResCode != 0 {
		n += 1 + sovBf8100(uint64(m.ResCode))
	}
	if m.Sid != 0 {
		n += 1 + sovBf8100(uint64(m.Sid))
	}
	if m.HangupTime != 0 {
		n += 1 + sovBf8100(uint64(m.HangupTime))
	}
	if m.HttpPort != 0 {
		n += 1 + sovBf8100(uint64(m.HttpPort))
	}
	l = len(m.ServerVersion)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.SettingLastUpdateTime)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *ResRepeaterState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceDmrid != 0 {
		n += 5
	}
	if m.ChannelId != 0 {
		n += 1 + sovBf8100(uint64(m.ChannelId))
	}
	if m.RxFrequency != 0 {
		n += 5
	}
	if m.TxFrequency != 0 {
		n += 5
	}
	if m.PowerValue != 0 {
		n += 1 + sovBf8100(uint64(m.PowerValue))
	}
	if m.IpAddr != 0 {
		n += 5
	}
	if m.VolValue != 0 {
		n += 1 + sovBf8100(uint64(m.VolValue))
	}
	if m.TmpValue != 0 {
		n += 1 + sovBf8100(uint64(m.TmpValue))
	}
	if m.TmpErr != 0 {
		n += 1 + sovBf8100(uint64(m.TmpErr))
	}
	if m.AntErr != 0 {
		n += 1 + sovBf8100(uint64(m.AntErr))
	}
	if m.GpsErr != 0 {
		n += 1 + sovBf8100(uint64(m.GpsErr))
	}
	if m.VolErr != 0 {
		n += 1 + sovBf8100(uint64(m.VolErr))
	}
	if m.RxPllErr != 0 {
		n += 1 + sovBf8100(uint64(m.RxPllErr))
	}
	if m.TxPllErr != 0 {
		n += 1 + sovBf8100(uint64(m.TxPllErr))
	}
	if m.FanErr != 0 {
		n += 1 + sovBf8100(uint64(m.FanErr))
	}
	if m.Signal != 0 {
		n += 2 + sovBf8100(uint64(m.Signal))
	}
	if m.AntValue != 0 {
		n += 2 + sovBf8100(uint64(m.AntValue))
	}
	return n
}

func (m *RepeaterErrStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceDmrid != 0 {
		n += 5
	}
	if m.TmpErr != 0 {
		n += 1 + sovBf8100(uint64(m.TmpErr))
	}
	if m.AntErr != 0 {
		n += 1 + sovBf8100(uint64(m.AntErr))
	}
	if m.GpsErr != 0 {
		n += 1 + sovBf8100(uint64(m.GpsErr))
	}
	if m.VolErr != 0 {
		n += 1 + sovBf8100(uint64(m.VolErr))
	}
	if m.RxPllErr != 0 {
		n += 1 + sovBf8100(uint64(m.RxPllErr))
	}
	if m.TxPllErr != 0 {
		n += 1 + sovBf8100(uint64(m.TxPllErr))
	}
	if m.FanErr != 0 {
		n += 1 + sovBf8100(uint64(m.FanErr))
	}
	if m.Signal != 0 {
		n += 1 + sovBf8100(uint64(m.Signal))
	}
	return n
}

func (m *DeviceSend) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeaterDmrid != 0 {
		n += 5
	}
	l = len(m.Fsk)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.SourceRepeaterDmrid != 0 {
		n += 5
	}
	return n
}

func (m *ServerSend) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Fsk)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *Bc71) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeaterDmrid != 0 {
		n += 5
	}
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	if m.FieldIntensity != 0 {
		n += 1 + sovBf8100(uint64(m.FieldIntensity))
	}
	if m.SupportDigital != 0 {
		n += 1 + sovBf8100(uint64(m.SupportDigital))
	}
	if m.SupportAnalog != 0 {
		n += 1 + sovBf8100(uint64(m.SupportAnalog))
	}
	if m.TimeSlotNo != 0 {
		n += 1 + sovBf8100(uint64(m.TimeSlotNo))
	}
	if m.Priority != 0 {
		n += 1 + sovBf8100(uint64(m.Priority))
	}
	if m.SoundType != 0 {
		n += 1 + sovBf8100(uint64(m.SoundType))
	}
	l = len(m.PhoneNo)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.CallDuplex != 0 {
		n += 1 + sovBf8100(uint64(m.CallDuplex))
	}
	if m.SourceRepeaterDmrid != 0 {
		n += 5
	}
	if m.PreferInterruptTargetDmrid != 0 {
		n += 6
	}
	return n
}

func (m *Cb71) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeaterDmrid != 0 {
		n += 5
	}
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	if m.ReqNo != 0 {
		n += 1 + sovBf8100(uint64(m.ReqNo))
	}
	if m.SupportDigital != 0 {
		n += 1 + sovBf8100(uint64(m.SupportDigital))
	}
	if m.SupportAnalog != 0 {
		n += 1 + sovBf8100(uint64(m.SupportAnalog))
	}
	if m.TimeSlotNo != 0 {
		n += 1 + sovBf8100(uint64(m.TimeSlotNo))
	}
	if m.Priority != 0 {
		n += 1 + sovBf8100(uint64(m.Priority))
	}
	if m.Result != 0 {
		n += 1 + sovBf8100(uint64(m.Result))
	}
	if m.InterruptDmrid != 0 {
		n += 5
	}
	if m.SoundType != 0 {
		n += 1 + sovBf8100(uint64(m.SoundType))
	}
	l = len(m.PhoneNo)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.CallDuplex != 0 {
		n += 1 + sovBf8100(uint64(m.CallDuplex))
	}
	if m.SourceRepeaterDmrid != 0 {
		n += 5
	}
	return n
}

func (m *Bc73) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeaterDmrid != 0 {
		n += 5
	}
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	if m.TimeSlotNo != 0 {
		n += 1 + sovBf8100(uint64(m.TimeSlotNo))
	}
	if m.SourceRepeaterDmrid != 0 {
		n += 5
	}
	return n
}

func (m *Cb75) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	if m.Reason != 0 {
		n += 1 + sovBf8100(uint64(m.Reason))
	}
	return n
}

func (m *Bc15) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeaterDmrid != 0 {
		n += 5
	}
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	if m.FieldIntensity != 0 {
		n += 1 + sovBf8100(uint64(m.FieldIntensity))
	}
	if m.SupportDigital != 0 {
		n += 1 + sovBf8100(uint64(m.SupportDigital))
	}
	if m.SupportAnalog != 0 {
		n += 1 + sovBf8100(uint64(m.SupportAnalog))
	}
	if m.TimeSlotNo != 0 {
		n += 1 + sovBf8100(uint64(m.TimeSlotNo))
	}
	if m.Priority != 0 {
		n += 1 + sovBf8100(uint64(m.Priority))
	}
	if m.CallType != 0 {
		n += 1 + sovBf8100(uint64(m.CallType))
	}
	if m.SupportInterrupt != 0 {
		n += 1 + sovBf8100(uint64(m.SupportInterrupt))
	}
	if m.CallStatus != 0 {
		n += 1 + sovBf8100(uint64(m.CallStatus))
	}
	if m.SoundType != 0 {
		n += 1 + sovBf8100(uint64(m.SoundType))
	}
	l = len(m.PhoneNo)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.CallDuplex != 0 {
		n += 1 + sovBf8100(uint64(m.CallDuplex))
	}
	if m.SourceRepeaterDmrid != 0 {
		n += 5
	}
	if m.StartTime != 0 {
		n += 10
	}
	return n
}

func (m *Bc10) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	if m.CallType != 0 {
		n += 1 + sovBf8100(uint64(m.CallType))
	}
	if m.Priority != 0 {
		n += 1 + sovBf8100(uint64(m.Priority))
	}
	if m.SupportInterrupt != 0 {
		n += 1 + sovBf8100(uint64(m.SupportInterrupt))
	}
	if m.FrameNo != 0 {
		n += 5
	}
	l = len(m.OpusData_1)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.OpusData_2)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *Bc30) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeaterDmrid != 0 {
		n += 5
	}
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	if m.TimeSlotNo != 0 {
		n += 1 + sovBf8100(uint64(m.TimeSlotNo))
	}
	if m.CallType != 0 {
		n += 1 + sovBf8100(uint64(m.CallType))
	}
	if m.Priority != 0 {
		n += 1 + sovBf8100(uint64(m.Priority))
	}
	if m.SupportInterrupt != 0 {
		n += 1 + sovBf8100(uint64(m.SupportInterrupt))
	}
	if m.FrameNo != 0 {
		n += 5
	}
	l = len(m.AmbeData)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.SoundType != 0 {
		n += 1 + sovBf8100(uint64(m.SoundType))
	}
	if m.SourceRepeaterDmrid != 0 {
		n += 5
	}
	return n
}

func (m *Dtmf) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeaterDmrid != 0 {
		n += 5
	}
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	l = len(m.DtmfStr)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.SourceRepeaterDmrid != 0 {
		n += 5
	}
	return n
}

func (m *EndCall) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RepeaterDmrid != 0 {
		n += 5
	}
	if m.TargetDmrid != 0 {
		n += 5
	}
	if m.SourceDmrid != 0 {
		n += 5
	}
	if m.SoundType != 0 {
		n += 1 + sovBf8100(uint64(m.SoundType))
	}
	l = len(m.PhoneNo)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.SourceRepeaterDmrid != 0 {
		n += 5
	}
	return n
}

func (m *PhoneTransfer) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PhoneDmrid != 0 {
		n += 5
	}
	if m.NowTarget != 0 {
		n += 5
	}
	if m.TransferTarget != 0 {
		n += 5
	}
	return n
}

func (m *OneChannelItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.No != 0 {
		n += 1 + sovBf8100(uint64(m.No))
	}
	l = len(m.SendGroup)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if len(m.ListenGroup) > 0 {
		for _, s := range m.ListenGroup {
			l = len(s)
			n += 1 + l + sovBf8100(uint64(l))
		}
	}
	l = len(m.ZoneRid)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *Channels) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Channels) > 0 {
		for _, e := range m.Channels {
			l = e.Size()
			n += 1 + l + sovBf8100(uint64(l))
		}
	}
	l = len(m.DeviceDmrid)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.DevicePriority != 0 {
		n += 1 + sovBf8100(uint64(m.DevicePriority))
	}
	return n
}

func (m *PhoneAdapterShortNo2DmridReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ShortNo)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.Opt != 0 {
		n += 1 + sovBf8100(uint64(m.Opt))
	}
	if m.Dmrid != 0 {
		n += 5
	}
	return n
}

func (m *PhoneAdapterShortNo2DmridRes) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Result != 0 {
		n += 1 + sovBf8100(uint64(m.Result))
	}
	if m.Dmrid != 0 {
		n += 5
	}
	l = len(m.Info)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *PhoneLineSetting) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ActionCode != 0 {
		n += 1 + sovBf8100(uint64(m.ActionCode))
	}
	if len(m.LinePos) > 0 {
		l = 0
		for _, e := range m.LinePos {
			l += sovBf8100(uint64(e))
		}
		n += 1 + sovBf8100(uint64(l)) + l
	}
	if len(m.LinePhoneNo) > 0 {
		for _, s := range m.LinePhoneNo {
			l = len(s)
			n += 1 + l + sovBf8100(uint64(l))
		}
	}
	if len(m.LineDmrid) > 0 {
		n += 1 + sovBf8100(uint64(len(m.LineDmrid)*4)) + len(m.LineDmrid)*4
	}
	return n
}

func (m *ExOneOrg) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Rid)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.OrgSelfId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.OrgShortName)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.OrgFullName)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.Note)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.OrgIsVirtual != 0 {
		n += 1 + sovBf8100(uint64(m.OrgIsVirtual))
	}
	l = len(m.DmrId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.ParentOrgId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *ExOneDevice) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.OrgId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.SelfId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.DmrId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.VirOrgs)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.Note)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.DeviceType != 0 {
		n += 1 + sovBf8100(uint64(m.DeviceType))
	}
	if m.Priority != 0 {
		n += 1 + sovBf8100(uint64(m.Priority))
	}
	return n
}

func (m *AmbeSerialCode) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Code) > 0 {
		l = 0
		for _, e := range m.Code {
			l += sovBf8100(uint64(e))
		}
		n += 1 + sovBf8100(uint64(l)) + l
	}
	return n
}

func (m *ExOnelineDevices) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.DmrId) > 0 {
		l = 0
		for _, e := range m.DmrId {
			l += sovBf8100(uint64(e))
		}
		n += 1 + sovBf8100(uint64(l)) + l
	}
	if len(m.LastDataTime) > 0 {
		l = 0
		for _, e := range m.LastDataTime {
			l += sovBf8100(uint64(e))
		}
		n += 1 + sovBf8100(uint64(l)) + l
	}
	return n
}

func (m *IotData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Cmd != 0 {
		n += 1 + sozBf8100(uint64(m.Cmd))
	}
	if m.DevType != 0 {
		n += 1 + sozBf8100(uint64(m.DevType))
	}
	l = len(m.DevId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.CmdParam)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.RecvDevId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.RecvTime != 0 {
		n += 1 + sozBf8100(uint64(m.RecvTime))
	}
	l = len(m.KcpRecvDevId)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *DevDataInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovBf8100(uint64(m.Code))
	}
	if m.SrcDmrid != 0 {
		n += 1 + sovBf8100(uint64(m.SrcDmrid))
	}
	if m.DstDmrid != 0 {
		n += 1 + sovBf8100(uint64(m.DstDmrid))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *AddrBook) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovBf8100(uint64(m.Type))
	}
	if m.Code != 0 {
		n += 1 + sovBf8100(uint64(m.Code))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.ParaStr)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *AddrBookList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.AddrBookList) > 0 {
		for _, e := range m.AddrBookList {
			l = e.Size()
			n += 1 + l + sovBf8100(uint64(l))
		}
	}
	return n
}

func (m *Bc40Req) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DevDmrid != 0 {
		n += 5
	}
	if m.GroupDmrid != 0 {
		n += 5
	}
	if m.Riss != 0 {
		n += 1 + sovBf8100(uint64(m.Riss))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.PowerEvent != 0 {
		n += 1 + sovBf8100(uint64(m.PowerEvent))
	}
	if m.LastPowerEvent != 0 {
		n += 1 + sovBf8100(uint64(m.LastPowerEvent))
	}
	if m.Roaming != 0 {
		n += 1 + sovBf8100(uint64(m.Roaming))
	}
	if m.BsIndex != 0 {
		n += 1 + sovBf8100(uint64(m.BsIndex))
	}
	return n
}

func (m *Bc40Resp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DevDmrid != 0 {
		n += 5
	}
	if m.ResCode != 0 {
		n += 1 + sovBf8100(uint64(m.ResCode))
	}
	if m.SysTime != 0 {
		n += 1 + sovBf8100(uint64(m.SysTime))
	}
	return n
}

func (m *Bf8100DmridInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DmrID != 0 {
		n += 5
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.OrgUUID)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.MyUUID)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *MeshGpsInfoT) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SourceId != 0 {
		n += 1 + sovBf8100(uint64(m.SourceId))
	}
	if m.TargetId != 0 {
		n += 1 + sovBf8100(uint64(m.TargetId))
	}
	if m.Hour != 0 {
		n += 1 + sovBf8100(uint64(m.Hour))
	}
	if m.Minute != 0 {
		n += 1 + sovBf8100(uint64(m.Minute))
	}
	if m.Second != 0 {
		n += 1 + sovBf8100(uint64(m.Second))
	}
	if m.Day != 0 {
		n += 1 + sovBf8100(uint64(m.Day))
	}
	if m.Month != 0 {
		n += 1 + sovBf8100(uint64(m.Month))
	}
	if m.Year != 0 {
		n += 1 + sovBf8100(uint64(m.Year))
	}
	if m.Available != 0 {
		n += 1 + sovBf8100(uint64(m.Available))
	}
	if m.Latitude != 0 {
		n += 1 + sovBf8100(uint64(m.Latitude))
	}
	if m.NorthOrSouth != 0 {
		n += 1 + sovBf8100(uint64(m.NorthOrSouth))
	}
	if m.Longitude != 0 {
		n += 1 + sovBf8100(uint64(m.Longitude))
	}
	if m.EastOrWest != 0 {
		n += 1 + sovBf8100(uint64(m.EastOrWest))
	}
	if m.Speed != 0 {
		n += 1 + sovBf8100(uint64(m.Speed))
	}
	if m.Direction != 0 {
		n += 1 + sovBf8100(uint64(m.Direction))
	}
	if m.Altitude != 0 {
		n += 2 + sovBf8100(uint64(m.Altitude))
	}
	if m.GpsDataType != 0 {
		n += 2 + sovBf8100(uint64(m.GpsDataType))
	}
	return n
}

func (m *MeshGpsInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DmrID != 0 {
		n += 5
	}
	if m.GpsInfo != nil {
		l = m.GpsInfo.Size()
		n += 1 + l + sovBf8100(uint64(l))
	}
	return n
}

func (m *PocCommand) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Cmd != 0 {
		n += 1 + sovBf8100(uint64(m.Cmd))
	}
	if m.SeqNo != 0 {
		n += 1 + sovBf8100(uint64(m.SeqNo))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.ParaStr)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	l = len(m.ParaBin)
	if l > 0 {
		n += 1 + l + sovBf8100(uint64(l))
	}
	if m.ParaInt != 0 {
		n += 1 + sovBf8100(uint64(m.ParaInt))
	}
	return n
}

func (m *PocDefaultGroup) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DefaultSendGroupDmrid != 0 {
		n += 5
	}
	if len(m.DefaultListenGroupDmrids) > 0 {
		n += 1 + sovBf8100(uint64(len(m.DefaultListenGroupDmrids)*4)) + len(m.DefaultListenGroupDmrids)*4
	}
	return n
}

func (m *PocSubscribleUpdateOption) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FrameNo != 0 {
		n += 1 + sovBf8100(uint64(m.FrameNo))
	}
	if m.FrameType != 0 {
		n += 1 + sovBf8100(uint64(m.FrameType))
	}
	return n
}

func (m *PocConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CanEditSubscriptionLocal != 0 {
		n += 1 + sovBf8100(uint64(m.CanEditSubscriptionLocal))
	}
	if m.Rgps != 0 {
		n += 1 + sovBf8100(uint64(m.Rgps))
	}
	return n
}

func sovBf8100(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozBf8100(x uint64) (n int) {
	return sovBf8100(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RpcCmd) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: rpc_cmd: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: rpc_cmd: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SeqNo", wireType)
			}
			m.SeqNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sid", wireType)
			}
			m.Sid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			m.Cmd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cmd |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Res", wireType)
			}
			m.Res = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Res |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = append(m.Body[:0], dAtA[iNdEx:postIndex]...)
			if m.Body == nil {
				m.Body = []byte{}
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParaStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParaBin = append(m.ParaBin[:0], dAtA[iNdEx:postIndex]...)
			if m.ParaBin == nil {
				m.ParaBin = []byte{}
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaInt", wireType)
			}
			m.ParaInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParaInt |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Login) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: login: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: login: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceDmrid", wireType)
			}
			m.DeviceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginType", wireType)
			}
			m.LoginType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceModel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceModel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PasswordMethod", wireType)
			}
			m.PasswordMethod = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PasswordMethod |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TimeStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SysId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SysId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ExtraOption = append(m.ExtraOption, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBf8100
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBf8100
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.ExtraOption) == 0 {
					m.ExtraOption = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBf8100
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ExtraOption = append(m.ExtraOption, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field ExtraOption", wireType)
			}
		case 10:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Codec = append(m.Codec, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBf8100
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBf8100
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Codec) == 0 {
					m.Codec = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBf8100
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Codec = append(m.Codec, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Codec", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResLoginParaBin) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: res_login_para_bin: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: res_login_para_bin: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ValidSnCode", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ValidSnCode = append(m.ValidSnCode[:0], dAtA[iNdEx:postIndex]...)
			if m.ValidSnCode == nil {
				m.ValidSnCode = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImbeSn", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImbeSn = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsHaveFullCallPerm", wireType)
			}
			m.IsHaveFullCallPerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsHaveFullCallPerm |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResLogin) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: res_login: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: res_login: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResCode", wireType)
			}
			m.ResCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResCode |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sid", wireType)
			}
			m.Sid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HangupTime", wireType)
			}
			m.HangupTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HangupTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HttpPort", wireType)
			}
			m.HttpPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HttpPort |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServerVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ServerVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SettingLastUpdateTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SettingLastUpdateTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResRepeaterState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: res_repeater_state: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: res_repeater_state: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceDmrid", wireType)
			}
			m.DeviceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RxFrequency", wireType)
			}
			m.RxFrequency = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RxFrequency = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 4:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxFrequency", wireType)
			}
			m.TxFrequency = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TxFrequency = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PowerValue", wireType)
			}
			m.PowerValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PowerValue |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field IpAddr", wireType)
			}
			m.IpAddr = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.IpAddr = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field VolValue", wireType)
			}
			m.VolValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VolValue |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TmpValue", wireType)
			}
			m.TmpValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TmpValue |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TmpErr", wireType)
			}
			m.TmpErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TmpErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AntErr", wireType)
			}
			m.AntErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AntErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GpsErr", wireType)
			}
			m.GpsErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GpsErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field VolErr", wireType)
			}
			m.VolErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VolErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RxPllErr", wireType)
			}
			m.RxPllErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RxPllErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxPllErr", wireType)
			}
			m.TxPllErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TxPllErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FanErr", wireType)
			}
			m.FanErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FanErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Signal", wireType)
			}
			m.Signal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Signal |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AntValue", wireType)
			}
			m.AntValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AntValue |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RepeaterErrStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: repeater_err_status: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: repeater_err_status: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceDmrid", wireType)
			}
			m.DeviceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TmpErr", wireType)
			}
			m.TmpErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TmpErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AntErr", wireType)
			}
			m.AntErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AntErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GpsErr", wireType)
			}
			m.GpsErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GpsErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field VolErr", wireType)
			}
			m.VolErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VolErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RxPllErr", wireType)
			}
			m.RxPllErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RxPllErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxPllErr", wireType)
			}
			m.TxPllErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TxPllErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FanErr", wireType)
			}
			m.FanErr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FanErr |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Signal", wireType)
			}
			m.Signal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Signal |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeviceSend) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: device_send: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: device_send: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeaterDmrid", wireType)
			}
			m.RepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Fsk", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Fsk = append(m.Fsk[:0], dAtA[iNdEx:postIndex]...)
			if m.Fsk == nil {
				m.Fsk = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRepeaterDmrid", wireType)
			}
			m.SourceRepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServerSend) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: server_send: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: server_send: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Fsk", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Fsk = append(m.Fsk[:0], dAtA[iNdEx:postIndex]...)
			if m.Fsk == nil {
				m.Fsk = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Bc71) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: bc71: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: bc71: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeaterDmrid", wireType)
			}
			m.RepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FieldIntensity", wireType)
			}
			m.FieldIntensity = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FieldIntensity |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportDigital", wireType)
			}
			m.SupportDigital = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportDigital |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportAnalog", wireType)
			}
			m.SupportAnalog = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportAnalog |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeSlotNo", wireType)
			}
			m.TimeSlotNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeSlotNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SoundType", wireType)
			}
			m.SoundType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SoundType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PhoneNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PhoneNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallDuplex", wireType)
			}
			m.CallDuplex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallDuplex |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRepeaterDmrid", wireType)
			}
			m.SourceRepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 16:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field PreferInterruptTargetDmrid", wireType)
			}
			m.PreferInterruptTargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.PreferInterruptTargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Cb71) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: cb71: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: cb71: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeaterDmrid", wireType)
			}
			m.RepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReqNo", wireType)
			}
			m.ReqNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportDigital", wireType)
			}
			m.SupportDigital = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportDigital |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportAnalog", wireType)
			}
			m.SupportAnalog = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportAnalog |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeSlotNo", wireType)
			}
			m.TimeSlotNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeSlotNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field InterruptDmrid", wireType)
			}
			m.InterruptDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.InterruptDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SoundType", wireType)
			}
			m.SoundType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SoundType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PhoneNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PhoneNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallDuplex", wireType)
			}
			m.CallDuplex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallDuplex |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRepeaterDmrid", wireType)
			}
			m.SourceRepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Bc73) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: bc73: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: bc73: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeaterDmrid", wireType)
			}
			m.RepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeSlotNo", wireType)
			}
			m.TimeSlotNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeSlotNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRepeaterDmrid", wireType)
			}
			m.SourceRepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Cb75) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: cb75: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: cb75: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			m.Reason = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Reason |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Bc15) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: bc15: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: bc15: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeaterDmrid", wireType)
			}
			m.RepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FieldIntensity", wireType)
			}
			m.FieldIntensity = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FieldIntensity |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportDigital", wireType)
			}
			m.SupportDigital = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportDigital |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportAnalog", wireType)
			}
			m.SupportAnalog = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportAnalog |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeSlotNo", wireType)
			}
			m.TimeSlotNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeSlotNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallType", wireType)
			}
			m.CallType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportInterrupt", wireType)
			}
			m.SupportInterrupt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportInterrupt |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallStatus", wireType)
			}
			m.CallStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallStatus |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SoundType", wireType)
			}
			m.SoundType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SoundType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PhoneNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PhoneNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallDuplex", wireType)
			}
			m.CallDuplex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallDuplex |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRepeaterDmrid", wireType)
			}
			m.SourceRepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 16:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.StartTime = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Bc10) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: bc10: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: bc10: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallType", wireType)
			}
			m.CallType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportInterrupt", wireType)
			}
			m.SupportInterrupt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportInterrupt |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field FrameNo", wireType)
			}
			m.FrameNo = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.FrameNo = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpusData_1", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OpusData_1 = append(m.OpusData_1[:0], dAtA[iNdEx:postIndex]...)
			if m.OpusData_1 == nil {
				m.OpusData_1 = []byte{}
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpusData_2", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OpusData_2 = append(m.OpusData_2[:0], dAtA[iNdEx:postIndex]...)
			if m.OpusData_2 == nil {
				m.OpusData_2 = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Bc30) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: bc30: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: bc30: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeaterDmrid", wireType)
			}
			m.RepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeSlotNo", wireType)
			}
			m.TimeSlotNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeSlotNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallType", wireType)
			}
			m.CallType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SupportInterrupt", wireType)
			}
			m.SupportInterrupt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportInterrupt |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field FrameNo", wireType)
			}
			m.FrameNo = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.FrameNo = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AmbeData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AmbeData = append(m.AmbeData[:0], dAtA[iNdEx:postIndex]...)
			if m.AmbeData == nil {
				m.AmbeData = []byte{}
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SoundType", wireType)
			}
			m.SoundType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SoundType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRepeaterDmrid", wireType)
			}
			m.SourceRepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Dtmf) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: dtmf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: dtmf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeaterDmrid", wireType)
			}
			m.RepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DtmfStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DtmfStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRepeaterDmrid", wireType)
			}
			m.SourceRepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EndCall) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: end_call: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: end_call: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field RepeaterDmrid", wireType)
			}
			m.RepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.RepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetDmrid", wireType)
			}
			m.TargetDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceDmrid", wireType)
			}
			m.SourceDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SoundType", wireType)
			}
			m.SoundType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SoundType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PhoneNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PhoneNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRepeaterDmrid", wireType)
			}
			m.SourceRepeaterDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRepeaterDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PhoneTransfer) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: phone_transfer: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: phone_transfer: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field PhoneDmrid", wireType)
			}
			m.PhoneDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.PhoneDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field NowTarget", wireType)
			}
			m.NowTarget = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.NowTarget = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 4:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferTarget", wireType)
			}
			m.TransferTarget = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.TransferTarget = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OneChannelItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OneChannelItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OneChannelItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field No", wireType)
			}
			m.No = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.No |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SendGroup", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SendGroup = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListenGroup", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ListenGroup = append(m.ListenGroup, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ZoneRid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ZoneRid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Channels) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Channels: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Channels: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Channels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Channels = append(m.Channels, &OneChannelItem{})
			if err := m.Channels[len(m.Channels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceDmrid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceDmrid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevicePriority", wireType)
			}
			m.DevicePriority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DevicePriority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PhoneAdapterShortNo2DmridReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PhoneAdapterShortNo2DmridReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PhoneAdapterShortNo2DmridReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShortNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShortNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Opt", wireType)
			}
			m.Opt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Opt |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dmrid", wireType)
			}
			m.Dmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.Dmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PhoneAdapterShortNo2DmridRes) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PhoneAdapterShortNo2DmridRes: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PhoneAdapterShortNo2DmridRes: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dmrid", wireType)
			}
			m.Dmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.Dmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Info = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PhoneLineSetting) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PhoneLineSetting: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PhoneLineSetting: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActionCode", wireType)
			}
			m.ActionCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActionCode |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.LinePos = append(m.LinePos, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBf8100
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBf8100
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.LinePos) == 0 {
					m.LinePos = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBf8100
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.LinePos = append(m.LinePos, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field LinePos", wireType)
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LinePhoneNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LinePhoneNo = append(m.LinePhoneNo, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType == 5 {
				var v uint32
				if (iNdEx + 4) > l {
					return io.ErrUnexpectedEOF
				}
				v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
				iNdEx += 4
				m.LineDmrid = append(m.LineDmrid, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBf8100
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBf8100
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 4
				if elementCount != 0 && len(m.LineDmrid) == 0 {
					m.LineDmrid = make([]uint32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint32
					if (iNdEx + 4) > l {
						return io.ErrUnexpectedEOF
					}
					v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
					iNdEx += 4
					m.LineDmrid = append(m.LineDmrid, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field LineDmrid", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExOneOrg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ex_one_org: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ex_one_org: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgSelfId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgSelfId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgShortName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgShortName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgFullName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgFullName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgIsVirtual", wireType)
			}
			m.OrgIsVirtual = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OrgIsVirtual |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DmrId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DmrId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParentOrgId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParentOrgId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExOneDevice) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ex_one_device: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ex_one_device: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SelfId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SelfId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DmrId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DmrId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VirOrgs", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.VirOrgs = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AmbeSerialCode) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ambe_serial_code: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ambe_serial_code: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Code = append(m.Code, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBf8100
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBf8100
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Code) == 0 {
					m.Code = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBf8100
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Code = append(m.Code, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExOnelineDevices) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ex_oneline_devices: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ex_oneline_devices: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.DmrId = append(m.DmrId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBf8100
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBf8100
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.DmrId) == 0 {
					m.DmrId = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBf8100
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.DmrId = append(m.DmrId, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field DmrId", wireType)
			}
		case 2:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.LastDataTime = append(m.LastDataTime, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBf8100
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBf8100
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.LastDataTime) == 0 {
					m.LastDataTime = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBf8100
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.LastDataTime = append(m.LastDataTime, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field LastDataTime", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IotData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: iot_data: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: iot_data: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Cmd = v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevType", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.DevType = v
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DevId = append(m.DevId[:0], dAtA[iNdEx:postIndex]...)
			if m.DevId == nil {
				m.DevId = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdParam", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CmdParam = append(m.CmdParam[:0], dAtA[iNdEx:postIndex]...)
			if m.CmdParam == nil {
				m.CmdParam = []byte{}
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RecvDevId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RecvDevId = append(m.RecvDevId[:0], dAtA[iNdEx:postIndex]...)
			if m.RecvDevId == nil {
				m.RecvDevId = []byte{}
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RecvTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = (v >> 1) ^ uint64((int64(v&1)<<63)>>63)
			m.RecvTime = int64(v)
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KcpRecvDevId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KcpRecvDevId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DevDataInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: dev_data_info: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: dev_data_info: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SrcDmrid", wireType)
			}
			m.SrcDmrid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SrcDmrid |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DstDmrid", wireType)
			}
			m.DstDmrid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DstDmrid |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddrBook) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: addr_book: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: addr_book: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = append(m.Body[:0], dAtA[iNdEx:postIndex]...)
			if m.Body == nil {
				m.Body = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParaStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddrBookList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: addr_book_list: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: addr_book_list: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AddrBookList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AddrBookList = append(m.AddrBookList, &AddrBook{})
			if err := m.AddrBookList[len(m.AddrBookList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Bc40Req) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: bc40_req: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: bc40_req: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevDmrid", wireType)
			}
			m.DevDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.DevDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field GroupDmrid", wireType)
			}
			m.GroupDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.GroupDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Riss", wireType)
			}
			m.Riss = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Riss |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = append(m.Status[:0], dAtA[iNdEx:postIndex]...)
			if m.Status == nil {
				m.Status = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PowerEvent", wireType)
			}
			m.PowerEvent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PowerEvent |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastPowerEvent", wireType)
			}
			m.LastPowerEvent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastPowerEvent |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Roaming", wireType)
			}
			m.Roaming = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Roaming |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BsIndex", wireType)
			}
			m.BsIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BsIndex |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Bc40Resp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: bc40_resp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: bc40_resp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevDmrid", wireType)
			}
			m.DevDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.DevDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResCode", wireType)
			}
			m.ResCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResCode |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SysTime", wireType)
			}
			m.SysTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SysTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Bf8100DmridInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Bf8100DmridInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Bf8100DmridInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field DmrID", wireType)
			}
			m.DmrID = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.DmrID = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgUUID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgUUID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MyUUID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MyUUID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MeshGpsInfoT) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: mesh_gps_info_t: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: mesh_gps_info_t: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceId", wireType)
			}
			m.SourceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceId |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetId", wireType)
			}
			m.TargetId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetId |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hour", wireType)
			}
			m.Hour = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Hour |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Minute", wireType)
			}
			m.Minute = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Minute |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Second", wireType)
			}
			m.Second = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Second |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Day", wireType)
			}
			m.Day = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Day |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Month", wireType)
			}
			m.Month = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Month |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Year", wireType)
			}
			m.Year = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Year |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Available", wireType)
			}
			m.Available = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Available |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Latitude", wireType)
			}
			m.Latitude = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Latitude |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NorthOrSouth", wireType)
			}
			m.NorthOrSouth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NorthOrSouth |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Longitude", wireType)
			}
			m.Longitude = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Longitude |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EastOrWest", wireType)
			}
			m.EastOrWest = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EastOrWest |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Speed", wireType)
			}
			m.Speed = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Speed |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Direction", wireType)
			}
			m.Direction = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Direction |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Altitude", wireType)
			}
			m.Altitude = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Altitude |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GpsDataType", wireType)
			}
			m.GpsDataType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GpsDataType |= MESH_GPS_DATA_TYPE(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MeshGpsInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MeshGpsInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MeshGpsInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field DmrID", wireType)
			}
			m.DmrID = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.DmrID = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GpsInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GpsInfo == nil {
				m.GpsInfo = &MeshGpsInfoT{}
			}
			if err := m.GpsInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PocCommand) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PocCommand: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PocCommand: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			m.Cmd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cmd |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SeqNo", wireType)
			}
			m.SeqNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqNo |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = append(m.Body[:0], dAtA[iNdEx:postIndex]...)
			if m.Body == nil {
				m.Body = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParaStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBf8100
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBf8100
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParaBin = append(m.ParaBin[:0], dAtA[iNdEx:postIndex]...)
			if m.ParaBin == nil {
				m.ParaBin = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParaInt", wireType)
			}
			m.ParaInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParaInt |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PocDefaultGroup) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PocDefaultGroup: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PocDefaultGroup: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultSendGroupDmrid", wireType)
			}
			m.DefaultSendGroupDmrid = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.DefaultSendGroupDmrid = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType == 5 {
				var v uint32
				if (iNdEx + 4) > l {
					return io.ErrUnexpectedEOF
				}
				v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
				iNdEx += 4
				m.DefaultListenGroupDmrids = append(m.DefaultListenGroupDmrids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBf8100
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBf8100
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBf8100
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 4
				if elementCount != 0 && len(m.DefaultListenGroupDmrids) == 0 {
					m.DefaultListenGroupDmrids = make([]uint32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint32
					if (iNdEx + 4) > l {
						return io.ErrUnexpectedEOF
					}
					v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
					iNdEx += 4
					m.DefaultListenGroupDmrids = append(m.DefaultListenGroupDmrids, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultListenGroupDmrids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PocSubscribleUpdateOption) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PocSubscribleUpdateOption: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PocSubscribleUpdateOption: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FrameNo", wireType)
			}
			m.FrameNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FrameNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FrameType", wireType)
			}
			m.FrameType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FrameType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PocConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PocConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PocConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CanEditSubscriptionLocal", wireType)
			}
			m.CanEditSubscriptionLocal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CanEditSubscriptionLocal |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rgps", wireType)
			}
			m.Rgps = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rgps |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBf8100(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBf8100
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipBf8100(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBf8100
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBf8100
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthBf8100
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupBf8100
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthBf8100
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthBf8100        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBf8100          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupBf8100 = fmt.Errorf("proto: unexpected end of group")
)
