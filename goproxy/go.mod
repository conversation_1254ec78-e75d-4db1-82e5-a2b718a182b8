module goproxy

go 1.21.13

require (
	git.kicad99.com/ykit/goutil v1.14.0
	github.com/albenik/bcd v0.0.0-20170831201648-635201416bc7
	github.com/deckarep/golang-set/v2 v2.6.0
	github.com/gogo/protobuf v1.3.2
	github.com/gorilla/websocket v1.5.3
	github.com/hashicorp/go-version v1.7.0
	github.com/mattn/go-sqlite3 v1.14.24
	github.com/muesli/cache2go v0.0.0-20221011235721-518229cd8021
	github.com/xtaci/kcp-go/v5 v5.6.1
	go.uber.org/atomic v1.11.0
)

require (
	github.com/google/uuid v1.1.3 // indirect
	github.com/klauspost/cpuid/v2 v2.2.6 // indirect
	github.com/klauspost/reedsolomon v1.12.0 // indirect
	github.com/nats-io/jwt v0.3.2 // indirect
	github.com/nats-io/nats.go v1.10.0 // indirect
	github.com/nats-io/nkeys v0.1.4 // indirect
	github.com/nats-io/nuid v1.0.1 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/templexxx/cpu v0.1.1 // indirect
	github.com/templexxx/xorsimd v0.4.3 // indirect
	github.com/tjfoc/gmsm v1.4.1 // indirect
	github.com/yangjuncode/yrpcmsg v1.2.5 // indirect
	golang.org/x/crypto v0.21.0 // indirect
	golang.org/x/net v0.23.0 // indirect
	golang.org/x/sys v0.22.0 // indirect
	google.golang.org/grpc v1.34.0 // indirect
)

replace github.com/xtaci/kcp-go/v5 => github.com/yangjuncode/kcp-go/v5 v5.6.2
