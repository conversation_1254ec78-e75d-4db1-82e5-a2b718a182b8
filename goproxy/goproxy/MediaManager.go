package goproxy

import (
	"log"
	"sync"
	"time"
	"unsafe"

	"goproxy/app_proto"
	"goproxy/bfkcp"

	"go.uber.org/atomic"
)

var GlobalMediaManager *MediaManager

type MediaManager struct {
	mediaPlayer   *MediaPlayer
	mediaRecorder *MediaRecorder
	mediaCache    *MediaCache

	mediaInput8kCh          chan []byte
	meidaInput8kChListened  *atomic.Bool
	mediaInput48kCh         chan []byte
	meidaInput48kChListened *atomic.Bool

	nextMediaSoftware int32

	speakTimeoutDuration time.Duration
	speakingId           *atomic.Uint32
	//0:no denoise 1:asac denoise 2:rnnoise denoise
	denoiseSetting   int32
	DebugRecorderPcm int32
	//xxx% 10-1000
	RecorderPcmGain int32
}

func init() {
	GlobalMediaManager = &MediaManager{
		mediaPlayer: &MediaPlayer{
			isPhoneCallIn:      atomic.NewBool(false),
			playerStatus:       PlayerNormal,
			inputTarget:        InputTarget2Current,
			nextSpeaker:        InValidSpeakerDmrid,
			nextSpeaker_target: InValidSpeakerDmrid,
			nextPlayBuffer:     make([]byte, 0),

			currentSpeaker:        InValidSpeakerDmrid,
			currentSpeaker_target: InValidSpeakerDmrid,
			currentPlayBuffer:     make([]byte, 0),

			emptyTimes:    atomic.NewInt32(0),
			playSessionId: atomic.NewInt32(0),

			lastOnSampleTime: atomic.Value{},
			lastReleaseTime:  atomic.Value{},

			isPlaying:            atomic.NewBool(false),
			currentMediaSoftware: DefaultMediaSoftware,
		},
		mediaRecorder: &MediaRecorder{
			buffer:               make([]byte, 0),
			isRecording:          atomic.NewBool(false),
			isStopPcm:            atomic.NewBool(false),
			isSendingLeastData:   atomic.NewBool(false),
			Mutex:                sync.Mutex{},
			currentMediaSoftware: DefaultMediaSoftware,
		},
		mediaCache: &MediaCache{
			mediaPackageMap:              TMediaPackageItem{},
			currentStoreMediaPackageId:   invalidTimeStampStr,
			currentPlayingMediaPackageId: invalidTimeStampStr,
			mediaPlayerBuffer:            make([]byte, 0),
			isPlaying:                    atomic.NewBool(false),
			timeoutTimes:                 atomic.NewInt32(0),
			playSid:                      atomic.NewInt32(0),
			lastSampleTime:               atomic.Value{},
			currentMediaSoftware:         DefaultMediaSoftware,
		},
		mediaInput8kCh:          make(chan []byte, 50),
		meidaInput8kChListened:  atomic.NewBool(false),
		mediaInput48kCh:         make(chan []byte, 50),
		meidaInput48kChListened: atomic.NewBool(false),

		nextMediaSoftware: DefaultMediaSoftware,

		speakTimeoutDuration: time.Second * time.Duration(speakTimeout),
		speakingId:           atomic.NewUint32(0),

		denoiseSetting:  1,
		RecorderPcmGain: 100,
	}

	var t = time.Date(2000, 1, 1, 1, 1, 1, 1, time.Local)

	GlobalMediaManager.mediaCache.lastSampleTime.Store(t)
	GlobalMediaManager.mediaPlayer.lastReleaseTime.Store(t)
}

/*
 * ******************************************
 * ASAC Init
 * ******************************************
 */

func (m *MediaManager) TryInitial() {
	//QueryAmbeSn()
	log.Println("MediaManager TryInitial: denoiseSetting=", GlobalMediaManager.denoiseSetting, "RecorderPcmGain=", GlobalMediaManager.RecorderPcmGain)
}

var GlobalSN []uint16 = []uint16{0x0098, 0x9681, 0x12a7, 0x7dd9, 0x2fb1, 0xce9e, 0x26fb, 0x3531, 0xba51, 0xbdc5, 0xa168, 0xc072, 0xb2bd, 0x36e9, 0x67c7, 0x534e, 0xc0b0, 0x0720, 0x9b98, 0xf40a}

var UserSn []uint16
var SysSn []uint16

func SetUserSn(sn []uint16) {
	UserSn = sn

	InitAsacInMem(sn)
}

func ClearSysSn() {
	//SysSn = nil
}

func SetSysSn(sn []uint16) {
	SysSn = sn

	if len(UserSn) == 0 {
		UserSn = sn
		//user not set sn
	}
	InitAsacInMem(sn)
}

func IsSetSn() bool {
	if len(UserSn) != 0 {
		return true
	}

	if len(SysSn) != 0 {
		return true
	}

	return false
}

func CheckSn() {
	//if len(GlobalSN) != 24 {
	//	return
	//}
	//
	//if len(GlobalApp.SnParseBin) != 8 {
	//	return
	//}
	//
	//var snParseBin []uint16
	//sliceHeader := (*reflect.SliceHeader)(unsafe.Pointer(&snParseBin))
	//sliceHeader.Cap = 4
	//sliceHeader.Len = 4
	//sliceHeader.Data = uintptr(unsafe.Pointer(&GlobalApp.SnParseBin[0]))
	//
	//for i := 0; i < 4; i++ {
	//	if GlobalSN[20+i] != snParseBin[i] {
	//		return
	//	}
	//}

	ResetAllPitch()
}

func InitAsacInMem(sn []uint16) {
	if len(sn) == 0 {
		return
	}

	GlobalSN = sn
	log.Println("set SN", GlobalSN)
	InitAsac(unsafe.Pointer(&GlobalSN[0]))

	//check the last uint16 data
	CheckSn()
}

func (m *MediaManager) InitMediaCtx() {
	//var err error
	//if m.ctx == nil {
	//	m.ctx, err = malgo.InitContext(nil, malgo.ContextConfig{}, func(message string) {
	//		fmt.Printf("------ StartRecord <%v>\n", message)
	//	})
	//	if err != nil {
	//		log.Println("StartRecord InitContext Err:", err)
	//		return
	//	}
	//}
}

func (m *MediaManager) On8K16BitsSamplesListen() {
	if m.meidaInput8kChListened.Load() {
		return
	}
	m.meidaInput8kChListened.Store(true)

	go func() {
		for {
			select {
			case data := <-m.mediaInput8kCh:
				if GlobalMediaManager.RecorderPcmGain != 100 {
					gain := float64(GlobalMediaManager.RecorderPcmGain) / 100.0
					//need apply pcm gain
					for i := 0; i < len(data)-1; i += 2 {
						pInt16 := (*int16)(unsafe.Pointer(&data[i]))
						gainValue := float64(*pInt16) * gain
						if GlobalMediaManager.RecorderPcmGain > 100 {
							if gainValue > 32767 {
								gainValue = 32767
							} else if gainValue < -32768 {
								gainValue = -32768
							}
						}
						*pInt16 = int16(gainValue)
					}
				}
				m.mediaRecorder.on8kSample(nil, data, uint32(len(data)/2))
			}
		}
	}()

}

func (m *MediaManager) On8K16BitsSamples(pOutputSample, pInputSamples []byte, _frameCount uint32) {
	m.On8K16BitsSamplesListen()
	if m.DebugRecorderPcm == 1 {
		pcm := make([]byte, len(pInputSamples))
		copy(pcm, pInputSamples)
		go GlobalApp.BoradcastGotPcm(8000, pcm)
	}
	m.mediaInput8kCh <- pInputSamples

	//m.mediaRecorder.on8kSample(pOutputSample, pInputSamples, _frameCount)
}

func (m *MediaManager) On48K16BitsSamplesListen() {
	if m.meidaInput48kChListened.Load() {
		return
	}
	m.meidaInput48kChListened.Store(true)

	go func() {
		for {
			select {
			case data := <-m.mediaInput48kCh:
				if GlobalMediaManager.RecorderPcmGain != 100 {
					gain := float64(GlobalMediaManager.RecorderPcmGain) / 100.0
					//need apply pcm gain
					for i := 0; i < len(data)-1; i += 2 {
						pInt16 := (*int16)(unsafe.Pointer(&data[i]))
						gainValue := float64(*pInt16) * gain
						if GlobalMediaManager.RecorderPcmGain > 100 {
							if gainValue > 32767 {
								gainValue = 32767
							} else if gainValue < -32768 {
								gainValue = -32768
							}
						}
						*pInt16 = int16(gainValue)
					}
				}
				m.mediaRecorder.denoiseInput(nil, data, uint32(len(data)/2))
			}
		}
	}()

}

func (m *MediaManager) On48K16BitsSamples(pOutputSample, pInputSamples []byte, _frameCount uint32) {
	m.On48K16BitsSamplesListen()
	if m.DebugRecorderPcm == 1 {
		pcm := make([]byte, len(pInputSamples))
		copy(pcm, pInputSamples)
		go GlobalApp.BoradcastGotPcm(48000, pcm)
	}
	m.mediaInput48kCh <- pInputSamples

	//m.mediaRecorder.denoiseInput(pOutputSample, pInputSamples, _frameCount)
}

/*
 * ******************************************
 * Mobile platform phone state evt
 * ******************************************
 */

func (m *MediaManager) OnPhoneCallOut() {
	m.mediaPlayer.onPhoneCallOut()
	m.mediaRecorder.StopRecord()
}

func (m *MediaManager) OnPhoneCallIn() {
	m.mediaPlayer.onPhoneCallIn()
	m.mediaRecorder.StopRecord()
}

func (m *MediaManager) OnPhoneCallIdle() {
	m.mediaPlayer.onPhoneCallIdle()
}

/*
 * ******************************************
 * Media Player
 * ******************************************
 */

/**
 * @Description: check is playing interval,if not call stopPlayer.
 * @param playSid: need check play sid
 * @param getLastPlaySid: func for get player current play sid.
 * @param isPlaying: func for check player is playing,if not auto exit this func.
 * @param getLastSampleTime: func for get player last onSample time to check if is playing
 * @param stopPlayer: a handle func to stop player
 */
func checkIfStopPlayer(playSid int32, getLastPlaySid func() int32, isPlaying func() bool, getLastSampleTime func() time.Time, stopPlayer func(), onPlay func()) {
	for {
		time.Sleep(2 * time.Second)
		if playSid != getLastPlaySid() || !isPlaying() {
			break
		}

		if time.Since(getLastSampleTime()) > 500*time.Millisecond {
			stopPlayer()
			break
		}
		onPlay()
	}
}

func (m *MediaManager) GotBc10(bc10 *bfkcp.Bc10) {
	if m.mediaPlayer.currentSpeaker == InValidSpeakerDmrid && bc10.SourceDmrid != InValidSpeakerDmrid {
		m.mediaPlayer.currentSpeaker = bc10.SourceDmrid
		m.mediaPlayer.currentSpeakerPriority = bc10.Priority
		m.mediaPlayer.currentSpeaker_target = bc10.TargetDmrid
		m.mediaPlayer.inputTarget = InputTarget2Current
	}

	m.mediaCache.GotBc10(bc10)

	pcm960 := make([]int16, 480)
	l := OpusCodecDecode(bc10.OpusData_1, pcm960)
	if l <= 0 {
		log.Println("GotBc10 OpusCodecDecode 1 failed, len:", l)
		return
	}
	m.mediaPlayer.GotPcm60ms(Int16s2Uint16s(pcm960))

	if len(bc10.OpusData_2) == 0 {
		return
	}
	pcm960 = make([]int16, 480)
	l = OpusCodecDecode(bc10.OpusData_2, pcm960)
	if l <= 0 {
		log.Println("GotBc10 OpusCodecDecode 2 failed, len:", l)
		return
	}
	// send to player
	m.mediaPlayer.GotPcm60ms(Int16s2Uint16s(pcm960[:l]))
}

func (m *MediaManager) GotBc30(bc30 *bfkcp.Bc30) {
	if m.mediaPlayer.currentSpeaker == InValidSpeakerDmrid && bc30.SourceDmrid != InValidSpeakerDmrid {
		m.mediaPlayer.currentSpeaker = bc30.SourceDmrid
		m.mediaPlayer.currentSpeakerPriority = bc30.Priority
		m.mediaPlayer.currentSpeaker_target = bc30.TargetDmrid
		m.mediaPlayer.inputTarget = InputTarget2Current
	}

	//log.Println("MediaManager GotBc30")
	m.mediaCache.GotBc30(bc30)
	pcm960 := make([]uint16, 480)
	AsacDecode60ms(unsafe.Pointer(&bc30.AmbeData[0]), unsafe.Pointer(&pcm960[0]), 0, int16(Gain))

	// send to player
	m.mediaPlayer.GotPcm60ms(pcm960)
}

func (m *MediaManager) GotBc15(bc15 *bfkcp.Bc15) {
	m.mediaCache.GotBc15(bc15)
	m.mediaPlayer.GotBc15(bc15)
}

/*
 * ******************************************
 * Media Recorder
 * ******************************************
 */

func (m *MediaManager) CheckSpeakIsTimeout(speakId uint32) {
	time.Sleep(m.speakTimeoutDuration)
	//time.Sleep(5 * time.Second)
	//log.Println("CheckSpeakIsTimeout", m.speakTimeoutDuration)
	if speakId == m.speakingId.Load() && m.mediaRecorder.isRecording.Load() {
		m.StopRecording()
	}

}

func (m *MediaManager) SetSpeakTimeout(t int32) {
	//speakTimeout = t
	m.speakTimeoutDuration = time.Duration(t) * time.Second
	log.Println("SetSpeakTimeout", t)
}

func (m *MediaManager) GetSpeakTimeout() int32 {
	return int32(m.speakTimeoutDuration)
}

func (m *MediaManager) DoRelease() {
	if m.mediaCache.isPlaying.Load() {
		m.mediaCache.StopPlayer()
	}

	if m.mediaPlayer.isPlaying.Load() {
		m.mediaPlayer.ReleaseMediaPlayer()
	}

	if m.mediaRecorder.isRecording.Load() {
		m.mediaRecorder.StopRecord()
	}
}

func (m *MediaManager) StartRecording() {
	isOk := m.mediaRecorder.StartRecord()
	if isOk {
		// send cb15 start
		GlobalApp.SendBc15(true)
		//notify client start record
		GlobalApp.processSpeakStatus()
		m.speakingId.Inc()
		go m.CheckSpeakIsTimeout(m.speakingId.Load())
	} else {
		// notify start record error
		GlobalApp.PressNotify(&app_proto.Notify{Code: 1})
	}
}

func (m *MediaManager) StopRecording() {
	log.Println("call StopRecording")
	if m.mediaRecorder.isRecording.Load() {
		m.mediaRecorder.StopRecord()
	}
}

func (m *MediaManager) Got960PcmData(pcm960 []byte, frameNo uint32) {
	if !GlobalMediaManager.mediaRecorder.isRecording.Load() {
		return
	}

	denoseSettingVal := m.GetDenoiseSetting()
	if denoseSettingVal >= 2 {
		denoseSettingVal = 0
	}

	if GlobalApp.PreferCodec == 0 {
		ambe := make([]byte, 27)
		AsacEncode60ms(unsafe.Pointer(&pcm960[0]), unsafe.Pointer(&ambe[0]), int16(denoseSettingVal))
		//log.Println("Got960PcmData ambe:", hex.EncodeToString(ambe))
		GlobalApp.GotAmbeRecord(ambe, frameNo)
	} else {
		opus := make([]byte, 320)
		l := OpusCodecEncode(opus, Bytes2I16s(pcm960))
		if l <= 0 {
			log.Println("Got960PcmData OpusCodecEncode failed, len:", l)
			return
		}
		//log.Println("Got960PcmData opus:", hex.EncodeToString(opus))
		GlobalApp.GotOpusRecord(opus[:l], frameNo)
	}
}

func (m *MediaManager) OnLeastPcmDataSendFinished() {
	// send cb15 end
	GlobalApp.SendBc15(false)
	//notify client stop record
	GlobalApp.processSpeakStatus()
}

func (m *MediaManager) GetOnePackPcmData(pOutputSamples []byte, _frameCount uint32) (validLen uint32) {
	if m.mediaPlayer.isPlaying.Load() {
		return m.mediaPlayer._onSamples(pOutputSamples, nil, _frameCount)
	} else {
		return m.mediaCache._onSamples(pOutputSamples, nil, _frameCount)
	}
}

func (m *MediaManager) UpdateMediaSoftware(softType int32) (isOk bool) {
	if softType != SDKMedia && softType != NDKMedia {
		return
	}
	//m.nextMediaSoftware = softType
	isOk = true
	return
}

func (m *MediaManager) GetCurrentSpeakerPriority() int32 {
	return m.mediaPlayer.currentSpeakerPriority
}

func (m *MediaManager) GetCurrentSpeakerDmrid() uint32 {
	return m.mediaPlayer.currentSpeaker
}

func (m *MediaManager) GetDenoiseSetting() int {
	return int(m.denoiseSetting)

}
