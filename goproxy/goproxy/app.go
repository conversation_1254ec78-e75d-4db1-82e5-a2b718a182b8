package goproxy

import (
	"crypto/sha256"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"goproxy/app_proto"
	"goproxy/bfdx_proto"
	"goproxy/bfkcp"

	"git.kicad99.com/ykit/goutil"
	"github.com/gorilla/websocket"
	"github.com/muesli/cache2go"
	"go.uber.org/atomic"
)

// 默认消息类型
const wsReadWriteMsgType = websocket.BinaryMessage

var GlobalLoginRespParaBin []byte

// WsReaderWriterErrorCode 读写严重错误
const WsReaderWriterErrorCode = -1

const WsReadTimeoutErrorCode = -2

// 普通错误
const WsNormalErrorCode = 0

const wsReadDeadLine = time.Duration(6) * time.Minute

var up_cmd_cache = cache2go.Cache("client_up_cmd")

//dmridStr->bool
//go:generate syncmap -name TListenGroupMap -pkg goproxy "map[string]bool"
//dmrid->bool
//go:generate syncmap -name TDmridMap -pkg goproxy "map[int32]bool"
//dmridStr->*app_proto.AddressBook
//go:generate syncmap -name TAddressBookMap -pkg goproxy "map[string]*app_proto.AddressBook"

type App struct {
	//IsNetworkLost    *atomic.Bool
	IsInReLoginState   *atomic.Bool
	IsLogin            *atomic.Bool
	IsPPTPressed       *atomic.Bool
	IsLocationOnce     *atomic.Bool
	IsLocationServerOn *atomic.Bool
	UserName           string
	Sys                string
	UserPass           string
	SessionID          string
	SnStr              string
	IsHaveFullCallPerm int32
	SnParseBin         []byte

	//self dev info
	DevDmrid                  uint32
	DevDmridStr               string
	DevicePriority            int32
	ChNo                      int32
	CurrentSpeakTarget        string
	UserSetSpeakTarget        string
	CallBackSpeakTarget       string
	CallBackSpeakSource       string
	ServerConfigDefaultTarget string
	DyGroupDefaultTarget      string
	OnlineDevices             TDmridMap
	ListenGroupList           TListenGroupMap
	AddrBookList              TAddressBookMap
	UnknownUserBookList       TAddressBookMap

	Dic byte

	listenGroupUpdateSeqNo int32

	//len is 6
	DevStatus *DevStatus
	// 场强，默认为0，可以考虑使用手机卡信号强度
	FieldStrength byte

	// 是否紧急报警通话中
	IsAlarmSpeaking *atomic.Bool

	ws *websocket.Conn
	//write do not support mutl thread
	LastDataTime time.Time
	sync.Mutex

	//can display map
	canDisplayMap bool
	//map token from cmd 316, in map url it's bftk
	mapToken uint32
	//map token set time
	mapTokenSetTime time.Time

	//8100 server version
	ServerVersion      string
	IsServerSupportMap bool

	/**
	 *POC终端专有
	 */
	PocConfig *bfkcp.PocConfig
	// 配置更新时间
	SettingLastUpdateTime string
	//// 通讯录组呼列表, key: DMRID
	//ContactOrgMap xsync.MapOf[string, *bfdx_proto.DbOrg]
	//// 通讯录单呼列表, key: DMRID
	//ContactDeviceMap xsync.MapOf[string, *bfdx_proto.DbDevice]
	//// 默认发射组和收听组
	//PocDefaultGroup *bfkcp.PocDefaultGroup
	//// 当前收听组
	//PocCurrentGroup *bfkcp.PocDefaultGroup
	IsSupportAmbe bool
	IsSupportOpus bool
	// 0: ambe, 1: opus
	PreferCodec int32
	LoginDevice *app_proto.DbDevice

	// 最后一次定位数据
	LastKnownLocation *app_proto.Gps84
	Cb01Locator       *Cb01Locator
	Cb02Locator       *Cb02Locator
	Cb03AreaCheck     *Cb03AreaCheck
	Cb04Locator       *Cb04Locator
	Cb05Locator       *Cb05Locator
	Cb06Locator       *Cb06Locator
	Bc18Locator       *Bc18Locator
	// 紧急报警监控参数
	CB07 *bfdx_proto.Cb07
	// CB10解除报警信号
	Cb10Signal chan byte
	// 登录后从数据库读取
	CbxxArgsRows []*CbxxArgsRow
	// 卫星定位模块动态开启/关闭
	// 10:关闭 11:开启 12:查询 14:不支持/不适用
	Cb42Code int32

	// 最后一次发送的bcxx time
	lastSendBcxxTime time.Time
	// 语音监控定时器
	monitoringTimer *time.Timer
	// 服务器CB00应答的bcxx channel maps
	// key：dic，指令序号
	Cb00ResponseChannels sync.Map
	Cb00ResBB01Dic       sync.Map
	// 已经完成数据初始化
	InitDataFinish bool
}

type updateListenGroupReq struct {
	cmd     int32
	seqNo   int32
	lsGroup []string
}

func (m *App) Log2Server(logstr string) {
	rpcCmd := &bfkcp.RpcCmd{
		Cmd:     8,
		ParaStr: logstr,
	}
	_, _ = m.Write(rpcCmd)
}

func (m *App) SetCallBackSpeakTarget(src, target string) {
	m.CallBackSpeakTarget = target
	m.CallBackSpeakSource = src
	m.PressCallBackSpeakTarget()
	m.LogUserSpeakTargets()
}

func (m *App) QueryUnknownUserByDmridHex(dmridHex string, seqNo int32) {
	req_cmd := &bfkcp.RpcCmd{Cmd: 315, ParaStr: dmridHex, SeqNo: seqNo}
	_ = kcpInstance.send(req_cmd)
}

func (m *App) queryUserNameInAddrBookList(dmridHex string) (string, bool) {
	addrBook, ok := m.AddrBookList.Load(dmridHex)
	if ok {
		return addrBook.Name, true
	}

	u_addrBook, u_ok := m.UnknownUserBookList.Load(dmridHex)
	if u_ok {
		return u_addrBook.Name, true
	}

	m.QueryUnknownUserByDmridHex(dmridHex, 0)
	return dmridHex, false
}

/* export Func */
func (m *App) QueryUserNameInAddrBookList(dmridHex string) string {
	userName, _ := m.queryUserNameInAddrBookList(dmridHex)
	return userName
}

/* ws conn func */

func (m *App) GotNewConn(ws *websocket.Conn) {
	log.Println("GotNewConn", ws.RemoteAddr().String())
	m.Lock()
	defer m.Unlock()

	if m.ws != nil {
		_ = m.ws.Close()
	}
	m.ws = ws
}

func (m *App) Read() (error, int, *bfkcp.RpcCmd) {
	err := m.ws.SetReadDeadline(time.Now().Add(wsReadDeadLine))
	if err != nil {
		return err, WsReaderWriterErrorCode, nil
	}

	msgType, message, err := m.ws.ReadMessage()
	if err != nil {
		if strings.Contains(err.Error(), "timeout") {
			//log.Println("ws ReadMessage timeout,Err:", err)
			return err, WsReadTimeoutErrorCode, nil
		} else {
			log.Println("ws ReadMessage err:", err, m.ws.RemoteAddr().String())
		}
		return err, WsReaderWriterErrorCode, nil
	}

	m.LastDataTime = time.Now()

	if msgType != wsReadWriteMsgType {
		log.Println("recv not binary msg:", msgType, message)
		return fmt.Errorf("recv not binary msg:%d", msgType), WsNormalErrorCode, nil
	}

	rpc_cmd := &bfkcp.RpcCmd{}
	err = rpc_cmd.Unmarshal(message)
	if err != nil {
		log.Println("rpc cmd decode err:", err)
		return fmt.Errorf("rpc cmd decode err:%s", err.Error()), WsNormalErrorCode, nil
	}
	return nil, WsNormalErrorCode, rpc_cmd
}

func (m *App) Write(rpc_cmd *bfkcp.RpcCmd) (error, int) {
	if m.ws == nil {
		log.Println("App Write Err,ws is nil")
		return errors.New("ws is nil!"), -1
	}

	m.Lock()
	defer m.Unlock()

	if rpc_cmd == nil {
		fmt.Printf("ws write error,nil rpc_cmd,sys:%s", m.Sys)
		return fmt.Errorf("ws write error,nil rpc_cmd,sys:%s", m.Sys), WsNormalErrorCode
	}

	data, err := rpc_cmd.Marshal()
	if err != nil {
		fmt.Printf("in ws write. rpc_cmd marchal error,rpc_cmd:%d ,Error:%s", rpc_cmd.Cmd, err.Error())
		return err, WsNormalErrorCode
	}

	//todo 向socket写入数据应该不需要设置超时
	//deadLine := time.Now().Add(3 * time.Second)
	//_ = m.ws.SetWriteDeadline(deadLine)
	err = m.ws.WriteMessage(wsReadWriteMsgType, data)
	if err != nil {
		fmt.Printf("ws write error,Error:%s", err.Error())
		return err, WsReaderWriterErrorCode
	}

	//log.Println("ws Write", m.ws.RemoteAddr().String(), rpc_cmd)

	return nil, WsNormalErrorCode
}

func (m *App) WriteError(respCmd app_proto.CmdCode, err error) {
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(respCmd), Res: int32(app_proto.ResCode_fialed), ParaStr: err.Error()}

	_, _ = m.Write(resp_cmd)
}

func (m *App) WriteUnMarshalError(respCmd app_proto.CmdCode, err error) {
	const unMarshalErrorHeader = "req body unMarshal error:%w"
	m.WriteError(respCmd, fmt.Errorf(unMarshalErrorHeader, err))
}

func (m *App) GetListenGroupList() []string {
	ret := make([]string, 0)
	m.ListenGroupList.Range(func(key string, value bool) bool {
		ret = append(ret, key)
		return true
	})
	return ret
}

func (m *App) GetAddrBookList() []*app_proto.AddressBook {
	ret := make([]*app_proto.AddressBook, 0)
	m.AddrBookList.Range(func(key string, value *app_proto.AddressBook) bool {
		ret = append(ret, value)
		return true
	})
	return ret
}

func (m *App) GetUnknownAddrBookList() []*app_proto.AddressBook {
	ret := make([]*app_proto.AddressBook, 0)
	m.UnknownUserBookList.Range(func(key string, value *app_proto.AddressBook) bool {
		ret = append(ret, value)
		return true
	})
	return ret
}

func (m *App) ProcessPing(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	tRpcCmd := &bfkcp.RpcCmd{
		Res: 1,
		Cmd: int32(app_proto.CmdCode_cmd_resp_ping),
	}

	_, _ = m.Write(tRpcCmd)
}

func (m *App) processPasswordLogin() {
	utcTimeStr := goutil.NowTimeStrInUtc()
	userPassHash := sha256.Sum256([]byte(m.UserName + m.UserPass))
	bs64UserPassHash := base64.StdEncoding.EncodeToString(userPassHash[:])
	sum256 := sha256.Sum256([]byte(utcTimeStr + bs64UserPassHash))
	sumBs64 := base64.StdEncoding.EncodeToString(sum256[:])

	req_login_rpc := &bfkcp.Login{
		DeviceDmrid:    m.DevDmrid,
		DeviceName:     m.UserName,
		LoginType:      23,
		Password:       sumBs64,
		PasswordMethod: 11,
		TimeStr:        utcTimeStr,
		SysId:          m.Sys,
	}

	if m.canDisplayMap {
		// need gps info
		req_login_rpc.ExtraOption = append(req_login_rpc.ExtraOption, 375)
	}

	if m.IsSupportAmbe {
		req_login_rpc.Codec = append(req_login_rpc.Codec, 0)
	}

	if m.IsSupportOpus {
		req_login_rpc.Codec = append(req_login_rpc.Codec, 1)
	}

	data, err := req_login_rpc.Marshal()
	if err != nil {
		log.Println("processPasswordLogin req_login_rpc marshal error:", err)
		return
	}

	req_rpc := &bfkcp.RpcCmd{
		Cmd:  100,
		Body: data,
	}

	Login(req_rpc)
}

func (m *App) _processSessionLoginWithReconnect() {
	req_login_rpc := &bfkcp.Login{
		DeviceDmrid:    m.DevDmrid,
		DeviceName:     m.UserName,
		LoginType:      23,
		Password:       m.SessionID,
		PasswordMethod: 13,
		SysId:          m.Sys,
		ExtraOption:    []int32{375}, // need gps info
	}

	if m.IsSupportAmbe {
		req_login_rpc.Codec = append(req_login_rpc.Codec, 0)
	}

	if m.IsSupportOpus {
		req_login_rpc.Codec = append(req_login_rpc.Codec, 1)
	}

	_Login(req_login_rpc)
}

func _Login(req_login_rpc *bfkcp.Login) {
	data, err := req_login_rpc.Marshal()
	if err != nil {
		log.Println("processPasswordLogin req_login_rpc marshal error:", err)
		return
	}

	req_rpc := &bfkcp.RpcCmd{
		Cmd:  100,
		Body: data,
	}

	Login(req_rpc)
}

func (m *App) processSessionLogin() {
	req_login_rpc := &bfkcp.Login{
		DeviceDmrid:    m.DevDmrid,
		DeviceName:     m.UserName,
		LoginType:      23,
		Password:       m.SessionID,
		PasswordMethod: 12,
		SysId:          m.Sys,
		ExtraOption:    []int32{375},
	}

	if m.IsSupportAmbe {
		req_login_rpc.Codec = append(req_login_rpc.Codec, 0)
	}

	if m.IsSupportOpus {
		req_login_rpc.Codec = append(req_login_rpc.Codec, 1)
	}

	_Login(req_login_rpc)
}

func (m *App) AlreadyLoginButGotNewLoginInfo(reqLogin *app_proto.ReqLogin) {
	GlobalApp.loginQuit()
	go m._ProcessLogin(reqLogin)
}

func (m *App) ProcessLoginButAlreadyLogin(reqLogin *app_proto.ReqLogin) {
	//if reqLogin.SysId == GlobalApp.Sys && reqLogin.UserName == GlobalApp.UserName {
	if reqLogin.UserName == GlobalApp.UserName {
		// tell user login ok
		resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_login), Res: 0, ParaStr: GlobalApp.SessionID}

		paraBin := &bfkcp.ResLoginParaBin{
			ValidSnCode:        GlobalApp.SnParseBin,
			ImbeSn:             GlobalApp.SnStr,
			IsHaveFullCallPerm: GlobalApp.IsHaveFullCallPerm,
		}
		b, err := paraBin.Marshal()
		if err != nil {
			log.Println("ProcessLoginButAlreadyLogin paraBin marshal err:", err)
		} else {
			resp_cmd.ParaBin = b
		}

		err, _ = m.Write(resp_cmd)
		if err != nil {
			log.Println("ProcessLoginButAlreadyLogin Write err:", err)
		}
		m.pressStaticData()
	} else {
		//newly user info,quit old login
		m.AlreadyLoginButGotNewLoginInfo(reqLogin)
	}
}

func (m *App) _ProcessLogin(reqLogin *app_proto.ReqLogin) {
	log.Println("processLogin", reqLogin)

	//save user info into struct
	m.UserName = reqLogin.UserName
	m.canDisplayMap = reqLogin.CanDisplayMap
	m.PreferCodec = reqLogin.PreferCodec

	if reqLogin.LoginMethod == 0 {
		//password login
		m.UserPass = reqLogin.UserPass
		m.processPasswordLogin()
	} else {
		//session login
		m.SessionID = reqLogin.UserPass
		m.processSessionLogin()
	}
}

func (m *App) ProcessLogin(rpc_cmd *bfkcp.RpcCmd) {
	reqLogin := &app_proto.ReqLogin{}

	err := reqLogin.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("processLogin Unmarshal error", err.Error())
		m.WriteUnMarshalError(app_proto.CmdCode_cmd_resp_login, err)
		return
	}

	if GlobalApp.IsLogin.Load() {
		GlobalApp.loginQuit()
	}

	m.DevDmridStr = reqLogin.UserName
	// 转换终端的dmrId，并获取系统号
	m.DevDmrid = hexDmrid2Uint32(reqLogin.UserName)
	sysId := extractSysIdFromDmrid(m.DevDmrid)
	m.Sys = sysId
	reqLogin.SysId = sysId

	//if GlobalApp.IsLogin.Load() && kcpInstance.IsConn2Server.Load() {
	//	m.ProcessLoginButAlreadyLogin(reqLogin)
	//} else {
	//	m._ProcessLogin(reqLogin)
	//}

	m._ProcessLogin(reqLogin)
}

func (m *App) ProcessUpdateListenGroupList(rpc_cmd *bfkcp.RpcCmd) {
	req_rpc := &app_proto.ReqUpdateListenGroupList{}
	err := req_rpc.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("ProcessUpdateListenGroupList Unmarshal rpc_cmd.Body err:", err.Error())
		m.WriteUnMarshalError(app_proto.CmdCode_cmd_resp_update_listen_group_list, err)
		return
	}
	m.ModifyListenGroup(req_rpc.ListenGroupList)
}

func (m *App) ProcessListenGroupList() {
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_listen_group_list)}

	resp_rpc := &app_proto.ReqUpdateListenGroupList{
		ListenGroupList: m.GetListenGroupList(),
	}

	data, err := resp_rpc.Marshal()
	if err != nil {
		log.Println("ProcessListenGroupList marshal ReqUpdateListenGroupList err:", err.Error())
		return
	}

	resp_cmd.Body = data
	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessQueryListenGroupList(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd

	m.ProcessListenGroupList()
}

func (m *App) ProcessUpdateSpeakTarget(rpc_cmd *bfkcp.RpcCmd) {
	resp_cmd := &bfkcp.RpcCmd{
		Cmd: int32(app_proto.CmdCode_cmd_resp_update_speak_target),
		Res: int32(app_proto.ResCode_fialed),
	}

	defer func() {
		log.Println("ProcessUpdateSpeakTarget resp", resp_cmd)
		err, _ := m.Write(resp_cmd)
		if err != nil {
			log.Println("ProcessUpdateSpeakTarget Write err:", err)
		}
	}()

	if GlobalMediaManager.mediaRecorder.isRecording.Load() {
		log.Println("ProcessUpdateSpeakTarget isRecording,can not update defaultTarget")
		return
	}

	req_rpc := &app_proto.ReqUpdateSpeakTarget{}
	err := req_rpc.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("ProcessUpdateSpeakTarget Unmarshal ReqUpdateDefaultTarget Error", err.Error())
		m.WriteUnMarshalError(app_proto.CmdCode_cmd_resp_update_speak_target, err)
		return
	}

	//req_rpc.CurrentSpeakTarget
	m.UserSetSpeakTarget = req_rpc.SpeakTarget
	log.Println("ProcessUpdateSpeakTarget UserSetSpeakTarget:", m.UserSetSpeakTarget)
	//m.CurrentSpeakTarget = req_rpc.SpeakTarget
	resp_cmd.Res = int32(app_proto.ResCode_success)
}

func (m *App) ProcessDefaultTarget() {
	req_rpc := &app_proto.ReqUpdateSpeakTarget{SpeakTarget: m.ServerConfigDefaultTarget}
	data, err := req_rpc.Marshal()
	if err != nil {
		log.Println("ProcessDefaultTarget Marshal ReqUpdateDefaultTarget err", err.Error())
		return
	}

	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_default_speak_target), Body: data}

	_, _ = m.Write(resp_cmd)
	log.Println("press default repeater data to client")
}

func (m *App) ProcessSpeakTarget() {
	req_rpc := &app_proto.ReqUpdateSpeakTarget{SpeakTarget: m.CurrentSpeakTarget}
	data, err := req_rpc.Marshal()
	if err != nil {
		log.Println("ProcessSpeakTarget Marshal ReqUpdateSpeakTarget err", err.Error())
		return
	}

	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_speak_target), Body: data}

	_, _ = m.Write(resp_cmd)
	log.Println("press speak repeater data to client")
}

func (m *App) ProcessQueryDefaultTarget(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.ProcessDefaultTarget()
}

func (m *App) ProcessQuerySpeakTarget(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.ProcessSpeakTarget()
}

func (m *App) ProcessQueryDevDefaultConfig(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_default_dev_config)}

	err := kcpInstance.queryUserDefaultListenGroup()
	if err != nil {
		log.Println("ProcessQueryDevDefaultConfig kcp send err:", err)
		resp_cmd.Res = int32(app_proto.ResCode_fialed)
		resp_cmd.ParaStr = err.Error()
	}
	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessPlayerLocalCacheMedia(rpc_cmd *bfkcp.RpcCmd) {
	if !IsSetSn() {
		resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_play_local_cache_media), Res: 3}

		_, _ = m.Write(resp_cmd)
		return
	}

	_ = rpc_cmd

	if GlobalMediaManager.mediaPlayer.IsPlaying() || GlobalMediaManager.mediaRecorder.isRecording.Load() {
		resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_play_local_cache_media)}

		resp_cmd.Res = 2
		_, _ = m.Write(resp_cmd)
		return
	}

	GlobalMediaManager.mediaCache.PlayMediaDataByTimeStamp(rpc_cmd.ParaStr, func(errCode int32) {
		resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_play_local_cache_media)}

		resp_cmd.Res = errCode
		_, _ = m.Write(resp_cmd)
	})
}

func (m *App) ProcessAddrBook() {
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_address_book)}

	resp_rpc := &app_proto.AddressBookList{AddrBookList: m.GetAddrBookList()}

	data, err := resp_rpc.Marshal()
	if err != nil {
		log.Println("ProcessAddrBook marshal AddressBookList err:", err.Error())
		return
	}

	resp_cmd.Body = data
	_, _ = m.Write(resp_cmd)

	log.Println("press AddrBook data to client")
}

func (m *App) ProcessUnknownAddrBook() {
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_address_book), Res: 1}

	resp_rpc := &app_proto.AddressBookList{AddrBookList: m.GetUnknownAddrBookList()}

	data, err := resp_rpc.Marshal()
	if err != nil {
		log.Println("ProcessUnknownAddrBook marshal AddressBookList err:", err.Error())
		return
	}

	resp_cmd.Body = data
	_, _ = m.Write(resp_cmd)

	log.Println("press ProcessUnknownAddrBook data to client")
}

func (m *App) ProcessQueryAddrBook(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.ProcessAddrBook()
}

func (m *App) SendBc71() error {
	Priority := m.DevicePriority
	// 紧急报警呼叫，优化级设置为4
	if m.DevStatus.GetEmergencyAlarm() == 1 && m.IsAlarmSpeaking.Load() {
		Priority = 4
	}

	bc71 := &bfkcp.Bc71{
		TargetDmrid:    hexDmrid2Uint32(m.CurrentSpeakTarget),
		SourceDmrid:    hexDmrid2Uint32(m.DevDmridStr),
		SupportDigital: 1,
		SupportAnalog:  0,
		TimeSlotNo:     0,
		Priority:       Priority,
		SoundType:      0,
		CallDuplex:     0,
	}
	rpc_cmd := &bfkcp.RpcCmd{Cmd: 71}

	bytes, err := bc71.Marshal()
	if err != nil {
		log.Println("SendBc71 bc71.Marshal Err", err)
		return err
	}

	rpc_cmd.Body = bytes

	log.Println("------------------------------------------------- SendBc71:", bc71)

	return kcpInstance.send(rpc_cmd)
}

func (m *App) GotCb171(rpc_cmd *bfkcp.RpcCmd) {
	cb71 := &bfkcp.Cb71{}
	err := cb71.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotCb171 cb71.Unmarshal err", err)
		return
	}
	log.Println("GotCb71", cb71, m.DevDmrid)
	if cb71.SourceDmrid != m.DevDmrid {
		return
	}

	m.IsAlarmSpeaking.Store(false)

	resp_cmd := &bfkcp.RpcCmd{
		Cmd:  int32(app_proto.CmdCode_cmd_resp_speak_start),
		Body: rpc_cmd.Body,
	}

	_, _ = m.Write(resp_cmd)

	switch cb71.Result {
	case 0x00, 0x01, 0x02, 0x03:
		//可以呼
		GlobalMediaManager.mediaPlayer.ReleaseMediaPlayer()
		// 在开启监听功能时的呼叫不需要发出提示音
		if m.DevStatus.GetEnableMonitoringFunc() == 0 {
			PlayOk()
		}
		m.ClearCallBackSpeakTargetAndNotifyClient()
		GlobalMediaManager.StartRecording()
	case 0x83, 0x80:
		//被抢断
		PlayErr()
		GlobalMediaManager.StopRecording()
	default:
		PlayErr()
		GlobalMediaManager.StopRecording()
		log.Println("Got unHandled cb71.Result", cb71.Result)
	}
}

func (m *App) SendBc18() error {
	// 判断是否有开启紧急报警功能
	enable := m.DevStatus.GetEnableEmergencyAlarm()
	if enable != 1 {
		return errors.New("isNotEnable")
	}

	// 设置状态位
	m.DevStatus.SetEmergencyAlarm(1)

	fsk := make([]byte, 0)
	fsk = append(fsk, 1)

	// 未定位
	if m.LastKnownLocation == nil {
		fsk = append(fsk, genFskGpsBytes(time.Now().UTC(), 0, 0, 0, 0, 0, false)...)
	} else {
		_time, err := time.Parse(TimeFormatStr, m.LastKnownLocation.GpsTime)
		isOk := err != nil
		fsk = append(fsk, genFskGpsBytes(_time, m.LastKnownLocation.Lon, m.LastKnownLocation.Lat, m.LastKnownLocation.Speed, m.LastKnownLocation.Direction, m.LastKnownLocation.Altitude, isOk)...)
	}
	err := m.SendBcXX(0xbc18, 0, m.DevDmrid, fsk)

	// 发送bc18失败，取消报警状态
	if err != nil {
		m.DevStatus.SetEmergencyAlarm(0)
	}

	return err
}

func (m *App) GotCb175(rpc_cmd *bfkcp.RpcCmd) {
	cb75 := &bfkcp.Cb75{}
	err := cb75.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotCb175 cb75.Unmarshal err", err)
		return
	}
	log.Println("GotCb175", cb75, m.DevDmrid)
	if cb75.SourceDmrid != m.DevDmrid {
		return
	}

	_, _ = m.Write(rpc_cmd)
	if GlobalMediaManager.mediaRecorder.isRecording.Load() {
		GlobalMediaManager.StopRecording()
		//GlobalApp.SendBc15(false)
	}
}

func (m *App) GotAmbeRecord(ambe []byte, frameNo uint32) {
	bc30 := &bfkcp.Bc30{
		TargetDmrid:      hexDmrid2Uint32(m.CurrentSpeakTarget),
		SourceDmrid:      hexDmrid2Uint32(m.DevDmridStr),
		TimeSlotNo:       0,
		CallType:         1,
		Priority:         m.DevicePriority,
		SupportInterrupt: 1,
		FrameNo:          frameNo,
		AmbeData:         ambe,
		SoundType:        0,
	}

	bytes, err := bc30.Marshal()
	if err != nil {
		log.Println("bc30.Marshal err", err)
		return
	}

	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:  30,
		Body: bytes,
	}

	if bc30.FrameNo == 1 {
		log.Println("========= send bc30 ===========", bc30)
	}

	_ = kcpInstance.send(rpc_cmd)
}

func (m *App) GotOpusRecord(opus []byte, frameNo uint32) {
	log.Println("GotOpusRecord", opus)
	bc10 := &bfkcp.Bc10{
		TargetDmrid:      hexDmrid2Uint32(m.CurrentSpeakTarget),
		SourceDmrid:      hexDmrid2Uint32(m.DevDmridStr),
		CallType:         1,
		Priority:         m.DevicePriority,
		SupportInterrupt: 1,
		FrameNo:          frameNo,
		OpusData_1:       opus,
	}

	bytes, err := bc10.Marshal()
	if err != nil {
		log.Println("bc10.Marshal err", err)
		return
	}

	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:  10,
		Body: bytes,
	}

	if bc10.FrameNo == 1 {
		log.Println("========= send bc10 ===========", bc10)
	}

	_ = kcpInstance.send(rpc_cmd)
}

func (m *App) LogUserSpeakTargets() {
	log.Println("app LogUserSpeakTargets.", "CurrentSpeakTarget:", m.CurrentSpeakTarget, " ,CallBackSpeakTarget:", m.CallBackSpeakTarget, " ,UserSetSpeakTarget:", m.UserSetSpeakTarget)
}

func (m *App) gotSpeakStartButAlreadyInSpeakMode() {
	bc71 := &bfkcp.Bc71{
		TargetDmrid: hexDmrid2Uint32(m.CurrentSpeakTarget),
		SourceDmrid: hexDmrid2Uint32(m.DevDmridStr),
	}
	rpc_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_speak_start), Res: 2}

	bytes, err := bc71.Marshal()
	if err != nil {
		log.Println("SendBc71 bc71.Marshal Err", err)
		return
	}

	rpc_cmd.Body = bytes
	err, _ = m.Write(rpc_cmd)
	if err != nil {
		log.Println("gotSpeakStartButAlreadyInSpeakMode Write err:", err)
	}
}

func (m *App) gotSpeakStartButSnInvalid() {
	rpc_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_speak_start), Res: 3}

	_, _ = m.Write(rpc_cmd)
}

func (m *App) ProcessSpeakStart(rpc_cmd *bfkcp.RpcCmd, isMonitoringCall bool) {
	// 如果不是紧急报警呼叫，则需要判断是否被禁发
	if m.DevStatus.GetEmergencyAlarm() != 1 && m.DevStatus.GetForbiddenCall() == 1 {
		resp_cmd := &bfkcp.RpcCmd{
			Cmd: int32(app_proto.CmdCode_cmd_resp_speak_start),
			Res: 4,
		}
		_, _ = m.Write(resp_cmd)
		return
	}

	// 发起呼叫时，不是发起的语音监控呼叫但是已经开启了语音监控，先停止监控断掉语音在发起呼叫
	if !isMonitoringCall && m.DevStatus.GetEnableMonitoringFunc() == 1 {
		m.clearMonitoringAndTimer()
	}

	m.CurrentSpeakTarget = rpc_cmd.ParaStr
	m.UserSetSpeakTarget = rpc_cmd.ParaStr

	m.stopPlayLocalCacheMedia()

	m.LogUserSpeakTargets()

	if GlobalMediaManager.mediaRecorder.isRecording.Load() {
		//已经在录音了，通知客户端当前在对谁讲话
		m.gotSpeakStartButAlreadyInSpeakMode()
		return
	}

	//if m.CallBackSpeakTarget == "" && m.UserSetSpeakTarget == "" {
	//	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_speak_start), Res: 1}
	//	_, _ = m.Write(resp_cmd)
	//	return
	//}
	//if m.CallBackSpeakTarget != "" {
	//	m.CurrentSpeakTarget = m.CallBackSpeakTarget
	//} else {
	//	m.CurrentSpeakTarget = m.UserSetSpeakTarget
	//}

	if m.DevStatus.GetEnableMonitoringFunc() == 0 && m.DevStatus.GetEmergencyAlarm() != 1 &&
		GlobalMediaManager.mediaPlayer.IsPlaying() && GlobalMediaManager.GetCurrentSpeakerPriority() > m.DevicePriority {
		// 无权
		respCb71 := bfkcp.Cb71{
			TargetDmrid:    hexDmrid2Uint32(m.CurrentSpeakTarget),
			SourceDmrid:    m.DevDmrid,
			Priority:       m.DevicePriority,
			Result:         0x84, //被抢断
			InterruptDmrid: GlobalMediaManager.GetCurrentSpeakerDmrid(),
		}
		respBody, err := respCb71.Marshal()
		if err != nil {
			log.Println("ProcessSpeakStart user priority lower than current speaker,but resp cmd marshal error:", err)
			return
		}
		resp_cmd := &bfkcp.RpcCmd{
			Cmd:  int32(app_proto.CmdCode_cmd_resp_speak_start),
			Body: respBody,
		}

		_, _ = m.Write(resp_cmd)

		return
	}

	log.Println("ProcessSpeakStart ", m.CurrentSpeakTarget)
	// 申请话权
	_ = m.SendBc71()
	m.IsPPTPressed.Store(true)
	LocationOnce(1)
}

func (m *App) ProcessSpeakStop(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	GlobalMediaManager.StopRecording()

	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_speak_stop), Res: int32(app_proto.ResCode_success)}
	_, _ = GlobalApp.Write(resp_cmd)
}

func (m *App) processSpeakStatus() {
	//defer log.Println("out processSpeakStatus")
	log.Println("processSpeakStatus isRecording?", GlobalMediaManager.mediaRecorder.isRecording.Load())
	// 开启语音监听中，不向UI广播呼叫信息
	if m.DevStatus.GetEnableMonitoringFunc() == 1 {
		return
	}

	resp_cmd := &bfkcp.RpcCmd{
		Cmd: int32(app_proto.CmdCode_cmd_resp_speak_status),
		Res: int32(app_proto.MediaStatus_stoped),
	}
	if GlobalMediaManager.mediaRecorder.isRecording.Load() {
		resp_cmd.Res = int32(app_proto.MediaStatus_start)
	}
	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessSpeakStatus(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.processSpeakStatus()
}

func (m *App) processPlayStatus() {
	speaker, target := GlobalMediaManager.mediaPlayer.gotSpeaker()
	if speaker == InValidSpeakerDmrid || target == InValidSpeakerDmrid {
		GlobalMediaManager.mediaPlayer.isPlaying.Store(false)
	}
	log.Println("player is playing:", GlobalMediaManager.mediaPlayer.IsPlaying())
	resp_cmd := &bfkcp.RpcCmd{
		Cmd:     int32(app_proto.CmdCode_cmd_resp_media_play_status),
		Res:     int32(app_proto.MediaStatus_stoped),
		ParaInt: 1,
	}
	if GlobalMediaManager.mediaPlayer.IsPlaying() {
		resp_cmd.Res = int32(app_proto.MediaStatus_start)
		resp_cmd.ParaStr = dmrid2Hex(speaker) + "-" + dmrid2Hex(target)
	}
	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessPlayStatus(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.processPlayStatus()
}

func (m *App) ProcessConfirmShortMessage(rpc_cmd *bfkcp.RpcCmd) {
	sms := &app_proto.ShortMessages{}
	err := sms.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("ProcessConfirmShortMessage Unmarshal err", err)
		return
	}

	m.SendConfirmSms(hexDmrid2Uint32(sms.TargetDmrid), hexDmrid2Uint32(sms.SenderDmrid), sms.SmsNo)
}

func (m *App) ProcessSendShortMessage(rpc_cmd *bfkcp.RpcCmd) {
	sms := &app_proto.ShortMessages{}
	err := sms.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("ProcessConfirmShortMessage Unmarshal err", err)
		return
	}

	//SenderDmrid string `protobuf:"bytes,4,opt,name=sender_dmrid,json=senderDmrid,proto3" json:"sender_dmrid,omitempty"`
	//TargetDmrid string `protobuf:"bytes,5,opt,name=target_dmrid,json=targetDmrid,proto3" json:"target_dmrid,omitempty"`
	//SmsContent string `protobuf:"bytes,7,opt,name=sms_content,json=smsContent,proto3" json:"sms_content,omitempty"`
	//SmsNo int32 `protobuf:"varint,8,opt,name=sms_no,json=smsNo,proto3" json:"sms_no,omitempty"`
	//Codec int32 `protobuf:"varint,10,opt,name=codec,proto3" json:"codec,omitempty"`
	//SmsType int32 `protobuf:"varint,11,opt,name=sms_type,json=smsType,proto3" json:"sms_type,omitempty"`
	codec := byte(0)
	codec = codec | byte(sms.SmsType&0x0f)<<4
	codec = codec | byte(sms.Codec&0x0f)

	m.SendSms(hexDmrid2Uint32(sms.TargetDmrid), hexDmrid2Uint32(sms.SenderDmrid), codec, sms.SmsNo, encode2UTF16(sms.SmsContent, false))
}

func (m *App) pressStaticData() {
	log.Println("pressStaticData")
	m.ProcessLoginDev()
	m.ProcessLoginUser()
	m.ProcessDefaultTarget()
	m.ProcessSpeakTarget()
	m.ProcessListenGroupList()
	m.ProcessAddrBook()
	m.PressOnlineDevices()
	m.ProcessUnknownAddrBook()
}

func GenRpcForRespUserIsLogin() *bfkcp.RpcCmd {
	resp_cmd := &bfkcp.RpcCmd{
		Cmd: int32(app_proto.CmdCode_cmd_resp_is_login),
		Res: 0,
	}

	if GlobalApp.IsLogin.Load() {
		resp_cmd.ParaStr = GlobalApp.SessionID
		resp_cmd.ParaBin = GlobalLoginRespParaBin
		if kcpInstance.IsConn2Server.Load() {
			resp_cmd.Res = 1
		} else {
			resp_cmd.Res = 2
		}
	}

	return resp_cmd
}

func (m *App) ProcessQueryIsLogin(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	resp_cmd := GenRpcForRespUserIsLogin()
	if resp_cmd.Res != 0 {
		defer func() {
			GlobalApp.pressStaticData()
		}()
	}

	log.Println("ProcessQueryIsLogin", resp_cmd)
	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessQueryIsConnServer(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_is_conn_server), Res: int32(app_proto.ResCode_fialed)}

	if CheckIfConn() {
		resp_cmd.Res = int32(app_proto.ResCode_success)
	}

	_, _ = m.Write(resp_cmd)
}

func parseSn(s string) (snInts []uint16, isValidSn bool) {
	formatedSn := s
	formatedSn = strings.ReplaceAll(formatedSn, " ", "")
	formatedSn = strings.ReplaceAll(formatedSn, "0x", "")
	formatedSn = strings.ReplaceAll(formatedSn, "0X", "")
	formatedSn = strings.ReplaceAll(formatedSn, ",", "")
	formatedSn = strings.ReplaceAll(formatedSn, "，", "")

	if len(formatedSn) == 0 {
		return
	}
	snInts = make([]uint16, 24)
	isValidSn = true

	fixSnLen := 24 * 4

	snCount := len(formatedSn)
	if snCount < fixSnLen {
		formatedSn += strings.Repeat("0", fixSnLen-snCount)
	}

	if len(formatedSn) < fixSnLen {
		log.Println("ProcessResetAsacCode Got Invalid SN len:", len(formatedSn), formatedSn)
		isValidSn = false
		return
	}

	upperSn := strings.ToUpper(formatedSn)
	for i := 0; i < 24; i++ {
		oneSnStr := upperSn[:4]
		upperSn = upperSn[4:]
		oneSn, err := strconv.ParseUint(oneSnStr, 16, 32)
		if err != nil {
			log.Println("ProcessResetAsacCode,sn ParseUint Error", err)
			isValidSn = false
			return
		}
		snInts[i] = uint16(oneSn)
	}

	return
}

func (m *App) ProcessUpdateServerAddr(rpc_cmd *bfkcp.RpcCmd) {
	req_rpc := &app_proto.ServerAddr{}
	err := req_rpc.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("ProcessUpdateServerAddr Unmarshal ServerAddr Err:", err)
		m.WriteUnMarshalError(app_proto.CmdCode_cmd_resp_update_server_addr, err)
		return
	}

	if !(int(req_rpc.Port) == kcpInstance.ServerPort && req_rpc.Host == kcpInstance.ServerHost) {
		m.loginQuit()
		UpdateServerAddr(req_rpc.Host, int(req_rpc.Port))
	}

	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_update_server_addr), Res: int32(app_proto.ResCode_success)}

	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessLoginUser() {
	rpc_cmd := &app_proto.ReqLogin{
		SysId:    m.Sys,
		UserName: m.UserName,
	}
	bytes, err := rpc_cmd.Marshal()
	if err != nil {
		log.Println("ProcessLoginUser ReqLogin Marshal err:", err)
		return
	}
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_login_user), Body: bytes}
	_, _ = m.Write(resp_cmd)
	log.Println("press Login User data to client")
}

func (m *App) ProcessQueryLoginUser(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.ProcessLoginUser()
}

func (m *App) ProcessLoginDev() {
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_login_dev), Res: m.DevicePriority, ParaStr: m.DevDmridStr}
	_, _ = m.Write(resp_cmd)
	log.Println("press Login Dev data to client", m.DevDmridStr)
}

func (m *App) ProcessQueryLoginDev(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.ProcessLoginDev()
}

func (m *App) ProcessSetSpeakTimeOut(rpc_cmd *bfkcp.RpcCmd) {
	resp_cmd := &bfkcp.RpcCmd{
		Cmd: int32(app_proto.CmdCode_cmd_resp_set_speak_time_out_duration),
		Res: int32(app_proto.ResCode_success),
	}

	defer func() {
		_, _ = m.Write(resp_cmd)
	}()

	if rpc_cmd.Res > 300 || rpc_cmd.Res < 0 {
		resp_cmd.Res = int32(app_proto.ResCode_fialed)
		resp_cmd.ParaStr = "invalid time out duration"
		return
	}
	GlobalMediaManager.SetSpeakTimeout(rpc_cmd.Res)
}

func (m *App) ProcessGetSpeakTimeOut(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	resp_cmd := &bfkcp.RpcCmd{
		Cmd: int32(app_proto.CmdCode_cmd_resp_set_speak_time_out_duration),
		Res: GlobalMediaManager.GetSpeakTimeout(),
	}

	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessInitSystemConf(rpc_cmd *bfkcp.RpcCmd) {
	sys_config := &app_proto.VoiceConfig{}
	err := sys_config.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("ProcessInitSystemConf sys_config.Unmarshal Err", err)
		return
	}

	Gain = int(sys_config.Gain)
	MediaAutoStartBufferSize = sys_config.MediaAutoStartSize
	MediaBufferSize = sys_config.MediaBufferSize
	//speakTimeout = sys_config.SpeakTimeout

	GlobalMediaManager.SetSpeakTimeout(sys_config.SpeakTimeout)
	GlobalMediaManager.denoiseSetting = sys_config.DenoiseSetting
	GlobalMediaManager.DebugRecorderPcm = sys_config.DebugRecorderPcm
	if sys_config.RecorderPcmGain > 0 {
		GlobalMediaManager.RecorderPcmGain = sys_config.RecorderPcmGain
	} else {
		GlobalMediaManager.RecorderPcmGain = 100
	}

	resp_cmd := &bfkcp.RpcCmd{
		Cmd: int32(app_proto.CmdCode_cmd_resp_update_voice_config),
		Res: int32(app_proto.ResCode_success),
	}

	//sn, isValidSn := parseSn(sys_config.Sn)
	//if isValidSn {
	//	SetUserSn(sn)
	//}

	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessUpdateListenGroupPackages(listenGroupList []string, cmd int32) (err error) {
	var bytes []byte
	//for len(listenGroupList) > listenGroupUpdateChunkSize {
	//	oneChItem := &bfkcp.OneChannelItem{
	//		No:          0,
	//		ListenGroup: listenGroupList[:listenGroupUpdateChunkSize],
	//	}
	//	bytes, err = oneChItem.Marshal()
	//	if err != nil {
	//		log.Println("ProcessUpdateListenGroupPackages OneChannelItem Marshal Err", err)
	//		return
	//	}
	//	rpc_cmd := &bfkcp.RpcCmd{Cmd: cmd, Body: bytes}
	//	log.Println("oneChItem:", oneChItem.ListenGroup)
	//	err = kcpInstance.send(rpc_cmd)
	//	if err != nil {
	//		log.Println("kcpInstance send Err", err)
	//		return
	//	}
	//
	//	listenGroupList = listenGroupList[listenGroupUpdateChunkSize:]
	//}
	m.listenGroupUpdateSeqNo++

	req := &updateListenGroupReq{
		cmd:     cmd,
		seqNo:   m.listenGroupUpdateSeqNo,
		lsGroup: listenGroupList,
	}

	up_cmd_cache.Add(cmd<<16+m.listenGroupUpdateSeqNo, 30*time.Second, req)

	oneChItem := &bfkcp.OneChannelItem{
		No:          0,
		ListenGroup: listenGroupList[:],
	}
	bytes, err = oneChItem.Marshal()
	if err != nil {
		log.Println("ProcessUpdateListenGroupPackages OneChannelItem Marshal Err", err)
		return
	}
	rpc_cmd := &bfkcp.RpcCmd{Cmd: cmd, Body: bytes, SeqNo: m.listenGroupUpdateSeqNo}
	log.Println("oneChItem:", oneChItem.ListenGroup)
	err = kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("kcpInstance send Err", err)
		return
	}
	return nil
}

func (m *App) ProcessDeleteListenGroup(rpc_cmd *bfkcp.RpcCmd) {
	listenGroup := &app_proto.ReqUpdateListenGroupList{}
	var err error
	defer func() {
		if err != nil {
			log.Println("ProcessDeleteListenGroup listenGroup. err", err)
			resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_delete_listen_group), Res: int32(app_proto.ResCode_fialed)}
			_, _ = m.Write(resp_cmd)
		}
	}()
	err = listenGroup.Unmarshal(rpc_cmd.Body)

	if err != nil {
		return
	}

	err = m.ProcessUpdateListenGroupPackages(listenGroup.ListenGroupList, 313)
}

func (m *App) ProcessAddListenGroup(rpc_cmd *bfkcp.RpcCmd) {
	listenGroup := &app_proto.ReqUpdateListenGroupList{}
	var err error
	defer func() {
		if err != nil {
			log.Println("ProcessAddListenGroup listenGroup.Unmarshal err", err)
			resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_add_listen_group), Res: int32(app_proto.ResCode_fialed)}
			_, _ = m.Write(resp_cmd)
		}
	}()
	err = listenGroup.Unmarshal(rpc_cmd.Body)

	if err != nil {
		return
	}

	err = m.ProcessUpdateListenGroupPackages(listenGroup.ListenGroupList, 312)
}

func (m *App) PressCallBackSpeakTarget() {
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_query_call_back_target), ParaStr: m.CallBackSpeakTarget}
	_, _ = m.Write(resp_cmd)
}

func (m *App) ClearCallBackSpeakTarget() {
	m.CallBackSpeakTarget = ""
	m.CallBackSpeakSource = ""
	m.LogUserSpeakTargets()
}

func (m *App) TryClearCallBackSpeakTargetAndNotifyClient(src, target string) {
	log.Println("TryClearCallBackSpeakTargetAndNotifyClient ", target, m.CallBackSpeakTarget)

	if m.CallBackSpeakTarget != target || m.CallBackSpeakSource != src {
		return
	}
	log.Println("TryClearCallBackSpeakTargetAndNotifyClient ClearCallBackSpeakTargetAndNotifyClient", target, m.CallBackSpeakTarget)
	m.ClearCallBackSpeakTargetAndNotifyClient()
}

func (m *App) ClearCallBackSpeakTargetAndNotifyClient() {
	m.ClearCallBackSpeakTarget()
	m.PressCallBackSpeakTarget()
}

func (m *App) ProcessQueryCallBackSpeakTarget(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.PressCallBackSpeakTarget()
}

func (m *App) ProcessClearCallBackSpeakTarget(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.ClearCallBackSpeakTarget()
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_clear_call_back_target), Res: int32(app_proto.ResCode_success)}
	_, _ = m.Write(resp_cmd)
}

func (m *App) stopPlayLocalCacheMedia() {
	GlobalMediaManager.mediaCache.StopPlayer()
}

func (m *App) ProcessStopPlayLocalCacheMedia(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.stopPlayLocalCacheMedia()
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_stop_play_local_cache_media), Res: int32(app_proto.ResCode_success)}

	if !GlobalMediaManager.mediaCache.isPlaying.Load() {
		resp_cmd.Res = 2
	}
	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessSetMediaSoftware(rpc_cmd *bfkcp.RpcCmd) {
	isOk := GlobalMediaManager.UpdateMediaSoftware(rpc_cmd.Res)

	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_set_media_software), Res: int32(app_proto.ResCode_fialed)}
	if isOk {
		resp_cmd.Res = int32(app_proto.ResCode_success)
	}
	_, _ = m.Write(resp_cmd)
}

func (m *App) ProcessQueryDevByDmrid(rpc_cmd *bfkcp.RpcCmd) {
	m.QueryUnknownUserByDmridHex(rpc_cmd.ParaStr, rpc_cmd.SeqNo)
}

func (m *App) ProcessRequestMapToken(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	if m.mapToken == 0 && time.Since(m.mapTokenSetTime) > 9*time.Minute {
		m.RequestMapToken()
		return
	}

	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_map_token), ParaInt: int64(m.mapToken)}
	_, _ = m.Write(resp_cmd)
}
func (m *App) ProcessRequestGpsLocationOnce(rpc_cmd *bfkcp.RpcCmd) {
	m.IsLocationOnce.Store(true)
	LocationOnce(int(rpc_cmd.ParaInt))
}
func (m *App) ProcessRequestGpsLocationOn(_ *bfkcp.RpcCmd) {
	m.IsLocationServerOn.Store(true)
	GpsService.StartService("App-Gps-Location")
}
func (m *App) ProcessRequestGpsLocationOff(_ *bfkcp.RpcCmd) {
	m.IsLocationServerOn.Store(false)
	GpsService.StopService("App-Gps-Location")
}

func (m *App) loginQuit() {
	//kcp dis conn
	//clean mem data
	if m.IsLogin.Load() {
		kcpInstance.LoginQuit()
		// 复用上个kcp实例的连接地址
		lastServerHost := kcpInstance.ServerHost
		lastServerPort := kcpInstance.ServerPort
		kcpInstance = &Kcp{
			LastDataTime:  atomic.NewTime(time.Now()),
			LastSendTime:  atomic.NewTime(time.Now()),
			IsConn2Server: atomic.NewBool(false),
			IsOldKcp:      atomic.NewBool(false),
			IsLoginState:  atomic.NewBool(false),
			ServerHost:    lastServerHost,
			ServerPort:    lastServerPort,
		}
	}

	GlobalMediaManager.DoRelease()

	ClearSysSn()

	m.Lock()
	defer m.Unlock()
	m.IsLogin.Store(false)
	m.UserName = ""
	m.Sys = ""
	m.UserPass = ""
	m.SessionID = ""

	m.SnParseBin = make([]byte, 8)

	m.DevDmrid = 0
	m.DevDmridStr = ""
	m.DevicePriority = 0
	m.ChNo = 0
	m.UserSetSpeakTarget = ""
	m.CurrentSpeakTarget = ""
	m.ServerConfigDefaultTarget = ""
	m.DyGroupDefaultTarget = ""
	m.CallBackSpeakTarget = ""
	m.CallBackSpeakSource = ""
	m.ClearListenGroup()
	m.AddrBookList.Range(func(key string, value *app_proto.AddressBook) bool {
		m.AddrBookList.Delete(key)
		return true
	})
	m.Dic = 0
	m.DevStatus = CreateDefaultDevStatus()
	m.LastKnownLocation = nil
	m.InitDataFinish = false
}

func (m *App) stopAllCbxxTask() {
	if m.Cb01Locator != nil {
		m.Cb01Locator.stop()
		m.Cb01Locator = nil
	}

	if m.Cb02Locator != nil {
		m.Cb02Locator.stop()
		m.Cb02Locator = nil
	}

	if m.Cb03AreaCheck != nil {
		m.Cb03AreaCheck.stop()
		m.Cb03AreaCheck = nil
	}

	if m.Cb04Locator != nil {
		m.Cb04Locator.stop()
		m.Cb04Locator = nil
	}

	if m.Cb05Locator != nil {
		m.Cb05Locator.stop()
		m.Cb05Locator = nil
	}

	if m.Cb06Locator != nil {
		m.Cb06Locator.stop()
		m.Cb06Locator = nil
	}

	if m.Bc18Locator != nil {
		m.Bc18Locator.stop()
		m.Bc18Locator = nil
	}
}

func (m *App) ProcessLoginQuit(cmd *bfkcp.RpcCmd) {
	_ = cmd
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:     11,
		ParaInt: 14,
	}
	_ = kcpInstance.send(rpc_cmd)
	// 向服务器发送退出登录的命令就可以直接退出了, 不需要等待服务器回应
	m.loginQuit()
	resp_cmd := &bfkcp.RpcCmd{
		Cmd: int32(app_proto.CmdCode_cmd_resp_login_quit),
		Res: int32(app_proto.ResCode_success),
	}
	_, _ = m.Write(resp_cmd)
	//if err == nil {
	//	fmt.Println("ProcessLoginQuit send ok")
	//	// 设置一个5秒的定时器，在5秒内没有收到后台响应, 超时, 直接退出
	//	if m.logOutTimer != nil {
	//		m.logOutTimer.Stop()
	//		m.logOutTimer = nil
	//	}
	//	if m.logOutChan != nil {
	//		close(m.logOutChan)
	//		m.logOutChan = nil
	//	}
	//	m.logOutChan = make(chan struct{})
	//
	//	m.logOutTimer = time.AfterFunc(5*time.Second, func() {
	//		select {
	//		case <-m.logOutChan:
	//		default: // 没有收到响应，执行超时逻辑
	//			m.loginQuit()
	//			resp_cmd := &bfkcp.RpcCmd{
	//				Cmd: int32(app_proto.CmdCode_cmd_resp_login_quit),
	//				Res: int32(app_proto.ResCode_success),
	//			}
	//			_, _ = m.Write(resp_cmd)
	//		}
	//	})
	//}

	// 用户主动退出登录，结束所有定时任务
	m.stopAllCbxxTask()
}

func (m *App) PressShortMessage(sms *app_proto.ShortMessages) {
	//_, ok := m.queryUserNameInAddrBookList(sms.SenderDmrid)
	//if !ok {
	//	for i := 0; i < 10; i++ {
	//		time.Sleep(1 * time.Second)
	//		_, o := m.UnknownUserBookList.Load(sms.SenderDmrid)
	//		if o {
	//			break
	//		}
	//	}
	//}

	//sms.SenderDmrid
	rpc_cmd := &bfkcp.RpcCmd{Cmd: 24}
	bytes, err := sms.Marshal()
	if err != nil {
		log.Println("PressShortMessage err:", err)
		return
	}
	rpc_cmd.Body = bytes
	_, _ = m.Write(rpc_cmd)
}

func (m *App) PressGpsInfo(dmrid string, gps *app_proto.Gps84, activeStatus int32) {
	log.Println("App PressGpsInfo", dmrid, gps)
	info := &app_proto.GpsInfo{
		Dmrid:        dmrid,
		GpsInfo:      gps,
		ActiveStatus: activeStatus,
	}

	data, err := info.Marshal()
	if err != nil {
		log.Println("PressGpsInfo Marshal err:", err)
		return
	}

	m.PressNotify(&app_proto.Notify{Code: 20, Body: data})
}

func (m *App) PressNotify(notify *app_proto.Notify) {
	log.Println("App PressNotify", notify)
	bytes, err := notify.Marshal()
	if err != nil {
		log.Println("PressNotify notify.Marshal Err:", err)
		return
	}

	rpc_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_notify), Body: bytes}

	_, _ = m.Write(rpc_cmd)
}

func (m *App) BoradcastGotPcm(samplerate int32, pcm []byte) {
	log.Println("BoradcastGotPcm len:", len(pcm), " samplerate:", samplerate)
	rpc_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_got_pcm_data), ParaInt: int64(samplerate), Body: pcm}
	_, _ = m.Write(rpc_cmd)
}

func (m *App) Exit() {
	//m.loginQuit()
	//m.Write(&bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_exit)})
	//time.Sleep(500 * time.Millisecond)
	//if m.ws != nil {
	//	m.ws.Close()
	//}
}

// TryReLogin this method is mul-thread safety
func (m *App) TryReLogin() {
	if !m.IsLogin.Load() {
		log.Println("=================== user is not login ====================")
		return
	}

	if kcpInstance.IsConn2Server.Load() {
		log.Println("=================== err kcp is already conn ====================")
		return
	}

	log.Println("=================== TryReLogin ====================")

	//time.Sleep(2 * time.Second)

	if m.IsInReLoginState.Load() {
		log.Println("=================== err TryReLogin but IsInReLoginState is true ====================", string(debug.Stack()))
		return
	}

	m.IsInReLoginState.Store(true)
	m._processSessionLoginWithReconnect()
	if m.IsInReLoginState.Load() {
		log.Println("=================== TryReLogin but failed ====================", string(debug.Stack()))
		m.IsInReLoginState.Store(false)
		//m.PressNotify(&app_proto.Notify{Code: 3})
	}
}

/*
 * *************************************************************
 * cgo export func
 * *************************************************************
 */

func (m *App) OnNetworkConn() {
	//m.IsNetworkLost.Store(false)
	//m.TryReLogin()
	m.TryReLogin()
}

func (m *App) OnNetworkLoseConn() {
	//m.IsNetworkLost.Store(true)
}

func IsValidLocationTime(t string) bool {
	gpsTime, err := time.Parse(TimeFormatStr, t)
	if err != nil {
		return false
	}
	// 如果最后一次定位时间在5分钟内，则当前定位为有效定位
	return time.Since(gpsTime) <= (5 * time.Minute)
}

func (m *App) OnLoginSuccess(rpc_cmd *bfkcp.RpcCmd) {
	//login ok
	GlobalApp.SessionID = rpc_cmd.ParaStr

	GlobalApp.IsLogin.Store(true)

	GlobalMediaManager.TryInitial()

	SetSysSn(GlobalSN)

	// 生成默认的报警监控参数
	speakTimeoutDuration := GlobalMediaManager.speakTimeoutDuration
	m.CB07 = &bfdx_proto.Cb07{
		YN:     1,
		JtTime: int32(speakTimeoutDuration.Seconds()),
		DwTime: int32(speakTimeoutDuration.Seconds()),
	}

	// 如果3分钟内没有收到初始化数据完成的命令，则自动执行相关任务
	time.AfterFunc(time.Minute*3, func() {
		if m.InitDataFinish {
			return
		}
		m.NotifyInitDataFinish(&bfkcp.RpcCmd{})
	})

	//paraBin := &bfkcp.ResLoginParaBin{}
	//err := paraBin.Unmarshal(rpc_cmd.ParaBin)
	//if err != nil {
	//	return
	//}
	//
	//m.SnStr = paraBin.ImbeSn
	//m.IsHaveFullCallPerm = paraBin.IsHaveFullCallPerm
	//sn, validSn := parseSn(paraBin.ImbeSn)
	//if validSn {
	//	SetSysSn(sn)
	//} else {
	//	log.Println("OnLoginSuccess Got Invalid SN")
	//}
	//
	//copy(GlobalApp.SnParseBin, paraBin.ValidSnCode)
	//if len(paraBin.ValidSnCode) == 8 {
	//	CheckSn()
	//}
}

func (m *App) DoSubscribe() {
	err := m.ProcessUpdateListenGroupPackages(m.GetListenGroupList(), 312)
	if err != nil {
		log.Println("DoSubscribe err:", err)
	}
}

/*
 * *************************************************************
 * kcp conn func
 * *************************************************************
 */
func (m *App) GotLoginResp(rpc_cmd *bfkcp.RpcCmd) (isOk bool) {
	//resp client
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_login), Res: 10}

	defer func(resp *bfkcp.RpcCmd) {
		_, _ = GlobalApp.Write(resp)
	}(resp_cmd)

	GlobalLoginRespParaBin = rpc_cmd.ParaBin

	resp_cmd.ParaBin = rpc_cmd.ParaBin

	res_rpc := &bfkcp.ResLogin{}
	err := res_rpc.Unmarshal(rpc_cmd.Body)
	if err != nil {
		return
	}

	loginDevice := &app_proto.DbDevice{}
	err = loginDevice.Unmarshal(rpc_cmd.ParaBin)
	if err != nil {
		log.Println("GotLoginResp loginDevice Unmarshal Err:", err)
	}

	if loginDevice != nil {
		m.LoginDevice = loginDevice
		m.DevicePriority = loginDevice.Priority
	}
	m.SettingLastUpdateTime = res_rpc.SettingLastUpdateTime
	m.ServerVersion = res_rpc.ServerVersion
	m.IsServerSupportMap, _ = checkIfServerSupportMap(res_rpc.ServerVersion)
	respLogin := &app_proto.RespLogin{
		IsServerSupportMap:    m.IsServerSupportMap,
		ServerVersion:         res_rpc.ServerVersion,
		SettingLastUpdateTime: res_rpc.SettingLastUpdateTime,
		Device:                loginDevice,
	}
	respLoginByte, _ := respLogin.Marshal()
	resp_cmd.Body = respLoginByte

	// check if is re-login
	if m.IsInReLoginState.Load() {
		resp_cmd.ParaInt = 1
		m.IsInReLoginState.Store(false)
		//// 订阅
		//if res_rpc.ResCode == 0 {
		//	go m.DoSubscribe()
		//}
	}

	resp_cmd.Res = res_rpc.ResCode
	resp_cmd.ParaStr = rpc_cmd.ParaStr

	// save login info
	if res_rpc.ResCode == 0 {
		isOk = true
		//login ok
		m.OnLoginSuccess(rpc_cmd)
	}
	log.Println("GotLoginResp sid:", GlobalApp.SessionID, isOk)

	return
}

func (m *App) ClearListenGroup() {
	m.ListenGroupList.Range(func(key string, value bool) bool {
		m.ListenGroupList.Delete(key)
		return true
	})
}

func (m *App) GotDevChannels(rpc_cmd *bfkcp.RpcCmd) {
	channs := &bfkcp.Channels{}
	err := channs.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotDevChannels Channels Unmarshal Err:", err)
		return
	}

	if rpc_cmd.Res == 1 {
		//第一包
		m.DevDmrid = hexDmrid2Uint32(channs.DeviceDmrid)
		m.DevDmridStr = channs.DeviceDmrid
		m.DevicePriority = channs.DevicePriority
		m.ChNo = channs.Channels[0].No
		if m.ServerConfigDefaultTarget == "" { //first login
			m.ServerConfigDefaultTarget = channs.Channels[0].SendGroup
		}
	}

	m.ClearListenGroup()

	for _, item := range channs.Channels[0].ListenGroup {
		m.ListenGroupList.Store(item, true)
	}

	if int64(rpc_cmd.Res) == rpc_cmd.ParaInt {
		//最后一包 通知客户端
		m.ProcessLoginDev()
		m.ProcessDefaultTarget()
		m.ProcessSpeakTarget()
		m.ProcessListenGroupList()
	}
}

func (m *App) GotOrgList(rpc_cmd *bfkcp.RpcCmd) {
	orgList := &bfdx_proto.DbOrgList{}
	err := orgList.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotOrgList DbOrgList Unmarshal Err:", err)
		return
	}

	for _, item := range orgList.Rows {
		//log.Println("org:", item.DmrId, " : ", item.ParentOrgId)
		if item.OrgIsVirtual == 1 {
			item.OrgIsVirtual = 111
		} else if item.OrgIsVirtual == 2 {
			item.OrgIsVirtual = 112
		}
		addr := &app_proto.AddressBook{
			ParentDmrid:  item.ParentOrgId,
			Dmrid:        item.DmrId,
			Name:         item.OrgShortName,
			DevType:      item.OrgIsVirtual,
			OrgSortValue: item.OrgSortValue,
		}
		m.AddrBookList.Store(addr.Dmrid, addr)
	}

	//if int64(rpc_cmd.Res) == rpc_cmd.ParaInt {
	//	//最后一包 通知客户端
	//	m.ProcessAddrBook()
	//}
}

func (m *App) GotDevList(rpc_cmd *bfkcp.RpcCmd) {
	devList := &bfdx_proto.DbDeviceList{}
	err := devList.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotOrgList GotDevList Unmarshal Err:", err)
		return
	}

	for _, item := range devList.Rows {
		//log.Println("dev:", item.DmrId, " : ", item.DmrId, item.DeviceType)
		addr := &app_proto.AddressBook{
			ParentDmrid: item.OrgId,
			Dmrid:       item.DmrId,
			Name:        item.SelfId,
			DevType:     item.DeviceType,
		}
		m.AddrBookList.Store(addr.Dmrid, addr)
	}

	if int64(rpc_cmd.Res) == rpc_cmd.ParaInt {
		//最后一包 通知客户端
		m.ProcessAddrBook()
	}
}

func (m *App) GotBc15(rpc_cmd *bfkcp.RpcCmd) {
	// 判断是否被禁听
	if m.DevStatus.GetForbiddenListen() == 1 {
		return
	}

	// 收到语音时，如果处于语音监控状态，则关闭监控
	if m.DevStatus.GetEnableMonitoringFunc() == 1 {
		m.clearMonitoringAndTimer()
	}

	//转发bc15
	bc15 := &bfkcp.Bc15{}
	err := bc15.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("Unmarshal err:", err)
		return
	}

	log.Println("Got bc15", bc15)

	//transfer bc15
	_, _ = m.Write(&bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_bc15), Body: rpc_cmd.Body})

	GlobalMediaManager.GotBc15(bc15)
	//if bc15.CallStatus == 0 {
	//	GlobalAudioManager.StopPlay(true)
	//} else {
	//	GlobalAudioManager.StartPlay(rpc_cmd.Body)
	//}
}

func (m *App) SendBc15(isStart bool) {
	bc15 := &bfkcp.Bc15{
		TargetDmrid:      hexDmrid2Uint32(m.CurrentSpeakTarget),
		SourceDmrid:      hexDmrid2Uint32(m.DevDmridStr),
		SupportDigital:   1,
		SupportAnalog:    0,
		TimeSlotNo:       0,
		Priority:         m.DevicePriority,
		SoundType:        0,
		CallDuplex:       0,
		CallType:         1,
		SupportInterrupt: 1,
		CallStatus:       0,
	}
	if isStart {
		bc15.CallStatus = 1
	}

	bytes, err := bc15.Marshal()
	if err != nil {
		log.Println("SendBc15 bc15 Marshal err", err)
		return
	}
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:  15,
		Body: bytes,
	}
	_ = kcpInstance.send(rpc_cmd)
}

func (m *App) OnKcpLoseConn() {
	log.Println("OnKcpLoseConn")
	// 通知客户端
	//m.PressNotify(&app_proto.Notify{Code: 2})

	// 如果当前网络断开 则等待网络恢复再重连
	//if m.IsNetworkLost.Load() {
	//	return
	//}
	// re-login
	m.TryReLogin()
}

func (m *App) GotBc10(rpc_cmd *bfkcp.RpcCmd) {
	// 判断是否被禁听
	if m.DevStatus.GetForbiddenListen() == 1 {
		return
	}
	bc10 := &bfkcp.Bc10{}
	err := bc10.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotBc10 Unmarshal err:", err)
		return
	}
	if bc10.FrameNo%100 == 1 {
		log.Println("Got bc10", bc10)
	}
	GlobalMediaManager.GotBc10(bc10)
}

// 他人讲话语音包
func (m *App) GotBc30(rpc_cmd *bfkcp.RpcCmd) {
	// 判断是否被禁听
	if m.DevStatus.GetForbiddenListen() == 1 {
		return
	}

	bc30 := &bfkcp.Bc30{}
	err := bc30.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotBc30 Unmarshal err:", err)
		return
	}
	if bc30.FrameNo%100 == 1 {
		log.Println("Got bc30", bc30)
	}
	if len(bc30.AmbeData) >= 27 {
		GlobalMediaManager.GotBc30(bc30)
	} else {
		log.Println("Got invalid bc30 len", len(bc30.AmbeData))
	}
	//GlobalAudioManager.GotBc30(bc30)
	//if bc30.FrameNo == 1 {
	//	log.Println("GotBc30")
	//}
}

func (m *App) GotFSK(rpc_cmd *bfkcp.RpcCmd) {
	Bcxx := &bfkcp.ServerSend{}
	err := Bcxx.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotFSK Unmarshal Err", err)
		return
	}
	dic, bcxx, data, err := parseBcXXFSKCmd(Bcxx.Fsk)
	if err != nil {
		log.Println("GotFSK parseBcXXFSKCmd err:", err.Error())
		return
	}
	log.Println("Fsk. Cmd:", dmrid2Hex(uint32(bcxx)), ", body:", data)

	switch bcxx {
	case 0xbc31:
		m.GotBc31(dic, bcxx, data)
	case 0xcb32:
		m.GotCb32(dic, bcxx, data)
	case 0xcb36:
		m.GotCb36(dic, bcxx, data)
	case 0xcb37:
		m.GotCb37(dic, bcxx, data)
	case 0xcb38:
		m.GotCb38(dic, bcxx, data)
	case 0xcb39:
		m.GotCb39(dic, bcxx, data)
	case 0xcb01:
		m.GotCb01(dic, bcxx, data)
	case 0xcb02:
		m.GotCb02(dic, bcxx, data)
	case 0xcb03:
		m.GotCb03(dic, bcxx, data)
	case 0xcb04:
		m.GotCb04(dic, bcxx, data)
	case 0xcb05:
		m.GotCb05(dic, bcxx, data)
	case 0xcb06:
		m.GotCb06(dic, bcxx, data)
	case 0xcb07:
		m.GotCb07(dic, bcxx, data)
	case 0xcb08:
		m.GotCb08(dic, bcxx, data)
	case 0xcb09:
		m.GotCb09(dic, bcxx, data)
	case 0xcb10: // 解除报警
		m.GotCb10(dic, bcxx, data)
	case 0xcb42:
		m.GotCb42(dic, bcxx, data)
	case 0xcb00:
		m.GotCb00(dic, bcxx, data)

	default:
		log.Printf("GotFSK unknown cmd:%x\n", bcxx)
	}
}

func (m *App) GotCb00(dic uint8, bcxx uint16, data []byte) {
	_ = dic
	_ = bcxx
	target, _, resDic, resBcxx, err := parseCb00Args(data)
	log.Println("GotCb00:", dic, bcxx, resDic, resBcxx, target)
	if err != nil {
		log.Println("GotCb00 Got Error fsk:", err, " . ", hex.EncodeToString(data))
		return
	}

	// not send to me
	if target != m.DevDmrid {
		return
	}

	// 根据应答的指令处理不同的应答
	switch resBcxx {
	case 0xbb01:
		m.Cb00ResBB01Dic.Delete(resDic)
		// 服务器确认收到bb01定位数据上报，解除超时
		ch, ok := m.Cb00ResponseChannels.Load(resDic)
		if ok {
			ch.(chan byte) <- 1
		}
	}
}

func (m *App) Got8100BroadcastGpsData(devDataInfo *bfkcp.DevDataInfo) {
	gps := &bfdx_proto.Gps84{}
	err := gps.Unmarshal(devDataInfo.Data)
	if err != nil {
		log.Println("Got8100BroadcastGpsData Unmarshal err:", err)
		return
	}
	log.Println("Got8100BroadcastGpsData", gps, devDataInfo.SrcDmrid)
	go m.PressGpsInfo(dmrid2Hex(devDataInfo.SrcDmrid), (*app_proto.Gps84)(gps), 0)
}

func (m *App) GotDevSpeakingNotify(rpc_cmd *bfkcp.RpcCmd) {
	m.PressNotify(&app_proto.Notify{
		Code: 8,
		Body: rpc_cmd.Body,
	})
}

func (m *App) GotDevicesOnOffLine(rpc_cmd *bfkcp.RpcCmd) {
	devDataInfo := &bfkcp.DevDataInfo{}
	err := devDataInfo.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("GotDevicesOnOffLine devDataInfo.Unmarshal Err", err)
		return
	}
	//log.Println("GotDevicesOnOffLine devDataInfo", devDataInfo)

	m.PressNotify(&app_proto.Notify{
		Code: 334,
		Body: rpc_cmd.Body,
	})
}

func (m *App) parseSmsBcXX(fsk []byte) (target, source uint32, data []byte, err error) {
	if len(fsk) < 8 {
		err = errors.New("parseSmsBcXX invalid fsk len")
		return
	}
	target, _ = bytes2uint32(fsk[0:4])
	source, _ = bytes2uint32(fsk[4:8])

	data = fsk[8:]
	return
}

func (m *App) GotBc31(dic uint8, bcxx uint16, data []byte) {
	_ = dic
	_ = bcxx
	target, source, _data, err := m.parseSmsBcXX(data)

	if err != nil {
		log.Println("GotBc31 Got Error fsk:", err, " . ", hex.EncodeToString(data))
		return
	}

	if len(_data) < 12 {
		log.Println("GotBc31 Got Error fsk.valid len", hex.EncodeToString(data))
		return
	}

	codec := _data[7]
	no := _data[8:10]
	lenght := _data[10:12]

	if len(_data) < 12+bytesDecodeByBCD(lenght) {
		log.Println("GotBc31 Got Error fsk.valid len while bytesDecodeByBCD,len:", lenght, " ,fsk:", hex.EncodeToString(data))
		return
	}

	bmsg := _data[12 : 12+bytesDecodeByBCD(lenght)]

	msg, err := uft16Decode(bmsg)
	if err != nil {
		log.Println("GotBc31 uft16Decode err:", err)
		return
	}

	sms := &app_proto.ShortMessages{
		//发起者dmrid
		SenderDmrid: dmrid2Hex(source),
		//接收方dmrid
		TargetDmrid: dmrid2Hex(target),
		//短信内容
		SmsContent: msg,
		//短信序号
		SmsNo: int32(binary.BigEndian.Uint16(no)),
		Codec: int32(codec & 0x0f),
		//短信类型 0:普通短信 1:自动播报短信
		SmsType: int32(codec >> 4),
		Time:    time.Now().UTC().Unix(),
	}

	log.Println("sms:", sms, _data)
	go m.PressShortMessage(sms)
}

func (m *App) GotCb32(dic uint8, bcxx uint16, data []byte) {
	_ = dic
	_ = bcxx
	target, source, _data, err := m.parseSmsBcXX(data)

	if err != nil {
		log.Println("GotCb32 Got Error fsk:", err, " . ", hex.EncodeToString(data))
		return
	}

	smsNo := _data[:2]
	smsSenderTarget := make([]string, 0)
	smsSenderTarget = append(smsSenderTarget, dmrid2Hex(source))
	smsSenderTarget = append(smsSenderTarget, dmrid2Hex(target))
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_send_short_messages), Res: int32(binary.BigEndian.Uint16(smsNo)), ParaStr: strings.Join(smsSenderTarget, ",")}
	_, _ = m.Write(resp_cmd)
}

func (m *App) GotListenGroupListDmrids() []string {
	dmrids := make([]string, 0)

	m.ListenGroupList.Range(func(key string, value bool) bool {
		dmrids = append(dmrids, key)
		return true
	})
	return dmrids
}

func (m *App) GotAddrBookListDmrids() []string {
	dmrids := make([]string, 0)

	m.AddrBookList.Range(func(key string, value *app_proto.AddressBook) bool {
		dmrids = append(dmrids, key)
		return true
	})
	return dmrids
}

func (m *App) PrintRamData() {
	log.Println("ListenGroupListDmrids:", m.GotListenGroupListDmrids())
	log.Println("AddrBookListDmrids:", m.GotAddrBookListDmrids())
}

func (m *App) GotCb36(dic uint8, bcxx uint16, data []byte) {
	_ = dic
	_ = bcxx
	target, _, _data, err := m.parseSmsBcXX(data)

	if err != nil {
		log.Println("GotCb36 Got Error fsk:", err, " . ", hex.EncodeToString(data))
		return
	}

	groupIdHex := dmrid2Hex(target)

	groupName, _, err := parseDyGroupName(_data)
	if err != nil {
		log.Println("GotCb36 parseDyGroupName Err:", err)
		return
	}

	log.Println("GotCb36 before add listenGroup and addrBook:", groupIdHex, groupName)
	m.PrintRamData()

	addrBook := &app_proto.AddressBook{
		ParentDmrid: "",
		Dmrid:       groupIdHex,
		Name:        groupName,
		DevType:     100,
	}

	m.ListenGroupList.Store(groupIdHex, true)
	m.AddrBookList.Store(groupIdHex, addrBook)

	log.Println("GotCb36 after add listenGroup and addrBook:", groupIdHex)
	m.PrintRamData()

	notify := &app_proto.Notify{Code: 0xcb36, ParamStr: groupIdHex}
	bytes, err := addrBook.Marshal()
	if err == nil {
		notify.Body = bytes
	}

	m.PressNotify(notify)
}

func (m *App) GotCb37(dic uint8, bcxx uint16, data []byte) {
	_ = dic
	_ = bcxx
	target, _, _, err := m.parseSmsBcXX(data)

	if err != nil {
		log.Println("GotCb37 Got Error fsk:", err, " . ", hex.EncodeToString(data))
		return
	}

	groupIdHex := dmrid2Hex(target)

	log.Println("GotCb37 before rm listenGroup and addrBook:", groupIdHex)
	m.PrintRamData()

	m.ListenGroupList.Delete(groupIdHex)
	m.AddrBookList.Delete(groupIdHex)

	log.Println("GotCb37 after rm listenGroup and addrBook:", groupIdHex)
	m.PrintRamData()

	m.PressNotify(&app_proto.Notify{Code: 0xcb37, ParamStr: groupIdHex})
}

func (m *App) GotCb38(dic uint8, bcxx uint16, data []byte) {
	_ = dic
	_ = bcxx
	target, source, _data, err := m.parseSmsBcXX(data)

	if err != nil {
		log.Println("GotCb38 Got Error fsk:", err, " . ", hex.EncodeToString(data))
		return
	}

	if len(_data) < 5 {
		log.Println("GotCb38 valid fsk,M_Group_ID and Group_Name len is less 5")
		return
	}
	groupId, _ := bytes2uint32(_data[0:4])
	groupIdHex := dmrid2Hex(groupId)

	groupName, _, err := parseDyGroupName(_data[4:])
	if err != nil {
		log.Println("GotCb38 uft16Decode Err:", err)
		return
	}
	log.Println("GotCb38:", target, source, groupIdHex, groupName)

	log.Println("GotCb38 before add listenGroup and addrBook:", groupIdHex)
	m.PrintRamData()
	addrBook := &app_proto.AddressBook{
		ParentDmrid: "",
		Dmrid:       groupIdHex,
		Name:        groupName,
		DevType:     101,
	}

	m.ListenGroupList.Store(groupIdHex, true)
	m.AddrBookList.Store(groupIdHex, addrBook)

	log.Println("GotCb38 after add listenGroup and addrBook:", groupIdHex)
	m.PrintRamData()

	notify := &app_proto.Notify{Code: 0xcb38, ParamStr: groupIdHex}
	bytes, err := addrBook.Marshal()
	if err == nil {
		notify.Body = bytes
	}

	m.PressNotify(notify)
	m.ConfirmJoinTaskGroup(target, source, groupId)

	m.DyGroupDefaultTarget = groupIdHex
}

func (m *App) GotCb39(dic uint8, bcxx uint16, data []byte) {
	_ = dic
	_ = bcxx
	target, source, _data, err := m.parseSmsBcXX(data)

	if err != nil {
		log.Println("GotCb39 Got Error fsk:", err, " . ", hex.EncodeToString(data))
		return
	}

	if len(_data) != 4 {
		log.Println("GotCb39 valid fsk,M_Group_ID’ len is not equal 4")
		return
	}
	groupId, _ := bytes2uint32(_data[0:4])

	groupIdHex := dmrid2Hex(groupId)
	log.Println("GotCb39:", target, source, groupIdHex)

	log.Println("GotCb39 before rm listenGroup and addrBook:", groupIdHex)
	m.PrintRamData()

	if groupId == 0 {
		// quit all taskGroup
		m.ListenGroupList.Range(func(key string, value bool) bool {
			addrBook, ok := m.AddrBookList.Load(key)
			if !ok {
				return true
			}

			if addrBook.DevType == 101 {
				//quit task group
				m.AddrBookList.Delete(key)
				m.PressNotify(&app_proto.Notify{Code: 0xcb39, ParamStr: key})
				m.ListenGroupList.Delete(key)
			}
			return true
		})
	} else {
		m.ListenGroupList.Delete(groupIdHex)
		m.PressNotify(&app_proto.Notify{Code: 0xcb39, ParamStr: groupIdHex})
		m.AddrBookList.Delete(groupIdHex)
	}

	log.Println("GotCb39 after rm listenGroup and addrBook:", groupIdHex)
	m.PrintRamData()

	m.ConfirmExitTaskGroup(target, source, groupId)

	m.DyGroupDefaultTarget = ""
}

func (m *App) ParseCb01(fsk []byte) (_target, _source uint32, _interval, _distance, _count int, err error) {
	if len(fsk) != 13 {
		s := fmt.Sprintf("Got valid cb01. len is not equal to 13,fsk:%s", hex.EncodeToString(fsk))
		err = errors.New(s)
		log.Println(err.Error())
		return
	}

	_target, _ = bytes2uint32(fsk[0:4])
	_source, _ = bytes2uint32(fsk[4:8])
	_interval = bytesDecodeByBCD(fsk[8:10])
	_distance = bytesDecodeByBCD(fsk[10:11])
	_count = bytesDecodeByBCD(fsk[11:13])
	return
}

//func respCb01Test(dic uint8, Lon, Lat float64, _source, _target uint32, isRespCb01 bool) ([]byte){
//	body := make([]byte, 7)
//	cmd := 0xbc01
//	if isRespCb01 {
//		cmd = 0xbc00
//		body = append(body, dic)
//		body = append(body, uint16ToBytes(0xcb01)...)
//	}
//
//	body = append(body, genBCDTimeBytes()...) //time
//	body = append(body, lat2bytes(Lat)...)    //Lat 0...1
//	body = append(body, lat2bytes(Lon)...)    //Lon ....1
//	body = append(body, make([]byte, 5)...)
//	log.Println("cmd:", cmd, ", body:", body)
//	return body
//}

// 生成fsk gps部分字节序
func genFskGpsBytes(t time.Time, lon, lat, speed float64, direction, altitude int32, isOk bool) []byte {
	bytes := make([]byte, 0)

	bytes = append(bytes, genBCDTimeBytes(t, isOk)...) //time
	bytes = append(bytes, lat2bytes(lat, isOk)...)     //Lat 0...1
	bytes = append(bytes, lon2bytes(lon)...)           //Lon ....1
	// 1.5字节速度(3 ASCII)
	// speed为千米/时
	s := int32ToString(int32(speed), 3)
	// 1.5字节方向(3 ASCII)
	s += int32ToString(direction, 3)
	// 2字节海拔(4 ASCII)
	s += int32ToString(altitude, 4)
	b, _ := hex.DecodeString(s)
	bytes = append(bytes, b...)
	//bytes = append(bytes, make([]byte, 5)...)

	return bytes
}

func (m *App) sendBc01(location *LocationData, isOk bool) {
	if m.PocConfig != nil && m.PocConfig.Rgps == 1 {
		m.sendBb01(location, isOk)
		return
	}

	if m.Cb42Code != 11 {
		log.Printf("sendBc01 location not enable: cb42 code = %d", m.Cb42Code)
		return
	}
	gpsByte := genFskGpsBytes(location.GpsTime, location.Lon, location.Lat, location.Speed, int32(location.Direction), int32(location.Altitude), isOk)
	_ = m.SendBcXX(uint16(0xbc01), 0, m.DevDmrid, gpsByte)
}

// 保存未发送成功的bb01到数据库
// 只缓存状态、和定位数据
func saveBb01ToDatabase(bcxx uint16, target, source uint32, status, data []byte) error {
	r := &WaitingSendCommandsRow{
		DmrId:     dmrid2Hex(source),
		Bcxx:      bcxx,
		Target:    target,
		DevStatus: status,
		Data:      data,
	}
	log.Println("saveBb01ToDatabase", r)
	err := r.Insert()
	if err != nil {
		log.Println("saveBb01ToDatabase err:", err)
	}
	return err
}

// 带应答的定位上传命令，参数与BC01一致
// 服务器采用CB00来应答BB01命令
func (m *App) sendBb01(location *LocationData, isOk bool) {
	if m.Cb42Code != 11 {
		log.Printf("sendBc01 location not enable: cb42 code = %d", m.Cb42Code)
		return
	}
	gpsByte := genFskGpsBytes(location.GpsTime, location.Lon, location.Lat, location.Speed, int32(location.Direction), int32(location.Altitude), isOk)
	m.Dic++
	resDic := m.Dic
	m.lastSendBcxxTime = time.Now()
	err := m.SendBcXXWithDic(resDic, uint16(0xbb01), 0, m.DevDmrid, gpsByte)

	saveBb01 := func() {
		_ = saveBb01ToDatabase(uint16(0xbb01), 0, m.DevDmrid, *m.DevStatus, gpsByte)
	}

	// 发送失败，缓存到数据库中
	if err != nil {
		go saveBb01()
		return
	}

	// 在协程中等行为CB00应答，如果超时未应答，则需要存储到数据库，等待网络正常后重新发送
	go func() {
		responseChan := make(chan byte, 1)
		m.Cb00ResponseChannels.Store(resDic, responseChan)
		defer m.Cb00ResponseChannels.Delete(resDic) // 确保退出时删除，即使panic

		select {
		case <-responseChan:
			log.Println("got cb00 response bb01")
			// 收到了应答，无需做任何操作，协程结束
		case <-time.After(time.Second * 5):
			// 超时未收到cb00应答
			go saveBb01()
		}

		close(responseChan) //确保关闭channel
	}()
}

func (m *App) sendBc02(location *LocationData, isOk bool) {
	gpsByte := genFskGpsBytes(location.GpsTime, location.Lon, location.Lat, location.Speed, int32(location.Direction), int32(location.Altitude), isOk)
	_ = m.SendBcXX(uint16(0xbc02), 0, m.DevDmrid, gpsByte)
}

func (m *App) sendBc03(dic uint8, location *LocationData, isOk bool) {
	gpsByte := genFskGpsBytes(location.GpsTime, location.Lon, location.Lat, location.Speed, int32(location.Direction), int32(location.Altitude), isOk)
	_ = m.SendBcXXWithDic(dic, uint16(0xbc03), 0, m.DevDmrid, gpsByte)
}

func (m *App) respCb01(dic uint8, lon, lat float64, _source, _target uint32, t time.Time, isRespCb01, isOk bool) {
	body := make([]byte, 0)
	cmd := 0xbc01
	if isRespCb01 {
		cmd = 0xbc00
		body = append(body, dic)
		body = append(body, uint16ToBytes(0xcb01)...)
	}

	body = append(body, genBCDTimeBytes(t, isOk)...) //time
	body = append(body, lat2bytes(lat, isOk)...)     //Lat 0...1
	body = append(body, lon2bytes(lon)...)           //Lon ....1
	body = append(body, make([]byte, 5)...)
	_ = m.SendBcXX(uint16(cmd), _source, _target, body)
}

func (m *App) GotCb01(dic uint8, bcxx uint16, data []byte) {
	// 卫星定位未开启
	if m.Cb42Code != 11 {
		_ = m.SendBc42(dic, m.Cb42Code)
		return
	}

	_ = bcxx
	_target, _source, _interval, _distance, _count, err := m.ParseCb01(data)

	log.Println("GotCb01 ,t:", dmrid2Hex(_target), " ,s:", dmrid2Hex(_source), " ,time:", _interval, " ,distance:", _distance, " ,count:", _count, " ,err:", err)

	if err != nil {
		return
	}
	if _target != m.DevDmrid {
		log.Println("GotCb01 ,but _target is not me,Target:", dmrid2Hex(_target), " ,me:", dmrid2Hex(m.DevDmrid))
		return
	}

	// 先应答
	m.DevStatus.SetCentralPositionMonitoring(1)
	_ = m.SendBc00(dic, 0xcb01)

	if m.Cb01Locator != nil {
		m.Cb01Locator.stop()
	}
	m.Cb01Locator = &Cb01Locator{
		needCount: _count,
		sendCount: 0,
		interval:  time.Duration(_interval*5) * (time.Second),
		distance:  _distance,
		endSignal: make(chan byte),
	}
	// CB01优先级高于CB02, 暂停CB02
	go func() {
		if m.Cb02Locator != nil {
			m.Cb02Locator.stop()
			m.Cb02Locator = nil
		}

		for {
			select {
			case <-m.Cb01Locator.endSignal:
				m.Cb01Locator = nil
				// 重新执行CB02
				for _, r := range m.CbxxArgsRows {
					if r.Cbxx == 0xcb02 {
						go m.RunCb02Task(r, nil)
						return
					}
				}
				// 没有需要执行的任务，循环结束
				return
			}
		}
	}()
	// 开始定位
	m.Cb01Locator.start()
}

func (m *App) SendBc02(dic uint8, cb02Obj *bfdx_proto.Cb02) error {
	// 01 bc02 00000000 000ad603 04 08000100c200 01 0001 01
	// 022013090824023087953111317723010002510077
	fskBody := make([]byte, 0)
	fskBody = append(fskBody, byte(cb02Obj.YN))
	fskBody = append(fskBody, int2BcdBytes(int(cb02Obj.Time))...)
	fskBody = append(fskBody, byte(cb02Obj.Size_))
	fskBody = append(fskBody, m.GetLastKnownLocationBytes()...)
	return m.SendBcXXWithDic(dic, 0xbc02, 0, m.DevDmrid, fskBody)
}

func (m *App) GotCb02(dic uint8, bcxx uint16, data []byte) {
	// 卫星定位未开启
	if m.Cb42Code != 11 {
		_ = m.SendBc42(dic, m.Cb42Code)
		return
	}

	_ = bcxx
	target, _, cb02Args, err := parseCb02Args(data)
	log.Println("GotCb02 args:", cb02Args, "target:", target)
	if err != nil {
		return
	}

	if target != m.DevDmrid {
		log.Println("GotCb02, but target is not me, Target:", dmrid2Hex(target), ", me:", dmrid2Hex(m.DevDmrid))
		return
	}

	row := &CbxxArgsRow{
		DmrId: m.DevDmridStr,
		Cbxx:  0xcb02,
	}

	// CB01的定位监控高于CB02，即在有CB01定位监控时，优先按CB01处理，直到CB01指令结束再恢复CB02监控
	switch cb02Args.YN {
	case 0: // 取消
		if m.Cb02Locator != nil {
			m.Cb02Locator.stop()
			m.Cb02Locator = nil
		}
		m.DevStatus.SetAutoPositionMonitoring(0)
		_ = m.SendBc00(dic, 0xcb02)
		// 从数据库删除
		err = row.Delete()
		if err != nil {
			log.Println("GotCb02 delete from db error:", err)
		}
		m.CbxxArgsRows = RemoveCbxxArgsRow(m.CbxxArgsRows, row)

	case 1: // 启动
		// 应答
		m.DevStatus.SetAutoPositionMonitoring(1)
		_ = m.SendBc00(dic, 0xcb02)
		// 启动任务
		go m.RunCb02Task(row, cb02Args)

	case 2: // 查询
		resCb02 := &bfdx_proto.Cb02{}
		var argsBytes []byte
		index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
		if index == -1 {
			err = row.QueryWithCbxx()
			argsBytes = row.Args
		} else {
			item := m.CbxxArgsRows[index]
			argsBytes = item.Args
		}
		// 上报查询结果
		_ = resCb02.Unmarshal(argsBytes)
		err = m.SendBc02(dic, resCb02)
		if err != nil {
			log.Println("SendBc02 error:", err)
		}

	default:
		log.Printf("GotCb02 unknown YN:%x\n", cb02Args.YN)
	}
}

func (m *App) GotCb03(dic uint8, bcxx uint16, data []byte) {
	// 卫星定位未开启
	if m.Cb42Code != 11 {
		_ = m.SendBc42(dic, m.Cb42Code)
		return
	}

	//_ = dic
	_ = bcxx

	target, _, minLat, minLon, maxLat, maxLon := parseCb03Body(data)
	log.Println("GotCb03 target:", dmrid2Hex(target), " , args:", minLat, minLon, maxLat, maxLon)
	m.Cb03AreaCheck = &Cb03AreaCheck{
		dic:        dic,
		isGroupCmd: target != m.DevDmrid,
		isResolved: false,
		MinLon:     minLon,
		MaxLon:     maxLon,
		MinLat:     minLat,
		MaxLat:     maxLat,
	}
	m.Cb03AreaCheck.start()
	LocationOnce(1)
}

func (m *App) SendBc04(dic uint8, cb04Obj *bfdx_proto.Cb04, tp byte, location *LocationData) error {
	// 05 bc04 00000000 000ad604 04 080009008200
	// 00 02 00 01 123086033 1113174850 123089395 1113179419
	fskBody := make([]byte, 0)
	// TP为0，返回查询结果
	fskBody = append(fskBody, tp)
	fskBody = append(fskBody, byte(cb04Obj.YN))
	fskBody = append(fskBody, byte(cb04Obj.PenN))
	fskBody = append(fskBody, byte(cb04Obj.Time))
	if tp == 0 {
		// 经纬度需要先拼接成完成的字节
		locationStr := lat2AsciiStr(cb04Obj.MinLat)
		locationStr += lon2AsciiStr(cb04Obj.MinLon)
		locationStr += lat2AsciiStr(cb04Obj.MaxLat)
		locationStr += lon2AsciiStr(cb04Obj.MaxLon)
		locationBytes, _ := hex.DecodeString(locationStr)
		fskBody = append(fskBody, locationBytes...)
	} else {
		// 报警上报，携带定位数据
		gpsByte := genFskGpsBytes(location.GpsTime, location.Lon, location.Lat, location.Speed, int32(location.Direction), int32(location.Altitude), true)
		fskBody = append(fskBody, gpsByte...)
	}

	return m.SendBcXXWithDic(dic, 0xbc04, 0, m.DevDmrid, fskBody)
}

func (m *App) GotCb04(dic uint8, bcxx uint16, data []byte) {
	// 卫星定位未开启
	if m.Cb42Code != 11 {
		_ = m.SendBc42(dic, m.Cb42Code)
		return
	}

	_ = bcxx
	target, _, args, err := parseCb04Args(data)
	log.Println("GotCb04 args:", args, "target:", target)
	if err != nil {
		return
	}

	if target != m.DevDmrid {
		log.Println("GotCb04, but target is not me, Target:", dmrid2Hex(target), ", me:", dmrid2Hex(m.DevDmrid))
		return
	}

	row := &CbxxArgsRow{
		DmrId: m.DevDmridStr,
		Cbxx:  0xcb04,
	}

	// YN: 1和2不能共存
	switch args.YN {
	case 0: // 取消
		if m.Cb04Locator != nil {
			m.Cb04Locator.stop()
			m.Cb04Locator = nil
		}
		m.DevStatus.SetEnableInBoundsMonitoring(0)
		m.DevStatus.SetInBoundsMonitoringAlarm(0)
		m.DevStatus.SetEnableOutOfBoundsMonitoring(0)
		m.DevStatus.SetOutOfBoundsMonitoringAlarm(0)
		_ = m.SendBc00(dic, 0xcb04)
		// 从数据库删除
		err = row.Delete()
		if err != nil {
			log.Println("GotCb04 delete from db error:", err)
		}
		m.CbxxArgsRows = RemoveCbxxArgsRow(m.CbxxArgsRows, row)

	case 1: // 启动入界监控，则取消出界监控
		// 应答
		m.DevStatus.SetEnableOutOfBoundsMonitoring(0)
		m.DevStatus.SetOutOfBoundsMonitoringAlarm(0)
		m.DevStatus.SetEnableInBoundsMonitoring(1)
		_ = m.SendBc00(dic, 0xcb04)
		// 启动任务
		go m.RunCb04Task(row, args)

	case 2: // 启动出界监控，则取消入界监控
		// 应答
		m.DevStatus.SetEnableInBoundsMonitoring(0)
		m.DevStatus.SetInBoundsMonitoringAlarm(0)
		m.DevStatus.SetEnableOutOfBoundsMonitoring(1)
		_ = m.SendBc00(dic, 0xcb04)
		// 启动任务
		go m.RunCb04Task(row, args)

	case 9: // 查询
		resCb04 := &bfdx_proto.Cb04{}
		var argsBytes []byte
		index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
		if index == -1 {
			err = row.QueryWithCbxx()
			argsBytes = row.Args
		} else {
			item := m.CbxxArgsRows[index]
			argsBytes = item.Args
		}
		// 上报查询结果
		_ = resCb04.Unmarshal(argsBytes)
		err = m.SendBc04(dic, resCb04, 0, nil)
		if err != nil {
			log.Println("SendBc04 error:", err)
		}

	default:
		log.Printf("GotCb04 unknown YN:%x\n", args.YN)
	}
}

// SendBc05 两种指令结构，一是返回查询参数，二是报警
func (m *App) SendBc05(dic uint8, cb05Obj *bfdx_proto.Cb05, tp byte, location *LocationData) error {
	fskBody := make([]byte, 0)
	fskBody = append(fskBody, tp)
	// TP为0，返回查询结果，tp==1: 回岗提示，tp==2：离岗报警
	if tp == 0 {
		// 2a bc05 00000000 000ad603 04 08000400c200 00 1 02 123089318 1113176736 27 29
		// 先拼接参数的字节
		ynStr := strconv.Itoa(int(cb05Obj.YN))
		timeStr := strconv.Itoa(int(cb05Obj.Time))
		latStr := lat2AsciiStr(cb05Obj.Lat)
		lonStr := lon2AsciiStr(cb05Obj.Lon)
		argsBytes, _ := hex.DecodeString(ynStr + timeStr + latStr + lonStr + cb05Obj.LatDif + cb05Obj.LonDif)
		fskBody = append(fskBody, argsBytes...)
	} else {
		// 报警上报，携带定位数据
		fskBody = append(fskBody, byte(cb05Obj.YN))
		fskBody = append(fskBody, byte(cb05Obj.Time))
		gpsByte := genFskGpsBytes(location.GpsTime, location.Lon, location.Lat, location.Speed, int32(location.Direction), int32(location.Altitude), true)
		fskBody = append(fskBody, gpsByte...)
	}

	return m.SendBcXXWithDic(dic, 0xbc05, 0, m.DevDmrid, fskBody)
}

func (m *App) GotCb05(dic uint8, bcxx uint16, data []byte) {
	// 卫星定位未开启
	if m.Cb42Code != 11 {
		_ = m.SendBc42(dic, m.Cb42Code)
		return
	}

	_ = bcxx
	target, _, args, err := parseCb05Args(data)
	log.Println("GotCb05 args:", args, "target:", target)
	if err != nil {
		return
	}

	if target != m.DevDmrid {
		log.Println("GotCb05, but target is not me, Target:", dmrid2Hex(target), ", me:", dmrid2Hex(m.DevDmrid))
		return
	}

	row := &CbxxArgsRow{
		DmrId: m.DevDmridStr,
		Cbxx:  0xcb05,
	}

	// YN: 1和2不能共存
	switch args.YN {
	case 0: // 取消
		if m.Cb05Locator != nil {
			m.Cb05Locator.stop()
			m.Cb05Locator = nil
		}
		m.DevStatus.SetEnableSentryMonitoring(0)
		m.DevStatus.SetSentryMonitoringAlarm(0)
		_ = m.SendBc00(dic, 0xcb05)
		// 从数据库删除
		m.RemoveCbxxArgsRow(row)

	case 1: // 启动入界监控，则取消出界监控
		// 应答
		m.DevStatus.SetEnableSentryMonitoring(1)
		m.DevStatus.SetSentryMonitoringAlarm(0)
		_ = m.SendBc00(dic, 0xcb05)
		// 启动任务
		go m.RunCb05Task(row, args)

	case 2: // 查询
		resCb05 := &bfdx_proto.Cb05{}
		argsBytes := m.GetCbxxArgsBytesWithQueryCmd(row)
		_ = resCb05.Unmarshal(argsBytes)
		err = m.SendBc05(dic, resCb05, 0, nil)
		if err != nil {
			log.Println("GotCb05 error:", err)
		}

	default:
		log.Printf("GotCb05 unknown YN:%x\n", args.YN)
	}
}

// SendBc06 两种指令结构，一是返回查询参数，二是报警
func (m *App) SendBc06(dic uint8, cb06Obj *bfdx_proto.Cb06, tp byte, location *LocationData) error {
	fskBody := make([]byte, 0)
	// 先拼接参数的字节
	tpStr := strconv.Itoa(int(tp))
	ynStr := strconv.Itoa(int(cb06Obj.YN))
	argsBytes, _ := hex.DecodeString(tpStr + ynStr)
	fskBody = append(fskBody, argsBytes...)
	fskBody = append(fskBody, byte(cb06Obj.Time))
	// TP为0，返回查询结果，tp==1: 开始走动提示。tp==2: 停留报警提示
	// 09 bc06 00000000 000ad604 04 080005008200 0 0 01
	if tp != 0 {
		// 报警上报，携带定位数据
		gpsByte := genFskGpsBytes(location.GpsTime, location.Lon, location.Lat, location.Speed, int32(location.Direction), int32(location.Altitude), true)
		fskBody = append(fskBody, gpsByte...)
	}

	return m.SendBcXXWithDic(dic, 0xbc06, 0, m.DevDmrid, fskBody)
}

func (m *App) GotCb06(dic uint8, bcxx uint16, data []byte) {
	// 卫星定位未开启
	if m.Cb42Code != 11 {
		_ = m.SendBc42(dic, m.Cb42Code)
		return
	}

	_ = bcxx
	target, _, args, err := parseCb06Args(data)
	log.Println("GotCb06 args:", args, "target:", target)
	if err != nil {
		return
	}

	if target != m.DevDmrid {
		log.Println("GotCb06, but target is not me, Target:", dmrid2Hex(target), ", me:", dmrid2Hex(m.DevDmrid))
		return
	}

	row := &CbxxArgsRow{
		DmrId: m.DevDmridStr,
		Cbxx:  0xcb06,
	}

	switch args.YN {
	case 0: // 取消
		if m.Cb06Locator != nil {
			m.Cb06Locator.stop()
			m.Cb06Locator = nil
		}
		m.DevStatus.SetEnableMobileMonitoring(0)
		m.DevStatus.SetMobileMonitoringAlarm(0)
		_ = m.SendBc00(dic, 0xcb06)
		// 从数据库删除
		m.RemoveCbxxArgsRow(row)

	case 1: // 启动移动监控
		// 应答
		m.DevStatus.SetEnableMobileMonitoring(1)
		m.DevStatus.SetMobileMonitoringAlarm(0)
		_ = m.SendBc00(dic, 0xcb06)
		// 启动任务
		go m.RunCb06Task(row, args)

	case 2: // 查询
		resCb06 := &bfdx_proto.Cb06{}
		argsBytes := m.GetCbxxArgsBytesWithQueryCmd(row)
		_ = resCb06.Unmarshal(argsBytes)
		err = m.SendBc06(dic, resCb06, 0, nil)
		if err != nil {
			log.Println("GotCb06 error:", err)
		}

	default:
		log.Printf("GotCb06 unknown YN:%x\n", args.YN)
	}
}

func (m *App) SendBc07(dic uint8, cb07Obj *bfdx_proto.Cb07) error {
	//24 bc07 00000000 000ad603 04 08000100c200 01 22 11 022013090824023087953111317723010002510077
	fskBody := make([]byte, 0)
	fskBody = append(fskBody, byte(cb07Obj.YN))
	fskBody = append(fskBody, int2BcdByte(int(cb07Obj.JtTime)))
	fskBody = append(fskBody, int2BcdByte(int(cb07Obj.DwTime)))
	fskBody = append(fskBody, m.GetLastKnownLocationBytes()...)
	return m.SendBcXXWithDic(dic, 0xbc07, 0, m.DevDmrid, fskBody)
}

func (m *App) GotCb07(dic uint8, bcxx uint16, data []byte) {
	_ = bcxx
	target, _, cb07Args, err := parseCb07Args(data)
	log.Println("GotCb07 args:", cb07Args, "target:", target)
	if err != nil {
		return
	}

	if target != m.DevDmrid {
		log.Println("GotCb07, but target is not me, Target:", dmrid2Hex(target), ", me:", dmrid2Hex(m.DevDmrid))
		return
	}

	row := &CbxxArgsRow{
		DmrId: m.DevDmridStr,
		Cbxx:  0xcb07,
	}

	switch cb07Args.YN {
	case 0: // 取消
		m.DevStatus.SetEnableEmergencyAlarm(0)
		_ = m.SendBc00(dic, 0xcb07)
		// 从数据库删除
		err = row.Delete()
		if err != nil {
			log.Println("GotCb07 delete from db error:", err)
		}
		m.CbxxArgsRows = RemoveCbxxArgsRow(m.CbxxArgsRows, row)
	case 1: // 启动
		m.DevStatus.SetEnableEmergencyAlarm(1)
		_ = m.SendBc00(dic, 0xcb07)

		m.CB07 = cb07Args

		index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
		if index == -1 {
			m.CbxxArgsRows = append(m.CbxxArgsRows, row)
		} else {
			m.CbxxArgsRows[index] = row
		}

		// 写入数据库
		argsByte, _ := cb07Args.Marshal()
		row.Args = argsByte
		err := row.InsertOrReplace()
		if err != nil {
			log.Println("Cb07 InsertOrReplace error:", err)
		}
	case 2: // 查询
		var resCb07 = m.CB07
		if m.CB07 == nil {
			resCb07 = &bfdx_proto.Cb07{}
			var argsBytes []byte
			index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
			if index == -1 {
				err = row.QueryWithCbxx()
				argsBytes = row.Args
			} else {
				item := m.CbxxArgsRows[index]
				argsBytes = item.Args
			}
			// 上报查询结果
			_ = resCb07.Unmarshal(argsBytes)
			m.CB07 = resCb07
		}
		err = m.SendBc07(dic, resCb07)
		if err != nil {
			log.Println("SendBc02 error:", err)
		}
	default:
		log.Printf("GotCb07 unknown YN:%x\n", cb07Args.YN)
	}
}

func (m *App) GotCb08(dic uint8, bcxx uint16, data []byte) {
	target, source, args, err := parseCb08Args(data)
	log.Println("GotCb08 args:", args, "target:", target)
	if err != nil {
		return
	}

	if target != m.DevDmrid {
		log.Println("GotCb08, but target is not me, Target:", dmrid2Hex(target), ", me:", dmrid2Hex(m.DevDmrid))
		return
	}

	switch args.YN {
	case 0: // 取消
		// 取消定时器
		if m.monitoringTimer != nil {
			m.monitoringTimer.Stop()
			m.monitoringTimer = nil
		}

		m.DevStatus.SetEnableMonitoringFunc(0)
		_ = m.SendBc00(dic, bcxx)

		// 结束当前监听的语音呼叫
		if GlobalMediaManager.mediaRecorder.isRecording.Load() {
			m.ProcessSpeakStop(&bfkcp.RpcCmd{})
		}

	case 1: // 启动
		// 正在进行通话，不启动监听
		if GlobalMediaManager.mediaRecorder.isRecording.Load() {
			_ = m.SendBc00(dic, bcxx)
			return
		}

		m.DevStatus.SetEnableMonitoringFunc(1)
		_ = m.SendBc00(dic, bcxx)

		// 开始监听呼叫
		callCmd := &bfkcp.RpcCmd{
			ParaStr: dmrid2Hex(source),
		}
		m.ProcessSpeakStart(callCmd, true)

		// 等待监听时间结束，停止呼叫
		go func() {
			m.monitoringTimer = time.NewTimer(time.Duration(args.Time) * time.Second)
			select {
			case <-m.monitoringTimer.C:
				// 定时器触发后的逻辑
				m.DevStatus.SetEnableMonitoringFunc(0)
				if GlobalMediaManager.mediaRecorder.isRecording.Load() {
					m.ProcessSpeakStop(&bfkcp.RpcCmd{})
					m.SendBc00ToSyncDevStatus()
				}
			}
		}()

	default:
		log.Printf("GotCb08 unknown YN:%x\n", args.YN)
	}
}

func (m *App) clearMonitoringAndTimer() {
	m.DevStatus.SetEnableMonitoringFunc(0)
	m.SendBc00ToSyncDevStatus()
	// 清除语音监控定时器
	if m.monitoringTimer != nil {
		m.monitoringTimer.Stop()
		m.monitoringTimer = nil
	}
	// 结束当前监听的语音呼叫
	if GlobalMediaManager.mediaRecorder.isRecording.Load() {
		m.ProcessSpeakStop(&bfkcp.RpcCmd{})
	}
}

func (m *App) SendBc09(dic uint8, cb09Obj *bfdx_proto.Cb09) error {
	//16 bc09 00000000 000ad603 04 080000008200 00 00 022013 090824 023087953111317723010002510077
	fskBody := make([]byte, 0)
	fskBody = append(fskBody, byte(cb09Obj.YN))
	fskBody = append(fskBody, int2BcdByte(int(cb09Obj.St)))
	fskBody = append(fskBody, m.GetLastKnownLocationBytes()...)
	return m.SendBcXXWithDic(dic, 0xbc09, 0, m.DevDmrid, fskBody)
}

func (m *App) GotCb09(dic uint8, bcxx uint16, data []byte) {
	target, _, args, err := parseCb09Args(data)
	log.Println("GotCb09 args:", args, "target:", target)
	if err != nil {
		return
	}

	if target != m.DevDmrid {
		log.Println("GotCb09, but target is not me, Target:", dmrid2Hex(target), ", me:", dmrid2Hex(m.DevDmrid))
		return
	}

	// 应答命令后，通知客户端锁机状态
	defer func() {
		if args.YN == 2 {
			return
		}

		m.NotifyDeviceLockStatus(args.YN, args.St)
	}()

	switch args.YN {
	case 0: // 开机
		m.DevStatus.SetForbiddenListen(0)
		m.DevStatus.SetForbiddenCall(0)
		_ = m.SendBc00(dic, bcxx)

	case 1: // 锁机
		switch args.St {
		case 1: // 禁听锁机
			m.DevStatus.SetForbiddenListen(1)
			m.DevStatus.SetForbiddenCall(0)
		case 2: // 禁发锁机
			m.DevStatus.SetForbiddenCall(1)
			m.DevStatus.SetForbiddenListen(0)
		case 3: // 禁听/禁发锁机
			m.DevStatus.SetForbiddenListen(1)
			m.DevStatus.SetForbiddenCall(1)
		}
		_ = m.SendBc00(dic, bcxx)

	case 2: //查询
		var St int32 = 0
		isForbiddenCall := m.DevStatus.GetForbiddenCall()
		isForbiddenListen := m.DevStatus.GetForbiddenListen()
		if isForbiddenCall == 1 && isForbiddenListen == 1 {
			St = 3
		} else if isForbiddenCall == 1 {
			St = 2
		} else if isForbiddenListen == 1 {
			St = 1
		} else {
			St = 0
		}
		resCb09 := &bfdx_proto.Cb09{
			YN: 1,
			St: St,
		}
		err = m.SendBc09(dic, resCb09)
		if err != nil {
			log.Println("GotCb09 error:", err)
		}

	default:
		log.Printf("GotCb09 unknown YN:%x\n", args.YN)
	}
}

func (m *App) GotCb10(dic uint8, bcxx uint16, data []byte) {
	// 取消紧急报警状态
	m.DevStatus.SetEmergencyAlarm(0)
	_ = bcxx
	_ = m.SendBc00(dic, 0xcb10)
	if m.Cb10Signal != nil {
		m.Cb10Signal <- 1
		close(m.Cb10Signal)
		m.Cb10Signal = nil
	}
	_, _ = m.Write(&bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_cb10)})
}

// 10:关闭状态 11:开启状态 14:不支持/不适用
func (m *App) SendBc42(dic uint8, code int32) error {
	fskBody := make([]byte, 0)
	fskBody = append(fskBody, byte(code))
	return m.SendBcXXWithDic(dic, 0xbc42, 0, m.DevDmrid, fskBody)
}

// 调用Cgo实现android端的gps开关设置
func (m *App) callUniproGpsSetMethod(code int32) {
	if code == 11 {
		enableGps()
	} else {
		disableGps()
	}
}

// 终端卫星定位模块动态开启/关闭
func (m *App) GotCb42(dic uint8, bcxx uint16, data []byte) {
	target, _, args, err := parseCb42Args(data)
	log.Println("GotCb42 args:", args, "target:", target)
	if err != nil {
		return
	}

	if target != m.DevDmrid {
		log.Println("GotCb42, but target is not me, Target:", dmrid2Hex(target), ", me:", dmrid2Hex(m.DevDmrid))
		return
	}

	row := &CbxxArgsRow{
		DmrId: m.DevDmridStr,
		Cbxx:  0xcb42,
	}

	// 查询
	if args.Code == 12 {
		args.Code = m.Cb42Code
		_ = m.SendBc42(dic, args.Code)
		m.callUniproGpsSetMethod(m.Cb42Code)
		return
	}

	// 关闭 或 开启
	if args.Code == 10 || args.Code == 11 {
		m.callUniproGpsSetMethod(args.Code)
		m.Cb42Code = args.Code
		_ = m.SendBc42(dic, args.Code)

		index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
		if index == -1 {
			m.CbxxArgsRows = append(m.CbxxArgsRows, row)
		} else {
			m.CbxxArgsRows[index] = row
		}

		// 写入数据库
		argsByte, _ := args.Marshal()
		row.Args = argsByte
		err := row.InsertOrReplace()
		if err != nil {
			log.Println("Cb42 InsertOrReplace error:", err)
		}
		return
	}
}

func (m *App) SendBcXXWithDic(dic uint8, bcxx uint16, target, source uint32, data []byte) error {
	// BC XX 命令头部
	fsk := make([]byte, 0)
	fsk = append(fsk, dic)
	fsk = append(fsk, uint16ToBytes(bcxx)...)
	fsk = append(fsk, uint32ToBytes(target)...)
	fsk = append(fsk, uint32ToBytes(source)...)
	// 场强
	fsk = append(fsk, m.FieldStrength)
	// 设备状态
	fsk = append(fsk, *m.DevStatus...)
	// 具体参数区域
	fsk = append(fsk, data...)

	Bcxx := &bfkcp.DeviceSend{
		Fsk: fsk,
	}
	bytes, err := Bcxx.Marshal()
	if err != nil {
		log.Println("SendBcXX Bcxx.Marshal Err", err)
		return err
	}

	log.Println("SendBcXX fsk hex:", hex.EncodeToString(Bcxx.Fsk))
	rpc_cmd := &bfkcp.RpcCmd{Cmd: 2, Body: bytes}
	err = kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("SendBcXX kcp send Err", err)
	}

	return err
}

func (m *App) SendBcXXWithDicAndStatus(dic uint8, bcxx uint16, target, source uint32, status []byte, data []byte) error {
	// BC XX 命令头部
	fsk := make([]byte, 0)
	fsk = append(fsk, dic)
	fsk = append(fsk, uint16ToBytes(bcxx)...)
	fsk = append(fsk, uint32ToBytes(target)...)
	fsk = append(fsk, uint32ToBytes(source)...)
	// 场强
	fsk = append(fsk, m.FieldStrength)
	// 设备状态
	fsk = append(fsk, status...)
	// 具体参数区域
	fsk = append(fsk, data...)

	Bcxx := &bfkcp.DeviceSend{
		Fsk: fsk,
	}
	bytes, err := Bcxx.Marshal()
	if err != nil {
		log.Println("SendBcXX Bcxx.Marshal Err", err)
		return err
	}

	log.Println("SendBcXX fsk hex:", hex.EncodeToString(Bcxx.Fsk))
	rpc_cmd := &bfkcp.RpcCmd{Cmd: 2, Body: bytes}
	err = kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("SendBcXX kcp send Err", err)
	}

	return err
}

func (m *App) SendBcXX(bcxx uint16, target, source uint32, data []byte) error {
	m.Dic++
	m.lastSendBcxxTime = time.Now()
	return m.SendBcXXWithDic(m.Dic, bcxx, target, source, data)
}

func (m *App) GetLastKnownLocationBytes() []byte {
	bytes := make([]byte, 0)
	if m.LastKnownLocation == nil {
		bytes = append(bytes, genFskGpsBytes(time.Now().UTC(), 0, 0, 0, 0, 0, false)...)
	} else {
		_time, err := time.Parse(TimeFormatStr, m.LastKnownLocation.GpsTime)
		isOk := err != nil
		bytes = append(bytes, genFskGpsBytes(_time, m.LastKnownLocation.Lon, m.LastKnownLocation.Lat, m.LastKnownLocation.Speed, m.LastKnownLocation.Direction, m.LastKnownLocation.Altitude, isOk)...)
	}
	return bytes
}

func (m *App) SendBc00(dic uint8, cbxx uint16) error {
	// 0c bc00 00000000 000ad604 04 08000100c200
	// 0c cb10 021507 090824 023087894111317715410002780106
	fskBody := make([]byte, 0)
	fskBody = append(fskBody, dic)
	fskBody = append(fskBody, uint16ToBytes(cbxx)...)
	fskBody = append(fskBody, m.GetLastKnownLocationBytes()...)
	return m.SendBcXXWithDic(dic, 0xbc00, 0, m.DevDmrid, fskBody)
}

// 处理语音监控的时候没有同步设备状态，发送bc00来同步状态
// sendBc15和sendBc71方法都没有携带设备状态，所以需要发送一个bc00来同步状态
func (m *App) SendBc00ToSyncDevStatus() {
	m.Dic++
	_ = m.SendBc00(m.Dic, 0xbc00)
}

func (m *App) SendConfirmSms(target, source uint32, smsNo int32) {
	msgNo := uint16ToBytes(uint16(smsNo))

	fsk := make([]byte, 0)
	fsk = append(fsk, msgNo...)

	//target与source逆过来，表示回应
	_ = m.SendBcXX(0xbc32, target, source, fsk)
}

func (m *App) SendSms(target, source uint32, codec byte, smsNo int32, data []byte) {
	msgNo := uint16ToBytes(uint16(smsNo))

	dataLen := int2BcdBytes(len(data))

	fsk := make([]byte, 0)
	fsk = append(fsk, codec)
	fsk = append(fsk, msgNo...)
	fsk = append(fsk, dataLen...)
	fsk = append(fsk, data...)
	log.Println(codec, msgNo, dataLen, data)
	_ = m.SendBcXX(0xbc31, target, source, fsk)
}

func (m *App) ConfirmJoinTaskGroup(source, target, groupDmrid uint32) {
	fsk := make([]byte, 0)
	fsk = append(fsk, uint32ToBytes(groupDmrid)...)
	_ = m.SendBcXX(0xbc38, target, source, fsk)
}

func (m *App) ConfirmExitTaskGroup(source, target, groupDmrid uint32) {
	fsk := make([]byte, 0)
	fsk = append(fsk, uint32ToBytes(groupDmrid)...)
	_ = m.SendBcXX(0xbc39, target, source, fsk)
}

var update_listen_group_list []string

func (m *App) ModifyListenGroup(ListenGroupList []string) {
	log.Println("update_listen_group_list:", ListenGroupList)
	update_listen_group_list = ListenGroupList
	oneChannelItem := &bfkcp.OneChannelItem{
		No:          1,
		SendGroup:   m.CurrentSpeakTarget,
		ListenGroup: ListenGroupList,
	}

	bytes, err := oneChannelItem.Marshal()
	if err != nil {
		log.Println("ModifyListenGroup oneChannelItem Marshal,err", err)
		return
	}

	rpc_cmd := &bfkcp.RpcCmd{Cmd: 311, Body: bytes}
	_ = kcpInstance.send(rpc_cmd)
}

func (m *App) GotUpdateListenGroupResp(rpc_cmd *bfkcp.RpcCmd) {
	log.Println("GotUpdateListenGroupResp rpc_cmd", rpc_cmd)
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_resp_update_listen_group_list), Res: int32(app_proto.ResCode_fialed)}
	if rpc_cmd.Res == 0 { //成功
		resp_cmd.Res = int32(app_proto.ResCode_success)
		m.ClearListenGroup()

		for _, item := range update_listen_group_list {
			m.ListenGroupList.Store(item, true)
		}
	}

	_, _ = m.Write(resp_cmd)
}

func (m *App) GotPartUpdateListenGroupResp(rpc_cmd *bfkcp.RpcCmd) {
	resp_cmd := &bfkcp.RpcCmd{Res: rpc_cmd.Res}

	log.Println("GotPartUpdateListenGroupResp RpcCmd", rpc_cmd)

	if rpc_cmd.Cmd == 312 {
		//add
		resp_cmd.Cmd = int32(app_proto.CmdCode_cmd_resp_add_listen_group)
	} else { //delete
		resp_cmd.Cmd = int32(app_proto.CmdCode_cmd_resp_delete_listen_group)
	}

	_, _ = m.Write(resp_cmd)

	lsGroup, err := up_cmd_cache.Value(rpc_cmd.Cmd<<16 + rpc_cmd.SeqNo)
	if err != nil {
		log.Println("err up_cmd_cache can not load data", rpc_cmd, up_cmd_cache)
		return
	}
	log.Println("GotPartUpdateListenGroupResp begin", lsGroup)
	log.Println("GotPartUpdateListenGroupResp begin", m.GetListenGroupList())
	if rpc_cmd.Res != 0 {
		return
	}

	if rpc_cmd.Cmd == 312 { //add
		for _, oneGroup := range lsGroup.Data().(*updateListenGroupReq).lsGroup {
			m.ListenGroupList.Store(oneGroup, true)
		}
	} else { //remove
		for _, oneGroup := range lsGroup.Data().(*updateListenGroupReq).lsGroup {
			m.ListenGroupList.Delete(oneGroup)
		}
	}
	log.Println("GotPartUpdateListenGroupResp end", m.GetListenGroupList())
}

func (m *App) OnGotAddrBook(rpc_cmd *bfkcp.RpcCmd) {
	resultCmd := int32(app_proto.CmdCode_cmd_resp_query_addr_book_by_dmrid)
	if rpc_cmd.SeqNo == 0 {
		resultCmd = int32(app_proto.CmdCode_cmd_resp_query_addr_book_by_dmrid_proxy)
	}
	if rpc_cmd.Res < 0 {
		_, _ = m.Write(&bfkcp.RpcCmd{Cmd: resultCmd, SeqNo: rpc_cmd.SeqNo, Res: rpc_cmd.Res})
		return
	}

	addrBookList := &bfkcp.AddrBookList{}
	err := addrBookList.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("OnGotAddrBook unmarshal err:", err)
	}

	result := &app_proto.ReqAddressBookResult{
		SuccessList: make([]*app_proto.AddressBook, 0),
		FailedList:  make([]string, 0),
	}

	for _, addrBook := range addrBookList.AddrBookList {
		if addrBook.Code < 0 {
			log.Println("err addrBook, dmrid:", addrBook.ParaStr, " ,code:", addrBook.Code)
			result.FailedList = append(result.FailedList, addrBook.ParaStr)
			continue
		}

		addr := app_proto.AddressBook{
			ParentDmrid: "",
			Dmrid:       addrBook.ParaStr,
			Name:        "",
			DevType:     0,
		}

		if addrBook.Type == 1 {
			//组呼
			org := &bfdx_proto.DbOrg{}
			err2 := org.Unmarshal(addrBook.Body)
			if err2 != nil {
				result.FailedList = append(result.FailedList, addrBook.ParaStr)
				continue
			}
			log.Println("addrbook_Org:", org)
			addr.Name = org.OrgShortName
			addr.ParentDmrid = org.ParentOrgId
			addr.DevType = org.OrgIsVirtual
		} else {
			//单呼
			dev := &bfdx_proto.DbDevice{}
			err2 := dev.Unmarshal(addrBook.Body)
			if err2 != nil {
				result.FailedList = append(result.FailedList, addrBook.ParaStr)
				continue
			}
			log.Println("addrbook_Dev:", dev)
			addr.Name = dev.SelfId
			addr.ParentDmrid = dev.OrgId
			addr.DevType = dev.DeviceType
		}
		result.SuccessList = append(result.SuccessList, &addr)
		m.UnknownUserBookList.Store(addr.Dmrid, &addr)
	}
	go GlobalMediaManager.mediaPlayer.UpdateNotifySpeakInfo(false)
	b, err := result.Marshal()
	if err != nil {
		log.Println("OnGotAddrBook addr.Marshal err", err)
		_, _ = m.Write(&bfkcp.RpcCmd{Cmd: resultCmd, Res: -1, SeqNo: rpc_cmd.SeqNo})
		return
	}
	_, _ = m.Write(&bfkcp.RpcCmd{Cmd: resultCmd, Body: b, SeqNo: rpc_cmd.SeqNo})
}

func (m *App) QueryOnlineDevices() {
	rpc_cmd := &bfkcp.RpcCmd{Cmd: 333}
	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("QueryOnlineDevices send Err", err)
	}
}

func (m *App) getOnlineDevices() []int32 {
	onlineDevs := make([]int32, 0)
	m.OnlineDevices.Range(func(key int32, value bool) bool {
		_ = value
		onlineDevs = append(onlineDevs, key)
		return true
	})

	return onlineDevs
}

func (m *App) PressOnlineDevices() {
	exOnlineDevices := &bfkcp.ExOnelineDevices{
		DmrId: m.getOnlineDevices(),
	}
	data, err := exOnlineDevices.Marshal()
	if err != nil {
		log.Println("PressOnlineDevices exOnlineDevices.Marshal Err", err)
		return
	}
	m.PressNotify(&app_proto.Notify{
		Code: 333,
		Body: data,
	})
}

func (m *App) OnGotOnlineDevices(rpc_cmd *bfkcp.RpcCmd) {
	exOnlineDevices := &bfkcp.ExOnelineDevices{}
	err := exOnlineDevices.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("OnGotOnlineDevices exOnlineDevices Unmarshal Err:", err)
		return
	}
	log.Println("OnGotOnlineDevices", exOnlineDevices)

	//first pkg,init cache
	if rpc_cmd.Res == 1 {
		m.OnlineDevices = TDmridMap{}
	}

	for _, dmrid := range exOnlineDevices.DmrId {
		m.OnlineDevices.Store(dmrid, true)
	}

	//last pkg,sync to client
	if int64(rpc_cmd.Res) == rpc_cmd.ParaInt {
		go m.PressOnlineDevices()
	}
}

func (m *App) GotOfflinePanic(rpc_cmd *bfkcp.RpcCmd) {
	_ = rpc_cmd
	m.loginQuit()
	resp_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_force_exit)}

	log.Println("GotOfflinePanic RpcCmd", rpc_cmd)

	_, _ = m.Write(resp_cmd)
}

func (m *App) OnAppGetLocation(location *LocationData) {
	if m.Cb42Code != 11 {
		log.Printf("OnAppGetLocation location not enable: cb42 code = %d", m.Cb42Code)
		return
	}

	IsLocationOnce := m.IsLocationOnce.Load()
	activeStatus := int32(0)
	if IsLocationOnce {
		activeStatus = 1
	}
	m.LastKnownLocation = location.toAppGps84()
	m.PressGpsInfo(m.DevDmridStr, m.LastKnownLocation, activeStatus)
	// 存储到数据库中
	go func() {
		bytes, _ := m.LastKnownLocation.Marshal()
		r := &LastKnownLocationRow{
			DmrId:    m.DevDmridStr,
			Location: bytes,
		}
		_ = r.Delete()
		_ = r.Insert()
	}()

	// 主动定位一次，发送BC01
	if IsLocationOnce {
		m.IsLocationOnce.Store(false)
		m.sendBc01(location, true)
		return
	}

	// 定位优先级：紧急报警定位 > CB01中心监控定位 > CB02自动监控定位
	if m.Bc18Locator != nil {
		m.Bc18Locator.onLocation(location)
	}
	if m.Cb01Locator != nil {
		m.Cb01Locator.onLocation(location)
	}
	if m.Cb02Locator != nil {
		m.Cb02Locator.onLocation(location)
	}
	if m.Cb03AreaCheck != nil {
		m.Cb03AreaCheck.onLocation(location)
	}
	if m.Cb04Locator != nil {
		m.Cb04Locator.onLocation(location)
	}
	if m.Cb05Locator != nil {
		m.Cb05Locator.onLocation(location)
	}
	if m.Cb06Locator != nil {
		m.Cb06Locator.onLocation(location)
	}
	//if !m.IsPPTPressed.Load() {
	//	return
	//}
	//m.IsPPTPressed.Store(false)
}

func (m *App) RequestMapToken() {
	rpc_cmd := &bfkcp.RpcCmd{Cmd: 1440}
	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("RequestMapToken send Err", err)
		return
	}
}

func (m *App) OnGotMapToken(cmd *bfkcp.RpcCmd) {
	cmd.Cmd = int32(app_proto.CmdCode_cmd_resp_map_token)
	_, _ = m.Write(cmd)

	if cmd.Res != 0 {
		log.Println("OnGotMapToken err:", cmd.Res)
		return
	}

	m.mapToken = uint32(cmd.ParaInt)
	m.mapTokenSetTime = time.Now()
}

func (m *App) ProcessRequestDeviceGpsLocationPermission(cmd *bfkcp.RpcCmd) {
	rpc_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_req_device_gps_location_permission)}
	rpc_cmd.Body = cmd.Body

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("kcp RequestDeviceGpsLocationPermission send Err", err)
		return
	}
}

func (m *App) ProcessRequestQueryDeviceGpsLocationPermission(cmd *bfkcp.RpcCmd) {
	rpc_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_req_query_device_gps_location_permission)}
	rpc_cmd.Body = cmd.Body

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("kcp ProcessRequestQueryDeviceGpsLocationPermission send Err", err)
		return
	}
}

func (m *App) GotDeviceRespGpsLocationPermission(cmd *bfkcp.RpcCmd) {
	res := &app_proto.ResGpsPermission{}
	err := res.Unmarshal(cmd.Body)
	if err != nil {
		log.Println("kcp GotDeviceRespGpsLocationPermission Unmarshal Err", err)
		return
	}

	log.Println("kcp GotDeviceRespGpsLocationPermission", res)
	cmd.Cmd = int32(app_proto.CmdCode_cmd_resp_device_gps_location_permission)
	_, _ = m.Write(cmd)
}

func (m *App) GotDeviceRespQueryGpsLocationPermission(cmd *bfkcp.RpcCmd) {
	cmd.Cmd = int32(app_proto.CmdCode_cmd_resp_query_device_gps_location_permission)
	_, _ = m.Write(cmd)
}

func (m *App) NotifyDeviceStatus() {
	cmd := &bfkcp.RpcCmd{
		Cmd:     int32(app_proto.CmdCode_cmd_notify_device_status),
		ParaBin: *m.DevStatus,
	}
	_, _ = m.Write(cmd)
}

func (m *App) NotifyDeviceLockStatus(yn, st int32) {
	cmd := &bfkcp.RpcCmd{
		Cmd:     int32(app_proto.CmdCode_cmd_notify_lock_device_status),
		ParaBin: []byte{byte(yn), byte(st)},
	}
	_, _ = m.Write(cmd)
}
