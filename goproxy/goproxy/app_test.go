package goproxy

import (
	"reflect"
	"testing"
)

func Test_respCb01Test(t *testing.T) {
	//type args struct {
	//	dic        uint8
	//	Lon        float64
	//	Lat        float64
	//	_source    uint32
	//	_target    uint32
	//	isRespCb01 bool
	//}
	//tests := []struct {
	//	name string
	//	args args
	//	want []byte
	//}{
	//	{
	//		name: "t1",
	//		args: args{
	//			dic:        0,
	//			Lon:        0,
	//			Lat:        0,
	//			_source:    0xeeeeeeee,
	//			_target:    0xffffffff,
	//			isRespCb01: false,
	//		},
	//		want: []byte{},
	//	},
	//}
	//for _, tt := range tests {
	//	t.Run(tt.name, func(t *testing.T) {
	//		if got := respCb01Test(tt.args.dic, tt.args.Lon, tt.args.Lat, tt.args._source, tt.args._target, tt.args.isRespCb01); !reflect.DeepEqual(got, tt.want) {
	//			t.Errorf("respCb01Test() = %v, want %v", got, tt.want)
	//		}
	//	})
	//}
}

func Test_parseSn(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name          string
		args          args
		wantSnInts    []uint16
		wantIsValidSn bool
	}{
		{
			name:          "invalid len",
			args:          args{s: ""},
			wantSnInts:    make([]uint16, 24),
			wantIsValidSn: false,
		},
		{
			name:          "invalid code 0crd",
			args:          args{s: "0crd968112a77dd92fb1ce9e26fb3531ba51bdc5a168c072b2bd36e967c7534ec0b007209b98f40a000000000000083c"},
			wantSnInts:    make([]uint16, 24),
			wantIsValidSn: false,
		},
		{
			name:          "success-1",
			args:          args{s: "0098968112a77dd92fb1ce9e26fb3531ba51bdc5a168c072b2bd36e967c7534ec0b007209b98f40a0000000000000000"},
			wantSnInts:    []uint16{0x0098, 0x9681, 0x12a7, 0x7dd9, 0x2fb1, 0xce9e, 0x26fb, 0x3531, 0xba51, 0xbdc5, 0xa168, 0xc072, 0xb2bd, 0x36e9, 0x67c7, 0x534e, 0xc0b0, 0x0720, 0x9b98, 0xf40a, 0x0000, 0x0000, 0x0000, 0x0000},
			wantIsValidSn: true,
		},
		{
			name:          "success-2",
			args:          args{s: "0cdd968112a77dd92fb1ce9e26fb3531ba51bdc5a168c072b2bd36e967c7534ec0b007209b98f40a000000000000083c"},
			wantSnInts:    []uint16{0x0cdd, 0x9681, 0x12a7, 0x7dd9, 0x2fb1, 0xce9e, 0x26fb, 0x3531, 0xba51, 0xbdc5, 0xa168, 0xc072, 0xb2bd, 0x36e9, 0x67c7, 0x534e, 0xc0b0, 0x0720, 0x9b98, 0xf40a, 0x0000, 0x0000, 0x0000, 0x083c},
			wantIsValidSn: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSnInts, gotIsValidSn := parseSn(tt.args.s)
			if !reflect.DeepEqual(gotSnInts, tt.wantSnInts) {
				t.Errorf("parseSn() gotSnInts = %v, want %v", gotSnInts, tt.wantSnInts)
			}
			if gotIsValidSn != tt.wantIsValidSn {
				t.Errorf("parseSn() gotIsValidSn = %v, want %v", gotIsValidSn, tt.wantIsValidSn)
			}
		})
	}
}
