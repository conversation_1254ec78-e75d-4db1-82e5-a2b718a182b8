// 处理配置工具app_config的请求
package goproxy

import (
	"io"
	"log"
	"net/http"
	"sync/atomic"
	"time"

	"goproxy/app_proto"
	"goproxy/bfkcp"
)

func appBuildInfo(w http.ResponseWriter, r *http.Request) {
	info := GetAppBuildInfo()
	if info == nil {
		// write response 404
		http.Error(w, "not found", http.StatusNotFound)
		return
	}

	b, err := info.Marshal()
	if err != nil {
		// write server error 500
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	_, err = w.Write(b)
	if err != nil {
		log.Println("appBuildInfo write err:", err)
	}
}

var appConfigResponseChMap = make(map[int32]chan *bfkcp.RpcCmd)

func appConfig(w http.ResponseWriter, r *http.Request) {
	log.Println("appConfig", r.Method)
	switch r.Method {
	case "GET":
		onGetAppConfig(w, r)
	case "POST":
		onPostAppConfig(w, r)
	default:
		log.Println("appConfig unknown method:", r.Method)
	}
}

func onGetAppConfig(w http.ResponseWriter, r *http.Request) {
	// 生成唯一ID
	id := generateUniqueID()
	// 创建响应通道
	responseCh := make(chan *bfkcp.RpcCmd, 1)
	appConfigResponseChMap[id] = responseCh

	// 发送请求
	rpc_cmd := &bfkcp.RpcCmd{
		SeqNo: id,
		Cmd:   int32(app_proto.CmdCode_cmd_req_query_app_config),
	}
	err, _ := GlobalApp.Write(rpc_cmd)
	if err != nil {
		// write server error 500
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 等待响应
	select {
	case rpc_cmd := <-responseCh:
		if rpc_cmd.Res != int32(app_proto.ResCode_success) {
			// write server error 500
			http.Error(w, rpc_cmd.ParaStr, http.StatusInternalServerError)
		}
		_, _ = w.Write(rpc_cmd.Body)
	case <-time.After(time.Second * 10):
		// write server error 500
		http.Error(w, "onGetAppConfig timeout", http.StatusGatewayTimeout)
	}
}

func onPostAppConfig(w http.ResponseWriter, r *http.Request) {
	// 生成唯一ID
	id := generateUniqueID()
	// 创建响应通道
	responseCh := make(chan *bfkcp.RpcCmd, 1)
	appConfigResponseChMap[id] = responseCh

	// 发送请求
	b, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
	}
	rpc_cmd := &bfkcp.RpcCmd{
		SeqNo: id,
		Cmd:   int32(app_proto.CmdCode_cmd_req_set_app_config),
		Body:  b,
	}
	err, _ = GlobalApp.Write(rpc_cmd)
	if err != nil {
		// write server error 500
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 等待响应
	select {
	case rpc_cmd := <-responseCh:
		if rpc_cmd.Res != int32(app_proto.ResCode_success) {
			// write server error 500
			http.Error(w, rpc_cmd.ParaStr, http.StatusInternalServerError)
			return
		}
		w.WriteHeader(http.StatusOK)
	case <-time.After(time.Second * 10):
		// write server error 500
		http.Error(w, "onPostAppConfig timeout", http.StatusGatewayTimeout)
	}
}

var counter uint32 = 0

func generateUniqueID() int32 {
	timestamp := uint32(time.Now().UnixMilli() >> 16)       // 取时间戳的高16位
	id := (timestamp << 16) | atomic.AddUint32(&counter, 1) // 组合时间戳和计数器
	return int32(id)
}
