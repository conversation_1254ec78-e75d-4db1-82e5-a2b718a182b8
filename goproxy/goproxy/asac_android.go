//go:build android && (arm64 || arm)
// +build android
// +build arm64 arm

package goproxy

//#cgo CFLAGS: -I./lib/asac_android64
//#cgo arm LDFLAGS: -L${SRCDIR}/lib/asac_android32 -lasac_armeabi-v7
//#cgo arm LDFLAGS: -L${SRCDIR}/lib/asac_android32 -lspeexdsp
//#cgo arm LDFLAGS: -L${SRCDIR}/lib/asac_android32 -lrnnoise
//#cgo arm64 LDFLAGS: -L${SRCDIR}/lib/asac_android64 -lasac
//#cgo arm64 LDFLAGS: -L${SRCDIR}/lib/asac_android64 -lspeexdsp
//#cgo arm64 LDFLAGS: -L${SRCDIR}/lib/asac_android64 -lrnnoise
//#cgo LDFLAGS:  -lm
//#include "libasac.h"
//#include "bfasac.c"
//#include "denoise.c"
import "C"
import (
	"unsafe"
)

func InitAsac(sn unsafe.Pointer) {
	C.asacInit((*C.short)(sn))
}

func ResetAllPitch() {
	C.resetAllPitch()
}

// 60ms ambe27 to pcm960
func AsacDecode60ms(ambe27 unsafe.Pointer, pcm960 unsafe.Pointer, ns, gain int16) {
	if !IsSetSn() {
		return
	}
	C.asacDecode60ms((*C.uchar)(ambe27), (*C.short)(pcm960), (C.short)(ns), (C.short)(gain))
}

// 60ms pcm960 to ambe27
func AsacEncode60ms(pcm960 unsafe.Pointer, ambe27 unsafe.Pointer, ns int16) {
	if !IsSetSn() {
		return
	}
	C.asacEncode60ms((*C.short)(pcm960), (*C.uchar)(ambe27), (C.short)(ns))
}

// denoise by rnnoise,48k,10ms data(480 short),resample to 8k,80 short in place
// return sample count in 8k,80 (short)
func DenoisePcm480Sample(pcm480 unsafe.Pointer) int {
	//log.Println("DenoisePcm480Sample called ")
	return (int)(C.denoisePcm480Sample(pcm480))
}

// short pcm8k80sample[80], short pcm48k480sample[480]
func Resample8kTo48k(pcm8k, pcm48k unsafe.Pointer) int {
	return (int)(C.speex_resample8kTo48k((*C.short)(pcm8k), (*C.short)(pcm48k)))
}

func PortAudioStartRecord(m *MediaRecorder) {
	panic("no portaudio in android now")
}

func PortAudioStartPlay(onSamples func(pOutputSample, pInputSamples []byte, needCount uint32) uint32, isPlaying func() bool) {
	panic("no portaudio in android now")
}
