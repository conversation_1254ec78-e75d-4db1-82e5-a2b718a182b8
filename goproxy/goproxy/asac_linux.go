//go:build linux && amd64
// +build linux,amd64

package goproxy

//#cgo CFLAGS: -I./lib/asac_linux64
//#cgo LDFLAGS: -L${SRCDIR}/lib/asac_linux64 -lasac
//#cgo LDFLAGS: -lspeexdsp -lrnnoise -lportaudio
//#include "libasac.h"
//#include "portaudio.h"
//#include "bfasac.c"
//#include "denoise.c"
import "C"
import (
	"log"
	"runtime"
	"time"
	"unsafe"
)

func InitAsac(sn unsafe.Pointer) {
	C.asacInit((*C.short)(sn))
}

func init() {
	initOK := (int)(C.Pa_Initialize())
	if initOK != 0 {
		log.Println("init portaudio err")
		return
	}
}

func ResetAllPitch() {
	//C.resetPitch()
}

// 60ms ambe27 to pcm960
func AsacDecode60ms(ambe27 unsafe.Pointer, pcm960 unsafe.Pointer, ns, gain int16) {
	C.asacDecode60ms((*C.uchar)(ambe27), (*C.short)(pcm960), (C.short)(ns), (C.short)(gain))
	ResetAllPitch()
}

// 60ms pcm960 to ambe27
func AsacEncode60ms(pcm960 unsafe.Pointer, ambe27 unsafe.Pointer, ns int16) {
	C.asacEncode60ms((*C.short)(pcm960), (*C.uchar)(ambe27), (C.short)(ns))
	ResetAllPitch()
}

// denoise by rnnoise,48k,10ms data
func DenoisePcm480Sample(pcm480 unsafe.Pointer) int {
	return (int)(C.denoisePcm480Sample(pcm480))
}

// short pcm8k80sample[80], short pcm48k480sample[480]
func Resample8kTo48k(pcm8k, pcm48k unsafe.Pointer) int {
	//return (int)(C.speex_resample8kTo48k((*C.short)(pcm8k), (*C.short)(pcm48k)))
	panic("Resample8kTo48k")
}

func PortAudioStartRecord(m *MediaRecorder) {
	runtime.LockOSThread()

	pcmbuf := make([]byte, 480*2)

	paNoError := 0
	paInputOverflowed := -9981
	err := 0

	var stream unsafe.Pointer
	openOK := (int)(C.Pa_OpenDefaultStream(&stream, 1, 0, 0x00000008, 48000, 0, nil, nil))

	if openOK != 0 {
		log.Println("Pa_OpenDefaultStream err")
		goto error
	}

	err = (int)(C.Pa_StartStream(stream))
	if err != paNoError {
		goto error
	}

	for m.isRecording.Load() {
		err = (int)(C.Pa_ReadStream(stream, unsafe.Pointer(&pcmbuf[0]), 480))

		if err != paNoError && err != paInputOverflowed {
			//出错了
			goto error
		}
		m.onSamples(nil, pcmbuf, 480)

	}

	err = (int)(C.Pa_CloseStream(stream))

	if err != paNoError {
		goto error
	}

	return

error:
	//get portaudio error message
	log.Println("Pa err:", C.GoString(C.Pa_GetErrorText(C.PaError(err))))
	//C.printf("pa err:%s\n", C.Pa_GetErrorText(C.PaError(err)))
}

func PortAudioStartPlay(onSamples func(pOutputSample, pInputSamples []byte, needCount uint32) uint32, isPlaying func() bool) {
	runtime.LockOSThread()

	pcmbuf := make([]byte, 480*2)

	paNoError := 0
	paOutputUnderflowed := -9980
	err := 0

	var stream unsafe.Pointer
	openOK := (int)(C.Pa_OpenDefaultStream(&stream, 0, 1, 0x00000008, 8000, 0, nil, nil))

	if openOK != 0 {
		log.Println("Pa_OpenDefaultStream err")
		goto error
	}

	err = (int)(C.Pa_StartStream(stream))
	if err != paNoError {
		goto error
	}

	for isPlaying() {
		samplecount := onSamples(pcmbuf, nil, 480)

		if samplecount == 0 {
			time.Sleep(50 * time.Millisecond)
			continue
		}

		err = (int)(C.Pa_WriteStream(stream, unsafe.Pointer(&pcmbuf[0]), (C.ulong)(samplecount)))

		if err != paNoError && err != paOutputUnderflowed {
			//出错了
			goto error
		}

	}

	err = (int)(C.Pa_CloseStream(stream))

	if err != paNoError {
		goto error
	}

	return

error:
	//get portaudio error message
	//C.printf("pa err:%s\n", C.Pa_GetErrorText(err))
	log.Println("Pa err:", C.GoString(C.Pa_GetErrorText(C.PaError(err))))
}

/*
func initAsacTest() {
	sn := []uint16{0x0098, 0x9681, 0x12a7, 0x7dd9, 0x2fb1, 0xce9e, 0x26fb,
		0x3531, 0xba51, 0xbdc5, 0xa168, 0xc072, 0xb2bd, 0x36e9,
		0x67c7, 0x534e, 0xc0b0, 0x0720, 0x9b98, 0xf40a, 0x0000, 0x0000, 0x0000, 0x0000}
	C.asacInit((*C.short)(unsafe.Pointer(&sn[0])))
}

func getSn() {
	sn := make([]uint16, 24)
	C.getSn((*C.short)(unsafe.Pointer(&sn[0])))
	log.Println("sn", sn)
}

func asacEncode20ms(pcm160 unsafe.Pointer, ambe9 unsafe.Pointer, ns int16) {
	C.asacEncode20ms((*C.short)(pcm160), (*C.uchar)(ambe9), (C.short)(ns))
}

func asacDecode20ms(ambe9 unsafe.Pointer, pcm160 unsafe.Pointer, ns, Gain int16) {
	C.asacDecode20ms((*C.uchar)(ambe9), (*C.short)(pcm160), (C.short)(ns), (C.short)(Gain))
}

func mediaPlayerTest() {
	file, err := os.Open("/home/<USER>/Workspace/git.kicad99.com/bfdx/bf8100app/test/demo.wav")
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}

	defer file.Close()

	var reader io.Reader
	var channels, sampleRate uint32

	w := wav.NewReader(file)
	f, err := w.Format()
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}

	reader = w
	channels = uint32(f.NumChannels)
	sampleRate = f.SampleRate

	ctx, err := malgo.InitContext(nil, malgo.ContextConfig{}, func(message string) {
		fmt.Printf("LOG <%v>\n", message)
	})
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}
	defer func() {
		_ = ctx.Uninit()
		ctx.Free()
	}()

	deviceConfig := malgo.DefaultDeviceConfig(malgo.Playback)
	deviceConfig.Playback.Format = malgo.FormatS16
	deviceConfig.Playback.Channels = channels
	deviceConfig.SampleRate = sampleRate
	deviceConfig.Alsa.NoMMap = 1

	// This is the function that's used for sending more data to the device for playback.
	onSamples := func(pOutputSample, pInputSamples []byte, framecount uint32) {
		io.ReadFull(reader, pOutputSample)
	}

	deviceCallbacks := malgo.DeviceCallbacks{
		Data: onSamples,
	}
	device, err := malgo.InitDevice(ctx.Context, deviceConfig, deviceCallbacks)
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}
	defer device.Uninit()

	err = device.Start()
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}

	log.Println("Press Enter to quit...")
	fmt.Scanln()
}

func mediaPlayerTest1() {
	file, err := os.Open("/home/<USER>/Workspace/git.kicad99.com/bfdx/bf8100app/test/demo.wav")
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}

	defer file.Close()

	var reader io.Reader

	reader = wav.NewReader(file)

	byteArr := make([]byte, 960)
	bytesArr := make([]byte, 0)
	for {
		len, err := reader.Read(byteArr)
		if len == 0 {
			break
		}
		if err != nil {
			break
		}
		bytesArr = append(bytesArr, byteArr...)
	}
	playMedia(bytesArr)
}

func playMedia(data []byte) {
	ctx, err := malgo.InitContext(nil, malgo.ContextConfig{}, func(message string) {
		fmt.Printf("LOG <%v>\n", message)
	})
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}
	defer func() {
		_ = ctx.Uninit()
		ctx.Free()
	}()

	deviceConfig := malgo.DefaultDeviceConfig(malgo.Playback)
	deviceConfig.Playback.Format = malgo.FormatS16
	deviceConfig.Playback.Channels = 1
	deviceConfig.SampleRate = 8000

	// This is the function that's used for sending more data to the device for playback.
	onSamples := func(pOutputSample, pInputSamples []byte, framecount uint32) {
		//log.Println("pOutputSample:", len(pOutputSample)," ,framecount",framecount)
		oLen := int(framecount) * 2
		if oLen > len(data) {
			copy(pOutputSample, data)
		} else {
			copy(pOutputSample, data[0:oLen])
		}
		data = data[oLen:]
	}

	deviceCallbacks := malgo.DeviceCallbacks{
		Data: onSamples,
	}
	device, err := malgo.InitDevice(ctx.Context, deviceConfig, deviceCallbacks)
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}
	defer device.Uninit()

	err = device.Start()
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}

	log.Println("Press Enter to quit...")
	fmt.Scanln()
}

func asacEncodeTest() {
	file, err := os.Open("/home/<USER>/Workspace/git.kicad99.com/bfdx/bf8100app/test/demo.wav")
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}
	defer file.Close()

	r := bufio.NewReader(file)

	pcm160 := make([]uint16, 160)
	ambe9 := make([]byte, 9)
	pcm160_decode := make([]uint16, 160)
	result_decode := make([]uint16, 0)

	buf := make([]byte, 320) //一次读取多少个字节

	for {
		n, err := r.Read(buf)
		if err != nil && err != io.EOF {
			panic(err)
		}
		if 0 == n {
			break
		}
		for i := 0; i < 160; i++ {
			pcm160[i] = *(*uint16)(unsafe.Pointer(&buf[i*2]))
		}

		asacEncode20ms(unsafe.Pointer(&pcm160[0]), unsafe.Pointer(&ambe9[0]), 1)
		asacDecode20ms(unsafe.Pointer(&ambe9[0]), unsafe.Pointer(&pcm160_decode[0]), 1, 0)

		result_decode = append(result_decode, pcm160_decode...)

		//log.Println("pcm160", pcm160)
		//log.Println("ambe9", ambe9)
		//log.Println("pcm160_decode", pcm160_decode)
	}

	byteArr := make([]byte, 0)

	for i := 0; i < len(result_decode); i++ {
		byteArr = append(byteArr, uint8(result_decode[i]&0xff))
		byteArr = append(byteArr, uint8(result_decode[i]>>8))
	}

	playMedia(byteArr)
}

func asacDecodeTest() {
	file, err := os.Open("/home/<USER>/Workspace/git.kicad99.com/bfdx/bf8100app/test/demo.ambe")
	if err != nil {
		log.Println(err)
		os.Exit(1)
	}

	defer file.Close()

	r := bufio.NewReader(file)

	pcm160 := make([]uint16, 160)
	ambe9 := make([]byte, 9)

	result := make([]uint16, 0)
	for {
		n, err := r.Read(ambe9)
		if err != nil && err != io.EOF {
			panic(err)
		}
		if 0 == n {
			break
		}
		asacDecode20ms(unsafe.Pointer(&ambe9[0]), unsafe.Pointer(&pcm160[0]), 0, 5000)
		result = append(result, pcm160...)
	}

	var o []byte
	sliceHeader := (*reflect.SliceHeader)((unsafe.Pointer(&o)))
	sliceHeader.Cap = len(result) * 2
	sliceHeader.Len = len(result) * 2
	sliceHeader.Data = uintptr(unsafe.Pointer((&result[0])))

	playMedia(o)
}

func AsacTest() {
	//initAsacTest()
	//getSn()
	//mediaPlayerTest()
	//mediaPlayerTest1()
	//asacEncodeTest()
	//asacDecodeTest()
}
*/
