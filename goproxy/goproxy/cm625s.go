package goproxy

import (
	"log"
	"time"

	"goproxy/app_proto"
	"goproxy/bfdx_proto"
	"goproxy/bfkcp"
)

// GotPocDeviceRpcCmd 从服务器接收到的命令，处理后WebSocket发送给客户端
func (m *App) GotPocDeviceRpcCmd(rpcCmd *bfkcp.RpcCmd) {
	log.Println("GotPocDeviceRpcCmd", rpcCmd)
	respRpcCmd := &bfkcp.RpcCmd{
		ParaInt: rpcCmd.ParaInt,
		ParaStr: rpcCmd.ParaStr,
		ParaBin: rpcCmd.ParaBin,
		SeqNo:   rpcCmd.SeqNo,
	}

	// 按子命令处理
	switch rpcCmd.ParaInt {
	case 1:
		// 通讯录列的单位列表
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_query_contact)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		respRpcCmd.Body = rpcCmd.Body
		_, _ = m.Write(respRpcCmd)
		//dbOrgList := &bfdx_proto.DbOrgList{}
		//err := dbOrgList.Unmarshal(rpcCmd.Body)
		//if err != nil {
		//	log.Println("GotPocDeviceRpcCmd Unmarshal dbOrgList Err:", err)
		//	return
		//}
		//for _, item := range dbOrgList.Rows {
		//	m.ContactOrgMap.Store(item.DmrId, item)
		//}

	case 2:
		// 通讯录列的终端列表
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_query_contact)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		respRpcCmd.Body = rpcCmd.Body
		_, _ = m.Write(respRpcCmd)
		//dbDeviceList := &bfdx_proto.DbDeviceList{}
		//err := dbDeviceList.Unmarshal(rpcCmd.Body)
		//if err != nil {
		//	log.Println("GotPocDeviceRpcCmd Unmarshal dbDeviceList Err:", err)
		//	return
		//}
		//for _, item := range dbDeviceList.Rows {
		//	m.ContactDeviceMap.Store(item.DmrId, item)
		//}

	case 3:
		// 默认发射组和收听组
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_query_poc_default_group)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		respRpcCmd.Body = rpcCmd.Body
		_, _ = m.Write(respRpcCmd)
		//pocDefaultGroup := &bfkcp.PocDefaultGroup{}
		//err := pocDefaultGroup.Unmarshal(rpcCmd.Body)
		//if err != nil {
		//	log.Println("GotPocDeviceRpcCmd Unmarshal pocDefaultGroup Err:", err)
		//	return
		//}
		//m.PocDefaultGroup = pocDefaultGroup

	case 4:
		// poc更新收听组
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_update_poc_listen_group)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		_, _ = m.Write(respRpcCmd)

	case 5:
		// poc查询当前收听组
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_query_poc_listen_group)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		respRpcCmd.Body = rpcCmd.Body
		_, _ = m.Write(respRpcCmd)
	//pocDefaultGroup := &bfkcp.PocDefaultGroup{}
	//err := pocDefaultGroup.Unmarshal(rpcCmd.Body)
	//if err != nil {
	//	log.Println("GotPocDeviceRpcCmd Unmarshal current pocDefaultGroup Err:", err)
	//	return
	//}
	//m.PocCurrentGroup = pocDefaultGroup

	case 6:
		// 请示权限外的通讯录数据响应
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_query_outside_permission_contact)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		respRpcCmd.Body = rpcCmd.Body
		respRpcCmd.ParaStr = rpcCmd.ParaStr
		_, _ = m.Write(respRpcCmd)

	case 10:
		// 查询PocConfig结果
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_query_poc_config)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		respRpcCmd.Body = rpcCmd.Body
		_, _ = m.Write(respRpcCmd)

		// 将PocConfig解析出来，以便后续处理
		m.SyncPocConfig(rpcCmd)

	case 111:
		// bf8100系统修改了通讯录，服务器广播
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_notify_poc_setting_changed)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		respRpcCmd.ParaStr = rpcCmd.ParaStr
		_, _ = m.Write(respRpcCmd)
	case 11:
		// 收到系统返回的查询通讯录在线列表
		respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_query_online_contact)
		respRpcCmd.Res = int32(app_proto.ResCode_success)
		respRpcCmd.Body = rpcCmd.Body
		_, _ = m.Write(respRpcCmd)
	case 14:
		//// 收到服务器回应的退出登录成功
		//if m.logOutTimer != nil {
		//	m.logOutTimer.Stop() // 停止定时器
		//	m.logOutTimer = nil
		//}
		//
		//// 通知定时器收到响应
		//if m.logOutChan != nil {
		//	select {
		//	case <-m.logOutChan:
		//	default:
		//		close(m.logOutChan)
		//		m.logOutChan = nil
		//	}
		//}
		//m.loginQuit()
		//respRpcCmd.Cmd = int32(app_proto.CmdCode_cmd_resp_login_quit)
		//respRpcCmd.Res = int32(app_proto.ResCode_success)
		//_, _ = m.Write(respRpcCmd)

	default:
		log.Println("GotPocDeviceRpcCmd unknown cmd:", rpcCmd.Cmd)
	}
}

// QueryPocContacts 向服务器请求通讯录列表
func (m *App) QueryPocContacts(cmd *bfkcp.RpcCmd) {
	_ = cmd
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:     11,
		ParaInt: 1,
	}

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("QueryPocContacts send Err", err)
		respRpcCmd := &bfkcp.RpcCmd{
			Cmd:     int32(app_proto.CmdCode_cmd_resp_query_contact),
			Res:     int32(app_proto.ResCode_fialed),
			ParaStr: kcpSendErrorStr,
		}
		_, _ = m.Write(respRpcCmd)
	}
}

// QueryPocDefaultGroup 查询默认发射组和收听组
func (m *App) QueryPocDefaultGroup(cmd *bfkcp.RpcCmd) {
	_ = cmd
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:     11,
		ParaInt: 3,
	}

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("QueryPocDefaultGroup send Err", err)
		respRpcCmd := &bfkcp.RpcCmd{
			Cmd:     int32(app_proto.CmdCode_cmd_resp_query_poc_default_group),
			Res:     int32(app_proto.ResCode_fialed),
			ParaStr: kcpSendErrorStr,
		}
		_, _ = m.Write(respRpcCmd)
	}
}

// UpdatePocListenGroup poc更新收听组
func (m *App) UpdatePocListenGroup(cmd *bfkcp.RpcCmd) {
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:     11,
		ParaInt: 4,
		Body:    cmd.Body,
		ParaBin: cmd.ParaBin,
	}

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("UpdatePocListenGroup send Err", err)
		respRpcCmd := &bfkcp.RpcCmd{
			Cmd:     int32(app_proto.CmdCode_cmd_resp_update_poc_listen_group),
			Res:     int32(app_proto.ResCode_fialed),
			ParaStr: kcpSendErrorStr,
		}
		_, _ = m.Write(respRpcCmd)
	}
}

// QueryPocListenGroup poc查询当前收听组
func (m *App) QueryPocListenGroup(cmd *bfkcp.RpcCmd) {
	_ = cmd
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:     11,
		ParaInt: 5,
	}

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("QueryPocListenGroup send Err", err)
		respRpcCmd := &bfkcp.RpcCmd{
			Cmd:     int32(app_proto.CmdCode_cmd_resp_query_poc_listen_group),
			Res:     int32(app_proto.ResCode_fialed),
			ParaStr: kcpSendErrorStr,
		}
		_, _ = m.Write(respRpcCmd)
	}
}

// UpdateLastUnknownPosition 更新最后定位数据
//func (m *App) UpdateLastUnknownPosition(cmd *bfkcp.RpcCmd) {
//	position := &app_proto.Gps84{}
//	err := position.Unmarshal(cmd.Body)
//	if err != nil {
//		log.Println("UpdateLastUnknownPosition Unmarshal Err", err)
//		return
//	}
//
//	m.LastKnownPosition = position
//}

// QueryOutsidePermissionContact 更新最后定位数据
func (m *App) QueryOutsidePermissionContact(cmd *bfkcp.RpcCmd) {
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:     11,
		ParaInt: 6,
		ParaStr: cmd.ParaStr,
	}

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("QueryOutsidePermissionContact send Err", err)
		respRpcCmd := &bfkcp.RpcCmd{
			Cmd:     int32(app_proto.CmdCode_cmd_resp_query_outside_permission_contact),
			Res:     int32(app_proto.ResCode_fialed),
			ParaStr: kcpSendErrorStr,
		}
		_, _ = m.Write(respRpcCmd)
	}
}

// SendAlarm poc终端报警
func (m *App) SendAlarm(cmd *bfkcp.RpcCmd) {
	err := m.SendBc18()
	respRpcCmd := &bfkcp.RpcCmd{
		Cmd: int32(app_proto.CmdCode_cmd_resp_send_alarm),
	}
	if err != nil {
		log.Println("SendAlarm send Err", err)
		respRpcCmd.Res = int32(app_proto.ResCode_fialed)
		respRpcCmd.ParaStr = err.Error()
		_, _ = m.Write(respRpcCmd)
		return
	}

	// 应答UI客户端
	respRpcCmd.Res = int32(app_proto.ResCode_success)
	_, _ = m.Write(respRpcCmd)

	// 初始化CB10信号
	if m.Cb10Signal != nil {
		close(m.Cb10Signal)
	}
	m.Cb10Signal = make(chan byte, 1)

	// 没有报警监控参数，则不启动监控功能
	if m.CB07 == nil {
		return
	}

	// 报警呼叫监控，按指定的监听时间呼叫，时间为0则不启动
	if m.CB07.JtTime > 0 {
		go func() {
			m.IsAlarmSpeaking.Store(true)
			// 先更新呼叫超时参数，避免默认超时时间到了，自动结束呼叫
			speakTimeout := int32(GlobalMediaManager.speakTimeoutDuration.Seconds())
			GlobalMediaManager.SetSpeakTimeout(m.CB07.JtTime)
			defer func() {
				log.Printf("m.CB07.JtTime defer, JtTime: %d, default speakTimeout: %d, ", m.CB07.JtTime, speakTimeout)
				GlobalMediaManager.SetSpeakTimeout(speakTimeout)
				m.IsAlarmSpeaking.Store(false)
			}()
			m.ProcessSpeakStart(cmd, false)

			// 等待可能的解除报警信号
			timer := time.NewTimer(time.Duration(m.CB07.JtTime) * time.Second)
			for {
				select {
				case <-m.Cb10Signal:
					m.ProcessSpeakStop(cmd)
					timer.Stop()
					return
				case <-timer.C:
					// 呼叫结束了，退出等待解除报警信号
					m.ProcessSpeakStop(cmd)
					return
				}
			}
		}()
	}

	// 报警定位监控，时间为0则不启动
	if m.CB07.DwTime > 0 {
		// 卫星定位未开启
		if m.Cb42Code != 11 {
			m.Dic++
			_ = m.SendBc42(m.Dic, m.Cb42Code)
			return
		}

		go func() {
			if m.Bc18Locator != nil {
				m.Bc18Locator.stop()
			}
			m.Bc18Locator = &Bc18Locator{
				DwTime: m.CB07.DwTime,
			}
			m.Bc18Locator.start()
		}()
	}
}

// ResumeDevStatus 恢复设备状态
func (m *App) ResumeDevStatus() {
	d := DevStatusRow{
		DmrId: m.DevDmridStr,
	}
	err := d.Query()
	if err != nil {
		log.Println("RunTaskWhenLogin resume dev status error", err)
		return
	}

	m.DevStatus = &d.Status
	log.Println("current devStatus", m.DevStatus)
	m.NotifyDeviceStatus()
}

// RunCbxxTaskWhenLogin 登录后，执行系统下发的命令，如cb02
func (m *App) RunCbxxTaskWhenLogin() {
	c := &CbxxArgsRow{
		DmrId: m.DevDmridStr,
	}
	rows, err := c.Query()
	if err != nil {
		log.Println("RunCbxxTaskWhenLogin query cbxx args from db error", err)
		return
	}
	log.Println("RunCbxxTaskWhenLogin rows", rows)
	m.CbxxArgsRows = rows

	for _, r := range m.CbxxArgsRows {
		switch r.Cbxx {
		case 0xcb02:
			go m.RunCb02Task(r, nil)

		case 0xcb04:
			go m.RunCb04Task(r, nil)

		case 0xcb05:
			go m.RunCb05Task(r, nil)

		case 0xcb06:
			go m.RunCb06Task(r, nil)

		case 0xcb07:
			_ = m.CB07.Unmarshal(r.Args)

		case 0xcb42:
			cb42Args := &bfdx_proto.Cb42{}
			_ = cb42Args.Unmarshal(r.Args)
			m.Cb42Code = cb42Args.Code
		}
	}

	// 登录后，必须调用，以同步系统设置的卫星定位开关状态
	m.callUniproGpsSetMethod(m.Cb42Code)
}

// RunBCxxTaskWhenLogin 登录后，执行没有向服务器发送的命令，如bb01
func (m *App) RunBCxxTaskWhenLogin() {
	// 必须检测是否有未发送的指令，如bb01
	r := &WaitingSendCommandsRow{
		DmrId: m.DevDmridStr,
		Bcxx:  0xbb01,
	}
	limit := 16
	responseChan := make(chan struct{}, 1)
	defer close(responseChan)

	for {
		// 每次查询16条数据，然后发送到服务器上，完成后继续下一轮循环
		rows, err := r.Query(limit)
		if err != nil {
			log.Println("RunBCxxTaskWhenLogin query error", err)
			return
		}
		// 已经全部发送
		if len(rows) == 0 {
			log.Println("bb01 rows is empty")
			return
		}

		pendingIds := make(map[int64]bool)
		// 本次循环发送失败次数，超过一半发不出去，则结束发送
		sendErrorCount := 0
		for _, row := range rows {
			m.Dic++
			resDic := m.Dic
			m.lastSendBcxxTime = time.Now()
			err := m.SendBcXXWithDicAndStatus(resDic, uint16(0xbb01), 0, m.DevDmrid, row.DevStatus, row.Data)
			if err != nil {
				sendErrorCount++
				// 超过一半发不出去，则结束发送
				if sendErrorCount >= limit/2 {
					break
				}
			}

			m.Cb00ResBB01Dic.Store(resDic, row.Id)
			pendingIds[row.Id] = true
		}

		// 启动一个 goroutine 监控响应
		go func() {
			checkTicker := time.NewTicker(200 * time.Millisecond)
			defer checkTicker.Stop()
			timeout := time.After(time.Duration(limit) * time.Second)

			for {
				select {
				case <-checkTicker.C:
					// 默认已经应答，检查是否还有应答没有收到
					hasResponse := false
					m.Cb00ResBB01Dic.Range(func(key, value interface{}) bool {
						hasResponse = true
						return false // 只要发现有一个就停止遍历
					})
					if !hasResponse {
						// 删除所有已经收到响应的ID
						deleteIds := make([]int64, 0, len(pendingIds))
						for id := range pendingIds {
							deleteIds = append(deleteIds, id)
						}
						err = r.DeleteWithIds(deleteIds)
						if err != nil {
							log.Println("DeleteWithIds err:", err)
						}
						responseChan <- struct{}{}
						return
					}
				case <-timeout:
					responseMap := make(map[byte]int64)
					m.Cb00ResBB01Dic.Range(func(key, value interface{}) bool {
						responseMap[key.(byte)] = value.(int64)
						return true
					})

					log.Println("RunBCxxTaskWhenLogin checkTicker timeout", len(responseMap), responseMap)
					// 超时处理，只删除已经收到响应的ID
					deleteIds := make([]int64, 0)
					for id := range pendingIds {
						isWaiting := false
						for _, mId := range responseMap {
							if id == mId {
								isWaiting = true
								break
							}
						}
						if !isWaiting {
							deleteIds = append(deleteIds, id)
						}
					}
					err = r.DeleteWithIds(deleteIds)
					if err != nil {
						log.Println("DeleteWithIds err:", err)
					}
					m.Cb00ResBB01Dic.Range(func(key, value interface{}) bool {
						m.Cb00ResBB01Dic.Delete(key)
						return true
					})
					responseChan <- struct{}{}
					return
				}
			}
		}()

		<-responseChan
		log.Println("send bb01 next for loop")
	}
}

func (m *App) InsertOrReplaceCbxxArgsRows(row *CbxxArgsRow) {
	index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
	if index == -1 {
		m.CbxxArgsRows = append(m.CbxxArgsRows, row)
	} else {
		m.CbxxArgsRows[index] = row
	}

	// 写入数据库
	err := row.InsertOrReplace()
	if err != nil {
		log.Println("CbxxARgsRow InsertOrReplace error:", err)
	}
}

func (m *App) GetCbxxArgsBytesWithQueryCmd(row *CbxxArgsRow) []byte {
	var argsBytes []byte
	index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
	if index == -1 {
		_ = row.QueryWithCbxx()
		argsBytes = row.Args
	} else {
		item := m.CbxxArgsRows[index]
		argsBytes = item.Args
	}

	return argsBytes
}

func (m *App) RemoveCbxxArgsRow(row *CbxxArgsRow) {
	err := row.Delete()
	if err != nil {
		log.Println("CbxxARgsRow delete from db error:", err)
	}
	m.CbxxArgsRows = RemoveCbxxArgsRow(m.CbxxArgsRows, row)
}

func (m *App) RunCb02Task(row *CbxxArgsRow, cb02Args *bfdx_proto.Cb02) {
	// 先停止上一个监控
	if m.Cb02Locator != nil {
		m.Cb02Locator.stop()
	}

	if cb02Args == nil {
		cb02Args = &bfdx_proto.Cb02{}
		_ = cb02Args.Unmarshal(row.Args)
	}

	// 开始监控
	m.Cb02Locator = &Cb02Locator{
		Cb02:           cb02Args,
		isPausedSignal: make(chan byte),
	}
	m.Cb02Locator.start()

	index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
	if index == -1 {
		m.CbxxArgsRows = append(m.CbxxArgsRows, row)
	} else {
		m.CbxxArgsRows[index] = row
	}

	// 写入数据库
	argsByte, _ := cb02Args.Marshal()
	row.Args = argsByte
	err := row.InsertOrReplace()
	if err != nil {
		log.Println("RunCb02Task InsertOrReplace error:", err)
	}
}

func (m *App) RunCb04Task(row *CbxxArgsRow, args *bfdx_proto.Cb04) {
	// 先停止上一个监控
	if m.Cb04Locator != nil {
		m.Cb04Locator.stop()
	}

	if args == nil {
		args = &bfdx_proto.Cb04{}
		_ = args.Unmarshal(row.Args)
	}

	// 开始监控
	m.Cb04Locator = &Cb04Locator{
		Cb04: args,
	}
	m.Cb04Locator.start()

	index := FindCbxxArgsRowIndex(m.CbxxArgsRows, row)
	if index == -1 {
		m.CbxxArgsRows = append(m.CbxxArgsRows, row)
	} else {
		m.CbxxArgsRows[index] = row
	}

	// 写入数据库
	argsByte, _ := args.Marshal()
	row.Args = argsByte
	err := row.InsertOrReplace()
	if err != nil {
		log.Println("RunCb04InBoundsMonitoringTask InsertOrReplace error:", err)
	}
}

func (m *App) RunCb05Task(row *CbxxArgsRow, args *bfdx_proto.Cb05) {
	// 先停止上一个监控
	if m.Cb05Locator != nil {
		m.Cb05Locator.stop()
	}

	if args == nil {
		args = &bfdx_proto.Cb05{}
		_ = args.Unmarshal(row.Args)
	}

	// 开始监控
	m.Cb05Locator = &Cb05Locator{
		Cb05:   args,
		Radius: CalcRadiusFromLatDiffString(args.LatDif),
	}
	m.Cb05Locator.start()

	// 写入数据库
	argsByte, _ := args.Marshal()
	row.Args = argsByte
	m.InsertOrReplaceCbxxArgsRows(row)
}

func (m *App) RunCb06Task(row *CbxxArgsRow, args *bfdx_proto.Cb06) {
	// 先停止上一个监控
	if m.Cb06Locator != nil {
		m.Cb06Locator.stop()
	}

	if args == nil {
		args = &bfdx_proto.Cb06{}
		_ = args.Unmarshal(row.Args)
	}

	// 开始监控
	m.Cb06Locator = &Cb06Locator{
		Cb06:   args,
		Radius: CalcRadiusFromLatDiffString(args.LatDif),
	}
	m.Cb06Locator.start()

	// 写入数据库
	argsByte, _ := args.Marshal()
	row.Args = argsByte
	m.InsertOrReplaceCbxxArgsRows(row)
}

func (m *App) QueryPocConfig(cmd *bfkcp.RpcCmd) {
	_ = cmd
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:     11,
		ParaInt: 10,
	}

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("QueryPocConfig send Err", err)
		respRpcCmd := &bfkcp.RpcCmd{
			Cmd:     int32(app_proto.CmdCode_cmd_resp_query_poc_config),
			Res:     int32(app_proto.ResCode_fialed),
			ParaStr: kcpSendErrorStr,
		}
		_, _ = m.Write(respRpcCmd)
	}
}

func (m *App) QueryOnlineContact(cmd *bfkcp.RpcCmd) {
	_ = cmd
	rpc_cmd := &bfkcp.RpcCmd{
		Cmd:     11,
		ParaInt: 11,
		Body:    cmd.Body,
	}

	err := kcpInstance.send(rpc_cmd)
	if err != nil {
		log.Println("QueryOnlineContact send Err", err)
		respRpcCmd := &bfkcp.RpcCmd{
			Cmd:     int32(app_proto.CmdCode_cmd_resp_query_online_contact),
			Res:     int32(app_proto.ResCode_fialed),
			ParaStr: kcpSendErrorStr,
		}
		_, _ = m.Write(respRpcCmd)
	}
}

// Respond2AppConfig write app config response to appConfigResponseChMap
func (m *App) Respond2AppConfig(cmd *bfkcp.RpcCmd) {
	id := cmd.SeqNo
	ch, ok := appConfigResponseChMap[id]
	if !ok {
		return
	}
	ch <- cmd
}

func (m *App) SyncPocConfig(cmd *bfkcp.RpcCmd) {
	pocConfig := &bfkcp.PocConfig{}
	err := pocConfig.Unmarshal(cmd.Body)
	if err != nil {
		log.Println("SyncPocConfig Unmarshal err:", err)
		return
	}
	log.Println("SyncPocConfig pocConfig:", pocConfig)
	m.PocConfig = pocConfig
}

// 在终端初始化配置后执行
func (m *App) NotifyInitDataFinish(cmd *bfkcp.RpcCmd) {
	_ = cmd
	m.InitDataFinish = true
	log.Println("NotifyInitDataFinish", m.InitDataFinish)

	// 恢复状态，要同步执行，后续指令需要实际状态参数
	GlobalApp.ResumeDevStatus()

	go func() {
		r := &LastKnownLocationRow{
			DmrId: m.DevDmridStr,
		}
		err := r.Query()
		if err != nil {
			log.Println("RunTaskWhenLogin select last known location error", err)
			return
		}

		location := &LocationData{}
		l := &app_proto.Gps84{}
		_ = l.Unmarshal(r.Location)
		m.LastKnownLocation = l
		if m.LastKnownLocation != nil {
			location.fromAppGps84(m.LastKnownLocation)
			m.sendBc01(location, IsValidLocationTime(m.LastKnownLocation.GpsTime))
		}
	}()

	go GlobalApp.RunCbxxTaskWhenLogin()
	go GlobalApp.RunBCxxTaskWhenLogin()

	go func() {
		for {
			// 计算当前时间与 m.lastSendBcxxTime 的时间差
			elapsed := time.Since(m.lastSendBcxxTime)

			// 判断是否超过25分钟
			if elapsed > 25*time.Minute {
				// 如果超过25分钟，发送 bc01
				newLocation := &LocationData{
					GpsTime: time.Now().UTC(),
				}
				isOkTime := false
				if GlobalApp.LastKnownLocation != nil {
					isOkTime = IsValidLocationTime(m.LastKnownLocation.GpsTime)
				} else {
					isOkTime = false
				}
				m.sendBc01(newLocation, isOkTime)
			} else {
				// 如果没有超过25分钟，等待25 - elapsed分钟
				time.Sleep((25 * time.Minute) - elapsed)
				// 如果已经退出登录，则结束
				if !m.IsLogin.Load() {
					return
				}
			}
		}
	}()
}

// 在终端开机自动登录失败后执行，主要用于开启可靠定位功能后终端开机能继续执行定位任务
func (m *App) NotifyAutoLoginTimeout(cmd *bfkcp.RpcCmd) {
	m.SyncPocConfig(cmd)
	log.Printf("NotifyAutoLoginTimeout, DmrId=%s, PocConfig=%v", cmd.ParaStr, m.PocConfig)

	// 未开启可靠定位模式，不执行任务
	if m.PocConfig == nil || m.PocConfig.Rgps != 1 {
		return
	}

	row := &CbxxArgsRow{
		DmrId: cmd.ParaStr,
		Cbxx:  0xcb02,
	}
	err := row.QueryWithCbxx()
	if err != nil {
		log.Println("NotifyAutoLoginTimeout QueryWithCbxx error:", err)
		return
	}
	if row.Args == nil {
		return
	}

	// 开启可靠定位模式下的CB02定位监控任务
	go m.RunCb02Task(row, nil)
}
