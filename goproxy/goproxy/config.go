package goproxy

import "go.uber.org/atomic"

// app 已经退出
var AppIsExited *atomic.Bool = atomic.NewBool(false)

// 代理 http 端口
var ProxyHttpPort uint16 = 12255

// //服务器 kcp 地址
var ServerKcpHost = "t2.bfdx.net"

//const ServerKcpHost = "t2.bfdx.net"

// //服务器 kcp 端口
var ServerKcpPort uint16 = 2235

//const ServerKcpPort uint16 = 2235

// 60ms 语音包byte大小
const pcm960Size = 960

// 语音放大倍数
var Gain = 1000

// 缓冲只有n个包，就收到了cb15 则不需要播放
const MinMediaPlaySize = 5

// 接受语音缓冲包个数 *60ms
var MediaAutoStartBufferSize = int32(8)

//var MediaAutoStartBufferSize = int32(50)

// 语音缓冲包总个数  *60ms
var MediaBufferSize = int32(250)

// 讲话超时 秒
const speakTimeout = int32(60)

// 收听组更新每一包的大小
//const listenGroupUpdateChunkSize = 20

const NDKMedia = 1
const SDKMedia = 2

const DefaultMediaSoftware = SDKMedia
