package goproxy

import (
	"time"
)

/**
 * 详细请看BF_DMR_GPS_RFID 管理系统通讯协议.doc 无线终端状态定义 表A.4
 * 电池电量位合并到信道状态位，以支持上千个信道
 * 位序	  当前工作状态(1B)		当前调度状态(2B)			设置状态(3B)			报警状态(4B)		频道值班级别(5B)		电池电压(6B)
 * 0	1	终端类型为车载台		1	已紧急全呼调度	1	开启紧急报警		1	发生紧急报警		信道状态位0			信道状态位0
 * 1	1	锁机状态BIT0位(禁听)	1	已基站群呼调度	1	开启移动监控		1	移动监控报警		信道状态位1			信道状态位1
 * 2	1	锁机状态BIT1位(禁发)	1	已信道群呼调度	1	开启岗哨监控		1	岗哨监控报警		信道状态位2			信道状态位2
 * 3	1	已GPS自动监控定位		1	已动态组呼调度	1	开启出界监控		1	出界监控报警		信道状态位3			信道状态位3
 * 4	1	已中心监控定位		1	已级别组呼调度	1	开启入界监控		1	入界监控报警		信道状态位4			信道状态位4
 * 5	1	已紧急报警自动定位	1	已固定组呼调度	1	开启调度功能		1	强行脱网报警		兼任值班机			信道状态位5
 * 6	1	已漫游				1	已漫游联网调度	1	开启监听功能		1	发生欠压报警		级别BIT0位			信道状态位6
 * 7	1	已脱网				1	已工作区越界		1	开启外设功能		1	GPS故障报警		级别BIT1位			指挥机标志
 */

// DevStatus 6个字节状态
type DevStatus []byte

// CreateDefaultDevStatus 创建默认的状态参数
func CreateDefaultDevStatus() *DevStatus {
	status := &DevStatus{0, 0, 0, 0, 0, 0}
	status.SetEnableEmergencyAlarm(1)
	status.SetChannelNo(1)
	status.SetDevPriority(1)
	return status
}

// SetByteWithPos 修改参数指定位数据
func (s DevStatus) SetByteWithPos(byteIndex, bitIndex, val byte) {
	target := s[byteIndex]
	// 设置指定位为 1
	if val == 1 {
		mask := 1 << bitIndex
		result := target | byte(mask)
		s[byteIndex] = result
		return
	}

	// 清除指定位
	mask := ^(1 << bitIndex)
	result := target & byte(mask)
	s[byteIndex] = result
}

// SetByteWithRange 将给定的整数按指定位范围设置到指定的参数上
func (s DevStatus) SetByteWithRange(byteIndex, startIndex, endIndex, val byte) {
	target := s[byteIndex]
	// 确定哪些位需要被修改
	mask := 0
	for i := startIndex; i <= endIndex; i++ {
		mask |= 1 << i
	}

	// 将目标位上的原有值清除
	target &= ^byte(mask)

	// 将新值移到正确的位置，并与目标字节合并
	result := target | (val << startIndex)

	s[byteIndex] = result
}

// GetByteWithRange 读取指定位范围数据
func (s DevStatus) GetByteWithRange(byteIndex, startIndex, endIndex byte) byte {
	target := s[byteIndex]
	// 构造掩码
	mask := 0
	for i := startIndex; i <= endIndex; i++ {
		mask |= 1 << i
	}
	// 与操作提取
	result := target & byte(mask)
	// 右移调整
	return result >> startIndex
}

// GetByteWithPos 读取指定位数据
func (s DevStatus) GetByteWithPos(byteIndex, bitIndex byte) byte {
	target := s[byteIndex]
	// 构造掩码
	mask := 1 << bitIndex
	// 与操作提取
	result := target & byte(mask)
	// 右移调整
	return result >> bitIndex
}

// SetDevType 终端类型为车载台
func (s DevStatus) SetDevType(value byte) {
	s.SetByteWithPos(0, 0, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetDevType 终端类型为车载台
func (s DevStatus) GetDevType() byte {
	return s.GetByteWithPos(0, 0)
}

// SetForbiddenListen 锁机状态BIT0位(禁听)
func (s DevStatus) SetForbiddenListen(value byte) {
	s.SetByteWithPos(0, 1, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetForbiddenListen 锁机状态BIT0位(禁听)
func (s DevStatus) GetForbiddenListen() byte {
	return s.GetByteWithPos(0, 1)
}

// SetForbiddenCall 锁机状态BIT1位(禁发)
func (s DevStatus) SetForbiddenCall(value byte) {
	s.SetByteWithPos(0, 2, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetForbiddenCall 锁机状态BIT1位(禁发)
func (s DevStatus) GetForbiddenCall() byte {
	return s.GetByteWithPos(0, 2)
}

// SetAutoPositionMonitoring 已GPS自动监控定位
func (s DevStatus) SetAutoPositionMonitoring(value byte) {
	s.SetByteWithPos(0, 3, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetAutoPositionMonitoring 已GPS自动监控定位
func (s DevStatus) GetAutoPositionMonitoring() byte {
	return s.GetByteWithPos(0, 3)
}

// SetCentralPositionMonitoring 已中心监控定位
func (s DevStatus) SetCentralPositionMonitoring(value byte) {
	s.SetByteWithPos(0, 4, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetCentralPositionMonitoring 已中心监控定位
func (s DevStatus) GetCentralPositionMonitoring() byte {
	return s.GetByteWithPos(0, 4)
}

// SetEmergencyAlarmAutoPositioning 已紧急报警自动定位
func (s DevStatus) SetEmergencyAlarmAutoPositioning(value byte) {
	s.SetByteWithPos(0, 5, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEmergencyAlarmAutoPositioning 已紧急报警自动定位
func (s DevStatus) GetEmergencyAlarmAutoPositioning() byte {
	return s.GetByteWithPos(0, 5)
}

// SetRoaming 已漫游
func (s DevStatus) SetRoaming(value byte) {
	s.SetByteWithPos(0, 6, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetRoaming 已漫游
func (s DevStatus) GetRoaming() byte {
	return s.GetByteWithPos(0, 6)
}

// SetOffline 已脱网
func (s DevStatus) SetOffline(value byte) {
	s.SetByteWithPos(0, 7, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetOffline 已脱网
func (s DevStatus) GetOffline() byte {
	return s.GetByteWithPos(0, 7)
}

// SetEmergencyAllCallDispatch 已紧急全呼调度
func (s DevStatus) SetEmergencyAllCallDispatch(value byte) {
	s.SetByteWithPos(1, 0, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEmergencyAllCallDispatch 已紧急全呼调度
func (s DevStatus) GetEmergencyAllCallDispatch() byte {
	return s.GetByteWithPos(1, 0)
}

// SetBaseStationGroupCallDispatch 已基站群呼调度
func (s DevStatus) SetBaseStationGroupCallDispatch(value byte) {
	s.SetByteWithPos(1, 1, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetBaseStationGroupCallDispatch 已基站群呼调度
func (s DevStatus) GetBaseStationGroupCallDispatch() byte {
	return s.GetByteWithPos(1, 1)
}

// SetChannelGroupCallDispatch 已信道群呼调度
func (s DevStatus) SetChannelGroupCallDispatch(value byte) {
	s.SetByteWithPos(1, 2, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetChannelGroupCallDispatch 已信道群呼调度
func (s DevStatus) GetChannelGroupCallDispatch() byte {
	return s.GetByteWithPos(1, 2)
}

// SetDynamicGroupCallDispatch 已动态组呼调度
func (s DevStatus) SetDynamicGroupCallDispatch(value byte) {
	s.SetByteWithPos(1, 3, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetDynamicGroupCallDispatch 已动态组呼调度
func (s DevStatus) GetDynamicGroupCallDispatch() byte {
	return s.GetByteWithPos(1, 3)
}

// SetLevelGroupCallDispatch 已级别组呼调度
func (s DevStatus) SetLevelGroupCallDispatch(value byte) {
	s.SetByteWithPos(1, 4, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetLevelGroupCallDispatch 已级别组呼调度
func (s DevStatus) GetLevelGroupCallDispatch() byte {
	return s.GetByteWithPos(1, 4)
}

// SetFixedGroupCallDispatch 已固定组呼调度
func (s DevStatus) SetFixedGroupCallDispatch(value byte) {
	s.SetByteWithPos(1, 5, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetFixedGroupCallDispatch 已固定组呼调度
func (s DevStatus) GetFixedGroupCallDispatch() byte {
	return s.GetByteWithPos(1, 5)
}

// SetRoamingNetworkDispatch 已漫游联网调度
func (s DevStatus) SetRoamingNetworkDispatch(value byte) {
	s.SetByteWithPos(1, 6, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetRoamingNetworkDispatch 已漫游联网调度
func (s DevStatus) GetRoamingNetworkDispatch() byte {
	return s.GetByteWithPos(1, 6)
}

// SetWorkAreaHasExceededLimit 已工作区越界
func (s DevStatus) SetWorkAreaHasExceededLimit(value byte) {
	s.SetByteWithPos(1, 7, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetWorkAreaHasExceededLimit 已工作区越界
func (s DevStatus) GetWorkAreaHasExceededLimit() byte {
	return s.GetByteWithPos(1, 7)
}

// SetEnableEmergencyAlarm 开启紧急报警
func (s DevStatus) SetEnableEmergencyAlarm(value byte) {
	s.SetByteWithPos(2, 0, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEnableEmergencyAlarm 开启紧急报警
func (s DevStatus) GetEnableEmergencyAlarm() byte {
	return s.GetByteWithPos(2, 0)
}

// SetEnableMobileMonitoring 开启移动监控
func (s DevStatus) SetEnableMobileMonitoring(value byte) {
	s.SetByteWithPos(2, 1, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEnableMobileMonitoring 开启移动监控
func (s DevStatus) GetEnableMobileMonitoring() byte {
	return s.GetByteWithPos(2, 1)
}

// SetEnableSentryMonitoring 开启岗哨监控
func (s DevStatus) SetEnableSentryMonitoring(value byte) {
	s.SetByteWithPos(2, 2, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEnableSentryMonitoring 开启岗哨监控
func (s DevStatus) GetEnableSentryMonitoring() byte {
	return s.GetByteWithPos(2, 2)
}

// SetEnableOutOfBoundsMonitoring 开启出界监控
func (s DevStatus) SetEnableOutOfBoundsMonitoring(value byte) {
	s.SetByteWithPos(2, 3, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEnableOutOfBoundsMonitoring 开启出界监控
func (s DevStatus) GetEnableOutOfBoundsMonitoring() byte {
	return s.GetByteWithPos(2, 3)
}

// SetEnableInBoundsMonitoring 开启入界监控
func (s DevStatus) SetEnableInBoundsMonitoring(value byte) {
	s.SetByteWithPos(2, 4, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEnableInBoundsMonitoring 开启入界监控
func (s DevStatus) GetEnableInBoundsMonitoring() byte {
	return s.GetByteWithPos(2, 4)
}

// SetEnableSchedulingFunc 开启调度功能
func (s DevStatus) SetEnableSchedulingFunc(value byte) {
	s.SetByteWithPos(2, 5, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEnableSchedulingFunc 开启调度功能
func (s DevStatus) GetEnableSchedulingFunc() byte {
	return s.GetByteWithPos(2, 5)
}

// SetEnableMonitoringFunc 开启监听功能
func (s DevStatus) SetEnableMonitoringFunc(value byte) {
	s.SetByteWithPos(2, 6, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEnableMonitoringFunc 开启监听功能
func (s DevStatus) GetEnableMonitoringFunc() byte {
	return s.GetByteWithPos(2, 6)
}

// SetEnablePeripheralFunc 开启外设功能
func (s DevStatus) SetEnablePeripheralFunc(value byte) {
	s.SetByteWithPos(2, 7, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEnablePeripheralFunc 开启外设功能
func (s DevStatus) GetEnablePeripheralFunc() byte {
	return s.GetByteWithPos(2, 7)
}

// SetEmergencyAlarm 发生紧急报警
func (s DevStatus) SetEmergencyAlarm(value byte) {
	s.SetByteWithPos(3, 0, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetEmergencyAlarm 发生紧急报警
func (s DevStatus) GetEmergencyAlarm() byte {
	return s.GetByteWithPos(3, 0)
}

// SetMobileMonitoringAlarm 移动监控报警
func (s DevStatus) SetMobileMonitoringAlarm(value byte) {
	s.SetByteWithPos(3, 1, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetMobileMonitoringAlarm 移动监控报警
func (s DevStatus) GetMobileMonitoringAlarm() byte {
	return s.GetByteWithPos(3, 1)
}

// SetSentryMonitoringAlarm 岗哨监控报警
func (s DevStatus) SetSentryMonitoringAlarm(value byte) {
	s.SetByteWithPos(3, 2, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetSentryMonitoringAlarm 岗哨监控报警
func (s DevStatus) GetSentryMonitoringAlarm() byte {
	return s.GetByteWithPos(3, 2)
}

// SetOutOfBoundsMonitoringAlarm 出界监控报警
func (s DevStatus) SetOutOfBoundsMonitoringAlarm(value byte) {
	s.SetByteWithPos(3, 3, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetOutOfBoundsMonitoringAlarm 出界监控报警
func (s DevStatus) GetOutOfBoundsMonitoringAlarm() byte {
	return s.GetByteWithPos(3, 3)
}

// SetInBoundsMonitoringAlarm 入界监控报警
func (s DevStatus) SetInBoundsMonitoringAlarm(value byte) {
	s.SetByteWithPos(3, 4, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetInBoundsMonitoringAlarm 入界监控报警
func (s DevStatus) GetInBoundsMonitoringAlarm() byte {
	return s.GetByteWithPos(3, 4)
}

// SetForcedOfflineAlarm 强行脱网报警
func (s DevStatus) SetForcedOfflineAlarm(value byte) {
	s.SetByteWithPos(3, 5, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetForcedOfflineAlarm 强行脱网报警
func (s DevStatus) GetForcedOfflineAlarm() byte {
	return s.GetByteWithPos(3, 5)
}

// SetUnderVoltageAlarm 发生欠压报警
func (s DevStatus) SetUnderVoltageAlarm(value byte) {
	s.SetByteWithPos(3, 6, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetUnderVoltageAlarm 发生欠压报警
func (s DevStatus) GetUnderVoltageAlarm() byte {
	return s.GetByteWithPos(3, 6)
}

// SetGpsFailureAlarm GPS故障报警
func (s DevStatus) SetGpsFailureAlarm(value byte) {
	s.SetByteWithPos(3, 7, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetGpsFailureAlarm GPS故障报警
func (s DevStatus) GetGpsFailureAlarm() byte {
	return s.GetByteWithPos(3, 7)
}

// SetChannelNo 信道状态位，即信道号
func (s DevStatus) SetChannelNo(value int32) {
	// 信道共12位
	value = value & 0x0FFF
	// 低字节只占5位
	lowByte := byte(value & 0x1F)
	// 高字节占7位
	highByte := byte(value >> 5)
	s.SetByteWithRange(4, 0, 4, lowByte)
	s.SetByteWithRange(5, 0, 6, highByte)
	s.SaveToDb()
	s.NotifyClient()
}

// GetChannelNo 信道状态位，即信道号
func (s DevStatus) GetChannelNo() int32 {
	lowByte := int32(s.GetByteWithRange(4, 0, 4))
	highByte := int32(s.GetByteWithRange(5, 0, 6))
	return (highByte << 5) + lowByte
}

// SetDutyAircraft 兼任值班机
func (s DevStatus) SetDutyAircraft(value byte) {
	s.SetByteWithPos(4, 5, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetDutyAircraft 兼任值班机
func (s DevStatus) GetDutyAircraft() byte {
	return s.GetByteWithPos(4, 5)
}

// SetDevPriority 级别BIT0位，级别BIT1位，即终端优先级
func (s DevStatus) SetDevPriority(value byte) {
	s.SetByteWithRange(4, 6, 7, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetDevPriority 级别BIT0位，级别BIT1位，即终端优先级
func (s DevStatus) GetDevPriority() byte {
	value := s.GetByteWithRange(4, 6, 7)
	return value
}

// SetCommandAircraft 指挥机标志
func (s DevStatus) SetCommandAircraft(value byte) {
	s.SetByteWithPos(5, 7, value)
	s.SaveToDb()
	s.NotifyClient()
}

// GetCommandAircraft 指挥机标志
func (s DevStatus) GetCommandAircraft() byte {
	return s.GetByteWithPos(5, 7)
}

// SaveToDb 缓存到数据库
func (s DevStatus) SaveToDb() {
	// 未登录，或DMRID异常
	if !GlobalApp.IsLogin.Load() || GlobalApp.DevDmridStr == "" {
		return
	}

	d := DevStatusRow{
		DmrId:  GlobalApp.DevDmridStr,
		Status: s,
	}
	_ = d.InsertOrReplace()
}

var debounceFunc = debounce(func() {
	GlobalApp.NotifyDeviceStatus()
}, 200*time.Millisecond)

func (s DevStatus) NotifyClient() {
	if !GlobalApp.IsLogin.Load() {
		return
	}

	debounceFunc()
}
