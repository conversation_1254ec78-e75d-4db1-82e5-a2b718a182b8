package goproxy

import (
	"reflect"
	"testing"
)

func Test_DevStatus(t *testing.T) {
	devStatus := DevStatus{8, 0, 1, 0, 194, 0}
	t.Run("setByteWithPos", func(t *testing.T) {
		byteIndex := byte(4)
		bitIndex := byte(4)
		target := devStatus[byteIndex]
		devStatus.SetByteWithPos(byteIndex, bitIndex, 1)
		if !reflect.DeepEqual(devStatus[byteIndex], byte(210)) {
			t.<PERSON><PERSON><PERSON>("expect %v, but got %v", byte(210), devStatus[byteIndex])
		}
		devStatus.SetByteWithPos(byteIndex, bitIndex, 0)
		if !reflect.DeepEqual(devStatus[byteIndex], target) {
			t.<PERSON>rf("expect %v, but got %v", target, devStatus[byteIndex])
		}
	})
	t.Run("GetByteWithPos", func(t *testing.T) {
		result := devStatus.GetByteWithPos(0, 3)
		if !reflect.DeepEqual(result, byte(1)) {
			t.<PERSON><PERSON><PERSON>("expect %v, but got %v", byte(1), result)
		}
	})
	t.Run("SetByteWithRange & GetByteWithRange", func(t *testing.T) {
		result := devStatus.GetByteWithRange(4, 0, 4)
		if !reflect.DeepEqual(result, byte(2)) {
			t.Errorf("expect %v, but got %v", byte(2), result)
		}

		devStatus.SetByteWithRange(4, 0, 4, 26)
		if !reflect.DeepEqual(devStatus[4], byte(218)) {
			t.Errorf("expect %v, but got %v", byte(218), devStatus[4])
		}

		result = devStatus.GetByteWithRange(4, 0, 4)
		if !reflect.DeepEqual(result, byte(26)) {
			t.Errorf("expect %v, but got %v", byte(26), result)
		}
	})
	t.Run("SetChannelNo & GetChannelNo", func(t *testing.T) {
		var expect int32 = int32(2)
		result := devStatus.GetChannelNo()
		if !reflect.DeepEqual(result, expect) {
			t.Errorf("expect %v, but got %v", expect, result)
		}

		expect = 19
		devStatus.SetChannelNo(expect)
		result = devStatus.GetChannelNo()
		if !reflect.DeepEqual(result, expect) {
			t.Errorf("expect %v, but got %v", expect, result)
		}

		expect = 1555
		devStatus.SetChannelNo(1555)
		result = devStatus.GetChannelNo()
		if !reflect.DeepEqual(result, expect) {
			t.Errorf("expect %v, but got %v", expect, result)
		}
		if !reflect.DeepEqual(devStatus[4], byte(211)) {
			t.Errorf("expect %v, but got %v", byte(211), devStatus[4])
		}
		if !reflect.DeepEqual(devStatus[5], byte(48)) {
			t.Errorf("expect %v, but got %v", byte(48), devStatus[5])
		}

		expect = 2018
		devStatus.SetChannelNo(2018)
		result = devStatus.GetChannelNo()
		if !reflect.DeepEqual(result, expect) {
			t.Errorf("expect %v, but got %v", expect, result)
		}

		if !reflect.DeepEqual(devStatus[4], byte(194)) {
			t.Errorf("expect %v, but got %v", byte(194), devStatus[4])
		}
		if !reflect.DeepEqual(devStatus[5], byte(63)) {
			t.Errorf("expect %v, but got %v", byte(63), devStatus[5])
		}
	})
}
