package goproxy

import "C"
import (
	"bytes"
	"unsafe"

	"goproxy/app_proto"

	"github.com/gogo/protobuf/jsonpb"
)

//typedef void (*wakeLockCallBack)();
//wakeLockCallBack g_LockFunc,g_ReleaseFunc;
//void setwakeLockCallBack(void* cb){g_LockFunc=(wakeLockCallBack)(cb); return;}
//void wakeLock(){if(g_LockFunc){g_LockFunc();} return;}
//void setwakeReleaseCallBack(void* cb){g_ReleaseFunc=(wakeLockCallBack)(cb); return;}
//void wakeRelease(){if(g_ReleaseFunc){g_ReleaseFunc();} return;}
//
//typedef void (*mediaStateChangeCallBack)();
//mediaStateChangeCallBack g_mediaStartFunc,g_mediaStopFunc;
//void setMeidaStateStartCallBack(void* cb){g_mediaStartFunc=(mediaStateChangeCallBack)(cb); return;}
//void mediaStart(){if(g_mediaStartFunc){g_mediaStartFunc();} return;}
//
//typedef void (*notificationTxtChangeCallBack)(void*,int);
//notificationTxtChangeCallBack g_updateTitleFunc,g_updateContentFunc;
//void setUpdateNotificationTitleCallBack(void* cb){g_updateTitleFunc=(notificationTxtChangeCallBack)(cb); return;}
//void updateTitle(void* data,int data_len){if(g_updateTitleFunc){g_updateTitleFunc(data,data_len);} return;}
//void setUpdateNotificationContentCallBack(void* cb){g_updateContentFunc=(notificationTxtChangeCallBack)(cb); return;}
//void updateContent(void* data,int data_len){if(g_updateContentFunc){g_updateContentFunc(data,data_len);} return;}
//
//typedef void (*kcpStateChangedCallBack)();
//kcpStateChangedCallBack g_kcpConnFunc,g_kcpConnLoseFunc;
//void setkcpStateConnCallBack(void* cb){g_kcpConnFunc=(kcpStateChangedCallBack)(cb); return;}
//void kcpConn(){if(g_kcpConnFunc){g_kcpConnFunc();} return;}
//void setkcpStateConnLoseCallBack(void* cb){g_kcpConnLoseFunc=(kcpStateChangedCallBack)(cb); return;}
//void kcpConnLose(){if(g_kcpConnLoseFunc){g_kcpConnLoseFunc();} return;}
//
//typedef void (*locatorCallBack)();
//typedef void (*locatoionOnceCallBack)(int);
//locatoionOnceCallBack g_locationOnceFunc;
//locatorCallBack g_locationStartFunc,g_locationStopFunc;
//void setLocationOnceCallBack(void* cb){g_locationOnceFunc=(locatoionOnceCallBack)(cb); return;}
//void locationOnce(int force){if(g_locationOnceFunc){g_locationOnceFunc(force);} return;}
//void setLocationStartCallBack(void* cb){g_locationStartFunc=(locatorCallBack)(cb); return;}
//void locationStart(){if(g_locationStartFunc){g_locationStartFunc();} return;}
//void setLocationStopCallBack(void* cb){g_locationStopFunc=(locatorCallBack)(cb); return;}
//void locationStop(){if(g_locationStopFunc){g_locationStopFunc();} return;}
//
//typedef void (*recorderCallBack)();
//recorderCallBack g_recorderStartFunc,g_recorderStopFunc;
//void setRecorderStartCallBack(void* cb){g_recorderStartFunc=(recorderCallBack)(cb); return;}
//void recorderStart(){if(g_recorderStartFunc){g_recorderStartFunc();} return;}
//void setRecorderStopCallBack(void* cb){g_recorderStopFunc=(recorderCallBack)(cb); return;}
//void recorderStop(){if(g_recorderStopFunc){g_recorderStopFunc();} return;}
//
//typedef void (*playerCallBack)();
//playerCallBack g_playerStartFunc,g_playerStopFunc;
//void setPlayerStartCallBack(void* cb){g_playerStartFunc=(playerCallBack)(cb); return;}
//void playerStart(){if(g_playerStartFunc){g_playerStartFunc();} return;}
//void setPlayerStopCallBack(void* cb){g_playerStopFunc=(playerCallBack)(cb); return;}
//void playerStop(){if(g_playerStopFunc){g_playerStopFunc();} return;}
//
//typedef void (*playOkErrCallBack)();
//playOkErrCallBack g_playOkFunc,g_playErrFunc;
//void setPlayOkCallBack(void* cb){g_playOkFunc=(playOkErrCallBack)(cb); return;}
//void playOkStart(){if(g_playOkFunc){g_playOkFunc();} return;}
//void setPlayErrCallBack(void* cb){g_playErrFunc=(playOkErrCallBack)(cb); return;}
//void playErrStart(){if(g_playErrFunc){g_playErrFunc();} return;}
//
//typedef int (*opusCodecCallBack)(unsigned char *opus, int opus_len, short *pcm, int pcm_frame_size);
//opusCodecCallBack g_opusCodecEncodeFunc,g_opusCodecDecodeFunc;
//void setOpusCodecEncodeFunc(void* cb){g_opusCodecEncodeFunc=(opusCodecCallBack)(cb); return;}
//int opusCodecEncode(unsigned char *opus, int opus_len, short *pcm, int pcm_frame_size){int len=0;
//if(g_opusCodecEncodeFunc){len=g_opusCodecEncodeFunc(opus,opus_len,pcm,pcm_frame_size);} return len;}
//void setOpusCodecDecodeFunc(void* cb){g_opusCodecDecodeFunc=(opusCodecCallBack)(cb); return;}
//int opusCodecDecode(unsigned char *opus, int opus_len, short *pcm, int pcm_frame_size){int len=0;
//if(g_opusCodecDecodeFunc){len=g_opusCodecDecodeFunc(opus,opus_len,pcm,pcm_frame_size);} return len;}
//
//typedef int (*getAppBuildInfoFunc)(char *data, int data_len);
//getAppBuildInfoFunc g_getAppBuildInfo;
//void setGetAppBuildInfoFunc(void* cb){g_getAppBuildInfo=(getAppBuildInfoFunc)(cb); return;}
//int getAppBuildInfo(char *data, int data_len)
//{int len=0; if(g_getAppBuildInfo){len=g_getAppBuildInfo(data, data_len);} return len;}
//
//typedef void (*uniproGpsSet)();
//uniproGpsSet g_enableGps,g_disableGps;
//void setUniproGpsSet(void* enableCb, void* disableCb){g_enableGps=(uniproGpsSet)(enableCb); g_disableGps=(uniproGpsSet)(disableCb); return;}
//void enableGps(){if(g_enableGps){g_enableGps();} return;}
//void disableGps(){if(g_disableGps){g_disableGps();} return;}
import "C"

func RegisterUniproGpsSetFunc(enable, disable unsafe.Pointer) {
	C.setUniproGpsSet(enable, disable)
}

func enableGps() {
	C.enableGps()
}

func disableGps() {
	C.disableGps()
}

func RegisterWakeLockFunc(lock, release unsafe.Pointer) {
	C.setwakeLockCallBack(lock)
	C.setwakeReleaseCallBack(release)
}

func wakeLock() {
	C.wakeLock()
}

func wakeRelease() {
	C.wakeRelease()
}

func RegisterMediaStateChangeFunc(start unsafe.Pointer) {
	C.setMeidaStateStartCallBack(start)
}

func mediaStart() {
	C.mediaStart()
}

func RegisterUpdateNotificationChangeFunc(updateTitle, updateContent unsafe.Pointer) {
	C.setUpdateNotificationTitleCallBack(updateTitle)
	C.setUpdateNotificationContentCallBack(updateContent)
}

func updateTitle(data []byte) {
	C.updateTitle(unsafe.Pointer(&data[0]), C.int(len(data)))
}

func updateContent(data []byte) {
	if len(data) == 0 {
		C.updateContent(nil, C.int(0))
	} else {
		C.updateContent(unsafe.Pointer(&data[0]), C.int(len(data)))
	}
}

func RegisterKcpStateChangeFunc(conn, connLose unsafe.Pointer) {
	C.setkcpStateConnCallBack(conn)
	C.setkcpStateConnLoseCallBack(connLose)
}

func kcpConn() {
	C.kcpConn()
}

func kcpConnLose() {
	C.kcpConnLose()
}

func RegisterLocatorFunc(locationOnce, start, stop unsafe.Pointer) {
	C.setLocationOnceCallBack(locationOnce)
	C.setLocationStartCallBack(start)
	C.setLocationStopCallBack(stop)
}

func LocationOnce(force int) {
	C.locationOnce(C.int(force))
}

func RegisterLocationOnceFunc(locationOnce unsafe.Pointer) {
	C.setLocationOnceCallBack(locationOnce)
}

func LocationStart() {
	C.locationStart()
}

func locationStop() {
	C.locationStop()
}

func RegisterRecorderFunc(start, stop unsafe.Pointer) {
	C.setRecorderStartCallBack(start)
	C.setRecorderStopCallBack(stop)
}

func RecorderStart() {
	C.recorderStart()
}

func RecorderStop() {
	C.recorderStop()
}

func RegisterPlayerFunc(start, stop unsafe.Pointer) {
	C.setPlayerStartCallBack(start)
	C.setPlayerStopCallBack(stop)
}

func PlayerStart() {
	C.playerStart()
}

func PlayerStop() {
	C.playerStop()
}

func RegisterOkErrPlayerFunc(ok, err unsafe.Pointer) {
	C.setPlayOkCallBack(ok)
	C.setPlayErrCallBack(err)
}

func PlayOk() {
	C.playOkStart()
}

func PlayErr() {
	C.playErrStart()
}

func RegisterOpusCodecFunc(encode, decode unsafe.Pointer) {
	C.setOpusCodecEncodeFunc(encode)
	C.setOpusCodecDecodeFunc(decode)
}

func OpusCodecEncode(opus []byte, pcm []int16) int {
	l := C.opusCodecEncode((*C.uchar)(unsafe.Pointer(&opus[0])), C.int(len(opus)),
		(*C.short)(unsafe.Pointer(&pcm[0])), C.int(len(pcm)))
	return int(l)
}

func OpusCodecDecode(opus []byte, pcm []int16) int {
	l := C.opusCodecDecode((*C.uchar)(unsafe.Pointer(&opus[0])), C.int(len(opus)),
		(*C.short)(unsafe.Pointer(&pcm[0])), C.int(len(pcm)))
	return int(l)
}

func RegisterGetAppBuildInfoFunc(cb unsafe.Pointer) {
	C.setGetAppBuildInfoFunc(cb)
}

func GetAppBuildInfo() *app_proto.AppBuildInfo {
	data := make([]byte, 480)
	length := C.getAppBuildInfo((*C.char)(unsafe.Pointer(&data[0])), C.int(len(data)))
	if length <= 0 {
		return nil
	}
	res := &app_proto.AppBuildInfo{}
	jsonpb.Unmarshal(bytes.NewReader(data[:length]), res)
	return res
}
