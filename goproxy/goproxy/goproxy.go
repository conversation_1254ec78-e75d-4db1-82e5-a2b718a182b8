package goproxy

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	_ "net/http/pprof"
	"strconv"
	"time"

	"goproxy/app_proto"
	"goproxy/bfkcp"

	"github.com/gorilla/websocket"
	"go.uber.org/atomic"
)

var upGrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

var GlobalApp = &App{
	// IsNetworkLost: atomic.NewBool(false),
	IsInReLoginState:       atomic.NewBool(false),
	IsPPTPressed:           atomic.NewBool(false),
	IsLocationOnce:         atomic.NewBool(false),
	IsLocationServerOn:     atomic.NewBool(false),
	ListenGroupList:        TListenGroupMap{},
	AddrBookList:           TAddressBookMap{},
	UnknownUserBookList:    TAddressBookMap{},
	OnlineDevices:          TDmridMap{},
	SnParseBin:             make([]byte, 8),
	IsLogin:                atomic.NewBool(false),
	LastDataTime:           time.Now(),
	listenGroupUpdateSeqNo: 0,
	Dic:                    0x00,
	IsSupportAmbe:          true,
	IsSupportOpus:          true,
	PreferCodec:            0,
	IsAlarmSpeaking:        atomic.NewBool(false),
	Cb42Code:               11, // 默认开启卫星定位
	lastSendBcxxTime:       time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC),
	InitDataFinish:         false,
}

var GlobalHttpServer *http.Server

var methodMap = make(map[app_proto.CmdCode]func(*bfkcp.RpcCmd))

func initClientReqHandler() {
	methodMap[app_proto.CmdCode_cmd_req_ping] = GlobalApp.ProcessPing
	methodMap[app_proto.CmdCode_cmd_req_login] = GlobalApp.ProcessLogin
	methodMap[app_proto.CmdCode_cmd_req_update_listen_group_list] = GlobalApp.ProcessUpdateListenGroupList
	methodMap[app_proto.CmdCode_cmd_req_query_listen_group_list] = GlobalApp.ProcessQueryListenGroupList
	methodMap[app_proto.CmdCode_cmd_req_update_speak_target] = GlobalApp.ProcessUpdateSpeakTarget
	methodMap[app_proto.CmdCode_cmd_req_query_speak_target] = GlobalApp.ProcessQuerySpeakTarget
	methodMap[app_proto.CmdCode_cmd_req_query_address_book] = GlobalApp.ProcessQueryAddrBook
	methodMap[app_proto.CmdCode_cmd_req_speak_start] = func(rpcCmd *bfkcp.RpcCmd) {
		GlobalApp.ProcessSpeakStart(rpcCmd, false)
	}
	methodMap[app_proto.CmdCode_cmd_req_speak_stop] = GlobalApp.ProcessSpeakStop
	methodMap[app_proto.CmdCode_cmd_req_speak_status] = GlobalApp.ProcessSpeakStatus
	methodMap[app_proto.CmdCode_cmd_req_media_play_status] = GlobalApp.ProcessPlayStatus
	methodMap[app_proto.CmdCode_cmd_resp_confirm_short_messages] = GlobalApp.ProcessConfirmShortMessage
	methodMap[app_proto.CmdCode_cmd_send_short_messages] = GlobalApp.ProcessSendShortMessage
	methodMap[app_proto.CmdCode_cmd_req_is_login] = GlobalApp.ProcessQueryIsLogin
	methodMap[app_proto.CmdCode_cmd_req_is_conn_server] = GlobalApp.ProcessQueryIsConnServer
	methodMap[app_proto.CmdCode_cmd_req_update_server_addr] = GlobalApp.ProcessUpdateServerAddr
	methodMap[app_proto.CmdCode_cmd_req_query_login_user] = GlobalApp.ProcessQueryLoginUser
	methodMap[app_proto.CmdCode_cmd_req_query_login_dev] = GlobalApp.ProcessQueryLoginDev
	methodMap[app_proto.CmdCode_cmd_req_set_speak_time_out_duration] = GlobalApp.ProcessSetSpeakTimeOut
	methodMap[app_proto.CmdCode_cmd_req_get_speak_time_out_duration] = GlobalApp.ProcessGetSpeakTimeOut
	methodMap[app_proto.CmdCode_cmd_req_update_voice_config] = GlobalApp.ProcessInitSystemConf
	methodMap[app_proto.CmdCode_cmd_req_delete_listen_group] = GlobalApp.ProcessDeleteListenGroup
	methodMap[app_proto.CmdCode_cmd_req_add_listen_group] = GlobalApp.ProcessAddListenGroup
	methodMap[app_proto.CmdCode_cmd_req_login_quit] = GlobalApp.ProcessLoginQuit
	methodMap[app_proto.CmdCode_cmd_req_query_default_speak_target] = GlobalApp.ProcessQueryDefaultTarget
	methodMap[app_proto.CmdCode_cmd_req_query_default_dev_config] = GlobalApp.ProcessQueryDevDefaultConfig
	methodMap[app_proto.CmdCode_cmd_req_play_local_cache_media] = GlobalApp.ProcessPlayerLocalCacheMedia
	methodMap[app_proto.CmdCode_cmd_req_query_call_back_target] =
		GlobalApp.ProcessQueryCallBackSpeakTarget
	methodMap[app_proto.CmdCode_cmd_req_clear_call_back_target] =
		GlobalApp.ProcessClearCallBackSpeakTarget
	methodMap[app_proto.CmdCode_cmd_req_stop_play_local_cache_media] =
		GlobalApp.ProcessStopPlayLocalCacheMedia
	methodMap[app_proto.CmdCode_cmd_req_set_media_software] = GlobalApp.ProcessSetMediaSoftware
	methodMap[app_proto.CmdCode_cmd_req_query_addr_book_by_dmrid] = GlobalApp.ProcessQueryDevByDmrid
	methodMap[app_proto.CmdCode_cmd_req_map_token] = GlobalApp.ProcessRequestMapToken
	methodMap[app_proto.CmdCode_cmd_req_gps_location_once] = GlobalApp.ProcessRequestGpsLocationOnce
	methodMap[app_proto.CmdCode_cmd_req_gps_location_on] = GlobalApp.ProcessRequestGpsLocationOn
	methodMap[app_proto.CmdCode_cmd_req_gps_location_off] = GlobalApp.ProcessRequestGpsLocationOff
	// 81
	methodMap[app_proto.CmdCode_cmd_req_device_gps_location_permission] =
		GlobalApp.ProcessRequestDeviceGpsLocationPermission
	methodMap[app_proto.CmdCode_cmd_req_query_device_gps_location_permission] =
		GlobalApp.ProcessRequestQueryDeviceGpsLocationPermission

	/**
	 * 以下为POC终端各类命令接口映射
	 */
	methodMap[app_proto.CmdCode_cmd_req_query_contact] = GlobalApp.QueryPocContacts
	methodMap[app_proto.CmdCode_cmd_req_query_poc_default_group] = GlobalApp.QueryPocDefaultGroup
	methodMap[app_proto.CmdCode_cmd_req_update_poc_listen_group] = GlobalApp.UpdatePocListenGroup
	methodMap[app_proto.CmdCode_cmd_req_query_poc_listen_group] = GlobalApp.QueryPocListenGroup
	// methodMap[app_proto.CmdCode_cmd_update_last_unknown_position] = GlobalApp.UpdateLastUnknownPosition
	methodMap[app_proto.CmdCode_cmd_req_query_outside_permission_contact] = GlobalApp.QueryOutsidePermissionContact
	methodMap[app_proto.CmdCode_cmd_req_send_alarm] = GlobalApp.SendAlarm
	methodMap[app_proto.CmdCode_cmd_req_query_poc_config] = GlobalApp.QueryPocConfig
	methodMap[app_proto.CmdCode_cmd_req_query_online_contact] = GlobalApp.QueryOnlineContact
	methodMap[app_proto.CmdCode_cmd_sync_poc_config_to_proxy] = GlobalApp.SyncPocConfig
	methodMap[app_proto.CmdCode_cmd_notify_init_data_finish] = GlobalApp.NotifyInitDataFinish
	methodMap[app_proto.CmdCode_cmd_notify_login_timeout] = GlobalApp.NotifyAutoLoginTimeout
	/**
	 * 以下为Flutter端对配置工具请求的响应
	 */
	methodMap[app_proto.CmdCode_cmd_resp_query_app_config] = GlobalApp.Respond2AppConfig
	methodMap[app_proto.CmdCode_cmd_resp_set_app_config] = GlobalApp.Respond2AppConfig
}

func checkIfKcpLoseConn() {
	go func() {
		for {
			time.Sleep(time.Second * 20)

			if kcpInstance.IsConn2Server.Load() {
				if time.Since(kcpInstance.LastDataTime.Load()) > kcpTimeout {
					kcpInstance.close()
				}
				continue
			}

			if GlobalApp.IsLogin.Load() && !kcpInstance.IsConn2Server.Load() {
				log.Println("checkIfKcpLoseConn ", &kcpInstance.UdpSession)
				GlobalApp.OnKcpLoseConn()
			}
		}
	}()
}

func getClientReqHandlerByCmdCode(code app_proto.CmdCode) (handler func(*bfkcp.RpcCmd), err error) {
	if methodMap == nil {
		err = errors.New("getClientReqHandlerByCmdCode methodMap is nil")
		return
	}

	handler = methodMap[code]
	if handler == nil {
		errStr := fmt.Sprintf("getClientReqHandlerByCmdCode can not find handler in methodMap.cmdCode:%d", code)
		err = errors.New(errStr)
	}
	return
}

func processAppConn(ws *websocket.Conn) {
	defer func(ws *websocket.Conn) {
		log.Println("last ws was close", ws.RemoteAddr().String())
		_ = ws.Close()
	}(ws)

	GlobalApp.DevStatus = CreateDefaultDevStatus()
	GlobalApp.GotNewConn(ws)

	for {
		err, errorCode, rpc_cmd := GlobalApp.Read()
		if err != nil {
			// 读取超时， 继续等待读取数据
			if errorCode == WsReadTimeoutErrorCode {
				continue
			}
			if errorCode == WsReaderWriterErrorCode {
				break
			} else {
				continue
			}
		}

		log.Println("ws read rpc_cmd:", app_proto.CmdCode(rpc_cmd.Cmd), rpc_cmd)
		handler, err := getClientReqHandlerByCmdCode(app_proto.CmdCode(rpc_cmd.Cmd))
		if err != nil {
			log.Println("processAppConn getClientReqHandlerByCmdCode err", err)
		} else {
			go handler(rpc_cmd)
		}
	}
}

func home(w http.ResponseWriter, r *http.Request) {
	_, _ = w.Write([]byte("bf8100 device app goproxy running"))
	_ = r
}

func proxy(w http.ResponseWriter, r *http.Request) {
	c, err := upGrader.Upgrade(w, r, nil)
	if err != nil {
		log.Print("upgrade proxy:", err)
		return
	}

	processAppConn(c)
}

const proxyVersion = "1.0.0"

func StartProxy() {
	log.Printf("goproxy start proxy, version=%s", proxyVersion)
	log.Println("GlobalApp.DevStatus", GlobalApp.DevStatus)
	checkIfKcpLoseConn()
	initClientReqHandler()
	http.HandleFunc("/", home)
	http.HandleFunc("/proxy", proxy)
	http.HandleFunc("/appBuildInfo", appBuildInfo)
	http.HandleFunc("/appConfig", appConfig)

	for i := 0; i < 3; i++ {
		listenPort := ":" + strconv.Itoa(int(ProxyHttpPort))
		GlobalHttpServer = &http.Server{
			Addr: listenPort,
		}
		log.Println("http listen on " + listenPort)
		err := GlobalHttpServer.ListenAndServe()
		if err != nil {
			log.Println("<<<<<<<<<<<<<<<<<<<<<< ListenAndServe start error:", err.Error())
		}
		ProxyHttpPort++
		if AppIsExited.Load() {
			break
		}
	}
}

func GetProxyPort() uint16 {
	return ProxyHttpPort
}

func FuncTest() {
	//time.Sleep(10 * time.Second)
	//
	//lg := []string{"800D4265", "800D04D2", "80080001", "80080004"}
	//GlobalApp.ProcessUpdateListenGroupPackages(lg, 312)
	//time.Sleep(10 * time.Second)
	//GlobalApp.ProcessUpdateListenGroupPackages(lg, 313)
}

func FuncTestSms() {
	time.Sleep(10 * time.Second)

	GlobalApp.SendSms(0x00088265, GlobalApp.DevDmrid, 0x02, 1, encode2UTF16("hello world", false))
}

func LoginTest(isPwdLogin bool) {
	GlobalApp.UserName = "jhx"
	GlobalApp.Sys = "01"

	//go func() {
	//	time.Sleep(10 * time.Second)
	//	GlobalMediaManager.mediaCache.PlayMediaDataByTimeStamp("000884DE-000AD603-08360209062021")
	//	GlobalMediaManager.mediaCache.PlayMediaDataByTimeStamp("000884DE-000AD603-09583909062021")
	//}()

	//go func() {
	//	GlobalMediaManager.SetSpeakTimeout(4)
	//	time.Sleep(15 * time.Second)
	//	GlobalApp.CurrentSpeakTarget = dmrid2Hex(0x80088217)
	//	log.Println("GlobalApp.SendBc71()")
	//	GlobalApp.SendBc71()
	//
	//	log.Println("speakTarget", GlobalApp.CurrentSpeakTarget)
	//	time.Sleep(10 * time.Second)
	//	GlobalMediaManager.StopRecording()
	//}()

	//go func() {
	//	time.Sleep(10 * time.Second)
	//	GlobalApp.QueryUnknownUserByDmridHex("800FA24A,800FA11F,800F333F,00084401,00086666,", 2)
	//}()

	if isPwdLogin {
		reqLogin := &app_proto.ReqLogin{
			SysId:       "01",
			UserName:    "yyyjjj",
			UserPass:    "yyyjjj",
			LoginMethod: 0,
		}
		data, _ := reqLogin.Marshal()
		rpc_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_req_login), Body: data}
		GlobalApp.ProcessLogin(rpc_cmd)
	} else {
		reqLogin := &app_proto.ReqLogin{
			SysId:       "01",
			UserName:    "linfl",
			UserPass:    "123",
			LoginMethod: 0,
		}
		data, _ := reqLogin.Marshal()
		rpc_cmd := &bfkcp.RpcCmd{Cmd: int32(app_proto.CmdCode_cmd_req_login), Body: data}
		GlobalApp.ProcessLogin(rpc_cmd)
	}
}

func ExitProxy() {
	GlobalApp.Exit()
	AppIsExited.Store(true)
	// GlobalHttpServer.Shutdown(context.Background())
	// GlobalHttpServer.Close()

	// GlobalHttpServer.RegisterOnShutdown(proxy)

	// GlobalHttpServer.Shutdown(context.Background())
}
