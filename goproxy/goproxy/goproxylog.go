package goproxy

//typedef void (*logcallback)(void*,int);
//logcallback g_logcb;
//void setlogoutputcb(void* cb){g_logcb=(logcallback)(cb); return;}
//void logoutput(void* data,int data_len){if(g_logcb){g_logcb(data,data_len);} return;}
import "C"
import "unsafe"

func SetLogCallback(logcb unsafe.Pointer) {
	C.setlogoutputcb(logcb)
}

func LogOutput(data []byte) {
	C.logoutput(unsafe.Pointer(&data[0]), C.int(len(data)))
}
