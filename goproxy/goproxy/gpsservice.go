package goproxy

import (
	mapset "github.com/deckarep/golang-set/v2"
	"sync"
)

var GpsService = TgpsService{
	starters: mapset.NewSet[string](),
}

type TgpsService struct {
	sync.Mutex

	starters mapset.Set[string]
}

func (this *TgpsService) StartService(starter string) {
	this.Lock()
	defer this.Unlock()

	if this.starters.Contains(starter) {
		return
	}

	this.starters.Add(starter)

	if this.starters.Cardinality() == 1 {
		//start gps service
		go this.startGps()
	}
}

func (this *TgpsService) StopService(starter string) {
	this.Lock()
	defer this.Unlock()

	if !this.starters.Contains(starter) {
		return
	}

	this.starters.Remove(starter)

	if this.starters.Cardinality() == 0 {
		//stop gps service
		go this.stopGps()
	}

}

func (this *TgpsService) startGps() {
	LocationStart()
}

func (this *TgpsService) stopGps() {
	locationStop()
}
