package goproxy

import (
	"errors"
	"fmt"
	"log"
	"net"
	"runtime"
	"strconv"
	"strings"
	"time"

	"goproxy/app_proto"
	"goproxy/bfkcp"

	"github.com/xtaci/kcp-go/v5"
	"go.uber.org/atomic"
)

const kcpReadDeadLine = time.Duration(8) * time.Second
const kcpTimeout = time.Duration(30) * time.Second

const unmarshalErrorStr = "protobuf unmarshal error:"
const unmarshalErrorHeader = unmarshalErrorStr + "%w"
const kcpSendErrorStr = "kcp send error"

var read_buf = make([]byte, 2048)

type Kcp struct {
	ServerHost string
	ServerPort int

	IsLoginState *atomic.Bool
	IsOldKcp     *atomic.Bool

	LastDataTime  *atomic.Time
	LastSendTime  *atomic.Time
	UdpSession    *kcp.UDPSession
	IsConn2Server *atomic.Bool
}

var kcpInstance = &Kcp{
	LastDataTime:  atomic.NewTime(time.Now()),
	LastSendTime:  atomic.NewTime(time.Now()),
	IsConn2Server: atomic.NewBool(false),
	IsOldKcp:      atomic.NewBool(false),
	IsLoginState:  atomic.NewBool(false),
	ServerHost:    ServerKcpHost,
	ServerPort:    int(ServerKcpPort),
}

func (m *Kcp) getServerAddr() string {
	return m.ServerHost + ":" + strconv.Itoa(m.ServerPort)
}

func (m *Kcp) send(rpc_cmd *bfkcp.RpcCmd) error {
	if m.UdpSession == nil {
		err := errors.New("kcp send err,not connected")
		log.Println(err.Error())
		return err
	}
	data, err := rpc_cmd.Marshal()
	if err != nil {
		log.Println("Kcp sed marshal error:", err)
		return err
	}

	if rpc_cmd.Cmd != 30 && rpc_cmd.Cmd != 10 && rpc_cmd.Cmd != 1 { // not show ping
		log.Println("kcp send:", rpc_cmd)
	}

	m.LastSendTime.Store(time.Now())
	_, err = m.UdpSession.Write(data)
	return err
}

func (m *Kcp) read() (rpc_cmd *bfkcp.RpcCmd, err error, kcpError bool) {
	//每次读数据都不超过 deadLine
	m.setReadDeadLine()

	read_len, err := m.UdpSession.Read(read_buf)
	log.Println("kcp read", read_len, err)
	if read_len == 0 && err != nil {
		if strings.Contains(err.Error(), "timeout") {
			//log.Println("kcp read timeout:", err)
			if time.Since(m.LastDataTime.Load()) < kcpTimeout {
				pingerr := m.ping()
				if pingerr != nil {
					log.Println("ping kcp err:", pingerr)
					// ping 错误不是超时，此连接不可用
					if !strings.Contains(pingerr.Error(), "timeout") {
						kcpError = true
					}
				}
			} else { //超时两次
				kcpError = true
				log.Println("kcp ping timeout")
			}
		} else {
			log.Println("kcp read err:", err)
			kcpError = true
		}
		return
	}

	m.LastDataTime.Store(time.Now())

	if time.Since(m.LastSendTime.Load()) > kcpTimeout {
		_ = m.ping()
	}

	// 复制一份读取的数据，避免在goroutine中出现内存数据被修改导致数据异常问题
	rpc_cmd_bytes := make([]byte, read_len)
	copy(rpc_cmd_bytes, read_buf)
	rpc_cmd = &bfkcp.RpcCmd{}
	err = rpc_cmd.Unmarshal(rpc_cmd_bytes)
	if err != nil {
		log.Println("kcp read. unmarshal err:", m.remoteAddr().String(), err)
		err = fmt.Errorf(unmarshalErrorHeader, err)
		return
	}
	if rpc_cmd.Cmd == 30 || rpc_cmd.Cmd == 10 || rpc_cmd.Cmd == 1 {
		return
	}

	if read_len < 30 {
		log.Println("kcp read:", rpc_cmd)
	} else {
		log.Println("kcp read: len ", read_len, " cmd ", rpc_cmd.Cmd)
	}
	return
}

func (m *Kcp) ping() error {
	//go func() {
	//	wakeLock()
	//	time.Sleep(time.Second * 1)
	//	wakeRelease()
	//}()
	if m.UdpSession == nil {
		return fmt.Errorf("UdpSession is null")
	}

	rpc_cmd := &bfkcp.RpcCmd{Cmd: 1}

	return m.send(rpc_cmd)
}

func (m *Kcp) pong() error {
	if m.UdpSession == nil {
		return fmt.Errorf("UdpSession is null")
	}

	rpc_cmd := &bfkcp.RpcCmd{Cmd: 1, Res: 1}

	return m.send(rpc_cmd)
}

func (m *Kcp) setReadDeadLine() {
	_ = m.UdpSession.SetReadDeadline(time.Now().Add(kcpReadDeadLine))
}

func (m *Kcp) close() {
	m.IsConn2Server.Store(false)
	if m.UdpSession == nil {
		return
	}
	err := m.UdpSession.Close()
	if err != nil {
		log.Println("kcp close error", err)
	}
}

func (m *Kcp) remoteAddr() net.Addr {
	return m.UdpSession.RemoteAddr()
}

func (m *Kcp) checkUdpConnWithServer() bool {
	log.Println("kcp try checkUdpConnWithServer", m.getServerAddr())

	m.close()

	sess, err := kcp.DialWithOptions(m.getServerAddr(), nil, 0, 0)

	if err != nil {
		log.Println("kcp dial error:", err)
		return false
	}

	sess.FnOutOfBandPing = func(pkt *kcp.TudpPing) {
		//fmt.Println(time.Now().Format(time.StampMilli), "FnOutOfBandPing", pkt.Addr, pkt.Cmd)
		if !GlobalApp.IsLogin.Load() {
			fmt.Println("FnOutOfBandPing not login, ignore")
			return
		}
		if pkt.Cmd == 4 {
			fmt.Println("got out of band ping 4 ,need relogin")
			_ = sess.Close()
			return
		}
		if pkt.Cmd != 8 {
			return
		}

		var PktUdpPing0 = []byte{0xaa, 0xbb, 0xcc, 0xdd, 0xaa, 0xbb, 0xcc, 0xdd,
			0x00, 0x00, 0x00, 0x00,
			3, 0, 0, 0}
		PktUdpPing0[0+8] = byte(GlobalApp.DevDmrid)
		PktUdpPing0[1+8] = byte(GlobalApp.DevDmrid >> 8)
		PktUdpPing0[2+8] = byte(GlobalApp.DevDmrid >> 16)
		PktUdpPing0[3+8] = byte(GlobalApp.DevDmrid >> 24)

		_, _ = sess.WriteOutOfBand(PktUdpPing0)
	}
	m.UdpSession = sess
	sess.SetNoDelay(1, 40, 2, 1)
	sess.SetStreamMode(false)
	sess.SetWriteDelay(false)

	_ = m.ping()
	//got pong
	_, _, kcpError := m.read()
	if kcpError {
		return false
	}

	log.Println("kcp checkUdpConnWithServer", m.UdpSession.LocalAddr().String(), " , ", &sess)
	m.IsConn2Server.Store(true)
	return true
}

func (m *Kcp) initAsac(rpc_cmd *bfkcp.RpcCmd) {
	codes := &bfkcp.AmbeSerialCode{}
	err := codes.Unmarshal(rpc_cmd.Body)
	if err != nil {
		log.Println("initAsac Unmarshal err", err)
		return
	}
	sn := make([]uint16, 24)
	for i, item := range codes.Code {
		sn[i] = uint16(item)
	}
	SetUserSn(sn)
}

func (m *Kcp) queryUserDefaultListenGroup() error {
	rpc_cmd := &bfkcp.RpcCmd{Cmd: 314}
	return m.send(rpc_cmd)
}

func (m *Kcp) LoginQuit() {
	log.Println("kcp LoginQuit")
	m.IsOldKcp.Store(true)
	m.close()
}

func UpdateServerAddr(host string, port int) {
	kcpInstance.ServerPort = port
	kcpInstance.ServerHost = host
}

func CheckIfConn() bool {
	return kcpInstance.IsConn2Server.Load()
}

func Login(rpc_cmd *bfkcp.RpcCmd) {
	log.Println("kcp Login in")

	//prevent login method called repeat
	if kcpInstance.IsLoginState.Load() {
		log.Println("kcp is in Login state")
		return
	}
	kcpInstance.IsLoginState.Store(true)

	// checkUdpConnWithServer server. need got pong
	isConn := kcpInstance.checkUdpConnWithServer()
	if !isConn {
		kcpInstance.IsLoginState.Store(false)
		GlobalApp.PressNotify(&app_proto.Notify{Code: 5})
		log.Println("kcpInstance.checkUdpConnWithServer() failed")
		return
	}

	// 发送登录请求
	_ = kcpInstance.send(rpc_cmd)

	// 处理服务器的kcp消息
	kcpMessageHandler()
}

func QueryAmbeSn() {
	//linux
	platform := int64(1)

	if runtime.GOOS == "android" {
		platform = 2
	}

	if runtime.GOOS == "windows" {
		platform = 3
	}

	rpc_cmd := &bfkcp.RpcCmd{Cmd: 180, Sid: platform}
	_ = kcpInstance.send(rpc_cmd)
}

func kcpMessageHandler() {
	isGotLoginResp := false
	isLoginOk := false

	kcpConn()

	kcpHandle := kcpInstance

	defer func() {
		log.Println("kcp Login out", kcpHandle.UdpSession.LocalAddr().String(), " , ", &kcpHandle.UdpSession)
		if isLoginOk {
			GlobalApp.PressNotify(&app_proto.Notify{Code: 2})
		}
	}()

	//process server msg
	for {
		rpcCmd, err, kcpError := kcpHandle.read()
		if err != nil {
			if kcpHandle.IsOldKcp.Load() {
				log.Println("old kcp closed")
				return
			}
			if kcpError {
				break
			} else {
				continue
			}
		}

		switch rpcCmd.Cmd {
		case 1:
			if rpcCmd.Res == 0 {
				_ = kcpHandle.pong()
			}
		case 2, 3:
			GlobalApp.GotFSK(rpcCmd)
		case 8:
			GlobalApp.GotDevicesOnOffLine(rpcCmd)
		case 10:
			GlobalApp.GotBc10(rpcCmd)
		case 15: // media in start/stop
			GlobalApp.GotBc15(rpcCmd)
		case 30: // media .ambe package
			GlobalApp.GotBc30(rpcCmd)
		case 82:
			GlobalApp.GotDeviceRespGpsLocationPermission(rpcCmd)
		case 84:
			GlobalApp.GotDeviceRespQueryGpsLocationPermission(rpcCmd)
		case 100:
			if rpcCmd.Res == 0 { //valid resp login
				isGotLoginResp = true
				isLoginOk = GlobalApp.GotLoginResp(rpcCmd)
			}
		case 171:
			GlobalApp.GotCb171(rpcCmd)
		case 175:
			GlobalApp.GotCb175(rpcCmd)
		//case 180: //init asac
		//	kcpHandle.initAsac(rpcCmd)
		case 300: //channels
			GlobalApp.GotDevChannels(rpcCmd)
		case 305: //GotOrgList
			GlobalApp.GotOrgList(rpcCmd)
		case 307: //GotDevList
			GlobalApp.GotDevList(rpcCmd)
		case 311:
			GlobalApp.GotUpdateListenGroupResp(rpcCmd)
		case 312, 313:
			GlobalApp.GotPartUpdateListenGroupResp(rpcCmd)
		case 315:
			GlobalApp.OnGotAddrBook(rpcCmd)
		case 333:
			GlobalApp.OnGotOnlineDevices(rpcCmd)
		case 444: //通知被挤下线
			GlobalApp.GotOfflinePanic(rpcCmd)
		case 1440:
			GlobalApp.OnGotMapToken(rpcCmd)

		case 11:
			// poc终端命令处理
			GlobalApp.GotPocDeviceRpcCmd(rpcCmd)

		default:
			log.Println("Got unknown cmd:", rpcCmd.Cmd)
		}

		if isGotLoginResp && !isLoginOk { //login failed
			GlobalApp.IsLogin.Store(false)
			break
		}
	}

	kcpHandle.IsConn2Server.Store(false)
	kcpHandle.IsLoginState.Store(false)
	kcpConnLose()
}
