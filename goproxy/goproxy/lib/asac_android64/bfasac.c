
#include "libasac.h"
#include "bfasac.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

extern short imbe_count; // idb
extern short allpitch; // idb
extern short allamp;
extern short amp_tongji[20]; // idb
extern short amp_weight[10]; // idb
extern int32_t imbe_number; // idb

static int pitchTag = 0;

void resetAllPitch(){
    pitchTag = 1;
}

static short keyNoR1[24];
static int asacIninted = 0;
int isAsasIninted(void){
    return asacIninted;
}

void pcmGain(short pcm320[], short gain){
	if (gain == 0){
		return;
	}
	float multiplier = gain / 100.0;
	for (int ctr = 0; ctr < 160; ctr++) {
		double pcmval = pcm320[ctr] * multiplier;
		if (pcmval < 32767 && pcmval > -32768) {
			pcm320[ctr] = pcmval;
		}
		else if (pcmval > 32767) {
			pcm320[ctr] = 32767;
		}
		else if (pcmval < -32768) {
			pcm320[ctr] = -32768;
		}
	}
}

void resumeAsacStatus(short sn[24]){
    amp_tongji[10] = sn[0] ^ 0x5A7A;
    amp_tongji[11] = sn[1] ^ 0x5A7A;
    amp_tongji[12] = sn[2] ^ 0x5A7A;
    amp_tongji[13] = sn[3] ^ 0x5A7A;
    amp_tongji[14] = sn[4] ^ 0x5A7A;
    amp_tongji[15] = sn[5] ^ 0x5A7A;
    amp_tongji[16] = sn[6] ^ 0x5A7A;
    amp_tongji[17] = sn[7] ^ 0x5A7A;

    for (int i = 0; i < 10; i++) {
        amp_weight[i] = sn[i] ^ 0xa5a7;
    }
}

void asacInit(short sn[24]) {
    for(int i=0;i<24;i++){
        keyNoR1[i]=sn[i];
    }

    resumeAsacStatus(sn);

	asacInitEncode();
	asacInitDecode();
    asacIninted = 1;
}


void asacInitEncode(){
	encode_init( keyNoR1);
}

void asacInitDecode(){
	decode_init( keyNoR1);

}


void ambe9Toambe72(unsigned char ambe9[9], short ambe72[72]) {
	for (int j = 0; j < 9; ++j) {
		for (int k = 0; k < 8; ++k) {
			int idx72 = j * 8 + k;
			int t = 1 << (7 - k);
			if (ambe9[j] & t) {
				ambe72[idx72] = 1;
			}
			else {
				ambe72[idx72] = 0;
			}
		}
	}
}

void ambe72Toambe9(unsigned char ambe9[9], short ambe72[72]) {
	for (int j = 0; j < 9; ++j) {
		for (int k = 0; k < 8; ++k) {
			int idx72 = j * 8 + k;
			int t = 1 << (7 - k);
			if (ambe72[idx72] & 1) {
				ambe9[j] |= t;
			}
			else {
				ambe9[j] &= (~t);
			}
		}
	}
}

void asacEncode20ms(short buf_pcm160[160], unsigned char ambe9[9], short ns) {
	short buf_ambe49[170];
	short buf_ambe72[170];

	// pcm320 to ambe49
	asac_encode( buf_ambe49, buf_pcm160, ns, keyNoR1);

	// ambe fec to ambe72
	asac_channel_encode( buf_ambe49, buf_ambe72, keyNoR1);

	ambe72Toambe9(ambe9, buf_ambe72);

}
void asacDecode20ms(unsigned char ambe9[9], short buf_pcm160[160], short ns, short gain) {
	short buf_ambe49[170];
	short buf_ambe72[170];
	ambe9Toambe72(ambe9, buf_ambe72);

	// ambe72 fec to ambe49
	asac_channel_decode( buf_ambe49, buf_ambe72, keyNoR1);

    if(pitchTag == 1){
        imbe_number=0;
        imbe_count=0;
        allpitch=0;
        allamp=0;
    }

	// ambe decode 49bit->320byte
	asac_decode( buf_ambe49, buf_pcm160,  keyNoR1);

	pcmGain(buf_pcm160, gain);

}
void asacEncode60ms(short pcm960[480],unsigned char ambe27[27],  short ns) {
	for (int i = 0; i < 3; ++i) {
		asacEncode20ms(pcm960 + 160 * i, ambe27 + 9 * i, ns);
	}
}
void asacDecode60ms(unsigned char ambe27[27], short pcm960[480], short ns, short gain) {
	for (int i = 0; i < 3; ++i) {
		asacDecode20ms(ambe27 + 9 * i, pcm960 + 160 * i, ns, gain);
	}
}

#ifdef __cplusplus
}
#endif
