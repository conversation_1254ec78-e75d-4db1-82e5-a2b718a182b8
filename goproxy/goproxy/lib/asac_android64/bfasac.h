#ifndef ASAC_DLL_H_
#define ASAC_DLL_H_

#ifdef __cplusplus
extern "C" {
#endif

#pragma once

void resetAllPitch();
int isAsasIninted(void);

//init asac coder
void asacInit(short sn[24]);
void asacInitEncode(void);
void asacInitDecode(void);

void ambe9Toambe72(unsigned char ambe9[9], short ambe72[72]);

void ambe72Toambe9(unsigned char ambe9[9], short ambe72[72]);

//pcm format 8k16bit little endian signed short

// 20ms pcm(320bytes) to ambe(9bytes)
void asacEncode20ms(short pcm160[160], unsigned char ambe9[9],
                             short ns);
// 20ms ambe9 to pcm320
void asacDecode20ms(unsigned char ambe9[9], short pcm160[160],
                              short ns, short gain);
// 60ms pcm960 to ambe27
void asacEncode60ms(short pcm960[480],unsigned char ambe27[27],
                             short ns);
// 60ms ambe27 to pcm960
void asacDecode60ms(unsigned char ambe27[27], short pcm960[480],
                              short ns, short gain);


#ifdef __cplusplus
}
#endif

#endif
