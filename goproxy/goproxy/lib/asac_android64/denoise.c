#include "speex/speex_resampler.h"
#include "rnnoise.h"
#include <stdio.h>
#include "denoise.h"
#define FRAME_SIZE 480
static DenoiseState *rnnoise_st;
static float frame_buf[FRAME_SIZE];

SpeexResamplerState *resample_st;
SpeexResamplerState *resample_8To48k;

void initDenoiser() {
  rnnoise_st = rnnoise_create(NULL);
  int err;
  resample_st = speex_resampler_init(1, 48000, 8000, 8, &err);
}
void deInitDenoiser(){
   if(rnnoise_st){
     rnnoise_destroy(rnnoise_st);
     rnnoise_st = 0;
   }
}

//每次将8k int16 80sample 重采样为 48k int16 480 sample 10ms
int speex_resample8kTo48k(short pcm8k80sample[80], short pcm48k480sample[480]){
  if (!resample_8To48k){
    int err;
    resample_8To48k = speex_resampler_init(1, 8000, 48000, 8, &err);
  }

  spx_uint32_t in_len = 80;
  spx_uint32_t out_len = 480 * 2;

  if(!resample_8To48k){
    return 0;
  }

  speex_resampler_process_int(resample_8To48k, 0, pcm8k80sample, &in_len,
                              pcm48k480sample, &out_len);

  return out_len;
}

int denoisePcm480Sample(void* vpcm480){
   if(!rnnoise_st){
      initDenoiser();
   }

   if(!rnnoise_st){
      printf("rnnoise_st is null\n");
      return 0;
   }

   short *pcm480 = vpcm480;

       for (int i = 0; i < FRAME_SIZE; i++) {
     frame_buf[i] = pcm480[i];
   }

   rnnoise_process_frame(rnnoise_st, frame_buf, frame_buf);

   short tmp480[FRAME_SIZE];
   for (int i = 0; i < FRAME_SIZE; i++) {
     tmp480[i] = (short)frame_buf[i];
   }

   spx_uint32_t in_len,out_len;

   in_len=FRAME_SIZE;
   out_len = FRAME_SIZE;

  int r = speex_resampler_process_int(resample_st, 0, tmp480, &in_len,
                                           pcm480, &out_len);

   //printf("resample rxxx=%d in len:%d, output len:%d\n",r, in_len, out_len);
   return out_len;
}