#ifdef __cplusplus
extern "C" {
#endif

void encode_init(short *keyNo);
void decode_init(short *keyNo);
void asac_encode(short *bit_stream, short *snd, short se_enable, short *keyNo);
void asac_channel_encode(short *bit_stream, short *channel_stream, short *keyNo);
void asac_decode(short *bit_stream, short *snd, short *keyNo);
void asac_channel_decode(short *bit_stream, short *channel_stream, short *keyNo);


#ifdef __cplusplus
}
#endif
