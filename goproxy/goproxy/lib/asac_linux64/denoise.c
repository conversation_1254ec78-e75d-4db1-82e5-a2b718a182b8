#include <speex/speex_resampler.h>
#include <rnnoise.h>
#include <stdio.h>
#include "denoise.h"
#define FRAME_SIZE 480
static DenoiseState *rnnoise_st;
static float frame_buf[FRAME_SIZE];

SpeexResamplerState *resample_st;

void initDenoiser() {
  rnnoise_st = rnnoise_create(NULL);
  int err;
  resample_st = speex_resampler_init(1, 48000, 8000, 6, &err);
}
void deInitDenoiser(){
   if(rnnoise_st){
     rnnoise_destroy(rnnoise_st);
     rnnoise_st = 0;
   }
}

int denoisePcm480Sample(void* vpcm480){
   if(!rnnoise_st){
      initDenoiser();
   }

   if(!rnnoise_st){
      printf("rnnoise_st is null\n");
      return 0;
   }

   short *pcm480 = vpcm480;

       for (int i = 0; i < FRAME_SIZE; i++) {
     frame_buf[i] = pcm480[i];
   }

   rnnoise_process_frame(rnnoise_st, frame_buf, frame_buf);

   short tmp480[FRAME_SIZE];
   for (int i = 0; i < FRAME_SIZE; i++) {
     tmp480[i] = (short)frame_buf[i];
   }

   spx_uint32_t in_len,out_len;

   in_len=FRAME_SIZE;
   out_len = FRAME_SIZE;

  int r = speex_resampler_process_int(resample_st, 0, tmp480, &in_len,
                                           pcm480, &out_len);

   //printf("resample rxxx=%d in len:%d, output len:%d\n",r, in_len, out_len);
   return out_len;
}