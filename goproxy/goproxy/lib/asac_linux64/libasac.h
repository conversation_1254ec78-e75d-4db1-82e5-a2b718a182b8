#ifdef __cplusplus
extern "C" {
#endif

#define BITLEN 72
#define FRAME  160
#define NUM 2


void encode_init(short index, short *keyNo);
void decode_init(short index, short *keyNo);
void asac_encode(short index, short *bit_stream, short *snd, short se_enable, short *keyNo);
void asac_channel_encode(short index, short *bit_stream, short *channel_stream, short *keyNo);
void asac_decode(short index, short *bit_stream, short *snd, short vad_enable, short *keyNo);
void asac_channel_decode(short index, short *bit_stream, short *channel_stream, short *keyNo);


#ifdef __cplusplus
}
#endif
