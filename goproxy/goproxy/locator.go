package goproxy

import (
	"goproxy/app_proto"
	"goproxy/bfdx_proto"
	"log"
	"math"
	"strconv"
	"time"
)

var DefaultUTCTime = time.Date(2020, 01, 01, 00, 00, 00, 00, time.UTC)

const (
	earthRadiusMi = 6367000
)

func CalcRadiusFromLatDiffString(latDiff string) int {
	latDif, _ := strconv.Atoi(latDiff)
	val := math.Round(float64(latDif) * 1.85)
	return int(val)
}

func ToRadians(degrees float64) float64 {
	return degrees * math.Pi / 180
}

func CalculateRadius(center, boundary *LocationData) float64 {
	// 将经纬度转换为弧度
	lat1 := ToRadians(center.Lat)
	lon1 := ToRadians(center.Lon)
	lat2 := ToRadians(boundary.Lat)
	lon2 := ToRadians(boundary.Lon)

	// Haversine公式
	dLat := lat2 - lat1
	dLon := lon2 - lon1
	a := math.Sin(dLat/2)*math.Sin(dLat/2) +
		math.Cos(lat1)*math.Cos(lat2)*
			math.Sin(dLon/2)*math.Sin(dLon/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	distance := earthRadiusMi * c

	return distance
}

type LocationData struct {
	GpsTime                    time.Time
	Lon, Lat                   float64
	Direction, Speed, Altitude float64
}

func (m *LocationData) degreesToRadians() (_lon, _lat float64) {
	return ToRadians(m.Lon), ToRadians(m.Lat)
}

func (m *LocationData) getSubDistance(another *LocationData) (distance int) {
	return int(CalculateRadius(m, another))
}

func (m *LocationData) toGps84() *bfdx_proto.Gps84 {
	return &bfdx_proto.Gps84{
		GpsTime:   m.GpsTime.UTC().Format(TimeFormatStr),
		Lon:       m.Lon,
		Lat:       m.Lat,
		Direction: int32(m.Direction),
		Speed:     m.Speed,
		Altitude:  int32(m.Altitude),
	}
}

func (m *LocationData) toAppGps84() *app_proto.Gps84 {
	return &app_proto.Gps84{
		GpsTime:   m.GpsTime.UTC().Format(TimeFormatStr),
		Lon:       m.Lon,
		Lat:       m.Lat,
		Direction: int32(m.Direction),
		Speed:     m.Speed,
		Altitude:  int32(m.Altitude),
	}
}
func (m *LocationData) fromAppGps84(appGps84 *app_proto.Gps84) {
	_time, err := time.Parse(TimeFormatStr, appGps84.GpsTime)
	if err != nil {
		m.GpsTime = _time
	} else {
		m.GpsTime = time.Now().UTC()
	}

	m.Lon = appGps84.Lon
	m.Lat = appGps84.Lat
	m.Direction = float64(appGps84.Direction)
	m.Speed = appGps84.Speed
	m.Altitude = float64(appGps84.Altitude)
}

type Cb01Locator struct {
	*LocationData

	//发送间隔
	interval time.Duration
	//超出距离，send指令
	distance int
	//需要/已经 发送次数
	needCount, sendCount int
	// 定位监控结束信号
	endSignal chan byte
}

func (m *Cb01Locator) start() {
	GpsService.StartService("Cb01Locator")
}

func (m *Cb01Locator) stop() {
	GpsService.StopService("Cb01Locator")
	m.endSignal <- 1
	close(m.endSignal)
}

func (m *Cb01Locator) updateLocation(newLocation *LocationData) {
	m.sendCount++
	isEnd := m.sendCount >= m.needCount
	m.LocationData = newLocation
	GlobalApp.DevStatus.SetCentralPositionMonitoring(0)
	GlobalApp.sendBc01(newLocation, true)
	if isEnd {
		m.stop()
	}
}

func (m *Cb01Locator) checkIfNeedUpdate(newLocation *LocationData) (isNeed bool) {
	// 第一次定位时，为空
	if m.LocationData == nil || GlobalApp.Bc18Locator != nil {
		return true
	}
	if newLocation.GpsTime.Sub(m.LocationData.GpsTime) > m.interval {
		return true
	}
	if m.LocationData.getSubDistance(newLocation) >= m.distance {
		return true
	}

	return false
}

func (m *Cb01Locator) onLocation(location *LocationData) {
	isNeedUpdate := m.checkIfNeedUpdate(location)
	if !isNeedUpdate {
		return
	}
	m.updateLocation(location)
}

type Cb02Locator struct {
	*bfdx_proto.Cb02
	// 是否暂停，有CB01命令优先处理
	isPausedSignal chan byte
	isPaused       bool
	// 任务定时器
	ticker *time.Ticker
	*LocationData
	reportTime time.Time
}

func (m *Cb02Locator) runTask() {
	m.isPaused = false
	m.ticker = time.NewTicker(time.Duration(m.Time*5) * time.Second)

	for {
		select {
		// 按指定的时间间隔来上报
		case <-m.ticker.C:
			// 上一次定位上报间隔小于条件间隔，则不上报
			if time.Now().UTC().Sub(m.reportTime) < time.Duration(m.Time*5)*time.Second {
				continue
			}

			isValidLocation := true
			// 未定位，使用上一次定位数据
			if m.LocationData == nil {
				location := &LocationData{
					GpsTime: time.Now().UTC(),
				}
				if GlobalApp.LastKnownLocation != nil {
					location.fromAppGps84(GlobalApp.LastKnownLocation)
					isValidLocation = true
				} else {
					isValidLocation = false
				}
				m.LocationData = location
			}

			// 上报定位数据
			GlobalApp.sendBc01(m.LocationData, isValidLocation)

		// 得到CB01命令，暂停CB02信号
		case <-m.isPausedSignal:
			return
		}
	}
}

func (m *Cb02Locator) start() {
	GpsService.StartService("Cb02Locator")
	go m.runTask()
}

func (m *Cb02Locator) stop() {
	GpsService.StopService("Cb02Locator")
	m.isPausedSignal <- 1
	close(m.isPausedSignal)
	m.isPaused = true
	m.ticker.Stop()
}

func (m *Cb02Locator) checkIfNeedUpdate(newLocation *LocationData) (isNeed bool) {
	if m.isPaused || GlobalApp.Bc18Locator != nil || GlobalApp.Cb01Locator != nil {
		return false
	}
	// 第一次定位时，为空
	if m.LocationData == nil {
		return true
	}
	//if newLocation.GpsTime.Sub(m.LocationData.GpsTime) > time.Duration(m.Time)*time.Second {
	//	return true
	//}
	if m.LocationData.getSubDistance(newLocation) >= int(m.Size_*5) {
		return true
	}

	return false
}

func (m *Cb02Locator) onLocation(location *LocationData) {
	needReport := m.checkIfNeedUpdate(location)
	if !needReport {
		return
	}

	GlobalApp.sendBc01(location, true)
	m.reportTime = time.Now().UTC()
	m.LocationData = location
}

type Cb03AreaCheck struct {
	isGroupCmd                     bool
	dic                            uint8
	MinLon, MinLat, MaxLon, MaxLat float64
	isResolved                     bool
}

func (m *Cb03AreaCheck) start() {
	GpsService.StartService("Cb03AreaCheck")
}

func (m *Cb03AreaCheck) stop() {
	GpsService.StopService("Cb03AreaCheck")
}

func (m *Cb03AreaCheck) onLocation(location *LocationData) {
	if m.isResolved {
		return
	}

	isValidLocation := false
	if m.MinLon <= location.Lon && m.MaxLon >= location.Lon &&
		m.MinLat <= location.Lat && m.MaxLat >= location.Lat {
		isValidLocation = true
	}

	if m.isGroupCmd {
		GlobalApp.sendBc01(location, true)
	} else {
		GlobalApp.sendBc03(m.dic, location, isValidLocation)
	}
	m.isResolved = true
	m.stop()
}

type Cb04Locator struct {
	*LocationData

	// 入界监控/出界界监控
	*bfdx_proto.Cb04
	ticker *time.Ticker
	// 回岗信号
	goWorkSignal chan byte
	// 入界/出界界监牢中
	isMonitoring bool
	// 已经报过警
	crossBorderAlarm bool
}

func (m *Cb04Locator) runMonitoringTask() {
	defer func() {
		m.isMonitoring = false
		m.ticker.Stop()
	}()

	m.isMonitoring = true
	m.ticker = time.NewTicker(time.Duration(m.Time) * time.Minute)

	for {
		select {
		// 按指定的时间间隔来上报
		case <-m.ticker.C:
			// 未定位，使用上一次定位数据
			if m.LocationData == nil {
				location := &LocationData{
					GpsTime: time.Now().UTC(),
				}
				if GlobalApp.LastKnownLocation != nil {
					location.fromAppGps84(GlobalApp.LastKnownLocation)
				}
				m.LocationData = location
			}

			// 上报报警
			switch m.YN {
			case 1: // 启动入界监控
				if m.LocationData.Lat < m.MinLat || m.LocationData.Lat > m.MaxLat ||
					m.LocationData.Lon < m.MinLon || m.LocationData.Lon > m.MaxLon {
					continue
				}
				GlobalApp.DevStatus.SetEnableOutOfBoundsMonitoring(0)
				GlobalApp.DevStatus.SetOutOfBoundsMonitoringAlarm(0)
				GlobalApp.DevStatus.SetEnableInBoundsMonitoring(1)
				GlobalApp.DevStatus.SetInBoundsMonitoringAlarm(1)
			case 2: // 启动出界监控
				if m.LocationData.Lat > m.MinLat && m.LocationData.Lat < m.MaxLat &&
					m.LocationData.Lon > m.MinLon && m.LocationData.Lon < m.MaxLon {
					continue
				}
				GlobalApp.DevStatus.SetEnableInBoundsMonitoring(0)
				GlobalApp.DevStatus.SetInBoundsMonitoringAlarm(0)
				GlobalApp.DevStatus.SetEnableOutOfBoundsMonitoring(1)
				GlobalApp.DevStatus.SetOutOfBoundsMonitoringAlarm(1)
			default:
				continue
			}

			GlobalApp.Dic++
			_ = GlobalApp.SendBc04(GlobalApp.Dic, m.Cb04, byte(m.YN), m.LocationData)
			m.crossBorderAlarm = true

		case <-m.goWorkSignal:
			return
		}
	}
}

func (m *Cb04Locator) start() {
	m.goWorkSignal = make(chan byte)
	GpsService.StartService("Cb04Locator")
}

func (m *Cb04Locator) stop() {
	GpsService.StopService("Cb04Locator")
	m.goWorkSignal <- 1
	close(m.goWorkSignal)
}

func (m *Cb04Locator) onLocation(location *LocationData) {
	// [指令类型TP[2] = 01 启动入界监控；02=启动出界监控]
	tp := byte(m.YN)
	m.LocationData = location
	defer func() {
		// 判断是否有过越界报警，有则取消报警状态
		if m.crossBorderAlarm {
			m.goWorkSignal <- 1
			GlobalApp.DevStatus.SetOutOfBoundsMonitoringAlarm(0)
			GlobalApp.DevStatus.SetInBoundsMonitoringAlarm(0)
			GlobalApp.Dic++
			_ = GlobalApp.SendBc04(GlobalApp.Dic, m.Cb04, tp, location)
			m.crossBorderAlarm = false
		}
	}()

	switch m.YN {
	case 1: // 启动入界监控
		// 没有入界
		if location.Lat < m.MinLat || location.Lat > m.MaxLat ||
			location.Lon < m.MinLon || location.Lon > m.MaxLon {
			return
		}

		// 按轮询时间监控上报
		if !m.isMonitoring {
			go m.runMonitoringTask()
		}
	case 2: // 启动出界监控
		// 没有出界
		if location.Lat > m.MinLat && location.Lat < m.MaxLat &&
			location.Lon > m.MinLon && location.Lon < m.MaxLon {
			return
		}

		// 按轮询时间监控上报
		if !m.isMonitoring {
			go m.runMonitoringTask()
		}
	default:
		log.Println("Cb04Locator onLocation, but ignore YN:", m.YN)
	}
}

type Cb05Locator struct {
	// 岗哨监控参数
	*bfdx_proto.Cb05
	// 监控的半径
	Radius int

	*LocationData
	ticker *time.Ticker
	// 回岗/离岗的动作
	lastTp byte
	// 回岗信号
	goWorkSignal chan byte
	// 离岗监牢中
	outWorkMonitoring bool
}

func (m *Cb05Locator) runMonitoringTask() {
	defer func() {
		m.outWorkMonitoring = false
		m.ticker.Stop()
	}()

	m.outWorkMonitoring = true
	m.ticker = time.NewTicker(time.Duration(m.Time) * time.Minute)

	for {
		select {
		// 按指定的时间间隔来上报
		case <-m.ticker.C:
			// 已经回岗
			if m.lastTp == 1 {
				return
			}

			// 未定位，使用上一次定位数据
			if m.LocationData == nil {
				location := &LocationData{
					GpsTime: time.Now().UTC(),
				}
				if GlobalApp.LastKnownLocation != nil {
					location.fromAppGps84(GlobalApp.LastKnownLocation)
				}
				m.LocationData = location
			}

			// 上报离岗报警
			GlobalApp.Dic++
			GlobalApp.DevStatus.SetSentryMonitoringAlarm(1)
			_ = GlobalApp.SendBc05(GlobalApp.Dic, m.Cb05, m.lastTp, m.LocationData)

		case <-m.goWorkSignal:
			return
		}
	}
}

func (m *Cb05Locator) start() {
	m.goWorkSignal = make(chan byte)
	GpsService.StartService("Cb05Locator")
}

func (m *Cb05Locator) stop() {
	GpsService.StopService("Cb05Locator")
	m.goWorkSignal <- 1
	close(m.goWorkSignal)
}

func (m *Cb05Locator) onLocation(location *LocationData) {
	if m.YN != 1 {
		log.Println("Cb05Locator onLocation, but ignore YN:", m.YN)
		return
	}

	// [指令类型TP[2] = 01入界回岗提示；02=出界离岗报警提示]
	var tp byte
	defer func() {
		m.lastTp = tp
		m.LocationData = location
	}()

	// 计算两个坐标的距离
	cb05Location := &LocationData{
		Lon: m.Cb05.Lon,
		Lat: m.Cb05.Lat,
	}
	distance := location.getSubDistance(cb05Location)

	// 判断当前是回岗或离岗
	if distance > m.Radius {
		tp = 2
	} else {
		tp = 1
	}

	// 持续在岗则不上报
	if m.lastTp == 1 && tp == 1 {
		return
	}

	// 离岗后回岗
	if m.lastTp == 2 && tp == 1 {
		m.goWorkSignal <- 1
		GlobalApp.Dic++
		GlobalApp.DevStatus.SetSentryMonitoringAlarm(0)
		_ = GlobalApp.SendBc05(GlobalApp.Dic, m.Cb05, tp, location)
		return
	}

	// 按轮询时间监控上报
	if m.outWorkMonitoring {
		return
	}
	go m.runMonitoringTask()
}

type Cb06Locator struct {
	// 岗哨监控参数
	*bfdx_proto.Cb06
	// 监控的半径
	Radius int

	*LocationData
	ticker *time.Ticker
	// tp==1: 开始走动提示。tp==2: 停留报警提示
	lastTp byte
	// 回岗信号
	goWorkSignal chan byte
	// 离岗监牢中
	isMonitoring bool
}

func (m *Cb06Locator) runMonitoringTask() {
	defer func() {
		m.isMonitoring = false
		m.ticker.Stop()
	}()

	m.isMonitoring = true
	m.ticker = time.NewTicker(time.Duration(m.Time) * time.Minute)

	for {
		select {
		// 按指定的时间间隔来上报
		case <-m.ticker.C:
			// 已经开始走动，上报过了
			if m.lastTp == 1 {
				return
			}

			// 未定位，使用上一次定位数据
			if m.LocationData == nil {
				location := &LocationData{
					GpsTime: time.Now().UTC(),
				}
				if GlobalApp.LastKnownLocation != nil {
					location.fromAppGps84(GlobalApp.LastKnownLocation)
				}
				m.LocationData = location
			}

			// 上报离岗报警
			GlobalApp.DevStatus.SetMobileMonitoringAlarm(1)
			GlobalApp.Dic++
			_ = GlobalApp.SendBc06(GlobalApp.Dic, m.Cb06, m.lastTp, m.LocationData)

		case <-m.goWorkSignal:
			return
		}
	}
}

func (m *Cb06Locator) start() {
	m.goWorkSignal = make(chan byte)
	GpsService.StartService("Cb06Locator")
}

func (m *Cb06Locator) stop() {
	GpsService.StopService("Cb06Locator")
	m.goWorkSignal <- 1
	close(m.goWorkSignal)
}

func (m *Cb06Locator) onLocation(location *LocationData) {
	if m.YN != 1 {
		log.Println("Cb06Locator onLocation, but ignore YN:", m.YN)
		return
	}

	// tp==1: 开始走动提示。tp==2: 停留报警提示
	var tp byte
	defer func() {
		m.lastTp = tp
		m.LocationData = location
	}()

	// 第一次定位，无法比较
	if m.LocationData == nil {
		return
	}

	lastLocation := m.LocationData
	distance := location.getSubDistance(lastLocation)
	if distance > m.Radius {
		tp = 1
	} else {
		tp = 2
	}

	// 持续走动则不上报
	if m.lastTp == 1 && tp == 1 {
		return
	}

	// 停留报警后重新开始走动
	if m.lastTp == 2 && tp == 1 {
		m.goWorkSignal <- 1
		GlobalApp.DevStatus.SetMobileMonitoringAlarm(0)
		GlobalApp.Dic++
		_ = GlobalApp.SendBc06(GlobalApp.Dic, m.Cb06, m.lastTp, location)
		return
	}

	// 按轮询时间监控上报
	if m.isMonitoring {
		return
	}
	go m.runMonitoringTask()
}

type Bc18Locator struct {
	// 报警监控参数
	DwTime int32
	timer  *time.Timer
}

func (m *Bc18Locator) runMonitoringTask() {
	m.timer = time.NewTimer(time.Duration(m.DwTime) * time.Second)

	for {
		select {
		case <-m.timer.C:
			// 定位监控时间到，停止
			m.stop()
			return

		case <-GlobalApp.Cb10Signal:
			// CB10解除报警信号，停止
			m.stop()
			return
		}
	}
}

func (m *Bc18Locator) start() {
	GpsService.StartService("Bc18Locator")
	go m.runMonitoringTask()
}

func (m *Bc18Locator) stop() {
	GpsService.StopService("Bc18Monitor")
	GlobalApp.Bc18Locator = nil
}

func (m *Bc18Locator) onLocation(location *LocationData) {
	GlobalApp.sendBc01(location, true)
}
