package goproxy

import (
	"testing"
	"time"
)

func Test_locationData_getSubDistance(t *testing.T) {
	type fields struct {
		updateTime time.Time
		lon        float64
		lat        float64
	}
	type args struct {
		another *LocationData
	}
	tests := []struct {
		name         string
		fields       fields
		args         args
		wantDistance int
	}{
		{
			name: "t1",
			fields: fields{
				updateTime: time.Now(),
				lon:        113.2953035,
				lat:        23.1463263,
			},
			args: args{
				another: &LocationData{
					GpsTime: time.Now(),
					Lon:     113.2953035,
					Lat:     23.1463263,
				},
			},
			wantDistance: 0,
		},
		{
			name: "t2",
			fields: fields{
				updateTime: time.Now(),
				lon:        113.2953035,
				lat:        23.1463263,
			},
			args: args{
				another: &LocationData{
					GpsTime: time.Now(),
					Lon:     113.2952668,
					Lat:     23.1462938,
				},
			},
			wantDistance: 5,
		},
		{
			name: "t3",
			fields: fields{
				updateTime: time.Now(),
				lon:        113.2953035,
				lat:        23.1463263,
			},
			args: args{
				another: &LocationData{
					GpsTime: time.Now(),
					Lon:     113.2952682,
					Lat:     23.1462905,
				},
			},
			wantDistance: 5,
		},
		{
			name: "t3",
			fields: fields{
				updateTime: time.Now(),
				lon:        113.2953035,
				lat:        23.1463263,
			},
			args: args{
				another: &LocationData{
					GpsTime: time.Now(),
					Lon:     113.2252682,
					Lat:     23.1162905,
				},
			},
			wantDistance: 7897,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &LocationData{
				GpsTime: tt.fields.updateTime,
				Lon:     tt.fields.lon,
				Lat:     tt.fields.lat,
			}
			if gotDistance := m.getSubDistance(tt.args.another); gotDistance != tt.wantDistance {
				t.Errorf("getSubDistance() = %v, want %v", gotDistance, tt.wantDistance)
			}
		})
	}
}
