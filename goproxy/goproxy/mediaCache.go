package goproxy

import (
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"
	"unsafe"

	"goproxy/app_proto"
	"goproxy/bfkcp"

	"go.uber.org/atomic"
)

//string->*MediaPackageItem
//go:generate syncmap -name TMediaPackageItem -pkg goproxy "map[string]*MediaPackageItem"

var invalidTimeStampStr = ""

type MediaPackageItem struct {
	//speak time
	packageId         string
	speaker, repeater uint32
	//data list
	sounds     [][]byte
	isDecoding bool

	currentCodec int32
}

func (m *MediaPackageItem) GotBc10(bc10 *bfkcp.Bc10) error {
	if m.speaker != bc10.SourceDmrid {
		return errors.New("MediaCache,Got bc10 but current media is valid.the speaker of source is not equal! maybe lose bc15-end of front media package and lose bc15-start of this media package")
	}

	if m.currentCodec == -1 {
		m.currentCodec = 1
	}

	m.sounds = append(m.sounds, bc10.OpusData_1)
	if len(bc10.OpusData_2) > 0 {
		m.sounds = append(m.sounds, bc10.OpusData_2)
	}
	return nil
}

func (m *MediaPackageItem) GotBc30(bc30 *bfkcp.Bc30) error {
	if m.repeater != bc30.RepeaterDmrid || m.speaker != bc30.SourceDmrid {
		return errors.New("MediaCache,Got bc30 but current media is valid.the speaker of source is not equal! maybe lose bc15-end of front media package and lose bc15-start of this media package")
	}

	if m.currentCodec == -1 {
		m.currentCodec = 0
	}

	m.sounds = append(m.sounds, bc30.AmbeData)
	return nil
}

func (m *MediaPackageItem) StartDecodeMediaData(cb func(pcm []byte)) {
	m.isDecoding = true
	for _, sound := range m.sounds {
		if !m.isDecoding {
			break
		}

		if m.currentCodec == 1 {
			pcm960 := make([]int16, 480)
			l := OpusCodecDecode(sound, pcm960)
			if l <= 0 {
				continue
			}
			cb(Int16s2Bytes(pcm960[:l]))
		} else {
			if len(sound) != 27 {
				continue
			}
			pcm960 := make([]byte, 960)
			AsacDecode60ms(unsafe.Pointer(&sound[0]), unsafe.Pointer(&pcm960[0]), 0, int16(Gain))
			cb(pcm960)
		}
	}
	m.isDecoding = false
}

func (m *MediaPackageItem) StopDecodeMediaData() {
	m.isDecoding = false
}

type MediaCache struct {
	//"repeater-source-timeFormat"->MediaPackageItem
	mediaPackageMap TMediaPackageItem

	currentStoreMediaPackageId   string
	currentPlayingMediaPackageId string

	mediaPlayerBuffer []byte

	currentPlayMediaPackage *MediaPackageItem

	isPlaying    *atomic.Bool
	timeoutTimes *atomic.Int32

	playSid        *atomic.Int32
	lastSampleTime atomic.Value

	currentMediaSoftware int32

	sync.Mutex
}

func (m *MediaCache) _onSamples(pOutputSample, pInputSamples []byte, needCount uint32) (validLen uint32) {
	m.lastSampleTime.Store(time.Now())
	_ = pInputSamples
	needLen := int(needCount * 2)

	if m.timeoutTimes.Load() > 10 {
		go m.StopPlayer()
		return
	}

	if len(m.mediaPlayerBuffer) < needLen {
		m.timeoutTimes.Inc()
		return
	}

	if m.timeoutTimes.Load() != 0 {
		m.timeoutTimes.Store(0)
	}

	m.Lock()
	copy(pOutputSample, m.mediaPlayerBuffer[0:needLen])
	m.mediaPlayerBuffer = m.mediaPlayerBuffer[needLen:]
	m.Unlock()
	validLen = uint32(needLen)
	return
}

func (m *MediaCache) onSamples(pOutputSample, pInputSamples []byte, needCount uint32) (sampleCount uint32) {
	m.lastSampleTime.Store(time.Now())
	_ = pInputSamples
	needLen := int(needCount * 2)

	if m.timeoutTimes.Load() > 10 {
		go m.StopPlayer()
		return
	}

	if len(m.mediaPlayerBuffer) < needLen {
		m.timeoutTimes.Inc()
		return
	}

	if m.timeoutTimes.Load() != 0 {
		m.timeoutTimes.Store(0)
	}

	m.Lock()
	copy(pOutputSample, m.mediaPlayerBuffer[0:needLen])
	m.mediaPlayerBuffer = m.mediaPlayerBuffer[needLen:]
	sampleCount = needCount
	m.Unlock()
	return
}

// time.Now().Unix()
// CCCCCCCC-MMMMMMMM-HHMMSSddmmyy.ambe
func (m *MediaCache) genMediaCacheId(bc15 *bfkcp.Bc15) string {
	startTimeUtc := time.Unix(int64(bc15.StartTime), 0).UTC()
	return dmrid2Hex(bc15.RepeaterDmrid) + "-" + dmrid2Hex(bc15.SourceDmrid) + "-" + startTimeUtc.Format("15040502012006")
}

func (m *MediaCache) GotBc15(bc15 *bfkcp.Bc15) {
	// stop player
	m.StopPlayer()

	if bc15.CallStatus == 0 { //stop
		m.currentStoreMediaPackageId = invalidTimeStampStr
		return
	}

	//start
	m.currentStoreMediaPackageId = m.genMediaCacheId(bc15)
	mediaPackageBeginner := &MediaPackageItem{
		packageId:    m.currentStoreMediaPackageId,
		speaker:      bc15.SourceDmrid,
		repeater:     bc15.RepeaterDmrid,
		sounds:       make([][]byte, 0),
		currentCodec: -1,
	}
	m.mediaPackageMap.Store(m.currentStoreMediaPackageId, mediaPackageBeginner)

	//test code
	//go func() {
	//	time.Sleep(10 * time.Second)
	//	m.PlayMediaDataByTimeStamp(m.genMediaCacheId(bc15))
	//	time.Sleep(3 * time.Second)
	//	m.PlayMediaDataByTimeStamp(m.genMediaCacheId(bc15))
	//
	//	time.Sleep(10 * time.Second)
	//	//good
	//	m.PlayMediaDataByTimeStamp("000884DE-000AD603-08360209062021")
	//
	//	time.Sleep(10 * time.Second)
	//	//bad
	//	m.PlayMediaDataByTimeStamp("000938B2-000938B2-0732290862021")
	//}()
}

func (m *MediaCache) GotBc10(bc10 *bfkcp.Bc10) {
	// stop player
	m.StopPlayer()

	if m.currentStoreMediaPackageId == invalidTimeStampStr {
		log.Println("MediaCache,Got bc10 but current media time stamp is valid,maybe not got bc15 start before bc10")
		return
	}
	currentMediaPackage, ok := m.mediaPackageMap.Load(m.currentStoreMediaPackageId)
	if !ok { //process exception
		log.Println("******** MediaCache,Got bc10 but can not find the currentMediaPackage by currentStoreMediaPackageId.Exception")
		log.Println(debug.Stack())
		return
	}

	err := currentMediaPackage.GotBc10(bc10)
	if err != nil {
		log.Println(err.Error())
		m.currentStoreMediaPackageId = invalidTimeStampStr
	}
}

func (m *MediaCache) GotBc30(bc30 *bfkcp.Bc30) {
	// stop player
	m.StopPlayer()

	if m.currentStoreMediaPackageId == invalidTimeStampStr {
		//not got bc15 start but got bc30
		//do not cache these bc30
		log.Println("MediaCache,Got bc30 but current media time stamp is valid,maybe not got bc15 start before bc30")
		return
	}
	currentMediaPackage, ok := m.mediaPackageMap.Load(m.currentStoreMediaPackageId)
	if !ok { //process exception
		log.Println("******** MediaCache,Got bc30 but can not find the currentMediaPackage by currentStoreMediaPackageId.Exception")
		log.Println(debug.Stack())
		return
	}

	err := currentMediaPackage.GotBc30(bc30)
	if err != nil {
		log.Println(err.Error())
		m.currentStoreMediaPackageId = invalidTimeStampStr
	}
}

func (m *MediaCache) GetPlaySid() int32 {
	return m.playSid.Load()
}

func (m *MediaCache) IsPlaying() bool {
	return m.isPlaying.Load()
}

func (m *MediaCache) GetLastSampleTime() time.Time {
	return m.lastSampleTime.Load().(time.Time)
}

func (m *MediaCache) StartPlayer() {
	if GlobalMediaManager.mediaPlayer.isPlaying.Load() {
		return
	}

	if m.isPlaying.Load() {
		log.Println("media cache Start player. the dev is already start")
		return
	}

	m.currentMediaSoftware = GlobalMediaManager.nextMediaSoftware
	if runtime.GOOS == "android" && m.currentMediaSoftware == SDKMedia {
		PlayerStart()
	} else {
		go PortAudioStartPlay(m.onSamples, m.IsPlaying)
		//isOk := false
		//m.dev, isOk = GlobalMediaManager.StartDev(m.onSamples)
		//if !isOk {
		//	return
		//}
	}

	m.isPlaying.Store(true)

	lastPlaySid := m.playSid.Inc()
	m.lastSampleTime.Store(time.Now())
	go checkIfStopPlayer(lastPlaySid, m.GetPlaySid, m.IsPlaying, m.GetLastSampleTime, m.StopPlayer, func() {
		GlobalApp.PressNotify(&app_proto.Notify{Code: 12, ParamStr: m.currentPlayingMediaPackageId})
	})
}

func (m *MediaCache) StopPlayer() {
	if !m.isPlaying.Load() {
		return
	}
	//notify client that cache media player stop
	GlobalApp.PressNotify(&app_proto.Notify{Code: 7})

	if runtime.GOOS == "android" && m.currentMediaSoftware == SDKMedia {
		PlayerStop()
		//} else {
		//if m.dev != nil {
		//	m.dev.Uninit()
		//	m.dev = nil
		//	log.Println("ReleaseMediaPlayer Success", string(debug.Stack()))
		//} else {
		//	log.Println("ReleaseMediaPlayer already released")
		//}
	}

	m.isPlaying.Store(false)
}

func (m *MediaCache) AppendPCMDataIntoBuffer(pcm []byte) {
	m.Lock()
	m.mediaPlayerBuffer = append(m.mediaPlayerBuffer, pcm...)
	m.Unlock()
}

func parseMediaCacheId(mediaCacheId string) (bc15 *bfkcp.Bc15, err error) {
	bc15Attrs := strings.Split(mediaCacheId, "-")
	if len(bc15Attrs) != 3 {
		err = errors.New("parseMediaCacheId invalid mediaCacheId,strings.Split but got invalid structure")
		return
	}

	if len(bc15Attrs[0]) != 8 || len(bc15Attrs[1]) != 8 {
		err = errors.New("parseMediaCacheId invalid mediaCacheId,invalid dmrid")
		return
	}

	bc15 = &bfkcp.Bc15{
		RepeaterDmrid: hexDmrid2Uint32(bc15Attrs[0]),
		SourceDmrid:   hexDmrid2Uint32(bc15Attrs[1]),
	}
	return
}

func (m *MediaCache) QueryMediaAmbeDataByMediaCacheId(mediaCacheId string) (mediaPackageItem *MediaPackageItem, err error) {
	bc15, err := parseMediaCacheId(mediaCacheId)
	if err != nil {
		log.Println("QueryMediaAmbeDataByMediaCacheId Got invalid mediaCacheId", err, string(debug.Stack()))
		return
	}

	client := http.Client{
		Timeout: 15 * time.Second,
	}

	url := "http://" + kcpInstance.ServerHost + ":" + strconv.Itoa(int(kcpInstance.ServerPort)) + "/download_sound?sys=" + extractSysIdFromDmrid(bc15.SourceDmrid) + "&filename=" + mediaCacheId + ".ambe"
	log.Println("QueryMediaAmbeDataByMediaCacheId http url", url)
	resp, err := client.Get(url)
	if err != nil {
		log.Println("QueryMediaAmbeDataByMediaCacheId http get err", err, " ,url: ", url)
		return
	}

	if resp.StatusCode != 200 {
		err = errors.New("QueryMediaAmbeDataByMediaCacheId http get err ")
		return
	}

	mediaPackageItem = &MediaPackageItem{
		packageId:    mediaCacheId,
		speaker:      bc15.SourceDmrid,
		repeater:     bc15.RepeaterDmrid,
		sounds:       make([][]byte, 0),
		isDecoding:   false,
		currentCodec: -1,
	}

	ambeData, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}
	if len(ambeData) > 27 {
		for {
			mediaPackageItem.sounds = append(mediaPackageItem.sounds, ambeData[:27])
			ambeData = ambeData[27:]
			if len(ambeData) < 27 {
				break
			}
		}
	}

	if len(mediaPackageItem.sounds) < 3 {
		errStr := fmt.Sprintf("QueryMediaAmbeDataByMediaCacheId http get err.invalid ambe27 data len,url:%s", url)
		log.Println(errStr)
		err = errors.New(errStr)
		return
	}
	m.mediaPackageMap.Store(mediaCacheId, mediaPackageItem)
	return
}

func (m *MediaCache) StartPlay(mediaPackageItem *MediaPackageItem) {
	if mediaPackageItem == nil {
		log.Println("=========== Err StartPlay mediaPackageItem is nil", string(debug.Stack()))
		return
	}
	m.Lock()
	m.mediaPlayerBuffer = make([]byte, 0)
	m.Unlock()

	m.currentPlayMediaPackage = mediaPackageItem

	go m.currentPlayMediaPackage.StartDecodeMediaData(m.AppendPCMDataIntoBuffer)

	m.timeoutTimes.Store(0)
	m.StartPlayer()
	log.Println("MediaCache StartPlay")
	GlobalApp.PressNotify(&app_proto.Notify{Code: 6, ParamStr: m.currentPlayingMediaPackageId})
}

func (m *MediaCache) PlayMediaDataByTimeStamp(mediaCacheId string, respCb func(errCode int32)) {
	errCode := int32(0)

	//即将播放
	m.currentPlayingMediaPackageId = mediaCacheId

	log.Println("PlayMediaDataByTimeStamp", mediaCacheId)

	if m.currentPlayMediaPackage != nil {
		m.currentPlayMediaPackage.StopDecodeMediaData()
	}
	mediaPackageItem, ok := m.mediaPackageMap.Load(mediaCacheId)
	if !ok {
		errCode = 1
		respCb(errCode)

		go func(_mediaCacheId string) {
			_mediaPackageItem, err2 := m.QueryMediaAmbeDataByMediaCacheId(mediaCacheId)
			if err2 != nil {
				log.Println("PlayMediaDataByTimeStamp  QueryMediaAmbeDataByMediaCacheId err2", err2)
				GlobalApp.PressNotify(&app_proto.Notify{Code: 9})
				return
			}
			// check if need play
			if m.currentPlayingMediaPackageId == _mediaCacheId {
				m.StartPlay(_mediaPackageItem)
			}
		}(mediaCacheId)

		return
	}

	respCb(errCode)

	m.StartPlay(mediaPackageItem)
}
