package goproxy

import (
	"go.uber.org/atomic"
	"sync"
	"testing"
)

func TestMediaCache_QueryMediaAmbeDataByMediaCacheId(t *testing.T) {
	type fields struct {
		mediaPackageMap            TMediaPackageItem
		currentStoreMediaPackageId string
		mediaPlayerBuffer          []byte
		currentPlayMediaPackage    *MediaPackageItem
		isPlaying                  *atomic.Bool
		timeoutTimes               *atomic.Int32
		playSid                    *atomic.Int32
		lastSampleTime             atomic.Value
		Mutex                      sync.Mutex
	}

	f := fields{
		mediaPackageMap:            TMediaPackageItem{},
		currentStoreMediaPackageId: "",
		mediaPlayerBuffer:          make([]byte, 0),
		currentPlayMediaPackage:    nil,
		isPlaying:                  atomic.NewBool(false),
		timeoutTimes:               atomic.NewInt32(0),
		playSid:                    nil,
		lastSampleTime:             atomic.Value{},
		Mutex:                      sync.Mutex{},
	}

	type args struct {
		mediaCacheId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "t1",
			fields:  f,
			args:    args{mediaCacheId: "000938B2-000938B2-07322908062021"},
			wantErr: false,
		},
		{
			name:    "t2",
			fields:  f,
			args:    args{mediaCacheId: "0009382-000938B2-07322908062021"},
			wantErr: true,
		},
		{
			name:    "t3",
			fields:  f,
			args:    args{mediaCacheId: "000938B2-00093B2-07322908062021"},
			wantErr: true,
		},
		{
			name:    "t4",
			fields:  f,
			args:    args{mediaCacheId: "000938B2-000938B2-0732290862021"},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MediaCache{
				mediaPackageMap:            tt.fields.mediaPackageMap,
				currentStoreMediaPackageId: tt.fields.currentStoreMediaPackageId,
				mediaPlayerBuffer:          tt.fields.mediaPlayerBuffer,
				currentPlayMediaPackage:    tt.fields.currentPlayMediaPackage,
				isPlaying:                  tt.fields.isPlaying,
				timeoutTimes:               tt.fields.timeoutTimes,
				playSid:                    tt.fields.playSid,
				lastSampleTime:             tt.fields.lastSampleTime,
				Mutex:                      tt.fields.Mutex,
			}
			if err, _ := m.QueryMediaAmbeDataByMediaCacheId(tt.args.mediaCacheId); (err != nil) != tt.wantErr {
				t.Errorf("QueryMediaAmbeDataByMediaCacheId() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
