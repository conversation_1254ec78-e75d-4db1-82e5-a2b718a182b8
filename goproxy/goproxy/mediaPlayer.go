package goproxy

import (
	"log"
	"reflect"
	"runtime"
	"sync"
	"time"
	"unsafe"

	"go.uber.org/atomic"
	"goproxy/app_proto"
	"goproxy/bfkcp"
)

const InValidSpeakerDmrid = 0x11111111
const DefaultSpeakerPriority = 0

const InputTarget2Next = 1
const InputTarget2Current = 2

const PlayerStopped = 0
const PlayerWillBeClosed = 1
const PlayerWillBeRestart = 2
const PlayerNormal = 3

type MediaPlayer struct {
	isPhoneCallIn *atomic.Bool

	//1.will be closed 2.will be restart
	playerStatus int

	//current pcm stream input repeater
	//1.nextPlayBuffer 2.currentPlayBuffer
	inputTarget int

	nextSpeaker         uint32
	nextSpeakerPriority int32
	nextSpeaker_target  uint32
	//need init when create MediaPlayer obj
	nextPlayBuffer []byte

	currentSpeaker         uint32
	currentSpeakerPriority int32
	currentSpeaker_target  uint32
	//need init when create MediaPlayer obj
	currentPlayBuffer []byte

	//the onSamples had been called,but got no data
	emptyTimes *atomic.Int32

	isPlaying *atomic.Bool

	playSessionId *atomic.Int32

	lastOnSampleTime atomic.Value

	lastReleaseTime atomic.Value

	currentMediaSoftware int32

	//lock for buffer opt
	sync.Mutex
}

func (m *MediaPlayer) IsPlaying() bool {
	return m.isPlaying.Load()
}

func (m *MediaPlayer) gotSpeaker() (currentSpeaker, currentSpeaker_target uint32) {
	return m.currentSpeaker, m.currentSpeaker_target
}

// This is the function that's used for sending more data to the device for playback.
func (m *MediaPlayer) _onSamples(pOutputSample, pInputSamples []byte, needCount uint32) (validLen uint32) {
	m.lastOnSampleTime.Store(time.Now())
	_ = pInputSamples
	needLen := needCount * 2

	if len(m.currentPlayBuffer) == 0 {
		m.onPlayBuffNotGotData()
		//copy(pOutputSample, make([]byte,needLen))
		return
	}
	m.Lock()
	if int(needLen) > len(m.currentPlayBuffer) {
		validLen = uint32(len(m.currentPlayBuffer))
		copy(pOutputSample, m.currentPlayBuffer)
		m.currentPlayBuffer = make([]byte, 0)
	} else {
		validLen = needLen
		copy(pOutputSample, m.currentPlayBuffer[0:needLen])
		m.currentPlayBuffer = m.currentPlayBuffer[needLen:]
	}
	//log.Println("onSamples currentPlayBuffer len ", len(m.currentPlayBuffer), len(pOutputSample), pOutputSample)
	m.Unlock()

	m.onPlayBuffGotData()
	return
}

// This is the function that's used for sending more data to the device for playback.
func (m *MediaPlayer) onSamples(pOutputSample, pInputSamples []byte, needCount uint32) (sampleCount uint32) {
	m.lastOnSampleTime.Store(time.Now())
	_ = pInputSamples
	needLen := needCount * 2

	if len(m.currentPlayBuffer) == 0 {
		m.onPlayBuffNotGotData()
		//copy(pOutputSample, make([]byte,needLen))
		return
	}
	m.Lock()
	if int(needLen) > len(m.currentPlayBuffer) {
		copy(pOutputSample, m.currentPlayBuffer)
		sampleCount = uint32(len(m.currentPlayBuffer) / 2)

		m.currentPlayBuffer = make([]byte, 0)
	} else {
		copy(pOutputSample, m.currentPlayBuffer[0:needLen])
		m.currentPlayBuffer = m.currentPlayBuffer[needLen:]
		sampleCount = needCount
	}
	//log.Println("onSamples currentPlayBuffer len ", len(m.currentPlayBuffer), len(pOutputSample), pOutputSample)
	m.Unlock()

	m.onPlayBuffGotData()

	return
}

var PlaySid int32

func (m *MediaPlayer) setCallBackTarget(src, target string) {
	PlaySid = m.playSessionId.Load()
	GlobalApp.SetCallBackSpeakTarget(src, target)
}

func (m *MediaPlayer) clearCallBackTarget(src, target string) {
	stopSid := m.playSessionId.Load()
	time.Sleep(5 * time.Second)
	if stopSid != PlaySid {
		return
	}
	GlobalApp.TryClearCallBackSpeakTargetAndNotifyClient(src, target)
}

func (m *MediaPlayer) processSpeakStatus(stop_speaker, stop_speaker_target, start_speaker, start_speaker_target uint32, is_stop, is_start bool) {
	log.Println("processSpeakStatus stop", stop_speaker, stop_speaker_target, is_stop)
	log.Println("processSpeakStatus start", start_speaker, start_speaker_target, is_start)
	if is_stop {
		if stop_speaker == InValidSpeakerDmrid {
			return
		}
		speakTarget := stop_speaker
		if checkIsGroupDmrid(stop_speaker_target) {
			speakTarget = stop_speaker_target
		}
		go m.clearCallBackTarget(dmrid2Hex(stop_speaker), dmrid2Hex(speakTarget))
		//process cb 15 stop
		bc15 := &bfkcp.Bc15{
			SourceDmrid: stop_speaker,
			TargetDmrid: stop_speaker_target,
			CallStatus:  0,
		}

		bytes, err := bc15.Marshal()
		if err != nil {
			log.Println("processSpeakStatus bc15.Marshal err", err)
			return
		}

		resp_cmd := &bfkcp.RpcCmd{
			Cmd:  int32(app_proto.CmdCode_cmd_resp_media_play_status),
			Res:  int32(app_proto.MediaStatus_stoped),
			Body: bytes,
		}

		resp_cmd.ParaStr = dmrid2Hex(stop_speaker) + "-" + dmrid2Hex(stop_speaker_target)

		_, _ = GlobalApp.Write(resp_cmd)
	}

	if is_start {
		if start_speaker == InValidSpeakerDmrid {
			return
		}
		//process cb15 start
		bc15 := &bfkcp.Bc15{
			SourceDmrid: start_speaker,
			TargetDmrid: start_speaker_target,
			CallStatus:  1,
		}

		bytes, err := bc15.Marshal()
		if err != nil {
			log.Println("processSpeakStatus bc15.Marshal err", err)
			return
		}

		resp_cmd := &bfkcp.RpcCmd{
			Cmd:  int32(app_proto.CmdCode_cmd_resp_media_play_status),
			Res:  int32(app_proto.MediaStatus_start),
			Body: bytes,
		}

		resp_cmd.ParaStr = dmrid2Hex(start_speaker) + "-" + dmrid2Hex(start_speaker_target)

		_, _ = GlobalApp.Write(resp_cmd)

		speakTarget := start_speaker
		if checkIsGroupDmrid(start_speaker_target) {
			speakTarget = start_speaker_target
		}

		m.setCallBackTarget(dmrid2Hex(start_speaker), dmrid2Hex(speakTarget))
	}
}

func (m *MediaPlayer) clearPlayBuffer() {
	m.playerStatus = PlayerStopped
	m.inputTarget = InputTarget2Current

	m.nextSpeaker = InValidSpeakerDmrid
	m.nextSpeakerPriority = DefaultSpeakerPriority
	m.nextSpeaker_target = InValidSpeakerDmrid
	m.currentSpeaker = InValidSpeakerDmrid
	m.currentSpeakerPriority = DefaultSpeakerPriority
	m.currentSpeaker_target = InValidSpeakerDmrid

	m.nextPlayBuffer = make([]byte, 0)
	m.currentPlayBuffer = make([]byte, 0)
}

func (m *MediaPlayer) checkoutNextPlayBuf() {
	m.processSpeakStatus(m.currentSpeaker, m.currentSpeaker_target, m.nextSpeaker, m.nextSpeaker_target, true, true)

	m.currentSpeaker = m.nextSpeaker
	m.currentSpeakerPriority = m.nextSpeakerPriority
	m.currentSpeaker_target = m.nextSpeaker_target

	m.nextSpeaker = InValidSpeakerDmrid
	m.nextSpeakerPriority = DefaultSpeakerPriority
	m.nextSpeaker_target = InValidSpeakerDmrid

	m.currentPlayBuffer = m.nextPlayBuffer
	m.nextPlayBuffer = make([]byte, 0)

	m.inputTarget = InputTarget2Current

	m.emptyTimes.Store(0)

	m.playerStatus = PlayerNormal
}

func (m *MediaPlayer) onPlayBuffNotGotData() {
	m.emptyTimes.Inc()
	log.Println("onPlayBuffNotGotData,m.emptyTimes:", m.emptyTimes.Load())
	if m.playerStatus == PlayerWillBeClosed {
		//stop player
		go m.ReleaseMediaPlayer()
		return
	}

	if m.playerStatus == PlayerWillBeRestart || m.inputTarget == InputTarget2Next {
		m.checkoutNextPlayBuf()
		return
	}

	if m.playerStatus == PlayerNormal || m.inputTarget == InputTarget2Current {
		//lose cb15 end
		if m.emptyTimes.Load() > 30 {
			//time out,but not have next speaker
			go m.ReleaseMediaPlayer()
			m.emptyTimes.Store(0)
			return
		}
	}
}

func (m *MediaPlayer) onPlayBuffGotData() {
	if m.emptyTimes.Load() != 0 {
		m.emptyTimes.Store(0)
	}
}

func (m *MediaPlayer) PrintBuffStatus() {
	log.Println("currentPlayBuffer,len:", len(m.currentPlayBuffer), " speaker:", dmrid2Hex(m.currentSpeaker), " repeater:", dmrid2Hex(m.currentSpeaker_target))
	log.Println("nextPlayBuffer,len:", len(m.nextPlayBuffer), " speaker:", dmrid2Hex(m.nextSpeaker), " repeater:", dmrid2Hex(m.nextSpeaker_target))
}

func (m *MediaPlayer) UpdateNotifySpeakInfo(isDefault bool) {
	if isDefault || InValidSpeakerDmrid == m.currentSpeaker || InValidSpeakerDmrid == m.currentSpeaker_target {
		updateContent([]byte(""))
	} else {
		updateContent([]byte(GlobalApp.QueryUserNameInAddrBookList(dmrid2Hex(m.currentSpeaker)) + " => " +
			GlobalApp.QueryUserNameInAddrBookList(dmrid2Hex(m.currentSpeaker_target))))
	}
}

func (m *MediaPlayer) GetPlaySid() int32 {
	return m.playSessionId.Load()
}

func (m *MediaPlayer) GetLastSampleTime() time.Time {
	return m.lastOnSampleTime.Load().(time.Time)
}

func (m *MediaPlayer) InitMediaPlayer() {
	m.lastReleaseTime.Store(time.Date(2000, 1, 1, 1, 1, 1, 1, time.Local))
	if m.isPhoneCallIn.Load() {
		return
	}
	log.Println("-----------InitMediaPlayer Called------------")
	m.Lock()
	defer m.Unlock()
	if m.isPlaying.Load() {
		return
	}
	m.PrintBuffStatus()
	mediaStart()

	//var ok bool
	//m.dev, ok = GlobalMediaManager.StartDev(m.onSamples)
	//if !ok {
	//	return
	//}
	m.currentMediaSoftware = GlobalMediaManager.nextMediaSoftware
	if runtime.GOOS == "android" && m.currentMediaSoftware == SDKMedia {
		PlayerStart()
	} else {
		go PortAudioStartPlay(m.onSamples, m.IsPlaying)
	}

	m.isPlaying.Store(true)

	m.UpdateNotifySpeakInfo(false)

	log.Println("StartPlayer Success")

	lastPlaySid := m.playSessionId.Inc()
	m.lastOnSampleTime.Store(time.Now())
	m.processSpeakStatus(0, 0, m.currentSpeaker, m.currentSpeaker_target, false, true)
	go checkIfStopPlayer(lastPlaySid, m.GetPlaySid, m.IsPlaying, m.GetLastSampleTime, m.ReleaseMediaPlayer, func() {
		GlobalApp.PressNotify(&app_proto.Notify{Code: 11, ParamStr: dmrid2Hex(m.currentSpeaker) + "-" + dmrid2Hex(m.currentSpeaker_target)})
	})
}

func (m *MediaPlayer) InitStatus() {
	if m.isPhoneCallIn.Load() {
		return
	}
	//log.Println("MediaPlayer InitStatus Called", string(debug.Stack()))
	m.clearPlayBuffer()
}

func (m *MediaPlayer) ReleaseMediaPlayer() {
	lastReleaseTime := m.lastReleaseTime.Load().(time.Time)
	log.Println("call ReleaseMediaPlayer") //, string(debug.Stack()))

	if !m.isPlaying.Load() {
		log.Println("call ReleaseMediaPlayer not in playing") //, string(debug.Stack()))
		//return
	}

	if time.Since(lastReleaseTime) < 1*time.Second {
		return
	}
	m.lastReleaseTime.Store(time.Now())

	m.Lock()
	defer m.Unlock()

	m.isPlaying.Store(false)

	//if m.dev != nil {
	//	m.dev.Uninit()
	//	m.dev = nil
	//	log.Println("ReleaseMediaPlayer Success")
	//} else {
	//	log.Println("ReleaseMediaPlayer already released")
	//}

	if runtime.GOOS == "android" && m.currentMediaSoftware == SDKMedia {
		PlayerStop()
		//} else {
		//if m.dev != nil {
		//	m.dev.Uninit()
		//	m.dev = nil
		//	log.Println("ReleaseMediaPlayer Success")
		//} else {
		//	log.Println("ReleaseMediaPlayer already released")
		//}
	}

	//_ = m.ctx.Uninit()
	//m.ctx.Free()

	m.PrintBuffStatus()
	m.processSpeakStatus(m.currentSpeaker, m.currentSpeaker_target, 0, 0, true, false)
	m.UpdateNotifySpeakInfo(true)

	m.InitStatus()
}

// called byGotPcm60ms
// 1.got bc15 end
// 2.got phone idle
func (m *MediaPlayer) tryResumePlayer() {
	if len(m.currentPlayBuffer) > MinMediaPlaySize*pcm960Size {
		m.InitMediaPlayer()
	} else {
		m.Lock()
		m.InitStatus()
		m.Unlock()
	}
}

func (m *MediaPlayer) GotBc15(bc15 *bfkcp.Bc15) {
	//bc15.SourceDmrid
	if bc15.CallStatus == 0 { //Bc15 stop
		if m.isPlaying.Load() {
			//set status to will be closed
			log.Println("GotBc15 set PlayerWillBeClosed")
			m.playerStatus = PlayerWillBeClosed
		} else {
			m.tryResumePlayer()
		}
		return
	}

	//no one speak
	if m.currentSpeaker == InValidSpeakerDmrid {
		m.playerStatus = PlayerNormal
		m.inputTarget = InputTarget2Current

		m.currentSpeaker = bc15.SourceDmrid
		m.currentSpeakerPriority = bc15.Priority
		m.currentSpeaker_target = bc15.TargetDmrid
		return
	}

	//if other is speaking
	if m.nextSpeaker != InValidSpeakerDmrid {
		//只支持2级缓冲
		return
	} else {
		m.playerStatus = PlayerWillBeRestart
		m.inputTarget = InputTarget2Next
		m.nextSpeaker = bc15.SourceDmrid
		m.nextSpeakerPriority = bc15.Priority
		m.nextSpeaker_target = bc15.TargetDmrid
	}
}

func (m *MediaPlayer) AppendPcm(pcm []byte, target *[]byte) {
	m.Lock()
	defer m.Unlock()

	overflowLen := MediaBufferSize * pcm960Size

	pcmLen := len(pcm)
	targetLen := len(*target)

	if (pcmLen + targetLen) > int(overflowLen) {
		*target = (*target)[(MediaBufferSize/4)*pcm960Size:]
	}
	*target = append(*target, pcm...)
}

func (m *MediaPlayer) GotPcm60ms(_pcm []uint16) {
	//log.Println("GotPcm60ms", m.inputTarget, len(m.currentPlayBuffer), len(m.nextPlayBuffer))
	var pcm []byte
	sliceHeader := (*reflect.SliceHeader)((unsafe.Pointer(&pcm)))
	sliceHeader.Cap = len(_pcm) * 2
	sliceHeader.Len = len(_pcm) * 2
	sliceHeader.Data = uintptr(unsafe.Pointer((&_pcm[0])))

	if m.inputTarget == InputTarget2Current {
		m.AppendPcm(pcm, &m.currentPlayBuffer)
		if m.isPlaying.Load() {
			return
		}
		//超出MediaBufferSize缓冲,自启动
		if len(m.currentPlayBuffer) > int(MediaAutoStartBufferSize*pcm960Size) {
			m.InitMediaPlayer()
		}
	}

	if m.inputTarget == InputTarget2Next {
		m.AppendPcm(pcm, &m.nextPlayBuffer)
		if !m.isPlaying.Load() {
			m.InitMediaPlayer()
		}
	}
}

func (m *MediaPlayer) onPhoneCallOut() {
	if m.isPhoneCallIn.Load() {
		return
	}
	m.isPhoneCallIn.Store(true)
	m.ReleaseMediaPlayer()
}

func (m *MediaPlayer) onPhoneCallIn() {
	if m.isPhoneCallIn.Load() {
		return
	}
	m.isPhoneCallIn.Store(true)
	m.ReleaseMediaPlayer()
}

func (m *MediaPlayer) onPhoneCallIdle() {
	m.isPhoneCallIn.Store(false)

	m.tryResumePlayer()
}
