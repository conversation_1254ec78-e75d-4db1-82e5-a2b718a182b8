package goproxy

import (
	"log"
	"runtime"
	"runtime/debug"
	"sync"
	"time"
	"unsafe"

	"go.uber.org/atomic"
)

type MediaRecorder struct {
	buffer []byte

	//原始的Pcm数据
	rawPcmBuffer    []byte
	_8kRawPcmBuffer []byte
	rawSampleRate   int
	rawLock         sync.Mutex
	_8kRawLock      sync.Mutex

	isRecording *atomic.Bool
	isStopPcm   *atomic.Bool

	//用户调用stop record后 发送剩余的语音数据
	isSendingLeastData *atomic.Bool

	currentMediaSoftware int32

	sync.Mutex
}

func (m *MediaRecorder) processLeastMediaData(pcmLeastData []byte, frameNo uint32) {
	for {
		//send least data as soon as possible;do not need sleep
		if len(pcmLeastData) > pcm960Size {
			m.processOnPack(pcmLeastData[:pcm960Size], frameNo)
			pcmLeastData = pcmLeastData[pcm960Size:]
			frameNo++
		} else {
			m.processOnPack(pcmLeastData[:], frameNo)
			break
		}
	}

	m.isSendingLeastData.Store(false)
	// send bc15 end
	GlobalMediaManager.OnLeastPcmDataSendFinished()
}

// to be promise, _pcm960 <= pcm960Size
func (m *MediaRecorder) processOnPack(pcm960 []byte, frameNo uint32) {
	pcmLen := len(pcm960)

	if pcmLen > pcm960Size {
		log.Println("======== sound record processOnPack invalid pak len =======", pcmLen)
		return
	}

	if pcmLen != pcm960Size {
		if pcmLen < (pcm960Size / 3) {
			//丢弃1/3都不足的语音包
			log.Println("======== sound record processOnPack too min pak len =======", pcmLen)
			return
		}
		//补0
		pcm960 = append(pcm960, make([]byte, pcm960Size-pcmLen)...)
	}
	// push out the pcm data
	GlobalMediaManager.Got960PcmData(pcm960, frameNo)
}

// to be promise,the dev need be stopped before set m.isRecording = false
func (m *MediaRecorder) checkoutOnePackPer60ms() {
	frameNo := uint32(0)

	//pc sleep time
	sleepTimeDuration := 60 * time.Millisecond
	//if runtime.GOOS == "android" {
	//	sleepTimeDuration = 45 * time.Millisecond
	//}

	for m.isRecording.Load() {
		bfLen := len(m.buffer)
		if bfLen > pcm960Size {
			//log.Println("checkoutOnePackPer60ms Lock")
			m.Lock()
			pcm960 := m.buffer[0:pcm960Size]
			m.buffer = m.buffer[pcm960Size:]
			//log.Println("checkoutOnePackPer60ms Unlock")
			m.Unlock()
			m.processOnPack(pcm960, frameNo)
			frameNo++
			if len(m.buffer) >= pcm960Size {
				time.Sleep(10 * time.Millisecond)
			} else {
				time.Sleep(sleepTimeDuration)
			}
		} else {
			//>>4  <=>  *(60/960)
			//log.Println("================== checkoutOnePackPer60ms Can not got pack ======================")
			time.Sleep(100 * time.Millisecond)
			//looseLen := pcm960Size - bfLen
			//if looseLen > 150 {
			//	sleepTime := looseLen >> 4
			//	time.Sleep(time.Duration(sleepTime) * time.Millisecond)
			//} else {
			//	time.Sleep(10 * time.Millisecond)
			//}
		}
	}
	// send least * media
	m.isSendingLeastData.Store(true)
	m.processLeastMediaData(m.buffer[:], frameNo)
}

// This is the function that's used for receiving more data to the device for playback.
func (m *MediaRecorder) onSamples(pOutputSample, pInputSamples []byte, _frameCount uint32) {

	if m.rawSampleRate == 8000 {
		if GlobalMediaManager.DebugRecorderPcm == 1 {
			GlobalApp.BoradcastGotPcm(8000, pInputSamples)
		}

		if GlobalMediaManager.RecorderPcmGain != 100 {
			gain := float64(GlobalMediaManager.RecorderPcmGain) / 100.0
			//need apply pcm gain
			for i := 0; i < len(pInputSamples)-1; i += 2 {
				pInt16 := (*int16)(unsafe.Pointer(&pInputSamples[i]))
				gainValue := float64(*pInt16) * gain
				if GlobalMediaManager.RecorderPcmGain > 100 {
					if gainValue > 32767 {
						gainValue = 32767
					} else if gainValue < -32768 {
						gainValue = -32768
					}
				}
				*pInt16 = int16(gainValue)
			}
		}

		m.on8kSample(pOutputSample, pInputSamples, _frameCount)
	} else {
		//48000 inpc
		if GlobalMediaManager.DebugRecorderPcm == 1 {
			GlobalApp.BoradcastGotPcm(48000, pInputSamples)
		}

		if GlobalMediaManager.RecorderPcmGain != 100 {
			gain := float64(GlobalMediaManager.RecorderPcmGain) / 100.0
			//need apply pcm gain
			for i := 0; i < len(pInputSamples)-1; i += 2 {
				pInt16 := (*int16)(unsafe.Pointer(&pInputSamples[i]))
				gainValue := float64(*pInt16) * gain
				if GlobalMediaManager.RecorderPcmGain > 100 {
					if gainValue > 32767 {
						gainValue = 32767
					} else if gainValue < -32768 {
						gainValue = -32768
					}
				}
				*pInt16 = int16(gainValue)
			}
		}

		m.denoiseInput(pOutputSample, pInputSamples, _frameCount)
	}
}

func (m *MediaRecorder) storeIntoPlayBuf(pOutputSample, pInputSamples []byte, _frameCount uint32) {
	if m.isStopPcm.Load() {
		return
	}
	frameCount := int32(_frameCount) * 2

	//log.Println("storeIntoPlayBuf Lock")
	m.Lock()
	defer func() {
		//log.Println("storeIntoPlayBuf Unlock")
		m.Unlock()
	}()

	bufLen := int32(len(m.buffer))
	if (bufLen + frameCount) > MediaBufferSize*pcm960Size {
		m.buffer = m.buffer[(MediaBufferSize/4)*pcm960Size:]
	}

	m.buffer = append(m.buffer, pInputSamples[:frameCount]...)

	//log.Println("====== record onSamples ======= ", len(m.buffer))
}

func (m *MediaRecorder) on8kSample(pOutputSample, pInputSamples []byte, _frameCount uint32) {
	//m.storeIntoPlayBuf(nil, pInputSamples, uint32(len(pInputSamples)/2))
	if m.isStopPcm.Load() {
		return
	}

	m._8kRawLock.Lock()
	defer func() {
		//log.Println("denoiseInput rawLock-Unlock")
		m._8kRawLock.Unlock()
	}()

	denoiseSettingVal := GlobalMediaManager.GetDenoiseSetting()
	if denoiseSettingVal == 0 || denoiseSettingVal == 1 {
		//no need denoise
		m.storeIntoPlayBuf(nil, pInputSamples, uint32(len(pInputSamples)/2))
		return

	}

	m._8kRawPcmBuffer = append(m._8kRawPcmBuffer, pInputSamples...)

	for len(m._8kRawPcmBuffer) >= 160 {
		_48kSample := make([]byte, 960)
		outPcmLen := Resample8kTo48k(unsafe.Pointer(&m._8kRawPcmBuffer[0]), unsafe.Pointer(&_48kSample[0]))
		//log.Println("-- m.rawPcmBuffer:", outPcmLen, " , ", hex.EncodeToString(_48kSample))

		m.denoiseInput(nil, _48kSample, uint32(outPcmLen))
		//m.storeIntoPlayBuf(nil, _48kSample[0:outPcmLen*2], uint32(outPcmLen))

		m._8kRawPcmBuffer = m._8kRawPcmBuffer[160:]
	}
}

func (m *MediaRecorder) denoiseInput(pOutputSample, pInputSamples []byte, _frameCount uint32) {
	//log.Println("denoiseInput rawLock-Lock")
	m.rawLock.Lock()
	defer func() {
		//log.Println("denoiseInput rawLock-Unlock")
		m.rawLock.Unlock()
	}()

	m.rawPcmBuffer = append(m.rawPcmBuffer, pInputSamples...)

	//log.Println("got 48k sample:",_frameCount)
	if m.isStopPcm.Load() {
		return
	}

	for len(m.rawPcmBuffer) >= 960 {
		outPcmLen := DenoisePcm480Sample(unsafe.Pointer(&m.rawPcmBuffer[0]))
		//log.Println("10ms 8k16bit:",outPcmLen)
		m.storeIntoPlayBuf(nil, m.rawPcmBuffer[0:outPcmLen*2], uint32(outPcmLen))
		m.rawPcmBuffer = m.rawPcmBuffer[960:]
	}
}

/*
RecorderStart()
	isOk = true
	return
*/

func (m *MediaRecorder) StartRecord() (isOk bool) {
	log.Println("StartRecord", " isRecording ", m.isRecording.Load(), " isSendingLeastData ", m.isSendingLeastData.Load())
	log.Println("denoiseSetting:", GlobalMediaManager.denoiseSetting, " ,pcm gain: ", GlobalMediaManager.RecorderPcmGain,
		" debugPcm:", GlobalMediaManager.DebugRecorderPcm)
	//log.Println("StartRecord Lock")
	m.Lock()
	defer func() {
		//log.Println("StartRecord Unlock")
		m.Unlock()
	}()
	if m.isRecording.Load() || m.isSendingLeastData.Load() {
		return
	}

	/*var err error
	if GlobalMediaManager.ctx == nil {
		log.Println("GlobalMediaManager AllocatedContext re-init")
		GlobalMediaManager.InitMediaCtx()
		return
	}

	deviceConfig := malgo.DefaultDeviceConfig(malgo.Duplex)
	deviceConfig.Capture.Format = malgo.FormatS16
	deviceConfig.Capture.Channels = 1
	deviceConfig.SampleRate = 8000

	if runtime.GOOS == "linux" && runtime.GOARCH == "amd64" {
		deviceConfig.SampleRate = 48000
	}

	m.rawSampleRate = int(deviceConfig.SampleRate)

	captureCallbacks := malgo.DeviceCallbacks{
		Data: m.onSamples,
	}

	m.dev, err = malgo.InitDevice(GlobalMediaManager.ctx.Context, deviceConfig, captureCallbacks)
	if err != nil {
		log.Println("StartRecord InitDevice Err:", err)
		return
	}

	err = m.dev.Start()
	if err != nil {
		log.Println("StartRecord dev Start Err:", err)
		m.dev.Uninit()
		return
	}*/

	m.currentMediaSoftware = GlobalMediaManager.nextMediaSoftware
	if runtime.GOOS == "android" && m.currentMediaSoftware == SDKMedia {
		m.rawSampleRate = 48000

		RecorderStart()
	} else {
		//var err error
		//if GlobalMediaManager.ctx == nil {
		//	log.Println("GlobalMediaManager AllocatedContext re-init")
		//	GlobalMediaManager.InitMediaCtx()
		//	return
		//}
		//
		//deviceConfig := malgo.DefaultDeviceConfig(malgo.Capture)
		//deviceConfig.Capture.Format = malgo.FormatS16
		//deviceConfig.Capture.Channels = 1
		//deviceConfig.SampleRate = 8000
		//
		//if runtime.GOOS == "linux" && runtime.GOARCH == "amd64" {
		//	deviceConfig.SampleRate = 48000
		//}
		//
		//m.rawSampleRate = int(deviceConfig.SampleRate)
		//
		//captureCallbacks := malgo.DeviceCallbacks{
		//	Data: m.onSamples,
		//}
		//
		//m.dev, err = malgo.InitDevice(GlobalMediaManager.ctx.Context, deviceConfig, captureCallbacks)
		//if err != nil {
		//	log.Println("StartRecord InitDevice Err:", err)
		//	return
		//}
		//
		//err = m.dev.Start()
		//if err != nil {
		//	log.Println("StartRecord dev Start Err:", err)
		//	m.dev.Uninit()
		//	return
		//}

		go PortAudioStartRecord(m)
	}

	isOk = true
	m.buffer = make([]byte, 0)
	m.isRecording.Store(true)
	m.isStopPcm.Store(false)

	m.rawLock.Lock()
	m.rawPcmBuffer = nil
	m.rawLock.Unlock()

	m._8kRawLock.Lock()
	m._8kRawPcmBuffer = nil
	m._8kRawLock.Unlock()

	go m.checkoutOnePackPer60ms()
	log.Println("StartRecorder Success")
	return
}

func (m *MediaRecorder) StopRecord() {

	//if m.dev != nil {
	//	m.dev.Uninit()
	//	m.dev = nil
	//	log.Println("StopRecorder Success")
	//} else {
	//	log.Println("StopRecorder already stoped")
	//}

	if runtime.GOOS == "android" && m.currentMediaSoftware == SDKMedia {
		RecorderStop()
		//} else {
		//if m.dev != nil {
		//	m.dev.Uninit()
		//	m.dev = nil
		//	log.Println("StopRecorder Success")
		//} else {
		//	log.Println("StopRecorder already stoped")
		//}
	}

	log.Println("after dev StopRecord")
	m.Lock()
	defer func() {
		//log.Println("StopRecord Unlock")
		m.Unlock()
	}()

	if !m.isRecording.Load() {
		log.Println("call StopRecord not in recording", string(debug.Stack()))
		//return
	}

	m.isStopPcm.Store(true)
	m.isRecording.Store(false)
	GlobalApp.processSpeakStatus()
}
