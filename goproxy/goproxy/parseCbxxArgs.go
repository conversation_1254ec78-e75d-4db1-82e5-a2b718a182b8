package goproxy

import (
	"encoding/binary"
	"encoding/hex"
	"errors"
	"goproxy/bfdx_proto"
	"strconv"
)

// 65 cb00 000ad603 00000000 65 bb01
func parseCb00Args(data []byte) (target, source uint32, dic uint8, bcxx uint16, err error) {
	if len(data) < 11 {
		return 0, 0, 0, 0, errors.New("bad parameter")
	}

	target, _ = bytes2uint32(data[0:4])
	source, _ = bytes2uint32(data[4:8])
	dic = data[8]
	bcxx = binary.BigEndian.Uint16(data[9:11])

	return target, source, dic, bcxx, nil
}

func parseCb02Args(data []byte) (uint32, uint32, *bfdx_proto.Cb02, error) {
	if len(data) < 12 {
		return 0, 0, nil, errors.New("bad parameter")
	}

	target, _ := bytes2uint32(data[0:4])
	source, _ := bytes2uint32(data[4:8])
	result := &bfdx_proto.Cb02{
		YN:    int32(bytesDecodeByBCD(data[8:9])),
		Time:  int32(bytesDecodeByBCD(data[9:11])),
		Size_: int32(bytesDecodeByBCD(data[11:12])),
	}

	return target, source, result, nil
}

func parseCb04Args(data []byte) (uint32, uint32, *bfdx_proto.Cb04, error) {
	if len(data) < 30 {
		return 0, 0, nil, errors.New("bad parameter")
	}

	target, _ := bytes2uint32(data[0:4])
	source, _ := bytes2uint32(data[4:8])

	// 04 cb04 000ad604 000000000 2000112308603311131748501230893951113179420
	latLonStr := hex.EncodeToString(data[11:])
	minLatStr := latLonStr[0:9]
	minLonStr := latLonStr[9:19]
	maxLatStr := latLonStr[19:28]
	maxLonStr := latLonStr[28:38]

	minLat := parseCb03Lat(minLatStr)
	minLon := parseCb03Lon(minLonStr)
	maxLat := parseCb03Lat(maxLatStr)
	maxLon := parseCb03Lon(maxLonStr)

	result := &bfdx_proto.Cb04{
		YN:     int32(bytesDecodeByBCD(data[8:9])),
		PenN:   int32(bytesDecodeByBCD(data[9:10])),
		Time:   int32(bytesDecodeByBCD(data[10:11])),
		MinLon: minLon,
		MinLat: minLat,
		MaxLon: maxLon,
		MaxLat: maxLat,
	}

	return target, source, result, nil
}

func parseCb05Args(data []byte) (uint32, uint32, *bfdx_proto.Cb05, error) {
	if len(data) < 21 {
		return 0, 0, nil, errors.New("bad parameter")
	}

	target, _ := bytes2uint32(data[0:4])
	source, _ := bytes2uint32(data[4:8])

	// 2a cb05 000ad603 00000000 2 02 123089318 1113176736 27 29
	// 25 cb05 000ad604 00000000 2 99 100000000 1000000000 27 27
	args := hex.EncodeToString(data[8:])
	ynStr := args[0:1]
	timeStr := args[1:3]
	latStr := args[3:12]
	lonStr := args[12:22]
	lat := parseCb03Lat(latStr)
	lon := parseCb03Lon(lonStr)
	yn, _ := strconv.Atoi(ynStr)
	_time, _ := strconv.Atoi(timeStr)

	result := &bfdx_proto.Cb05{
		// 半个hex字节，补0
		YN:     int32(yn),
		Time:   int32(_time),
		Lat:    lat,
		Lon:    lon,
		LatDif: args[22:24],
		LonDif: args[24:26],
	}
	return target, source, result, nil
}

func parseCb06Args(data []byte) (uint32, uint32, *bfdx_proto.Cb06, error) {
	// 08 cb06 000ad604 00000000 02 01 75 99
	if len(data) < 12 {
		return 0, 0, nil, errors.New("bad parameter")
	}

	target, _ := bytes2uint32(data[0:4])
	source, _ := bytes2uint32(data[4:8])

	latDifInt := bytesDecodeByBCD(data[10:11])
	lonDifInt := bytesDecodeByBCD(data[11:12])
	result := &bfdx_proto.Cb06{
		YN:     int32(bytesDecodeByBCD(data[8:9])),
		Time:   int32(bytesDecodeByBCD(data[9:10])),
		LatDif: strconv.Itoa(latDifInt),
		LonDif: strconv.Itoa(lonDifInt),
	}
	return target, source, result, nil
}

func parseCb07Args(data []byte) (uint32, uint32, *bfdx_proto.Cb07, error) {
	if len(data) < 11 {
		return 0, 0, nil, errors.New("bad parameter")
	}

	target, _ := bytes2uint32(data[0:4])
	source, _ := bytes2uint32(data[4:8])
	result := &bfdx_proto.Cb07{
		YN:     int32(bytesDecodeByBCD(data[8:9])),
		JtTime: int32(bytesDecodeByBCD(data[9:10])),
		DwTime: int32(bytesDecodeByBCD(data[10:11])),
	}

	return target, source, result, nil
}

func parseCb08Args(data []byte) (uint32, uint32, *bfdx_proto.Cb08, error) {
	if len(data) < 11 {
		return 0, 0, nil, errors.New("bad parameter")
	}

	// 03 cb08 0008216e 00088265 00 00 00
	target, _ := bytes2uint32(data[0:4])
	source, _ := bytes2uint32(data[4:8])
	result := &bfdx_proto.Cb08{
		YN:   int32(bytesDecodeByBCD(data[8:9])),
		JtCh: int32(bytesDecodeByBCD(data[9:10])),
		Time: int32(bytesDecodeByBCD(data[10:11])),
	}

	return target, source, result, nil
}

func parseCb09Args(data []byte) (uint32, uint32, *bfdx_proto.Cb09, error) {
	if len(data) < 10 {
		return 0, 0, nil, errors.New("bad parameter")
	}

	// 15 cb09 000ad603 00000000 02 02
	target, _ := bytes2uint32(data[0:4])
	source, _ := bytes2uint32(data[4:8])
	result := &bfdx_proto.Cb09{
		YN: int32(bytesDecodeByBCD(data[8:9])),
		St: int32(bytesDecodeByBCD(data[9:10])),
	}

	return target, source, result, nil
}

func parseCb42Args(data []byte) (uint32, uint32, *bfdx_proto.Cb42, error) {
	if len(data) < 9 {
		return 0, 0, nil, errors.New("bad parameter")
	}

	// 08 cb42 000ad604 00000000 0c
	target, _ := bytes2uint32(data[0:4])
	source, _ := bytes2uint32(data[4:8])
	result := &bfdx_proto.Cb42{
		Code: int32(bytesDecodeByBCD(data[8:9])),
	}

	return target, source, result, nil
}
