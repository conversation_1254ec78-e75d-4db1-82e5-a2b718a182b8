package goproxy

import (
	"encoding/hex"
	"reflect"
	"testing"
)

func Test_parseCbxxArgs(t *testing.T) {
	cb05Bytes, _ := hex.DecodeString("25cb05000ad6040000000029910000000010000000002727")
	//cb05Bytes, _ := hex.DecodeString("2acb05000ad6030000000020212308931811131767362729")
	_, _, args, err := parseCb05Args(cb05Bytes[3:])
	if !reflect.DeepEqual(err, nil) {
		t.Errorf("expect %v, but got %v", nil, err)
	}
	if !reflect.DeepEqual(args.YN, int32(2)) {
		t.<PERSON><PERSON><PERSON>("expect %v, but got %v", int32(2), args.YN)
	}

	// 05cb05000ad6040000000010312308930511131767332729
	location1 := &LocationData{
		Lon: 113.29455475726883,
		Lat: 23.148841765887155,
	}
	// 04cb05000ad6040000000010312308819011131772632729
	location2 := &LocationData{
		Lon: 113.29543896797043,
		Lat: 23.146983421589795,
	}
	val := CalcRadiusFromLatDiffString("27")
	if !reflect.DeepEqual(val, 50) {
		t.Errorf("expect %v, but got %v", 50, val)
	}
	val2 := location1.getSubDistance(location2)
	if !reflect.DeepEqual(val2, 225) {
		t.Errorf("expect %v, but got %v", 225, val2)
	}
	val3 := int(CalculateRadius(location1, location2))
	if !reflect.DeepEqual(val3, 225) {
		t.Errorf("expect %v, but got %v", 225, val3)
	}
}

func Test_parseCb06Args(t *testing.T) {
	cb06Bytes, _ := hex.DecodeString("08cb06000ad6040000000002017599")
	_, _, args, err := parseCb06Args(cb06Bytes[3:])
	if !reflect.DeepEqual(err, nil) {
		t.Errorf("expect %v, but got %v", nil, err)
	}
	if !reflect.DeepEqual(args.YN, int32(2)) {
		t.Errorf("expect %v, but got %v", int32(2), args.YN)
	}
	if !reflect.DeepEqual(args.Time, int32(1)) {
		t.Errorf("expect %v, but got %v", int32(1), args.Time)
	}
	if !reflect.DeepEqual(args.LatDif, "75") {
		t.Errorf("expect %v, but got %v", "75", args.LatDif)
	}
	if !reflect.DeepEqual(args.LonDif, "99") {
		t.Errorf("expect %v, but got %v", "99", args.LonDif)
	}
}

func Test_parseCb08Args(t *testing.T) {
	bytes, _ := hex.DecodeString("03cb080008216e00088265000000")
	_, _, args, err := parseCb08Args(bytes[3:])
	if !reflect.DeepEqual(err, nil) {
		t.Errorf("expect %v, but got %v", nil, err)
	}
	if !reflect.DeepEqual(args.YN, int32(0)) {
		t.Errorf("expect %v, but got %v", int32(0), args.YN)
	}
	if !reflect.DeepEqual(args.Time, int32(0)) {
		t.Errorf("expect %v, but got %v", int32(0), args.Time)
	}
	bytes2, _ := hex.DecodeString("04cb080008216e00088265010030")
	_, _, args2, err := parseCb08Args(bytes2[3:])
	if !reflect.DeepEqual(err, nil) {
		t.Errorf("expect %v, but got %v", nil, err)
	}
	if !reflect.DeepEqual(args2.YN, int32(1)) {
		t.Errorf("expect %v, but got %v", int32(1), args2.YN)
	}
	if !reflect.DeepEqual(args2.Time, int32(30)) {
		t.Errorf("expect %v, but got %v", int32(30), args2.Time)
	}
}

func Test_parseCb09Args(t *testing.T) {
	bytes, _ := hex.DecodeString("15cb09000ad603000000000202")
	_, _, args, err := parseCb09Args(bytes[3:])
	if !reflect.DeepEqual(err, nil) {
		t.Errorf("expect %v, but got %v", nil, err)
	}
	if !reflect.DeepEqual(args.YN, int32(02)) {
		t.Errorf("expect %v, but got %v", int32(2), args.YN)
	}
	if !reflect.DeepEqual(args.St, int32(2)) {
		t.Errorf("expect %v, but got %v", int32(2), args.St)
	}
}

func Test_parseCb42Args(t *testing.T) {
	bytes, _ := hex.DecodeString("08cb42000ad604000000000c")
	_, _, args, err := parseCb42Args(bytes[3:])
	if !reflect.DeepEqual(err, nil) {
		t.Errorf("expect %v, but got %v", nil, err)
	}
	if !reflect.DeepEqual(args.Code, int32(0x0c)) {
		t.Errorf("expect %v, but got %v", int32(0x0c), args.Code)
	}
}

func Test_parseCb00Args(t *testing.T) {
	bytes, _ := hex.DecodeString("07cb000008216d0000000007bb01")
	target, _, resDic, resBcxx, err := parseCb00Args(bytes[3:])
	if !reflect.DeepEqual(err, nil) {
		t.Errorf("expect %v, but got %v", nil, err)
	}
	if !reflect.DeepEqual(target, uint32(0x0008216d)) {
		t.Errorf("expect %v, but got %v", uint32(0x0008216d), resDic)
	}
	if !reflect.DeepEqual(resDic, uint8(0x07)) {
		t.Errorf("expect %v, but got %v", uint8(0x07), resDic)
	}
	if !reflect.DeepEqual(resBcxx, uint16(0xbb01)) {
		t.Errorf("expect %v, but got %v", uint16(0xbb01), resBcxx)
	}
}
