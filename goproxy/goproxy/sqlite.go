package goproxy

import (
	"database/sql"
	"errors"
	_ "github.com/mattn/go-sqlite3"
	"log"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"
)

// sqlite数据库文件路径
var sqliteFilesDirPath = "/tmp"
var sqliteDbFileName = "state.db"
var sqliteFlag = "?cache=shared&_loc=auto"

func SetAppFilesDirPath(filesDirPath string) {
	// 复制一份，避免被C++释放内存导致路径变更
	sqliteFilesDirPath = string([]byte(filesDirPath))
}

func SetSqliteDbFileName(fileName string) {
	sqliteDbFileName = fileName
}

var db *sql.DB
var once sync.Once

func GetSqliteDB() *sql.DB {
	once.Do(func() {
		testSqliteDbPath := filepath.Join(sqliteFilesDirPath, sqliteDbFileName)
		log.Println("GetSqliteDB, db path:", testSqliteDbPath)
		var err error
		db, err = sql.Open("sqlite3", "file:"+testSqliteDbPath+sqliteFlag)
		if err != nil {
			panic(err)
		}
		// 设置连接池参数
		db.SetMaxOpenConns(10)
		db.SetMaxIdleConns(5)
	})
	return db
}

// 初始化sqlite各种表
func initSqlite() {
	var err error
	// 初始化设备状态数据表
	sqlStmt := `create table if not exists dev_status (id integer not null primary key, dmrId text not null, status blob not null, updateTime integer);`
	_, err = db.Exec(sqlStmt)
	if err != nil {
		log.Printf("%q: %s\n", err, sqlStmt)
		return
	}

	// 初始化设备cbxx指令的参数，比如cb02序列化后的数据
	sqlStmt = `create table if not exists dev_cbxx_args (id integer not null primary key, dmrId text not null, cbxx integer not null, args blob not null, updateTime integer);`
	_, err = db.Exec(sqlStmt)
	if err != nil {
		log.Printf("%q: %s\n", err, sqlStmt)
		return
	}

	// 设备最后一次定位数据表
	sqlStmt = `create table if not exists dev_last_known_location (id integer not null primary key, dmrId text not null, location blob not null, updateTime integer);`
	_, err = db.Exec(sqlStmt)
	if err != nil {
		log.Printf("%q: %s\n", err, sqlStmt)
		return
	}

	// 缓存种种bcxx,bbxx指令，当前主要缓存bb01
	sqlStmt = `create table if not exists dev_waiting_send_commands (id integer not null primary key, dmrId text not null, bcxx integer not null, target integer not null, dev_status blob not null,  data blob not null, updateTime integer);`
	_, err = db.Exec(sqlStmt)
	if err != nil {
		log.Printf("%q: %s\n", err, sqlStmt)
		return
	}
}

func InitSqliteDataBase() {
	_ = GetSqliteDB()
	initSqlite()
}

type DevStatusRow struct {
	id         int64
	DmrId      string
	Status     DevStatus
	UpdateTime int64
}

func (r *DevStatusRow) Query() error {
	row := db.QueryRow("SELECT * FROM dev_status WHERE dmrId = ?;", r.DmrId)
	if row == nil {
		return errors.New("bad query row from " + r.DmrId)
	}
	if row.Err() != nil {
		return row.Err()
	}

	err := row.Scan(&r.id, &r.DmrId, &r.Status, &r.UpdateTime)
	return err
}

func (r *DevStatusRow) Insert() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("INSERT INTO dev_status (dmrId, status, updateTime) VALUES (?, ?, ?);", r.DmrId, r.Status, now)
	if err != nil {
		return err
	}
	id, _ := res.LastInsertId()
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.id = id
		r.UpdateTime = now
	}
	return nil
}

func (r *DevStatusRow) Update() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("UPDATE dev_status SET status = ?, updateTime = ? WHERE dmrId = ?;", r.Status, now, r.DmrId)
	if err != nil {
		return err
	}
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.UpdateTime = now
	}
	return nil
}

func (r *DevStatusRow) InsertOrReplace() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("INSERT OR REPLACE INTO dev_status (id, dmrId, status, updateTime) VALUES (?, ?, ?, ?);", r.id, r.DmrId, r.Status, now)
	if err != nil {
		return err
	}
	id, _ := res.LastInsertId()
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.id = id
		r.UpdateTime = now
	}
	return nil
}

func (r *DevStatusRow) Delete() error {
	_, err := db.Exec("DELETE FROM dev_status WHERE dmrId = ?", r.DmrId)
	if err != nil {
		return err
	}
	return nil
}

type CbxxArgsRow struct {
	id         int64
	Cbxx       int64
	DmrId      string
	Args       []byte
	UpdateTime int64
}

func (r *CbxxArgsRow) Query() ([]*CbxxArgsRow, error) {
	rows, err := db.Query("SELECT * FROM dev_cbxx_args WHERE dmrId = ?;", r.DmrId)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 遍历查询结果
	result := make([]*CbxxArgsRow, 0)
	for rows.Next() {
		var s = &CbxxArgsRow{}
		err = rows.Scan(&s.id, &s.DmrId, &s.Cbxx, &s.Args, &s.UpdateTime)
		if err == nil {
			result = append(result, s)
		}
	}
	return result, err
}

func (r *CbxxArgsRow) QueryWithCbxx() error {
	row := db.QueryRow("SELECT * FROM dev_cbxx_args WHERE dmrId = ? and cbxx = ?;", r.DmrId, r.Cbxx)
	if row == nil {
		return errors.New("bad query row from " + strconv.Itoa(int(r.Cbxx)) + ", " + r.DmrId)
	}
	if row.Err() != nil {
		return row.Err()
	}

	err := row.Scan(&r.id, &r.DmrId, &r.Cbxx, &r.Args, &r.UpdateTime)
	return err
}

func (r *CbxxArgsRow) Insert() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("INSERT INTO dev_cbxx_args (dmrId, cbxx, args, updateTime) VALUES (?, ?, ?, ?);", r.DmrId, r.Cbxx, r.Args, now)
	if err != nil {
		return err
	}
	id, _ := res.LastInsertId()
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.id = id
		r.UpdateTime = now
	}
	return nil
}

func (r *CbxxArgsRow) Update() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("UPDATE dev_cbxx_args SET args = ?, updateTime = ? WHERE cbxx = ?;", r.Args, now, r.Cbxx)
	if err != nil {
		return err
	}
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.UpdateTime = now
	}
	return nil
}

func (r *CbxxArgsRow) InsertOrReplace() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("INSERT OR REPLACE INTO dev_cbxx_args (id, dmrId, cbxx, args, updateTime) VALUES (?, ?, ?, ?, ?);", r.id, r.DmrId, r.Cbxx, r.Args, now)
	if err != nil {
		return err
	}
	id, _ := res.LastInsertId()
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.id = id
		r.UpdateTime = now
	}
	return nil
}

func (r *CbxxArgsRow) Delete() error {
	_, err := db.Exec("DELETE FROM dev_cbxx_args WHERE dmrId = ? and cbxx = ?;", r.DmrId, r.Cbxx)
	if err != nil {
		return err
	}
	return nil
}

type LastKnownLocationRow struct {
	id         int64
	DmrId      string
	Location   []byte
	UpdateTime int64
}

func (r *LastKnownLocationRow) Query() error {
	row := db.QueryRow("SELECT * FROM dev_last_known_location WHERE dmrId = ?;", r.DmrId)
	if row == nil {
		return errors.New("bad query row from " + r.DmrId)
	}
	if row.Err() != nil {
		return row.Err()
	}

	err := row.Scan(&r.id, &r.DmrId, &r.Location, &r.UpdateTime)
	return err
}

func (r *LastKnownLocationRow) Insert() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("INSERT INTO dev_last_known_location (dmrId, location, updateTime) VALUES (?, ?, ?);", r.DmrId, r.Location, now)
	if err != nil {
		return err
	}
	id, _ := res.LastInsertId()
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.id = id
		r.UpdateTime = now
	}
	return nil
}

func (r *LastKnownLocationRow) Update() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("UPDATE dev_last_known_location SET location = ?, updateTime = ? WHERE dmrId = ?;", r.Location, now, r.DmrId)
	if err != nil {
		return err
	}
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.UpdateTime = now
	}
	return nil
}

func (r *LastKnownLocationRow) InsertOrReplace() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("INSERT OR REPLACE INTO dev_last_known_location (id, dmrId, location, updateTime) VALUES (?, ?, ?, ?);", r.id, r.DmrId, r.Location, now)
	if err != nil {
		return err
	}
	id, _ := res.LastInsertId()
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.id = id
		r.UpdateTime = now
	}
	return nil
}

func (r *LastKnownLocationRow) Delete() error {
	_, err := db.Exec("DELETE FROM dev_last_known_location WHERE dmrId = ?", r.DmrId)
	if err != nil {
		return err
	}
	return nil
}

// dev_waiting_send_commands 一行数据结构
type WaitingSendCommandsRow struct {
	Id         int64
	DmrId      string
	Bcxx       uint16
	Target     uint32
	DevStatus  []byte
	Data       []byte
	UpdateTime int64
}

func (r *WaitingSendCommandsRow) Query(limit int) ([]*WaitingSendCommandsRow, error) {
	rows, err := db.Query("SELECT * FROM dev_waiting_send_commands WHERE dmrId = ? limit ?;", r.DmrId, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 遍历查询结果
	result := make([]*WaitingSendCommandsRow, 0)
	for rows.Next() {
		var s = &WaitingSendCommandsRow{}
		err := rows.Scan(&s.Id, &s.DmrId, &s.Bcxx, &s.Target, &s.DevStatus, &s.Data, &s.UpdateTime)
		if err == nil {
			result = append(result, s)
		}
	}
	return result, err
}

func (r *WaitingSendCommandsRow) Insert() error {
	now := time.Now().UTC().UnixMilli()
	res, err := db.Exec("INSERT INTO dev_waiting_send_commands (dmrId, bcxx, target, dev_status, data, updateTime) VALUES (?, ?, ?, ?, ?, ?);", r.DmrId, r.Bcxx, r.Target, r.DevStatus, r.Data, now)
	if err != nil {
		return err
	}
	id, _ := res.LastInsertId()
	affected, _ := res.RowsAffected()
	if affected > 0 {
		r.Id = id
		r.UpdateTime = now
	}
	return nil
}

func (r *WaitingSendCommandsRow) Delete() error {
	_, err := db.Exec("DELETE FROM dev_waiting_send_commands WHERE dmrId = ? and id = ? ;", r.DmrId, r.Id)
	if err != nil {
		return err
	}
	return nil
}

func (r *WaitingSendCommandsRow) DeleteWithIds(ids []int64) error {
	log.Println("WaitingSendCommandsRow DeleteWithIds:", ids)
	idsLen := len(ids)
	if idsLen == 0 {
		return nil
	}

	// Create placeholders for each ID
	placeholders := make([]string, idsLen)
	args := make([]interface{}, idsLen+1)
	args[0] = r.DmrId

	for i := range ids {
		placeholders[i] = "?"
		args[i+1] = ids[i]
	}

	sql := "DELETE FROM dev_waiting_send_commands WHERE dmrId = ? AND id IN (" + strings.Join(placeholders, ",") + ")"
	_, err := db.Exec(sql, args...)
	return err
}

func (r *WaitingSendCommandsRow) Clean() error {
	_, err := db.Exec("DELETE FROM dev_waiting_send_commands WHERE dmrId = ?;", r.DmrId)
	if err != nil {
		return err
	}
	return nil
}
