package goproxy

import (
	"encoding/hex"
	"goproxy/bfdx_proto"
	"log"
	"reflect"
	"testing"
)

func Test_Sqlite3(t *testing.T) {
	var err error
	d := DevStatusRow{
		DmrId:  "00082164",
		Status: *CreateDefaultDevStatus(),
	}

	SetSqliteDbFileName("state_test.db")
	InitSqliteDataBase()

	t.Run("Test dev_status", func(t *testing.T) {
		err = d.InsertOrReplace()
		if !reflect.DeepEqual(err, nil) {
			t.<PERSON>rrorf("expect %v, but got %v", nil, err)
		}

		d.Status.SetChannelNo(128)
		err = d.Update()
		if !reflect.DeepEqual(err, nil) {
			t.<PERSON>rf("expect %v, but got %v", nil, err)
		}

		d.DmrId = "00082165"
		err = d.Insert()
		if !reflect.DeepEqual(err, nil) {
			t.<PERSON>("expect %v, but got %v", nil, err)
		}

		d.DmrId = "00082166"
		err = d.Delete()
		if !reflect.DeepEqual(err, nil) {
			t.<PERSON>("expect %v, but got %v", nil, err)
		}
	})
	t.Run("Test dev_cbxx_args", func(t *testing.T) {
		cb02 := &bfdx_proto.Cb02{
			YN:    1,
			Time:  5,
			Size_: 4,
		}
		cb02_bytes, _ := cb02.Marshal()
		log.Println("cb02_bytes:", cb02_bytes)
		c := CbxxArgsRow{
			DmrId: d.DmrId,
			Cbxx:  0xcb02,
			Args:  cb02_bytes,
		}
		err = c.InsertOrReplace()
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("expect %v, but got %v", nil, err)
		}

		c.DmrId = "00082166"
		err = c.Update()
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("expect %v, but got %v", nil, err)
		}

		err = c.Delete()
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("expect %v, but got %v", nil, err)
		}

		c.DmrId = "00082168"
		err = c.Insert()
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("expect %v, but got %v", nil, err)
		}

		c.DmrId = "00082169"
		err = c.Insert()
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("expect %v, but got %v", nil, err)
		}
	})
	t.Run("Test dev_waiting_send_commands", func(t *testing.T) {
		limit := 256
		tr := &WaitingSendCommandsRow{
			DmrId: d.DmrId,
		}

		err = tr.Clean()
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("WaitingSendCommandsRow Clean error: expect %v, but got %v", nil, err)
		}

		rows := []string{
			"000001000000014959120325100000000100000000010000000000",
			"000001000000014959120325100000000100000000010000000000",
			"000001000000014959120325100000000100000000010000000000",
			"000001000000000000000000000000000100000000010000000000",
			"000001004100000000000000000000000100000000010000000000",
		}
		for _, row := range rows {
			data, _ := hex.DecodeString(row)
			r := &WaitingSendCommandsRow{
				DmrId:     d.DmrId,
				Bcxx:      0xbb01,
				Target:    0,
				DevStatus: data[0:6],
				Data:      data[6:],
			}
			err = r.Insert()
			if !reflect.DeepEqual(err, nil) {
				t.Errorf("WaitingSendCommandsRow Insert error: expect %v, but got %v", nil, err)
			}
		}

		rows2, err := tr.Query(limit)
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("WaitingSendCommandsRow Query error: expect %v, but got %v", nil, err)
		}
		if !reflect.DeepEqual(len(rows2), 5) {
			t.Errorf("expect %v, but got %v", 5, len(rows2))
		}

		tr2 := rows2[1]
		t.Log("WaitingSendCommandsRow tr2:", tr2)
		err = tr2.Delete()
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("WaitingSendCommandsRow Delete error: expect %v, but got %v", nil, err)
		}

		rows3, err := tr.Query(limit)
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("WaitingSendCommandsRow Query error: expect %v, but got %v", nil, err)
		}
		if !reflect.DeepEqual(len(rows3), 4) {
			t.Errorf("expect %v, but got %v", 4, len(rows3))
		}

		err = tr.DeleteWithIds([]int64{1, 3, 4})
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("WaitingSendCommandsRow Query error: expect %v, but got %v", nil, err)
		}

		rows4, err := tr.Query(limit)
		if !reflect.DeepEqual(err, nil) {
			t.Errorf("WaitingSendCommandsRow Query error: expect %v, but got %v", nil, err)
		}
		if !reflect.DeepEqual(len(rows4), 1) {
			t.Errorf("expect %v, but got %v", 1, len(rows4))
		}
	})
}
