package goproxy

import (
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"math"
	"reflect"
	"runtime/debug"
	"strconv"
	"strings"
	"time"
	"unicode/utf16"
	"unsafe"

	"github.com/hashicorp/go-version"
)

const ServerSupportMapVersion = "2.92.7"

var TimeFormatStr = "2006-01-02 15:04:05"

func padStrWithLeadingZero(str string, str_min_len int) string {
	result := str
	for len(result) < str_min_len {
		result = "0" + result
	}

	return result
}

func dmrid2Hex(dmrid uint32) string {
	hex := strconv.FormatInt(int64(dmrid), 16)
	return padStrWithLeadingZero(strings.ToUpper(hex), 8)
}

func hexDmrid2Uint32(hexDmrid string) uint32 {
	id, err := strconv.ParseUint(hexDmrid, 16, 32)

	if err != nil {
		return 0
	}

	return uint32(id)
}

func parseBcXXFSKCmd(fsk []byte) (dic uint8, bcxx uint16, data []byte, err error) {
	if len(fsk) < 4 {
		err = errors.New("parseBcXXFSKCmd invalid fsk len")
		return
	}
	dic = fsk[0]
	bcxx = binary.BigEndian.Uint16(fsk[1:3])

	data = fsk[3:]
	return
}

func uft16Decode(data []byte) (string, error) {
	if len(data)%2 != 0 {
		return "", fmt.Errorf("Must have even length byte slice")
	}
	_data := make([]uint16, (len(data)+1)/2)
	for i := 0; i < len(data)-1; i += 2 {
		_data[i/2] = uint16(data[i]) + (uint16(data[i+1]) << 8)
	}
	return string(utf16.Decode(_data)), nil
}

// EncodeUTF16 get a utf8 string and translate it into a slice of bytes of ucs2
func encode2UTF16(s string, add_bom bool) []byte {
	r := []rune(s)
	iresult := utf16.Encode(r)
	var bytes []byte
	if add_bom {
		//bytes = make([]byte, 2)
		bytes = []byte{254, 255}
	}
	for _, i := range iresult {
		temp := make([]byte, 2)
		binary.LittleEndian.PutUint16(temp, i)
		bytes = append(bytes, temp...)
	}
	return bytes
}

func bytesDecodeByBCD(code []byte) int {
	ret := 0
	for i := 0; i < len(code); i++ {
		ret = 100*ret + int(code[i]>>4)*10 + int(code[i]&0x0f)
	}
	return ret
}

func int2BcdByte(d int) byte {
	r := byte(0)
	r = byte(d % 10)
	d = d / 10
	r = r | (byte(d%10) << 4)
	return r
}

// the ret len is 2
func int2BcdBytes(d int) []byte {
	ret := make([]byte, 2)
	ret[1] = byte(d % 10)
	d = d / 10
	ret[1] = ret[1] | (byte(d%10) << 4)
	d = d / 10
	ret[0] = byte(d % 10)
	d = d / 10
	ret[0] = ret[0] | (byte(d%10) << 4)

	return ret
}

// the ret len is 3
func int64ToBcdBytes(d int64) []byte {
	ret := make([]byte, 5)
	for i := 4; i > 0; i-- {
		tmp := d % 100
		ret[i] = int2BcdByte(int(tmp))
		d = d / 100
	}

	return ret
}

func uint16ToBytes(u uint16) []byte {
	ret := make([]byte, 2)
	binary.BigEndian.PutUint16(ret, u)
	return ret
}

func uint32ToBytes(u uint32) []byte {
	ret := make([]byte, 4)
	binary.BigEndian.PutUint32(ret, u)
	return ret
}

// data len == 4
func bytes2uint32(data []byte) (result uint32, err error) {
	if len(data) != 4 {
		err = errors.New("bytes2uint32 - valid byte array len")
		return
	}
	return binary.BigEndian.Uint32(data), nil
}

func parseDyGroupName(_data []byte) (name string, restData []byte, err error) {
	if len(_data) <= 0 {
		err = errors.New("parseDyGroupName Err,valid data len." + string(debug.Stack()))
		return
	}

	length := int(_data[0])

	if length > len(_data)-1 {
		err = errors.New("parseDyGroupName Err,valid data len." + string(debug.Stack()))
		return
	}

	name, err = uft16Decode(_data[1 : 1+length])
	if err != nil {
		err = errors.New("parseDyGroupName Err,uft16Decode err." + err.Error() + string(debug.Stack()))
		return
	}
	restData = _data[1+length:]
	return
}

// int32 to string
func int32ToString(v int32, str_len int) (result string) {
	result = strconv.Itoa(int(v))

	for len(result) < str_len {
		result = "0" + result
	}

	if len(result) > str_len {
		result = result[0:str_len]
	}

	return
}

func extractSysIdFromDmrid(dmr_id uint32) (sysid string) {
	ss := (dmr_id & 0xF80000) >> 19

	return int32ToString(int32(ss), 2)
}

func checkIsGroupDmrid(dmr_id uint32) bool {
	return (dmr_id & 0xFF000000) == 0x80000000
}

//len:6 byte HHMMSS , DDMMYY
func genBCDTimeBytes(utcTime time.Time, isOk bool) []byte {
	if !isOk {
		return make([]byte, 6)
	}
	r := make([]byte, 0)
	r = append(r, int2BcdByte(utcTime.Hour()))
	r = append(r, int2BcdByte(utcTime.Minute()))
	r = append(r, int2BcdByte(utcTime.Second()))
	r = append(r, int2BcdByte(utcTime.Day()))
	r = append(r, int2BcdByte(int(utcTime.Month())))
	r = append(r, int2BcdByte(utcTime.Year()%100))
	return r
}

func genBCDTimeBytesTest(utcTime time.Time) []byte {
	r := make([]byte, 0)
	r = append(r, int2BcdByte(utcTime.Hour()))
	r = append(r, int2BcdByte(utcTime.Minute()))
	r = append(r, int2BcdByte(utcTime.Second()))
	r = append(r, int2BcdByte(utcTime.Day()))
	r = append(r, int2BcdByte(int(utcTime.Month())))
	r = append(r, int2BcdByte(utcTime.Year()%100))
	return r
}

// .....1
func lon2bytes(lon float64) []byte {
	isNegative := lon < 0

	if isNegative {
		lon = -lon
	}

	lon_int := math.Floor(lon)
	dd := int32ToString(int32(lon_int), 3)
	lon_fraction := (lon - lon_int) * 60 * 10000
	mmmm := int32ToString(int32(lon_fraction), 6)

	EW := "1"
	if isNegative {
		EW = "0"
	}

	r := dd + mmmm + EW

	decodeString, _ := hex.DecodeString(r)
	return decodeString
}

// 1....1
// AV值只占半个字节，与纬度一起编码
func lat2bytes(lat float64, isOk bool) []byte {
	isNegative := lat < 0

	if isNegative {
		lat = -lat
	}

	lat_int := math.Floor(lat)
	dd := int32ToString(int32(lat_int), 2)
	lat_fraction := (lat - lat_int) * 60 * 10000
	mmmm := int32ToString(int32(lat_fraction), 6)

	AV := "0"
	if isOk {
		AV = "1"
	}

	NS := "1"
	if isNegative {
		NS = "0"
	}

	r := AV + dd + mmmm + NS

	decodeString, _ := hex.DecodeString(r)
	return decodeString
}

// 1/0 + lat，9个Ascii码字符
func lat2AsciiStr(lat float64) string {
	NS := "1"
	isNegative := lat < 0
	if isNegative {
		lat = -lat
		NS = "0"
	}

	lat_int := math.Floor(lat)
	dd := int32ToString(int32(lat_int), 2)
	lat_fraction := (lat - lat_int) * 60 * 10000
	mmmm := int32ToString(int32(lat_fraction), 6)

	return NS + dd + mmmm
}

// 1/0 + lon，10个Ascii码字符
func lon2AsciiStr(lon float64) string {
	EW := "1"
	isNegative := lon < 0
	if isNegative {
		lon = -lon
		EW = "0"
	}

	lon_int := math.Floor(lon)
	dd := int32ToString(int32(lon_int), 3)
	lon_fraction := (lon - lon_int) * 60 * 10000
	mmmm := int32ToString(int32(lon_fraction), 6)

	return EW + dd + mmmm
}

func parseCb03Lat(s string) float64 {
	PNTag, err := strconv.Atoi(string(s[0]))
	if err != nil {
		log.Println("parseCb03Lat parse PN-Tag Err:", err, s)
		return 0
	}

	latInt, err := strconv.Atoi(s[1:3])
	if err != nil {
		log.Println("parseCb03Lat parse latInt Err:", err, s[1:3])
		return 0
	}

	latFloat, err := strconv.Atoi(s[3:])
	if err != nil {
		log.Println("parseCb03Lat parse latFloat Err:", err, s[3:])
		return 0
	}

	latFloatDecimal, _ := strconv.ParseFloat(fmt.Sprintf("%.6f", float64(latFloat)/(60*10000)), 64)

	lat := float64(latInt) + latFloatDecimal
	if PNTag == 0 {
		lat *= -1
	}

	return lat
}

func parseCb03Lon(s string) float64 {
	PNTag, err := strconv.Atoi(string(s[0]))
	if err != nil {
		log.Println("parseCb03Lon parse PN-Tag Err:", err, s)
		return 0
	}

	lonInt, err := strconv.Atoi(s[1:4])
	if err != nil {
		log.Println("parseCb03Lon parse lonInt Err:", err, s[1:4])
		return 0
	}

	lonFloat, err := strconv.Atoi(s[4:])
	if err != nil {
		log.Println("parseCb03Lon parse lonFloat Err:", err, s[4:])
		return 0
	}

	lonFloatDecimal, _ := strconv.ParseFloat(fmt.Sprintf("%.6f", float64(lonFloat)/(60*10000)), 64)

	lon := float64(lonInt) + lonFloatDecimal
	if PNTag == 0 {
		lon *= -1
	}
	return lon
}

// 型如：000948ed 00000000 1-23.087782 1-113.176914 1-23.088356 1-113.177651
// 000948ed00000000 123087782 1113176914 123088356 1113177651
func parseCb03Body(fsk []byte) (target, source uint32, minLat, minLon, maxLat, maxLon float64) {
	if len(fsk) < 27 {
		log.Println("parseCb03Body got invalid fsk.partFsk:", hex.EncodeToString(fsk))
		return
	}

	target, _ = bytes2uint32(fsk[0:4])
	source, _ = bytes2uint32(fsk[4:8])
	latLonStr := hex.EncodeToString(fsk[8:27])
	minLatStr := latLonStr[0:9]
	minLonStr := latLonStr[9:19]
	maxLatStr := latLonStr[19:28]
	maxLonStr := latLonStr[28:38]

	minLat = parseCb03Lat(minLatStr)
	minLon = parseCb03Lon(minLonStr)
	maxLat = parseCb03Lat(maxLatStr)
	maxLon = parseCb03Lon(maxLonStr)

	return
}

func checkIfServerSupportMap(ver string) (bool, error) {
	if len(ver) == 0 {
		return false, errors.New("version string is empty")
	}
	currentVersion, err := version.NewVersion(ver)
	if err != nil {
		return false, err
	}

	supportVersion, err := version.NewVersion(ServerSupportMapVersion)
	if err != nil {
		return false, err
	}

	if supportVersion.LessThanOrEqual(currentVersion) {
		return true, nil
	}

	return false, nil
}

func Int16s2Bytes(i16s []int16) []byte {
	var bs []byte
	var ptrBs = (*reflect.SliceHeader)(unsafe.Pointer(&bs))
	var ptrI16s = (*reflect.SliceHeader)(unsafe.Pointer(&i16s))
	ptrBs.Data = ptrI16s.Data
	ptrBs.Len = ptrI16s.Len * 2
	ptrBs.Cap = ptrBs.Len
	return bs
}

func Bytes2I16s(bs []byte) []int16 {
	var i16s []int16
	var ptrBs = (*reflect.SliceHeader)(unsafe.Pointer(&bs))
	var ptrI16s = (*reflect.SliceHeader)(unsafe.Pointer(&i16s))
	ptrI16s.Data = ptrBs.Data
	ptrI16s.Len = ptrBs.Len / 2
	ptrI16s.Cap = ptrI16s.Len
	return i16s
}

func Int16s2Uint16s(i16s []int16) []uint16 {
	var us []uint16
	var ptrUs = (*reflect.SliceHeader)(unsafe.Pointer(&us))
	var ptrI16s = (*reflect.SliceHeader)(unsafe.Pointer(&i16s))
	ptrUs.Data = ptrI16s.Data
	ptrUs.Len = ptrI16s.Len
	ptrUs.Cap = ptrUs.Len
	return us
}
func RemoveCbxxArgsRowAt(s []*CbxxArgsRow, index int) []*CbxxArgsRow {
	if index < 0 || index >= len(s) {
		return s
	}

	copy(s[index:], s[index+1:])
	s = s[:len(s)-1]
	return s
}

func RemoveCbxxArgsRow(s []*CbxxArgsRow, item *CbxxArgsRow) []*CbxxArgsRow {
	index := -1
	for i, v := range s {
		if v.DmrId == item.DmrId && v.Cbxx == item.Cbxx {
			index = i
			break
		}
	}

	return RemoveCbxxArgsRowAt(s, index)
}

func FindCbxxArgsRowIndex(s []*CbxxArgsRow, item *CbxxArgsRow) int {
	for i, v := range s {
		if v.DmrId == item.DmrId && v.Cbxx == item.Cbxx {
			return i
		}
	}

	return -1
}

func debounce(f func(), delay time.Duration) func() {
	var timer *time.Timer
	return func() {
		if timer != nil {
			timer.Stop()
		}
		timer = time.AfterFunc(delay, f)
	}
}
