package goproxy

import (
	"encoding/hex"
	"github.com/albenik/bcd"
	"log"
	"reflect"
	"testing"
	"time"
)

func Test_int2BcdBytes(t *testing.T) {
	type args struct {
		d int
	}
	tests := []struct {
		name string
		args args
		want []byte
	}{
		{
			name: "test1",
			args: args{d: 1},
			want: []byte{0x00, 0x01},
		},
		{
			name: "test10",
			args: args{d: 10},
			want: []byte{0x00, 0x10},
		},
		{
			name: "test99",
			args: args{d: 99},
			want: []byte{0x00, 0x99},
		},
		{
			name: "test100",
			args: args{d: 100},
			want: []byte{0x01, 0x00},
		},
		{
			name: "test200",
			args: args{d: 200},
			want: []byte{0x02, 0x00},
		},
		{
			name: "test300",
			args: args{d: 300},
			want: []byte{0x03, 0x00},
		},
		{
			name: "test9999",
			args: args{d: 9999},
			want: []byte{0x99, 0x99},
		},
		{
			name: "test10000",
			args: args{d: 10000},
			want: []byte{0x00, 0x00},
		},
		{
			name: "test19999",
			args: args{d: 19999},
			want: []byte{0x99, 0x99},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := int2BcdBytes(tt.args.d); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("int2BcdBytes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_checkIsGroupDmrid(t *testing.T) {
	type args struct {
		dmr_id uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "t1",
			args: args{dmr_id: 0x80123124},
			want: true,
		},
		{
			name: "t2",
			args: args{dmr_id: 0x81111111},
			want: false,
		},
		{
			name: "t3",
			args: args{dmr_id: 0x80898787},
			want: true,
		},
		{
			name: "t4",
			args: args{dmr_id: 0x70898787},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkIsGroupDmrid(tt.args.dmr_id); got != tt.want {
				t.Errorf("checkIsGroupDmrid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_genBCDTimeBytesTest(t *testing.T) {
	type args struct {
		utcTime time.Time
	}
	tests := []struct {
		name string
		args args
		want []byte
	}{
		{
			name: "t1",
			args: struct{ utcTime time.Time }{utcTime: time.Date(2021, 1, 1, 1, 1, 1, 1, time.Local)},
			want: []byte{0x01, 0x01, 0x01, 0x01, 0x01, 0x21},
		},
		{
			name: "t2",
			args: struct{ utcTime time.Time }{utcTime: time.Date(2021, 12, 12, 12, 12, 12, 1, time.Local)},
			want: []byte{0x12, 0x12, 0x12, 0x12, 0x12, 0x21},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genBCDTimeBytesTest(tt.args.utcTime); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("genBCDTimeBytesTest() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_lat2bytes(t *testing.T) {
	bytes := bcd.FromUint64(12345678)
	log.Println(len(bytes))
	decodeString, _ := hex.DecodeString("000000 1231231410")
	type args struct {
		lat float64
	}
	tests := []struct {
		name string
		args args
		want []byte
	}{
		{
			name: "t1",
			args: struct{ lat float64 }{lat: 231231410.},
			want: decodeString,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := lat2bytes(tt.args.lat, true); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("lat2bytes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_lat2bytes1(t *testing.T) {
	ret1, _ := hex.DecodeString("1220740731")
	ret2, _ := hex.DecodeString("1220740730")
	ret3, _ := hex.DecodeString("0220740730")

	type args struct {
		lat  float64
		isOk bool
	}
	tests := []struct {
		name string
		args args
		want []byte
	}{
		{
			name: "t1",
			args: args{
				lat:  22.123456,
				isOk: true,
			},
			want: ret1,
		},
		{
			name: "t2",
			args: args{
				lat:  -22.123456,
				isOk: true,
			},
			want: ret2,
		},
		{
			name: "t3",
			args: args{
				lat:  -22.123456,
				isOk: false,
			},
			want: ret3,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := lat2bytes(tt.args.lat, tt.args.isOk); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("lat2bytes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_lon2bytes(t *testing.T) {
	ret1, _ := hex.DecodeString("1220740731")
	ret2, _ := hex.DecodeString("1221234560")

	type args struct {
		lon float64
	}
	tests := []struct {
		name string
		args args
		want []byte
	}{
		{
			name: "t1",
			args: args{lon: 122.123456},
			want: ret1,
		},
		{
			name: "t2",
			args: args{lon: -122.123456},
			want: ret2,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := lon2bytes(tt.args.lon); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("lon2bytes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_parseCb03Lat(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{
			name: "success1",
			args: args{s: "123146303"},
			want: 23.243838,
		},
		{
			name: "success2",
			args: args{s: "023146303"},
			want: -23.243838,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := parseCb03Lat(tt.args.s); got != tt.want {
				t.Errorf("parseCb03Lat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_parseCb03Lon(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{
			name: "success1",
			args: args{s: "1113176914"},
			want: 113.294857,
		},
		{
			name: "success2",
			args: args{s: "0113176914"},
			want: -113.294857,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := parseCb03Lon(tt.args.s); got != tt.want {
				t.Errorf("parseCb03Lon() = %v, want %v", got, tt.want)
			}
		})
	}
}
