#!/bin/bash

export ANDROID_OUT=../../android/app/src/main/jniLibs
mkdir -p ${ANDROID_OUT}

set HOME=/home/<USER>
if [[ -z "${ANDROID_HOME}" ]]; then
	if [[ -d "$HOME/Android/Sdk/" ]]; then
		echo "set env ANDROID_HOME=$HOME/Android/Sdk"
		export ANDROID_HOME="$HOME/Android/Sdk"
	fi
fi

if [[ -z "${ANDROID_HOME}" ]]; then
	echo "not set env ANDROID_HOME"
	exit 1
else
	export ANDROID_SDK=${ANDROID_HOME}
fi

if [[ -z "${ANDROID_NDK_VERSION}" ]]; then
	echo "not set env ANDROID_NDK_VERSION, use 23.1.7779620"
	export ANDROID_NDK_VERSION=23.1.7779620
fi

if [[ ! -d ${ANDROID_HOME} ]]; then
	echo "android sdk dir not exists: " ${ANDROID_HOME}
	exit 1
fi

export NDK_BIN=${ANDROID_SDK}/ndk/$ANDROID_NDK_VERSION/toolchains/llvm/prebuilt/linux-x86_64/bin

if [[ ! -d ${NDK_BIN} ]]; then
	echo "android ndk bin dir not exists " ${NDK_BIN}
	exit 1
fi

echo "android sdk: ${ANDROID_SDK}"
echo "ndk bin: ${NDK_BIN}"

mkdir -p ${ANDROID_OUT}/arm64-v8a
if [[ -f ${ANDROID_OUT}/arm64-v8a/libproxy.so ]]; then
  rm -f ${ANDROID_OUT}/arm64-v8a/libproxy.so
fi

#go env

#android-arm64: shared lib
CGO_ENABLED=1 \
	GOOS=android \
	GOARCH=arm64 \
	CC=${NDK_BIN}/aarch64-linux-android21-clang \
	go build -buildvcs=false -buildmode=c-shared -ldflags="-s -w" -o ${ANDROID_OUT}/arm64-v8a/libproxy.so .

mkdir -p ${ANDROID_OUT}/armeabi-v7a
if [[ -f ${ANDROID_OUT}/armeabi-v7a/libproxy.so ]]; then
  rm -f ${ANDROID_OUT}/armeabi-v7a/libproxy.so
fi

#android-armeabi-v7a: shared lib
CGO_ENABLED=1 \
	GOOS=android \
	GOARCH=arm \
	GOARM=7 \
	CC=${NDK_BIN}/armv7a-linux-androideabi21-clang \
	go build -buildvcs=false -buildmode=c-shared -ldflags="-s -w" -o ${ANDROID_OUT}/armeabi-v7a/libproxy.so .

#android-arm64: static lib
# CGO_ENABLED=1 \
# GOOS=android \
# GOARCH=arm64 \
# CC=${NDK_BIN}/aarch64-linux-android21-clang \
# go build -buildmode=c-shared  -ldflags="-s -w" -o ${ANDROID_OUT}/arm64-v8a/libproxy.so .
