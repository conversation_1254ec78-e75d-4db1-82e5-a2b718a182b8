package main

import (
	"log"
	"reflect"
	"runtime"
	"time"
	"unsafe"

	"goproxy/goproxy"
)

import "C"

type proxyLogger struct {
	//conn net.Conn
}

//func (this *proxyLogger) connectHost(hostaddr string) {
//if this.conn == nil {
//	this.conn, _ = net.Dial("tcp", hostaddr)
//}
//}

func (this *proxyLogger) Write(p []byte) (n int, err error) {
	goproxy.LogOutput(p)

	//if this.conn != nil {
	//	_, _ = this.conn.Write(p)
	//}
	return
}

//export ProxyMain
func ProxyMain() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	if runtime.GOOS == "android" {
		proxylog := &proxyLogger{}
		//proxylog.connectHost("************:7000")
		log.Default().SetOutput(proxylog)
	}

	defer func() {
		if err := recover(); err != nil {
			log.Println("###########ProxyMain", err)
		}
	}()
	//go func() {
	//	time.Sleep(10 * time.Second)
	//	goproxy.LocationStart()
	//}()
	goproxy.GlobalMediaManager.InitMediaCtx()
	goproxy.StartProxy()
}

//export SetLogOutput
func SetLogOutput(cb unsafe.Pointer) {
	goproxy.SetLogCallback(cb)
}

//export TestStr
func TestStr() string {
	return "test str from golang"
}

//export ExitProxyMain
func ExitProxyMain() {
	goproxy.ExitProxy()
}

//export Test
func Test() {
	goproxy.LoginTest(true)
}

//export GetProxyPort
func GetProxyPort() uint16 {
	return goproxy.GetProxyPort()
}

//export GetValidLocation
func GetValidLocation(t int64, lon, lat float64, direction, speed, altitude float64) {
	location := &goproxy.LocationData{
		GpsTime:   time.Unix(t, 0),
		Lon:       lon,
		Lat:       lat,
		Speed:     speed,
		Altitude:  altitude,
		Direction: direction,
	}
	log.Println("GetValidLocation", location)
	go goproxy.GlobalApp.OnAppGetLocation(location)
}

//export GetPhoneStateCallIn
func GetPhoneStateCallIn() {
	go goproxy.GlobalMediaManager.OnPhoneCallIn()
}

//export GetDenoiseSetting
func GetDenoiseSetting() C.int {
	return C.int(goproxy.GlobalMediaManager.GetDenoiseSetting())
}

//export On8K16BitsSamples
func On8K16BitsSamples(data unsafe.Pointer, sumLen, dataLen int) {
	var o []byte
	sliceHeader := (*reflect.SliceHeader)(unsafe.Pointer(&o))
	sliceHeader.Cap = sumLen
	sliceHeader.Len = sumLen
	sliceHeader.Data = uintptr(data)

	pcm := make([]byte, sumLen)
	copy(pcm, o)
	go goproxy.GlobalMediaManager.On8K16BitsSamples(nil, pcm, uint32(dataLen/2))
	//log.Println("GetPcmData :", sumLen, " ,", dataLen)
	//log.Println("GetPcmData :", sumLen, " ,", dataLen, " ,", hex.EncodeToString(o))
}

//export On48K16BitsSamples
func On48K16BitsSamples(data unsafe.Pointer, sumLen, dataLen int) {
	var o []byte
	sliceHeader := (*reflect.SliceHeader)(unsafe.Pointer(&o))
	sliceHeader.Cap = sumLen
	sliceHeader.Len = sumLen
	sliceHeader.Data = uintptr(data)

	pcm := make([]byte, sumLen)
	copy(pcm, o)
	go goproxy.GlobalMediaManager.On48K16BitsSamples(nil, pcm, uint32(dataLen/2))
	//log.Println("GetPcmData :", sumLen, " ,", dataLen)
	//log.Println("GetPcmData :", sumLen, " ,", dataLen, " ,", hex.EncodeToString(o))
}

//export GetPhoneStateCallOut
func GetPhoneStateCallOut() {
	go goproxy.GlobalMediaManager.OnPhoneCallOut()
}

//export GetPhoneStateIDLE
func GetPhoneStateIDLE() {
	go goproxy.GlobalMediaManager.OnPhoneCallIdle()
}

//export GetNetworkConn
func GetNetworkConn() {
	go goproxy.GlobalApp.OnNetworkConn()
}

//export GetNetworkLoseConn
func GetNetworkLoseConn() {
	go goproxy.GlobalApp.OnNetworkLoseConn()
}

//export RegisterWakeLockFunc
func RegisterWakeLockFunc(lock, release unsafe.Pointer) {
	goproxy.RegisterWakeLockFunc(lock, release)
}

//export RegisterMediaStateChangeFunc
func RegisterMediaStateChangeFunc(start unsafe.Pointer) {
	goproxy.RegisterMediaStateChangeFunc(start)
}

//export RegisterKcpStateChangeFunc
func RegisterKcpStateChangeFunc(conn, connLose unsafe.Pointer) {
	goproxy.RegisterKcpStateChangeFunc(conn, connLose)
}

//export RegisterUpdateNotificationChangeFunc
func RegisterUpdateNotificationChangeFunc(updateTitle, updateContent unsafe.Pointer) {
	goproxy.RegisterUpdateNotificationChangeFunc(updateTitle, updateContent)
}

//export RegisterLocatorFunc
func RegisterLocatorFunc(locationOnce, start, stop unsafe.Pointer) {
	goproxy.RegisterLocatorFunc(locationOnce, start, stop)
}

//export RegisterLocationOnceFunc
func RegisterLocationOnceFunc(locationOnce unsafe.Pointer) {
	goproxy.RegisterLocationOnceFunc(locationOnce)
}

//export RegisterRecorderFunc
func RegisterRecorderFunc(start, stop unsafe.Pointer) {
	goproxy.RegisterRecorderFunc(start, stop)
}

//export RegisterPlayerFunc
func RegisterPlayerFunc(start, stop unsafe.Pointer) {
	goproxy.RegisterPlayerFunc(start, stop)
}

//export RegisterOkErrPlayerFunc
func RegisterOkErrPlayerFunc(ok, err unsafe.Pointer) {
	goproxy.RegisterOkErrPlayerFunc(ok, err)
}

//export GetOnePackPcmData
func GetOnePackPcmData(buffer unsafe.Pointer, frameCount int32) C.int {
	//log.Println("GetOnePackPcmData called ")

	dataLen := frameCount * 2
	var o []byte
	sliceHeader := (*reflect.SliceHeader)(unsafe.Pointer(&o))
	sliceHeader.Cap = int(dataLen)
	sliceHeader.Len = int(dataLen)
	sliceHeader.Data = uintptr(buffer)

	validLen := goproxy.GlobalMediaManager.GetOnePackPcmData(o, uint32(frameCount))
	//log.Println("GetOnePackPcmData GetOnePackPcmData:", hex.EncodeToString(o), " ,len:", dataLen)

	return C.int(validLen)
}

//export RegisterOpusCodecFunc
func RegisterOpusCodecFunc(encode, decode unsafe.Pointer) {
	goproxy.RegisterOpusCodecFunc(encode, decode)
}

//export SetAppFilesDirPath
func SetAppFilesDirPath(filesDirPath string) {
	goproxy.SetAppFilesDirPath(filesDirPath)
}

//export InitSqliteDataBase
func InitSqliteDataBase() {
	goproxy.InitSqliteDataBase()
}

//export RegisterGetAppBuildInfoFunc
func RegisterGetAppBuildInfoFunc(cb unsafe.Pointer) {
	goproxy.RegisterGetAppBuildInfoFunc(cb)
}

//export RegisterUniproGpsSetFunc
func RegisterUniproGpsSetFunc(enable, disable unsafe.Pointer) {
	goproxy.RegisterUniproGpsSetFunc(enable, disable)
}

//export Log2Server
func Log2Server(utf8Str *C.char, len C.int) {
	if len > 1024 {
		len = 1024
	}
	// convert to string
	str := C.GoStringN(utf8Str, len)
	go goproxy.GlobalApp.Log2Server(str)
}

func main() {

}
