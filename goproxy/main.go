package main

import (
	"goproxy/goproxy"
	"sync"
)

var process_wg sync.WaitGroup

func main() {
	go goproxy.StartProxy()
	goproxy.GlobalMediaManager.InitMediaCtx()
	goproxy.InitSqliteDataBase()

	//// 桌面启动定位模拟
	//go func() {
	//	log.Println("start gps mock")
	//	for {
	//		location := &goproxy.LocationData{
	//			GpsTime:   time.Now().UTC(),
	//			Lon:       113.29499499999997,
	//			Lat:       23.147234999999966,
	//			Speed:     0,
	//			Altitude:  0,
	//			Direction: 0,
	//		}
	//		if goproxy.GlobalApp.IsLogin.Load() {
	//			go goproxy.GlobalApp.OnAppGetLocation(location)
	//		}
	//		time.Sleep(time.Second * 3)
	//	}
	//}()

	process_wg.Add(1)
	process_wg.Wait()
}
