//
//  Generated code. Do not modify.
//  source: app_config.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class app_config extends $pb.GeneratedMessage {
  factory app_config({
    $core.String? host,
    $core.int? port,
    $core.String? dmrid,
    $core.String? password,
    $core.bool? canEditLoginParam,
  }) {
    final $result = create();
    if (host != null) {
      $result.host = host;
    }
    if (port != null) {
      $result.port = port;
    }
    if (dmrid != null) {
      $result.dmrid = dmrid;
    }
    if (password != null) {
      $result.password = password;
    }
    if (canEditLoginParam != null) {
      $result.canEditLoginParam = canEditLoginParam;
    }
    return $result;
  }
  app_config._() : super();
  factory app_config.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory app_config.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'app_config', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'host')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'port', $pb.PbFieldType.O3)
    ..aOS(3, _omitFieldNames ? '' : 'dmrid')
    ..aOS(4, _omitFieldNames ? '' : 'password')
    ..aOB(5, _omitFieldNames ? '' : 'canEditLoginParam')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  app_config clone() => app_config()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  app_config copyWith(void Function(app_config) updates) => super.copyWith((message) => updates(message as app_config)) as app_config;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static app_config create() => app_config._();
  app_config createEmptyInstance() => create();
  static $pb.PbList<app_config> createRepeated() => $pb.PbList<app_config>();
  @$core.pragma('dart2js:noInline')
  static app_config getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<app_config>(create);
  static app_config? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get host => $_getSZ(0);
  @$pb.TagNumber(1)
  set host($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHost() => $_has(0);
  @$pb.TagNumber(1)
  void clearHost() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get port => $_getIZ(1);
  @$pb.TagNumber(2)
  set port($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPort() => $_has(1);
  @$pb.TagNumber(2)
  void clearPort() => clearField(2);

  /// hex 8位，不足8位用0填充
  @$pb.TagNumber(3)
  $core.String get dmrid => $_getSZ(2);
  @$pb.TagNumber(3)
  set dmrid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearDmrid() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get password => $_getSZ(3);
  @$pb.TagNumber(4)
  set password($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPassword() => $_has(3);
  @$pb.TagNumber(4)
  void clearPassword() => clearField(4);

  @$pb.TagNumber(5)
  $core.bool get canEditLoginParam => $_getBF(4);
  @$pb.TagNumber(5)
  set canEditLoginParam($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCanEditLoginParam() => $_has(4);
  @$pb.TagNumber(5)
  void clearCanEditLoginParam() => clearField(5);
}

class app_build_info extends $pb.GeneratedMessage {
  factory app_build_info({
    $core.String? version,
    $core.String? buildTime,
  }) {
    final $result = create();
    if (version != null) {
      $result.version = version;
    }
    if (buildTime != null) {
      $result.buildTime = buildTime;
    }
    return $result;
  }
  app_build_info._() : super();
  factory app_build_info.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory app_build_info.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'app_build_info', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'version')
    ..aOS(2, _omitFieldNames ? '' : 'buildTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  app_build_info clone() => app_build_info()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  app_build_info copyWith(void Function(app_build_info) updates) => super.copyWith((message) => updates(message as app_build_info)) as app_build_info;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static app_build_info create() => app_build_info._();
  app_build_info createEmptyInstance() => create();
  static $pb.PbList<app_build_info> createRepeated() => $pb.PbList<app_build_info>();
  @$core.pragma('dart2js:noInline')
  static app_build_info getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<app_build_info>(create);
  static app_build_info? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get version => $_getSZ(0);
  @$pb.TagNumber(1)
  set version($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasVersion() => $_has(0);
  @$pb.TagNumber(1)
  void clearVersion() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get buildTime => $_getSZ(1);
  @$pb.TagNumber(2)
  set buildTime($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBuildTime() => $_has(1);
  @$pb.TagNumber(2)
  void clearBuildTime() => clearField(2);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
