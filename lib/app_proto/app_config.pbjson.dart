//
//  Generated code. Do not modify.
//  source: app_config.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use app_configDescriptor instead')
const app_config$json = {
  '1': 'app_config',
  '2': [
    {'1': 'host', '3': 1, '4': 1, '5': 9, '10': 'host'},
    {'1': 'port', '3': 2, '4': 1, '5': 5, '10': 'port'},
    {'1': 'dmrid', '3': 3, '4': 1, '5': 9, '10': 'dmrid'},
    {'1': 'password', '3': 4, '4': 1, '5': 9, '10': 'password'},
    {'1': 'can_edit_login_param', '3': 5, '4': 1, '5': 8, '10': 'canEditLoginParam'},
  ],
};

/// Descriptor for `app_config`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List app_configDescriptor = $convert.base64Decode(
    'CgphcHBfY29uZmlnEhIKBGhvc3QYASABKAlSBGhvc3QSEgoEcG9ydBgCIAEoBVIEcG9ydBIUCg'
    'VkbXJpZBgDIAEoCVIFZG1yaWQSGgoIcGFzc3dvcmQYBCABKAlSCHBhc3N3b3JkEi8KFGNhbl9l'
    'ZGl0X2xvZ2luX3BhcmFtGAUgASgIUhFjYW5FZGl0TG9naW5QYXJhbQ==');

@$core.Deprecated('Use app_build_infoDescriptor instead')
const app_build_info$json = {
  '1': 'app_build_info',
  '2': [
    {'1': 'version', '3': 1, '4': 1, '5': 9, '10': 'version'},
    {'1': 'build_time', '3': 2, '4': 1, '5': 9, '10': 'buildTime'},
  ],
};

/// Descriptor for `app_build_info`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List app_build_infoDescriptor = $convert.base64Decode(
    'Cg5hcHBfYnVpbGRfaW5mbxIYCgd2ZXJzaW9uGAEgASgJUgd2ZXJzaW9uEh0KCmJ1aWxkX3RpbW'
    'UYAiABKAlSCWJ1aWxkVGltZQ==');

