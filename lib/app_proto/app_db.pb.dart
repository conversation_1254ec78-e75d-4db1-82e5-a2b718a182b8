//
//  Generated code. Do not modify.
//  source: app_db.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class db_org_list extends $pb.GeneratedMessage {
  factory db_org_list({
    $core.Iterable<db_org>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_org_list._() : super();
  factory db_org_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_org_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_org_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..pc<db_org>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: db_org.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_org_list clone() => db_org_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_org_list copyWith(void Function(db_org_list) updates) => super.copyWith((message) => updates(message as db_org_list)) as db_org_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_org_list create() => db_org_list._();
  db_org_list createEmptyInstance() => create();
  static $pb.PbList<db_org_list> createRepeated() => $pb.PbList<db_org_list>();
  @$core.pragma('dart2js:noInline')
  static db_org_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_org_list>(create);
  static db_org_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<db_org> get rows => $_getList(0);
}

class db_device_list extends $pb.GeneratedMessage {
  factory db_device_list({
    $core.Iterable<db_device>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_device_list._() : super();
  factory db_device_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..pc<db_device>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: db_device.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_list clone() => db_device_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_list copyWith(void Function(db_device_list) updates) => super.copyWith((message) => updates(message as db_device_list)) as db_device_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_list create() => db_device_list._();
  db_device_list createEmptyInstance() => create();
  static $pb.PbList<db_device_list> createRepeated() => $pb.PbList<db_device_list>();
  @$core.pragma('dart2js:noInline')
  static db_device_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_list>(create);
  static db_device_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<db_device> get rows => $_getList(0);
}

/// 组织架构表
class db_org extends $pb.GeneratedMessage {
  factory db_org({
    $core.String? rid,
    $core.String? orgSelfId,
    $core.String? orgShortName,
    $core.int? orgIsVirtual,
    $core.String? dmrId,
    $core.String? parentOrgId,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgSelfId != null) {
      $result.orgSelfId = orgSelfId;
    }
    if (orgShortName != null) {
      $result.orgShortName = orgShortName;
    }
    if (orgIsVirtual != null) {
      $result.orgIsVirtual = orgIsVirtual;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (parentOrgId != null) {
      $result.parentOrgId = parentOrgId;
    }
    return $result;
  }
  db_org._() : super();
  factory db_org.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_org.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_org', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(3, _omitFieldNames ? '' : 'orgSelfId')
    ..aOS(5, _omitFieldNames ? '' : 'orgShortName')
    ..a<$core.int>(8, _omitFieldNames ? '' : 'orgIsVirtual', $pb.PbFieldType.O3)
    ..aOS(9, _omitFieldNames ? '' : 'dmrId')
    ..aOS(11, _omitFieldNames ? '' : 'parentOrgId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_org clone() => db_org()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_org copyWith(void Function(db_org) updates) => super.copyWith((message) => updates(message as db_org)) as db_org;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_org create() => db_org._();
  db_org createEmptyInstance() => create();
  static $pb.PbList<db_org> createRepeated() => $pb.PbList<db_org>();
  @$core.pragma('dart2js:noInline')
  static db_org getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_org>(create);
  static db_org? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table varchar(64) unique not null
  /// 组织机构编号
  @$pb.TagNumber(3)
  $core.String get orgSelfId => $_getSZ(1);
  @$pb.TagNumber(3)
  set orgSelfId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgSelfId() => $_has(1);
  @$pb.TagNumber(3)
  void clearOrgSelfId() => clearField(3);

  /// @table varchar(32) unique not null
  /// 机构名称,缩写
  @$pb.TagNumber(5)
  $core.String get orgShortName => $_getSZ(2);
  @$pb.TagNumber(5)
  set orgShortName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasOrgShortName() => $_has(2);
  @$pb.TagNumber(5)
  void clearOrgShortName() => clearField(5);

  /// @table int default 2
  /// 2:真实机构,1:虚拟机构 虚拟机构没有真实设备和用户,只是引用真实机构的数据,相当于额外的分组
  @$pb.TagNumber(8)
  $core.int get orgIsVirtual => $_getIZ(3);
  @$pb.TagNumber(8)
  set orgIsVirtual($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(8)
  $core.bool hasOrgIsVirtual() => $_has(3);
  @$pb.TagNumber(8)
  void clearOrgIsVirtual() => clearField(8);

  /// @table varchar(8) unique
  /// DMR ID,可用作组呼的ID
  @$pb.TagNumber(9)
  $core.String get dmrId => $_getSZ(4);
  @$pb.TagNumber(9)
  set dmrId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(9)
  $core.bool hasDmrId() => $_has(4);
  @$pb.TagNumber(9)
  void clearDmrId() => clearField(9);

  /// @table uuid not null default '11111111-1111-1111-1111-111111111111'
  /// 此组织的上级机构device
  @$pb.TagNumber(11)
  $core.String get parentOrgId => $_getSZ(5);
  @$pb.TagNumber(11)
  set parentOrgId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(11)
  $core.bool hasParentOrgId() => $_has(5);
  @$pb.TagNumber(11)
  void clearParentOrgId() => clearField(11);
}

/// 对讲机设备表
class db_device extends $pb.GeneratedMessage {
  factory db_device({
    $core.String? orgId,
    $core.String? selfId,
    $core.String? dmrId,
    $core.int? deviceType,
    $core.int? priority,
  }) {
    final $result = create();
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (selfId != null) {
      $result.selfId = selfId;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (deviceType != null) {
      $result.deviceType = deviceType;
    }
    if (priority != null) {
      $result.priority = priority;
    }
    return $result;
  }
  db_device._() : super();
  factory db_device.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'selfId')
    ..aOS(5, _omitFieldNames ? '' : 'dmrId')
    ..a<$core.int>(9, _omitFieldNames ? '' : 'deviceType', $pb.PbFieldType.O3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'priority', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device clone() => db_device()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device copyWith(void Function(db_device) updates) => super.copyWith((message) => updates(message as db_device)) as db_device;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device create() => db_device._();
  db_device createEmptyInstance() => create();
  static $pb.PbList<db_device> createRepeated() => $pb.PbList<db_device>();
  @$core.pragma('dart2js:noInline')
  static db_device getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device>(create);
  static db_device? _defaultInstance;

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  /// 设备所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(0);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(0);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null unique
  /// 设备名称
  @$pb.TagNumber(4)
  $core.String get selfId => $_getSZ(1);
  @$pb.TagNumber(4)
  set selfId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(4)
  $core.bool hasSelfId() => $_has(1);
  @$pb.TagNumber(4)
  void clearSelfId() => clearField(4);

  /// @table varchar(16) not null unique
  /// 设备DMR-ID
  @$pb.TagNumber(5)
  $core.String get dmrId => $_getSZ(2);
  @$pb.TagNumber(5)
  set dmrId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasDmrId() => $_has(2);
  @$pb.TagNumber(5)
  void clearDmrId() => clearField(5);

  /// @table int not null default 0
  /// 设备类型 0:对讲机手台 1：车台 3:电话网关设备 4:中继虚拟终端 5:互联网关终端 6:模拟网关终端 7:数字网关终端
  @$pb.TagNumber(9)
  $core.int get deviceType => $_getIZ(3);
  @$pb.TagNumber(9)
  set deviceType($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(9)
  $core.bool hasDeviceType() => $_has(3);
  @$pb.TagNumber(9)
  void clearDeviceType() => clearField(9);

  /// @table int
  /// 优先级
  @$pb.TagNumber(12)
  $core.int get priority => $_getIZ(4);
  @$pb.TagNumber(12)
  set priority($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(12)
  $core.bool hasPriority() => $_has(4);
  @$pb.TagNumber(12)
  void clearPriority() => clearField(12);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
