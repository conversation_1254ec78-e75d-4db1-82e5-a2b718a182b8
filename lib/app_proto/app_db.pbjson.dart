//
//  Generated code. Do not modify.
//  source: app_db.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use db_org_listDescriptor instead')
const db_org_list$json = {
  '1': 'db_org_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.app_proto.db_org', '10': 'rows'},
  ],
};

/// Descriptor for `db_org_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_org_listDescriptor = $convert.base64Decode(
    'CgtkYl9vcmdfbGlzdBIlCgRyb3dzGAEgAygLMhEuYXBwX3Byb3RvLmRiX29yZ1IEcm93cw==');

@$core.Deprecated('Use db_device_listDescriptor instead')
const db_device_list$json = {
  '1': 'db_device_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.app_proto.db_device', '10': 'rows'},
  ],
};

/// Descriptor for `db_device_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_listDescriptor = $convert.base64Decode(
    'Cg5kYl9kZXZpY2VfbGlzdBIoCgRyb3dzGAEgAygLMhQuYXBwX3Byb3RvLmRiX2RldmljZVIEcm'
    '93cw==');

@$core.Deprecated('Use db_orgDescriptor instead')
const db_org$json = {
  '1': 'db_org',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_self_id', '3': 3, '4': 1, '5': 9, '10': 'orgSelfId'},
    {'1': 'org_short_name', '3': 5, '4': 1, '5': 9, '10': 'orgShortName'},
    {'1': 'org_is_virtual', '3': 8, '4': 1, '5': 5, '10': 'orgIsVirtual'},
    {'1': 'dmr_id', '3': 9, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'parent_org_id', '3': 11, '4': 1, '5': 9, '10': 'parentOrgId'},
  ],
};

/// Descriptor for `db_org`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_orgDescriptor = $convert.base64Decode(
    'CgZkYl9vcmcSEAoDcmlkGAEgASgJUgNyaWQSHgoLb3JnX3NlbGZfaWQYAyABKAlSCW9yZ1NlbG'
    'ZJZBIkCg5vcmdfc2hvcnRfbmFtZRgFIAEoCVIMb3JnU2hvcnROYW1lEiQKDm9yZ19pc192aXJ0'
    'dWFsGAggASgFUgxvcmdJc1ZpcnR1YWwSFQoGZG1yX2lkGAkgASgJUgVkbXJJZBIiCg1wYXJlbn'
    'Rfb3JnX2lkGAsgASgJUgtwYXJlbnRPcmdJZA==');

@$core.Deprecated('Use db_deviceDescriptor instead')
const db_device$json = {
  '1': 'db_device',
  '2': [
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'self_id', '3': 4, '4': 1, '5': 9, '10': 'selfId'},
    {'1': 'dmr_id', '3': 5, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'device_type', '3': 9, '4': 1, '5': 5, '10': 'deviceType'},
    {'1': 'priority', '3': 12, '4': 1, '5': 5, '10': 'priority'},
  ],
};

/// Descriptor for `db_device`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_deviceDescriptor = $convert.base64Decode(
    'CglkYl9kZXZpY2USFQoGb3JnX2lkGAMgASgJUgVvcmdJZBIXCgdzZWxmX2lkGAQgASgJUgZzZW'
    'xmSWQSFQoGZG1yX2lkGAUgASgJUgVkbXJJZBIfCgtkZXZpY2VfdHlwZRgJIAEoBVIKZGV2aWNl'
    'VHlwZRIaCghwcmlvcml0eRgMIAEoBVIIcHJpb3JpdHk=');

