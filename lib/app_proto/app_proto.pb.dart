//
//  Generated code. Do not modify.
//  source: app_proto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'app_db.pb.dart' as $0;

export 'app_proto.pbenum.dart';

class req_login extends $pb.GeneratedMessage {
  factory req_login({
    $core.String? sysId,
    $core.String? userName,
    $core.String? userPass,
    $core.int? loginMethod,
    $core.bool? canDisplayMap,
    $core.int? preferCodec,
  }) {
    final $result = create();
    if (sysId != null) {
      $result.sysId = sysId;
    }
    if (userName != null) {
      $result.userName = userName;
    }
    if (userPass != null) {
      $result.userPass = userPass;
    }
    if (loginMethod != null) {
      $result.loginMethod = loginMethod;
    }
    if (canDisplayMap != null) {
      $result.canDisplayMap = canDisplayMap;
    }
    if (preferCodec != null) {
      $result.preferCodec = preferCodec;
    }
    return $result;
  }
  req_login._() : super();
  factory req_login.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory req_login.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'req_login', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'sysId')
    ..aOS(2, _omitFieldNames ? '' : 'userName')
    ..aOS(3, _omitFieldNames ? '' : 'userPass')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'loginMethod', $pb.PbFieldType.O3)
    ..aOB(5, _omitFieldNames ? '' : 'canDisplayMap')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'preferCodec', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  req_login clone() => req_login()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  req_login copyWith(void Function(req_login) updates) => super.copyWith((message) => updates(message as req_login)) as req_login;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static req_login create() => req_login._();
  req_login createEmptyInstance() => create();
  static $pb.PbList<req_login> createRepeated() => $pb.PbList<req_login>();
  @$core.pragma('dart2js:noInline')
  static req_login getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<req_login>(create);
  static req_login? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get sysId => $_getSZ(0);
  @$pb.TagNumber(1)
  set sysId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSysId() => $_has(0);
  @$pb.TagNumber(1)
  void clearSysId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get userName => $_getSZ(1);
  @$pb.TagNumber(2)
  set userName($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUserName() => $_has(1);
  @$pb.TagNumber(2)
  void clearUserName() => clearField(2);

  /// 用户密码 / session
  @$pb.TagNumber(3)
  $core.String get userPass => $_getSZ(2);
  @$pb.TagNumber(3)
  set userPass($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUserPass() => $_has(2);
  @$pb.TagNumber(3)
  void clearUserPass() => clearField(3);

  /// 登录系统方法,0:使用密码登录,1:使用sid登录
  @$pb.TagNumber(4)
  $core.int get loginMethod => $_getIZ(3);
  @$pb.TagNumber(4)
  set loginMethod($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLoginMethod() => $_has(3);
  @$pb.TagNumber(4)
  void clearLoginMethod() => clearField(4);

  /// can display map
  @$pb.TagNumber(5)
  $core.bool get canDisplayMap => $_getBF(4);
  @$pb.TagNumber(5)
  set canDisplayMap($core.bool v) { $_setBool(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCanDisplayMap() => $_has(4);
  @$pb.TagNumber(5)
  void clearCanDisplayMap() => clearField(5);

  /// 0:ambe, 1:opus
  @$pb.TagNumber(6)
  $core.int get preferCodec => $_getIZ(5);
  @$pb.TagNumber(6)
  set preferCodec($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPreferCodec() => $_has(5);
  @$pb.TagNumber(6)
  void clearPreferCodec() => clearField(6);
}

class resp_login extends $pb.GeneratedMessage {
  factory resp_login({
    $core.bool? isServerSupportMap,
    $core.String? serverVersion,
    $core.String? settingLastUpdateTime,
    $0.db_device? device,
  }) {
    final $result = create();
    if (isServerSupportMap != null) {
      $result.isServerSupportMap = isServerSupportMap;
    }
    if (serverVersion != null) {
      $result.serverVersion = serverVersion;
    }
    if (settingLastUpdateTime != null) {
      $result.settingLastUpdateTime = settingLastUpdateTime;
    }
    if (device != null) {
      $result.device = device;
    }
    return $result;
  }
  resp_login._() : super();
  factory resp_login.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory resp_login.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'resp_login', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOB(1, _omitFieldNames ? '' : 'isServerSupportMap')
    ..aOS(2, _omitFieldNames ? '' : 'serverVersion')
    ..aOS(3, _omitFieldNames ? '' : 'settingLastUpdateTime')
    ..aOM<$0.db_device>(4, _omitFieldNames ? '' : 'device', subBuilder: $0.db_device.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  resp_login clone() => resp_login()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  resp_login copyWith(void Function(resp_login) updates) => super.copyWith((message) => updates(message as resp_login)) as resp_login;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static resp_login create() => resp_login._();
  resp_login createEmptyInstance() => create();
  static $pb.PbList<resp_login> createRepeated() => $pb.PbList<resp_login>();
  @$core.pragma('dart2js:noInline')
  static resp_login getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<resp_login>(create);
  static resp_login? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get isServerSupportMap => $_getBF(0);
  @$pb.TagNumber(1)
  set isServerSupportMap($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasIsServerSupportMap() => $_has(0);
  @$pb.TagNumber(1)
  void clearIsServerSupportMap() => clearField(1);

  /// 服务器版本号
  @$pb.TagNumber(2)
  $core.String get serverVersion => $_getSZ(1);
  @$pb.TagNumber(2)
  set serverVersion($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasServerVersion() => $_has(1);
  @$pb.TagNumber(2)
  void clearServerVersion() => clearField(2);

  /// 配置最后更新时间,utc时间
  /// 目前只有poc终端有此字段
  @$pb.TagNumber(3)
  $core.String get settingLastUpdateTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set settingLastUpdateTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSettingLastUpdateTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearSettingLastUpdateTime() => clearField(3);

  /// 当前登录的终端设备
  @$pb.TagNumber(4)
  $0.db_device get device => $_getN(3);
  @$pb.TagNumber(4)
  set device($0.db_device v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasDevice() => $_has(3);
  @$pb.TagNumber(4)
  void clearDevice() => clearField(4);
  @$pb.TagNumber(4)
  $0.db_device ensureDevice() => $_ensure(3);
}

/// apply for device gps info permission
class req_gps_permission extends $pb.GeneratedMessage {
  factory req_gps_permission({
    $core.int? dmrid,
    $core.int? applyDmrid,
  }) {
    final $result = create();
    if (dmrid != null) {
      $result.dmrid = dmrid;
    }
    if (applyDmrid != null) {
      $result.applyDmrid = applyDmrid;
    }
    return $result;
  }
  req_gps_permission._() : super();
  factory req_gps_permission.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory req_gps_permission.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'req_gps_permission', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'dmrid', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'applyDmrid', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  req_gps_permission clone() => req_gps_permission()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  req_gps_permission copyWith(void Function(req_gps_permission) updates) => super.copyWith((message) => updates(message as req_gps_permission)) as req_gps_permission;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static req_gps_permission create() => req_gps_permission._();
  req_gps_permission createEmptyInstance() => create();
  static $pb.PbList<req_gps_permission> createRepeated() => $pb.PbList<req_gps_permission>();
  @$core.pragma('dart2js:noInline')
  static req_gps_permission getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<req_gps_permission>(create);
  static req_gps_permission? _defaultInstance;

  /// target device dmrid
  @$pb.TagNumber(1)
  $core.int get dmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set dmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearDmrid() => clearField(1);

  /// applier device dmrid
  @$pb.TagNumber(2)
  $core.int get applyDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set applyDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasApplyDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearApplyDmrid() => clearField(2);
}

/// got apply response for device gps info permission
class res_gps_permission extends $pb.GeneratedMessage {
  factory res_gps_permission({
    $core.int? code,
    $core.int? dmrid,
    $core.String? grantUserRid,
    $core.String? grantUserName,
    $core.String? expireTime,
  }) {
    final $result = create();
    if (code != null) {
      $result.code = code;
    }
    if (dmrid != null) {
      $result.dmrid = dmrid;
    }
    if (grantUserRid != null) {
      $result.grantUserRid = grantUserRid;
    }
    if (grantUserName != null) {
      $result.grantUserName = grantUserName;
    }
    if (expireTime != null) {
      $result.expireTime = expireTime;
    }
    return $result;
  }
  res_gps_permission._() : super();
  factory res_gps_permission.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory res_gps_permission.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'res_gps_permission', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'code', $pb.PbFieldType.O3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'dmrid', $pb.PbFieldType.OU3)
    ..aOS(3, _omitFieldNames ? '' : 'grantUserRid')
    ..aOS(4, _omitFieldNames ? '' : 'grantUserName')
    ..aOS(5, _omitFieldNames ? '' : 'expireTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  res_gps_permission clone() => res_gps_permission()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  res_gps_permission copyWith(void Function(res_gps_permission) updates) => super.copyWith((message) => updates(message as res_gps_permission)) as res_gps_permission;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static res_gps_permission create() => res_gps_permission._();
  res_gps_permission createEmptyInstance() => create();
  static $pb.PbList<res_gps_permission> createRepeated() => $pb.PbList<res_gps_permission>();
  @$core.pragma('dart2js:noInline')
  static res_gps_permission getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<res_gps_permission>(create);
  static res_gps_permission? _defaultInstance;

  /// response code
  /// 0:ok 4:reject
  @$pb.TagNumber(1)
  $core.int get code => $_getIZ(0);
  @$pb.TagNumber(1)
  set code($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearCode() => clearField(1);

  /// device dmrid
  @$pb.TagNumber(2)
  $core.int get dmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set dmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearDmrid() => clearField(2);

  /// who grant the permission, user rid
  @$pb.TagNumber(3)
  $core.String get grantUserRid => $_getSZ(2);
  @$pb.TagNumber(3)
  set grantUserRid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGrantUserRid() => $_has(2);
  @$pb.TagNumber(3)
  void clearGrantUserRid() => clearField(3);

  /// grant user name
  @$pb.TagNumber(4)
  $core.String get grantUserName => $_getSZ(3);
  @$pb.TagNumber(4)
  set grantUserName($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGrantUserName() => $_has(3);
  @$pb.TagNumber(4)
  void clearGrantUserName() => clearField(4);

  /// permission expire time
  @$pb.TagNumber(5)
  $core.String get expireTime => $_getSZ(4);
  @$pb.TagNumber(5)
  set expireTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasExpireTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearExpireTime() => clearField(5);
}

/// 一个通用的gps84定位数据
class gps84 extends $pb.GeneratedMessage {
  factory gps84({
    $core.String? gpsTime,
    $core.int? av,
    $core.double? lat,
    $core.double? lon,
    $core.double? speed,
    $core.int? direction,
    $core.int? altitude,
  }) {
    final $result = create();
    if (gpsTime != null) {
      $result.gpsTime = gpsTime;
    }
    if (av != null) {
      $result.av = av;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (speed != null) {
      $result.speed = speed;
    }
    if (direction != null) {
      $result.direction = direction;
    }
    if (altitude != null) {
      $result.altitude = altitude;
    }
    return $result;
  }
  gps84._() : super();
  factory gps84.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory gps84.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'gps84', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'gpsTime')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'av', $pb.PbFieldType.O3)
    ..a<$core.double>(3, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(4, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..a<$core.double>(5, _omitFieldNames ? '' : 'speed', $pb.PbFieldType.OD)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'direction', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'altitude', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  gps84 clone() => gps84()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  gps84 copyWith(void Function(gps84) updates) => super.copyWith((message) => updates(message as gps84)) as gps84;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static gps84 create() => gps84._();
  gps84 createEmptyInstance() => create();
  static $pb.PbList<gps84> createRepeated() => $pb.PbList<gps84>();
  @$core.pragma('dart2js:noInline')
  static gps84 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<gps84>(create);
  static gps84? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get gpsTime => $_getSZ(0);
  @$pb.TagNumber(1)
  set gpsTime($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasGpsTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearGpsTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get av => $_getIZ(1);
  @$pb.TagNumber(2)
  set av($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAv() => $_has(1);
  @$pb.TagNumber(2)
  void clearAv() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get lat => $_getN(2);
  @$pb.TagNumber(3)
  set lat($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLat() => $_has(2);
  @$pb.TagNumber(3)
  void clearLat() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get lon => $_getN(3);
  @$pb.TagNumber(4)
  set lon($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLon() => $_has(3);
  @$pb.TagNumber(4)
  void clearLon() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get speed => $_getN(4);
  @$pb.TagNumber(5)
  set speed($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSpeed() => $_has(4);
  @$pb.TagNumber(5)
  void clearSpeed() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get direction => $_getIZ(5);
  @$pb.TagNumber(6)
  set direction($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDirection() => $_has(5);
  @$pb.TagNumber(6)
  void clearDirection() => clearField(6);

  @$pb.TagNumber(7)
  $core.int get altitude => $_getIZ(6);
  @$pb.TagNumber(7)
  set altitude($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasAltitude() => $_has(6);
  @$pb.TagNumber(7)
  void clearAltitude() => clearField(7);
}

/// got device gps info
class gps_info extends $pb.GeneratedMessage {
  factory gps_info({
    $core.String? dmrid,
    gps84? gpsInfo,
    $core.int? activeStatus,
  }) {
    final $result = create();
    if (dmrid != null) {
      $result.dmrid = dmrid;
    }
    if (gpsInfo != null) {
      $result.gpsInfo = gpsInfo;
    }
    if (activeStatus != null) {
      $result.activeStatus = activeStatus;
    }
    return $result;
  }
  gps_info._() : super();
  factory gps_info.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory gps_info.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'gps_info', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'dmrid')
    ..aOM<gps84>(3, _omitFieldNames ? '' : 'gpsInfo', subBuilder: gps84.create)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'activeStatus', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  gps_info clone() => gps_info()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  gps_info copyWith(void Function(gps_info) updates) => super.copyWith((message) => updates(message as gps_info)) as gps_info;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static gps_info create() => gps_info._();
  gps_info createEmptyInstance() => create();
  static $pb.PbList<gps_info> createRepeated() => $pb.PbList<gps_info>();
  @$core.pragma('dart2js:noInline')
  static gps_info getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<gps_info>(create);
  static gps_info? _defaultInstance;

  /// device dmrid
  @$pb.TagNumber(1)
  $core.String get dmrid => $_getSZ(0);
  @$pb.TagNumber(1)
  set dmrid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearDmrid() => clearField(1);

  /// device gps info
  @$pb.TagNumber(3)
  gps84 get gpsInfo => $_getN(1);
  @$pb.TagNumber(3)
  set gpsInfo(gps84 v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasGpsInfo() => $_has(1);
  @$pb.TagNumber(3)
  void clearGpsInfo() => clearField(3);
  @$pb.TagNumber(3)
  gps84 ensureGpsInfo() => $_ensure(1);

  /// 1为用户主动定位
  @$pb.TagNumber(4)
  $core.int get activeStatus => $_getIZ(2);
  @$pb.TagNumber(4)
  set activeStatus($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasActiveStatus() => $_has(2);
  @$pb.TagNumber(4)
  void clearActiveStatus() => clearField(4);
}

class req_update_listen_group_list extends $pb.GeneratedMessage {
  factory req_update_listen_group_list({
    $core.Iterable<$core.String>? listenGroupList,
  }) {
    final $result = create();
    if (listenGroupList != null) {
      $result.listenGroupList.addAll(listenGroupList);
    }
    return $result;
  }
  req_update_listen_group_list._() : super();
  factory req_update_listen_group_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory req_update_listen_group_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'req_update_listen_group_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..pPS(1, _omitFieldNames ? '' : 'listenGroupList')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  req_update_listen_group_list clone() => req_update_listen_group_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  req_update_listen_group_list copyWith(void Function(req_update_listen_group_list) updates) => super.copyWith((message) => updates(message as req_update_listen_group_list)) as req_update_listen_group_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static req_update_listen_group_list create() => req_update_listen_group_list._();
  req_update_listen_group_list createEmptyInstance() => create();
  static $pb.PbList<req_update_listen_group_list> createRepeated() => $pb.PbList<req_update_listen_group_list>();
  @$core.pragma('dart2js:noInline')
  static req_update_listen_group_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<req_update_listen_group_list>(create);
  static req_update_listen_group_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.String> get listenGroupList => $_getList(0);
}

class req_update_speak_target extends $pb.GeneratedMessage {
  factory req_update_speak_target({
    $core.String? speakTarget,
  }) {
    final $result = create();
    if (speakTarget != null) {
      $result.speakTarget = speakTarget;
    }
    return $result;
  }
  req_update_speak_target._() : super();
  factory req_update_speak_target.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory req_update_speak_target.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'req_update_speak_target', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'speakTarget')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  req_update_speak_target clone() => req_update_speak_target()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  req_update_speak_target copyWith(void Function(req_update_speak_target) updates) => super.copyWith((message) => updates(message as req_update_speak_target)) as req_update_speak_target;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static req_update_speak_target create() => req_update_speak_target._();
  req_update_speak_target createEmptyInstance() => create();
  static $pb.PbList<req_update_speak_target> createRepeated() => $pb.PbList<req_update_speak_target>();
  @$core.pragma('dart2js:noInline')
  static req_update_speak_target getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<req_update_speak_target>(create);
  static req_update_speak_target? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get speakTarget => $_getSZ(0);
  @$pb.TagNumber(1)
  set speakTarget($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSpeakTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearSpeakTarget() => clearField(1);
}

/// 包含 单位 动态组 终端设备
class address_book extends $pb.GeneratedMessage {
  factory address_book({
    $core.String? parentDmrid,
    $core.String? dmrid,
    $core.String? name,
    $core.int? devType,
    $core.int? orgSortValue,
  }) {
    final $result = create();
    if (parentDmrid != null) {
      $result.parentDmrid = parentDmrid;
    }
    if (dmrid != null) {
      $result.dmrid = dmrid;
    }
    if (name != null) {
      $result.name = name;
    }
    if (devType != null) {
      $result.devType = devType;
    }
    if (orgSortValue != null) {
      $result.orgSortValue = orgSortValue;
    }
    return $result;
  }
  address_book._() : super();
  factory address_book.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory address_book.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'address_book', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'parentDmrid')
    ..aOS(2, _omitFieldNames ? '' : 'dmrid')
    ..aOS(3, _omitFieldNames ? '' : 'name')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'devType', $pb.PbFieldType.O3, protoName: 'devType')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'orgSortValue', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  address_book clone() => address_book()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  address_book copyWith(void Function(address_book) updates) => super.copyWith((message) => updates(message as address_book)) as address_book;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static address_book create() => address_book._();
  address_book createEmptyInstance() => create();
  static $pb.PbList<address_book> createRepeated() => $pb.PbList<address_book>();
  @$core.pragma('dart2js:noInline')
  static address_book getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<address_book>(create);
  static address_book? _defaultInstance;

  /// 上级单位的dmrid，如果对应是单位则不需要层层嵌套
  @$pb.TagNumber(1)
  $core.String get parentDmrid => $_getSZ(0);
  @$pb.TagNumber(1)
  set parentDmrid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasParentDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearParentDmrid() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get dmrid => $_getSZ(1);
  @$pb.TagNumber(2)
  set dmrid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearDmrid() => clearField(2);

  /// 设备名称
  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  /// 单位类型 111:虚拟机构  112:真实机构 100:临时组  101：任务组  102:自动失效的临时组
  /// 设备类型 0:对讲机手台 1：车台 2:指挥座席 3:电话网关设备 4:中继虚拟终端 5:互联网关终端 6:模拟网关终端 7:数字网关终端 21:基地台  22:android模拟终端
  @$pb.TagNumber(4)
  $core.int get devType => $_getIZ(3);
  @$pb.TagNumber(4)
  set devType($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDevType() => $_has(3);
  @$pb.TagNumber(4)
  void clearDevType() => clearField(4);

  /// 单位排序值
  @$pb.TagNumber(5)
  $core.int get orgSortValue => $_getIZ(4);
  @$pb.TagNumber(5)
  set orgSortValue($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasOrgSortValue() => $_has(4);
  @$pb.TagNumber(5)
  void clearOrgSortValue() => clearField(5);
}

class req_address_book_result extends $pb.GeneratedMessage {
  factory req_address_book_result({
    $core.Iterable<address_book>? successList,
    $core.Iterable<$core.String>? failedList,
  }) {
    final $result = create();
    if (successList != null) {
      $result.successList.addAll(successList);
    }
    if (failedList != null) {
      $result.failedList.addAll(failedList);
    }
    return $result;
  }
  req_address_book_result._() : super();
  factory req_address_book_result.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory req_address_book_result.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'req_address_book_result', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..pc<address_book>(1, _omitFieldNames ? '' : 'successList', $pb.PbFieldType.PM, subBuilder: address_book.create)
    ..pPS(2, _omitFieldNames ? '' : 'failedList')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  req_address_book_result clone() => req_address_book_result()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  req_address_book_result copyWith(void Function(req_address_book_result) updates) => super.copyWith((message) => updates(message as req_address_book_result)) as req_address_book_result;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static req_address_book_result create() => req_address_book_result._();
  req_address_book_result createEmptyInstance() => create();
  static $pb.PbList<req_address_book_result> createRepeated() => $pb.PbList<req_address_book_result>();
  @$core.pragma('dart2js:noInline')
  static req_address_book_result getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<req_address_book_result>(create);
  static req_address_book_result? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<address_book> get successList => $_getList(0);

  /// 失败的dmrid
  @$pb.TagNumber(2)
  $core.List<$core.String> get failedList => $_getList(1);
}

class address_book_list extends $pb.GeneratedMessage {
  factory address_book_list({
    $core.Iterable<address_book>? addrBookList,
  }) {
    final $result = create();
    if (addrBookList != null) {
      $result.addrBookList.addAll(addrBookList);
    }
    return $result;
  }
  address_book_list._() : super();
  factory address_book_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory address_book_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'address_book_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..pc<address_book>(1, _omitFieldNames ? '' : 'addrBookList', $pb.PbFieldType.PM, subBuilder: address_book.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  address_book_list clone() => address_book_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  address_book_list copyWith(void Function(address_book_list) updates) => super.copyWith((message) => updates(message as address_book_list)) as address_book_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static address_book_list create() => address_book_list._();
  address_book_list createEmptyInstance() => create();
  static $pb.PbList<address_book_list> createRepeated() => $pb.PbList<address_book_list>();
  @$core.pragma('dart2js:noInline')
  static address_book_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<address_book_list>(create);
  static address_book_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<address_book> get addrBookList => $_getList(0);
}

class server_addr extends $pb.GeneratedMessage {
  factory server_addr({
    $core.String? host,
    $core.int? port,
  }) {
    final $result = create();
    if (host != null) {
      $result.host = host;
    }
    if (port != null) {
      $result.port = port;
    }
    return $result;
  }
  server_addr._() : super();
  factory server_addr.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory server_addr.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'server_addr', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'host')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'port', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  server_addr clone() => server_addr()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  server_addr copyWith(void Function(server_addr) updates) => super.copyWith((message) => updates(message as server_addr)) as server_addr;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static server_addr create() => server_addr._();
  server_addr createEmptyInstance() => create();
  static $pb.PbList<server_addr> createRepeated() => $pb.PbList<server_addr>();
  @$core.pragma('dart2js:noInline')
  static server_addr getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<server_addr>(create);
  static server_addr? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get host => $_getSZ(0);
  @$pb.TagNumber(1)
  set host($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHost() => $_has(0);
  @$pb.TagNumber(1)
  void clearHost() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get port => $_getIZ(1);
  @$pb.TagNumber(2)
  set port($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPort() => $_has(1);
  @$pb.TagNumber(2)
  void clearPort() => clearField(2);
}

class short_messages extends $pb.GeneratedMessage {
  factory short_messages({
    $core.String? senderDmrid,
    $core.String? targetDmrid,
    $core.String? smsContent,
    $core.int? smsNo,
    $core.int? codec,
    $core.int? smsType,
    $fixnum.Int64? time,
  }) {
    final $result = create();
    if (senderDmrid != null) {
      $result.senderDmrid = senderDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (smsContent != null) {
      $result.smsContent = smsContent;
    }
    if (smsNo != null) {
      $result.smsNo = smsNo;
    }
    if (codec != null) {
      $result.codec = codec;
    }
    if (smsType != null) {
      $result.smsType = smsType;
    }
    if (time != null) {
      $result.time = time;
    }
    return $result;
  }
  short_messages._() : super();
  factory short_messages.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory short_messages.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'short_messages', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..aOS(4, _omitFieldNames ? '' : 'senderDmrid')
    ..aOS(5, _omitFieldNames ? '' : 'targetDmrid')
    ..aOS(7, _omitFieldNames ? '' : 'smsContent')
    ..a<$core.int>(8, _omitFieldNames ? '' : 'smsNo', $pb.PbFieldType.O3)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'codec', $pb.PbFieldType.O3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'smsType', $pb.PbFieldType.O3)
    ..aInt64(12, _omitFieldNames ? '' : 'time')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  short_messages clone() => short_messages()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  short_messages copyWith(void Function(short_messages) updates) => super.copyWith((message) => updates(message as short_messages)) as short_messages;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static short_messages create() => short_messages._();
  short_messages createEmptyInstance() => create();
  static $pb.PbList<short_messages> createRepeated() => $pb.PbList<short_messages>();
  @$core.pragma('dart2js:noInline')
  static short_messages getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<short_messages>(create);
  static short_messages? _defaultInstance;

  /// 发起者dmrid
  @$pb.TagNumber(4)
  $core.String get senderDmrid => $_getSZ(0);
  @$pb.TagNumber(4)
  set senderDmrid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(4)
  $core.bool hasSenderDmrid() => $_has(0);
  @$pb.TagNumber(4)
  void clearSenderDmrid() => clearField(4);

  /// 接收方dmrid
  @$pb.TagNumber(5)
  $core.String get targetDmrid => $_getSZ(1);
  @$pb.TagNumber(5)
  set targetDmrid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(5)
  $core.bool hasTargetDmrid() => $_has(1);
  @$pb.TagNumber(5)
  void clearTargetDmrid() => clearField(5);

  /// 短信内容 utf16 编码
  @$pb.TagNumber(7)
  $core.String get smsContent => $_getSZ(2);
  @$pb.TagNumber(7)
  set smsContent($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(7)
  $core.bool hasSmsContent() => $_has(2);
  @$pb.TagNumber(7)
  void clearSmsContent() => clearField(7);

  /// 短信序号
  @$pb.TagNumber(8)
  $core.int get smsNo => $_getIZ(3);
  @$pb.TagNumber(8)
  set smsNo($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(8)
  $core.bool hasSmsNo() => $_has(3);
  @$pb.TagNumber(8)
  void clearSmsNo() => clearField(8);

  /// 编码格式 2.utf16(目前手台只支持此编码)
  @$pb.TagNumber(10)
  $core.int get codec => $_getIZ(4);
  @$pb.TagNumber(10)
  set codec($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(10)
  $core.bool hasCodec() => $_has(4);
  @$pb.TagNumber(10)
  void clearCodec() => clearField(10);

  /// 短信类型 0:普通短信 1:自动播报短信
  @$pb.TagNumber(11)
  $core.int get smsType => $_getIZ(5);
  @$pb.TagNumber(11)
  set smsType($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(11)
  $core.bool hasSmsType() => $_has(5);
  @$pb.TagNumber(11)
  void clearSmsType() => clearField(11);

  /// 接受时间
  @$pb.TagNumber(12)
  $fixnum.Int64 get time => $_getI64(6);
  @$pb.TagNumber(12)
  set time($fixnum.Int64 v) { $_setInt64(6, v); }
  @$pb.TagNumber(12)
  $core.bool hasTime() => $_has(6);
  @$pb.TagNumber(12)
  void clearTime() => clearField(12);
}

class Notify extends $pb.GeneratedMessage {
  factory Notify({
    $core.int? code,
    $core.String? paramStr,
    $core.List<$core.int>? body,
  }) {
    final $result = create();
    if (code != null) {
      $result.code = code;
    }
    if (paramStr != null) {
      $result.paramStr = paramStr;
    }
    if (body != null) {
      $result.body = body;
    }
    return $result;
  }
  Notify._() : super();
  factory Notify.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Notify.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Notify', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'code', $pb.PbFieldType.O3)
    ..aOS(2, _omitFieldNames ? '' : 'paramStr', protoName: 'paramStr')
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'body', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Notify clone() => Notify()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Notify copyWith(void Function(Notify) updates) => super.copyWith((message) => updates(message as Notify)) as Notify;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Notify create() => Notify._();
  Notify createEmptyInstance() => create();
  static $pb.PbList<Notify> createRepeated() => $pb.PbList<Notify>();
  @$core.pragma('dart2js:noInline')
  static Notify getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Notify>(create);
  static Notify? _defaultInstance;

  /// 1.打开麦克风失败 2.kcp断开  5,kcp链接服务器失败
  /// 6.播放语音历史开始(paraStr为当前播放历史记录"repeaterIdHex-sourceHex-timeStampHex")  7.播放语音历史结束
  /// 9.录音历史下载失败
  ///  0xcb36.加入临时组  0xcb37.退出临时组 0xcb38.加入任务组  0xcb39.退出任务组
  /// 333.sync online devices,body = bfkcp.ex_oneline_devices
  /// 8.收到他人讲话通知。非bc15.body = bfkcp.dev_data_info
  /// 11.正在播放他人语音 paramStr=sourceHex-targetHex 12.正在播放语音历史。paramStr=repeaterIdHex-sourceHex-timeStampHex
  /// 20.收到gps定位信息, body = gps_info
  @$pb.TagNumber(1)
  $core.int get code => $_getIZ(0);
  @$pb.TagNumber(1)
  set code($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearCode() => clearField(1);

  /// 额外的附加信息
  /// 0xcb37/0xcb39 groupDmrid
  @$pb.TagNumber(2)
  $core.String get paramStr => $_getSZ(1);
  @$pb.TagNumber(2)
  set paramStr($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasParamStr() => $_has(1);
  @$pb.TagNumber(2)
  void clearParamStr() => clearField(2);

  /// code = 0xcb38,body = AddressBook
  /// code = 0xcb36,body = AddressBook
  @$pb.TagNumber(3)
  $core.List<$core.int> get body => $_getN(2);
  @$pb.TagNumber(3)
  set body($core.List<$core.int> v) { $_setBytes(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasBody() => $_has(2);
  @$pb.TagNumber(3)
  void clearBody() => clearField(3);
}

class voice_config extends $pb.GeneratedMessage {
  factory voice_config({
    $core.int? gain,
    $core.int? mediaAutoStartSize,
    $core.int? mediaBufferSize,
    $core.int? speakTimeout,
    $core.int? denoiseSetting,
    $core.int? debugRecorderPcm,
    $core.int? recorderPcmGain,
  }) {
    final $result = create();
    if (gain != null) {
      $result.gain = gain;
    }
    if (mediaAutoStartSize != null) {
      $result.mediaAutoStartSize = mediaAutoStartSize;
    }
    if (mediaBufferSize != null) {
      $result.mediaBufferSize = mediaBufferSize;
    }
    if (speakTimeout != null) {
      $result.speakTimeout = speakTimeout;
    }
    if (denoiseSetting != null) {
      $result.denoiseSetting = denoiseSetting;
    }
    if (debugRecorderPcm != null) {
      $result.debugRecorderPcm = debugRecorderPcm;
    }
    if (recorderPcmGain != null) {
      $result.recorderPcmGain = recorderPcmGain;
    }
    return $result;
  }
  voice_config._() : super();
  factory voice_config.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory voice_config.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'voice_config', package: const $pb.PackageName(_omitMessageNames ? '' : 'app_proto'), createEmptyInstance: create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'gain', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'mediaAutoStartSize', $pb.PbFieldType.O3, protoName: 'mediaAutoStartSize')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'mediaBufferSize', $pb.PbFieldType.O3, protoName: 'mediaBufferSize')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'speakTimeout', $pb.PbFieldType.O3, protoName: 'speakTimeout')
    ..a<$core.int>(8, _omitFieldNames ? '' : 'denoiseSetting', $pb.PbFieldType.O3, protoName: 'denoiseSetting')
    ..a<$core.int>(9, _omitFieldNames ? '' : 'debugRecorderPcm', $pb.PbFieldType.O3, protoName: 'debugRecorderPcm')
    ..a<$core.int>(10, _omitFieldNames ? '' : 'recorderPcmGain', $pb.PbFieldType.O3, protoName: 'recorderPcmGain')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  voice_config clone() => voice_config()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  voice_config copyWith(void Function(voice_config) updates) => super.copyWith((message) => updates(message as voice_config)) as voice_config;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static voice_config create() => voice_config._();
  voice_config createEmptyInstance() => create();
  static $pb.PbList<voice_config> createRepeated() => $pb.PbList<voice_config>();
  @$core.pragma('dart2js:noInline')
  static voice_config getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<voice_config>(create);
  static voice_config? _defaultInstance;

  /// 声音放大参数 //基准为1000  [500,10000]测试一下
  @$pb.TagNumber(3)
  $core.int get gain => $_getIZ(0);
  @$pb.TagNumber(3)
  set gain($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(3)
  $core.bool hasGain() => $_has(0);
  @$pb.TagNumber(3)
  void clearGain() => clearField(3);

  /// 播放媒体缓冲区（*个包） 一个包 60ms
  /// 范围 [8,25]  即 约[0.5,1.5]秒
  /// 超过这个大小，自动开始 播放
  @$pb.TagNumber(4)
  $core.int get mediaAutoStartSize => $_getIZ(1);
  @$pb.TagNumber(4)
  set mediaAutoStartSize($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(4)
  $core.bool hasMediaAutoStartSize() => $_has(1);
  @$pb.TagNumber(4)
  void clearMediaAutoStartSize() => clearField(4);

  /// 媒体总缓冲区大小（*个包） 一个包 60ms
  /// 范围 [250,500] 即[15,30]秒
  @$pb.TagNumber(5)
  $core.int get mediaBufferSize => $_getIZ(2);
  @$pb.TagNumber(5)
  set mediaBufferSize($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasMediaBufferSize() => $_has(2);
  @$pb.TagNumber(5)
  void clearMediaBufferSize() => clearField(5);

  /// 讲话超时 *秒
  @$pb.TagNumber(6)
  $core.int get speakTimeout => $_getIZ(3);
  @$pb.TagNumber(6)
  set speakTimeout($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasSpeakTimeout() => $_has(3);
  @$pb.TagNumber(6)
  void clearSpeakTimeout() => clearField(6);

  /// denoise setting
  /// 0: off, 1: ambe denoise 2:rnnoise
  @$pb.TagNumber(8)
  $core.int get denoiseSetting => $_getIZ(4);
  @$pb.TagNumber(8)
  set denoiseSetting($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(8)
  $core.bool hasDenoiseSetting() => $_has(4);
  @$pb.TagNumber(8)
  void clearDenoiseSetting() => clearField(8);

  /// debug recorder pcm_data
  /// 0: off 1:on
  @$pb.TagNumber(9)
  $core.int get debugRecorderPcm => $_getIZ(5);
  @$pb.TagNumber(9)
  set debugRecorderPcm($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(9)
  $core.bool hasDebugRecorderPcm() => $_has(5);
  @$pb.TagNumber(9)
  void clearDebugRecorderPcm() => clearField(9);

  /// recorder pcm gain
  /// xxx% 10-1000
  @$pb.TagNumber(10)
  $core.int get recorderPcmGain => $_getIZ(6);
  @$pb.TagNumber(10)
  set recorderPcmGain($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(10)
  $core.bool hasRecorderPcmGain() => $_has(6);
  @$pb.TagNumber(10)
  void clearRecorderPcmGain() => clearField(10);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
