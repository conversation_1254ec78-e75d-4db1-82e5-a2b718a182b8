//
//  Generated code. Do not modify.
//  source: app_proto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class media_status extends $pb.ProtobufEnum {
  static const media_status start = media_status._(0, _omitEnumNames ? '' : 'start');
  static const media_status stoped = media_status._(1, _omitEnumNames ? '' : 'stoped');

  static const $core.List<media_status> values = <media_status> [
    start,
    stoped,
  ];

  static final $core.Map<$core.int, media_status> _byValue = $pb.ProtobufEnum.initByValue(values);
  static media_status? valueOf($core.int value) => _byValue[value];

  const media_status._($core.int v, $core.String n) : super(v, n);
}

class res_code extends $pb.ProtobufEnum {
  static const res_code success = res_code._(0, _omitEnumNames ? '' : 'success');
  static const res_code fialed = res_code._(1, _omitEnumNames ? '' : 'fialed');

  static const $core.List<res_code> values = <res_code> [
    success,
    fialed,
  ];

  static final $core.Map<$core.int, res_code> _byValue = $pb.ProtobufEnum.initByValue(values);
  static res_code? valueOf($core.int value) => _byValue[value];

  const res_code._($core.int v, $core.String n) : super(v, n);
}

/// rpc 结构见 bf8100_project 的 bf8100.proto 中的 rpc_cmd
/// 默认req 使用的 message对应enum cmd_后面的部分
/// 如：cmd_req_ping的body 对应message req_login
/// 回应对body和res有对应介绍
/// 回应rpc.res = res_code.fialed 时候 para_str 携带错误原因
class cmd_code extends $pb.ProtobufEnum {
  static const cmd_code cmd_req_ping = cmd_code._(0, _omitEnumNames ? '' : 'cmd_req_ping');
  static const cmd_code cmd_resp_ping = cmd_code._(1, _omitEnumNames ? '' : 'cmd_resp_ping');
  static const cmd_code cmd_req_login = cmd_code._(2, _omitEnumNames ? '' : 'cmd_req_login');
  static const cmd_code cmd_resp_login = cmd_code._(3, _omitEnumNames ? '' : 'cmd_resp_login');
  static const cmd_code cmd_req_update_listen_group_list = cmd_code._(4, _omitEnumNames ? '' : 'cmd_req_update_listen_group_list');
  static const cmd_code cmd_resp_update_listen_group_list = cmd_code._(5, _omitEnumNames ? '' : 'cmd_resp_update_listen_group_list');
  static const cmd_code cmd_req_query_listen_group_list = cmd_code._(6, _omitEnumNames ? '' : 'cmd_req_query_listen_group_list');
  static const cmd_code cmd_resp_query_listen_group_list = cmd_code._(7, _omitEnumNames ? '' : 'cmd_resp_query_listen_group_list');
  static const cmd_code cmd_req_update_speak_target = cmd_code._(8, _omitEnumNames ? '' : 'cmd_req_update_speak_target');
  static const cmd_code cmd_resp_update_speak_target = cmd_code._(9, _omitEnumNames ? '' : 'cmd_resp_update_speak_target');
  static const cmd_code cmd_req_query_speak_target = cmd_code._(10, _omitEnumNames ? '' : 'cmd_req_query_speak_target');
  static const cmd_code cmd_resp_query_speak_target = cmd_code._(11, _omitEnumNames ? '' : 'cmd_resp_query_speak_target');
  static const cmd_code cmd_req_query_address_book = cmd_code._(12, _omitEnumNames ? '' : 'cmd_req_query_address_book');
  static const cmd_code cmd_resp_query_address_book = cmd_code._(13, _omitEnumNames ? '' : 'cmd_resp_query_address_book');
  static const cmd_code cmd_req_speak_start = cmd_code._(14, _omitEnumNames ? '' : 'cmd_req_speak_start');
  static const cmd_code cmd_resp_speak_start = cmd_code._(15, _omitEnumNames ? '' : 'cmd_resp_speak_start');
  static const cmd_code cmd_req_speak_stop = cmd_code._(16, _omitEnumNames ? '' : 'cmd_req_speak_stop');
  static const cmd_code cmd_resp_speak_stop = cmd_code._(17, _omitEnumNames ? '' : 'cmd_resp_speak_stop');
  static const cmd_code cmd_req_speak_status = cmd_code._(18, _omitEnumNames ? '' : 'cmd_req_speak_status');
  static const cmd_code cmd_resp_speak_status = cmd_code._(19, _omitEnumNames ? '' : 'cmd_resp_speak_status');
  static const cmd_code cmd_req_media_play_status = cmd_code._(20, _omitEnumNames ? '' : 'cmd_req_media_play_status');
  static const cmd_code cmd_resp_media_play_status = cmd_code._(21, _omitEnumNames ? '' : 'cmd_resp_media_play_status');
  static const cmd_code cmd_notify = cmd_code._(22, _omitEnumNames ? '' : 'cmd_notify');
  static const cmd_code cmd_short_messages = cmd_code._(24, _omitEnumNames ? '' : 'cmd_short_messages');
  static const cmd_code cmd_resp_confirm_short_messages = cmd_code._(25, _omitEnumNames ? '' : 'cmd_resp_confirm_short_messages');
  static const cmd_code cmd_send_short_messages = cmd_code._(26, _omitEnumNames ? '' : 'cmd_send_short_messages');
  static const cmd_code cmd_resp_send_short_messages = cmd_code._(27, _omitEnumNames ? '' : 'cmd_resp_send_short_messages');
  static const cmd_code cmd_req_is_login = cmd_code._(28, _omitEnumNames ? '' : 'cmd_req_is_login');
  static const cmd_code cmd_resp_is_login = cmd_code._(29, _omitEnumNames ? '' : 'cmd_resp_is_login');
  static const cmd_code cmd_req_is_conn_server = cmd_code._(30, _omitEnumNames ? '' : 'cmd_req_is_conn_server');
  static const cmd_code cmd_resp_is_conn_server = cmd_code._(31, _omitEnumNames ? '' : 'cmd_resp_is_conn_server');
  static const cmd_code cmd_req_update_server_addr = cmd_code._(32, _omitEnumNames ? '' : 'cmd_req_update_server_addr');
  static const cmd_code cmd_resp_update_server_addr = cmd_code._(33, _omitEnumNames ? '' : 'cmd_resp_update_server_addr');
  static const cmd_code cmd_req_query_login_user = cmd_code._(34, _omitEnumNames ? '' : 'cmd_req_query_login_user');
  static const cmd_code cmd_resp_query_login_user = cmd_code._(35, _omitEnumNames ? '' : 'cmd_resp_query_login_user');
  static const cmd_code cmd_req_query_login_dev = cmd_code._(36, _omitEnumNames ? '' : 'cmd_req_query_login_dev');
  static const cmd_code cmd_resp_query_login_dev = cmd_code._(37, _omitEnumNames ? '' : 'cmd_resp_query_login_dev');
  static const cmd_code cmd_req_set_speak_time_out_duration = cmd_code._(38, _omitEnumNames ? '' : 'cmd_req_set_speak_time_out_duration');
  static const cmd_code cmd_resp_set_speak_time_out_duration = cmd_code._(39, _omitEnumNames ? '' : 'cmd_resp_set_speak_time_out_duration');
  static const cmd_code cmd_req_get_speak_time_out_duration = cmd_code._(40, _omitEnumNames ? '' : 'cmd_req_get_speak_time_out_duration');
  static const cmd_code cmd_resp_get_speak_time_out_duration = cmd_code._(41, _omitEnumNames ? '' : 'cmd_resp_get_speak_time_out_duration');
  static const cmd_code cmd_req_update_voice_config = cmd_code._(42, _omitEnumNames ? '' : 'cmd_req_update_voice_config');
  static const cmd_code cmd_resp_update_voice_config = cmd_code._(43, _omitEnumNames ? '' : 'cmd_resp_update_voice_config');
  static const cmd_code cmd_req_delete_listen_group = cmd_code._(52, _omitEnumNames ? '' : 'cmd_req_delete_listen_group');
  static const cmd_code cmd_resp_delete_listen_group = cmd_code._(53, _omitEnumNames ? '' : 'cmd_resp_delete_listen_group');
  static const cmd_code cmd_req_add_listen_group = cmd_code._(54, _omitEnumNames ? '' : 'cmd_req_add_listen_group');
  static const cmd_code cmd_resp_add_listen_group = cmd_code._(55, _omitEnumNames ? '' : 'cmd_resp_add_listen_group');
  static const cmd_code cmd_req_login_quit = cmd_code._(56, _omitEnumNames ? '' : 'cmd_req_login_quit');
  static const cmd_code cmd_resp_login_quit = cmd_code._(57, _omitEnumNames ? '' : 'cmd_resp_login_quit');
  static const cmd_code cmd_req_query_default_speak_target = cmd_code._(58, _omitEnumNames ? '' : 'cmd_req_query_default_speak_target');
  static const cmd_code cmd_resp_query_default_speak_target = cmd_code._(59, _omitEnumNames ? '' : 'cmd_resp_query_default_speak_target');
  static const cmd_code cmd_req_query_default_dev_config = cmd_code._(60, _omitEnumNames ? '' : 'cmd_req_query_default_dev_config');
  static const cmd_code cmd_resp_query_default_dev_config = cmd_code._(61, _omitEnumNames ? '' : 'cmd_resp_query_default_dev_config');
  static const cmd_code cmd_req_play_local_cache_media = cmd_code._(62, _omitEnumNames ? '' : 'cmd_req_play_local_cache_media');
  static const cmd_code cmd_resp_play_local_cache_media = cmd_code._(63, _omitEnumNames ? '' : 'cmd_resp_play_local_cache_media');
  static const cmd_code cmd_req_query_call_back_target = cmd_code._(64, _omitEnumNames ? '' : 'cmd_req_query_call_back_target');
  static const cmd_code cmd_resp_query_call_back_target = cmd_code._(65, _omitEnumNames ? '' : 'cmd_resp_query_call_back_target');
  static const cmd_code cmd_req_clear_call_back_target = cmd_code._(66, _omitEnumNames ? '' : 'cmd_req_clear_call_back_target');
  static const cmd_code cmd_resp_clear_call_back_target = cmd_code._(67, _omitEnumNames ? '' : 'cmd_resp_clear_call_back_target');
  static const cmd_code cmd_req_stop_play_local_cache_media = cmd_code._(68, _omitEnumNames ? '' : 'cmd_req_stop_play_local_cache_media');
  static const cmd_code cmd_resp_stop_play_local_cache_media = cmd_code._(69, _omitEnumNames ? '' : 'cmd_resp_stop_play_local_cache_media');
  static const cmd_code cmd_req_set_media_software = cmd_code._(70, _omitEnumNames ? '' : 'cmd_req_set_media_software');
  static const cmd_code cmd_resp_set_media_software = cmd_code._(71, _omitEnumNames ? '' : 'cmd_resp_set_media_software');
  static const cmd_code cmd_req_query_addr_book_by_dmrid = cmd_code._(72, _omitEnumNames ? '' : 'cmd_req_query_addr_book_by_dmrid');
  static const cmd_code cmd_resp_query_addr_book_by_dmrid = cmd_code._(73, _omitEnumNames ? '' : 'cmd_resp_query_addr_book_by_dmrid');
  static const cmd_code cmd_resp_query_addr_book_by_dmrid_proxy = cmd_code._(74, _omitEnumNames ? '' : 'cmd_resp_query_addr_book_by_dmrid_proxy');
  static const cmd_code cmd_req_map_token = cmd_code._(75, _omitEnumNames ? '' : 'cmd_req_map_token');
  static const cmd_code cmd_resp_map_token = cmd_code._(76, _omitEnumNames ? '' : 'cmd_resp_map_token');
  static const cmd_code cmd_req_gps_location_once = cmd_code._(77, _omitEnumNames ? '' : 'cmd_req_gps_location_once');
  static const cmd_code cmd_req_gps_location_on = cmd_code._(78, _omitEnumNames ? '' : 'cmd_req_gps_location_on');
  static const cmd_code cmd_req_gps_location_off = cmd_code._(79, _omitEnumNames ? '' : 'cmd_req_gps_location_off');
  static const cmd_code cmd_got_pcm_data = cmd_code._(80, _omitEnumNames ? '' : 'cmd_got_pcm_data');
  static const cmd_code cmd_req_device_gps_location_permission = cmd_code._(81, _omitEnumNames ? '' : 'cmd_req_device_gps_location_permission');
  static const cmd_code cmd_resp_device_gps_location_permission = cmd_code._(82, _omitEnumNames ? '' : 'cmd_resp_device_gps_location_permission');
  static const cmd_code cmd_req_query_device_gps_location_permission = cmd_code._(83, _omitEnumNames ? '' : 'cmd_req_query_device_gps_location_permission');
  static const cmd_code cmd_resp_query_device_gps_location_permission = cmd_code._(84, _omitEnumNames ? '' : 'cmd_resp_query_device_gps_location_permission');
  static const cmd_code cmd_exit = cmd_code._(1001, _omitEnumNames ? '' : 'cmd_exit');
  static const cmd_code cmd_force_exit = cmd_code._(444, _omitEnumNames ? '' : 'cmd_force_exit');
  static const cmd_code cmd_bc15 = cmd_code._(445, _omitEnumNames ? '' : 'cmd_bc15');
  static const cmd_code cmd_req_query_contact = cmd_code._(1100, _omitEnumNames ? '' : 'cmd_req_query_contact');
  static const cmd_code cmd_resp_query_contact = cmd_code._(1101, _omitEnumNames ? '' : 'cmd_resp_query_contact');
  static const cmd_code cmd_req_query_poc_default_group = cmd_code._(1102, _omitEnumNames ? '' : 'cmd_req_query_poc_default_group');
  static const cmd_code cmd_resp_query_poc_default_group = cmd_code._(1103, _omitEnumNames ? '' : 'cmd_resp_query_poc_default_group');
  static const cmd_code cmd_req_update_poc_listen_group = cmd_code._(1104, _omitEnumNames ? '' : 'cmd_req_update_poc_listen_group');
  static const cmd_code cmd_resp_update_poc_listen_group = cmd_code._(1105, _omitEnumNames ? '' : 'cmd_resp_update_poc_listen_group');
  static const cmd_code cmd_req_query_poc_listen_group = cmd_code._(1106, _omitEnumNames ? '' : 'cmd_req_query_poc_listen_group');
  static const cmd_code cmd_resp_query_poc_listen_group = cmd_code._(1107, _omitEnumNames ? '' : 'cmd_resp_query_poc_listen_group');
  static const cmd_code cmd_req_query_outside_permission_contact = cmd_code._(1108, _omitEnumNames ? '' : 'cmd_req_query_outside_permission_contact');
  static const cmd_code cmd_resp_query_outside_permission_contact = cmd_code._(1109, _omitEnumNames ? '' : 'cmd_resp_query_outside_permission_contact');
  static const cmd_code cmd_notify_poc_setting_changed = cmd_code._(1111, _omitEnumNames ? '' : 'cmd_notify_poc_setting_changed');
  static const cmd_code cmd_req_send_alarm = cmd_code._(1113, _omitEnumNames ? '' : 'cmd_req_send_alarm');
  static const cmd_code cmd_resp_send_alarm = cmd_code._(1114, _omitEnumNames ? '' : 'cmd_resp_send_alarm');
  static const cmd_code cmd_cb10 = cmd_code._(1115, _omitEnumNames ? '' : 'cmd_cb10');
  static const cmd_code cmd_notify_device_status = cmd_code._(1116, _omitEnumNames ? '' : 'cmd_notify_device_status');
  static const cmd_code cmd_notify_lock_device_status = cmd_code._(1117, _omitEnumNames ? '' : 'cmd_notify_lock_device_status');
  static const cmd_code cmd_req_query_poc_config = cmd_code._(1118, _omitEnumNames ? '' : 'cmd_req_query_poc_config');
  static const cmd_code cmd_resp_query_poc_config = cmd_code._(1119, _omitEnumNames ? '' : 'cmd_resp_query_poc_config');
  static const cmd_code cmd_req_query_app_config = cmd_code._(10001, _omitEnumNames ? '' : 'cmd_req_query_app_config');
  static const cmd_code cmd_resp_query_app_config = cmd_code._(10002, _omitEnumNames ? '' : 'cmd_resp_query_app_config');
  static const cmd_code cmd_req_set_app_config = cmd_code._(10003, _omitEnumNames ? '' : 'cmd_req_set_app_config');
  static const cmd_code cmd_resp_set_app_config = cmd_code._(10004, _omitEnumNames ? '' : 'cmd_resp_set_app_config');
  static const cmd_code cmd_req_query_online_contact = cmd_code._(1120, _omitEnumNames ? '' : 'cmd_req_query_online_contact');
  static const cmd_code cmd_resp_query_online_contact = cmd_code._(1121, _omitEnumNames ? '' : 'cmd_resp_query_online_contact');
  static const cmd_code cmd_sync_poc_config_to_proxy = cmd_code._(1122, _omitEnumNames ? '' : 'cmd_sync_poc_config_to_proxy');
  static const cmd_code cmd_notify_login_timeout = cmd_code._(1124, _omitEnumNames ? '' : 'cmd_notify_login_timeout');
  static const cmd_code cmd_notify_init_data_finish = cmd_code._(1125, _omitEnumNames ? '' : 'cmd_notify_init_data_finish');

  static const $core.List<cmd_code> values = <cmd_code> [
    cmd_req_ping,
    cmd_resp_ping,
    cmd_req_login,
    cmd_resp_login,
    cmd_req_update_listen_group_list,
    cmd_resp_update_listen_group_list,
    cmd_req_query_listen_group_list,
    cmd_resp_query_listen_group_list,
    cmd_req_update_speak_target,
    cmd_resp_update_speak_target,
    cmd_req_query_speak_target,
    cmd_resp_query_speak_target,
    cmd_req_query_address_book,
    cmd_resp_query_address_book,
    cmd_req_speak_start,
    cmd_resp_speak_start,
    cmd_req_speak_stop,
    cmd_resp_speak_stop,
    cmd_req_speak_status,
    cmd_resp_speak_status,
    cmd_req_media_play_status,
    cmd_resp_media_play_status,
    cmd_notify,
    cmd_short_messages,
    cmd_resp_confirm_short_messages,
    cmd_send_short_messages,
    cmd_resp_send_short_messages,
    cmd_req_is_login,
    cmd_resp_is_login,
    cmd_req_is_conn_server,
    cmd_resp_is_conn_server,
    cmd_req_update_server_addr,
    cmd_resp_update_server_addr,
    cmd_req_query_login_user,
    cmd_resp_query_login_user,
    cmd_req_query_login_dev,
    cmd_resp_query_login_dev,
    cmd_req_set_speak_time_out_duration,
    cmd_resp_set_speak_time_out_duration,
    cmd_req_get_speak_time_out_duration,
    cmd_resp_get_speak_time_out_duration,
    cmd_req_update_voice_config,
    cmd_resp_update_voice_config,
    cmd_req_delete_listen_group,
    cmd_resp_delete_listen_group,
    cmd_req_add_listen_group,
    cmd_resp_add_listen_group,
    cmd_req_login_quit,
    cmd_resp_login_quit,
    cmd_req_query_default_speak_target,
    cmd_resp_query_default_speak_target,
    cmd_req_query_default_dev_config,
    cmd_resp_query_default_dev_config,
    cmd_req_play_local_cache_media,
    cmd_resp_play_local_cache_media,
    cmd_req_query_call_back_target,
    cmd_resp_query_call_back_target,
    cmd_req_clear_call_back_target,
    cmd_resp_clear_call_back_target,
    cmd_req_stop_play_local_cache_media,
    cmd_resp_stop_play_local_cache_media,
    cmd_req_set_media_software,
    cmd_resp_set_media_software,
    cmd_req_query_addr_book_by_dmrid,
    cmd_resp_query_addr_book_by_dmrid,
    cmd_resp_query_addr_book_by_dmrid_proxy,
    cmd_req_map_token,
    cmd_resp_map_token,
    cmd_req_gps_location_once,
    cmd_req_gps_location_on,
    cmd_req_gps_location_off,
    cmd_got_pcm_data,
    cmd_req_device_gps_location_permission,
    cmd_resp_device_gps_location_permission,
    cmd_req_query_device_gps_location_permission,
    cmd_resp_query_device_gps_location_permission,
    cmd_exit,
    cmd_force_exit,
    cmd_bc15,
    cmd_req_query_contact,
    cmd_resp_query_contact,
    cmd_req_query_poc_default_group,
    cmd_resp_query_poc_default_group,
    cmd_req_update_poc_listen_group,
    cmd_resp_update_poc_listen_group,
    cmd_req_query_poc_listen_group,
    cmd_resp_query_poc_listen_group,
    cmd_req_query_outside_permission_contact,
    cmd_resp_query_outside_permission_contact,
    cmd_notify_poc_setting_changed,
    cmd_req_send_alarm,
    cmd_resp_send_alarm,
    cmd_cb10,
    cmd_notify_device_status,
    cmd_notify_lock_device_status,
    cmd_req_query_poc_config,
    cmd_resp_query_poc_config,
    cmd_req_query_app_config,
    cmd_resp_query_app_config,
    cmd_req_set_app_config,
    cmd_resp_set_app_config,
    cmd_req_query_online_contact,
    cmd_resp_query_online_contact,
    cmd_sync_poc_config_to_proxy,
    cmd_notify_login_timeout,
    cmd_notify_init_data_finish,
  ];

  static final $core.Map<$core.int, cmd_code> _byValue = $pb.ProtobufEnum.initByValue(values);
  static cmd_code? valueOf($core.int value) => _byValue[value];

  const cmd_code._($core.int v, $core.String n) : super(v, n);
}


const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
