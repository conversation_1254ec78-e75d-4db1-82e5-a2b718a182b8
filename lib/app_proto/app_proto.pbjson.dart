//
//  Generated code. Do not modify.
//  source: app_proto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use media_statusDescriptor instead')
const media_status$json = {
  '1': 'media_status',
  '2': [
    {'1': 'start', '2': 0},
    {'1': 'stoped', '2': 1},
  ],
};

/// Descriptor for `media_status`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List media_statusDescriptor = $convert.base64Decode(
    'CgxtZWRpYV9zdGF0dXMSCQoFc3RhcnQQABIKCgZzdG9wZWQQAQ==');

@$core.Deprecated('Use res_codeDescriptor instead')
const res_code$json = {
  '1': 'res_code',
  '2': [
    {'1': 'success', '2': 0},
    {'1': 'fialed', '2': 1},
  ],
};

/// Descriptor for `res_code`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List res_codeDescriptor = $convert.base64Decode(
    'CghyZXNfY29kZRILCgdzdWNjZXNzEAASCgoGZmlhbGVkEAE=');

@$core.Deprecated('Use cmd_codeDescriptor instead')
const cmd_code$json = {
  '1': 'cmd_code',
  '2': [
    {'1': 'cmd_req_ping', '2': 0},
    {'1': 'cmd_resp_ping', '2': 1},
    {'1': 'cmd_req_login', '2': 2},
    {'1': 'cmd_resp_login', '2': 3},
    {'1': 'cmd_req_update_listen_group_list', '2': 4},
    {'1': 'cmd_resp_update_listen_group_list', '2': 5},
    {'1': 'cmd_req_query_listen_group_list', '2': 6},
    {'1': 'cmd_resp_query_listen_group_list', '2': 7},
    {'1': 'cmd_req_update_speak_target', '2': 8},
    {'1': 'cmd_resp_update_speak_target', '2': 9},
    {'1': 'cmd_req_query_speak_target', '2': 10},
    {'1': 'cmd_resp_query_speak_target', '2': 11},
    {'1': 'cmd_req_query_address_book', '2': 12},
    {'1': 'cmd_resp_query_address_book', '2': 13},
    {'1': 'cmd_req_speak_start', '2': 14},
    {'1': 'cmd_resp_speak_start', '2': 15},
    {'1': 'cmd_req_speak_stop', '2': 16},
    {'1': 'cmd_resp_speak_stop', '2': 17},
    {'1': 'cmd_req_speak_status', '2': 18},
    {'1': 'cmd_resp_speak_status', '2': 19},
    {'1': 'cmd_req_media_play_status', '2': 20},
    {'1': 'cmd_resp_media_play_status', '2': 21},
    {'1': 'cmd_notify', '2': 22},
    {'1': 'cmd_short_messages', '2': 24},
    {'1': 'cmd_resp_confirm_short_messages', '2': 25},
    {'1': 'cmd_send_short_messages', '2': 26},
    {'1': 'cmd_resp_send_short_messages', '2': 27},
    {'1': 'cmd_req_is_login', '2': 28},
    {'1': 'cmd_resp_is_login', '2': 29},
    {'1': 'cmd_req_is_conn_server', '2': 30},
    {'1': 'cmd_resp_is_conn_server', '2': 31},
    {'1': 'cmd_req_update_server_addr', '2': 32},
    {'1': 'cmd_resp_update_server_addr', '2': 33},
    {'1': 'cmd_req_query_login_user', '2': 34},
    {'1': 'cmd_resp_query_login_user', '2': 35},
    {'1': 'cmd_req_query_login_dev', '2': 36},
    {'1': 'cmd_resp_query_login_dev', '2': 37},
    {'1': 'cmd_req_set_speak_time_out_duration', '2': 38},
    {'1': 'cmd_resp_set_speak_time_out_duration', '2': 39},
    {'1': 'cmd_req_get_speak_time_out_duration', '2': 40},
    {'1': 'cmd_resp_get_speak_time_out_duration', '2': 41},
    {'1': 'cmd_req_update_voice_config', '2': 42},
    {'1': 'cmd_resp_update_voice_config', '2': 43},
    {'1': 'cmd_req_delete_listen_group', '2': 52},
    {'1': 'cmd_resp_delete_listen_group', '2': 53},
    {'1': 'cmd_req_add_listen_group', '2': 54},
    {'1': 'cmd_resp_add_listen_group', '2': 55},
    {'1': 'cmd_req_login_quit', '2': 56},
    {'1': 'cmd_resp_login_quit', '2': 57},
    {'1': 'cmd_req_query_default_speak_target', '2': 58},
    {'1': 'cmd_resp_query_default_speak_target', '2': 59},
    {'1': 'cmd_req_query_default_dev_config', '2': 60},
    {'1': 'cmd_resp_query_default_dev_config', '2': 61},
    {'1': 'cmd_req_play_local_cache_media', '2': 62},
    {'1': 'cmd_resp_play_local_cache_media', '2': 63},
    {'1': 'cmd_req_query_call_back_target', '2': 64},
    {'1': 'cmd_resp_query_call_back_target', '2': 65},
    {'1': 'cmd_req_clear_call_back_target', '2': 66},
    {'1': 'cmd_resp_clear_call_back_target', '2': 67},
    {'1': 'cmd_req_stop_play_local_cache_media', '2': 68},
    {'1': 'cmd_resp_stop_play_local_cache_media', '2': 69},
    {'1': 'cmd_req_set_media_software', '2': 70},
    {'1': 'cmd_resp_set_media_software', '2': 71},
    {'1': 'cmd_req_query_addr_book_by_dmrid', '2': 72},
    {'1': 'cmd_resp_query_addr_book_by_dmrid', '2': 73},
    {'1': 'cmd_resp_query_addr_book_by_dmrid_proxy', '2': 74},
    {'1': 'cmd_req_map_token', '2': 75},
    {'1': 'cmd_resp_map_token', '2': 76},
    {'1': 'cmd_req_gps_location_once', '2': 77},
    {'1': 'cmd_req_gps_location_on', '2': 78},
    {'1': 'cmd_req_gps_location_off', '2': 79},
    {'1': 'cmd_got_pcm_data', '2': 80},
    {'1': 'cmd_req_device_gps_location_permission', '2': 81},
    {'1': 'cmd_resp_device_gps_location_permission', '2': 82},
    {'1': 'cmd_req_query_device_gps_location_permission', '2': 83},
    {'1': 'cmd_resp_query_device_gps_location_permission', '2': 84},
    {'1': 'cmd_exit', '2': 1001},
    {'1': 'cmd_force_exit', '2': 444},
    {'1': 'cmd_bc15', '2': 445},
    {'1': 'cmd_req_query_contact', '2': 1100},
    {'1': 'cmd_resp_query_contact', '2': 1101},
    {'1': 'cmd_req_query_poc_default_group', '2': 1102},
    {'1': 'cmd_resp_query_poc_default_group', '2': 1103},
    {'1': 'cmd_req_update_poc_listen_group', '2': 1104},
    {'1': 'cmd_resp_update_poc_listen_group', '2': 1105},
    {'1': 'cmd_req_query_poc_listen_group', '2': 1106},
    {'1': 'cmd_resp_query_poc_listen_group', '2': 1107},
    {'1': 'cmd_req_query_outside_permission_contact', '2': 1108},
    {'1': 'cmd_resp_query_outside_permission_contact', '2': 1109},
    {'1': 'cmd_notify_poc_setting_changed', '2': 1111},
    {'1': 'cmd_req_send_alarm', '2': 1113},
    {'1': 'cmd_resp_send_alarm', '2': 1114},
    {'1': 'cmd_cb10', '2': 1115},
    {'1': 'cmd_notify_device_status', '2': 1116},
    {'1': 'cmd_notify_lock_device_status', '2': 1117},
    {'1': 'cmd_req_query_poc_config', '2': 1118},
    {'1': 'cmd_resp_query_poc_config', '2': 1119},
    {'1': 'cmd_req_query_app_config', '2': 10001},
    {'1': 'cmd_resp_query_app_config', '2': 10002},
    {'1': 'cmd_req_set_app_config', '2': 10003},
    {'1': 'cmd_resp_set_app_config', '2': 10004},
    {'1': 'cmd_req_query_online_contact', '2': 1120},
    {'1': 'cmd_resp_query_online_contact', '2': 1121},
    {'1': 'cmd_sync_poc_config_to_proxy', '2': 1122},
    {'1': 'cmd_notify_login_timeout', '2': 1124},
    {'1': 'cmd_notify_init_data_finish', '2': 1125},
  ],
};

/// Descriptor for `cmd_code`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List cmd_codeDescriptor = $convert.base64Decode(
    'CghjbWRfY29kZRIQCgxjbWRfcmVxX3BpbmcQABIRCg1jbWRfcmVzcF9waW5nEAESEQoNY21kX3'
    'JlcV9sb2dpbhACEhIKDmNtZF9yZXNwX2xvZ2luEAMSJAogY21kX3JlcV91cGRhdGVfbGlzdGVu'
    'X2dyb3VwX2xpc3QQBBIlCiFjbWRfcmVzcF91cGRhdGVfbGlzdGVuX2dyb3VwX2xpc3QQBRIjCh'
    '9jbWRfcmVxX3F1ZXJ5X2xpc3Rlbl9ncm91cF9saXN0EAYSJAogY21kX3Jlc3BfcXVlcnlfbGlz'
    'dGVuX2dyb3VwX2xpc3QQBxIfChtjbWRfcmVxX3VwZGF0ZV9zcGVha190YXJnZXQQCBIgChxjbW'
    'RfcmVzcF91cGRhdGVfc3BlYWtfdGFyZ2V0EAkSHgoaY21kX3JlcV9xdWVyeV9zcGVha190YXJn'
    'ZXQQChIfChtjbWRfcmVzcF9xdWVyeV9zcGVha190YXJnZXQQCxIeChpjbWRfcmVxX3F1ZXJ5X2'
    'FkZHJlc3NfYm9vaxAMEh8KG2NtZF9yZXNwX3F1ZXJ5X2FkZHJlc3NfYm9vaxANEhcKE2NtZF9y'
    'ZXFfc3BlYWtfc3RhcnQQDhIYChRjbWRfcmVzcF9zcGVha19zdGFydBAPEhYKEmNtZF9yZXFfc3'
    'BlYWtfc3RvcBAQEhcKE2NtZF9yZXNwX3NwZWFrX3N0b3AQERIYChRjbWRfcmVxX3NwZWFrX3N0'
    'YXR1cxASEhkKFWNtZF9yZXNwX3NwZWFrX3N0YXR1cxATEh0KGWNtZF9yZXFfbWVkaWFfcGxheV'
    '9zdGF0dXMQFBIeChpjbWRfcmVzcF9tZWRpYV9wbGF5X3N0YXR1cxAVEg4KCmNtZF9ub3RpZnkQ'
    'FhIWChJjbWRfc2hvcnRfbWVzc2FnZXMQGBIjCh9jbWRfcmVzcF9jb25maXJtX3Nob3J0X21lc3'
    'NhZ2VzEBkSGwoXY21kX3NlbmRfc2hvcnRfbWVzc2FnZXMQGhIgChxjbWRfcmVzcF9zZW5kX3No'
    'b3J0X21lc3NhZ2VzEBsSFAoQY21kX3JlcV9pc19sb2dpbhAcEhUKEWNtZF9yZXNwX2lzX2xvZ2'
    'luEB0SGgoWY21kX3JlcV9pc19jb25uX3NlcnZlchAeEhsKF2NtZF9yZXNwX2lzX2Nvbm5fc2Vy'
    'dmVyEB8SHgoaY21kX3JlcV91cGRhdGVfc2VydmVyX2FkZHIQIBIfChtjbWRfcmVzcF91cGRhdG'
    'Vfc2VydmVyX2FkZHIQIRIcChhjbWRfcmVxX3F1ZXJ5X2xvZ2luX3VzZXIQIhIdChljbWRfcmVz'
    'cF9xdWVyeV9sb2dpbl91c2VyECMSGwoXY21kX3JlcV9xdWVyeV9sb2dpbl9kZXYQJBIcChhjbW'
    'RfcmVzcF9xdWVyeV9sb2dpbl9kZXYQJRInCiNjbWRfcmVxX3NldF9zcGVha190aW1lX291dF9k'
    'dXJhdGlvbhAmEigKJGNtZF9yZXNwX3NldF9zcGVha190aW1lX291dF9kdXJhdGlvbhAnEicKI2'
    'NtZF9yZXFfZ2V0X3NwZWFrX3RpbWVfb3V0X2R1cmF0aW9uECgSKAokY21kX3Jlc3BfZ2V0X3Nw'
    'ZWFrX3RpbWVfb3V0X2R1cmF0aW9uECkSHwobY21kX3JlcV91cGRhdGVfdm9pY2VfY29uZmlnEC'
    'oSIAocY21kX3Jlc3BfdXBkYXRlX3ZvaWNlX2NvbmZpZxArEh8KG2NtZF9yZXFfZGVsZXRlX2xp'
    'c3Rlbl9ncm91cBA0EiAKHGNtZF9yZXNwX2RlbGV0ZV9saXN0ZW5fZ3JvdXAQNRIcChhjbWRfcm'
    'VxX2FkZF9saXN0ZW5fZ3JvdXAQNhIdChljbWRfcmVzcF9hZGRfbGlzdGVuX2dyb3VwEDcSFgoS'
    'Y21kX3JlcV9sb2dpbl9xdWl0EDgSFwoTY21kX3Jlc3BfbG9naW5fcXVpdBA5EiYKImNtZF9yZX'
    'FfcXVlcnlfZGVmYXVsdF9zcGVha190YXJnZXQQOhInCiNjbWRfcmVzcF9xdWVyeV9kZWZhdWx0'
    'X3NwZWFrX3RhcmdldBA7EiQKIGNtZF9yZXFfcXVlcnlfZGVmYXVsdF9kZXZfY29uZmlnEDwSJQ'
    'ohY21kX3Jlc3BfcXVlcnlfZGVmYXVsdF9kZXZfY29uZmlnED0SIgoeY21kX3JlcV9wbGF5X2xv'
    'Y2FsX2NhY2hlX21lZGlhED4SIwofY21kX3Jlc3BfcGxheV9sb2NhbF9jYWNoZV9tZWRpYRA/Ei'
    'IKHmNtZF9yZXFfcXVlcnlfY2FsbF9iYWNrX3RhcmdldBBAEiMKH2NtZF9yZXNwX3F1ZXJ5X2Nh'
    'bGxfYmFja190YXJnZXQQQRIiCh5jbWRfcmVxX2NsZWFyX2NhbGxfYmFja190YXJnZXQQQhIjCh'
    '9jbWRfcmVzcF9jbGVhcl9jYWxsX2JhY2tfdGFyZ2V0EEMSJwojY21kX3JlcV9zdG9wX3BsYXlf'
    'bG9jYWxfY2FjaGVfbWVkaWEQRBIoCiRjbWRfcmVzcF9zdG9wX3BsYXlfbG9jYWxfY2FjaGVfbW'
    'VkaWEQRRIeChpjbWRfcmVxX3NldF9tZWRpYV9zb2Z0d2FyZRBGEh8KG2NtZF9yZXNwX3NldF9t'
    'ZWRpYV9zb2Z0d2FyZRBHEiQKIGNtZF9yZXFfcXVlcnlfYWRkcl9ib29rX2J5X2RtcmlkEEgSJQ'
    'ohY21kX3Jlc3BfcXVlcnlfYWRkcl9ib29rX2J5X2RtcmlkEEkSKwonY21kX3Jlc3BfcXVlcnlf'
    'YWRkcl9ib29rX2J5X2RtcmlkX3Byb3h5EEoSFQoRY21kX3JlcV9tYXBfdG9rZW4QSxIWChJjbW'
    'RfcmVzcF9tYXBfdG9rZW4QTBIdChljbWRfcmVxX2dwc19sb2NhdGlvbl9vbmNlEE0SGwoXY21k'
    'X3JlcV9ncHNfbG9jYXRpb25fb24QThIcChhjbWRfcmVxX2dwc19sb2NhdGlvbl9vZmYQTxIUCh'
    'BjbWRfZ290X3BjbV9kYXRhEFASKgomY21kX3JlcV9kZXZpY2VfZ3BzX2xvY2F0aW9uX3Blcm1p'
    'c3Npb24QURIrCidjbWRfcmVzcF9kZXZpY2VfZ3BzX2xvY2F0aW9uX3Blcm1pc3Npb24QUhIwCi'
    'xjbWRfcmVxX3F1ZXJ5X2RldmljZV9ncHNfbG9jYXRpb25fcGVybWlzc2lvbhBTEjEKLWNtZF9y'
    'ZXNwX3F1ZXJ5X2RldmljZV9ncHNfbG9jYXRpb25fcGVybWlzc2lvbhBUEg0KCGNtZF9leGl0EO'
    'kHEhMKDmNtZF9mb3JjZV9leGl0ELwDEg0KCGNtZF9iYzE1EL0DEhoKFWNtZF9yZXFfcXVlcnlf'
    'Y29udGFjdBDMCBIbChZjbWRfcmVzcF9xdWVyeV9jb250YWN0EM0IEiQKH2NtZF9yZXFfcXVlcn'
    'lfcG9jX2RlZmF1bHRfZ3JvdXAQzggSJQogY21kX3Jlc3BfcXVlcnlfcG9jX2RlZmF1bHRfZ3Jv'
    'dXAQzwgSJAofY21kX3JlcV91cGRhdGVfcG9jX2xpc3Rlbl9ncm91cBDQCBIlCiBjbWRfcmVzcF'
    '91cGRhdGVfcG9jX2xpc3Rlbl9ncm91cBDRCBIjCh5jbWRfcmVxX3F1ZXJ5X3BvY19saXN0ZW5f'
    'Z3JvdXAQ0ggSJAofY21kX3Jlc3BfcXVlcnlfcG9jX2xpc3Rlbl9ncm91cBDTCBItCihjbWRfcm'
    'VxX3F1ZXJ5X291dHNpZGVfcGVybWlzc2lvbl9jb250YWN0ENQIEi4KKWNtZF9yZXNwX3F1ZXJ5'
    'X291dHNpZGVfcGVybWlzc2lvbl9jb250YWN0ENUIEiMKHmNtZF9ub3RpZnlfcG9jX3NldHRpbm'
    'dfY2hhbmdlZBDXCBIXChJjbWRfcmVxX3NlbmRfYWxhcm0Q2QgSGAoTY21kX3Jlc3Bfc2VuZF9h'
    'bGFybRDaCBINCghjbWRfY2IxMBDbCBIdChhjbWRfbm90aWZ5X2RldmljZV9zdGF0dXMQ3AgSIg'
    'odY21kX25vdGlmeV9sb2NrX2RldmljZV9zdGF0dXMQ3QgSHQoYY21kX3JlcV9xdWVyeV9wb2Nf'
    'Y29uZmlnEN4IEh4KGWNtZF9yZXNwX3F1ZXJ5X3BvY19jb25maWcQ3wgSHQoYY21kX3JlcV9xdW'
    'VyeV9hcHBfY29uZmlnEJFOEh4KGWNtZF9yZXNwX3F1ZXJ5X2FwcF9jb25maWcQkk4SGwoWY21k'
    'X3JlcV9zZXRfYXBwX2NvbmZpZxCTThIcChdjbWRfcmVzcF9zZXRfYXBwX2NvbmZpZxCUThIhCh'
    'xjbWRfcmVxX3F1ZXJ5X29ubGluZV9jb250YWN0EOAIEiIKHWNtZF9yZXNwX3F1ZXJ5X29ubGlu'
    'ZV9jb250YWN0EOEIEiEKHGNtZF9zeW5jX3BvY19jb25maWdfdG9fcHJveHkQ4ggSHQoYY21kX2'
    '5vdGlmeV9sb2dpbl90aW1lb3V0EOQIEiAKG2NtZF9ub3RpZnlfaW5pdF9kYXRhX2ZpbmlzaBDl'
    'CA==');

@$core.Deprecated('Use req_loginDescriptor instead')
const req_login$json = {
  '1': 'req_login',
  '2': [
    {'1': 'sys_id', '3': 1, '4': 1, '5': 9, '10': 'sysId'},
    {'1': 'user_name', '3': 2, '4': 1, '5': 9, '10': 'userName'},
    {'1': 'user_pass', '3': 3, '4': 1, '5': 9, '10': 'userPass'},
    {'1': 'login_method', '3': 4, '4': 1, '5': 5, '10': 'loginMethod'},
    {'1': 'can_display_map', '3': 5, '4': 1, '5': 8, '10': 'canDisplayMap'},
    {'1': 'prefer_codec', '3': 6, '4': 1, '5': 5, '10': 'preferCodec'},
  ],
};

/// Descriptor for `req_login`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List req_loginDescriptor = $convert.base64Decode(
    'CglyZXFfbG9naW4SFQoGc3lzX2lkGAEgASgJUgVzeXNJZBIbCgl1c2VyX25hbWUYAiABKAlSCH'
    'VzZXJOYW1lEhsKCXVzZXJfcGFzcxgDIAEoCVIIdXNlclBhc3MSIQoMbG9naW5fbWV0aG9kGAQg'
    'ASgFUgtsb2dpbk1ldGhvZBImCg9jYW5fZGlzcGxheV9tYXAYBSABKAhSDWNhbkRpc3BsYXlNYX'
    'ASIQoMcHJlZmVyX2NvZGVjGAYgASgFUgtwcmVmZXJDb2RlYw==');

@$core.Deprecated('Use resp_loginDescriptor instead')
const resp_login$json = {
  '1': 'resp_login',
  '2': [
    {'1': 'is_server_support_map', '3': 1, '4': 1, '5': 8, '10': 'isServerSupportMap'},
    {'1': 'server_version', '3': 2, '4': 1, '5': 9, '10': 'serverVersion'},
    {'1': 'setting_last_update_time', '3': 3, '4': 1, '5': 9, '10': 'settingLastUpdateTime'},
    {'1': 'device', '3': 4, '4': 1, '5': 11, '6': '.app_proto.db_device', '10': 'device'},
  ],
};

/// Descriptor for `resp_login`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List resp_loginDescriptor = $convert.base64Decode(
    'CgpyZXNwX2xvZ2luEjEKFWlzX3NlcnZlcl9zdXBwb3J0X21hcBgBIAEoCFISaXNTZXJ2ZXJTdX'
    'Bwb3J0TWFwEiUKDnNlcnZlcl92ZXJzaW9uGAIgASgJUg1zZXJ2ZXJWZXJzaW9uEjcKGHNldHRp'
    'bmdfbGFzdF91cGRhdGVfdGltZRgDIAEoCVIVc2V0dGluZ0xhc3RVcGRhdGVUaW1lEiwKBmRldm'
    'ljZRgEIAEoCzIULmFwcF9wcm90by5kYl9kZXZpY2VSBmRldmljZQ==');

@$core.Deprecated('Use req_gps_permissionDescriptor instead')
const req_gps_permission$json = {
  '1': 'req_gps_permission',
  '2': [
    {'1': 'dmrid', '3': 1, '4': 1, '5': 13, '10': 'dmrid'},
    {'1': 'apply_dmrid', '3': 2, '4': 1, '5': 13, '10': 'applyDmrid'},
  ],
};

/// Descriptor for `req_gps_permission`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List req_gps_permissionDescriptor = $convert.base64Decode(
    'ChJyZXFfZ3BzX3Blcm1pc3Npb24SFAoFZG1yaWQYASABKA1SBWRtcmlkEh8KC2FwcGx5X2Rtcm'
    'lkGAIgASgNUgphcHBseURtcmlk');

@$core.Deprecated('Use res_gps_permissionDescriptor instead')
const res_gps_permission$json = {
  '1': 'res_gps_permission',
  '2': [
    {'1': 'code', '3': 1, '4': 1, '5': 5, '10': 'code'},
    {'1': 'dmrid', '3': 2, '4': 1, '5': 13, '10': 'dmrid'},
    {'1': 'grant_user_rid', '3': 3, '4': 1, '5': 9, '10': 'grantUserRid'},
    {'1': 'grant_user_name', '3': 4, '4': 1, '5': 9, '10': 'grantUserName'},
    {'1': 'expire_time', '3': 5, '4': 1, '5': 9, '10': 'expireTime'},
  ],
};

/// Descriptor for `res_gps_permission`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List res_gps_permissionDescriptor = $convert.base64Decode(
    'ChJyZXNfZ3BzX3Blcm1pc3Npb24SEgoEY29kZRgBIAEoBVIEY29kZRIUCgVkbXJpZBgCIAEoDV'
    'IFZG1yaWQSJAoOZ3JhbnRfdXNlcl9yaWQYAyABKAlSDGdyYW50VXNlclJpZBImCg9ncmFudF91'
    'c2VyX25hbWUYBCABKAlSDWdyYW50VXNlck5hbWUSHwoLZXhwaXJlX3RpbWUYBSABKAlSCmV4cG'
    'lyZVRpbWU=');

@$core.Deprecated('Use gps84Descriptor instead')
const gps84$json = {
  '1': 'gps84',
  '2': [
    {'1': 'gps_time', '3': 1, '4': 1, '5': 9, '10': 'gpsTime'},
    {'1': 'av', '3': 2, '4': 1, '5': 5, '10': 'av'},
    {'1': 'lat', '3': 3, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'lon', '3': 4, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'speed', '3': 5, '4': 1, '5': 1, '10': 'speed'},
    {'1': 'direction', '3': 6, '4': 1, '5': 5, '10': 'direction'},
    {'1': 'altitude', '3': 7, '4': 1, '5': 5, '10': 'altitude'},
  ],
};

/// Descriptor for `gps84`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List gps84Descriptor = $convert.base64Decode(
    'CgVncHM4NBIZCghncHNfdGltZRgBIAEoCVIHZ3BzVGltZRIOCgJhdhgCIAEoBVICYXYSEAoDbG'
    'F0GAMgASgBUgNsYXQSEAoDbG9uGAQgASgBUgNsb24SFAoFc3BlZWQYBSABKAFSBXNwZWVkEhwK'
    'CWRpcmVjdGlvbhgGIAEoBVIJZGlyZWN0aW9uEhoKCGFsdGl0dWRlGAcgASgFUghhbHRpdHVkZQ'
    '==');

@$core.Deprecated('Use gps_infoDescriptor instead')
const gps_info$json = {
  '1': 'gps_info',
  '2': [
    {'1': 'dmrid', '3': 1, '4': 1, '5': 9, '10': 'dmrid'},
    {'1': 'gps_info', '3': 3, '4': 1, '5': 11, '6': '.app_proto.gps84', '10': 'gpsInfo'},
    {'1': 'active_status', '3': 4, '4': 1, '5': 5, '10': 'activeStatus'},
  ],
};

/// Descriptor for `gps_info`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List gps_infoDescriptor = $convert.base64Decode(
    'CghncHNfaW5mbxIUCgVkbXJpZBgBIAEoCVIFZG1yaWQSKwoIZ3BzX2luZm8YAyABKAsyEC5hcH'
    'BfcHJvdG8uZ3BzODRSB2dwc0luZm8SIwoNYWN0aXZlX3N0YXR1cxgEIAEoBVIMYWN0aXZlU3Rh'
    'dHVz');

@$core.Deprecated('Use req_update_listen_group_listDescriptor instead')
const req_update_listen_group_list$json = {
  '1': 'req_update_listen_group_list',
  '2': [
    {'1': 'listen_group_list', '3': 1, '4': 3, '5': 9, '10': 'listenGroupList'},
  ],
};

/// Descriptor for `req_update_listen_group_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List req_update_listen_group_listDescriptor = $convert.base64Decode(
    'ChxyZXFfdXBkYXRlX2xpc3Rlbl9ncm91cF9saXN0EioKEWxpc3Rlbl9ncm91cF9saXN0GAEgAy'
    'gJUg9saXN0ZW5Hcm91cExpc3Q=');

@$core.Deprecated('Use req_update_speak_targetDescriptor instead')
const req_update_speak_target$json = {
  '1': 'req_update_speak_target',
  '2': [
    {'1': 'speak_target', '3': 1, '4': 1, '5': 9, '10': 'speakTarget'},
  ],
};

/// Descriptor for `req_update_speak_target`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List req_update_speak_targetDescriptor = $convert.base64Decode(
    'ChdyZXFfdXBkYXRlX3NwZWFrX3RhcmdldBIhCgxzcGVha190YXJnZXQYASABKAlSC3NwZWFrVG'
    'FyZ2V0');

@$core.Deprecated('Use address_bookDescriptor instead')
const address_book$json = {
  '1': 'address_book',
  '2': [
    {'1': 'parent_dmrid', '3': 1, '4': 1, '5': 9, '10': 'parentDmrid'},
    {'1': 'dmrid', '3': 2, '4': 1, '5': 9, '10': 'dmrid'},
    {'1': 'name', '3': 3, '4': 1, '5': 9, '10': 'name'},
    {'1': 'devType', '3': 4, '4': 1, '5': 5, '10': 'devType'},
    {'1': 'org_sort_value', '3': 5, '4': 1, '5': 5, '10': 'orgSortValue'},
  ],
};

/// Descriptor for `address_book`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List address_bookDescriptor = $convert.base64Decode(
    'CgxhZGRyZXNzX2Jvb2sSIQoMcGFyZW50X2RtcmlkGAEgASgJUgtwYXJlbnREbXJpZBIUCgVkbX'
    'JpZBgCIAEoCVIFZG1yaWQSEgoEbmFtZRgDIAEoCVIEbmFtZRIYCgdkZXZUeXBlGAQgASgFUgdk'
    'ZXZUeXBlEiQKDm9yZ19zb3J0X3ZhbHVlGAUgASgFUgxvcmdTb3J0VmFsdWU=');

@$core.Deprecated('Use req_address_book_resultDescriptor instead')
const req_address_book_result$json = {
  '1': 'req_address_book_result',
  '2': [
    {'1': 'success_list', '3': 1, '4': 3, '5': 11, '6': '.app_proto.address_book', '10': 'successList'},
    {'1': 'failed_list', '3': 2, '4': 3, '5': 9, '10': 'failedList'},
  ],
};

/// Descriptor for `req_address_book_result`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List req_address_book_resultDescriptor = $convert.base64Decode(
    'ChdyZXFfYWRkcmVzc19ib29rX3Jlc3VsdBI6CgxzdWNjZXNzX2xpc3QYASADKAsyFy5hcHBfcH'
    'JvdG8uYWRkcmVzc19ib29rUgtzdWNjZXNzTGlzdBIfCgtmYWlsZWRfbGlzdBgCIAMoCVIKZmFp'
    'bGVkTGlzdA==');

@$core.Deprecated('Use address_book_listDescriptor instead')
const address_book_list$json = {
  '1': 'address_book_list',
  '2': [
    {'1': 'addr_book_list', '3': 1, '4': 3, '5': 11, '6': '.app_proto.address_book', '10': 'addrBookList'},
  ],
};

/// Descriptor for `address_book_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List address_book_listDescriptor = $convert.base64Decode(
    'ChFhZGRyZXNzX2Jvb2tfbGlzdBI9Cg5hZGRyX2Jvb2tfbGlzdBgBIAMoCzIXLmFwcF9wcm90by'
    '5hZGRyZXNzX2Jvb2tSDGFkZHJCb29rTGlzdA==');

@$core.Deprecated('Use server_addrDescriptor instead')
const server_addr$json = {
  '1': 'server_addr',
  '2': [
    {'1': 'host', '3': 1, '4': 1, '5': 9, '10': 'host'},
    {'1': 'port', '3': 2, '4': 1, '5': 5, '10': 'port'},
  ],
};

/// Descriptor for `server_addr`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List server_addrDescriptor = $convert.base64Decode(
    'CgtzZXJ2ZXJfYWRkchISCgRob3N0GAEgASgJUgRob3N0EhIKBHBvcnQYAiABKAVSBHBvcnQ=');

@$core.Deprecated('Use short_messagesDescriptor instead')
const short_messages$json = {
  '1': 'short_messages',
  '2': [
    {'1': 'sender_dmrid', '3': 4, '4': 1, '5': 9, '10': 'senderDmrid'},
    {'1': 'target_dmrid', '3': 5, '4': 1, '5': 9, '10': 'targetDmrid'},
    {'1': 'sms_content', '3': 7, '4': 1, '5': 9, '10': 'smsContent'},
    {'1': 'sms_no', '3': 8, '4': 1, '5': 5, '10': 'smsNo'},
    {'1': 'codec', '3': 10, '4': 1, '5': 5, '10': 'codec'},
    {'1': 'sms_type', '3': 11, '4': 1, '5': 5, '10': 'smsType'},
    {'1': 'time', '3': 12, '4': 1, '5': 3, '10': 'time'},
  ],
};

/// Descriptor for `short_messages`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List short_messagesDescriptor = $convert.base64Decode(
    'Cg5zaG9ydF9tZXNzYWdlcxIhCgxzZW5kZXJfZG1yaWQYBCABKAlSC3NlbmRlckRtcmlkEiEKDH'
    'RhcmdldF9kbXJpZBgFIAEoCVILdGFyZ2V0RG1yaWQSHwoLc21zX2NvbnRlbnQYByABKAlSCnNt'
    'c0NvbnRlbnQSFQoGc21zX25vGAggASgFUgVzbXNObxIUCgVjb2RlYxgKIAEoBVIFY29kZWMSGQ'
    'oIc21zX3R5cGUYCyABKAVSB3Ntc1R5cGUSEgoEdGltZRgMIAEoA1IEdGltZQ==');

@$core.Deprecated('Use notifyDescriptor instead')
const Notify$json = {
  '1': 'Notify',
  '2': [
    {'1': 'code', '3': 1, '4': 1, '5': 5, '10': 'code'},
    {'1': 'paramStr', '3': 2, '4': 1, '5': 9, '10': 'paramStr'},
    {'1': 'body', '3': 3, '4': 1, '5': 12, '10': 'body'},
  ],
};

/// Descriptor for `Notify`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List notifyDescriptor = $convert.base64Decode(
    'CgZOb3RpZnkSEgoEY29kZRgBIAEoBVIEY29kZRIaCghwYXJhbVN0chgCIAEoCVIIcGFyYW1TdH'
    'ISEgoEYm9keRgDIAEoDFIEYm9keQ==');

@$core.Deprecated('Use voice_configDescriptor instead')
const voice_config$json = {
  '1': 'voice_config',
  '2': [
    {'1': 'gain', '3': 3, '4': 1, '5': 5, '10': 'gain'},
    {'1': 'mediaAutoStartSize', '3': 4, '4': 1, '5': 5, '10': 'mediaAutoStartSize'},
    {'1': 'mediaBufferSize', '3': 5, '4': 1, '5': 5, '10': 'mediaBufferSize'},
    {'1': 'speakTimeout', '3': 6, '4': 1, '5': 5, '10': 'speakTimeout'},
    {'1': 'denoiseSetting', '3': 8, '4': 1, '5': 5, '10': 'denoiseSetting'},
    {'1': 'debugRecorderPcm', '3': 9, '4': 1, '5': 5, '10': 'debugRecorderPcm'},
    {'1': 'recorderPcmGain', '3': 10, '4': 1, '5': 5, '10': 'recorderPcmGain'},
  ],
};

/// Descriptor for `voice_config`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List voice_configDescriptor = $convert.base64Decode(
    'Cgx2b2ljZV9jb25maWcSEgoEZ2FpbhgDIAEoBVIEZ2FpbhIuChJtZWRpYUF1dG9TdGFydFNpem'
    'UYBCABKAVSEm1lZGlhQXV0b1N0YXJ0U2l6ZRIoCg9tZWRpYUJ1ZmZlclNpemUYBSABKAVSD21l'
    'ZGlhQnVmZmVyU2l6ZRIiCgxzcGVha1RpbWVvdXQYBiABKAVSDHNwZWFrVGltZW91dBImCg5kZW'
    '5vaXNlU2V0dGluZxgIIAEoBVIOZGVub2lzZVNldHRpbmcSKgoQZGVidWdSZWNvcmRlclBjbRgJ'
    'IAEoBVIQZGVidWdSZWNvcmRlclBjbRIoCg9yZWNvcmRlclBjbUdhaW4YCiABKAVSD3JlY29yZG'
    'VyUGNtR2Fpbg==');

