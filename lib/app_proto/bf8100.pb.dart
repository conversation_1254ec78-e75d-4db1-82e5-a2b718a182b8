//
//  Generated code. Do not modify.
//  source: bf8100.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'bf8100.pbenum.dart';

export 'bf8100.pbenum.dart';

/// 系统中所有的消息交互都以此为包装
class rpc_cmd extends $pb.GeneratedMessage {
  factory rpc_cmd({
    $core.int? seqNo,
    $fixnum.Int64? sid,
    $core.int? cmd,
    $core.int? res,
    $core.List<$core.int>? body,
    $core.String? paraStr,
    $core.List<$core.int>? paraBin,
    $fixnum.Int64? paraInt,
  }) {
    final $result = create();
    if (seqNo != null) {
      $result.seqNo = seqNo;
    }
    if (sid != null) {
      $result.sid = sid;
    }
    if (cmd != null) {
      $result.cmd = cmd;
    }
    if (res != null) {
      $result.res = res;
    }
    if (body != null) {
      $result.body = body;
    }
    if (paraStr != null) {
      $result.paraStr = paraStr;
    }
    if (paraBin != null) {
      $result.paraBin = paraBin;
    }
    if (paraInt != null) {
      $result.paraInt = paraInt;
    }
    return $result;
  }
  rpc_cmd._() : super();
  factory rpc_cmd.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory rpc_cmd.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'rpc_cmd', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'seqNo', $pb.PbFieldType.O3)
    ..aInt64(3, _omitFieldNames ? '' : 'sid')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'cmd', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'res', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(10, _omitFieldNames ? '' : 'body', $pb.PbFieldType.OY)
    ..aOS(11, _omitFieldNames ? '' : 'paraStr')
    ..a<$core.List<$core.int>>(12, _omitFieldNames ? '' : 'paraBin', $pb.PbFieldType.OY)
    ..aInt64(13, _omitFieldNames ? '' : 'paraInt')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  rpc_cmd clone() => rpc_cmd()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  rpc_cmd copyWith(void Function(rpc_cmd) updates) => super.copyWith((message) => updates(message as rpc_cmd)) as rpc_cmd;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static rpc_cmd create() => rpc_cmd._();
  rpc_cmd createEmptyInstance() => create();
  static $pb.PbList<rpc_cmd> createRepeated() => $pb.PbList<rpc_cmd>();
  @$core.pragma('dart2js:noInline')
  static rpc_cmd getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<rpc_cmd>(create);
  static rpc_cmd? _defaultInstance;

  /// sequence no
  @$pb.TagNumber(2)
  $core.int get seqNo => $_getIZ(0);
  @$pb.TagNumber(2)
  set seqNo($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(2)
  $core.bool hasSeqNo() => $_has(0);
  @$pb.TagNumber(2)
  void clearSeqNo() => clearField(2);

  /// session id
  @$pb.TagNumber(3)
  $fixnum.Int64 get sid => $_getI64(1);
  @$pb.TagNumber(3)
  set sid($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasSid() => $_has(1);
  @$pb.TagNumber(3)
  void clearSid() => clearField(3);

  /// rpc command code
  @$pb.TagNumber(5)
  $core.int get cmd => $_getIZ(2);
  @$pb.TagNumber(5)
  set cmd($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasCmd() => $_has(2);
  @$pb.TagNumber(5)
  void clearCmd() => clearField(5);

  /// response code
  @$pb.TagNumber(8)
  $core.int get res => $_getIZ(3);
  @$pb.TagNumber(8)
  set res($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(8)
  $core.bool hasRes() => $_has(3);
  @$pb.TagNumber(8)
  void clearRes() => clearField(8);

  /// command body
  @$pb.TagNumber(10)
  $core.List<$core.int> get body => $_getN(4);
  @$pb.TagNumber(10)
  set body($core.List<$core.int> v) { $_setBytes(4, v); }
  @$pb.TagNumber(10)
  $core.bool hasBody() => $_has(4);
  @$pb.TagNumber(10)
  void clearBody() => clearField(10);

  /// optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  @$pb.TagNumber(11)
  $core.String get paraStr => $_getSZ(5);
  @$pb.TagNumber(11)
  set paraStr($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(11)
  $core.bool hasParaStr() => $_has(5);
  @$pb.TagNumber(11)
  void clearParaStr() => clearField(11);

  /// optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  @$pb.TagNumber(12)
  $core.List<$core.int> get paraBin => $_getN(6);
  @$pb.TagNumber(12)
  set paraBin($core.List<$core.int> v) { $_setBytes(6, v); }
  @$pb.TagNumber(12)
  $core.bool hasParaBin() => $_has(6);
  @$pb.TagNumber(12)
  void clearParaBin() => clearField(12);

  /// optional int64 parameter
  @$pb.TagNumber(13)
  $fixnum.Int64 get paraInt => $_getI64(7);
  @$pb.TagNumber(13)
  set paraInt($fixnum.Int64 v) { $_setInt64(7, v); }
  @$pb.TagNumber(13)
  $core.bool hasParaInt() => $_has(7);
  @$pb.TagNumber(13)
  void clearParaInt() => clearField(13);
}

/// 登录请求   rpc.cmd=100
class login extends $pb.GeneratedMessage {
  factory login({
    $core.int? deviceDmrid,
    $core.String? deviceName,
    $core.int? loginType,
    $core.String? deviceModel,
    $core.String? password,
    $core.int? passwordMethod,
    $core.String? timeStr,
    $core.String? sysId,
    $core.Iterable<$core.int>? extraOption,
    $core.Iterable<$core.int>? codec,
  }) {
    final $result = create();
    if (deviceDmrid != null) {
      $result.deviceDmrid = deviceDmrid;
    }
    if (deviceName != null) {
      $result.deviceName = deviceName;
    }
    if (loginType != null) {
      $result.loginType = loginType;
    }
    if (deviceModel != null) {
      $result.deviceModel = deviceModel;
    }
    if (password != null) {
      $result.password = password;
    }
    if (passwordMethod != null) {
      $result.passwordMethod = passwordMethod;
    }
    if (timeStr != null) {
      $result.timeStr = timeStr;
    }
    if (sysId != null) {
      $result.sysId = sysId;
    }
    if (extraOption != null) {
      $result.extraOption.addAll(extraOption);
    }
    if (codec != null) {
      $result.codec.addAll(codec);
    }
    return $result;
  }
  login._() : super();
  factory login.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory login.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'login', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'deviceDmrid', $pb.PbFieldType.OF3)
    ..aOS(2, _omitFieldNames ? '' : 'deviceName')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'loginType', $pb.PbFieldType.O3)
    ..aOS(4, _omitFieldNames ? '' : 'deviceModel')
    ..aOS(5, _omitFieldNames ? '' : 'password')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'passwordMethod', $pb.PbFieldType.O3)
    ..aOS(7, _omitFieldNames ? '' : 'timeStr')
    ..aOS(8, _omitFieldNames ? '' : 'sysId')
    ..p<$core.int>(9, _omitFieldNames ? '' : 'extraOption', $pb.PbFieldType.K3)
    ..p<$core.int>(10, _omitFieldNames ? '' : 'codec', $pb.PbFieldType.K3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  login clone() => login()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  login copyWith(void Function(login) updates) => super.copyWith((message) => updates(message as login)) as login;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static login create() => login._();
  login createEmptyInstance() => create();
  static $pb.PbList<login> createRepeated() => $pb.PbList<login>();
  @$core.pragma('dart2js:noInline')
  static login getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<login>(create);
  static login? _defaultInstance;

  /// 设备dmrid  android端可不填
  @$pb.TagNumber(1)
  $core.int get deviceDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set deviceDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDeviceDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearDeviceDmrid() => clearField(1);

  /// 设备名字  android端填写username
  @$pb.TagNumber(2)
  $core.String get deviceName => $_getSZ(1);
  @$pb.TagNumber(2)
  set deviceName($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDeviceName() => $_has(1);
  @$pb.TagNumber(2)
  void clearDeviceName() => clearField(2);

  /// 登录类型,0:中继台　1:控制台程序 2:控制台程序/网关终端  3:android端登录 23:公网poc登录
  @$pb.TagNumber(3)
  $core.int get loginType => $_getIZ(2);
  @$pb.TagNumber(3)
  set loginType($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLoginType() => $_has(2);
  @$pb.TagNumber(3)
  void clearLoginType() => clearField(3);

  /// 设备型号    android端可不填
  ///  BF8100项目:TR805005,双频中继，2个时隙
  ///  BF-TR925项目:TR092500,双频中继，2个时隙
  ///  BF-TR925R项目:TR092501，单频中继，1个时隙
  ///  BF-TR925D项目:TR09250M，单频中继，1个时隙
  @$pb.TagNumber(4)
  $core.String get deviceModel => $_getSZ(3);
  @$pb.TagNumber(4)
  set deviceModel($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeviceModel() => $_has(3);
  @$pb.TagNumber(4)
  void clearDeviceModel() => clearField(4);

  /// 密码检验值   android端填写password/sid
  @$pb.TagNumber(5)
  $core.String get password => $_getSZ(4);
  @$pb.TagNumber(5)
  set password($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPassword() => $_has(4);
  @$pb.TagNumber(5)
  void clearPassword() => clearField(5);

  /// andoid端，公网poc端：
  /// 11:使用密码登录,password=base64(sha256(time_str+base64(sha256(user_name+user_pass))))
  /// 12:使用sid登录,首次登录
  /// 13:使用sid登录，断线重连登录
  @$pb.TagNumber(6)
  $core.int get passwordMethod => $_getIZ(5);
  @$pb.TagNumber(6)
  set passwordMethod($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPasswordMethod() => $_has(5);
  @$pb.TagNumber(6)
  void clearPasswordMethod() => clearField(6);

  ///  password_method=10/11 时需要的参数
  /// 取当前时间(utc)格式为: yyyy-mm-dd hh:mm:ss
  @$pb.TagNumber(7)
  $core.String get timeStr => $_getSZ(6);
  @$pb.TagNumber(7)
  set timeStr($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasTimeStr() => $_has(6);
  @$pb.TagNumber(7)
  void clearTimeStr() => clearField(7);

  /// 系统号,00-63
  @$pb.TagNumber(8)
  $core.String get sysId => $_getSZ(7);
  @$pb.TagNumber(8)
  set sysId($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasSysId() => $_has(7);
  @$pb.TagNumber(8)
  void clearSysId() => clearField(8);

  /// 额外要求的功能,每个额外的功能有相应的功能号
  ///  8: 中继支持监控功能
  ///  107：要求曾经在此中继下注册过的终端发送终端已在其它地方登录的提示，功能号为107
  ///  175: 控制器要求发送所有的会话结束信息，功能号为175
  ///  375: app 要求转发gps信息，它有地图显示功能
  @$pb.TagNumber(9)
  $core.List<$core.int> get extraOption => $_getList(8);

  /// 支持的codec编码器，用于后台判断是否支持该编码器以便转发相关数据
  /// 目前支持的codec编码器：
  /// 0: dmr ambe  -> bc30
  /// 1: opus  -> bc10
  /// 默认不填写，即只支持标准 dmr ambe，null=[0]
  /// poc端一般=[1]
  /// cm625=[0,1]
  @$pb.TagNumber(10)
  $core.List<$core.int> get codec => $_getList(9);
}

/// 登录回应的额外信息。放入rpc_cmd.para_bin
class res_login_para_bin extends $pb.GeneratedMessage {
  factory res_login_para_bin({
    $core.List<$core.int>? validSnCode,
    $core.String? imbeSn,
    $core.int? isHaveFullCallPerm,
  }) {
    final $result = create();
    if (validSnCode != null) {
      $result.validSnCode = validSnCode;
    }
    if (imbeSn != null) {
      $result.imbeSn = imbeSn;
    }
    if (isHaveFullCallPerm != null) {
      $result.isHaveFullCallPerm = isHaveFullCallPerm;
    }
    return $result;
  }
  res_login_para_bin._() : super();
  factory res_login_para_bin.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory res_login_para_bin.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'res_login_para_bin', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, _omitFieldNames ? '' : 'validSnCode', $pb.PbFieldType.OY, protoName: 'validSnCode')
    ..aOS(2, _omitFieldNames ? '' : 'imbeSn', protoName: 'imbeSn')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'isHaveFullCallPerm', $pb.PbFieldType.O3, protoName: 'isHaveFullCallPerm')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  res_login_para_bin clone() => res_login_para_bin()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  res_login_para_bin copyWith(void Function(res_login_para_bin) updates) => super.copyWith((message) => updates(message as res_login_para_bin)) as res_login_para_bin;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static res_login_para_bin create() => res_login_para_bin._();
  res_login_para_bin createEmptyInstance() => create();
  static $pb.PbList<res_login_para_bin> createRepeated() => $pb.PbList<res_login_para_bin>();
  @$core.pragma('dart2js:noInline')
  static res_login_para_bin getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<res_login_para_bin>(create);
  static res_login_para_bin? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get validSnCode => $_getN(0);
  @$pb.TagNumber(1)
  set validSnCode($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasValidSnCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearValidSnCode() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get imbeSn => $_getSZ(1);
  @$pb.TagNumber(2)
  set imbeSn($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasImbeSn() => $_has(1);
  @$pb.TagNumber(2)
  void clearImbeSn() => clearField(2);

  /// 0.没有权限  1.具有全呼权限
  @$pb.TagNumber(3)
  $core.int get isHaveFullCallPerm => $_getIZ(2);
  @$pb.TagNumber(3)
  set isHaveFullCallPerm($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasIsHaveFullCallPerm() => $_has(2);
  @$pb.TagNumber(3)
  void clearIsHaveFullCallPerm() => clearField(3);
}

/// 登录回应
/// 特别的，andoid端 需要主动返回 channels等信息
class res_login extends $pb.GeneratedMessage {
  factory res_login({
    $core.int? resCode,
    $fixnum.Int64? sid,
    $core.int? hangupTime,
    $core.int? httpPort,
    $core.String? serverVersion,
    $core.String? settingLastUpdateTime,
  }) {
    final $result = create();
    if (resCode != null) {
      $result.resCode = resCode;
    }
    if (sid != null) {
      $result.sid = sid;
    }
    if (hangupTime != null) {
      $result.hangupTime = hangupTime;
    }
    if (httpPort != null) {
      $result.httpPort = httpPort;
    }
    if (serverVersion != null) {
      $result.serverVersion = serverVersion;
    }
    if (settingLastUpdateTime != null) {
      $result.settingLastUpdateTime = settingLastUpdateTime;
    }
    return $result;
  }
  res_login._() : super();
  factory res_login.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory res_login.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'res_login', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'resCode', $pb.PbFieldType.O3)
    ..aInt64(3, _omitFieldNames ? '' : 'sid')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'hangupTime', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'httpPort', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'serverVersion')
    ..aOS(7, _omitFieldNames ? '' : 'settingLastUpdateTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  res_login clone() => res_login()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  res_login copyWith(void Function(res_login) updates) => super.copyWith((message) => updates(message as res_login)) as res_login;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static res_login create() => res_login._();
  res_login createEmptyInstance() => create();
  static $pb.PbList<res_login> createRepeated() => $pb.PbList<res_login>();
  @$core.pragma('dart2js:noInline')
  static res_login getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<res_login>(create);
  static res_login? _defaultInstance;

  /// 登录回应值
  ///  0:登录成功，
  ///  1:重复登录
  ///  4:密码不对
  ///  5:没有指定密码登录
  ///  10:登录失败,不存在此设备
  ///  44:bad param
  ///  303: 用户没有指定设备
  ///  404: 此session id不存在,需要换用户名密码登录
  ///  500: 服务器内部错误
  @$pb.TagNumber(1)
  $core.int get resCode => $_getIZ(0);
  @$pb.TagNumber(1)
  set resCode($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasResCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearResCode() => clearField(1);

  /// last session id
  @$pb.TagNumber(3)
  $fixnum.Int64 get sid => $_getI64(1);
  @$pb.TagNumber(3)
  set sid($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasSid() => $_has(1);
  @$pb.TagNumber(3)
  void clearSid() => clearField(3);

  /// 服务器配置的挂起时间，单位毫秒,0为无效值
  @$pb.TagNumber(4)
  $core.int get hangupTime => $_getIZ(2);
  @$pb.TagNumber(4)
  set hangupTime($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasHangupTime() => $_has(2);
  @$pb.TagNumber(4)
  void clearHangupTime() => clearField(4);

  /// 服务器http端口
  @$pb.TagNumber(5)
  $core.int get httpPort => $_getIZ(3);
  @$pb.TagNumber(5)
  set httpPort($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasHttpPort() => $_has(3);
  @$pb.TagNumber(5)
  void clearHttpPort() => clearField(5);

  /// 服务器版本号
  @$pb.TagNumber(6)
  $core.String get serverVersion => $_getSZ(4);
  @$pb.TagNumber(6)
  set serverVersion($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasServerVersion() => $_has(4);
  @$pb.TagNumber(6)
  void clearServerVersion() => clearField(6);

  /// 配置最后更新时间,utc时间
  /// 目前只有poc终端有此字段
  @$pb.TagNumber(7)
  $core.String get settingLastUpdateTime => $_getSZ(5);
  @$pb.TagNumber(7)
  set settingLastUpdateTime($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(7)
  $core.bool hasSettingLastUpdateTime() => $_has(5);
  @$pb.TagNumber(7)
  void clearSettingLastUpdateTime() => clearField(7);
}

/// 中继状态获取的应答
/// rpc.cmd=185
/// rpc.res=1
/// rpc.body=res_repeater_state
class res_repeater_state extends $pb.GeneratedMessage {
  factory res_repeater_state({
    $core.int? deviceDmrid,
    $core.int? channelId,
    $core.int? rxFrequency,
    $core.int? txFrequency,
    $core.int? powerValue,
    $core.int? ipAddr,
    $core.int? volValue,
    $core.int? tmpValue,
    $core.int? tmpErr,
    $core.int? antErr,
    $core.int? gpsErr,
    $core.int? volErr,
    $core.int? rxPllErr,
    $core.int? txPllErr,
    $core.int? fanErr,
    $core.int? signal,
    $core.int? antValue,
  }) {
    final $result = create();
    if (deviceDmrid != null) {
      $result.deviceDmrid = deviceDmrid;
    }
    if (channelId != null) {
      $result.channelId = channelId;
    }
    if (rxFrequency != null) {
      $result.rxFrequency = rxFrequency;
    }
    if (txFrequency != null) {
      $result.txFrequency = txFrequency;
    }
    if (powerValue != null) {
      $result.powerValue = powerValue;
    }
    if (ipAddr != null) {
      $result.ipAddr = ipAddr;
    }
    if (volValue != null) {
      $result.volValue = volValue;
    }
    if (tmpValue != null) {
      $result.tmpValue = tmpValue;
    }
    if (tmpErr != null) {
      $result.tmpErr = tmpErr;
    }
    if (antErr != null) {
      $result.antErr = antErr;
    }
    if (gpsErr != null) {
      $result.gpsErr = gpsErr;
    }
    if (volErr != null) {
      $result.volErr = volErr;
    }
    if (rxPllErr != null) {
      $result.rxPllErr = rxPllErr;
    }
    if (txPllErr != null) {
      $result.txPllErr = txPllErr;
    }
    if (fanErr != null) {
      $result.fanErr = fanErr;
    }
    if (signal != null) {
      $result.signal = signal;
    }
    if (antValue != null) {
      $result.antValue = antValue;
    }
    return $result;
  }
  res_repeater_state._() : super();
  factory res_repeater_state.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory res_repeater_state.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'res_repeater_state', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'deviceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'channelId', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'rxFrequency', $pb.PbFieldType.OF3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'txFrequency', $pb.PbFieldType.OF3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'powerValue', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'ipAddr', $pb.PbFieldType.OF3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'volValue', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'tmpValue', $pb.PbFieldType.O3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'tmpErr', $pb.PbFieldType.O3)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'antErr', $pb.PbFieldType.O3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'gpsErr', $pb.PbFieldType.O3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'volErr', $pb.PbFieldType.O3)
    ..a<$core.int>(13, _omitFieldNames ? '' : 'rxPllErr', $pb.PbFieldType.O3)
    ..a<$core.int>(14, _omitFieldNames ? '' : 'txPllErr', $pb.PbFieldType.O3)
    ..a<$core.int>(15, _omitFieldNames ? '' : 'fanErr', $pb.PbFieldType.O3)
    ..a<$core.int>(16, _omitFieldNames ? '' : 'signal', $pb.PbFieldType.O3)
    ..a<$core.int>(17, _omitFieldNames ? '' : 'antValue', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  res_repeater_state clone() => res_repeater_state()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  res_repeater_state copyWith(void Function(res_repeater_state) updates) => super.copyWith((message) => updates(message as res_repeater_state)) as res_repeater_state;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static res_repeater_state create() => res_repeater_state._();
  res_repeater_state createEmptyInstance() => create();
  static $pb.PbList<res_repeater_state> createRepeated() => $pb.PbList<res_repeater_state>();
  @$core.pragma('dart2js:noInline')
  static res_repeater_state getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<res_repeater_state>(create);
  static res_repeater_state? _defaultInstance;

  /// 设备dmrid
  @$pb.TagNumber(1)
  $core.int get deviceDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set deviceDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDeviceDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearDeviceDmrid() => clearField(1);

  /// 信道ID
  @$pb.TagNumber(2)
  $core.int get channelId => $_getIZ(1);
  @$pb.TagNumber(2)
  set channelId($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasChannelId() => $_has(1);
  @$pb.TagNumber(2)
  void clearChannelId() => clearField(2);

  /// 接收频率,Mhz
  @$pb.TagNumber(3)
  $core.int get rxFrequency => $_getIZ(2);
  @$pb.TagNumber(3)
  set rxFrequency($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRxFrequency() => $_has(2);
  @$pb.TagNumber(3)
  void clearRxFrequency() => clearField(3);

  /// 发射频率,Mhz
  @$pb.TagNumber(4)
  $core.int get txFrequency => $_getIZ(3);
  @$pb.TagNumber(4)
  set txFrequency($core.int v) { $_setUnsignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTxFrequency() => $_has(3);
  @$pb.TagNumber(4)
  void clearTxFrequency() => clearField(4);

  /// 功率值,W
  @$pb.TagNumber(5)
  $core.int get powerValue => $_getIZ(4);
  @$pb.TagNumber(5)
  set powerValue($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPowerValue() => $_has(4);
  @$pb.TagNumber(5)
  void clearPowerValue() => clearField(5);

  /// 中继本地IP地址
  @$pb.TagNumber(6)
  $core.int get ipAddr => $_getIZ(5);
  @$pb.TagNumber(6)
  set ipAddr($core.int v) { $_setUnsignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasIpAddr() => $_has(5);
  @$pb.TagNumber(6)
  void clearIpAddr() => clearField(6);

  /// 电压值,mV
  @$pb.TagNumber(7)
  $core.int get volValue => $_getIZ(6);
  @$pb.TagNumber(7)
  set volValue($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasVolValue() => $_has(6);
  @$pb.TagNumber(7)
  void clearVolValue() => clearField(7);

  /// 温度值,单位 0.1摄氏度
  @$pb.TagNumber(8)
  $core.int get tmpValue => $_getIZ(7);
  @$pb.TagNumber(8)
  set tmpValue($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasTmpValue() => $_has(7);
  @$pb.TagNumber(8)
  void clearTmpValue() => clearField(8);

  /// 温度状态
  /// 0:正常
  /// 1:温度过高
  @$pb.TagNumber(9)
  $core.int get tmpErr => $_getIZ(8);
  @$pb.TagNumber(9)
  set tmpErr($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasTmpErr() => $_has(8);
  @$pb.TagNumber(9)
  void clearTmpErr() => clearField(9);

  /// 天线(驻波)状态
  /// 0:正常
  /// 1:异常
  @$pb.TagNumber(10)
  $core.int get antErr => $_getIZ(9);
  @$pb.TagNumber(10)
  set antErr($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasAntErr() => $_has(9);
  @$pb.TagNumber(10)
  void clearAntErr() => clearField(10);

  /// GPS同步状态
  /// 0:未安装
  /// 1:未同步
  /// 2:已同步
  @$pb.TagNumber(11)
  $core.int get gpsErr => $_getIZ(10);
  @$pb.TagNumber(11)
  set gpsErr($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasGpsErr() => $_has(10);
  @$pb.TagNumber(11)
  void clearGpsErr() => clearField(11);

  /// 电压状态
  /// 0:正常
  /// 1:电压过高
  /// 2:电压过低
  @$pb.TagNumber(12)
  $core.int get volErr => $_getIZ(11);
  @$pb.TagNumber(12)
  set volErr($core.int v) { $_setSignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasVolErr() => $_has(11);
  @$pb.TagNumber(12)
  void clearVolErr() => clearField(12);

  /// 接收pll异常
  /// 0:正常
  /// 1:异常
  @$pb.TagNumber(13)
  $core.int get rxPllErr => $_getIZ(12);
  @$pb.TagNumber(13)
  set rxPllErr($core.int v) { $_setSignedInt32(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasRxPllErr() => $_has(12);
  @$pb.TagNumber(13)
  void clearRxPllErr() => clearField(13);

  /// 发射pll异常
  /// 0:正常
  /// 1:异常
  @$pb.TagNumber(14)
  $core.int get txPllErr => $_getIZ(13);
  @$pb.TagNumber(14)
  set txPllErr($core.int v) { $_setSignedInt32(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasTxPllErr() => $_has(13);
  @$pb.TagNumber(14)
  void clearTxPllErr() => clearField(14);

  /// 风扇异常
  /// 0:正常
  /// 1:异常
  @$pb.TagNumber(15)
  $core.int get fanErr => $_getIZ(14);
  @$pb.TagNumber(15)
  set fanErr($core.int v) { $_setSignedInt32(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasFanErr() => $_has(14);
  @$pb.TagNumber(15)
  void clearFanErr() => clearField(15);

  /// 信号干扰
  /// 0:无信号干扰
  /// 1:有信号干扰
  @$pb.TagNumber(16)
  $core.int get signal => $_getIZ(15);
  @$pb.TagNumber(16)
  set signal($core.int v) { $_setSignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasSignal() => $_has(15);
  @$pb.TagNumber(16)
  void clearSignal() => clearField(16);

  /// 驻波值,单位 0.1
  @$pb.TagNumber(17)
  $core.int get antValue => $_getIZ(16);
  @$pb.TagNumber(17)
  set antValue($core.int v) { $_setSignedInt32(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasAntValue() => $_has(16);
  @$pb.TagNumber(17)
  void clearAntValue() => clearField(17);
}

/// 中继异常状态上报
/// rpc.cmd=188
/// rpc.res=0
/// rpc.body=repeater_err_status
class repeater_err_status extends $pb.GeneratedMessage {
  factory repeater_err_status({
    $core.int? deviceDmrid,
    $core.int? tmpErr,
    $core.int? antErr,
    $core.int? gpsErr,
    $core.int? volErr,
    $core.int? rxPllErr,
    $core.int? txPllErr,
    $core.int? fanErr,
    $core.int? signal,
  }) {
    final $result = create();
    if (deviceDmrid != null) {
      $result.deviceDmrid = deviceDmrid;
    }
    if (tmpErr != null) {
      $result.tmpErr = tmpErr;
    }
    if (antErr != null) {
      $result.antErr = antErr;
    }
    if (gpsErr != null) {
      $result.gpsErr = gpsErr;
    }
    if (volErr != null) {
      $result.volErr = volErr;
    }
    if (rxPllErr != null) {
      $result.rxPllErr = rxPllErr;
    }
    if (txPllErr != null) {
      $result.txPllErr = txPllErr;
    }
    if (fanErr != null) {
      $result.fanErr = fanErr;
    }
    if (signal != null) {
      $result.signal = signal;
    }
    return $result;
  }
  repeater_err_status._() : super();
  factory repeater_err_status.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory repeater_err_status.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'repeater_err_status', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'deviceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'tmpErr', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'antErr', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'gpsErr', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'volErr', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'rxPllErr', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'txPllErr', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'fanErr', $pb.PbFieldType.O3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'signal', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  repeater_err_status clone() => repeater_err_status()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  repeater_err_status copyWith(void Function(repeater_err_status) updates) => super.copyWith((message) => updates(message as repeater_err_status)) as repeater_err_status;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static repeater_err_status create() => repeater_err_status._();
  repeater_err_status createEmptyInstance() => create();
  static $pb.PbList<repeater_err_status> createRepeated() => $pb.PbList<repeater_err_status>();
  @$core.pragma('dart2js:noInline')
  static repeater_err_status getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<repeater_err_status>(create);
  static repeater_err_status? _defaultInstance;

  /// 设备dmrid
  @$pb.TagNumber(1)
  $core.int get deviceDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set deviceDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDeviceDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearDeviceDmrid() => clearField(1);

  /// 温度状态
  /// 0:正常
  /// 1:温度过高
  @$pb.TagNumber(2)
  $core.int get tmpErr => $_getIZ(1);
  @$pb.TagNumber(2)
  set tmpErr($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTmpErr() => $_has(1);
  @$pb.TagNumber(2)
  void clearTmpErr() => clearField(2);

  /// 天线(驻波)状态
  /// 0:正常
  /// 1:异常
  @$pb.TagNumber(3)
  $core.int get antErr => $_getIZ(2);
  @$pb.TagNumber(3)
  set antErr($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasAntErr() => $_has(2);
  @$pb.TagNumber(3)
  void clearAntErr() => clearField(3);

  /// GPS同步状态
  /// 0:未安装
  /// 1:未同步
  /// 2:已同步
  @$pb.TagNumber(4)
  $core.int get gpsErr => $_getIZ(3);
  @$pb.TagNumber(4)
  set gpsErr($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGpsErr() => $_has(3);
  @$pb.TagNumber(4)
  void clearGpsErr() => clearField(4);

  /// 电压状态
  /// 0:正常
  /// 1:电压过高
  /// 2:电压过低
  @$pb.TagNumber(5)
  $core.int get volErr => $_getIZ(4);
  @$pb.TagNumber(5)
  set volErr($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasVolErr() => $_has(4);
  @$pb.TagNumber(5)
  void clearVolErr() => clearField(5);

  /// 接收pll异常
  /// 0:正常
  /// 1:异常
  @$pb.TagNumber(6)
  $core.int get rxPllErr => $_getIZ(5);
  @$pb.TagNumber(6)
  set rxPllErr($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasRxPllErr() => $_has(5);
  @$pb.TagNumber(6)
  void clearRxPllErr() => clearField(6);

  /// 发射pll异常
  /// 0:正常
  /// 1:异常
  @$pb.TagNumber(7)
  $core.int get txPllErr => $_getIZ(6);
  @$pb.TagNumber(7)
  set txPllErr($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasTxPllErr() => $_has(6);
  @$pb.TagNumber(7)
  void clearTxPllErr() => clearField(7);

  /// 风扇异常
  /// 0:正常
  /// 1:异常
  @$pb.TagNumber(8)
  $core.int get fanErr => $_getIZ(7);
  @$pb.TagNumber(8)
  set fanErr($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasFanErr() => $_has(7);
  @$pb.TagNumber(8)
  void clearFanErr() => clearField(8);

  /// 信号干扰
  /// 0:无信号干扰
  /// 1:有信号干扰
  @$pb.TagNumber(9)
  $core.int get signal => $_getIZ(8);
  @$pb.TagNumber(9)
  set signal($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasSignal() => $_has(8);
  @$pb.TagNumber(9)
  void clearSignal() => clearField(9);
}

/// 手台上传命令，bcxx,cdxx等
class device_send extends $pb.GeneratedMessage {
  factory device_send({
    $core.int? repeaterDmrid,
    $core.List<$core.int>? fsk,
    $core.int? sourceRepeaterDmrid,
  }) {
    final $result = create();
    if (repeaterDmrid != null) {
      $result.repeaterDmrid = repeaterDmrid;
    }
    if (fsk != null) {
      $result.fsk = fsk;
    }
    if (sourceRepeaterDmrid != null) {
      $result.sourceRepeaterDmrid = sourceRepeaterDmrid;
    }
    return $result;
  }
  device_send._() : super();
  factory device_send.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory device_send.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'device_send', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'repeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'fsk', $pb.PbFieldType.OY)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'sourceRepeaterDmrid', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  device_send clone() => device_send()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  device_send copyWith(void Function(device_send) updates) => super.copyWith((message) => updates(message as device_send)) as device_send;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static device_send create() => device_send._();
  device_send createEmptyInstance() => create();
  static $pb.PbList<device_send> createRepeated() => $pb.PbList<device_send>();
  @$core.pragma('dart2js:noInline')
  static device_send getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<device_send>(create);
  static device_send? _defaultInstance;

  /// 发起方 中继设备ID
  @$pb.TagNumber(1)
  $core.int get repeaterDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set repeaterDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRepeaterDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRepeaterDmrid() => clearField(1);

  /// 手台上传的fsk 内容
  @$pb.TagNumber(3)
  $core.List<$core.int> get fsk => $_getN(1);
  @$pb.TagNumber(3)
  set fsk($core.List<$core.int> v) { $_setBytes(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasFsk() => $_has(1);
  @$pb.TagNumber(3)
  void clearFsk() => clearField(3);

  /// 从机中继ID(针对同播控制器)
  @$pb.TagNumber(4)
  $core.int get sourceRepeaterDmrid => $_getIZ(2);
  @$pb.TagNumber(4)
  set sourceRepeaterDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasSourceRepeaterDmrid() => $_has(2);
  @$pb.TagNumber(4)
  void clearSourceRepeaterDmrid() => clearField(4);
}

/// 服务器下发的cbxx,dcxx等命令
class server_send extends $pb.GeneratedMessage {
  factory server_send({
    $core.List<$core.int>? fsk,
  }) {
    final $result = create();
    if (fsk != null) {
      $result.fsk = fsk;
    }
    return $result;
  }
  server_send._() : super();
  factory server_send.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory server_send.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'server_send', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'fsk', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  server_send clone() => server_send()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  server_send copyWith(void Function(server_send) updates) => super.copyWith((message) => updates(message as server_send)) as server_send;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static server_send create() => server_send._();
  server_send createEmptyInstance() => create();
  static $pb.PbList<server_send> createRepeated() => $pb.PbList<server_send>();
  @$core.pragma('dart2js:noInline')
  static server_send getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<server_send>(create);
  static server_send? _defaultInstance;

  /// 下发的fsk 内容
  @$pb.TagNumber(3)
  $core.List<$core.int> get fsk => $_getN(0);
  @$pb.TagNumber(3)
  set fsk($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(3)
  $core.bool hasFsk() => $_has(0);
  @$pb.TagNumber(3)
  void clearFsk() => clearField(3);
}

///  rpc.cmd=71,72
/// 手台申请话权
class bc71 extends $pb.GeneratedMessage {
  factory bc71({
    $core.int? repeaterDmrid,
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.int? fieldIntensity,
    $core.int? supportDigital,
    $core.int? supportAnalog,
    $core.int? timeSlotNo,
    $core.int? priority,
    $core.int? soundType,
    $core.String? phoneNo,
    $core.int? callDuplex,
    $core.int? sourceRepeaterDmrid,
    $core.int? preferInterruptTargetDmrid,
  }) {
    final $result = create();
    if (repeaterDmrid != null) {
      $result.repeaterDmrid = repeaterDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (fieldIntensity != null) {
      $result.fieldIntensity = fieldIntensity;
    }
    if (supportDigital != null) {
      $result.supportDigital = supportDigital;
    }
    if (supportAnalog != null) {
      $result.supportAnalog = supportAnalog;
    }
    if (timeSlotNo != null) {
      $result.timeSlotNo = timeSlotNo;
    }
    if (priority != null) {
      $result.priority = priority;
    }
    if (soundType != null) {
      $result.soundType = soundType;
    }
    if (phoneNo != null) {
      $result.phoneNo = phoneNo;
    }
    if (callDuplex != null) {
      $result.callDuplex = callDuplex;
    }
    if (sourceRepeaterDmrid != null) {
      $result.sourceRepeaterDmrid = sourceRepeaterDmrid;
    }
    if (preferInterruptTargetDmrid != null) {
      $result.preferInterruptTargetDmrid = preferInterruptTargetDmrid;
    }
    return $result;
  }
  bc71._() : super();
  factory bc71.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc71.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc71', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'repeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'fieldIntensity', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'supportDigital', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'supportAnalog', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'timeSlotNo', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'priority', $pb.PbFieldType.O3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'soundType', $pb.PbFieldType.O3)
    ..aOS(10, _omitFieldNames ? '' : 'phoneNo')
    ..a<$core.int>(14, _omitFieldNames ? '' : 'callDuplex', $pb.PbFieldType.O3)
    ..a<$core.int>(15, _omitFieldNames ? '' : 'sourceRepeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(16, _omitFieldNames ? '' : 'preferInterruptTargetDmrid', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc71 clone() => bc71()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc71 copyWith(void Function(bc71) updates) => super.copyWith((message) => updates(message as bc71)) as bc71;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc71 create() => bc71._();
  bc71 createEmptyInstance() => create();
  static $pb.PbList<bc71> createRepeated() => $pb.PbList<bc71>();
  @$core.pragma('dart2js:noInline')
  static bc71 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc71>(create);
  static bc71? _defaultInstance;

  /// 发起方 中继设备ID
  @$pb.TagNumber(1)
  $core.int get repeaterDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set repeaterDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRepeaterDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRepeaterDmrid() => clearField(1);

  /// 目标id
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// 场强
  @$pb.TagNumber(4)
  $core.int get fieldIntensity => $_getIZ(3);
  @$pb.TagNumber(4)
  set fieldIntensity($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasFieldIntensity() => $_has(3);
  @$pb.TagNumber(4)
  void clearFieldIntensity() => clearField(4);

  /// 语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
  @$pb.TagNumber(5)
  $core.int get supportDigital => $_getIZ(4);
  @$pb.TagNumber(5)
  set supportDigital($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSupportDigital() => $_has(4);
  @$pb.TagNumber(5)
  void clearSupportDigital() => clearField(5);

  /// 语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
  @$pb.TagNumber(6)
  $core.int get supportAnalog => $_getIZ(5);
  @$pb.TagNumber(6)
  set supportAnalog($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSupportAnalog() => $_has(5);
  @$pb.TagNumber(6)
  void clearSupportAnalog() => clearField(6);

  /// 时隙 0：时隙1，　1：时隙2
  @$pb.TagNumber(7)
  $core.int get timeSlotNo => $_getIZ(6);
  @$pb.TagNumber(7)
  set timeSlotNo($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasTimeSlotNo() => $_has(6);
  @$pb.TagNumber(7)
  void clearTimeSlotNo() => clearField(7);

  /// 通话优先级
  ///  0：普通通话， 1：优先级1
  ///  2: 优先级2，  3: 优先级3
  ///  4: 紧急通话
  @$pb.TagNumber(8)
  $core.int get priority => $_getIZ(7);
  @$pb.TagNumber(8)
  set priority($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasPriority() => $_has(7);
  @$pb.TagNumber(8)
  void clearPriority() => clearField(8);

  /// 话音申请类型
  ///  0:默认的正常手台语音申请
  ///  1:手台打电话申请
  ///  2:电话网关外线语音申请(被动)
  ///  3:手台报警后的自动语音申请
  ///  4:后台发监听,手台自动监听的语音申请
  ///  5:电话网关收到手台请求的电话后的语音申请
  @$pb.TagNumber(9)
  $core.int get soundType => $_getIZ(8);
  @$pb.TagNumber(9)
  set soundType($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasSoundType() => $_has(8);
  @$pb.TagNumber(9)
  void clearSoundType() => clearField(9);

  /// 电话号码,电话相关业务使用
  @$pb.TagNumber(10)
  $core.String get phoneNo => $_getSZ(9);
  @$pb.TagNumber(10)
  set phoneNo($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasPhoneNo() => $_has(9);
  @$pb.TagNumber(10)
  void clearPhoneNo() => clearField(10);

  /// 双工,半双工通话类型
  /// 0: 半双工
  /// 1: 全双工
  @$pb.TagNumber(14)
  $core.int get callDuplex => $_getIZ(10);
  @$pb.TagNumber(14)
  set callDuplex($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(14)
  $core.bool hasCallDuplex() => $_has(10);
  @$pb.TagNumber(14)
  void clearCallDuplex() => clearField(14);

  /// 从机中继ID(针对同播控制器)
  @$pb.TagNumber(15)
  $core.int get sourceRepeaterDmrid => $_getIZ(11);
  @$pb.TagNumber(15)
  set sourceRepeaterDmrid($core.int v) { $_setUnsignedInt32(11, v); }
  @$pb.TagNumber(15)
  $core.bool hasSourceRepeaterDmrid() => $_has(11);
  @$pb.TagNumber(15)
  void clearSourceRepeaterDmrid() => clearField(15);

  /// 如果涉及到抢占，控制器希望打断自己上面特定的会话时，可以通过此字段指定要打断的会话目标dmrid,默认不需要此功能
  /// 虚拟集群里面，如果时隙已经占满，则希望抢占当前时隙的会话
  @$pb.TagNumber(16)
  $core.int get preferInterruptTargetDmrid => $_getIZ(12);
  @$pb.TagNumber(16)
  set preferInterruptTargetDmrid($core.int v) { $_setUnsignedInt32(12, v); }
  @$pb.TagNumber(16)
  $core.bool hasPreferInterruptTargetDmrid() => $_has(12);
  @$pb.TagNumber(16)
  void clearPreferInterruptTargetDmrid() => clearField(16);
}

///  rpc.cmd=171
/// 申请话权回应
class cb71 extends $pb.GeneratedMessage {
  factory cb71({
    $core.int? repeaterDmrid,
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.int? reqNo,
    $core.int? supportDigital,
    $core.int? supportAnalog,
    $core.int? timeSlotNo,
    $core.int? priority,
    $core.int? result,
    $core.int? interruptDmrid,
    $core.int? soundType,
    $core.String? phoneNo,
    $core.int? callDuplex,
    $core.int? sourceRepeaterDmrid,
  }) {
    final $result = create();
    if (repeaterDmrid != null) {
      $result.repeaterDmrid = repeaterDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (reqNo != null) {
      $result.reqNo = reqNo;
    }
    if (supportDigital != null) {
      $result.supportDigital = supportDigital;
    }
    if (supportAnalog != null) {
      $result.supportAnalog = supportAnalog;
    }
    if (timeSlotNo != null) {
      $result.timeSlotNo = timeSlotNo;
    }
    if (priority != null) {
      $result.priority = priority;
    }
    if (result != null) {
      $result.result = result;
    }
    if (interruptDmrid != null) {
      $result.interruptDmrid = interruptDmrid;
    }
    if (soundType != null) {
      $result.soundType = soundType;
    }
    if (phoneNo != null) {
      $result.phoneNo = phoneNo;
    }
    if (callDuplex != null) {
      $result.callDuplex = callDuplex;
    }
    if (sourceRepeaterDmrid != null) {
      $result.sourceRepeaterDmrid = sourceRepeaterDmrid;
    }
    return $result;
  }
  cb71._() : super();
  factory cb71.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb71.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb71', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'repeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'reqNo', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'supportDigital', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'supportAnalog', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'timeSlotNo', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'priority', $pb.PbFieldType.O3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'result', $pb.PbFieldType.O3)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'interruptDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'soundType', $pb.PbFieldType.O3)
    ..aOS(12, _omitFieldNames ? '' : 'phoneNo')
    ..a<$core.int>(13, _omitFieldNames ? '' : 'callDuplex', $pb.PbFieldType.O3)
    ..a<$core.int>(14, _omitFieldNames ? '' : 'sourceRepeaterDmrid', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb71 clone() => cb71()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb71 copyWith(void Function(cb71) updates) => super.copyWith((message) => updates(message as cb71)) as cb71;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb71 create() => cb71._();
  cb71 createEmptyInstance() => create();
  static $pb.PbList<cb71> createRepeated() => $pb.PbList<cb71>();
  @$core.pragma('dart2js:noInline')
  static cb71 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb71>(create);
  static cb71? _defaultInstance;

  /// 发起方 中继设备ID
  @$pb.TagNumber(1)
  $core.int get repeaterDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set repeaterDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRepeaterDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRepeaterDmrid() => clearField(1);

  /// 目标id
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// 应答的请求指令号
  @$pb.TagNumber(4)
  $core.int get reqNo => $_getIZ(3);
  @$pb.TagNumber(4)
  set reqNo($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasReqNo() => $_has(3);
  @$pb.TagNumber(4)
  void clearReqNo() => clearField(4);

  /// 语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
  @$pb.TagNumber(5)
  $core.int get supportDigital => $_getIZ(4);
  @$pb.TagNumber(5)
  set supportDigital($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSupportDigital() => $_has(4);
  @$pb.TagNumber(5)
  void clearSupportDigital() => clearField(5);

  /// 语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
  @$pb.TagNumber(6)
  $core.int get supportAnalog => $_getIZ(5);
  @$pb.TagNumber(6)
  set supportAnalog($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSupportAnalog() => $_has(5);
  @$pb.TagNumber(6)
  void clearSupportAnalog() => clearField(6);

  /// 时隙 0：时隙1，　1：时隙2
  @$pb.TagNumber(7)
  $core.int get timeSlotNo => $_getIZ(6);
  @$pb.TagNumber(7)
  set timeSlotNo($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasTimeSlotNo() => $_has(6);
  @$pb.TagNumber(7)
  void clearTimeSlotNo() => clearField(7);

  /// 通话优先级
  @$pb.TagNumber(8)
  $core.int get priority => $_getIZ(7);
  @$pb.TagNumber(8)
  set priority($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasPriority() => $_has(7);
  @$pb.TagNumber(8)
  void clearPriority() => clearField(8);

  /// 应答结果
  ///  0x00：时隙占有，准备接收语音呼叫
  ///  0x01：允许联网呼叫
  ///  0x02：允许呼叫，中继与服务器断开
  ///  0x03: 手台电话申请成功
  ///  0x10: 组呼并入成功，集群模式下才有
  ///  0x80=128：拒绝呼叫，对方不在线
  ///  0x81=129：拒绝呼叫，对方在通话中
  ///  0x82=130：拒绝呼叫，中继信道忙
  ///  0x83=131：拒绝呼叫，被优先级更高手台抢占时隙资源
  ///  0x84=132: 拒绝呼叫，当前有更高优先级手台在通话中
  ///  0x85=133: 拒绝呼叫,后台已经释放了此手台的通话资源
  ///  0x86=134: 未登录,请先登录
  ///  0x87=135: 无电话网关可用,电话申请失败
  ///  0x88=136: 电话网关忙,电话申请失败
  ///  0x89=137: 电话黑名单,电话申请失败
  ///  0x8A=138: 系统授权已经过期，呼叫失败
  ///  0x90=144:
  ///  0x91=145: 后台数据库查询错误
  ///  0x92=146: 电话号码错误
  ///  0x93=147: 控制台登录号码和申请话权号码不一致,申请失败
  ///  0x94=148: 拒绝呼叫，临时组或任务组已失效
  ///  0xA0=160: 拒绝呼叫, 归属组无其它成员在线，集群模式下才有
  ///  0xA1=161: 拒绝呼叫, 手台所属中继错误，集群模式下才有
  ///  0xA2=162: 拒绝呼叫, 手台所属中继错误，集群模式下才有
  @$pb.TagNumber(9)
  $core.int get result => $_getIZ(8);
  @$pb.TagNumber(9)
  set result($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasResult() => $_has(8);
  @$pb.TagNumber(9)
  void clearResult() => clearField(9);

  /// 被更高优先级抢了语音的设备dmrid
  /// 当一个人A在通话中,但是有更高优先级的用户B发了bc71,
  /// 则会先给A发cb71.result=0x83,interrupt_dmrid=A的dmrid
  /// 然后给B发cb71.result=0x01
  @$pb.TagNumber(10)
  $core.int get interruptDmrid => $_getIZ(9);
  @$pb.TagNumber(10)
  set interruptDmrid($core.int v) { $_setUnsignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasInterruptDmrid() => $_has(9);
  @$pb.TagNumber(10)
  void clearInterruptDmrid() => clearField(10);

  /// 话音申请类型
  ///  0:默认的正常手台语音申请
  ///  1:手台打电话申请
  ///  2:电话网关外线语音申请(被动)
  ///  3:手台报警后的自动语音申请
  ///  4:后台发监听,手台自动监听的语音申请
  ///  5:电话网关收到手台请求的电话后的语音申请
  @$pb.TagNumber(11)
  $core.int get soundType => $_getIZ(10);
  @$pb.TagNumber(11)
  set soundType($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasSoundType() => $_has(10);
  @$pb.TagNumber(11)
  void clearSoundType() => clearField(11);

  /// 电话号码,电话相关业务使用
  @$pb.TagNumber(12)
  $core.String get phoneNo => $_getSZ(11);
  @$pb.TagNumber(12)
  set phoneNo($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasPhoneNo() => $_has(11);
  @$pb.TagNumber(12)
  void clearPhoneNo() => clearField(12);

  /// 双工,半双工通话类型
  /// 0: 半双工
  /// 1: 全双工
  @$pb.TagNumber(13)
  $core.int get callDuplex => $_getIZ(12);
  @$pb.TagNumber(13)
  set callDuplex($core.int v) { $_setSignedInt32(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasCallDuplex() => $_has(12);
  @$pb.TagNumber(13)
  void clearCallDuplex() => clearField(13);

  /// 从机中继ID(针对同播控制器)
  @$pb.TagNumber(14)
  $core.int get sourceRepeaterDmrid => $_getIZ(13);
  @$pb.TagNumber(14)
  set sourceRepeaterDmrid($core.int v) { $_setUnsignedInt32(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasSourceRepeaterDmrid() => $_has(13);
  @$pb.TagNumber(14)
  void clearSourceRepeaterDmrid() => clearField(14);
}

///  rpc.cmd=73
/// 抢断广播命令
class bc73 extends $pb.GeneratedMessage {
  factory bc73({
    $core.int? repeaterDmrid,
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.int? timeSlotNo,
    $core.int? sourceRepeaterDmrid,
  }) {
    final $result = create();
    if (repeaterDmrid != null) {
      $result.repeaterDmrid = repeaterDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (timeSlotNo != null) {
      $result.timeSlotNo = timeSlotNo;
    }
    if (sourceRepeaterDmrid != null) {
      $result.sourceRepeaterDmrid = sourceRepeaterDmrid;
    }
    return $result;
  }
  bc73._() : super();
  factory bc73.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc73.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc73', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'repeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'timeSlotNo', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'sourceRepeaterDmrid', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc73 clone() => bc73()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc73 copyWith(void Function(bc73) updates) => super.copyWith((message) => updates(message as bc73)) as bc73;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc73 create() => bc73._();
  bc73 createEmptyInstance() => create();
  static $pb.PbList<bc73> createRepeated() => $pb.PbList<bc73>();
  @$core.pragma('dart2js:noInline')
  static bc73 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc73>(create);
  static bc73? _defaultInstance;

  /// 发起方 中继设备ID
  @$pb.TagNumber(1)
  $core.int get repeaterDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set repeaterDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRepeaterDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRepeaterDmrid() => clearField(1);

  /// 目标id,被打断语音的发起人
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// 时隙 0：时隙1，　1：时隙2
  @$pb.TagNumber(4)
  $core.int get timeSlotNo => $_getIZ(3);
  @$pb.TagNumber(4)
  set timeSlotNo($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTimeSlotNo() => $_has(3);
  @$pb.TagNumber(4)
  void clearTimeSlotNo() => clearField(4);

  /// 从机中继ID(针对同播控制器)
  @$pb.TagNumber(5)
  $core.int get sourceRepeaterDmrid => $_getIZ(4);
  @$pb.TagNumber(5)
  set sourceRepeaterDmrid($core.int v) { $_setUnsignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSourceRepeaterDmrid() => $_has(4);
  @$pb.TagNumber(5)
  void clearSourceRepeaterDmrid() => clearField(5);
}

///  rpc.cmd=175
/// 后台通知会话需要立即结束
/// 不管是发起方还是接收方,此会话都必须立即结束
class cb75 extends $pb.GeneratedMessage {
  factory cb75({
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.int? reason,
  }) {
    final $result = create();
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (reason != null) {
      $result.reason = reason;
    }
    return $result;
  }
  cb75._() : super();
  factory cb75.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb75.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb75', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'reason', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb75 clone() => cb75()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb75 copyWith(void Function(cb75) updates) => super.copyWith((message) => updates(message as cb75)) as cb75;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb75 create() => cb75._();
  cb75 createEmptyInstance() => create();
  static $pb.PbList<cb75> createRepeated() => $pb.PbList<cb75>();
  @$core.pragma('dart2js:noInline')
  static cb75 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb75>(create);
  static cb75? _defaultInstance;

  /// 会话目标id
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(0);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(0);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 会话源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(1);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(1);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// 会话结束原因
  ///  0:会话正在被销毁,原因未知
  ///  1:被抢占
  ///  2:开始后指定时间内无语音
  ///  3:语音会话中无语音超时
  ///  4:会话已经销毁
  ///  5:BC15没抢到话权
  @$pb.TagNumber(5)
  $core.int get reason => $_getIZ(2);
  @$pb.TagNumber(5)
  set reason($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasReason() => $_has(2);
  @$pb.TagNumber(5)
  void clearReason() => clearField(5);
}

/// rpc.cmd=15
class bc15 extends $pb.GeneratedMessage {
  factory bc15({
    $core.int? repeaterDmrid,
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.int? fieldIntensity,
    $core.int? supportDigital,
    $core.int? supportAnalog,
    $core.int? timeSlotNo,
    $core.int? priority,
    $core.int? callType,
    $core.int? supportInterrupt,
    $core.int? callStatus,
    $core.int? soundType,
    $core.String? phoneNo,
    $core.int? callDuplex,
    $core.int? sourceRepeaterDmrid,
    $fixnum.Int64? startTime,
  }) {
    final $result = create();
    if (repeaterDmrid != null) {
      $result.repeaterDmrid = repeaterDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (fieldIntensity != null) {
      $result.fieldIntensity = fieldIntensity;
    }
    if (supportDigital != null) {
      $result.supportDigital = supportDigital;
    }
    if (supportAnalog != null) {
      $result.supportAnalog = supportAnalog;
    }
    if (timeSlotNo != null) {
      $result.timeSlotNo = timeSlotNo;
    }
    if (priority != null) {
      $result.priority = priority;
    }
    if (callType != null) {
      $result.callType = callType;
    }
    if (supportInterrupt != null) {
      $result.supportInterrupt = supportInterrupt;
    }
    if (callStatus != null) {
      $result.callStatus = callStatus;
    }
    if (soundType != null) {
      $result.soundType = soundType;
    }
    if (phoneNo != null) {
      $result.phoneNo = phoneNo;
    }
    if (callDuplex != null) {
      $result.callDuplex = callDuplex;
    }
    if (sourceRepeaterDmrid != null) {
      $result.sourceRepeaterDmrid = sourceRepeaterDmrid;
    }
    if (startTime != null) {
      $result.startTime = startTime;
    }
    return $result;
  }
  bc15._() : super();
  factory bc15.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc15.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc15', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'repeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'fieldIntensity', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'supportDigital', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'supportAnalog', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'timeSlotNo', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'priority', $pb.PbFieldType.O3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'callType', $pb.PbFieldType.O3)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'supportInterrupt', $pb.PbFieldType.O3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'callStatus', $pb.PbFieldType.O3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'soundType', $pb.PbFieldType.O3)
    ..aOS(13, _omitFieldNames ? '' : 'phoneNo')
    ..a<$core.int>(14, _omitFieldNames ? '' : 'callDuplex', $pb.PbFieldType.O3)
    ..a<$core.int>(15, _omitFieldNames ? '' : 'sourceRepeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$fixnum.Int64>(16, _omitFieldNames ? '' : 'startTime', $pb.PbFieldType.OF6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc15 clone() => bc15()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc15 copyWith(void Function(bc15) updates) => super.copyWith((message) => updates(message as bc15)) as bc15;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc15 create() => bc15._();
  bc15 createEmptyInstance() => create();
  static $pb.PbList<bc15> createRepeated() => $pb.PbList<bc15>();
  @$core.pragma('dart2js:noInline')
  static bc15 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc15>(create);
  static bc15? _defaultInstance;

  /// 发起方 中继设备ID
  @$pb.TagNumber(1)
  $core.int get repeaterDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set repeaterDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRepeaterDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRepeaterDmrid() => clearField(1);

  /// 目标id
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// 场强
  @$pb.TagNumber(4)
  $core.int get fieldIntensity => $_getIZ(3);
  @$pb.TagNumber(4)
  set fieldIntensity($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasFieldIntensity() => $_has(3);
  @$pb.TagNumber(4)
  void clearFieldIntensity() => clearField(4);

  /// 语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
  @$pb.TagNumber(5)
  $core.int get supportDigital => $_getIZ(4);
  @$pb.TagNumber(5)
  set supportDigital($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSupportDigital() => $_has(4);
  @$pb.TagNumber(5)
  void clearSupportDigital() => clearField(5);

  /// 语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
  @$pb.TagNumber(6)
  $core.int get supportAnalog => $_getIZ(5);
  @$pb.TagNumber(6)
  set supportAnalog($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSupportAnalog() => $_has(5);
  @$pb.TagNumber(6)
  void clearSupportAnalog() => clearField(6);

  /// 时隙 0：时隙1，　1：时隙2
  @$pb.TagNumber(7)
  $core.int get timeSlotNo => $_getIZ(6);
  @$pb.TagNumber(7)
  set timeSlotNo($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasTimeSlotNo() => $_has(6);
  @$pb.TagNumber(7)
  void clearTimeSlotNo() => clearField(7);

  /// 通话优先级
  ///  0：普通通话， 1：优先级1
  ///  2: 优先级2，  3: 优先级3
  ///  4: 紧急通话
  @$pb.TagNumber(8)
  $core.int get priority => $_getIZ(7);
  @$pb.TagNumber(8)
  set priority($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasPriority() => $_has(7);
  @$pb.TagNumber(8)
  void clearPriority() => clearField(8);

  /// 通话类型
  /// 0：本地通话, 1：联网通话
  @$pb.TagNumber(9)
  $core.int get callType => $_getIZ(8);
  @$pb.TagNumber(9)
  set callType($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasCallType() => $_has(8);
  @$pb.TagNumber(9)
  void clearCallType() => clearField(9);

  /// 优先打断标志
  /// 0：不支持优先打断，1支持优先打断
  @$pb.TagNumber(10)
  $core.int get supportInterrupt => $_getIZ(9);
  @$pb.TagNumber(10)
  set supportInterrupt($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasSupportInterrupt() => $_has(9);
  @$pb.TagNumber(10)
  void clearSupportInterrupt() => clearField(10);

  /// Call Status，通话状态
  /// 0：语音结束, 1：语音开始
  @$pb.TagNumber(11)
  $core.int get callStatus => $_getIZ(10);
  @$pb.TagNumber(11)
  set callStatus($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasCallStatus() => $_has(10);
  @$pb.TagNumber(11)
  void clearCallStatus() => clearField(11);

  /// 话音类型
  ///  0:默认的正常手台语音申请
  ///  1:手台电话会话语音
  ///  2:电话网关过来的开始结束指令
  @$pb.TagNumber(12)
  $core.int get soundType => $_getIZ(11);
  @$pb.TagNumber(12)
  set soundType($core.int v) { $_setSignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasSoundType() => $_has(11);
  @$pb.TagNumber(12)
  void clearSoundType() => clearField(12);

  /// 电话号码,电话相关业务使用,手台上来的bc15是没有此字段的
  @$pb.TagNumber(13)
  $core.String get phoneNo => $_getSZ(12);
  @$pb.TagNumber(13)
  set phoneNo($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasPhoneNo() => $_has(12);
  @$pb.TagNumber(13)
  void clearPhoneNo() => clearField(13);

  /// 通话类型
  /// 0: 半双工
  /// 1: 全双工
  @$pb.TagNumber(14)
  $core.int get callDuplex => $_getIZ(13);
  @$pb.TagNumber(14)
  set callDuplex($core.int v) { $_setSignedInt32(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasCallDuplex() => $_has(13);
  @$pb.TagNumber(14)
  void clearCallDuplex() => clearField(14);

  /// 从机中继ID(针对同播控制器)
  @$pb.TagNumber(15)
  $core.int get sourceRepeaterDmrid => $_getIZ(14);
  @$pb.TagNumber(15)
  set sourceRepeaterDmrid($core.int v) { $_setUnsignedInt32(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasSourceRepeaterDmrid() => $_has(14);
  @$pb.TagNumber(15)
  void clearSourceRepeaterDmrid() => clearField(15);

  /// 通话开始时间，服务器填写,目前只有app会用到
  /// Unix time, the number of seconds elapsed since January 1, 1970 UTC
  @$pb.TagNumber(16)
  $fixnum.Int64 get startTime => $_getI64(15);
  @$pb.TagNumber(16)
  set startTime($fixnum.Int64 v) { $_setInt64(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasStartTime() => $_has(15);
  @$pb.TagNumber(16)
  void clearStartTime() => clearField(16);
}

/// rpc.cmd=10
class bc10 extends $pb.GeneratedMessage {
  factory bc10({
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.int? callType,
    $core.int? priority,
    $core.int? supportInterrupt,
    $core.int? frameNo,
    $core.List<$core.int>? opusData1,
    $core.List<$core.int>? opusData2,
  }) {
    final $result = create();
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (callType != null) {
      $result.callType = callType;
    }
    if (priority != null) {
      $result.priority = priority;
    }
    if (supportInterrupt != null) {
      $result.supportInterrupt = supportInterrupt;
    }
    if (frameNo != null) {
      $result.frameNo = frameNo;
    }
    if (opusData1 != null) {
      $result.opusData1 = opusData1;
    }
    if (opusData2 != null) {
      $result.opusData2 = opusData2;
    }
    return $result;
  }
  bc10._() : super();
  factory bc10.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc10.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc10', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'callType', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'priority', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'supportInterrupt', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'frameNo', $pb.PbFieldType.OF3)
    ..a<$core.List<$core.int>>(9, _omitFieldNames ? '' : 'opusData1', $pb.PbFieldType.OY, protoName: 'opus_data_1')
    ..a<$core.List<$core.int>>(10, _omitFieldNames ? '' : 'opusData2', $pb.PbFieldType.OY, protoName: 'opus_data_2')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc10 clone() => bc10()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc10 copyWith(void Function(bc10) updates) => super.copyWith((message) => updates(message as bc10)) as bc10;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc10 create() => bc10._();
  bc10 createEmptyInstance() => create();
  static $pb.PbList<bc10> createRepeated() => $pb.PbList<bc10>();
  @$core.pragma('dart2js:noInline')
  static bc10 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc10>(create);
  static bc10? _defaultInstance;

  /// 目标id
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(0);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(0);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(1);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(1);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// 通话类型
  /// 0：本地通话, 1：联网通话
  @$pb.TagNumber(5)
  $core.int get callType => $_getIZ(2);
  @$pb.TagNumber(5)
  set callType($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasCallType() => $_has(2);
  @$pb.TagNumber(5)
  void clearCallType() => clearField(5);

  /// 通话优先级
  ///  0：普通通话， 1：优先级1
  ///  2: 优先级2，  3: 优先级3
  ///  4: 紧急通话
  @$pb.TagNumber(6)
  $core.int get priority => $_getIZ(3);
  @$pb.TagNumber(6)
  set priority($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasPriority() => $_has(3);
  @$pb.TagNumber(6)
  void clearPriority() => clearField(6);

  /// 优先打断标志
  /// 0：不支持优先打断，1支持优先打断
  @$pb.TagNumber(7)
  $core.int get supportInterrupt => $_getIZ(4);
  @$pb.TagNumber(7)
  set supportInterrupt($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(7)
  $core.bool hasSupportInterrupt() => $_has(4);
  @$pb.TagNumber(7)
  void clearSupportInterrupt() => clearField(7);

  /// Pld Seq,语音数据帧序列号
  @$pb.TagNumber(8)
  $core.int get frameNo => $_getIZ(5);
  @$pb.TagNumber(8)
  set frameNo($core.int v) { $_setUnsignedInt32(5, v); }
  @$pb.TagNumber(8)
  $core.bool hasFrameNo() => $_has(5);
  @$pb.TagNumber(8)
  void clearFrameNo() => clearField(8);

  /// 60ms语音帧数据, 一般为60ms发送一次，只有opus_data_1有效
  @$pb.TagNumber(9)
  $core.List<$core.int> get opusData1 => $_getN(6);
  @$pb.TagNumber(9)
  set opusData1($core.List<$core.int> v) { $_setBytes(6, v); }
  @$pb.TagNumber(9)
  $core.bool hasOpusData1() => $_has(6);
  @$pb.TagNumber(9)
  void clearOpusData1() => clearField(9);

  /// 网络不好时，可以一次发两个60ms的语音帧数据，opus_data_1和opus_data_2可以同时存在
  @$pb.TagNumber(10)
  $core.List<$core.int> get opusData2 => $_getN(7);
  @$pb.TagNumber(10)
  set opusData2($core.List<$core.int> v) { $_setBytes(7, v); }
  @$pb.TagNumber(10)
  $core.bool hasOpusData2() => $_has(7);
  @$pb.TagNumber(10)
  void clearOpusData2() => clearField(10);
}

/// rpc.cmd=30
class bc30 extends $pb.GeneratedMessage {
  factory bc30({
    $core.int? repeaterDmrid,
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.int? timeSlotNo,
    $core.int? callType,
    $core.int? priority,
    $core.int? supportInterrupt,
    $core.int? frameNo,
    $core.List<$core.int>? ambeData,
    $core.int? soundType,
    $core.int? sourceRepeaterDmrid,
  }) {
    final $result = create();
    if (repeaterDmrid != null) {
      $result.repeaterDmrid = repeaterDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (timeSlotNo != null) {
      $result.timeSlotNo = timeSlotNo;
    }
    if (callType != null) {
      $result.callType = callType;
    }
    if (priority != null) {
      $result.priority = priority;
    }
    if (supportInterrupt != null) {
      $result.supportInterrupt = supportInterrupt;
    }
    if (frameNo != null) {
      $result.frameNo = frameNo;
    }
    if (ambeData != null) {
      $result.ambeData = ambeData;
    }
    if (soundType != null) {
      $result.soundType = soundType;
    }
    if (sourceRepeaterDmrid != null) {
      $result.sourceRepeaterDmrid = sourceRepeaterDmrid;
    }
    return $result;
  }
  bc30._() : super();
  factory bc30.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc30.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc30', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'repeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'timeSlotNo', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'callType', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'priority', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'supportInterrupt', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'frameNo', $pb.PbFieldType.OF3)
    ..a<$core.List<$core.int>>(9, _omitFieldNames ? '' : 'ambeData', $pb.PbFieldType.OY)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'soundType', $pb.PbFieldType.O3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'sourceRepeaterDmrid', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc30 clone() => bc30()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc30 copyWith(void Function(bc30) updates) => super.copyWith((message) => updates(message as bc30)) as bc30;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc30 create() => bc30._();
  bc30 createEmptyInstance() => create();
  static $pb.PbList<bc30> createRepeated() => $pb.PbList<bc30>();
  @$core.pragma('dart2js:noInline')
  static bc30 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc30>(create);
  static bc30? _defaultInstance;

  /// 发起方 中继设备ID
  @$pb.TagNumber(1)
  $core.int get repeaterDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set repeaterDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRepeaterDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRepeaterDmrid() => clearField(1);

  /// 目标id
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// 时隙 0：时隙1，　1：时隙2
  @$pb.TagNumber(4)
  $core.int get timeSlotNo => $_getIZ(3);
  @$pb.TagNumber(4)
  set timeSlotNo($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTimeSlotNo() => $_has(3);
  @$pb.TagNumber(4)
  void clearTimeSlotNo() => clearField(4);

  /// 通话类型
  /// 0：本地通话, 1：联网通话
  @$pb.TagNumber(5)
  $core.int get callType => $_getIZ(4);
  @$pb.TagNumber(5)
  set callType($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCallType() => $_has(4);
  @$pb.TagNumber(5)
  void clearCallType() => clearField(5);

  /// 通话优先级
  ///  0：普通通话， 1：优先级1
  ///  2: 优先级2，  3: 优先级3
  ///  4: 紧急通话
  @$pb.TagNumber(6)
  $core.int get priority => $_getIZ(5);
  @$pb.TagNumber(6)
  set priority($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPriority() => $_has(5);
  @$pb.TagNumber(6)
  void clearPriority() => clearField(6);

  /// 优先打断标志
  /// 0：不支持优先打断，1支持优先打断
  @$pb.TagNumber(7)
  $core.int get supportInterrupt => $_getIZ(6);
  @$pb.TagNumber(7)
  set supportInterrupt($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSupportInterrupt() => $_has(6);
  @$pb.TagNumber(7)
  void clearSupportInterrupt() => clearField(7);

  /// Pld Seq,语音数据帧序列号
  @$pb.TagNumber(8)
  $core.int get frameNo => $_getIZ(7);
  @$pb.TagNumber(8)
  set frameNo($core.int v) { $_setUnsignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasFrameNo() => $_has(7);
  @$pb.TagNumber(8)
  void clearFrameNo() => clearField(8);

  /// 语音帧数据
  @$pb.TagNumber(9)
  $core.List<$core.int> get ambeData => $_getN(8);
  @$pb.TagNumber(9)
  set ambeData($core.List<$core.int> v) { $_setBytes(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasAmbeData() => $_has(8);
  @$pb.TagNumber(9)
  void clearAmbeData() => clearField(9);

  /// 语音类型
  /// 0:手台产生的语音
  /// 2:电话网关送过来的语音
  @$pb.TagNumber(10)
  $core.int get soundType => $_getIZ(9);
  @$pb.TagNumber(10)
  set soundType($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasSoundType() => $_has(9);
  @$pb.TagNumber(10)
  void clearSoundType() => clearField(10);

  /// 从机中继ID(针对同播控制器)
  @$pb.TagNumber(11)
  $core.int get sourceRepeaterDmrid => $_getIZ(10);
  @$pb.TagNumber(11)
  set sourceRepeaterDmrid($core.int v) { $_setUnsignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasSourceRepeaterDmrid() => $_has(10);
  @$pb.TagNumber(11)
  void clearSourceRepeaterDmrid() => clearField(11);
}

/// rpc.cmd=33
/// 手台输入dtmf
class dtmf extends $pb.GeneratedMessage {
  factory dtmf({
    $core.int? repeaterDmrid,
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.String? dtmfStr,
    $core.int? sourceRepeaterDmrid,
  }) {
    final $result = create();
    if (repeaterDmrid != null) {
      $result.repeaterDmrid = repeaterDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (dtmfStr != null) {
      $result.dtmfStr = dtmfStr;
    }
    if (sourceRepeaterDmrid != null) {
      $result.sourceRepeaterDmrid = sourceRepeaterDmrid;
    }
    return $result;
  }
  dtmf._() : super();
  factory dtmf.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory dtmf.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'dtmf', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'repeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..aOS(6, _omitFieldNames ? '' : 'dtmfStr')
    ..a<$core.int>(7, _omitFieldNames ? '' : 'sourceRepeaterDmrid', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  dtmf clone() => dtmf()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  dtmf copyWith(void Function(dtmf) updates) => super.copyWith((message) => updates(message as dtmf)) as dtmf;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static dtmf create() => dtmf._();
  dtmf createEmptyInstance() => create();
  static $pb.PbList<dtmf> createRepeated() => $pb.PbList<dtmf>();
  @$core.pragma('dart2js:noInline')
  static dtmf getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<dtmf>(create);
  static dtmf? _defaultInstance;

  /// 发起方 中继设备ID
  @$pb.TagNumber(1)
  $core.int get repeaterDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set repeaterDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRepeaterDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRepeaterDmrid() => clearField(1);

  /// 目标id
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// dtmf 字符串
  @$pb.TagNumber(6)
  $core.String get dtmfStr => $_getSZ(3);
  @$pb.TagNumber(6)
  set dtmfStr($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasDtmfStr() => $_has(3);
  @$pb.TagNumber(6)
  void clearDtmfStr() => clearField(6);

  /// 从机中继ID(针对同播控制器)
  @$pb.TagNumber(7)
  $core.int get sourceRepeaterDmrid => $_getIZ(4);
  @$pb.TagNumber(7)
  set sourceRepeaterDmrid($core.int v) { $_setUnsignedInt32(4, v); }
  @$pb.TagNumber(7)
  $core.bool hasSourceRepeaterDmrid() => $_has(4);
  @$pb.TagNumber(7)
  void clearSourceRepeaterDmrid() => clearField(7);
}

/// rpc.cmd=34
/// 结束电话通话
class end_call extends $pb.GeneratedMessage {
  factory end_call({
    $core.int? repeaterDmrid,
    $core.int? targetDmrid,
    $core.int? sourceDmrid,
    $core.int? soundType,
    $core.String? phoneNo,
    $core.int? sourceRepeaterDmrid,
  }) {
    final $result = create();
    if (repeaterDmrid != null) {
      $result.repeaterDmrid = repeaterDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (sourceDmrid != null) {
      $result.sourceDmrid = sourceDmrid;
    }
    if (soundType != null) {
      $result.soundType = soundType;
    }
    if (phoneNo != null) {
      $result.phoneNo = phoneNo;
    }
    if (sourceRepeaterDmrid != null) {
      $result.sourceRepeaterDmrid = sourceRepeaterDmrid;
    }
    return $result;
  }
  end_call._() : super();
  factory end_call.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory end_call.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'end_call', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'repeaterDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'sourceDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'soundType', $pb.PbFieldType.O3)
    ..aOS(13, _omitFieldNames ? '' : 'phoneNo')
    ..a<$core.int>(14, _omitFieldNames ? '' : 'sourceRepeaterDmrid', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  end_call clone() => end_call()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  end_call copyWith(void Function(end_call) updates) => super.copyWith((message) => updates(message as end_call)) as end_call;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static end_call create() => end_call._();
  end_call createEmptyInstance() => create();
  static $pb.PbList<end_call> createRepeated() => $pb.PbList<end_call>();
  @$core.pragma('dart2js:noInline')
  static end_call getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<end_call>(create);
  static end_call? _defaultInstance;

  /// 发起方 中继设备ID
  @$pb.TagNumber(1)
  $core.int get repeaterDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set repeaterDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRepeaterDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRepeaterDmrid() => clearField(1);

  /// 目标id
  @$pb.TagNumber(2)
  $core.int get targetDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetDmrid() => clearField(2);

  /// 源id
  @$pb.TagNumber(3)
  $core.int get sourceDmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set sourceDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSourceDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearSourceDmrid() => clearField(3);

  /// 话音类型
  ///  1:手台电话会话过来的
  ///  2:电话网关过来的
  @$pb.TagNumber(12)
  $core.int get soundType => $_getIZ(3);
  @$pb.TagNumber(12)
  set soundType($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(12)
  $core.bool hasSoundType() => $_has(3);
  @$pb.TagNumber(12)
  void clearSoundType() => clearField(12);

  /// 电话号码,电话相关业务使用
  @$pb.TagNumber(13)
  $core.String get phoneNo => $_getSZ(4);
  @$pb.TagNumber(13)
  set phoneNo($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(13)
  $core.bool hasPhoneNo() => $_has(4);
  @$pb.TagNumber(13)
  void clearPhoneNo() => clearField(13);

  /// 从机中继ID(针对同播控制器)
  @$pb.TagNumber(14)
  $core.int get sourceRepeaterDmrid => $_getIZ(5);
  @$pb.TagNumber(14)
  set sourceRepeaterDmrid($core.int v) { $_setUnsignedInt32(5, v); }
  @$pb.TagNumber(14)
  $core.bool hasSourceRepeaterDmrid() => $_has(5);
  @$pb.TagNumber(14)
  void clearSourceRepeaterDmrid() => clearField(14);
}

/// rpc.cmd=35
/// 电话转接命令,发送给电话网关
class phone_transfer extends $pb.GeneratedMessage {
  factory phone_transfer({
    $core.int? phoneDmrid,
    $core.int? nowTarget,
    $core.int? transferTarget,
  }) {
    final $result = create();
    if (phoneDmrid != null) {
      $result.phoneDmrid = phoneDmrid;
    }
    if (nowTarget != null) {
      $result.nowTarget = nowTarget;
    }
    if (transferTarget != null) {
      $result.transferTarget = transferTarget;
    }
    return $result;
  }
  phone_transfer._() : super();
  factory phone_transfer.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory phone_transfer.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'phone_transfer', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'phoneDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'nowTarget', $pb.PbFieldType.OF3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'transferTarget', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  phone_transfer clone() => phone_transfer()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  phone_transfer copyWith(void Function(phone_transfer) updates) => super.copyWith((message) => updates(message as phone_transfer)) as phone_transfer;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static phone_transfer create() => phone_transfer._();
  phone_transfer createEmptyInstance() => create();
  static $pb.PbList<phone_transfer> createRepeated() => $pb.PbList<phone_transfer>();
  @$core.pragma('dart2js:noInline')
  static phone_transfer getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<phone_transfer>(create);
  static phone_transfer? _defaultInstance;

  /// 电话的dmrid(当前的讲话者)
  @$pb.TagNumber(2)
  $core.int get phoneDmrid => $_getIZ(0);
  @$pb.TagNumber(2)
  set phoneDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(2)
  $core.bool hasPhoneDmrid() => $_has(0);
  @$pb.TagNumber(2)
  void clearPhoneDmrid() => clearField(2);

  /// 当前的讲话目标
  @$pb.TagNumber(3)
  $core.int get nowTarget => $_getIZ(1);
  @$pb.TagNumber(3)
  set nowTarget($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasNowTarget() => $_has(1);
  @$pb.TagNumber(3)
  void clearNowTarget() => clearField(3);

  /// 要转接到的目标dmrid
  @$pb.TagNumber(4)
  $core.int get transferTarget => $_getIZ(2);
  @$pb.TagNumber(4)
  set transferTarget($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasTransferTarget() => $_has(2);
  @$pb.TagNumber(4)
  void clearTransferTarget() => clearField(4);
}

/// 手台频道数据
class OneChannelItem extends $pb.GeneratedMessage {
  factory OneChannelItem({
    $core.int? no,
    $core.String? sendGroup,
    $core.Iterable<$core.String>? listenGroup,
    $core.String? zoneRid,
  }) {
    final $result = create();
    if (no != null) {
      $result.no = no;
    }
    if (sendGroup != null) {
      $result.sendGroup = sendGroup;
    }
    if (listenGroup != null) {
      $result.listenGroup.addAll(listenGroup);
    }
    if (zoneRid != null) {
      $result.zoneRid = zoneRid;
    }
    return $result;
  }
  OneChannelItem._() : super();
  factory OneChannelItem.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory OneChannelItem.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'OneChannelItem', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'no', $pb.PbFieldType.O3)
    ..aOS(2, _omitFieldNames ? '' : 'sendGroup', protoName: 'sendGroup')
    ..pPS(3, _omitFieldNames ? '' : 'listenGroup', protoName: 'listenGroup')
    ..aOS(4, _omitFieldNames ? '' : 'zoneRid', protoName: 'zoneRid')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  OneChannelItem clone() => OneChannelItem()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  OneChannelItem copyWith(void Function(OneChannelItem) updates) => super.copyWith((message) => updates(message as OneChannelItem)) as OneChannelItem;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static OneChannelItem create() => OneChannelItem._();
  OneChannelItem createEmptyInstance() => create();
  static $pb.PbList<OneChannelItem> createRepeated() => $pb.PbList<OneChannelItem>();
  @$core.pragma('dart2js:noInline')
  static OneChannelItem getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<OneChannelItem>(create);
  static OneChannelItem? _defaultInstance;

  /// 频道号
  @$pb.TagNumber(1)
  $core.int get no => $_getIZ(0);
  @$pb.TagNumber(1)
  set no($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasNo() => $_has(0);
  @$pb.TagNumber(1)
  void clearNo() => clearField(1);

  /// 默认发射组dmrid
  @$pb.TagNumber(2)
  $core.String get sendGroup => $_getSZ(1);
  @$pb.TagNumber(2)
  set sendGroup($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSendGroup() => $_has(1);
  @$pb.TagNumber(2)
  void clearSendGroup() => clearField(2);

  /// 接收组dmrid列表
  @$pb.TagNumber(3)
  $core.List<$core.String> get listenGroup => $_getList(2);

  /// 频道所在区域rid
  @$pb.TagNumber(4)
  $core.String get zoneRid => $_getSZ(3);
  @$pb.TagNumber(4)
  set zoneRid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasZoneRid() => $_has(3);
  @$pb.TagNumber(4)
  void clearZoneRid() => clearField(4);
}

/// 手台的频道相关数据
///  rpc.cmd=300
class Channels extends $pb.GeneratedMessage {
  factory Channels({
    $core.Iterable<OneChannelItem>? channels,
    $core.String? deviceDmrid,
    $core.int? devicePriority,
  }) {
    final $result = create();
    if (channels != null) {
      $result.channels.addAll(channels);
    }
    if (deviceDmrid != null) {
      $result.deviceDmrid = deviceDmrid;
    }
    if (devicePriority != null) {
      $result.devicePriority = devicePriority;
    }
    return $result;
  }
  Channels._() : super();
  factory Channels.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Channels.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Channels', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..pc<OneChannelItem>(1, _omitFieldNames ? '' : 'channels', $pb.PbFieldType.PM, subBuilder: OneChannelItem.create)
    ..aOS(2, _omitFieldNames ? '' : 'deviceDmrid')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'devicePriority', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Channels clone() => Channels()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Channels copyWith(void Function(Channels) updates) => super.copyWith((message) => updates(message as Channels)) as Channels;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Channels create() => Channels._();
  Channels createEmptyInstance() => create();
  static $pb.PbList<Channels> createRepeated() => $pb.PbList<Channels>();
  @$core.pragma('dart2js:noInline')
  static Channels getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Channels>(create);
  static Channels? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<OneChannelItem> get channels => $_getList(0);

  @$pb.TagNumber(2)
  $core.String get deviceDmrid => $_getSZ(1);
  @$pb.TagNumber(2)
  set deviceDmrid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDeviceDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearDeviceDmrid() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get devicePriority => $_getIZ(2);
  @$pb.TagNumber(3)
  set devicePriority($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDevicePriority() => $_has(2);
  @$pb.TagNumber(3)
  void clearDevicePriority() => clearField(3);
}

///  rpc.cmd=201
/// 电话网关向后台查询短号对应的dmrid
class PhoneAdapterShortNo2DmridReq extends $pb.GeneratedMessage {
  factory PhoneAdapterShortNo2DmridReq({
    $core.String? shortNo,
    $core.int? opt,
    $core.int? dmrid,
  }) {
    final $result = create();
    if (shortNo != null) {
      $result.shortNo = shortNo;
    }
    if (opt != null) {
      $result.opt = opt;
    }
    if (dmrid != null) {
      $result.dmrid = dmrid;
    }
    return $result;
  }
  PhoneAdapterShortNo2DmridReq._() : super();
  factory PhoneAdapterShortNo2DmridReq.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PhoneAdapterShortNo2DmridReq.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PhoneAdapterShortNo2DmridReq', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'shortNo')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'opt', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'dmrid', $pb.PbFieldType.OF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PhoneAdapterShortNo2DmridReq clone() => PhoneAdapterShortNo2DmridReq()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PhoneAdapterShortNo2DmridReq copyWith(void Function(PhoneAdapterShortNo2DmridReq) updates) => super.copyWith((message) => updates(message as PhoneAdapterShortNo2DmridReq)) as PhoneAdapterShortNo2DmridReq;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PhoneAdapterShortNo2DmridReq create() => PhoneAdapterShortNo2DmridReq._();
  PhoneAdapterShortNo2DmridReq createEmptyInstance() => create();
  static $pb.PbList<PhoneAdapterShortNo2DmridReq> createRepeated() => $pb.PbList<PhoneAdapterShortNo2DmridReq>();
  @$core.pragma('dart2js:noInline')
  static PhoneAdapterShortNo2DmridReq getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PhoneAdapterShortNo2DmridReq>(create);
  static PhoneAdapterShortNo2DmridReq? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get shortNo => $_getSZ(0);
  @$pb.TagNumber(1)
  set shortNo($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasShortNo() => $_has(0);
  @$pb.TagNumber(1)
  void clearShortNo() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get opt => $_getIZ(1);
  @$pb.TagNumber(2)
  set opt($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOpt() => $_has(1);
  @$pb.TagNumber(2)
  void clearOpt() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get dmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set dmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearDmrid() => clearField(3);
}

/// 后台回应电话网关查询短号
class PhoneAdapterShortNo2DmridRes extends $pb.GeneratedMessage {
  factory PhoneAdapterShortNo2DmridRes({
    $core.int? result,
    $core.int? dmrid,
    $core.String? info,
  }) {
    final $result = create();
    if (result != null) {
      $result.result = result;
    }
    if (dmrid != null) {
      $result.dmrid = dmrid;
    }
    if (info != null) {
      $result.info = info;
    }
    return $result;
  }
  PhoneAdapterShortNo2DmridRes._() : super();
  factory PhoneAdapterShortNo2DmridRes.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PhoneAdapterShortNo2DmridRes.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PhoneAdapterShortNo2DmridRes', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'result', $pb.PbFieldType.O3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'dmrid', $pb.PbFieldType.OF3)
    ..aOS(3, _omitFieldNames ? '' : 'info')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PhoneAdapterShortNo2DmridRes clone() => PhoneAdapterShortNo2DmridRes()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PhoneAdapterShortNo2DmridRes copyWith(void Function(PhoneAdapterShortNo2DmridRes) updates) => super.copyWith((message) => updates(message as PhoneAdapterShortNo2DmridRes)) as PhoneAdapterShortNo2DmridRes;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PhoneAdapterShortNo2DmridRes create() => PhoneAdapterShortNo2DmridRes._();
  PhoneAdapterShortNo2DmridRes createEmptyInstance() => create();
  static $pb.PbList<PhoneAdapterShortNo2DmridRes> createRepeated() => $pb.PbList<PhoneAdapterShortNo2DmridRes>();
  @$core.pragma('dart2js:noInline')
  static PhoneAdapterShortNo2DmridRes getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PhoneAdapterShortNo2DmridRes>(create);
  static PhoneAdapterShortNo2DmridRes? _defaultInstance;

  /// 结果码
  ///  0:成功获得对应dmrid
  ///  1:数据库没有此短号
  ///  2:数据库出错,无法查询
  ///  3:marshal/unmarshal error
  @$pb.TagNumber(1)
  $core.int get result => $_getIZ(0);
  @$pb.TagNumber(1)
  set result($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasResult() => $_has(0);
  @$pb.TagNumber(1)
  void clearResult() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get dmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set dmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearDmrid() => clearField(2);

  /// err info
  @$pb.TagNumber(3)
  $core.String get info => $_getSZ(2);
  @$pb.TagNumber(3)
  set info($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasInfo() => $_has(2);
  @$pb.TagNumber(3)
  void clearInfo() => clearField(3);
}

/// rpc.cmd=205
/// 查询与返回电话网关中电话配置信息
class PhoneLineSetting extends $pb.GeneratedMessage {
  factory PhoneLineSetting({
    $core.int? actionCode,
    $core.Iterable<$core.int>? linePos,
    $core.Iterable<$core.String>? linePhoneNo,
    $core.Iterable<$core.int>? lineDmrid,
  }) {
    final $result = create();
    if (actionCode != null) {
      $result.actionCode = actionCode;
    }
    if (linePos != null) {
      $result.linePos.addAll(linePos);
    }
    if (linePhoneNo != null) {
      $result.linePhoneNo.addAll(linePhoneNo);
    }
    if (lineDmrid != null) {
      $result.lineDmrid.addAll(lineDmrid);
    }
    return $result;
  }
  PhoneLineSetting._() : super();
  factory PhoneLineSetting.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PhoneLineSetting.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PhoneLineSetting', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'actionCode', $pb.PbFieldType.O3)
    ..p<$core.int>(2, _omitFieldNames ? '' : 'linePos', $pb.PbFieldType.K3)
    ..pPS(3, _omitFieldNames ? '' : 'linePhoneNo')
    ..p<$core.int>(4, _omitFieldNames ? '' : 'lineDmrid', $pb.PbFieldType.KF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PhoneLineSetting clone() => PhoneLineSetting()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PhoneLineSetting copyWith(void Function(PhoneLineSetting) updates) => super.copyWith((message) => updates(message as PhoneLineSetting)) as PhoneLineSetting;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PhoneLineSetting create() => PhoneLineSetting._();
  PhoneLineSetting createEmptyInstance() => create();
  static $pb.PbList<PhoneLineSetting> createRepeated() => $pb.PbList<PhoneLineSetting>();
  @$core.pragma('dart2js:noInline')
  static PhoneLineSetting getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PhoneLineSetting>(create);
  static PhoneLineSetting? _defaultInstance;

  /// 动作码
  /// 1: 网关查询电话配置
  /// 2: 后台回应配置信息
  /// <0:后台出错了
  @$pb.TagNumber(1)
  $core.int get actionCode => $_getIZ(0);
  @$pb.TagNumber(1)
  set actionCode($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasActionCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearActionCode() => clearField(1);

  /// 以后三个为电话线配置信息,数组一一对应
  /// 电话线位置,
  @$pb.TagNumber(2)
  $core.List<$core.int> get linePos => $_getList(1);

  /// 电话号码
  @$pb.TagNumber(3)
  $core.List<$core.String> get linePhoneNo => $_getList(2);

  /// 电话对应的dmrid
  @$pb.TagNumber(4)
  $core.List<$core.int> get lineDmrid => $_getList(3);
}

/// rpc.cmd=320
/// 网关终端请求群组(收听组)数据回应
class ex_one_org extends $pb.GeneratedMessage {
  factory ex_one_org({
    $core.String? rid,
    $core.String? orgSelfId,
    $core.String? orgShortName,
    $core.String? orgFullName,
    $core.String? note,
    $core.int? orgIsVirtual,
    $core.String? dmrId,
    $core.String? parentOrgId,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgSelfId != null) {
      $result.orgSelfId = orgSelfId;
    }
    if (orgShortName != null) {
      $result.orgShortName = orgShortName;
    }
    if (orgFullName != null) {
      $result.orgFullName = orgFullName;
    }
    if (note != null) {
      $result.note = note;
    }
    if (orgIsVirtual != null) {
      $result.orgIsVirtual = orgIsVirtual;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (parentOrgId != null) {
      $result.parentOrgId = parentOrgId;
    }
    return $result;
  }
  ex_one_org._() : super();
  factory ex_one_org.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ex_one_org.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ex_one_org', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(3, _omitFieldNames ? '' : 'orgSelfId')
    ..aOS(5, _omitFieldNames ? '' : 'orgShortName')
    ..aOS(6, _omitFieldNames ? '' : 'orgFullName')
    ..aOS(7, _omitFieldNames ? '' : 'note')
    ..a<$core.int>(8, _omitFieldNames ? '' : 'orgIsVirtual', $pb.PbFieldType.O3)
    ..aOS(9, _omitFieldNames ? '' : 'dmrId')
    ..aOS(11, _omitFieldNames ? '' : 'parentOrgId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ex_one_org clone() => ex_one_org()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ex_one_org copyWith(void Function(ex_one_org) updates) => super.copyWith((message) => updates(message as ex_one_org)) as ex_one_org;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ex_one_org create() => ex_one_org._();
  ex_one_org createEmptyInstance() => create();
  static $pb.PbList<ex_one_org> createRepeated() => $pb.PbList<ex_one_org>();
  @$core.pragma('dart2js:noInline')
  static ex_one_org getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ex_one_org>(create);
  static ex_one_org? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table varchar(64) unique not null
  /// 组织机构编号
  @$pb.TagNumber(3)
  $core.String get orgSelfId => $_getSZ(1);
  @$pb.TagNumber(3)
  set orgSelfId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgSelfId() => $_has(1);
  @$pb.TagNumber(3)
  void clearOrgSelfId() => clearField(3);

  /// @table varchar(32) unique not null
  /// 机构名称,缩写
  @$pb.TagNumber(5)
  $core.String get orgShortName => $_getSZ(2);
  @$pb.TagNumber(5)
  set orgShortName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasOrgShortName() => $_has(2);
  @$pb.TagNumber(5)
  void clearOrgShortName() => clearField(5);

  /// @table varchar(256)
  /// 机构名称,全称
  @$pb.TagNumber(6)
  $core.String get orgFullName => $_getSZ(3);
  @$pb.TagNumber(6)
  set orgFullName($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasOrgFullName() => $_has(3);
  @$pb.TagNumber(6)
  void clearOrgFullName() => clearField(6);

  /// @table text
  /// 机构详细描述
  @$pb.TagNumber(7)
  $core.String get note => $_getSZ(4);
  @$pb.TagNumber(7)
  set note($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(7)
  $core.bool hasNote() => $_has(4);
  @$pb.TagNumber(7)
  void clearNote() => clearField(7);

  /// @table int default 2
  /// 2:真实机构,1:虚拟机构 虚拟机构没有真实设备和用户,只是引用真实机构的数据,相当于额外的分组
  @$pb.TagNumber(8)
  $core.int get orgIsVirtual => $_getIZ(5);
  @$pb.TagNumber(8)
  set orgIsVirtual($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(8)
  $core.bool hasOrgIsVirtual() => $_has(5);
  @$pb.TagNumber(8)
  void clearOrgIsVirtual() => clearField(8);

  /// @table varchar(8) unique
  /// DMR ID,可用作组呼的ID
  @$pb.TagNumber(9)
  $core.String get dmrId => $_getSZ(6);
  @$pb.TagNumber(9)
  set dmrId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(9)
  $core.bool hasDmrId() => $_has(6);
  @$pb.TagNumber(9)
  void clearDmrId() => clearField(9);

  /// @table uuid not null default '11111111-1111-1111-1111-111111111111'
  /// 此组织的上级机构
  @$pb.TagNumber(11)
  $core.String get parentOrgId => $_getSZ(7);
  @$pb.TagNumber(11)
  set parentOrgId($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(11)
  $core.bool hasParentOrgId() => $_has(7);
  @$pb.TagNumber(11)
  void clearParentOrgId() => clearField(11);
}

/// rpc.cmd=330
/// 网关终端请求所有终端数据回应
class ex_one_device extends $pb.GeneratedMessage {
  factory ex_one_device({
    $core.String? orgId,
    $core.String? selfId,
    $core.String? dmrId,
    $core.String? virOrgs,
    $core.String? note,
    $core.int? deviceType,
    $core.int? priority,
  }) {
    final $result = create();
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (selfId != null) {
      $result.selfId = selfId;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (virOrgs != null) {
      $result.virOrgs = virOrgs;
    }
    if (note != null) {
      $result.note = note;
    }
    if (deviceType != null) {
      $result.deviceType = deviceType;
    }
    if (priority != null) {
      $result.priority = priority;
    }
    return $result;
  }
  ex_one_device._() : super();
  factory ex_one_device.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ex_one_device.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ex_one_device', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'selfId')
    ..aOS(5, _omitFieldNames ? '' : 'dmrId')
    ..aOS(6, _omitFieldNames ? '' : 'virOrgs')
    ..aOS(8, _omitFieldNames ? '' : 'note')
    ..a<$core.int>(9, _omitFieldNames ? '' : 'deviceType', $pb.PbFieldType.O3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'priority', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ex_one_device clone() => ex_one_device()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ex_one_device copyWith(void Function(ex_one_device) updates) => super.copyWith((message) => updates(message as ex_one_device)) as ex_one_device;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ex_one_device create() => ex_one_device._();
  ex_one_device createEmptyInstance() => create();
  static $pb.PbList<ex_one_device> createRepeated() => $pb.PbList<ex_one_device>();
  @$core.pragma('dart2js:noInline')
  static ex_one_device getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ex_one_device>(create);
  static ex_one_device? _defaultInstance;

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  /// 设备所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(0);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(0);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null unique
  /// 设备名称
  @$pb.TagNumber(4)
  $core.String get selfId => $_getSZ(1);
  @$pb.TagNumber(4)
  set selfId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(4)
  $core.bool hasSelfId() => $_has(1);
  @$pb.TagNumber(4)
  void clearSelfId() => clearField(4);

  /// @table varchar(16) not null unique
  /// 设备DMR-ID
  @$pb.TagNumber(5)
  $core.String get dmrId => $_getSZ(2);
  @$pb.TagNumber(5)
  set dmrId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasDmrId() => $_has(2);
  @$pb.TagNumber(5)
  void clearDmrId() => clearField(5);

  /// @table text default ''
  /// 对讲机所属的虚拟群组,逗号分隔的群组rid
  @$pb.TagNumber(6)
  $core.String get virOrgs => $_getSZ(3);
  @$pb.TagNumber(6)
  set virOrgs($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasVirOrgs() => $_has(3);
  @$pb.TagNumber(6)
  void clearVirOrgs() => clearField(6);

  /// @table text default ''
  /// 设备备注信息
  @$pb.TagNumber(8)
  $core.String get note => $_getSZ(4);
  @$pb.TagNumber(8)
  set note($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(8)
  $core.bool hasNote() => $_has(4);
  @$pb.TagNumber(8)
  void clearNote() => clearField(8);

  /// @table int not null default 0
  /// 设备类型 0:对讲机手台 1：车台 3:电话网关设备 4:中继虚拟终端 5:互联网关终端
  @$pb.TagNumber(9)
  $core.int get deviceType => $_getIZ(5);
  @$pb.TagNumber(9)
  set deviceType($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(9)
  $core.bool hasDeviceType() => $_has(5);
  @$pb.TagNumber(9)
  void clearDeviceType() => clearField(9);

  /// @table int
  /// 优先级
  @$pb.TagNumber(12)
  $core.int get priority => $_getIZ(6);
  @$pb.TagNumber(12)
  set priority($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(12)
  $core.bool hasPriority() => $_has(6);
  @$pb.TagNumber(12)
  void clearPriority() => clearField(12);
}

/// rpc.cmd=180
/// 查询ambe 序列号
class ambe_serial_code extends $pb.GeneratedMessage {
  factory ambe_serial_code({
    $core.Iterable<$core.int>? code,
  }) {
    final $result = create();
    if (code != null) {
      $result.code.addAll(code);
    }
    return $result;
  }
  ambe_serial_code._() : super();
  factory ambe_serial_code.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ambe_serial_code.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ambe_serial_code', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..p<$core.int>(1, _omitFieldNames ? '' : 'code', $pb.PbFieldType.K3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ambe_serial_code clone() => ambe_serial_code()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ambe_serial_code copyWith(void Function(ambe_serial_code) updates) => super.copyWith((message) => updates(message as ambe_serial_code)) as ambe_serial_code;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ambe_serial_code create() => ambe_serial_code._();
  ambe_serial_code createEmptyInstance() => create();
  static $pb.PbList<ambe_serial_code> createRepeated() => $pb.PbList<ambe_serial_code>();
  @$core.pragma('dart2js:noInline')
  static ambe_serial_code getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ambe_serial_code>(create);
  static ambe_serial_code? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get code => $_getList(0);
}

/// rpc.cmd=333
/// 在线终端信息
class ex_oneline_devices extends $pb.GeneratedMessage {
  factory ex_oneline_devices({
    $core.Iterable<$core.int>? dmrId,
    $core.Iterable<$fixnum.Int64>? lastDataTime,
  }) {
    final $result = create();
    if (dmrId != null) {
      $result.dmrId.addAll(dmrId);
    }
    if (lastDataTime != null) {
      $result.lastDataTime.addAll(lastDataTime);
    }
    return $result;
  }
  ex_oneline_devices._() : super();
  factory ex_oneline_devices.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ex_oneline_devices.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ex_oneline_devices', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..p<$core.int>(1, _omitFieldNames ? '' : 'dmrId', $pb.PbFieldType.K3)
    ..p<$fixnum.Int64>(2, _omitFieldNames ? '' : 'lastDataTime', $pb.PbFieldType.K6)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ex_oneline_devices clone() => ex_oneline_devices()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ex_oneline_devices copyWith(void Function(ex_oneline_devices) updates) => super.copyWith((message) => updates(message as ex_oneline_devices)) as ex_oneline_devices;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ex_oneline_devices create() => ex_oneline_devices._();
  ex_oneline_devices createEmptyInstance() => create();
  static $pb.PbList<ex_oneline_devices> createRepeated() => $pb.PbList<ex_oneline_devices>();
  @$core.pragma('dart2js:noInline')
  static ex_oneline_devices getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ex_oneline_devices>(create);
  static ex_oneline_devices? _defaultInstance;

  /// 在线的终端dmrid
  @$pb.TagNumber(1)
  $core.List<$core.int> get dmrId => $_getList(0);

  /// 终端对应的最后数据时间time.unix(单位:s)
  @$pb.TagNumber(2)
  $core.List<$fixnum.Int64> get lastDataTime => $_getList(1);
}

/// rpc.cmd=3
/// 只能上行是3,下行的cmd=3已经被用于系统后台发短信
/// 物联网数据
class iot_data extends $pb.GeneratedMessage {
  factory iot_data({
    $core.int? cmd,
    $core.int? devType,
    $core.List<$core.int>? devId,
    $core.List<$core.int>? cmdParam,
    $core.List<$core.int>? recvDevId,
    $fixnum.Int64? recvTime,
    $core.String? kcpRecvDevId,
  }) {
    final $result = create();
    if (cmd != null) {
      $result.cmd = cmd;
    }
    if (devType != null) {
      $result.devType = devType;
    }
    if (devId != null) {
      $result.devId = devId;
    }
    if (cmdParam != null) {
      $result.cmdParam = cmdParam;
    }
    if (recvDevId != null) {
      $result.recvDevId = recvDevId;
    }
    if (recvTime != null) {
      $result.recvTime = recvTime;
    }
    if (kcpRecvDevId != null) {
      $result.kcpRecvDevId = kcpRecvDevId;
    }
    return $result;
  }
  iot_data._() : super();
  factory iot_data.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory iot_data.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'iot_data', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'cmd', $pb.PbFieldType.OS3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'devType', $pb.PbFieldType.OS3)
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'devId', $pb.PbFieldType.OY)
    ..a<$core.List<$core.int>>(4, _omitFieldNames ? '' : 'cmdParam', $pb.PbFieldType.OY)
    ..a<$core.List<$core.int>>(9, _omitFieldNames ? '' : 'recvDevId', $pb.PbFieldType.OY)
    ..a<$fixnum.Int64>(10, _omitFieldNames ? '' : 'recvTime', $pb.PbFieldType.OS6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..aOS(11, _omitFieldNames ? '' : 'kcpRecvDevId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  iot_data clone() => iot_data()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  iot_data copyWith(void Function(iot_data) updates) => super.copyWith((message) => updates(message as iot_data)) as iot_data;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static iot_data create() => iot_data._();
  iot_data createEmptyInstance() => create();
  static $pb.PbList<iot_data> createRepeated() => $pb.PbList<iot_data>();
  @$core.pragma('dart2js:noInline')
  static iot_data getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<iot_data>(create);
  static iot_data? _defaultInstance;

  /// 指令
  @$pb.TagNumber(1)
  $core.int get cmd => $_getIZ(0);
  @$pb.TagNumber(1)
  set cmd($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCmd() => $_has(0);
  @$pb.TagNumber(1)
  void clearCmd() => clearField(1);

  /// 设备类型
  @$pb.TagNumber(2)
  $core.int get devType => $_getIZ(1);
  @$pb.TagNumber(2)
  set devType($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDevType() => $_has(1);
  @$pb.TagNumber(2)
  void clearDevType() => clearField(2);

  /// 终端设备ID,iot发射终端
  @$pb.TagNumber(3)
  $core.List<$core.int> get devId => $_getN(2);
  @$pb.TagNumber(3)
  set devId($core.List<$core.int> v) { $_setBytes(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDevId() => $_has(2);
  @$pb.TagNumber(3)
  void clearDevId() => clearField(3);

  /// 指令参数
  @$pb.TagNumber(4)
  $core.List<$core.int> get cmdParam => $_getN(3);
  @$pb.TagNumber(4)
  set cmdParam($core.List<$core.int> v) { $_setBytes(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCmdParam() => $_has(3);
  @$pb.TagNumber(4)
  void clearCmdParam() => clearField(4);

  /// iot接收设备ID
  @$pb.TagNumber(9)
  $core.List<$core.int> get recvDevId => $_getN(4);
  @$pb.TagNumber(9)
  set recvDevId($core.List<$core.int> v) { $_setBytes(4, v); }
  @$pb.TagNumber(9)
  $core.bool hasRecvDevId() => $_has(4);
  @$pb.TagNumber(9)
  void clearRecvDevId() => clearField(9);

  /// 接收时间,unix epoch时间(秒)
  @$pb.TagNumber(10)
  $fixnum.Int64 get recvTime => $_getI64(5);
  @$pb.TagNumber(10)
  set recvTime($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(10)
  $core.bool hasRecvTime() => $_has(5);
  @$pb.TagNumber(10)
  void clearRecvTime() => clearField(10);

  /// 后台接收此数据的控制器ID，(控制器上传时不需要填写此字段)
  @$pb.TagNumber(11)
  $core.String get kcpRecvDevId => $_getSZ(6);
  @$pb.TagNumber(11)
  set kcpRecvDevId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(11)
  $core.bool hasKcpRecvDevId() => $_has(6);
  @$pb.TagNumber(11)
  void clearKcpRecvDevId() => clearField(11);
}

/// rpc.cmd=8
/// 广播给app终端
class dev_data_info extends $pb.GeneratedMessage {
  factory dev_data_info({
    $core.int? code,
    $core.int? srcDmrid,
    $core.int? dstDmrid,
    $core.List<$core.int>? data,
  }) {
    final $result = create();
    if (code != null) {
      $result.code = code;
    }
    if (srcDmrid != null) {
      $result.srcDmrid = srcDmrid;
    }
    if (dstDmrid != null) {
      $result.dstDmrid = dstDmrid;
    }
    if (data != null) {
      $result.data = data;
    }
    return $result;
  }
  dev_data_info._() : super();
  factory dev_data_info.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory dev_data_info.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'dev_data_info', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'code', $pb.PbFieldType.O3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'srcDmrid', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'dstDmrid', $pb.PbFieldType.OU3)
    ..a<$core.List<$core.int>>(4, _omitFieldNames ? '' : 'data', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  dev_data_info clone() => dev_data_info()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  dev_data_info copyWith(void Function(dev_data_info) updates) => super.copyWith((message) => updates(message as dev_data_info)) as dev_data_info;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static dev_data_info create() => dev_data_info._();
  dev_data_info createEmptyInstance() => create();
  static $pb.PbList<dev_data_info> createRepeated() => $pb.PbList<dev_data_info>();
  @$core.pragma('dart2js:noInline')
  static dev_data_info getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<dev_data_info>(create);
  static dev_data_info? _defaultInstance;

  /// 广播代码
  /// 0:主动上线
  /// 1:主动下线
  /// 3:超时下线
  /// 14:断线下线
  /// 8：讲话
  /// 10: gps data data=bfdx_proto.Gps84
  @$pb.TagNumber(1)
  $core.int get code => $_getIZ(0);
  @$pb.TagNumber(1)
  set code($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearCode() => clearField(1);

  /// 源id
  @$pb.TagNumber(2)
  $core.int get srcDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set srcDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSrcDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearSrcDmrid() => clearField(2);

  /// 目标id
  @$pb.TagNumber(3)
  $core.int get dstDmrid => $_getIZ(2);
  @$pb.TagNumber(3)
  set dstDmrid($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDstDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearDstDmrid() => clearField(3);

  /// 数据
  @$pb.TagNumber(4)
  $core.List<$core.int> get data => $_getN(3);
  @$pb.TagNumber(4)
  set data($core.List<$core.int> v) { $_setBytes(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasData() => $_has(3);
  @$pb.TagNumber(4)
  void clearData() => clearField(4);
}

class addr_book extends $pb.GeneratedMessage {
  factory addr_book({
    $core.int? type,
    $core.int? code,
    $core.List<$core.int>? body,
    $core.String? paraStr,
  }) {
    final $result = create();
    if (type != null) {
      $result.type = type;
    }
    if (code != null) {
      $result.code = code;
    }
    if (body != null) {
      $result.body = body;
    }
    if (paraStr != null) {
      $result.paraStr = paraStr;
    }
    return $result;
  }
  addr_book._() : super();
  factory addr_book.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory addr_book.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'addr_book', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'type', $pb.PbFieldType.O3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'code', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'body', $pb.PbFieldType.OY)
    ..aOS(4, _omitFieldNames ? '' : 'paraStr')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  addr_book clone() => addr_book()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  addr_book copyWith(void Function(addr_book) updates) => super.copyWith((message) => updates(message as addr_book)) as addr_book;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static addr_book create() => addr_book._();
  addr_book createEmptyInstance() => create();
  static $pb.PbList<addr_book> createRepeated() => $pb.PbList<addr_book>();
  @$core.pragma('dart2js:noInline')
  static addr_book getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<addr_book>(create);
  static addr_book? _defaultInstance;

  /// 1.组呼  2.单呼
  @$pb.TagNumber(1)
  $core.int get type => $_getIZ(0);
  @$pb.TagNumber(1)
  set type($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasType() => $_has(0);
  @$pb.TagNumber(1)
  void clearType() => clearField(1);

  /// 0.ok  -1.query err  -2.marshal err
  @$pb.TagNumber(2)
  $core.int get code => $_getIZ(1);
  @$pb.TagNumber(2)
  set code($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearCode() => clearField(2);

  /// code=1,body=db_org
  /// code=2,body=db_device
  @$pb.TagNumber(3)
  $core.List<$core.int> get body => $_getN(2);
  @$pb.TagNumber(3)
  set body($core.List<$core.int> v) { $_setBytes(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasBody() => $_has(2);
  @$pb.TagNumber(3)
  void clearBody() => clearField(3);

  /// para_str=dmridHex
  @$pb.TagNumber(4)
  $core.String get paraStr => $_getSZ(3);
  @$pb.TagNumber(4)
  set paraStr($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasParaStr() => $_has(3);
  @$pb.TagNumber(4)
  void clearParaStr() => clearField(4);
}

/// 返回查询通讯录结果
/// rpc.cmd = 315
/// rpc.res  0.ok  -1.db err  -2.marshal err   -3.需要查询的dmrid为空
class addr_book_list extends $pb.GeneratedMessage {
  factory addr_book_list({
    $core.Iterable<addr_book>? addrBookList,
  }) {
    final $result = create();
    if (addrBookList != null) {
      $result.addrBookList.addAll(addrBookList);
    }
    return $result;
  }
  addr_book_list._() : super();
  factory addr_book_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory addr_book_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'addr_book_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..pc<addr_book>(1, _omitFieldNames ? '' : 'addrBookList', $pb.PbFieldType.PM, subBuilder: addr_book.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  addr_book_list clone() => addr_book_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  addr_book_list copyWith(void Function(addr_book_list) updates) => super.copyWith((message) => updates(message as addr_book_list)) as addr_book_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static addr_book_list create() => addr_book_list._();
  addr_book_list createEmptyInstance() => create();
  static $pb.PbList<addr_book_list> createRepeated() => $pb.PbList<addr_book_list>();
  @$core.pragma('dart2js:noInline')
  static addr_book_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<addr_book_list>(create);
  static addr_book_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<addr_book> get addrBookList => $_getList(0);
}

/// 虚拟集群手台注册请求
/// rpc_cmd.cmd = 108
/// rpc_cmd.res = 0
/// rpc_cmd.body=bc40_req
class bc40_req extends $pb.GeneratedMessage {
  factory bc40_req({
    $core.int? devDmrid,
    $core.int? groupDmrid,
    $core.int? riss,
    $core.List<$core.int>? status,
    $core.int? powerEvent,
    $core.int? lastPowerEvent,
    $core.int? roaming,
    $core.int? bsIndex,
  }) {
    final $result = create();
    if (devDmrid != null) {
      $result.devDmrid = devDmrid;
    }
    if (groupDmrid != null) {
      $result.groupDmrid = groupDmrid;
    }
    if (riss != null) {
      $result.riss = riss;
    }
    if (status != null) {
      $result.status = status;
    }
    if (powerEvent != null) {
      $result.powerEvent = powerEvent;
    }
    if (lastPowerEvent != null) {
      $result.lastPowerEvent = lastPowerEvent;
    }
    if (roaming != null) {
      $result.roaming = roaming;
    }
    if (bsIndex != null) {
      $result.bsIndex = bsIndex;
    }
    return $result;
  }
  bc40_req._() : super();
  factory bc40_req.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc40_req.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc40_req', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'devDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'groupDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'riss', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(4, _omitFieldNames ? '' : 'status', $pb.PbFieldType.OY)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'powerEvent', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'lastPowerEvent', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'roaming', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'bsIndex', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc40_req clone() => bc40_req()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc40_req copyWith(void Function(bc40_req) updates) => super.copyWith((message) => updates(message as bc40_req)) as bc40_req;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc40_req create() => bc40_req._();
  bc40_req createEmptyInstance() => create();
  static $pb.PbList<bc40_req> createRepeated() => $pb.PbList<bc40_req>();
  @$core.pragma('dart2js:noInline')
  static bc40_req getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc40_req>(create);
  static bc40_req? _defaultInstance;

  /// *手台ID*
  @$pb.TagNumber(1)
  $core.int get devDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set devDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDevDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearDevDmrid() => clearField(1);

  /// *手台归属组ID*
  @$pb.TagNumber(2)
  $core.int get groupDmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set groupDmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasGroupDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearGroupDmrid() => clearField(2);

  /// *无线终端的场强值,0~4*
  @$pb.TagNumber(3)
  $core.int get riss => $_getIZ(2);
  @$pb.TagNumber(3)
  set riss($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRiss() => $_has(2);
  @$pb.TagNumber(3)
  void clearRiss() => clearField(3);

  /// *手台状态信息 6字节*
  @$pb.TagNumber(4)
  $core.List<$core.int> get status => $_getN(3);
  @$pb.TagNumber(4)
  set status($core.List<$core.int> v) { $_setBytes(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasStatus() => $_has(3);
  @$pb.TagNumber(4)
  void clearStatus() => clearField(4);

  /// *本次开关机事件,0:关机,1:正常开机,2:电池开机,3:低压复位开机 8：信道切换注册*
  @$pb.TagNumber(5)
  $core.int get powerEvent => $_getIZ(4);
  @$pb.TagNumber(5)
  set powerEvent($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPowerEvent() => $_has(4);
  @$pb.TagNumber(5)
  void clearPowerEvent() => clearField(5);

  /// *上次开关机事件,1:正常开关机,2:异常开关机,3:低压关机/复位开机*
  @$pb.TagNumber(6)
  $core.int get lastPowerEvent => $_getIZ(5);
  @$pb.TagNumber(6)
  set lastPowerEvent($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLastPowerEvent() => $_has(5);
  @$pb.TagNumber(6)
  void clearLastPowerEvent() => clearField(6);

  /// *0:主信道即选定发起注册,1:漫游信道发起注册*
  @$pb.TagNumber(7)
  $core.int get roaming => $_getIZ(6);
  @$pb.TagNumber(7)
  set roaming($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasRoaming() => $_has(6);
  @$pb.TagNumber(7)
  void clearRoaming() => clearField(7);

  /// *虚拟集群下主中继在基站内的序列号*
  @$pb.TagNumber(8)
  $core.int get bsIndex => $_getIZ(7);
  @$pb.TagNumber(8)
  set bsIndex($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasBsIndex() => $_has(7);
  @$pb.TagNumber(8)
  void clearBsIndex() => clearField(8);
}

/// 虚拟集群手台注册应答
/// rpc_cmd.cmd = 108
/// rpc_cmd.res = 1
/// rpc_cmd.body=bc40_resp
class bc40_resp extends $pb.GeneratedMessage {
  factory bc40_resp({
    $core.int? devDmrid,
    $core.int? resCode,
    $fixnum.Int64? sysTime,
  }) {
    final $result = create();
    if (devDmrid != null) {
      $result.devDmrid = devDmrid;
    }
    if (resCode != null) {
      $result.resCode = resCode;
    }
    if (sysTime != null) {
      $result.sysTime = sysTime;
    }
    return $result;
  }
  bc40_resp._() : super();
  factory bc40_resp.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc40_resp.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc40_resp', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'devDmrid', $pb.PbFieldType.OF3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'resCode', $pb.PbFieldType.O3)
    ..aInt64(3, _omitFieldNames ? '' : 'sysTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc40_resp clone() => bc40_resp()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc40_resp copyWith(void Function(bc40_resp) updates) => super.copyWith((message) => updates(message as bc40_resp)) as bc40_resp;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc40_resp create() => bc40_resp._();
  bc40_resp createEmptyInstance() => create();
  static $pb.PbList<bc40_resp> createRepeated() => $pb.PbList<bc40_resp>();
  @$core.pragma('dart2js:noInline')
  static bc40_resp getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc40_resp>(create);
  static bc40_resp? _defaultInstance;

  /// *手台ID*
  @$pb.TagNumber(1)
  $core.int get devDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set devDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDevDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearDevDmrid() => clearField(1);

  /// *应答结果码 0: 注册成功 1：系统无此手台信息 2:归属组错误 3:终端类型配置错误 4:未知错误 10:关机成功*
  @$pb.TagNumber(2)
  $core.int get resCode => $_getIZ(1);
  @$pb.TagNumber(2)
  set resCode($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasResCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearResCode() => clearField(2);

  /// 当前系统时间，unix时间，单位为秒
  ///  the number of seconds elapsed since January 1, 1970 UTC
  @$pb.TagNumber(3)
  $fixnum.Int64 get sysTime => $_getI64(2);
  @$pb.TagNumber(3)
  set sysTime($fixnum.Int64 v) { $_setInt64(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSysTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearSysTime() => clearField(3);
}

/// msg for mesh gateway
/// rpc_cmd.cmd = 500=Query8100DmridInfo
/// rpc_cmd.para_int = dmrid
/// rcp_cmd.cmd = 501=QueryAll8100DmridInfo
/// rpc_cmd.para_int = 0: all group and radio info 1:query group info,2:query radio info
class Bf8100DmridInfo extends $pb.GeneratedMessage {
  factory Bf8100DmridInfo({
    $core.int? dmrID,
    $core.String? name,
    $core.String? orgUUID,
    $core.String? myUUID,
  }) {
    final $result = create();
    if (dmrID != null) {
      $result.dmrID = dmrID;
    }
    if (name != null) {
      $result.name = name;
    }
    if (orgUUID != null) {
      $result.orgUUID = orgUUID;
    }
    if (myUUID != null) {
      $result.myUUID = myUUID;
    }
    return $result;
  }
  Bf8100DmridInfo._() : super();
  factory Bf8100DmridInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Bf8100DmridInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Bf8100DmridInfo', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'DmrID', $pb.PbFieldType.OF3, protoName: 'DmrID')
    ..aOS(2, _omitFieldNames ? '' : 'Name', protoName: 'Name')
    ..aOS(4, _omitFieldNames ? '' : 'OrgUUID', protoName: 'OrgUUID')
    ..aOS(5, _omitFieldNames ? '' : 'MyUUID', protoName: 'MyUUID')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  Bf8100DmridInfo clone() => Bf8100DmridInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  Bf8100DmridInfo copyWith(void Function(Bf8100DmridInfo) updates) => super.copyWith((message) => updates(message as Bf8100DmridInfo)) as Bf8100DmridInfo;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Bf8100DmridInfo create() => Bf8100DmridInfo._();
  Bf8100DmridInfo createEmptyInstance() => create();
  static $pb.PbList<Bf8100DmridInfo> createRepeated() => $pb.PbList<Bf8100DmridInfo>();
  @$core.pragma('dart2js:noInline')
  static Bf8100DmridInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Bf8100DmridInfo>(create);
  static Bf8100DmridInfo? _defaultInstance;

  /// dmrid
  @$pb.TagNumber(1)
  $core.int get dmrID => $_getIZ(0);
  @$pb.TagNumber(1)
  set dmrID($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDmrID() => $_has(0);
  @$pb.TagNumber(1)
  void clearDmrID() => clearField(1);

  /// dmrid name
  @$pb.TagNumber(2)
  $core.String get name => $_getSZ(1);
  @$pb.TagNumber(2)
  set name($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasName() => $_has(1);
  @$pb.TagNumber(2)
  void clearName() => clearField(2);

  /// parent org uuid, 所属单位的uuid
  @$pb.TagNumber(4)
  $core.String get orgUUID => $_getSZ(2);
  @$pb.TagNumber(4)
  set orgUUID($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasOrgUUID() => $_has(2);
  @$pb.TagNumber(4)
  void clearOrgUUID() => clearField(4);

  /// self uuid
  @$pb.TagNumber(5)
  $core.String get myUUID => $_getSZ(3);
  @$pb.TagNumber(5)
  set myUUID($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasMyUUID() => $_has(3);
  @$pb.TagNumber(5)
  void clearMyUUID() => clearField(5);
}

/// * 定位数据 *
class mesh_gps_info_t extends $pb.GeneratedMessage {
  factory mesh_gps_info_t({
    $core.int? sourceId,
    $core.int? targetId,
    $core.int? hour,
    $core.int? minute,
    $core.int? second,
    $core.int? day,
    $core.int? month,
    $core.int? year,
    $core.int? available,
    $core.int? latitude,
    $core.int? northOrSouth,
    $core.int? longitude,
    $core.int? eastOrWest,
    $core.int? speed,
    $core.int? direction,
    $core.int? altitude,
    MESH_GPS_DATA_TYPE? gpsDataType,
  }) {
    final $result = create();
    if (sourceId != null) {
      $result.sourceId = sourceId;
    }
    if (targetId != null) {
      $result.targetId = targetId;
    }
    if (hour != null) {
      $result.hour = hour;
    }
    if (minute != null) {
      $result.minute = minute;
    }
    if (second != null) {
      $result.second = second;
    }
    if (day != null) {
      $result.day = day;
    }
    if (month != null) {
      $result.month = month;
    }
    if (year != null) {
      $result.year = year;
    }
    if (available != null) {
      $result.available = available;
    }
    if (latitude != null) {
      $result.latitude = latitude;
    }
    if (northOrSouth != null) {
      $result.northOrSouth = northOrSouth;
    }
    if (longitude != null) {
      $result.longitude = longitude;
    }
    if (eastOrWest != null) {
      $result.eastOrWest = eastOrWest;
    }
    if (speed != null) {
      $result.speed = speed;
    }
    if (direction != null) {
      $result.direction = direction;
    }
    if (altitude != null) {
      $result.altitude = altitude;
    }
    if (gpsDataType != null) {
      $result.gpsDataType = gpsDataType;
    }
    return $result;
  }
  mesh_gps_info_t._() : super();
  factory mesh_gps_info_t.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory mesh_gps_info_t.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'mesh_gps_info_t', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'sourceId', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'targetId', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'hour', $pb.PbFieldType.OU3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'minute', $pb.PbFieldType.OU3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'second', $pb.PbFieldType.OU3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'day', $pb.PbFieldType.OU3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'month', $pb.PbFieldType.OU3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'year', $pb.PbFieldType.OU3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'available', $pb.PbFieldType.OU3)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'latitude', $pb.PbFieldType.OU3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'northOrSouth', $pb.PbFieldType.OU3, protoName: 'northOrSouth')
    ..a<$core.int>(12, _omitFieldNames ? '' : 'longitude', $pb.PbFieldType.OU3)
    ..a<$core.int>(13, _omitFieldNames ? '' : 'eastOrWest', $pb.PbFieldType.OU3, protoName: 'eastOrWest')
    ..a<$core.int>(14, _omitFieldNames ? '' : 'speed', $pb.PbFieldType.OU3)
    ..a<$core.int>(15, _omitFieldNames ? '' : 'direction', $pb.PbFieldType.OU3)
    ..a<$core.int>(16, _omitFieldNames ? '' : 'altitude', $pb.PbFieldType.O3)
    ..e<MESH_GPS_DATA_TYPE>(17, _omitFieldNames ? '' : 'gpsDataType', $pb.PbFieldType.OE, defaultOrMaker: MESH_GPS_DATA_TYPE.ST_NONE, valueOf: MESH_GPS_DATA_TYPE.valueOf, enumValues: MESH_GPS_DATA_TYPE.values)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  mesh_gps_info_t clone() => mesh_gps_info_t()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  mesh_gps_info_t copyWith(void Function(mesh_gps_info_t) updates) => super.copyWith((message) => updates(message as mesh_gps_info_t)) as mesh_gps_info_t;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static mesh_gps_info_t create() => mesh_gps_info_t._();
  mesh_gps_info_t createEmptyInstance() => create();
  static $pb.PbList<mesh_gps_info_t> createRepeated() => $pb.PbList<mesh_gps_info_t>();
  @$core.pragma('dart2js:noInline')
  static mesh_gps_info_t getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<mesh_gps_info_t>(create);
  static mesh_gps_info_t? _defaultInstance;

  /// *数据源ID*
  @$pb.TagNumber(1)
  $core.int get sourceId => $_getIZ(0);
  @$pb.TagNumber(1)
  set sourceId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSourceId() => $_has(0);
  @$pb.TagNumber(1)
  void clearSourceId() => clearField(1);

  /// *目标ID*
  @$pb.TagNumber(2)
  $core.int get targetId => $_getIZ(1);
  @$pb.TagNumber(2)
  set targetId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTargetId() => $_has(1);
  @$pb.TagNumber(2)
  void clearTargetId() => clearField(2);

  /// *GPS数据*
  @$pb.TagNumber(3)
  $core.int get hour => $_getIZ(2);
  @$pb.TagNumber(3)
  set hour($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasHour() => $_has(2);
  @$pb.TagNumber(3)
  void clearHour() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get minute => $_getIZ(3);
  @$pb.TagNumber(4)
  set minute($core.int v) { $_setUnsignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasMinute() => $_has(3);
  @$pb.TagNumber(4)
  void clearMinute() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get second => $_getIZ(4);
  @$pb.TagNumber(5)
  set second($core.int v) { $_setUnsignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSecond() => $_has(4);
  @$pb.TagNumber(5)
  void clearSecond() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get day => $_getIZ(5);
  @$pb.TagNumber(6)
  set day($core.int v) { $_setUnsignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDay() => $_has(5);
  @$pb.TagNumber(6)
  void clearDay() => clearField(6);

  @$pb.TagNumber(7)
  $core.int get month => $_getIZ(6);
  @$pb.TagNumber(7)
  set month($core.int v) { $_setUnsignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasMonth() => $_has(6);
  @$pb.TagNumber(7)
  void clearMonth() => clearField(7);

  @$pb.TagNumber(8)
  $core.int get year => $_getIZ(7);
  @$pb.TagNumber(8)
  set year($core.int v) { $_setUnsignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasYear() => $_has(7);
  @$pb.TagNumber(8)
  void clearYear() => clearField(8);

  @$pb.TagNumber(9)
  $core.int get available => $_getIZ(8);
  @$pb.TagNumber(9)
  set available($core.int v) { $_setUnsignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasAvailable() => $_has(8);
  @$pb.TagNumber(9)
  void clearAvailable() => clearField(9);

  @$pb.TagNumber(10)
  $core.int get latitude => $_getIZ(9);
  @$pb.TagNumber(10)
  set latitude($core.int v) { $_setUnsignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasLatitude() => $_has(9);
  @$pb.TagNumber(10)
  void clearLatitude() => clearField(10);

  @$pb.TagNumber(11)
  $core.int get northOrSouth => $_getIZ(10);
  @$pb.TagNumber(11)
  set northOrSouth($core.int v) { $_setUnsignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasNorthOrSouth() => $_has(10);
  @$pb.TagNumber(11)
  void clearNorthOrSouth() => clearField(11);

  @$pb.TagNumber(12)
  $core.int get longitude => $_getIZ(11);
  @$pb.TagNumber(12)
  set longitude($core.int v) { $_setUnsignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasLongitude() => $_has(11);
  @$pb.TagNumber(12)
  void clearLongitude() => clearField(12);

  @$pb.TagNumber(13)
  $core.int get eastOrWest => $_getIZ(12);
  @$pb.TagNumber(13)
  set eastOrWest($core.int v) { $_setUnsignedInt32(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasEastOrWest() => $_has(12);
  @$pb.TagNumber(13)
  void clearEastOrWest() => clearField(13);

  @$pb.TagNumber(14)
  $core.int get speed => $_getIZ(13);
  @$pb.TagNumber(14)
  set speed($core.int v) { $_setUnsignedInt32(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasSpeed() => $_has(13);
  @$pb.TagNumber(14)
  void clearSpeed() => clearField(14);

  @$pb.TagNumber(15)
  $core.int get direction => $_getIZ(14);
  @$pb.TagNumber(15)
  set direction($core.int v) { $_setUnsignedInt32(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasDirection() => $_has(14);
  @$pb.TagNumber(15)
  void clearDirection() => clearField(15);

  @$pb.TagNumber(16)
  $core.int get altitude => $_getIZ(15);
  @$pb.TagNumber(16)
  set altitude($core.int v) { $_setSignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasAltitude() => $_has(15);
  @$pb.TagNumber(16)
  void clearAltitude() => clearField(16);

  /// * (预留)GPS数据类型 *
  @$pb.TagNumber(17)
  MESH_GPS_DATA_TYPE get gpsDataType => $_getN(16);
  @$pb.TagNumber(17)
  set gpsDataType(MESH_GPS_DATA_TYPE v) { setField(17, v); }
  @$pb.TagNumber(17)
  $core.bool hasGpsDataType() => $_has(16);
  @$pb.TagNumber(17)
  void clearGpsDataType() => clearField(17);
}

/// mesh send gps info to server
/// rpc_cmd.cmd = 502=MeshSendGpsInfo
/// rpc_cmd.body = MeshGpsInfo
class MeshGpsInfo extends $pb.GeneratedMessage {
  factory MeshGpsInfo({
    $core.int? dmrID,
    mesh_gps_info_t? gpsInfo,
  }) {
    final $result = create();
    if (dmrID != null) {
      $result.dmrID = dmrID;
    }
    if (gpsInfo != null) {
      $result.gpsInfo = gpsInfo;
    }
    return $result;
  }
  MeshGpsInfo._() : super();
  factory MeshGpsInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory MeshGpsInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'MeshGpsInfo', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'DmrID', $pb.PbFieldType.OF3, protoName: 'DmrID')
    ..aOM<mesh_gps_info_t>(2, _omitFieldNames ? '' : 'GpsInfo', protoName: 'GpsInfo', subBuilder: mesh_gps_info_t.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  MeshGpsInfo clone() => MeshGpsInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  MeshGpsInfo copyWith(void Function(MeshGpsInfo) updates) => super.copyWith((message) => updates(message as MeshGpsInfo)) as MeshGpsInfo;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static MeshGpsInfo create() => MeshGpsInfo._();
  MeshGpsInfo createEmptyInstance() => create();
  static $pb.PbList<MeshGpsInfo> createRepeated() => $pb.PbList<MeshGpsInfo>();
  @$core.pragma('dart2js:noInline')
  static MeshGpsInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<MeshGpsInfo>(create);
  static MeshGpsInfo? _defaultInstance;

  /// dev dmrid in 8100
  @$pb.TagNumber(1)
  $core.int get dmrID => $_getIZ(0);
  @$pb.TagNumber(1)
  set dmrID($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDmrID() => $_has(0);
  @$pb.TagNumber(1)
  void clearDmrID() => clearField(1);

  /// gps info
  @$pb.TagNumber(2)
  mesh_gps_info_t get gpsInfo => $_getN(1);
  @$pb.TagNumber(2)
  set gpsInfo(mesh_gps_info_t v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGpsInfo() => $_has(1);
  @$pb.TagNumber(2)
  void clearGpsInfo() => clearField(2);
  @$pb.TagNumber(2)
  mesh_gps_info_t ensureGpsInfo() => $_ensure(1);
}

class PocCommand extends $pb.GeneratedMessage {
  factory PocCommand({
    $core.int? cmd,
    $fixnum.Int64? seqNo,
    $core.List<$core.int>? body,
    $core.String? paraStr,
    $core.List<$core.int>? paraBin,
    $fixnum.Int64? paraInt,
  }) {
    final $result = create();
    if (cmd != null) {
      $result.cmd = cmd;
    }
    if (seqNo != null) {
      $result.seqNo = seqNo;
    }
    if (body != null) {
      $result.body = body;
    }
    if (paraStr != null) {
      $result.paraStr = paraStr;
    }
    if (paraBin != null) {
      $result.paraBin = paraBin;
    }
    if (paraInt != null) {
      $result.paraInt = paraInt;
    }
    return $result;
  }
  PocCommand._() : super();
  factory PocCommand.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PocCommand.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PocCommand', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'cmd', $pb.PbFieldType.O3)
    ..aInt64(2, _omitFieldNames ? '' : 'seqNo')
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'body', $pb.PbFieldType.OY)
    ..aOS(4, _omitFieldNames ? '' : 'paraStr')
    ..a<$core.List<$core.int>>(5, _omitFieldNames ? '' : 'paraBin', $pb.PbFieldType.OY)
    ..aInt64(6, _omitFieldNames ? '' : 'paraInt')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PocCommand clone() => PocCommand()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PocCommand copyWith(void Function(PocCommand) updates) => super.copyWith((message) => updates(message as PocCommand)) as PocCommand;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PocCommand create() => PocCommand._();
  PocCommand createEmptyInstance() => create();
  static $pb.PbList<PocCommand> createRepeated() => $pb.PbList<PocCommand>();
  @$core.pragma('dart2js:noInline')
  static PocCommand getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PocCommand>(create);
  static PocCommand? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get cmd => $_getIZ(0);
  @$pb.TagNumber(1)
  set cmd($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCmd() => $_has(0);
  @$pb.TagNumber(1)
  void clearCmd() => clearField(1);

  /// seq_no
  @$pb.TagNumber(2)
  $fixnum.Int64 get seqNo => $_getI64(1);
  @$pb.TagNumber(2)
  set seqNo($fixnum.Int64 v) { $_setInt64(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSeqNo() => $_has(1);
  @$pb.TagNumber(2)
  void clearSeqNo() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.int> get body => $_getN(2);
  @$pb.TagNumber(3)
  set body($core.List<$core.int> v) { $_setBytes(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasBody() => $_has(2);
  @$pb.TagNumber(3)
  void clearBody() => clearField(3);

  /// para_str
  @$pb.TagNumber(4)
  $core.String get paraStr => $_getSZ(3);
  @$pb.TagNumber(4)
  set paraStr($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasParaStr() => $_has(3);
  @$pb.TagNumber(4)
  void clearParaStr() => clearField(4);

  /// para_bin
  @$pb.TagNumber(5)
  $core.List<$core.int> get paraBin => $_getN(4);
  @$pb.TagNumber(5)
  set paraBin($core.List<$core.int> v) { $_setBytes(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasParaBin() => $_has(4);
  @$pb.TagNumber(5)
  void clearParaBin() => clearField(5);

  /// para_int
  @$pb.TagNumber(6)
  $fixnum.Int64 get paraInt => $_getI64(5);
  @$pb.TagNumber(6)
  set paraInt($fixnum.Int64 v) { $_setInt64(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasParaInt() => $_has(5);
  @$pb.TagNumber(6)
  void clearParaInt() => clearField(6);
}

class PocDefaultGroup extends $pb.GeneratedMessage {
  factory PocDefaultGroup({
    $core.int? defaultSendGroupDmrid,
    $core.Iterable<$core.int>? defaultListenGroupDmrids,
  }) {
    final $result = create();
    if (defaultSendGroupDmrid != null) {
      $result.defaultSendGroupDmrid = defaultSendGroupDmrid;
    }
    if (defaultListenGroupDmrids != null) {
      $result.defaultListenGroupDmrids.addAll(defaultListenGroupDmrids);
    }
    return $result;
  }
  PocDefaultGroup._() : super();
  factory PocDefaultGroup.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PocDefaultGroup.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PocDefaultGroup', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'defaultSendGroupDmrid', $pb.PbFieldType.OF3)
    ..p<$core.int>(2, _omitFieldNames ? '' : 'defaultListenGroupDmrids', $pb.PbFieldType.KF3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PocDefaultGroup clone() => PocDefaultGroup()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PocDefaultGroup copyWith(void Function(PocDefaultGroup) updates) => super.copyWith((message) => updates(message as PocDefaultGroup)) as PocDefaultGroup;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PocDefaultGroup create() => PocDefaultGroup._();
  PocDefaultGroup createEmptyInstance() => create();
  static $pb.PbList<PocDefaultGroup> createRepeated() => $pb.PbList<PocDefaultGroup>();
  @$core.pragma('dart2js:noInline')
  static PocDefaultGroup getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PocDefaultGroup>(create);
  static PocDefaultGroup? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get defaultSendGroupDmrid => $_getIZ(0);
  @$pb.TagNumber(1)
  set defaultSendGroupDmrid($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDefaultSendGroupDmrid() => $_has(0);
  @$pb.TagNumber(1)
  void clearDefaultSendGroupDmrid() => clearField(1);

  /// 最多256个，如果多于256个，则应该分包发送多次
  @$pb.TagNumber(2)
  $core.List<$core.int> get defaultListenGroupDmrids => $_getList(1);
}

class PocSubscribleUpdateOption extends $pb.GeneratedMessage {
  factory PocSubscribleUpdateOption({
    $core.int? frameNo,
    $core.int? frameType,
  }) {
    final $result = create();
    if (frameNo != null) {
      $result.frameNo = frameNo;
    }
    if (frameType != null) {
      $result.frameType = frameType;
    }
    return $result;
  }
  PocSubscribleUpdateOption._() : super();
  factory PocSubscribleUpdateOption.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PocSubscribleUpdateOption.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PocSubscribleUpdateOption', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'frameNo', $pb.PbFieldType.O3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'frameType', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PocSubscribleUpdateOption clone() => PocSubscribleUpdateOption()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PocSubscribleUpdateOption copyWith(void Function(PocSubscribleUpdateOption) updates) => super.copyWith((message) => updates(message as PocSubscribleUpdateOption)) as PocSubscribleUpdateOption;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PocSubscribleUpdateOption create() => PocSubscribleUpdateOption._();
  PocSubscribleUpdateOption createEmptyInstance() => create();
  static $pb.PbList<PocSubscribleUpdateOption> createRepeated() => $pb.PbList<PocSubscribleUpdateOption>();
  @$core.pragma('dart2js:noInline')
  static PocSubscribleUpdateOption getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PocSubscribleUpdateOption>(create);
  static PocSubscribleUpdateOption? _defaultInstance;

  /// 序号
  @$pb.TagNumber(1)
  $core.int get frameNo => $_getIZ(0);
  @$pb.TagNumber(1)
  set frameNo($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasFrameNo() => $_has(0);
  @$pb.TagNumber(1)
  void clearFrameNo() => clearField(1);

  /// 帧类型
  /// 0: 一次性更新
  /// 1: 开始批量更新
  /// 2: 处于批量更新中
  /// 3: 批量更新结束
  @$pb.TagNumber(2)
  $core.int get frameType => $_getIZ(1);
  @$pb.TagNumber(2)
  set frameType($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasFrameType() => $_has(1);
  @$pb.TagNumber(2)
  void clearFrameType() => clearField(2);
}

class PocConfig extends $pb.GeneratedMessage {
  factory PocConfig({
    $core.int? canEditSubscriptionLocal,
    $core.int? rgps,
  }) {
    final $result = create();
    if (canEditSubscriptionLocal != null) {
      $result.canEditSubscriptionLocal = canEditSubscriptionLocal;
    }
    if (rgps != null) {
      $result.rgps = rgps;
    }
    return $result;
  }
  PocConfig._() : super();
  factory PocConfig.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PocConfig.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PocConfig', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfkcp'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'canEditSubscriptionLocal', $pb.PbFieldType.O3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'rgps', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PocConfig clone() => PocConfig()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PocConfig copyWith(void Function(PocConfig) updates) => super.copyWith((message) => updates(message as PocConfig)) as PocConfig;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PocConfig create() => PocConfig._();
  PocConfig createEmptyInstance() => create();
  static $pb.PbList<PocConfig> createRepeated() => $pb.PbList<PocConfig>();
  @$core.pragma('dart2js:noInline')
  static PocConfig getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PocConfig>(create);
  static PocConfig? _defaultInstance;

  /// can edit subscription local
  /// 终端是否可以本地编辑收听组,0:不可以,1:可以
  @$pb.TagNumber(1)
  $core.int get canEditSubscriptionLocal => $_getIZ(0);
  @$pb.TagNumber(1)
  set canEditSubscriptionLocal($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCanEditSubscriptionLocal() => $_has(0);
  @$pb.TagNumber(1)
  void clearCanEditSubscriptionLocal() => clearField(1);

  /// reliable gps transfer
  /// 是否启用可靠的gps传输,0:不启用,1:启用
  @$pb.TagNumber(2)
  $core.int get rgps => $_getIZ(1);
  @$pb.TagNumber(2)
  set rgps($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRgps() => $_has(1);
  @$pb.TagNumber(2)
  void clearRgps() => clearField(2);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
