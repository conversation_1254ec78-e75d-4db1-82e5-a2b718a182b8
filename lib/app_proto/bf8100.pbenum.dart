//
//  Generated code. Do not modify.
//  source: bf8100.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

/// * 卫星定位数据类型 *
class MESH_GPS_DATA_TYPE extends $pb.ProtobufEnum {
  static const MESH_GPS_DATA_TYPE ST_NONE = MESH_GPS_DATA_TYPE._(0, _omitEnumNames ? '' : 'ST_NONE');
  static const MESH_GPS_DATA_TYPE ST_FUNCTION_KEY = MESH_GPS_DATA_TYPE._(1, _omitEnumNames ? '' : 'ST_FUNCTION_KEY');
  static const MESH_GPS_DATA_TYPE ST_POWER_ON = MESH_GPS_DATA_TYPE._(2, _omitEnumNames ? '' : 'ST_POWER_ON');
  static const MESH_GPS_DATA_TYPE ST_POWER_OFF = MESH_GPS_DATA_TYPE._(3, _omitEnumNames ? '' : 'ST_POWER_OFF');
  static const MESH_GPS_DATA_TYPE ST_TIME = MESH_GPS_DATA_TYPE._(4, _omitEnumNames ? '' : 'ST_TIME');
  static const MESH_GPS_DATA_TYPE ST_DISTANCE = MESH_GPS_DATA_TYPE._(5, _omitEnumNames ? '' : 'ST_DISTANCE');
  static const MESH_GPS_DATA_TYPE ST_FUNCTION_MENU = MESH_GPS_DATA_TYPE._(6, _omitEnumNames ? '' : 'ST_FUNCTION_MENU');
  static const MESH_GPS_DATA_TYPE ST_PWDERR = MESH_GPS_DATA_TYPE._(7, _omitEnumNames ? '' : 'ST_PWDERR');
  static const MESH_GPS_DATA_TYPE ST_DEVICE_DISABLE = MESH_GPS_DATA_TYPE._(8, _omitEnumNames ? '' : 'ST_DEVICE_DISABLE');
  static const MESH_GPS_DATA_TYPE ST_REMOTE_MONITOR = MESH_GPS_DATA_TYPE._(9, _omitEnumNames ? '' : 'ST_REMOTE_MONITOR');
  static const MESH_GPS_DATA_TYPE ST_PTT_BEYOND = MESH_GPS_DATA_TYPE._(10, _omitEnumNames ? '' : 'ST_PTT_BEYOND');
  static const MESH_GPS_DATA_TYPE ST_LINK_BEYOND = MESH_GPS_DATA_TYPE._(11, _omitEnumNames ? '' : 'ST_LINK_BEYOND');
  static const MESH_GPS_DATA_TYPE ST_GPS_QUERY = MESH_GPS_DATA_TYPE._(12, _omitEnumNames ? '' : 'ST_GPS_QUERY');
  static const MESH_GPS_DATA_TYPE ST_TX_ALARM = MESH_GPS_DATA_TYPE._(13, _omitEnumNames ? '' : 'ST_TX_ALARM');

  static const $core.List<MESH_GPS_DATA_TYPE> values = <MESH_GPS_DATA_TYPE> [
    ST_NONE,
    ST_FUNCTION_KEY,
    ST_POWER_ON,
    ST_POWER_OFF,
    ST_TIME,
    ST_DISTANCE,
    ST_FUNCTION_MENU,
    ST_PWDERR,
    ST_DEVICE_DISABLE,
    ST_REMOTE_MONITOR,
    ST_PTT_BEYOND,
    ST_LINK_BEYOND,
    ST_GPS_QUERY,
    ST_TX_ALARM,
  ];

  static final $core.Map<$core.int, MESH_GPS_DATA_TYPE> _byValue = $pb.ProtobufEnum.initByValue(values);
  static MESH_GPS_DATA_TYPE? valueOf($core.int value) => _byValue[value];

  const MESH_GPS_DATA_TYPE._($core.int v, $core.String n) : super(v, n);
}


const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
