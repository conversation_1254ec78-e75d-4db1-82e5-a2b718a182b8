//
//  Generated code. Do not modify.
//  source: bf8100.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use mESH_GPS_DATA_TYPEDescriptor instead')
const MESH_GPS_DATA_TYPE$json = {
  '1': 'MESH_GPS_DATA_TYPE',
  '2': [
    {'1': 'ST_NONE', '2': 0},
    {'1': 'ST_FUNCTION_KEY', '2': 1},
    {'1': 'ST_POWER_ON', '2': 2},
    {'1': 'ST_POWER_OFF', '2': 3},
    {'1': 'ST_TIME', '2': 4},
    {'1': 'ST_DISTANCE', '2': 5},
    {'1': 'ST_FUNCTION_MENU', '2': 6},
    {'1': 'ST_PWDERR', '2': 7},
    {'1': 'ST_DEVICE_DISABLE', '2': 8},
    {'1': 'ST_REMOTE_MONITOR', '2': 9},
    {'1': 'ST_PTT_BEYOND', '2': 10},
    {'1': 'ST_LINK_BEYOND', '2': 11},
    {'1': 'ST_GPS_QUERY', '2': 12},
    {'1': 'ST_TX_ALARM', '2': 13},
  ],
};

/// Descriptor for `MESH_GPS_DATA_TYPE`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List mESH_GPS_DATA_TYPEDescriptor = $convert.base64Decode(
    'ChJNRVNIX0dQU19EQVRBX1RZUEUSCwoHU1RfTk9ORRAAEhMKD1NUX0ZVTkNUSU9OX0tFWRABEg'
    '8KC1NUX1BPV0VSX09OEAISEAoMU1RfUE9XRVJfT0ZGEAMSCwoHU1RfVElNRRAEEg8KC1NUX0RJ'
    'U1RBTkNFEAUSFAoQU1RfRlVOQ1RJT05fTUVOVRAGEg0KCVNUX1BXREVSUhAHEhUKEVNUX0RFVk'
    'lDRV9ESVNBQkxFEAgSFQoRU1RfUkVNT1RFX01PTklUT1IQCRIRCg1TVF9QVFRfQkVZT05EEAoS'
    'EgoOU1RfTElOS19CRVlPTkQQCxIQCgxTVF9HUFNfUVVFUlkQDBIPCgtTVF9UWF9BTEFSTRAN');

@$core.Deprecated('Use rpc_cmdDescriptor instead')
const rpc_cmd$json = {
  '1': 'rpc_cmd',
  '2': [
    {'1': 'seq_no', '3': 2, '4': 1, '5': 5, '10': 'seqNo'},
    {'1': 'sid', '3': 3, '4': 1, '5': 3, '10': 'sid'},
    {'1': 'cmd', '3': 5, '4': 1, '5': 5, '10': 'cmd'},
    {'1': 'res', '3': 8, '4': 1, '5': 5, '10': 'res'},
    {'1': 'body', '3': 10, '4': 1, '5': 12, '10': 'body'},
    {'1': 'para_str', '3': 11, '4': 1, '5': 9, '10': 'paraStr'},
    {'1': 'para_bin', '3': 12, '4': 1, '5': 12, '10': 'paraBin'},
    {'1': 'para_int', '3': 13, '4': 1, '5': 3, '10': 'paraInt'},
  ],
};

/// Descriptor for `rpc_cmd`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List rpc_cmdDescriptor = $convert.base64Decode(
    'CgdycGNfY21kEhUKBnNlcV9ubxgCIAEoBVIFc2VxTm8SEAoDc2lkGAMgASgDUgNzaWQSEAoDY2'
    '1kGAUgASgFUgNjbWQSEAoDcmVzGAggASgFUgNyZXMSEgoEYm9keRgKIAEoDFIEYm9keRIZCghw'
    'YXJhX3N0chgLIAEoCVIHcGFyYVN0chIZCghwYXJhX2JpbhgMIAEoDFIHcGFyYUJpbhIZCghwYX'
    'JhX2ludBgNIAEoA1IHcGFyYUludA==');

@$core.Deprecated('Use loginDescriptor instead')
const login$json = {
  '1': 'login',
  '2': [
    {'1': 'device_dmrid', '3': 1, '4': 1, '5': 7, '10': 'deviceDmrid'},
    {'1': 'device_name', '3': 2, '4': 1, '5': 9, '10': 'deviceName'},
    {'1': 'login_type', '3': 3, '4': 1, '5': 5, '10': 'loginType'},
    {'1': 'device_model', '3': 4, '4': 1, '5': 9, '10': 'deviceModel'},
    {'1': 'password', '3': 5, '4': 1, '5': 9, '10': 'password'},
    {'1': 'password_method', '3': 6, '4': 1, '5': 5, '10': 'passwordMethod'},
    {'1': 'time_str', '3': 7, '4': 1, '5': 9, '10': 'timeStr'},
    {'1': 'sys_id', '3': 8, '4': 1, '5': 9, '10': 'sysId'},
    {'1': 'extra_option', '3': 9, '4': 3, '5': 5, '10': 'extraOption'},
    {'1': 'codec', '3': 10, '4': 3, '5': 5, '10': 'codec'},
  ],
};

/// Descriptor for `login`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List loginDescriptor = $convert.base64Decode(
    'CgVsb2dpbhIhCgxkZXZpY2VfZG1yaWQYASABKAdSC2RldmljZURtcmlkEh8KC2RldmljZV9uYW'
    '1lGAIgASgJUgpkZXZpY2VOYW1lEh0KCmxvZ2luX3R5cGUYAyABKAVSCWxvZ2luVHlwZRIhCgxk'
    'ZXZpY2VfbW9kZWwYBCABKAlSC2RldmljZU1vZGVsEhoKCHBhc3N3b3JkGAUgASgJUghwYXNzd2'
    '9yZBInCg9wYXNzd29yZF9tZXRob2QYBiABKAVSDnBhc3N3b3JkTWV0aG9kEhkKCHRpbWVfc3Ry'
    'GAcgASgJUgd0aW1lU3RyEhUKBnN5c19pZBgIIAEoCVIFc3lzSWQSIQoMZXh0cmFfb3B0aW9uGA'
    'kgAygFUgtleHRyYU9wdGlvbhIUCgVjb2RlYxgKIAMoBVIFY29kZWM=');

@$core.Deprecated('Use res_login_para_binDescriptor instead')
const res_login_para_bin$json = {
  '1': 'res_login_para_bin',
  '2': [
    {'1': 'validSnCode', '3': 1, '4': 1, '5': 12, '10': 'validSnCode'},
    {'1': 'imbeSn', '3': 2, '4': 1, '5': 9, '10': 'imbeSn'},
    {'1': 'isHaveFullCallPerm', '3': 3, '4': 1, '5': 5, '10': 'isHaveFullCallPerm'},
  ],
};

/// Descriptor for `res_login_para_bin`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List res_login_para_binDescriptor = $convert.base64Decode(
    'ChJyZXNfbG9naW5fcGFyYV9iaW4SIAoLdmFsaWRTbkNvZGUYASABKAxSC3ZhbGlkU25Db2RlEh'
    'YKBmltYmVTbhgCIAEoCVIGaW1iZVNuEi4KEmlzSGF2ZUZ1bGxDYWxsUGVybRgDIAEoBVISaXNI'
    'YXZlRnVsbENhbGxQZXJt');

@$core.Deprecated('Use res_loginDescriptor instead')
const res_login$json = {
  '1': 'res_login',
  '2': [
    {'1': 'res_code', '3': 1, '4': 1, '5': 5, '10': 'resCode'},
    {'1': 'sid', '3': 3, '4': 1, '5': 3, '10': 'sid'},
    {'1': 'hangup_time', '3': 4, '4': 1, '5': 5, '10': 'hangupTime'},
    {'1': 'http_port', '3': 5, '4': 1, '5': 5, '10': 'httpPort'},
    {'1': 'server_version', '3': 6, '4': 1, '5': 9, '10': 'serverVersion'},
    {'1': 'setting_last_update_time', '3': 7, '4': 1, '5': 9, '10': 'settingLastUpdateTime'},
  ],
};

/// Descriptor for `res_login`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List res_loginDescriptor = $convert.base64Decode(
    'CglyZXNfbG9naW4SGQoIcmVzX2NvZGUYASABKAVSB3Jlc0NvZGUSEAoDc2lkGAMgASgDUgNzaW'
    'QSHwoLaGFuZ3VwX3RpbWUYBCABKAVSCmhhbmd1cFRpbWUSGwoJaHR0cF9wb3J0GAUgASgFUgho'
    'dHRwUG9ydBIlCg5zZXJ2ZXJfdmVyc2lvbhgGIAEoCVINc2VydmVyVmVyc2lvbhI3ChhzZXR0aW'
    '5nX2xhc3RfdXBkYXRlX3RpbWUYByABKAlSFXNldHRpbmdMYXN0VXBkYXRlVGltZQ==');

@$core.Deprecated('Use res_repeater_stateDescriptor instead')
const res_repeater_state$json = {
  '1': 'res_repeater_state',
  '2': [
    {'1': 'device_dmrid', '3': 1, '4': 1, '5': 7, '10': 'deviceDmrid'},
    {'1': 'channel_id', '3': 2, '4': 1, '5': 5, '10': 'channelId'},
    {'1': 'rx_frequency', '3': 3, '4': 1, '5': 7, '10': 'rxFrequency'},
    {'1': 'tx_frequency', '3': 4, '4': 1, '5': 7, '10': 'txFrequency'},
    {'1': 'power_value', '3': 5, '4': 1, '5': 5, '10': 'powerValue'},
    {'1': 'ip_addr', '3': 6, '4': 1, '5': 7, '10': 'ipAddr'},
    {'1': 'vol_value', '3': 7, '4': 1, '5': 5, '10': 'volValue'},
    {'1': 'tmp_value', '3': 8, '4': 1, '5': 5, '10': 'tmpValue'},
    {'1': 'tmp_err', '3': 9, '4': 1, '5': 5, '10': 'tmpErr'},
    {'1': 'ant_err', '3': 10, '4': 1, '5': 5, '10': 'antErr'},
    {'1': 'gps_err', '3': 11, '4': 1, '5': 5, '10': 'gpsErr'},
    {'1': 'vol_err', '3': 12, '4': 1, '5': 5, '10': 'volErr'},
    {'1': 'rx_pll_err', '3': 13, '4': 1, '5': 5, '10': 'rxPllErr'},
    {'1': 'tx_pll_err', '3': 14, '4': 1, '5': 5, '10': 'txPllErr'},
    {'1': 'fan_err', '3': 15, '4': 1, '5': 5, '10': 'fanErr'},
    {'1': 'signal', '3': 16, '4': 1, '5': 5, '10': 'signal'},
    {'1': 'ant_value', '3': 17, '4': 1, '5': 5, '10': 'antValue'},
  ],
};

/// Descriptor for `res_repeater_state`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List res_repeater_stateDescriptor = $convert.base64Decode(
    'ChJyZXNfcmVwZWF0ZXJfc3RhdGUSIQoMZGV2aWNlX2RtcmlkGAEgASgHUgtkZXZpY2VEbXJpZB'
    'IdCgpjaGFubmVsX2lkGAIgASgFUgljaGFubmVsSWQSIQoMcnhfZnJlcXVlbmN5GAMgASgHUgty'
    'eEZyZXF1ZW5jeRIhCgx0eF9mcmVxdWVuY3kYBCABKAdSC3R4RnJlcXVlbmN5Eh8KC3Bvd2VyX3'
    'ZhbHVlGAUgASgFUgpwb3dlclZhbHVlEhcKB2lwX2FkZHIYBiABKAdSBmlwQWRkchIbCgl2b2xf'
    'dmFsdWUYByABKAVSCHZvbFZhbHVlEhsKCXRtcF92YWx1ZRgIIAEoBVIIdG1wVmFsdWUSFwoHdG'
    '1wX2VychgJIAEoBVIGdG1wRXJyEhcKB2FudF9lcnIYCiABKAVSBmFudEVychIXCgdncHNfZXJy'
    'GAsgASgFUgZncHNFcnISFwoHdm9sX2VychgMIAEoBVIGdm9sRXJyEhwKCnJ4X3BsbF9lcnIYDS'
    'ABKAVSCHJ4UGxsRXJyEhwKCnR4X3BsbF9lcnIYDiABKAVSCHR4UGxsRXJyEhcKB2Zhbl9lcnIY'
    'DyABKAVSBmZhbkVychIWCgZzaWduYWwYECABKAVSBnNpZ25hbBIbCglhbnRfdmFsdWUYESABKA'
    'VSCGFudFZhbHVl');

@$core.Deprecated('Use repeater_err_statusDescriptor instead')
const repeater_err_status$json = {
  '1': 'repeater_err_status',
  '2': [
    {'1': 'device_dmrid', '3': 1, '4': 1, '5': 7, '10': 'deviceDmrid'},
    {'1': 'tmp_err', '3': 2, '4': 1, '5': 5, '10': 'tmpErr'},
    {'1': 'ant_err', '3': 3, '4': 1, '5': 5, '10': 'antErr'},
    {'1': 'gps_err', '3': 4, '4': 1, '5': 5, '10': 'gpsErr'},
    {'1': 'vol_err', '3': 5, '4': 1, '5': 5, '10': 'volErr'},
    {'1': 'rx_pll_err', '3': 6, '4': 1, '5': 5, '10': 'rxPllErr'},
    {'1': 'tx_pll_err', '3': 7, '4': 1, '5': 5, '10': 'txPllErr'},
    {'1': 'fan_err', '3': 8, '4': 1, '5': 5, '10': 'fanErr'},
    {'1': 'signal', '3': 9, '4': 1, '5': 5, '10': 'signal'},
  ],
};

/// Descriptor for `repeater_err_status`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List repeater_err_statusDescriptor = $convert.base64Decode(
    'ChNyZXBlYXRlcl9lcnJfc3RhdHVzEiEKDGRldmljZV9kbXJpZBgBIAEoB1ILZGV2aWNlRG1yaW'
    'QSFwoHdG1wX2VychgCIAEoBVIGdG1wRXJyEhcKB2FudF9lcnIYAyABKAVSBmFudEVychIXCgdn'
    'cHNfZXJyGAQgASgFUgZncHNFcnISFwoHdm9sX2VychgFIAEoBVIGdm9sRXJyEhwKCnJ4X3BsbF'
    '9lcnIYBiABKAVSCHJ4UGxsRXJyEhwKCnR4X3BsbF9lcnIYByABKAVSCHR4UGxsRXJyEhcKB2Zh'
    'bl9lcnIYCCABKAVSBmZhbkVychIWCgZzaWduYWwYCSABKAVSBnNpZ25hbA==');

@$core.Deprecated('Use device_sendDescriptor instead')
const device_send$json = {
  '1': 'device_send',
  '2': [
    {'1': 'repeater_dmrid', '3': 1, '4': 1, '5': 7, '10': 'repeaterDmrid'},
    {'1': 'fsk', '3': 3, '4': 1, '5': 12, '10': 'fsk'},
    {'1': 'source_repeater_dmrid', '3': 4, '4': 1, '5': 7, '10': 'sourceRepeaterDmrid'},
  ],
};

/// Descriptor for `device_send`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List device_sendDescriptor = $convert.base64Decode(
    'CgtkZXZpY2Vfc2VuZBIlCg5yZXBlYXRlcl9kbXJpZBgBIAEoB1INcmVwZWF0ZXJEbXJpZBIQCg'
    'Nmc2sYAyABKAxSA2ZzaxIyChVzb3VyY2VfcmVwZWF0ZXJfZG1yaWQYBCABKAdSE3NvdXJjZVJl'
    'cGVhdGVyRG1yaWQ=');

@$core.Deprecated('Use server_sendDescriptor instead')
const server_send$json = {
  '1': 'server_send',
  '2': [
    {'1': 'fsk', '3': 3, '4': 1, '5': 12, '10': 'fsk'},
  ],
};

/// Descriptor for `server_send`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List server_sendDescriptor = $convert.base64Decode(
    'CgtzZXJ2ZXJfc2VuZBIQCgNmc2sYAyABKAxSA2Zzaw==');

@$core.Deprecated('Use bc71Descriptor instead')
const bc71$json = {
  '1': 'bc71',
  '2': [
    {'1': 'repeater_dmrid', '3': 1, '4': 1, '5': 7, '10': 'repeaterDmrid'},
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'field_intensity', '3': 4, '4': 1, '5': 5, '10': 'fieldIntensity'},
    {'1': 'support_digital', '3': 5, '4': 1, '5': 5, '10': 'supportDigital'},
    {'1': 'support_analog', '3': 6, '4': 1, '5': 5, '10': 'supportAnalog'},
    {'1': 'time_slot_no', '3': 7, '4': 1, '5': 5, '10': 'timeSlotNo'},
    {'1': 'priority', '3': 8, '4': 1, '5': 5, '10': 'priority'},
    {'1': 'sound_type', '3': 9, '4': 1, '5': 5, '10': 'soundType'},
    {'1': 'phone_no', '3': 10, '4': 1, '5': 9, '10': 'phoneNo'},
    {'1': 'call_duplex', '3': 14, '4': 1, '5': 5, '10': 'callDuplex'},
    {'1': 'source_repeater_dmrid', '3': 15, '4': 1, '5': 7, '10': 'sourceRepeaterDmrid'},
    {'1': 'prefer_interrupt_target_dmrid', '3': 16, '4': 1, '5': 7, '10': 'preferInterruptTargetDmrid'},
  ],
};

/// Descriptor for `bc71`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc71Descriptor = $convert.base64Decode(
    'CgRiYzcxEiUKDnJlcGVhdGVyX2RtcmlkGAEgASgHUg1yZXBlYXRlckRtcmlkEiEKDHRhcmdldF'
    '9kbXJpZBgCIAEoB1ILdGFyZ2V0RG1yaWQSIQoMc291cmNlX2RtcmlkGAMgASgHUgtzb3VyY2VE'
    'bXJpZBInCg9maWVsZF9pbnRlbnNpdHkYBCABKAVSDmZpZWxkSW50ZW5zaXR5EicKD3N1cHBvcn'
    'RfZGlnaXRhbBgFIAEoBVIOc3VwcG9ydERpZ2l0YWwSJQoOc3VwcG9ydF9hbmFsb2cYBiABKAVS'
    'DXN1cHBvcnRBbmFsb2cSIAoMdGltZV9zbG90X25vGAcgASgFUgp0aW1lU2xvdE5vEhoKCHByaW'
    '9yaXR5GAggASgFUghwcmlvcml0eRIdCgpzb3VuZF90eXBlGAkgASgFUglzb3VuZFR5cGUSGQoI'
    'cGhvbmVfbm8YCiABKAlSB3Bob25lTm8SHwoLY2FsbF9kdXBsZXgYDiABKAVSCmNhbGxEdXBsZX'
    'gSMgoVc291cmNlX3JlcGVhdGVyX2RtcmlkGA8gASgHUhNzb3VyY2VSZXBlYXRlckRtcmlkEkEK'
    'HXByZWZlcl9pbnRlcnJ1cHRfdGFyZ2V0X2RtcmlkGBAgASgHUhpwcmVmZXJJbnRlcnJ1cHRUYX'
    'JnZXREbXJpZA==');

@$core.Deprecated('Use cb71Descriptor instead')
const cb71$json = {
  '1': 'cb71',
  '2': [
    {'1': 'repeater_dmrid', '3': 1, '4': 1, '5': 7, '10': 'repeaterDmrid'},
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'req_no', '3': 4, '4': 1, '5': 5, '10': 'reqNo'},
    {'1': 'support_digital', '3': 5, '4': 1, '5': 5, '10': 'supportDigital'},
    {'1': 'support_analog', '3': 6, '4': 1, '5': 5, '10': 'supportAnalog'},
    {'1': 'time_slot_no', '3': 7, '4': 1, '5': 5, '10': 'timeSlotNo'},
    {'1': 'priority', '3': 8, '4': 1, '5': 5, '10': 'priority'},
    {'1': 'result', '3': 9, '4': 1, '5': 5, '10': 'result'},
    {'1': 'interrupt_dmrid', '3': 10, '4': 1, '5': 7, '10': 'interruptDmrid'},
    {'1': 'sound_type', '3': 11, '4': 1, '5': 5, '10': 'soundType'},
    {'1': 'phone_no', '3': 12, '4': 1, '5': 9, '10': 'phoneNo'},
    {'1': 'call_duplex', '3': 13, '4': 1, '5': 5, '10': 'callDuplex'},
    {'1': 'source_repeater_dmrid', '3': 14, '4': 1, '5': 7, '10': 'sourceRepeaterDmrid'},
  ],
};

/// Descriptor for `cb71`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb71Descriptor = $convert.base64Decode(
    'CgRjYjcxEiUKDnJlcGVhdGVyX2RtcmlkGAEgASgHUg1yZXBlYXRlckRtcmlkEiEKDHRhcmdldF'
    '9kbXJpZBgCIAEoB1ILdGFyZ2V0RG1yaWQSIQoMc291cmNlX2RtcmlkGAMgASgHUgtzb3VyY2VE'
    'bXJpZBIVCgZyZXFfbm8YBCABKAVSBXJlcU5vEicKD3N1cHBvcnRfZGlnaXRhbBgFIAEoBVIOc3'
    'VwcG9ydERpZ2l0YWwSJQoOc3VwcG9ydF9hbmFsb2cYBiABKAVSDXN1cHBvcnRBbmFsb2cSIAoM'
    'dGltZV9zbG90X25vGAcgASgFUgp0aW1lU2xvdE5vEhoKCHByaW9yaXR5GAggASgFUghwcmlvcm'
    'l0eRIWCgZyZXN1bHQYCSABKAVSBnJlc3VsdBInCg9pbnRlcnJ1cHRfZG1yaWQYCiABKAdSDmlu'
    'dGVycnVwdERtcmlkEh0KCnNvdW5kX3R5cGUYCyABKAVSCXNvdW5kVHlwZRIZCghwaG9uZV9ubx'
    'gMIAEoCVIHcGhvbmVObxIfCgtjYWxsX2R1cGxleBgNIAEoBVIKY2FsbER1cGxleBIyChVzb3Vy'
    'Y2VfcmVwZWF0ZXJfZG1yaWQYDiABKAdSE3NvdXJjZVJlcGVhdGVyRG1yaWQ=');

@$core.Deprecated('Use bc73Descriptor instead')
const bc73$json = {
  '1': 'bc73',
  '2': [
    {'1': 'repeater_dmrid', '3': 1, '4': 1, '5': 7, '10': 'repeaterDmrid'},
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'time_slot_no', '3': 4, '4': 1, '5': 5, '10': 'timeSlotNo'},
    {'1': 'source_repeater_dmrid', '3': 5, '4': 1, '5': 7, '10': 'sourceRepeaterDmrid'},
  ],
};

/// Descriptor for `bc73`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc73Descriptor = $convert.base64Decode(
    'CgRiYzczEiUKDnJlcGVhdGVyX2RtcmlkGAEgASgHUg1yZXBlYXRlckRtcmlkEiEKDHRhcmdldF'
    '9kbXJpZBgCIAEoB1ILdGFyZ2V0RG1yaWQSIQoMc291cmNlX2RtcmlkGAMgASgHUgtzb3VyY2VE'
    'bXJpZBIgCgx0aW1lX3Nsb3Rfbm8YBCABKAVSCnRpbWVTbG90Tm8SMgoVc291cmNlX3JlcGVhdG'
    'VyX2RtcmlkGAUgASgHUhNzb3VyY2VSZXBlYXRlckRtcmlk');

@$core.Deprecated('Use cb75Descriptor instead')
const cb75$json = {
  '1': 'cb75',
  '2': [
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'reason', '3': 5, '4': 1, '5': 5, '10': 'reason'},
  ],
};

/// Descriptor for `cb75`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb75Descriptor = $convert.base64Decode(
    'CgRjYjc1EiEKDHRhcmdldF9kbXJpZBgCIAEoB1ILdGFyZ2V0RG1yaWQSIQoMc291cmNlX2Rtcm'
    'lkGAMgASgHUgtzb3VyY2VEbXJpZBIWCgZyZWFzb24YBSABKAVSBnJlYXNvbg==');

@$core.Deprecated('Use bc15Descriptor instead')
const bc15$json = {
  '1': 'bc15',
  '2': [
    {'1': 'repeater_dmrid', '3': 1, '4': 1, '5': 7, '10': 'repeaterDmrid'},
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'field_intensity', '3': 4, '4': 1, '5': 5, '10': 'fieldIntensity'},
    {'1': 'support_digital', '3': 5, '4': 1, '5': 5, '10': 'supportDigital'},
    {'1': 'support_analog', '3': 6, '4': 1, '5': 5, '10': 'supportAnalog'},
    {'1': 'time_slot_no', '3': 7, '4': 1, '5': 5, '10': 'timeSlotNo'},
    {'1': 'call_type', '3': 9, '4': 1, '5': 5, '10': 'callType'},
    {'1': 'priority', '3': 8, '4': 1, '5': 5, '10': 'priority'},
    {'1': 'support_interrupt', '3': 10, '4': 1, '5': 5, '10': 'supportInterrupt'},
    {'1': 'call_status', '3': 11, '4': 1, '5': 5, '10': 'callStatus'},
    {'1': 'sound_type', '3': 12, '4': 1, '5': 5, '10': 'soundType'},
    {'1': 'phone_no', '3': 13, '4': 1, '5': 9, '10': 'phoneNo'},
    {'1': 'call_duplex', '3': 14, '4': 1, '5': 5, '10': 'callDuplex'},
    {'1': 'source_repeater_dmrid', '3': 15, '4': 1, '5': 7, '10': 'sourceRepeaterDmrid'},
    {'1': 'start_time', '3': 16, '4': 1, '5': 6, '10': 'startTime'},
  ],
};

/// Descriptor for `bc15`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc15Descriptor = $convert.base64Decode(
    'CgRiYzE1EiUKDnJlcGVhdGVyX2RtcmlkGAEgASgHUg1yZXBlYXRlckRtcmlkEiEKDHRhcmdldF'
    '9kbXJpZBgCIAEoB1ILdGFyZ2V0RG1yaWQSIQoMc291cmNlX2RtcmlkGAMgASgHUgtzb3VyY2VE'
    'bXJpZBInCg9maWVsZF9pbnRlbnNpdHkYBCABKAVSDmZpZWxkSW50ZW5zaXR5EicKD3N1cHBvcn'
    'RfZGlnaXRhbBgFIAEoBVIOc3VwcG9ydERpZ2l0YWwSJQoOc3VwcG9ydF9hbmFsb2cYBiABKAVS'
    'DXN1cHBvcnRBbmFsb2cSIAoMdGltZV9zbG90X25vGAcgASgFUgp0aW1lU2xvdE5vEhsKCWNhbG'
    'xfdHlwZRgJIAEoBVIIY2FsbFR5cGUSGgoIcHJpb3JpdHkYCCABKAVSCHByaW9yaXR5EisKEXN1'
    'cHBvcnRfaW50ZXJydXB0GAogASgFUhBzdXBwb3J0SW50ZXJydXB0Eh8KC2NhbGxfc3RhdHVzGA'
    'sgASgFUgpjYWxsU3RhdHVzEh0KCnNvdW5kX3R5cGUYDCABKAVSCXNvdW5kVHlwZRIZCghwaG9u'
    'ZV9ubxgNIAEoCVIHcGhvbmVObxIfCgtjYWxsX2R1cGxleBgOIAEoBVIKY2FsbER1cGxleBIyCh'
    'Vzb3VyY2VfcmVwZWF0ZXJfZG1yaWQYDyABKAdSE3NvdXJjZVJlcGVhdGVyRG1yaWQSHQoKc3Rh'
    'cnRfdGltZRgQIAEoBlIJc3RhcnRUaW1l');

@$core.Deprecated('Use bc10Descriptor instead')
const bc10$json = {
  '1': 'bc10',
  '2': [
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'call_type', '3': 5, '4': 1, '5': 5, '10': 'callType'},
    {'1': 'priority', '3': 6, '4': 1, '5': 5, '10': 'priority'},
    {'1': 'support_interrupt', '3': 7, '4': 1, '5': 5, '10': 'supportInterrupt'},
    {'1': 'frame_no', '3': 8, '4': 1, '5': 7, '10': 'frameNo'},
    {'1': 'opus_data_1', '3': 9, '4': 1, '5': 12, '10': 'opusData1'},
    {'1': 'opus_data_2', '3': 10, '4': 1, '5': 12, '10': 'opusData2'},
  ],
};

/// Descriptor for `bc10`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc10Descriptor = $convert.base64Decode(
    'CgRiYzEwEiEKDHRhcmdldF9kbXJpZBgCIAEoB1ILdGFyZ2V0RG1yaWQSIQoMc291cmNlX2Rtcm'
    'lkGAMgASgHUgtzb3VyY2VEbXJpZBIbCgljYWxsX3R5cGUYBSABKAVSCGNhbGxUeXBlEhoKCHBy'
    'aW9yaXR5GAYgASgFUghwcmlvcml0eRIrChFzdXBwb3J0X2ludGVycnVwdBgHIAEoBVIQc3VwcG'
    '9ydEludGVycnVwdBIZCghmcmFtZV9ubxgIIAEoB1IHZnJhbWVObxIeCgtvcHVzX2RhdGFfMRgJ'
    'IAEoDFIJb3B1c0RhdGExEh4KC29wdXNfZGF0YV8yGAogASgMUglvcHVzRGF0YTI=');

@$core.Deprecated('Use bc30Descriptor instead')
const bc30$json = {
  '1': 'bc30',
  '2': [
    {'1': 'repeater_dmrid', '3': 1, '4': 1, '5': 7, '10': 'repeaterDmrid'},
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'time_slot_no', '3': 4, '4': 1, '5': 5, '10': 'timeSlotNo'},
    {'1': 'call_type', '3': 5, '4': 1, '5': 5, '10': 'callType'},
    {'1': 'priority', '3': 6, '4': 1, '5': 5, '10': 'priority'},
    {'1': 'support_interrupt', '3': 7, '4': 1, '5': 5, '10': 'supportInterrupt'},
    {'1': 'frame_no', '3': 8, '4': 1, '5': 7, '10': 'frameNo'},
    {'1': 'ambe_data', '3': 9, '4': 1, '5': 12, '10': 'ambeData'},
    {'1': 'sound_type', '3': 10, '4': 1, '5': 5, '10': 'soundType'},
    {'1': 'source_repeater_dmrid', '3': 11, '4': 1, '5': 7, '10': 'sourceRepeaterDmrid'},
  ],
};

/// Descriptor for `bc30`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc30Descriptor = $convert.base64Decode(
    'CgRiYzMwEiUKDnJlcGVhdGVyX2RtcmlkGAEgASgHUg1yZXBlYXRlckRtcmlkEiEKDHRhcmdldF'
    '9kbXJpZBgCIAEoB1ILdGFyZ2V0RG1yaWQSIQoMc291cmNlX2RtcmlkGAMgASgHUgtzb3VyY2VE'
    'bXJpZBIgCgx0aW1lX3Nsb3Rfbm8YBCABKAVSCnRpbWVTbG90Tm8SGwoJY2FsbF90eXBlGAUgAS'
    'gFUghjYWxsVHlwZRIaCghwcmlvcml0eRgGIAEoBVIIcHJpb3JpdHkSKwoRc3VwcG9ydF9pbnRl'
    'cnJ1cHQYByABKAVSEHN1cHBvcnRJbnRlcnJ1cHQSGQoIZnJhbWVfbm8YCCABKAdSB2ZyYW1lTm'
    '8SGwoJYW1iZV9kYXRhGAkgASgMUghhbWJlRGF0YRIdCgpzb3VuZF90eXBlGAogASgFUglzb3Vu'
    'ZFR5cGUSMgoVc291cmNlX3JlcGVhdGVyX2RtcmlkGAsgASgHUhNzb3VyY2VSZXBlYXRlckRtcm'
    'lk');

@$core.Deprecated('Use dtmfDescriptor instead')
const dtmf$json = {
  '1': 'dtmf',
  '2': [
    {'1': 'repeater_dmrid', '3': 1, '4': 1, '5': 7, '10': 'repeaterDmrid'},
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'dtmf_str', '3': 6, '4': 1, '5': 9, '10': 'dtmfStr'},
    {'1': 'source_repeater_dmrid', '3': 7, '4': 1, '5': 7, '10': 'sourceRepeaterDmrid'},
  ],
};

/// Descriptor for `dtmf`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dtmfDescriptor = $convert.base64Decode(
    'CgRkdG1mEiUKDnJlcGVhdGVyX2RtcmlkGAEgASgHUg1yZXBlYXRlckRtcmlkEiEKDHRhcmdldF'
    '9kbXJpZBgCIAEoB1ILdGFyZ2V0RG1yaWQSIQoMc291cmNlX2RtcmlkGAMgASgHUgtzb3VyY2VE'
    'bXJpZBIZCghkdG1mX3N0chgGIAEoCVIHZHRtZlN0chIyChVzb3VyY2VfcmVwZWF0ZXJfZG1yaW'
    'QYByABKAdSE3NvdXJjZVJlcGVhdGVyRG1yaWQ=');

@$core.Deprecated('Use end_callDescriptor instead')
const end_call$json = {
  '1': 'end_call',
  '2': [
    {'1': 'repeater_dmrid', '3': 1, '4': 1, '5': 7, '10': 'repeaterDmrid'},
    {'1': 'target_dmrid', '3': 2, '4': 1, '5': 7, '10': 'targetDmrid'},
    {'1': 'source_dmrid', '3': 3, '4': 1, '5': 7, '10': 'sourceDmrid'},
    {'1': 'sound_type', '3': 12, '4': 1, '5': 5, '10': 'soundType'},
    {'1': 'phone_no', '3': 13, '4': 1, '5': 9, '10': 'phoneNo'},
    {'1': 'source_repeater_dmrid', '3': 14, '4': 1, '5': 7, '10': 'sourceRepeaterDmrid'},
  ],
};

/// Descriptor for `end_call`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List end_callDescriptor = $convert.base64Decode(
    'CghlbmRfY2FsbBIlCg5yZXBlYXRlcl9kbXJpZBgBIAEoB1INcmVwZWF0ZXJEbXJpZBIhCgx0YX'
    'JnZXRfZG1yaWQYAiABKAdSC3RhcmdldERtcmlkEiEKDHNvdXJjZV9kbXJpZBgDIAEoB1ILc291'
    'cmNlRG1yaWQSHQoKc291bmRfdHlwZRgMIAEoBVIJc291bmRUeXBlEhkKCHBob25lX25vGA0gAS'
    'gJUgdwaG9uZU5vEjIKFXNvdXJjZV9yZXBlYXRlcl9kbXJpZBgOIAEoB1ITc291cmNlUmVwZWF0'
    'ZXJEbXJpZA==');

@$core.Deprecated('Use phone_transferDescriptor instead')
const phone_transfer$json = {
  '1': 'phone_transfer',
  '2': [
    {'1': 'phone_dmrid', '3': 2, '4': 1, '5': 7, '10': 'phoneDmrid'},
    {'1': 'now_target', '3': 3, '4': 1, '5': 7, '10': 'nowTarget'},
    {'1': 'transfer_target', '3': 4, '4': 1, '5': 7, '10': 'transferTarget'},
  ],
};

/// Descriptor for `phone_transfer`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List phone_transferDescriptor = $convert.base64Decode(
    'Cg5waG9uZV90cmFuc2ZlchIfCgtwaG9uZV9kbXJpZBgCIAEoB1IKcGhvbmVEbXJpZBIdCgpub3'
    'dfdGFyZ2V0GAMgASgHUglub3dUYXJnZXQSJwoPdHJhbnNmZXJfdGFyZ2V0GAQgASgHUg50cmFu'
    'c2ZlclRhcmdldA==');

@$core.Deprecated('Use oneChannelItemDescriptor instead')
const OneChannelItem$json = {
  '1': 'OneChannelItem',
  '2': [
    {'1': 'no', '3': 1, '4': 1, '5': 5, '10': 'no'},
    {'1': 'sendGroup', '3': 2, '4': 1, '5': 9, '10': 'sendGroup'},
    {'1': 'listenGroup', '3': 3, '4': 3, '5': 9, '10': 'listenGroup'},
    {'1': 'zoneRid', '3': 4, '4': 1, '5': 9, '10': 'zoneRid'},
  ],
};

/// Descriptor for `OneChannelItem`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List oneChannelItemDescriptor = $convert.base64Decode(
    'Cg5PbmVDaGFubmVsSXRlbRIOCgJubxgBIAEoBVICbm8SHAoJc2VuZEdyb3VwGAIgASgJUglzZW'
    '5kR3JvdXASIAoLbGlzdGVuR3JvdXAYAyADKAlSC2xpc3Rlbkdyb3VwEhgKB3pvbmVSaWQYBCAB'
    'KAlSB3pvbmVSaWQ=');

@$core.Deprecated('Use channelsDescriptor instead')
const Channels$json = {
  '1': 'Channels',
  '2': [
    {'1': 'channels', '3': 1, '4': 3, '5': 11, '6': '.bfkcp.OneChannelItem', '10': 'channels'},
    {'1': 'device_dmrid', '3': 2, '4': 1, '5': 9, '10': 'deviceDmrid'},
    {'1': 'device_priority', '3': 3, '4': 1, '5': 5, '10': 'devicePriority'},
  ],
};

/// Descriptor for `Channels`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List channelsDescriptor = $convert.base64Decode(
    'CghDaGFubmVscxIxCghjaGFubmVscxgBIAMoCzIVLmJma2NwLk9uZUNoYW5uZWxJdGVtUghjaG'
    'FubmVscxIhCgxkZXZpY2VfZG1yaWQYAiABKAlSC2RldmljZURtcmlkEicKD2RldmljZV9wcmlv'
    'cml0eRgDIAEoBVIOZGV2aWNlUHJpb3JpdHk=');

@$core.Deprecated('Use phoneAdapterShortNo2DmridReqDescriptor instead')
const PhoneAdapterShortNo2DmridReq$json = {
  '1': 'PhoneAdapterShortNo2DmridReq',
  '2': [
    {'1': 'short_no', '3': 1, '4': 1, '5': 9, '10': 'shortNo'},
    {'1': 'opt', '3': 2, '4': 1, '5': 5, '10': 'opt'},
    {'1': 'dmrid', '3': 3, '4': 1, '5': 7, '10': 'dmrid'},
  ],
};

/// Descriptor for `PhoneAdapterShortNo2DmridReq`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List phoneAdapterShortNo2DmridReqDescriptor = $convert.base64Decode(
    'ChxQaG9uZUFkYXB0ZXJTaG9ydE5vMkRtcmlkUmVxEhkKCHNob3J0X25vGAEgASgJUgdzaG9ydE'
    '5vEhAKA29wdBgCIAEoBVIDb3B0EhQKBWRtcmlkGAMgASgHUgVkbXJpZA==');

@$core.Deprecated('Use phoneAdapterShortNo2DmridResDescriptor instead')
const PhoneAdapterShortNo2DmridRes$json = {
  '1': 'PhoneAdapterShortNo2DmridRes',
  '2': [
    {'1': 'result', '3': 1, '4': 1, '5': 5, '10': 'result'},
    {'1': 'dmrid', '3': 2, '4': 1, '5': 7, '10': 'dmrid'},
    {'1': 'info', '3': 3, '4': 1, '5': 9, '10': 'info'},
  ],
};

/// Descriptor for `PhoneAdapterShortNo2DmridRes`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List phoneAdapterShortNo2DmridResDescriptor = $convert.base64Decode(
    'ChxQaG9uZUFkYXB0ZXJTaG9ydE5vMkRtcmlkUmVzEhYKBnJlc3VsdBgBIAEoBVIGcmVzdWx0Eh'
    'QKBWRtcmlkGAIgASgHUgVkbXJpZBISCgRpbmZvGAMgASgJUgRpbmZv');

@$core.Deprecated('Use phoneLineSettingDescriptor instead')
const PhoneLineSetting$json = {
  '1': 'PhoneLineSetting',
  '2': [
    {'1': 'action_code', '3': 1, '4': 1, '5': 5, '10': 'actionCode'},
    {'1': 'line_pos', '3': 2, '4': 3, '5': 5, '10': 'linePos'},
    {'1': 'line_phone_no', '3': 3, '4': 3, '5': 9, '10': 'linePhoneNo'},
    {'1': 'line_dmrid', '3': 4, '4': 3, '5': 7, '10': 'lineDmrid'},
  ],
};

/// Descriptor for `PhoneLineSetting`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List phoneLineSettingDescriptor = $convert.base64Decode(
    'ChBQaG9uZUxpbmVTZXR0aW5nEh8KC2FjdGlvbl9jb2RlGAEgASgFUgphY3Rpb25Db2RlEhkKCG'
    'xpbmVfcG9zGAIgAygFUgdsaW5lUG9zEiIKDWxpbmVfcGhvbmVfbm8YAyADKAlSC2xpbmVQaG9u'
    'ZU5vEh0KCmxpbmVfZG1yaWQYBCADKAdSCWxpbmVEbXJpZA==');

@$core.Deprecated('Use ex_one_orgDescriptor instead')
const ex_one_org$json = {
  '1': 'ex_one_org',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_self_id', '3': 3, '4': 1, '5': 9, '10': 'orgSelfId'},
    {'1': 'org_short_name', '3': 5, '4': 1, '5': 9, '10': 'orgShortName'},
    {'1': 'org_full_name', '3': 6, '4': 1, '5': 9, '10': 'orgFullName'},
    {'1': 'note', '3': 7, '4': 1, '5': 9, '10': 'note'},
    {'1': 'org_is_virtual', '3': 8, '4': 1, '5': 5, '10': 'orgIsVirtual'},
    {'1': 'dmr_id', '3': 9, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'parent_org_id', '3': 11, '4': 1, '5': 9, '10': 'parentOrgId'},
  ],
};

/// Descriptor for `ex_one_org`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List ex_one_orgDescriptor = $convert.base64Decode(
    'CgpleF9vbmVfb3JnEhAKA3JpZBgBIAEoCVIDcmlkEh4KC29yZ19zZWxmX2lkGAMgASgJUglvcm'
    'dTZWxmSWQSJAoOb3JnX3Nob3J0X25hbWUYBSABKAlSDG9yZ1Nob3J0TmFtZRIiCg1vcmdfZnVs'
    'bF9uYW1lGAYgASgJUgtvcmdGdWxsTmFtZRISCgRub3RlGAcgASgJUgRub3RlEiQKDm9yZ19pc1'
    '92aXJ0dWFsGAggASgFUgxvcmdJc1ZpcnR1YWwSFQoGZG1yX2lkGAkgASgJUgVkbXJJZBIiCg1w'
    'YXJlbnRfb3JnX2lkGAsgASgJUgtwYXJlbnRPcmdJZA==');

@$core.Deprecated('Use ex_one_deviceDescriptor instead')
const ex_one_device$json = {
  '1': 'ex_one_device',
  '2': [
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'self_id', '3': 4, '4': 1, '5': 9, '10': 'selfId'},
    {'1': 'dmr_id', '3': 5, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'vir_orgs', '3': 6, '4': 1, '5': 9, '10': 'virOrgs'},
    {'1': 'note', '3': 8, '4': 1, '5': 9, '10': 'note'},
    {'1': 'device_type', '3': 9, '4': 1, '5': 5, '10': 'deviceType'},
    {'1': 'priority', '3': 12, '4': 1, '5': 5, '10': 'priority'},
  ],
};

/// Descriptor for `ex_one_device`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List ex_one_deviceDescriptor = $convert.base64Decode(
    'Cg1leF9vbmVfZGV2aWNlEhUKBm9yZ19pZBgDIAEoCVIFb3JnSWQSFwoHc2VsZl9pZBgEIAEoCV'
    'IGc2VsZklkEhUKBmRtcl9pZBgFIAEoCVIFZG1ySWQSGQoIdmlyX29yZ3MYBiABKAlSB3Zpck9y'
    'Z3MSEgoEbm90ZRgIIAEoCVIEbm90ZRIfCgtkZXZpY2VfdHlwZRgJIAEoBVIKZGV2aWNlVHlwZR'
    'IaCghwcmlvcml0eRgMIAEoBVIIcHJpb3JpdHk=');

@$core.Deprecated('Use ambe_serial_codeDescriptor instead')
const ambe_serial_code$json = {
  '1': 'ambe_serial_code',
  '2': [
    {'1': 'code', '3': 1, '4': 3, '5': 5, '10': 'code'},
  ],
};

/// Descriptor for `ambe_serial_code`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List ambe_serial_codeDescriptor = $convert.base64Decode(
    'ChBhbWJlX3NlcmlhbF9jb2RlEhIKBGNvZGUYASADKAVSBGNvZGU=');

@$core.Deprecated('Use ex_oneline_devicesDescriptor instead')
const ex_oneline_devices$json = {
  '1': 'ex_oneline_devices',
  '2': [
    {'1': 'dmr_id', '3': 1, '4': 3, '5': 5, '10': 'dmrId'},
    {'1': 'last_data_time', '3': 2, '4': 3, '5': 3, '10': 'lastDataTime'},
  ],
};

/// Descriptor for `ex_oneline_devices`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List ex_oneline_devicesDescriptor = $convert.base64Decode(
    'ChJleF9vbmVsaW5lX2RldmljZXMSFQoGZG1yX2lkGAEgAygFUgVkbXJJZBIkCg5sYXN0X2RhdG'
    'FfdGltZRgCIAMoA1IMbGFzdERhdGFUaW1l');

@$core.Deprecated('Use iot_dataDescriptor instead')
const iot_data$json = {
  '1': 'iot_data',
  '2': [
    {'1': 'cmd', '3': 1, '4': 1, '5': 17, '10': 'cmd'},
    {'1': 'dev_type', '3': 2, '4': 1, '5': 17, '10': 'devType'},
    {'1': 'dev_id', '3': 3, '4': 1, '5': 12, '10': 'devId'},
    {'1': 'cmd_param', '3': 4, '4': 1, '5': 12, '10': 'cmdParam'},
    {'1': 'recv_dev_id', '3': 9, '4': 1, '5': 12, '10': 'recvDevId'},
    {'1': 'recv_time', '3': 10, '4': 1, '5': 18, '10': 'recvTime'},
    {'1': 'kcp_recv_dev_id', '3': 11, '4': 1, '5': 9, '10': 'kcpRecvDevId'},
  ],
};

/// Descriptor for `iot_data`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List iot_dataDescriptor = $convert.base64Decode(
    'Cghpb3RfZGF0YRIQCgNjbWQYASABKBFSA2NtZBIZCghkZXZfdHlwZRgCIAEoEVIHZGV2VHlwZR'
    'IVCgZkZXZfaWQYAyABKAxSBWRldklkEhsKCWNtZF9wYXJhbRgEIAEoDFIIY21kUGFyYW0SHgoL'
    'cmVjdl9kZXZfaWQYCSABKAxSCXJlY3ZEZXZJZBIbCglyZWN2X3RpbWUYCiABKBJSCHJlY3ZUaW'
    '1lEiUKD2tjcF9yZWN2X2Rldl9pZBgLIAEoCVIMa2NwUmVjdkRldklk');

@$core.Deprecated('Use dev_data_infoDescriptor instead')
const dev_data_info$json = {
  '1': 'dev_data_info',
  '2': [
    {'1': 'code', '3': 1, '4': 1, '5': 5, '10': 'code'},
    {'1': 'src_dmrid', '3': 2, '4': 1, '5': 13, '10': 'srcDmrid'},
    {'1': 'dst_dmrid', '3': 3, '4': 1, '5': 13, '10': 'dstDmrid'},
    {'1': 'data', '3': 4, '4': 1, '5': 12, '10': 'data'},
  ],
};

/// Descriptor for `dev_data_info`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dev_data_infoDescriptor = $convert.base64Decode(
    'Cg1kZXZfZGF0YV9pbmZvEhIKBGNvZGUYASABKAVSBGNvZGUSGwoJc3JjX2RtcmlkGAIgASgNUg'
    'hzcmNEbXJpZBIbCglkc3RfZG1yaWQYAyABKA1SCGRzdERtcmlkEhIKBGRhdGEYBCABKAxSBGRh'
    'dGE=');

@$core.Deprecated('Use addr_bookDescriptor instead')
const addr_book$json = {
  '1': 'addr_book',
  '2': [
    {'1': 'type', '3': 1, '4': 1, '5': 5, '10': 'type'},
    {'1': 'code', '3': 2, '4': 1, '5': 5, '10': 'code'},
    {'1': 'body', '3': 3, '4': 1, '5': 12, '10': 'body'},
    {'1': 'para_str', '3': 4, '4': 1, '5': 9, '10': 'paraStr'},
  ],
};

/// Descriptor for `addr_book`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List addr_bookDescriptor = $convert.base64Decode(
    'CglhZGRyX2Jvb2sSEgoEdHlwZRgBIAEoBVIEdHlwZRISCgRjb2RlGAIgASgFUgRjb2RlEhIKBG'
    'JvZHkYAyABKAxSBGJvZHkSGQoIcGFyYV9zdHIYBCABKAlSB3BhcmFTdHI=');

@$core.Deprecated('Use addr_book_listDescriptor instead')
const addr_book_list$json = {
  '1': 'addr_book_list',
  '2': [
    {'1': 'addr_book_list', '3': 1, '4': 3, '5': 11, '6': '.bfkcp.addr_book', '10': 'addrBookList'},
  ],
};

/// Descriptor for `addr_book_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List addr_book_listDescriptor = $convert.base64Decode(
    'Cg5hZGRyX2Jvb2tfbGlzdBI2Cg5hZGRyX2Jvb2tfbGlzdBgBIAMoCzIQLmJma2NwLmFkZHJfYm'
    '9va1IMYWRkckJvb2tMaXN0');

@$core.Deprecated('Use bc40_reqDescriptor instead')
const bc40_req$json = {
  '1': 'bc40_req',
  '2': [
    {'1': 'dev_dmrid', '3': 1, '4': 1, '5': 7, '10': 'devDmrid'},
    {'1': 'group_dmrid', '3': 2, '4': 1, '5': 7, '10': 'groupDmrid'},
    {'1': 'riss', '3': 3, '4': 1, '5': 5, '10': 'riss'},
    {'1': 'status', '3': 4, '4': 1, '5': 12, '10': 'status'},
    {'1': 'power_event', '3': 5, '4': 1, '5': 5, '10': 'powerEvent'},
    {'1': 'last_power_event', '3': 6, '4': 1, '5': 5, '10': 'lastPowerEvent'},
    {'1': 'roaming', '3': 7, '4': 1, '5': 5, '10': 'roaming'},
    {'1': 'bs_index', '3': 8, '4': 1, '5': 5, '10': 'bsIndex'},
  ],
};

/// Descriptor for `bc40_req`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc40_reqDescriptor = $convert.base64Decode(
    'CghiYzQwX3JlcRIbCglkZXZfZG1yaWQYASABKAdSCGRldkRtcmlkEh8KC2dyb3VwX2RtcmlkGA'
    'IgASgHUgpncm91cERtcmlkEhIKBHJpc3MYAyABKAVSBHJpc3MSFgoGc3RhdHVzGAQgASgMUgZz'
    'dGF0dXMSHwoLcG93ZXJfZXZlbnQYBSABKAVSCnBvd2VyRXZlbnQSKAoQbGFzdF9wb3dlcl9ldm'
    'VudBgGIAEoBVIObGFzdFBvd2VyRXZlbnQSGAoHcm9hbWluZxgHIAEoBVIHcm9hbWluZxIZCghi'
    'c19pbmRleBgIIAEoBVIHYnNJbmRleA==');

@$core.Deprecated('Use bc40_respDescriptor instead')
const bc40_resp$json = {
  '1': 'bc40_resp',
  '2': [
    {'1': 'dev_dmrid', '3': 1, '4': 1, '5': 7, '10': 'devDmrid'},
    {'1': 'res_code', '3': 2, '4': 1, '5': 5, '10': 'resCode'},
    {'1': 'sys_time', '3': 3, '4': 1, '5': 3, '10': 'sysTime'},
  ],
};

/// Descriptor for `bc40_resp`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc40_respDescriptor = $convert.base64Decode(
    'CgliYzQwX3Jlc3ASGwoJZGV2X2RtcmlkGAEgASgHUghkZXZEbXJpZBIZCghyZXNfY29kZRgCIA'
    'EoBVIHcmVzQ29kZRIZCghzeXNfdGltZRgDIAEoA1IHc3lzVGltZQ==');

@$core.Deprecated('Use bf8100DmridInfoDescriptor instead')
const Bf8100DmridInfo$json = {
  '1': 'Bf8100DmridInfo',
  '2': [
    {'1': 'DmrID', '3': 1, '4': 1, '5': 7, '10': 'DmrID'},
    {'1': 'Name', '3': 2, '4': 1, '5': 9, '10': 'Name'},
    {'1': 'OrgUUID', '3': 4, '4': 1, '5': 9, '10': 'OrgUUID'},
    {'1': 'MyUUID', '3': 5, '4': 1, '5': 9, '10': 'MyUUID'},
  ],
};

/// Descriptor for `Bf8100DmridInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bf8100DmridInfoDescriptor = $convert.base64Decode(
    'Cg9CZjgxMDBEbXJpZEluZm8SFAoFRG1ySUQYASABKAdSBURtcklEEhIKBE5hbWUYAiABKAlSBE'
    '5hbWUSGAoHT3JnVVVJRBgEIAEoCVIHT3JnVVVJRBIWCgZNeVVVSUQYBSABKAlSBk15VVVJRA==');

@$core.Deprecated('Use mesh_gps_info_tDescriptor instead')
const mesh_gps_info_t$json = {
  '1': 'mesh_gps_info_t',
  '2': [
    {'1': 'source_id', '3': 1, '4': 1, '5': 13, '10': 'sourceId'},
    {'1': 'target_id', '3': 2, '4': 1, '5': 13, '10': 'targetId'},
    {'1': 'hour', '3': 3, '4': 1, '5': 13, '10': 'hour'},
    {'1': 'minute', '3': 4, '4': 1, '5': 13, '10': 'minute'},
    {'1': 'second', '3': 5, '4': 1, '5': 13, '10': 'second'},
    {'1': 'day', '3': 6, '4': 1, '5': 13, '10': 'day'},
    {'1': 'month', '3': 7, '4': 1, '5': 13, '10': 'month'},
    {'1': 'year', '3': 8, '4': 1, '5': 13, '10': 'year'},
    {'1': 'available', '3': 9, '4': 1, '5': 13, '10': 'available'},
    {'1': 'latitude', '3': 10, '4': 1, '5': 13, '10': 'latitude'},
    {'1': 'northOrSouth', '3': 11, '4': 1, '5': 13, '10': 'northOrSouth'},
    {'1': 'longitude', '3': 12, '4': 1, '5': 13, '10': 'longitude'},
    {'1': 'eastOrWest', '3': 13, '4': 1, '5': 13, '10': 'eastOrWest'},
    {'1': 'speed', '3': 14, '4': 1, '5': 13, '10': 'speed'},
    {'1': 'direction', '3': 15, '4': 1, '5': 13, '10': 'direction'},
    {'1': 'altitude', '3': 16, '4': 1, '5': 5, '10': 'altitude'},
    {'1': 'gps_data_type', '3': 17, '4': 1, '5': 14, '6': '.bfkcp.MESH_GPS_DATA_TYPE', '10': 'gpsDataType'},
  ],
};

/// Descriptor for `mesh_gps_info_t`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List mesh_gps_info_tDescriptor = $convert.base64Decode(
    'Cg9tZXNoX2dwc19pbmZvX3QSGwoJc291cmNlX2lkGAEgASgNUghzb3VyY2VJZBIbCgl0YXJnZX'
    'RfaWQYAiABKA1SCHRhcmdldElkEhIKBGhvdXIYAyABKA1SBGhvdXISFgoGbWludXRlGAQgASgN'
    'UgZtaW51dGUSFgoGc2Vjb25kGAUgASgNUgZzZWNvbmQSEAoDZGF5GAYgASgNUgNkYXkSFAoFbW'
    '9udGgYByABKA1SBW1vbnRoEhIKBHllYXIYCCABKA1SBHllYXISHAoJYXZhaWxhYmxlGAkgASgN'
    'UglhdmFpbGFibGUSGgoIbGF0aXR1ZGUYCiABKA1SCGxhdGl0dWRlEiIKDG5vcnRoT3JTb3V0aB'
    'gLIAEoDVIMbm9ydGhPclNvdXRoEhwKCWxvbmdpdHVkZRgMIAEoDVIJbG9uZ2l0dWRlEh4KCmVh'
    'c3RPcldlc3QYDSABKA1SCmVhc3RPcldlc3QSFAoFc3BlZWQYDiABKA1SBXNwZWVkEhwKCWRpcm'
    'VjdGlvbhgPIAEoDVIJZGlyZWN0aW9uEhoKCGFsdGl0dWRlGBAgASgFUghhbHRpdHVkZRI9Cg1n'
    'cHNfZGF0YV90eXBlGBEgASgOMhkuYmZrY3AuTUVTSF9HUFNfREFUQV9UWVBFUgtncHNEYXRhVH'
    'lwZQ==');

@$core.Deprecated('Use meshGpsInfoDescriptor instead')
const MeshGpsInfo$json = {
  '1': 'MeshGpsInfo',
  '2': [
    {'1': 'DmrID', '3': 1, '4': 1, '5': 7, '10': 'DmrID'},
    {'1': 'GpsInfo', '3': 2, '4': 1, '5': 11, '6': '.bfkcp.mesh_gps_info_t', '10': 'GpsInfo'},
  ],
};

/// Descriptor for `MeshGpsInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List meshGpsInfoDescriptor = $convert.base64Decode(
    'CgtNZXNoR3BzSW5mbxIUCgVEbXJJRBgBIAEoB1IFRG1ySUQSMAoHR3BzSW5mbxgCIAEoCzIWLm'
    'Jma2NwLm1lc2hfZ3BzX2luZm9fdFIHR3BzSW5mbw==');

@$core.Deprecated('Use pocCommandDescriptor instead')
const PocCommand$json = {
  '1': 'PocCommand',
  '2': [
    {'1': 'cmd', '3': 1, '4': 1, '5': 5, '10': 'cmd'},
    {'1': 'seq_no', '3': 2, '4': 1, '5': 3, '10': 'seqNo'},
    {'1': 'body', '3': 3, '4': 1, '5': 12, '10': 'body'},
    {'1': 'para_str', '3': 4, '4': 1, '5': 9, '10': 'paraStr'},
    {'1': 'para_bin', '3': 5, '4': 1, '5': 12, '10': 'paraBin'},
    {'1': 'para_int', '3': 6, '4': 1, '5': 3, '10': 'paraInt'},
  ],
};

/// Descriptor for `PocCommand`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pocCommandDescriptor = $convert.base64Decode(
    'CgpQb2NDb21tYW5kEhAKA2NtZBgBIAEoBVIDY21kEhUKBnNlcV9ubxgCIAEoA1IFc2VxTm8SEg'
    'oEYm9keRgDIAEoDFIEYm9keRIZCghwYXJhX3N0chgEIAEoCVIHcGFyYVN0chIZCghwYXJhX2Jp'
    'bhgFIAEoDFIHcGFyYUJpbhIZCghwYXJhX2ludBgGIAEoA1IHcGFyYUludA==');

@$core.Deprecated('Use pocDefaultGroupDescriptor instead')
const PocDefaultGroup$json = {
  '1': 'PocDefaultGroup',
  '2': [
    {'1': 'default_send_group_dmrid', '3': 1, '4': 1, '5': 7, '10': 'defaultSendGroupDmrid'},
    {'1': 'default_listen_group_dmrids', '3': 2, '4': 3, '5': 7, '10': 'defaultListenGroupDmrids'},
  ],
};

/// Descriptor for `PocDefaultGroup`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pocDefaultGroupDescriptor = $convert.base64Decode(
    'Cg9Qb2NEZWZhdWx0R3JvdXASNwoYZGVmYXVsdF9zZW5kX2dyb3VwX2RtcmlkGAEgASgHUhVkZW'
    'ZhdWx0U2VuZEdyb3VwRG1yaWQSPQobZGVmYXVsdF9saXN0ZW5fZ3JvdXBfZG1yaWRzGAIgAygH'
    'UhhkZWZhdWx0TGlzdGVuR3JvdXBEbXJpZHM=');

@$core.Deprecated('Use pocSubscribleUpdateOptionDescriptor instead')
const PocSubscribleUpdateOption$json = {
  '1': 'PocSubscribleUpdateOption',
  '2': [
    {'1': 'frame_no', '3': 1, '4': 1, '5': 5, '10': 'frameNo'},
    {'1': 'frame_type', '3': 2, '4': 1, '5': 5, '10': 'frameType'},
  ],
};

/// Descriptor for `PocSubscribleUpdateOption`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pocSubscribleUpdateOptionDescriptor = $convert.base64Decode(
    'ChlQb2NTdWJzY3JpYmxlVXBkYXRlT3B0aW9uEhkKCGZyYW1lX25vGAEgASgFUgdmcmFtZU5vEh'
    '0KCmZyYW1lX3R5cGUYAiABKAVSCWZyYW1lVHlwZQ==');

@$core.Deprecated('Use pocConfigDescriptor instead')
const PocConfig$json = {
  '1': 'PocConfig',
  '2': [
    {'1': 'can_edit_subscription_local', '3': 1, '4': 1, '5': 5, '10': 'canEditSubscriptionLocal'},
    {'1': 'rgps', '3': 2, '4': 1, '5': 5, '10': 'rgps'},
  ],
};

/// Descriptor for `PocConfig`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pocConfigDescriptor = $convert.base64Decode(
    'CglQb2NDb25maWcSPQobY2FuX2VkaXRfc3Vic2NyaXB0aW9uX2xvY2FsGAEgASgFUhhjYW5FZG'
    'l0U3Vic2NyaXB0aW9uTG9jYWwSEgoEcmdwcxgCIAEoBVIEcmdwcw==');

