//
//  Generated code. Do not modify.
//  source: bf_radio.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

/// 一个通用的gps84定位数据
class gps84 extends $pb.GeneratedMessage {
  factory gps84({
    $core.String? gpsTime,
    $core.int? av,
    $core.double? lat,
    $core.double? lon,
    $core.double? speed,
    $core.int? direction,
    $core.int? altitude,
  }) {
    final $result = create();
    if (gpsTime != null) {
      $result.gpsTime = gpsTime;
    }
    if (av != null) {
      $result.av = av;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (speed != null) {
      $result.speed = speed;
    }
    if (direction != null) {
      $result.direction = direction;
    }
    if (altitude != null) {
      $result.altitude = altitude;
    }
    return $result;
  }
  gps84._() : super();
  factory gps84.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory gps84.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'gps84', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'gpsTime')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'av', $pb.PbFieldType.O3)
    ..a<$core.double>(3, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(4, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..a<$core.double>(5, _omitFieldNames ? '' : 'speed', $pb.PbFieldType.OD)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'direction', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'altitude', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  gps84 clone() => gps84()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  gps84 copyWith(void Function(gps84) updates) => super.copyWith((message) => updates(message as gps84)) as gps84;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static gps84 create() => gps84._();
  gps84 createEmptyInstance() => create();
  static $pb.PbList<gps84> createRepeated() => $pb.PbList<gps84>();
  @$core.pragma('dart2js:noInline')
  static gps84 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<gps84>(create);
  static gps84? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get gpsTime => $_getSZ(0);
  @$pb.TagNumber(1)
  set gpsTime($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasGpsTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearGpsTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get av => $_getIZ(1);
  @$pb.TagNumber(2)
  set av($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAv() => $_has(1);
  @$pb.TagNumber(2)
  void clearAv() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get lat => $_getN(2);
  @$pb.TagNumber(3)
  set lat($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLat() => $_has(2);
  @$pb.TagNumber(3)
  void clearLat() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get lon => $_getN(3);
  @$pb.TagNumber(4)
  set lon($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLon() => $_has(3);
  @$pb.TagNumber(4)
  void clearLon() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get speed => $_getN(4);
  @$pb.TagNumber(5)
  set speed($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSpeed() => $_has(4);
  @$pb.TagNumber(5)
  void clearSpeed() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get direction => $_getIZ(5);
  @$pb.TagNumber(6)
  set direction($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDirection() => $_has(5);
  @$pb.TagNumber(6)
  void clearDirection() => clearField(6);

  @$pb.TagNumber(7)
  $core.int get altitude => $_getIZ(6);
  @$pb.TagNumber(7)
  set altitude($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasAltitude() => $_has(6);
  @$pb.TagNumber(7)
  void clearAltitude() => clearField(7);
}

/// 上来的BC命令通用数据头
class bcxx_head extends $pb.GeneratedMessage {
  factory bcxx_head({
    $core.String? xx,
    $core.int? bDic,
    $core.int? cmd,
    $core.String? sysId,
    $core.int? giFlag,
    $core.String? cConCh,
    $core.String? conCh,
    $core.String? mIdT,
    $core.String? mIdS,
    $core.String? cmdTime,
    $core.int? mTxE,
    $core.String? msStatus,
    $core.List<$core.int>? origData,
  }) {
    final $result = create();
    if (xx != null) {
      $result.xx = xx;
    }
    if (bDic != null) {
      $result.bDic = bDic;
    }
    if (cmd != null) {
      $result.cmd = cmd;
    }
    if (sysId != null) {
      $result.sysId = sysId;
    }
    if (giFlag != null) {
      $result.giFlag = giFlag;
    }
    if (cConCh != null) {
      $result.cConCh = cConCh;
    }
    if (conCh != null) {
      $result.conCh = conCh;
    }
    if (mIdT != null) {
      $result.mIdT = mIdT;
    }
    if (mIdS != null) {
      $result.mIdS = mIdS;
    }
    if (cmdTime != null) {
      $result.cmdTime = cmdTime;
    }
    if (mTxE != null) {
      $result.mTxE = mTxE;
    }
    if (msStatus != null) {
      $result.msStatus = msStatus;
    }
    if (origData != null) {
      $result.origData = origData;
    }
    return $result;
  }
  bcxx_head._() : super();
  factory bcxx_head.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bcxx_head.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bcxx_head', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'xx')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'bDic', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'cmd', $pb.PbFieldType.O3)
    ..aOS(4, _omitFieldNames ? '' : 'sysId')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'giFlag', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'cConCh')
    ..aOS(7, _omitFieldNames ? '' : 'conCh')
    ..aOS(8, _omitFieldNames ? '' : 'mIdT')
    ..aOS(9, _omitFieldNames ? '' : 'mIdS')
    ..aOS(10, _omitFieldNames ? '' : 'cmdTime')
    ..a<$core.int>(11, _omitFieldNames ? '' : 'mTxE', $pb.PbFieldType.O3)
    ..aOS(12, _omitFieldNames ? '' : 'msStatus')
    ..a<$core.List<$core.int>>(13, _omitFieldNames ? '' : 'origData', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bcxx_head clone() => bcxx_head()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bcxx_head copyWith(void Function(bcxx_head) updates) => super.copyWith((message) => updates(message as bcxx_head)) as bcxx_head;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bcxx_head create() => bcxx_head._();
  bcxx_head createEmptyInstance() => create();
  static $pb.PbList<bcxx_head> createRepeated() => $pb.PbList<bcxx_head>();
  @$core.pragma('dart2js:noInline')
  static bcxx_head getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bcxx_head>(create);
  static bcxx_head? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get xx => $_getSZ(0);
  @$pb.TagNumber(1)
  set xx($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasXx() => $_has(0);
  @$pb.TagNumber(1)
  void clearXx() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get bDic => $_getIZ(1);
  @$pb.TagNumber(2)
  set bDic($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBDic() => $_has(1);
  @$pb.TagNumber(2)
  void clearBDic() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get cmd => $_getIZ(2);
  @$pb.TagNumber(3)
  set cmd($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCmd() => $_has(2);
  @$pb.TagNumber(3)
  void clearCmd() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get sysId => $_getSZ(3);
  @$pb.TagNumber(4)
  set sysId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSysId() => $_has(3);
  @$pb.TagNumber(4)
  void clearSysId() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get giFlag => $_getIZ(4);
  @$pb.TagNumber(5)
  set giFlag($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasGiFlag() => $_has(4);
  @$pb.TagNumber(5)
  void clearGiFlag() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get cConCh => $_getSZ(5);
  @$pb.TagNumber(6)
  set cConCh($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCConCh() => $_has(5);
  @$pb.TagNumber(6)
  void clearCConCh() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get conCh => $_getSZ(6);
  @$pb.TagNumber(7)
  set conCh($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasConCh() => $_has(6);
  @$pb.TagNumber(7)
  void clearConCh() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get mIdT => $_getSZ(7);
  @$pb.TagNumber(8)
  set mIdT($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasMIdT() => $_has(7);
  @$pb.TagNumber(8)
  void clearMIdT() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get mIdS => $_getSZ(8);
  @$pb.TagNumber(9)
  set mIdS($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasMIdS() => $_has(8);
  @$pb.TagNumber(9)
  void clearMIdS() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get cmdTime => $_getSZ(9);
  @$pb.TagNumber(10)
  set cmdTime($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasCmdTime() => $_has(9);
  @$pb.TagNumber(10)
  void clearCmdTime() => clearField(10);

  @$pb.TagNumber(11)
  $core.int get mTxE => $_getIZ(10);
  @$pb.TagNumber(11)
  set mTxE($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasMTxE() => $_has(10);
  @$pb.TagNumber(11)
  void clearMTxE() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get msStatus => $_getSZ(11);
  @$pb.TagNumber(12)
  set msStatus($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasMsStatus() => $_has(11);
  @$pb.TagNumber(12)
  void clearMsStatus() => clearField(12);

  @$pb.TagNumber(13)
  $core.List<$core.int> get origData => $_getN(12);
  @$pb.TagNumber(13)
  set origData($core.List<$core.int> v) { $_setBytes(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasOrigData() => $_has(12);
  @$pb.TagNumber(13)
  void clearOrigData() => clearField(13);
}

/// 收到重复的命令
/// rpc_cmd.command=1101
class cmd_repeate_received extends $pb.GeneratedMessage {
  factory cmd_repeate_received({
    $core.String? dmrId,
    $core.String? cmd,
    $core.String? cDic,
    $core.String? sysId,
    $core.List<$core.int>? cmdBytes,
  }) {
    final $result = create();
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (cmd != null) {
      $result.cmd = cmd;
    }
    if (cDic != null) {
      $result.cDic = cDic;
    }
    if (sysId != null) {
      $result.sysId = sysId;
    }
    if (cmdBytes != null) {
      $result.cmdBytes = cmdBytes;
    }
    return $result;
  }
  cmd_repeate_received._() : super();
  factory cmd_repeate_received.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cmd_repeate_received.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cmd_repeate_received', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'dmrId')
    ..aOS(2, _omitFieldNames ? '' : 'cmd')
    ..aOS(3, _omitFieldNames ? '' : 'cDic')
    ..aOS(4, _omitFieldNames ? '' : 'sysId')
    ..a<$core.List<$core.int>>(5, _omitFieldNames ? '' : 'cmdBytes', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cmd_repeate_received clone() => cmd_repeate_received()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cmd_repeate_received copyWith(void Function(cmd_repeate_received) updates) => super.copyWith((message) => updates(message as cmd_repeate_received)) as cmd_repeate_received;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cmd_repeate_received create() => cmd_repeate_received._();
  cmd_repeate_received createEmptyInstance() => create();
  static $pb.PbList<cmd_repeate_received> createRepeated() => $pb.PbList<cmd_repeate_received>();
  @$core.pragma('dart2js:noInline')
  static cmd_repeate_received getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cmd_repeate_received>(create);
  static cmd_repeate_received? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get dmrId => $_getSZ(0);
  @$pb.TagNumber(1)
  set dmrId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDmrId() => $_has(0);
  @$pb.TagNumber(1)
  void clearDmrId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get cmd => $_getSZ(1);
  @$pb.TagNumber(2)
  set cmd($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCmd() => $_has(1);
  @$pb.TagNumber(2)
  void clearCmd() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get cDic => $_getSZ(2);
  @$pb.TagNumber(3)
  set cDic($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCDic() => $_has(2);
  @$pb.TagNumber(3)
  void clearCDic() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get sysId => $_getSZ(3);
  @$pb.TagNumber(4)
  set sysId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSysId() => $_has(3);
  @$pb.TagNumber(4)
  void clearSysId() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<$core.int> get cmdBytes => $_getN(4);
  @$pb.TagNumber(5)
  set cmdBytes($core.List<$core.int> v) { $_setBytes(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCmdBytes() => $_has(4);
  @$pb.TagNumber(5)
  void clearCmdBytes() => clearField(5);
}

/// 收到还没登录的对讲机上来的数据
/// rpc_cmd.command=1100
class not_register_device_cmd extends $pb.GeneratedMessage {
  factory not_register_device_cmd({
    $core.String? dmrId,
    $core.List<$core.int>? cmdBytes,
    $core.String? receivedControllerDmrId,
  }) {
    final $result = create();
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (cmdBytes != null) {
      $result.cmdBytes = cmdBytes;
    }
    if (receivedControllerDmrId != null) {
      $result.receivedControllerDmrId = receivedControllerDmrId;
    }
    return $result;
  }
  not_register_device_cmd._() : super();
  factory not_register_device_cmd.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory not_register_device_cmd.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'not_register_device_cmd', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'dmrId')
    ..a<$core.List<$core.int>>(2, _omitFieldNames ? '' : 'cmdBytes', $pb.PbFieldType.OY)
    ..aOS(3, _omitFieldNames ? '' : 'receivedControllerDmrId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  not_register_device_cmd clone() => not_register_device_cmd()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  not_register_device_cmd copyWith(void Function(not_register_device_cmd) updates) => super.copyWith((message) => updates(message as not_register_device_cmd)) as not_register_device_cmd;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static not_register_device_cmd create() => not_register_device_cmd._();
  not_register_device_cmd createEmptyInstance() => create();
  static $pb.PbList<not_register_device_cmd> createRepeated() => $pb.PbList<not_register_device_cmd>();
  @$core.pragma('dart2js:noInline')
  static not_register_device_cmd getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<not_register_device_cmd>(create);
  static not_register_device_cmd? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get dmrId => $_getSZ(0);
  @$pb.TagNumber(1)
  set dmrId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDmrId() => $_has(0);
  @$pb.TagNumber(1)
  void clearDmrId() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.int> get cmdBytes => $_getN(1);
  @$pb.TagNumber(2)
  set cmdBytes($core.List<$core.int> v) { $_setBytes(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCmdBytes() => $_has(1);
  @$pb.TagNumber(2)
  void clearCmdBytes() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get receivedControllerDmrId => $_getSZ(2);
  @$pb.TagNumber(3)
  set receivedControllerDmrId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasReceivedControllerDmrId() => $_has(2);
  @$pb.TagNumber(3)
  void clearReceivedControllerDmrId() => clearField(3);
}

/// 收到还没登记到数据库的控制器信息
/// rpc_cmd.cmd=1102
/// nats.subject=sys_id.new_controller
class new_controller extends $pb.GeneratedMessage {
  factory new_controller({
    $core.String? dmrId,
    $core.String? ipInfo,
    $core.String? model,
    $core.String? deviceName,
  }) {
    final $result = create();
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (ipInfo != null) {
      $result.ipInfo = ipInfo;
    }
    if (model != null) {
      $result.model = model;
    }
    if (deviceName != null) {
      $result.deviceName = deviceName;
    }
    return $result;
  }
  new_controller._() : super();
  factory new_controller.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory new_controller.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'new_controller', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'dmrId')
    ..aOS(2, _omitFieldNames ? '' : 'ipInfo')
    ..aOS(3, _omitFieldNames ? '' : 'model')
    ..aOS(4, _omitFieldNames ? '' : 'deviceName')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  new_controller clone() => new_controller()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  new_controller copyWith(void Function(new_controller) updates) => super.copyWith((message) => updates(message as new_controller)) as new_controller;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static new_controller create() => new_controller._();
  new_controller createEmptyInstance() => create();
  static $pb.PbList<new_controller> createRepeated() => $pb.PbList<new_controller>();
  @$core.pragma('dart2js:noInline')
  static new_controller getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<new_controller>(create);
  static new_controller? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get dmrId => $_getSZ(0);
  @$pb.TagNumber(1)
  set dmrId($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDmrId() => $_has(0);
  @$pb.TagNumber(1)
  void clearDmrId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get ipInfo => $_getSZ(1);
  @$pb.TagNumber(2)
  set ipInfo($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasIpInfo() => $_has(1);
  @$pb.TagNumber(2)
  void clearIpInfo() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get model => $_getSZ(2);
  @$pb.TagNumber(3)
  set model($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasModel() => $_has(2);
  @$pb.TagNumber(3)
  void clearModel() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get deviceName => $_getSZ(3);
  @$pb.TagNumber(4)
  set deviceName($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeviceName() => $_has(3);
  @$pb.TagNumber(4)
  void clearDeviceName() => clearField(4);
}

class bc00 extends $pb.GeneratedMessage {
  factory bc00({
    bcxx_head? head,
    gps84? gps,
    $core.int? cDic,
    $core.int? cbxx,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (cDic != null) {
      $result.cDic = cDic;
    }
    if (cbxx != null) {
      $result.cbxx = cbxx;
    }
    return $result;
  }
  bc00._() : super();
  factory bc00.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc00.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc00', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'cDic', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'cbxx', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc00 clone() => bc00()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc00 copyWith(void Function(bc00) updates) => super.copyWith((message) => updates(message as bc00)) as bc00;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc00 create() => bc00._();
  bc00 createEmptyInstance() => create();
  static $pb.PbList<bc00> createRepeated() => $pb.PbList<bc00>();
  @$core.pragma('dart2js:noInline')
  static bc00 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc00>(create);
  static bc00? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get cDic => $_getIZ(2);
  @$pb.TagNumber(3)
  set cDic($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCDic() => $_has(2);
  @$pb.TagNumber(3)
  void clearCDic() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get cbxx => $_getIZ(3);
  @$pb.TagNumber(4)
  set cbxx($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCbxx() => $_has(3);
  @$pb.TagNumber(4)
  void clearCbxx() => clearField(4);
}

class bc01 extends $pb.GeneratedMessage {
  factory bc01({
    bcxx_head? head,
    gps84? gps,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    return $result;
  }
  bc01._() : super();
  factory bc01.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc01.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc01', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc01 clone() => bc01()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc01 copyWith(void Function(bc01) updates) => super.copyWith((message) => updates(message as bc01)) as bc01;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc01 create() => bc01._();
  bc01 createEmptyInstance() => create();
  static $pb.PbList<bc01> createRepeated() => $pb.PbList<bc01>();
  @$core.pragma('dart2js:noInline')
  static bc01 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc01>(create);
  static bc01? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);
}

class bc02 extends $pb.GeneratedMessage {
  factory bc02({
    bcxx_head? head,
    gps84? gps,
    $core.int? yN,
    $core.int? time,
    $core.int? size,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (time != null) {
      $result.time = time;
    }
    if (size != null) {
      $result.size = size;
    }
    return $result;
  }
  bc02._() : super();
  factory bc02.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc02.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc02', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'size', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc02 clone() => bc02()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc02 copyWith(void Function(bc02) updates) => super.copyWith((message) => updates(message as bc02)) as bc02;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc02 create() => bc02._();
  bc02 createEmptyInstance() => create();
  static $pb.PbList<bc02> createRepeated() => $pb.PbList<bc02>();
  @$core.pragma('dart2js:noInline')
  static bc02 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc02>(create);
  static bc02? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(2);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(2);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get time => $_getIZ(3);
  @$pb.TagNumber(4)
  set time($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearTime() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get size => $_getIZ(4);
  @$pb.TagNumber(5)
  set size($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSize() => $_has(4);
  @$pb.TagNumber(5)
  void clearSize() => clearField(5);
}

class bc03 extends $pb.GeneratedMessage {
  factory bc03({
    bcxx_head? head,
    gps84? gps,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    return $result;
  }
  bc03._() : super();
  factory bc03.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc03.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc03', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc03 clone() => bc03()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc03 copyWith(void Function(bc03) updates) => super.copyWith((message) => updates(message as bc03)) as bc03;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc03 create() => bc03._();
  bc03 createEmptyInstance() => create();
  static $pb.PbList<bc03> createRepeated() => $pb.PbList<bc03>();
  @$core.pragma('dart2js:noInline')
  static bc03 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc03>(create);
  static bc03? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);
}

class bc04 extends $pb.GeneratedMessage {
  factory bc04({
    bcxx_head? head,
    gps84? gps,
    $core.int? tp,
    $core.int? yN,
    $core.int? penN,
    $core.int? time,
    $core.double? minLat,
    $core.double? minLon,
    $core.double? maxLat,
    $core.double? maxLon,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (tp != null) {
      $result.tp = tp;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (penN != null) {
      $result.penN = penN;
    }
    if (time != null) {
      $result.time = time;
    }
    if (minLat != null) {
      $result.minLat = minLat;
    }
    if (minLon != null) {
      $result.minLon = minLon;
    }
    if (maxLat != null) {
      $result.maxLat = maxLat;
    }
    if (maxLon != null) {
      $result.maxLon = maxLon;
    }
    return $result;
  }
  bc04._() : super();
  factory bc04.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc04.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc04', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'tp', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'penN', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.double>(7, _omitFieldNames ? '' : 'minLat', $pb.PbFieldType.OD)
    ..a<$core.double>(8, _omitFieldNames ? '' : 'minLon', $pb.PbFieldType.OD)
    ..a<$core.double>(9, _omitFieldNames ? '' : 'maxLat', $pb.PbFieldType.OD)
    ..a<$core.double>(10, _omitFieldNames ? '' : 'maxLon', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc04 clone() => bc04()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc04 copyWith(void Function(bc04) updates) => super.copyWith((message) => updates(message as bc04)) as bc04;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc04 create() => bc04._();
  bc04 createEmptyInstance() => create();
  static $pb.PbList<bc04> createRepeated() => $pb.PbList<bc04>();
  @$core.pragma('dart2js:noInline')
  static bc04 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc04>(create);
  static bc04? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get tp => $_getIZ(2);
  @$pb.TagNumber(3)
  set tp($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTp() => $_has(2);
  @$pb.TagNumber(3)
  void clearTp() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get yN => $_getIZ(3);
  @$pb.TagNumber(4)
  set yN($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasYN() => $_has(3);
  @$pb.TagNumber(4)
  void clearYN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get penN => $_getIZ(4);
  @$pb.TagNumber(5)
  set penN($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPenN() => $_has(4);
  @$pb.TagNumber(5)
  void clearPenN() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get time => $_getIZ(5);
  @$pb.TagNumber(6)
  set time($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasTime() => $_has(5);
  @$pb.TagNumber(6)
  void clearTime() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get minLat => $_getN(6);
  @$pb.TagNumber(7)
  set minLat($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasMinLat() => $_has(6);
  @$pb.TagNumber(7)
  void clearMinLat() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get minLon => $_getN(7);
  @$pb.TagNumber(8)
  set minLon($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasMinLon() => $_has(7);
  @$pb.TagNumber(8)
  void clearMinLon() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get maxLat => $_getN(8);
  @$pb.TagNumber(9)
  set maxLat($core.double v) { $_setDouble(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasMaxLat() => $_has(8);
  @$pb.TagNumber(9)
  void clearMaxLat() => clearField(9);

  @$pb.TagNumber(10)
  $core.double get maxLon => $_getN(9);
  @$pb.TagNumber(10)
  set maxLon($core.double v) { $_setDouble(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasMaxLon() => $_has(9);
  @$pb.TagNumber(10)
  void clearMaxLon() => clearField(10);
}

class bc05 extends $pb.GeneratedMessage {
  factory bc05({
    bcxx_head? head,
    gps84? gps,
    $core.int? tp,
    $core.int? yN,
    $core.int? time,
    $core.double? lat,
    $core.double? lon,
    $core.String? latDif,
    $core.String? lonDif,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (tp != null) {
      $result.tp = tp;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (time != null) {
      $result.time = time;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (latDif != null) {
      $result.latDif = latDif;
    }
    if (lonDif != null) {
      $result.lonDif = lonDif;
    }
    return $result;
  }
  bc05._() : super();
  factory bc05.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc05.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc05', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'tp', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.double>(6, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(7, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..aOS(8, _omitFieldNames ? '' : 'latDif')
    ..aOS(9, _omitFieldNames ? '' : 'lonDif')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc05 clone() => bc05()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc05 copyWith(void Function(bc05) updates) => super.copyWith((message) => updates(message as bc05)) as bc05;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc05 create() => bc05._();
  bc05 createEmptyInstance() => create();
  static $pb.PbList<bc05> createRepeated() => $pb.PbList<bc05>();
  @$core.pragma('dart2js:noInline')
  static bc05 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc05>(create);
  static bc05? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get tp => $_getIZ(2);
  @$pb.TagNumber(3)
  set tp($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTp() => $_has(2);
  @$pb.TagNumber(3)
  void clearTp() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get yN => $_getIZ(3);
  @$pb.TagNumber(4)
  set yN($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasYN() => $_has(3);
  @$pb.TagNumber(4)
  void clearYN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get time => $_getIZ(4);
  @$pb.TagNumber(5)
  set time($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearTime() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get lat => $_getN(5);
  @$pb.TagNumber(6)
  set lat($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLat() => $_has(5);
  @$pb.TagNumber(6)
  void clearLat() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get lon => $_getN(6);
  @$pb.TagNumber(7)
  set lon($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLon() => $_has(6);
  @$pb.TagNumber(7)
  void clearLon() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get latDif => $_getSZ(7);
  @$pb.TagNumber(8)
  set latDif($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLatDif() => $_has(7);
  @$pb.TagNumber(8)
  void clearLatDif() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get lonDif => $_getSZ(8);
  @$pb.TagNumber(9)
  set lonDif($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasLonDif() => $_has(8);
  @$pb.TagNumber(9)
  void clearLonDif() => clearField(9);
}

class bc06 extends $pb.GeneratedMessage {
  factory bc06({
    bcxx_head? head,
    gps84? gps,
    $core.int? tp,
    $core.int? yN,
    $core.int? time,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (tp != null) {
      $result.tp = tp;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (time != null) {
      $result.time = time;
    }
    return $result;
  }
  bc06._() : super();
  factory bc06.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc06.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc06', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'tp', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc06 clone() => bc06()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc06 copyWith(void Function(bc06) updates) => super.copyWith((message) => updates(message as bc06)) as bc06;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc06 create() => bc06._();
  bc06 createEmptyInstance() => create();
  static $pb.PbList<bc06> createRepeated() => $pb.PbList<bc06>();
  @$core.pragma('dart2js:noInline')
  static bc06 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc06>(create);
  static bc06? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get tp => $_getIZ(2);
  @$pb.TagNumber(3)
  set tp($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTp() => $_has(2);
  @$pb.TagNumber(3)
  void clearTp() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get yN => $_getIZ(3);
  @$pb.TagNumber(4)
  set yN($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasYN() => $_has(3);
  @$pb.TagNumber(4)
  void clearYN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get time => $_getIZ(4);
  @$pb.TagNumber(5)
  set time($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearTime() => clearField(5);
}

class bc07 extends $pb.GeneratedMessage {
  factory bc07({
    bcxx_head? head,
    gps84? gps,
    $core.int? yN,
    $core.int? jtTime,
    $core.int? dwTime,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (jtTime != null) {
      $result.jtTime = jtTime;
    }
    if (dwTime != null) {
      $result.dwTime = dwTime;
    }
    return $result;
  }
  bc07._() : super();
  factory bc07.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc07.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc07', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'jtTime', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'dwTime', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc07 clone() => bc07()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc07 copyWith(void Function(bc07) updates) => super.copyWith((message) => updates(message as bc07)) as bc07;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc07 create() => bc07._();
  bc07 createEmptyInstance() => create();
  static $pb.PbList<bc07> createRepeated() => $pb.PbList<bc07>();
  @$core.pragma('dart2js:noInline')
  static bc07 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc07>(create);
  static bc07? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(2);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(2);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get jtTime => $_getIZ(3);
  @$pb.TagNumber(4)
  set jtTime($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasJtTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearJtTime() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get dwTime => $_getIZ(4);
  @$pb.TagNumber(5)
  set dwTime($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDwTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearDwTime() => clearField(5);
}

class bc08 extends $pb.GeneratedMessage {
  factory bc08({
    bcxx_head? head,
    gps84? gps,
    $core.int? yN,
    $core.int? jtCh,
    $core.int? time,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (jtCh != null) {
      $result.jtCh = jtCh;
    }
    if (time != null) {
      $result.time = time;
    }
    return $result;
  }
  bc08._() : super();
  factory bc08.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc08.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc08', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'jtCh', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc08 clone() => bc08()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc08 copyWith(void Function(bc08) updates) => super.copyWith((message) => updates(message as bc08)) as bc08;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc08 create() => bc08._();
  bc08 createEmptyInstance() => create();
  static $pb.PbList<bc08> createRepeated() => $pb.PbList<bc08>();
  @$core.pragma('dart2js:noInline')
  static bc08 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc08>(create);
  static bc08? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(4)
  $core.int get yN => $_getIZ(2);
  @$pb.TagNumber(4)
  set yN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasYN() => $_has(2);
  @$pb.TagNumber(4)
  void clearYN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get jtCh => $_getIZ(3);
  @$pb.TagNumber(5)
  set jtCh($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasJtCh() => $_has(3);
  @$pb.TagNumber(5)
  void clearJtCh() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get time => $_getIZ(4);
  @$pb.TagNumber(6)
  set time($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasTime() => $_has(4);
  @$pb.TagNumber(6)
  void clearTime() => clearField(6);
}

class bc09 extends $pb.GeneratedMessage {
  factory bc09({
    bcxx_head? head,
    gps84? gps,
    $core.int? yN,
    $core.int? st,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (st != null) {
      $result.st = st;
    }
    return $result;
  }
  bc09._() : super();
  factory bc09.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc09.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc09', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'st', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc09 clone() => bc09()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc09 copyWith(void Function(bc09) updates) => super.copyWith((message) => updates(message as bc09)) as bc09;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc09 create() => bc09._();
  bc09 createEmptyInstance() => create();
  static $pb.PbList<bc09> createRepeated() => $pb.PbList<bc09>();
  @$core.pragma('dart2js:noInline')
  static bc09 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc09>(create);
  static bc09? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(2);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(2);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get st => $_getIZ(3);
  @$pb.TagNumber(4)
  set st($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSt() => $_has(3);
  @$pb.TagNumber(4)
  void clearSt() => clearField(4);
}

class bc10 extends $pb.GeneratedMessage {
  factory bc10({
    bcxx_head? head,
    gps84? gps,
    $core.int? cDic,
    $core.int? cbxx,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (cDic != null) {
      $result.cDic = cDic;
    }
    if (cbxx != null) {
      $result.cbxx = cbxx;
    }
    return $result;
  }
  bc10._() : super();
  factory bc10.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc10.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc10', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'cDic', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'cbxx', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc10 clone() => bc10()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc10 copyWith(void Function(bc10) updates) => super.copyWith((message) => updates(message as bc10)) as bc10;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc10 create() => bc10._();
  bc10 createEmptyInstance() => create();
  static $pb.PbList<bc10> createRepeated() => $pb.PbList<bc10>();
  @$core.pragma('dart2js:noInline')
  static bc10 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc10>(create);
  static bc10? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get cDic => $_getIZ(2);
  @$pb.TagNumber(3)
  set cDic($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCDic() => $_has(2);
  @$pb.TagNumber(3)
  void clearCDic() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get cbxx => $_getIZ(3);
  @$pb.TagNumber(4)
  set cbxx($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCbxx() => $_has(3);
  @$pb.TagNumber(4)
  void clearCbxx() => clearField(4);
}

class bc11 extends $pb.GeneratedMessage {
  factory bc11({
    bcxx_head? head,
    $core.int? yN,
    $core.List<$core.int>? cTp,
    $core.int? aTp,
    $core.int? ddCh,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (cTp != null) {
      $result.cTp = cTp;
    }
    if (aTp != null) {
      $result.aTp = aTp;
    }
    if (ddCh != null) {
      $result.ddCh = ddCh;
    }
    return $result;
  }
  bc11._() : super();
  factory bc11.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc11.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc11', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'cTp', $pb.PbFieldType.OY)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'aTp', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'ddCh', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc11 clone() => bc11()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc11 copyWith(void Function(bc11) updates) => super.copyWith((message) => updates(message as bc11)) as bc11;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc11 create() => bc11._();
  bc11 createEmptyInstance() => create();
  static $pb.PbList<bc11> createRepeated() => $pb.PbList<bc11>();
  @$core.pragma('dart2js:noInline')
  static bc11 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc11>(create);
  static bc11? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.int> get cTp => $_getN(2);
  @$pb.TagNumber(3)
  set cTp($core.List<$core.int> v) { $_setBytes(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCTp() => $_has(2);
  @$pb.TagNumber(3)
  void clearCTp() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get aTp => $_getIZ(3);
  @$pb.TagNumber(4)
  set aTp($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasATp() => $_has(3);
  @$pb.TagNumber(4)
  void clearATp() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get ddCh => $_getIZ(4);
  @$pb.TagNumber(5)
  set ddCh($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDdCh() => $_has(4);
  @$pb.TagNumber(5)
  void clearDdCh() => clearField(5);
}

class bc12 extends $pb.GeneratedMessage {
  factory bc12({
    bcxx_head? head,
    gps84? gps,
    $core.int? pS,
    $core.int? lpS,
    $core.String? lsTime,
    $core.String? sellId,
    $core.String? licence,
    $core.int? devGroup,
    $core.int? roaming,
    $core.bool? fromBc40,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (pS != null) {
      $result.pS = pS;
    }
    if (lpS != null) {
      $result.lpS = lpS;
    }
    if (lsTime != null) {
      $result.lsTime = lsTime;
    }
    if (sellId != null) {
      $result.sellId = sellId;
    }
    if (licence != null) {
      $result.licence = licence;
    }
    if (devGroup != null) {
      $result.devGroup = devGroup;
    }
    if (roaming != null) {
      $result.roaming = roaming;
    }
    if (fromBc40 != null) {
      $result.fromBc40 = fromBc40;
    }
    return $result;
  }
  bc12._() : super();
  factory bc12.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc12.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc12', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'pS', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'lpS', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'lsTime')
    ..aOS(6, _omitFieldNames ? '' : 'sellId')
    ..aOS(7, _omitFieldNames ? '' : 'licence')
    ..a<$core.int>(8, _omitFieldNames ? '' : 'devGroup', $pb.PbFieldType.OF3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'roaming', $pb.PbFieldType.O3)
    ..aOB(10, _omitFieldNames ? '' : 'fromBc40')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc12 clone() => bc12()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc12 copyWith(void Function(bc12) updates) => super.copyWith((message) => updates(message as bc12)) as bc12;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc12 create() => bc12._();
  bc12 createEmptyInstance() => create();
  static $pb.PbList<bc12> createRepeated() => $pb.PbList<bc12>();
  @$core.pragma('dart2js:noInline')
  static bc12 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc12>(create);
  static bc12? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get pS => $_getIZ(2);
  @$pb.TagNumber(3)
  set pS($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPS() => $_has(2);
  @$pb.TagNumber(3)
  void clearPS() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get lpS => $_getIZ(3);
  @$pb.TagNumber(4)
  set lpS($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLpS() => $_has(3);
  @$pb.TagNumber(4)
  void clearLpS() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get lsTime => $_getSZ(4);
  @$pb.TagNumber(5)
  set lsTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLsTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearLsTime() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get sellId => $_getSZ(5);
  @$pb.TagNumber(6)
  set sellId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSellId() => $_has(5);
  @$pb.TagNumber(6)
  void clearSellId() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get licence => $_getSZ(6);
  @$pb.TagNumber(7)
  set licence($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLicence() => $_has(6);
  @$pb.TagNumber(7)
  void clearLicence() => clearField(7);

  /// *手台归属组ID*
  @$pb.TagNumber(8)
  $core.int get devGroup => $_getIZ(7);
  @$pb.TagNumber(8)
  set devGroup($core.int v) { $_setUnsignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasDevGroup() => $_has(7);
  @$pb.TagNumber(8)
  void clearDevGroup() => clearField(8);

  /// *0:主信道即选定发起注册,1:漫游信道发起注册*
  @$pb.TagNumber(9)
  $core.int get roaming => $_getIZ(8);
  @$pb.TagNumber(9)
  set roaming($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasRoaming() => $_has(8);
  @$pb.TagNumber(9)
  void clearRoaming() => clearField(9);

  @$pb.TagNumber(10)
  $core.bool get fromBc40 => $_getBF(9);
  @$pb.TagNumber(10)
  set fromBc40($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasFromBc40() => $_has(9);
  @$pb.TagNumber(10)
  void clearFromBc40() => clearField(10);
}

class bc13 extends $pb.GeneratedMessage {
  factory bc13({
    bcxx_head? head,
    gps84? gps,
    $core.int? type,
    $core.int? landBs,
    $core.int? landCh,
    $core.int? devGroup,
    $core.int? roaming,
    $core.bool? fromBc40,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (type != null) {
      $result.type = type;
    }
    if (landBs != null) {
      $result.landBs = landBs;
    }
    if (landCh != null) {
      $result.landCh = landCh;
    }
    if (devGroup != null) {
      $result.devGroup = devGroup;
    }
    if (roaming != null) {
      $result.roaming = roaming;
    }
    if (fromBc40 != null) {
      $result.fromBc40 = fromBc40;
    }
    return $result;
  }
  bc13._() : super();
  factory bc13.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc13.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc13', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'type', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'landBs', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'landCh', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'devGroup', $pb.PbFieldType.OF3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'roaming', $pb.PbFieldType.O3)
    ..aOB(8, _omitFieldNames ? '' : 'fromBc40')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc13 clone() => bc13()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc13 copyWith(void Function(bc13) updates) => super.copyWith((message) => updates(message as bc13)) as bc13;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc13 create() => bc13._();
  bc13 createEmptyInstance() => create();
  static $pb.PbList<bc13> createRepeated() => $pb.PbList<bc13>();
  @$core.pragma('dart2js:noInline')
  static bc13 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc13>(create);
  static bc13? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get type => $_getIZ(2);
  @$pb.TagNumber(3)
  set type($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasType() => $_has(2);
  @$pb.TagNumber(3)
  void clearType() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get landBs => $_getIZ(3);
  @$pb.TagNumber(4)
  set landBs($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLandBs() => $_has(3);
  @$pb.TagNumber(4)
  void clearLandBs() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get landCh => $_getIZ(4);
  @$pb.TagNumber(5)
  set landCh($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLandCh() => $_has(4);
  @$pb.TagNumber(5)
  void clearLandCh() => clearField(5);

  /// *手台归属组ID*
  @$pb.TagNumber(6)
  $core.int get devGroup => $_getIZ(5);
  @$pb.TagNumber(6)
  set devGroup($core.int v) { $_setUnsignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDevGroup() => $_has(5);
  @$pb.TagNumber(6)
  void clearDevGroup() => clearField(6);

  /// *0:主信道即选定发起注册,1:漫游信道发起注册*
  @$pb.TagNumber(7)
  $core.int get roaming => $_getIZ(6);
  @$pb.TagNumber(7)
  set roaming($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasRoaming() => $_has(6);
  @$pb.TagNumber(7)
  void clearRoaming() => clearField(7);

  @$pb.TagNumber(8)
  $core.bool get fromBc40 => $_getBF(7);
  @$pb.TagNumber(8)
  set fromBc40($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasFromBc40() => $_has(7);
  @$pb.TagNumber(8)
  void clearFromBc40() => clearField(8);
}

class bc14 extends $pb.GeneratedMessage {
  factory bc14({
    bcxx_head? head,
    gps84? gps,
    $core.int? landBs,
    $core.int? audioCm,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (landBs != null) {
      $result.landBs = landBs;
    }
    if (audioCm != null) {
      $result.audioCm = audioCm;
    }
    return $result;
  }
  bc14._() : super();
  factory bc14.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc14.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc14', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'landBs', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'audioCm', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc14 clone() => bc14()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc14 copyWith(void Function(bc14) updates) => super.copyWith((message) => updates(message as bc14)) as bc14;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc14 create() => bc14._();
  bc14 createEmptyInstance() => create();
  static $pb.PbList<bc14> createRepeated() => $pb.PbList<bc14>();
  @$core.pragma('dart2js:noInline')
  static bc14 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc14>(create);
  static bc14? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get landBs => $_getIZ(2);
  @$pb.TagNumber(3)
  set landBs($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLandBs() => $_has(2);
  @$pb.TagNumber(3)
  void clearLandBs() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get audioCm => $_getIZ(3);
  @$pb.TagNumber(4)
  set audioCm($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasAudioCm() => $_has(3);
  @$pb.TagNumber(4)
  void clearAudioCm() => clearField(4);
}

class bc15 extends $pb.GeneratedMessage {
  factory bc15({
    bcxx_head? head,
    $core.int? voiceDType,
    $core.int? voiceAType,
    $core.int? callType,
    $core.int? callStatus,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (voiceDType != null) {
      $result.voiceDType = voiceDType;
    }
    if (voiceAType != null) {
      $result.voiceAType = voiceAType;
    }
    if (callType != null) {
      $result.callType = callType;
    }
    if (callStatus != null) {
      $result.callStatus = callStatus;
    }
    return $result;
  }
  bc15._() : super();
  factory bc15.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc15.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc15', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'voiceDType', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'voiceAType', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'callType', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'callStatus', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc15 clone() => bc15()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc15 copyWith(void Function(bc15) updates) => super.copyWith((message) => updates(message as bc15)) as bc15;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc15 create() => bc15._();
  bc15 createEmptyInstance() => create();
  static $pb.PbList<bc15> createRepeated() => $pb.PbList<bc15>();
  @$core.pragma('dart2js:noInline')
  static bc15 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc15>(create);
  static bc15? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get voiceDType => $_getIZ(1);
  @$pb.TagNumber(2)
  set voiceDType($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasVoiceDType() => $_has(1);
  @$pb.TagNumber(2)
  void clearVoiceDType() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get voiceAType => $_getIZ(2);
  @$pb.TagNumber(3)
  set voiceAType($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasVoiceAType() => $_has(2);
  @$pb.TagNumber(3)
  void clearVoiceAType() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get callType => $_getIZ(3);
  @$pb.TagNumber(4)
  set callType($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCallType() => $_has(3);
  @$pb.TagNumber(4)
  void clearCallType() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get callStatus => $_getIZ(4);
  @$pb.TagNumber(5)
  set callStatus($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasCallStatus() => $_has(4);
  @$pb.TagNumber(5)
  void clearCallStatus() => clearField(5);
}

class bc16 extends $pb.GeneratedMessage {
  factory bc16({
    bcxx_head? head,
    gps84? gps,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    return $result;
  }
  bc16._() : super();
  factory bc16.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc16.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc16', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc16 clone() => bc16()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc16 copyWith(void Function(bc16) updates) => super.copyWith((message) => updates(message as bc16)) as bc16;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc16 create() => bc16._();
  bc16 createEmptyInstance() => create();
  static $pb.PbList<bc16> createRepeated() => $pb.PbList<bc16>();
  @$core.pragma('dart2js:noInline')
  static bc16 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc16>(create);
  static bc16? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);
}

class bc17 extends $pb.GeneratedMessage {
  factory bc17({
    bcxx_head? head,
    $core.int? yN,
    $core.String? userSt,
    $core.int? userTp,
    $core.String? userAddrId,
    $core.String? license,
    $core.String? userShowId,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (userSt != null) {
      $result.userSt = userSt;
    }
    if (userTp != null) {
      $result.userTp = userTp;
    }
    if (userAddrId != null) {
      $result.userAddrId = userAddrId;
    }
    if (license != null) {
      $result.license = license;
    }
    if (userShowId != null) {
      $result.userShowId = userShowId;
    }
    return $result;
  }
  bc17._() : super();
  factory bc17.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc17.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc17', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..aOS(3, _omitFieldNames ? '' : 'userSt')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'userTp', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'userAddrId')
    ..aOS(6, _omitFieldNames ? '' : 'license')
    ..aOS(7, _omitFieldNames ? '' : 'userShowId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc17 clone() => bc17()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc17 copyWith(void Function(bc17) updates) => super.copyWith((message) => updates(message as bc17)) as bc17;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc17 create() => bc17._();
  bc17 createEmptyInstance() => create();
  static $pb.PbList<bc17> createRepeated() => $pb.PbList<bc17>();
  @$core.pragma('dart2js:noInline')
  static bc17 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc17>(create);
  static bc17? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get userSt => $_getSZ(2);
  @$pb.TagNumber(3)
  set userSt($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUserSt() => $_has(2);
  @$pb.TagNumber(3)
  void clearUserSt() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get userTp => $_getIZ(3);
  @$pb.TagNumber(4)
  set userTp($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasUserTp() => $_has(3);
  @$pb.TagNumber(4)
  void clearUserTp() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get userAddrId => $_getSZ(4);
  @$pb.TagNumber(5)
  set userAddrId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasUserAddrId() => $_has(4);
  @$pb.TagNumber(5)
  void clearUserAddrId() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get license => $_getSZ(5);
  @$pb.TagNumber(6)
  set license($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLicense() => $_has(5);
  @$pb.TagNumber(6)
  void clearLicense() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get userShowId => $_getSZ(6);
  @$pb.TagNumber(7)
  set userShowId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasUserShowId() => $_has(6);
  @$pb.TagNumber(7)
  void clearUserShowId() => clearField(7);
}

class bc18 extends $pb.GeneratedMessage {
  factory bc18({
    bcxx_head? head,
    gps84? gps,
    $core.int? alarm,
    $core.String? dbRid,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (alarm != null) {
      $result.alarm = alarm;
    }
    if (dbRid != null) {
      $result.dbRid = dbRid;
    }
    return $result;
  }
  bc18._() : super();
  factory bc18.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc18.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc18', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'alarm', $pb.PbFieldType.O3)
    ..aOS(4, _omitFieldNames ? '' : 'dbRid')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc18 clone() => bc18()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc18 copyWith(void Function(bc18) updates) => super.copyWith((message) => updates(message as bc18)) as bc18;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc18 create() => bc18._();
  bc18 createEmptyInstance() => create();
  static $pb.PbList<bc18> createRepeated() => $pb.PbList<bc18>();
  @$core.pragma('dart2js:noInline')
  static bc18 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc18>(create);
  static bc18? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get alarm => $_getIZ(2);
  @$pb.TagNumber(3)
  set alarm($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasAlarm() => $_has(2);
  @$pb.TagNumber(3)
  void clearAlarm() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get dbRid => $_getSZ(3);
  @$pb.TagNumber(4)
  set dbRid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDbRid() => $_has(3);
  @$pb.TagNumber(4)
  void clearDbRid() => clearField(4);
}

class bc19 extends $pb.GeneratedMessage {
  factory bc19({
    bcxx_head? head,
    gps84? gps,
    $core.int? yN,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    return $result;
  }
  bc19._() : super();
  factory bc19.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc19.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc19', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc19 clone() => bc19()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc19 copyWith(void Function(bc19) updates) => super.copyWith((message) => updates(message as bc19)) as bc19;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc19 create() => bc19._();
  bc19 createEmptyInstance() => create();
  static $pb.PbList<bc19> createRepeated() => $pb.PbList<bc19>();
  @$core.pragma('dart2js:noInline')
  static bc19 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc19>(create);
  static bc19? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(2);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(2);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);
}

class bc20 extends $pb.GeneratedMessage {
  factory bc20({
    bcxx_head? head,
    gps84? gps,
    $core.int? yN,
    $core.int? linkTp,
    $core.int? aTp,
    $core.String? netId,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (linkTp != null) {
      $result.linkTp = linkTp;
    }
    if (aTp != null) {
      $result.aTp = aTp;
    }
    if (netId != null) {
      $result.netId = netId;
    }
    return $result;
  }
  bc20._() : super();
  factory bc20.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc20.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc20', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'linkTp', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'aTp', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'netId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc20 clone() => bc20()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc20 copyWith(void Function(bc20) updates) => super.copyWith((message) => updates(message as bc20)) as bc20;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc20 create() => bc20._();
  bc20 createEmptyInstance() => create();
  static $pb.PbList<bc20> createRepeated() => $pb.PbList<bc20>();
  @$core.pragma('dart2js:noInline')
  static bc20 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc20>(create);
  static bc20? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(2);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(2);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get linkTp => $_getIZ(3);
  @$pb.TagNumber(4)
  set linkTp($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLinkTp() => $_has(3);
  @$pb.TagNumber(4)
  void clearLinkTp() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get aTp => $_getIZ(4);
  @$pb.TagNumber(5)
  set aTp($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasATp() => $_has(4);
  @$pb.TagNumber(5)
  void clearATp() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get netId => $_getSZ(5);
  @$pb.TagNumber(6)
  set netId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasNetId() => $_has(5);
  @$pb.TagNumber(6)
  void clearNetId() => clearField(6);
}

class bc21 extends $pb.GeneratedMessage {
  factory bc21({
    bcxx_head? head,
    gps84? gps,
    $core.int? cDic,
    $core.int? cbxx,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (cDic != null) {
      $result.cDic = cDic;
    }
    if (cbxx != null) {
      $result.cbxx = cbxx;
    }
    return $result;
  }
  bc21._() : super();
  factory bc21.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc21.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc21', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'cDic', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'cbxx', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc21 clone() => bc21()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc21 copyWith(void Function(bc21) updates) => super.copyWith((message) => updates(message as bc21)) as bc21;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc21 create() => bc21._();
  bc21 createEmptyInstance() => create();
  static $pb.PbList<bc21> createRepeated() => $pb.PbList<bc21>();
  @$core.pragma('dart2js:noInline')
  static bc21 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc21>(create);
  static bc21? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get cDic => $_getIZ(2);
  @$pb.TagNumber(3)
  set cDic($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCDic() => $_has(2);
  @$pb.TagNumber(3)
  void clearCDic() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get cbxx => $_getIZ(3);
  @$pb.TagNumber(4)
  set cbxx($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCbxx() => $_has(3);
  @$pb.TagNumber(4)
  void clearCbxx() => clearField(4);
}

class bc22 extends $pb.GeneratedMessage {
  factory bc22({
    bcxx_head? head,
    gps84? gps,
    $core.int? cDic,
    $core.int? cbxx,
    $core.int? yN,
    $core.int? checkSt,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (cDic != null) {
      $result.cDic = cDic;
    }
    if (cbxx != null) {
      $result.cbxx = cbxx;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (checkSt != null) {
      $result.checkSt = checkSt;
    }
    return $result;
  }
  bc22._() : super();
  factory bc22.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc22.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc22', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'cDic', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'cbxx', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'checkSt', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc22 clone() => bc22()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc22 copyWith(void Function(bc22) updates) => super.copyWith((message) => updates(message as bc22)) as bc22;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc22 create() => bc22._();
  bc22 createEmptyInstance() => create();
  static $pb.PbList<bc22> createRepeated() => $pb.PbList<bc22>();
  @$core.pragma('dart2js:noInline')
  static bc22 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc22>(create);
  static bc22? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get cDic => $_getIZ(2);
  @$pb.TagNumber(3)
  set cDic($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCDic() => $_has(2);
  @$pb.TagNumber(3)
  void clearCDic() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get cbxx => $_getIZ(3);
  @$pb.TagNumber(4)
  set cbxx($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCbxx() => $_has(3);
  @$pb.TagNumber(4)
  void clearCbxx() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get yN => $_getIZ(4);
  @$pb.TagNumber(5)
  set yN($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasYN() => $_has(4);
  @$pb.TagNumber(5)
  void clearYN() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get checkSt => $_getIZ(5);
  @$pb.TagNumber(6)
  set checkSt($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasCheckSt() => $_has(5);
  @$pb.TagNumber(6)
  void clearCheckSt() => clearField(6);
}

class bc23 extends $pb.GeneratedMessage {
  factory bc23({
    bcxx_head? head,
    $core.String? conStatus,
    $core.int? cDic,
    $core.int? cbxx,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (conStatus != null) {
      $result.conStatus = conStatus;
    }
    if (cDic != null) {
      $result.cDic = cDic;
    }
    if (cbxx != null) {
      $result.cbxx = cbxx;
    }
    return $result;
  }
  bc23._() : super();
  factory bc23.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc23.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc23', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOS(2, _omitFieldNames ? '' : 'conStatus')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'cDic', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'cbxx', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc23 clone() => bc23()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc23 copyWith(void Function(bc23) updates) => super.copyWith((message) => updates(message as bc23)) as bc23;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc23 create() => bc23._();
  bc23 createEmptyInstance() => create();
  static $pb.PbList<bc23> createRepeated() => $pb.PbList<bc23>();
  @$core.pragma('dart2js:noInline')
  static bc23 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc23>(create);
  static bc23? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get conStatus => $_getSZ(1);
  @$pb.TagNumber(2)
  set conStatus($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasConStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearConStatus() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get cDic => $_getIZ(2);
  @$pb.TagNumber(3)
  set cDic($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCDic() => $_has(2);
  @$pb.TagNumber(3)
  void clearCDic() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get cbxx => $_getIZ(3);
  @$pb.TagNumber(4)
  set cbxx($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCbxx() => $_has(3);
  @$pb.TagNumber(4)
  void clearCbxx() => clearField(4);
}

class bc25 extends $pb.GeneratedMessage {
  factory bc25({
    bcxx_head? head,
    $core.int? yN,
    $core.int? pointN,
    $core.String? pointCard,
    $core.double? lat,
    $core.double? lon,
    $core.String? latDif,
    $core.String? lonDif,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (pointN != null) {
      $result.pointN = pointN;
    }
    if (pointCard != null) {
      $result.pointCard = pointCard;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (latDif != null) {
      $result.latDif = latDif;
    }
    if (lonDif != null) {
      $result.lonDif = lonDif;
    }
    return $result;
  }
  bc25._() : super();
  factory bc25.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc25.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc25', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'pointN', $pb.PbFieldType.O3)
    ..aOS(4, _omitFieldNames ? '' : 'pointCard')
    ..a<$core.double>(5, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(6, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..aOS(7, _omitFieldNames ? '' : 'latDif')
    ..aOS(8, _omitFieldNames ? '' : 'lonDif')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc25 clone() => bc25()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc25 copyWith(void Function(bc25) updates) => super.copyWith((message) => updates(message as bc25)) as bc25;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc25 create() => bc25._();
  bc25 createEmptyInstance() => create();
  static $pb.PbList<bc25> createRepeated() => $pb.PbList<bc25>();
  @$core.pragma('dart2js:noInline')
  static bc25 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc25>(create);
  static bc25? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get pointN => $_getIZ(2);
  @$pb.TagNumber(3)
  set pointN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPointN() => $_has(2);
  @$pb.TagNumber(3)
  void clearPointN() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get pointCard => $_getSZ(3);
  @$pb.TagNumber(4)
  set pointCard($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPointCard() => $_has(3);
  @$pb.TagNumber(4)
  void clearPointCard() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get lat => $_getN(4);
  @$pb.TagNumber(5)
  set lat($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLat() => $_has(4);
  @$pb.TagNumber(5)
  void clearLat() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get lon => $_getN(5);
  @$pb.TagNumber(6)
  set lon($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLon() => $_has(5);
  @$pb.TagNumber(6)
  void clearLon() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get latDif => $_getSZ(6);
  @$pb.TagNumber(7)
  set latDif($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLatDif() => $_has(6);
  @$pb.TagNumber(7)
  void clearLatDif() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get lonDif => $_getSZ(7);
  @$pb.TagNumber(8)
  set lonDif($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLonDif() => $_has(7);
  @$pb.TagNumber(8)
  void clearLonDif() => clearField(8);
}

class bc26 extends $pb.GeneratedMessage {
  factory bc26({
    bcxx_head? head,
    $core.int? yN,
    $core.int? ch,
    $core.int? codeTp,
    $core.List<$core.int>? chName,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (ch != null) {
      $result.ch = ch;
    }
    if (codeTp != null) {
      $result.codeTp = codeTp;
    }
    if (chName != null) {
      $result.chName = chName;
    }
    return $result;
  }
  bc26._() : super();
  factory bc26.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc26.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc26', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'ch', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'codeTp', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(5, _omitFieldNames ? '' : 'chName', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc26 clone() => bc26()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc26 copyWith(void Function(bc26) updates) => super.copyWith((message) => updates(message as bc26)) as bc26;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc26 create() => bc26._();
  bc26 createEmptyInstance() => create();
  static $pb.PbList<bc26> createRepeated() => $pb.PbList<bc26>();
  @$core.pragma('dart2js:noInline')
  static bc26 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc26>(create);
  static bc26? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get ch => $_getIZ(2);
  @$pb.TagNumber(3)
  set ch($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCh() => $_has(2);
  @$pb.TagNumber(3)
  void clearCh() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get codeTp => $_getIZ(3);
  @$pb.TagNumber(4)
  set codeTp($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCodeTp() => $_has(3);
  @$pb.TagNumber(4)
  void clearCodeTp() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<$core.int> get chName => $_getN(4);
  @$pb.TagNumber(5)
  set chName($core.List<$core.int> v) { $_setBytes(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasChName() => $_has(4);
  @$pb.TagNumber(5)
  void clearChName() => clearField(5);
}

class bc28 extends $pb.GeneratedMessage {
  factory bc28({
    bcxx_head? head,
    $core.int? yN,
    $core.int? codeTp,
    $core.int? cByte,
    $core.List<$core.int>? data,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (codeTp != null) {
      $result.codeTp = codeTp;
    }
    if (cByte != null) {
      $result.cByte = cByte;
    }
    if (data != null) {
      $result.data = data;
    }
    return $result;
  }
  bc28._() : super();
  factory bc28.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc28.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc28', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'codeTp', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'cByte', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(5, _omitFieldNames ? '' : 'data', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc28 clone() => bc28()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc28 copyWith(void Function(bc28) updates) => super.copyWith((message) => updates(message as bc28)) as bc28;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc28 create() => bc28._();
  bc28 createEmptyInstance() => create();
  static $pb.PbList<bc28> createRepeated() => $pb.PbList<bc28>();
  @$core.pragma('dart2js:noInline')
  static bc28 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc28>(create);
  static bc28? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get codeTp => $_getIZ(2);
  @$pb.TagNumber(3)
  set codeTp($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCodeTp() => $_has(2);
  @$pb.TagNumber(3)
  void clearCodeTp() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get cByte => $_getIZ(3);
  @$pb.TagNumber(4)
  set cByte($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCByte() => $_has(3);
  @$pb.TagNumber(4)
  void clearCByte() => clearField(4);

  @$pb.TagNumber(5)
  $core.List<$core.int> get data => $_getN(4);
  @$pb.TagNumber(5)
  set data($core.List<$core.int> v) { $_setBytes(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasData() => $_has(4);
  @$pb.TagNumber(5)
  void clearData() => clearField(5);
}

class bc29 extends $pb.GeneratedMessage {
  factory bc29({
    bcxx_head? head,
    $core.int? yN,
    $core.String? keyId,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (keyId != null) {
      $result.keyId = keyId;
    }
    return $result;
  }
  bc29._() : super();
  factory bc29.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc29.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc29', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..aOS(3, _omitFieldNames ? '' : 'keyId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc29 clone() => bc29()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc29 copyWith(void Function(bc29) updates) => super.copyWith((message) => updates(message as bc29)) as bc29;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc29 create() => bc29._();
  bc29 createEmptyInstance() => create();
  static $pb.PbList<bc29> createRepeated() => $pb.PbList<bc29>();
  @$core.pragma('dart2js:noInline')
  static bc29 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc29>(create);
  static bc29? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get keyId => $_getSZ(2);
  @$pb.TagNumber(3)
  set keyId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasKeyId() => $_has(2);
  @$pb.TagNumber(3)
  void clearKeyId() => clearField(3);
}

class bc31 extends $pb.GeneratedMessage {
  factory bc31({
    bcxx_head? head,
    $core.String? smsType,
    $core.String? smsContent,
    $core.String? smsNo,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (smsType != null) {
      $result.smsType = smsType;
    }
    if (smsContent != null) {
      $result.smsContent = smsContent;
    }
    if (smsNo != null) {
      $result.smsNo = smsNo;
    }
    return $result;
  }
  bc31._() : super();
  factory bc31.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc31.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc31', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOS(2, _omitFieldNames ? '' : 'smsType')
    ..aOS(3, _omitFieldNames ? '' : 'smsContent')
    ..aOS(4, _omitFieldNames ? '' : 'smsNo')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc31 clone() => bc31()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc31 copyWith(void Function(bc31) updates) => super.copyWith((message) => updates(message as bc31)) as bc31;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc31 create() => bc31._();
  bc31 createEmptyInstance() => create();
  static $pb.PbList<bc31> createRepeated() => $pb.PbList<bc31>();
  @$core.pragma('dart2js:noInline')
  static bc31 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc31>(create);
  static bc31? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get smsType => $_getSZ(1);
  @$pb.TagNumber(2)
  set smsType($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSmsType() => $_has(1);
  @$pb.TagNumber(2)
  void clearSmsType() => clearField(2);

  /// 短信内容
  @$pb.TagNumber(3)
  $core.String get smsContent => $_getSZ(2);
  @$pb.TagNumber(3)
  set smsContent($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSmsContent() => $_has(2);
  @$pb.TagNumber(3)
  void clearSmsContent() => clearField(3);

  /// 短信序号,uint16(hex)
  @$pb.TagNumber(4)
  $core.String get smsNo => $_getSZ(3);
  @$pb.TagNumber(4)
  set smsNo($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSmsNo() => $_has(3);
  @$pb.TagNumber(4)
  void clearSmsNo() => clearField(4);
}

class bc38 extends $pb.GeneratedMessage {
  factory bc38({
    bcxx_head? head,
    $core.String? mGroupId,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (mGroupId != null) {
      $result.mGroupId = mGroupId;
    }
    return $result;
  }
  bc38._() : super();
  factory bc38.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc38.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc38', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOS(3, _omitFieldNames ? '' : 'mGroupId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc38 clone() => bc38()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc38 copyWith(void Function(bc38) updates) => super.copyWith((message) => updates(message as bc38)) as bc38;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc38 create() => bc38._();
  bc38 createEmptyInstance() => create();
  static $pb.PbList<bc38> createRepeated() => $pb.PbList<bc38>();
  @$core.pragma('dart2js:noInline')
  static bc38 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc38>(create);
  static bc38? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.String get mGroupId => $_getSZ(1);
  @$pb.TagNumber(3)
  set mGroupId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasMGroupId() => $_has(1);
  @$pb.TagNumber(3)
  void clearMGroupId() => clearField(3);
}

class bc39 extends $pb.GeneratedMessage {
  factory bc39({
    bcxx_head? head,
    $core.String? mGroupId,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (mGroupId != null) {
      $result.mGroupId = mGroupId;
    }
    return $result;
  }
  bc39._() : super();
  factory bc39.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc39.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc39', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOS(3, _omitFieldNames ? '' : 'mGroupId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc39 clone() => bc39()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc39 copyWith(void Function(bc39) updates) => super.copyWith((message) => updates(message as bc39)) as bc39;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc39 create() => bc39._();
  bc39 createEmptyInstance() => create();
  static $pb.PbList<bc39> createRepeated() => $pb.PbList<bc39>();
  @$core.pragma('dart2js:noInline')
  static bc39 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc39>(create);
  static bc39? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.String get mGroupId => $_getSZ(1);
  @$pb.TagNumber(3)
  set mGroupId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasMGroupId() => $_has(1);
  @$pb.TagNumber(3)
  void clearMGroupId() => clearField(3);
}

class bc42 extends $pb.GeneratedMessage {
  factory bc42({
    bcxx_head? head,
    $core.int? code,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (code != null) {
      $result.code = code;
    }
    return $result;
  }
  bc42._() : super();
  factory bc42.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory bc42.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'bc42', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'code', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  bc42 clone() => bc42()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  bc42 copyWith(void Function(bc42) updates) => super.copyWith((message) => updates(message as bc42)) as bc42;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static bc42 create() => bc42._();
  bc42 createEmptyInstance() => create();
  static $pb.PbList<bc42> createRepeated() => $pb.PbList<bc42>();
  @$core.pragma('dart2js:noInline')
  static bc42 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<bc42>(create);
  static bc42? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  /// code uint8: 10:关闭状态 11:开启状态 14:不支持/不适用
  @$pb.TagNumber(2)
  $core.int get code => $_getIZ(1);
  @$pb.TagNumber(2)
  set code($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearCode() => clearField(2);
}

class dc00 extends $pb.GeneratedMessage {
  factory dc00({
    bcxx_head? head,
    gps84? gps,
    $core.int? cDic,
    $core.int? cdxx,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (gps != null) {
      $result.gps = gps;
    }
    if (cDic != null) {
      $result.cDic = cDic;
    }
    if (cdxx != null) {
      $result.cdxx = cdxx;
    }
    return $result;
  }
  dc00._() : super();
  factory dc00.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory dc00.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'dc00', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..aOM<gps84>(2, _omitFieldNames ? '' : 'gps', subBuilder: gps84.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'cDic', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'cdxx', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  dc00 clone() => dc00()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  dc00 copyWith(void Function(dc00) updates) => super.copyWith((message) => updates(message as dc00)) as dc00;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static dc00 create() => dc00._();
  dc00 createEmptyInstance() => create();
  static $pb.PbList<dc00> createRepeated() => $pb.PbList<dc00>();
  @$core.pragma('dart2js:noInline')
  static dc00 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<dc00>(create);
  static dc00? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  @$pb.TagNumber(2)
  gps84 get gps => $_getN(1);
  @$pb.TagNumber(2)
  set gps(gps84 v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasGps() => $_has(1);
  @$pb.TagNumber(2)
  void clearGps() => clearField(2);
  @$pb.TagNumber(2)
  gps84 ensureGps() => $_ensure(1);

  @$pb.TagNumber(3)
  $core.int get cDic => $_getIZ(2);
  @$pb.TagNumber(3)
  set cDic($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCDic() => $_has(2);
  @$pb.TagNumber(3)
  void clearCDic() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get cdxx => $_getIZ(3);
  @$pb.TagNumber(4)
  set cdxx($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCdxx() => $_has(3);
  @$pb.TagNumber(4)
  void clearCdxx() => clearField(4);
}

class dc01_one_rfid extends $pb.GeneratedMessage {
  factory dc01_one_rfid({
    $core.String? readTime,
    $core.String? rfidId,
    $core.int? rfidType,
    $core.int? dbType,
  }) {
    final $result = create();
    if (readTime != null) {
      $result.readTime = readTime;
    }
    if (rfidId != null) {
      $result.rfidId = rfidId;
    }
    if (rfidType != null) {
      $result.rfidType = rfidType;
    }
    if (dbType != null) {
      $result.dbType = dbType;
    }
    return $result;
  }
  dc01_one_rfid._() : super();
  factory dc01_one_rfid.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory dc01_one_rfid.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'dc01_one_rfid', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'readTime')
    ..aOS(2, _omitFieldNames ? '' : 'rfidId')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'rfidType', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'dbType', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  dc01_one_rfid clone() => dc01_one_rfid()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  dc01_one_rfid copyWith(void Function(dc01_one_rfid) updates) => super.copyWith((message) => updates(message as dc01_one_rfid)) as dc01_one_rfid;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static dc01_one_rfid create() => dc01_one_rfid._();
  dc01_one_rfid createEmptyInstance() => create();
  static $pb.PbList<dc01_one_rfid> createRepeated() => $pb.PbList<dc01_one_rfid>();
  @$core.pragma('dart2js:noInline')
  static dc01_one_rfid getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<dc01_one_rfid>(create);
  static dc01_one_rfid? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get readTime => $_getSZ(0);
  @$pb.TagNumber(1)
  set readTime($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasReadTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearReadTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get rfidId => $_getSZ(1);
  @$pb.TagNumber(2)
  set rfidId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasRfidId() => $_has(1);
  @$pb.TagNumber(2)
  void clearRfidId() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get rfidType => $_getIZ(2);
  @$pb.TagNumber(3)
  set rfidType($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRfidType() => $_has(2);
  @$pb.TagNumber(3)
  void clearRfidType() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get dbType => $_getIZ(3);
  @$pb.TagNumber(4)
  set dbType($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDbType() => $_has(3);
  @$pb.TagNumber(4)
  void clearDbType() => clearField(4);
}

class dc01 extends $pb.GeneratedMessage {
  factory dc01({
    bcxx_head? head,
    $core.int? backlogPoints,
    $core.Iterable<dc01_one_rfid>? rfids,
  }) {
    final $result = create();
    if (head != null) {
      $result.head = head;
    }
    if (backlogPoints != null) {
      $result.backlogPoints = backlogPoints;
    }
    if (rfids != null) {
      $result.rfids.addAll(rfids);
    }
    return $result;
  }
  dc01._() : super();
  factory dc01.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory dc01.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'dc01', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<bcxx_head>(1, _omitFieldNames ? '' : 'head', subBuilder: bcxx_head.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'backlogPoints', $pb.PbFieldType.O3)
    ..pc<dc01_one_rfid>(4, _omitFieldNames ? '' : 'rfids', $pb.PbFieldType.PM, subBuilder: dc01_one_rfid.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  dc01 clone() => dc01()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  dc01 copyWith(void Function(dc01) updates) => super.copyWith((message) => updates(message as dc01)) as dc01;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static dc01 create() => dc01._();
  dc01 createEmptyInstance() => create();
  static $pb.PbList<dc01> createRepeated() => $pb.PbList<dc01>();
  @$core.pragma('dart2js:noInline')
  static dc01 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<dc01>(create);
  static dc01? _defaultInstance;

  @$pb.TagNumber(1)
  bcxx_head get head => $_getN(0);
  @$pb.TagNumber(1)
  set head(bcxx_head v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHead() => $_has(0);
  @$pb.TagNumber(1)
  void clearHead() => clearField(1);
  @$pb.TagNumber(1)
  bcxx_head ensureHead() => $_ensure(0);

  /// 对讲机里面的积压点数量
  @$pb.TagNumber(2)
  $core.int get backlogPoints => $_getIZ(1);
  @$pb.TagNumber(2)
  set backlogPoints($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasBacklogPoints() => $_has(1);
  @$pb.TagNumber(2)
  void clearBacklogPoints() => clearField(2);

  @$pb.TagNumber(4)
  $core.List<dc01_one_rfid> get rfids => $_getList(2);
}

class cbxx_target extends $pb.GeneratedMessage {
  factory cbxx_target({
    $core.Iterable<$core.String>? targetGroud,
    $core.Iterable<$core.String>? targetDevice,
  }) {
    final $result = create();
    if (targetGroud != null) {
      $result.targetGroud.addAll(targetGroud);
    }
    if (targetDevice != null) {
      $result.targetDevice.addAll(targetDevice);
    }
    return $result;
  }
  cbxx_target._() : super();
  factory cbxx_target.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cbxx_target.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cbxx_target', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pPS(1, _omitFieldNames ? '' : 'targetGroud')
    ..pPS(2, _omitFieldNames ? '' : 'targetDevice')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cbxx_target clone() => cbxx_target()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cbxx_target copyWith(void Function(cbxx_target) updates) => super.copyWith((message) => updates(message as cbxx_target)) as cbxx_target;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cbxx_target create() => cbxx_target._();
  cbxx_target createEmptyInstance() => create();
  static $pb.PbList<cbxx_target> createRepeated() => $pb.PbList<cbxx_target>();
  @$core.pragma('dart2js:noInline')
  static cbxx_target getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cbxx_target>(create);
  static cbxx_target? _defaultInstance;

  /// 目标群组列表,群组dmrid
  @$pb.TagNumber(1)
  $core.List<$core.String> get targetGroud => $_getList(0);

  /// 目标设备列表,设备dmrid
  @$pb.TagNumber(2)
  $core.List<$core.String> get targetDevice => $_getList(1);
}

class cb01 extends $pb.GeneratedMessage {
  factory cb01({
    cbxx_target? target,
    $core.int? time,
    $core.int? size,
    $core.int? count,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (time != null) {
      $result.time = time;
    }
    if (size != null) {
      $result.size = size;
    }
    if (count != null) {
      $result.count = count;
    }
    return $result;
  }
  cb01._() : super();
  factory cb01.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb01.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb01', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'size', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'count', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb01 clone() => cb01()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb01 copyWith(void Function(cb01) updates) => super.copyWith((message) => updates(message as cb01)) as cb01;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb01 create() => cb01._();
  cb01 createEmptyInstance() => create();
  static $pb.PbList<cb01> createRepeated() => $pb.PbList<cb01>();
  @$core.pragma('dart2js:noInline')
  static cb01 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb01>(create);
  static cb01? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get time => $_getIZ(1);
  @$pb.TagNumber(2)
  set time($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTime() => $_has(1);
  @$pb.TagNumber(2)
  void clearTime() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get size => $_getIZ(2);
  @$pb.TagNumber(3)
  set size($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSize() => $_has(2);
  @$pb.TagNumber(3)
  void clearSize() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get count => $_getIZ(3);
  @$pb.TagNumber(4)
  set count($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCount() => $_has(3);
  @$pb.TagNumber(4)
  void clearCount() => clearField(4);
}

class cb02 extends $pb.GeneratedMessage {
  factory cb02({
    cbxx_target? target,
    $core.int? yN,
    $core.int? time,
    $core.int? size,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (time != null) {
      $result.time = time;
    }
    if (size != null) {
      $result.size = size;
    }
    return $result;
  }
  cb02._() : super();
  factory cb02.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb02.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb02', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'size', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb02 clone() => cb02()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb02 copyWith(void Function(cb02) updates) => super.copyWith((message) => updates(message as cb02)) as cb02;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb02 create() => cb02._();
  cb02 createEmptyInstance() => create();
  static $pb.PbList<cb02> createRepeated() => $pb.PbList<cb02>();
  @$core.pragma('dart2js:noInline')
  static cb02 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb02>(create);
  static cb02? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get time => $_getIZ(2);
  @$pb.TagNumber(4)
  set time($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasTime() => $_has(2);
  @$pb.TagNumber(4)
  void clearTime() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get size => $_getIZ(3);
  @$pb.TagNumber(5)
  set size($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasSize() => $_has(3);
  @$pb.TagNumber(5)
  void clearSize() => clearField(5);
}

class cb03 extends $pb.GeneratedMessage {
  factory cb03({
    cbxx_target? target,
    $core.double? minLat,
    $core.double? minLon,
    $core.double? maxLat,
    $core.double? maxLon,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (minLat != null) {
      $result.minLat = minLat;
    }
    if (minLon != null) {
      $result.minLon = minLon;
    }
    if (maxLat != null) {
      $result.maxLat = maxLat;
    }
    if (maxLon != null) {
      $result.maxLon = maxLon;
    }
    return $result;
  }
  cb03._() : super();
  factory cb03.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb03.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb03', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.double>(7, _omitFieldNames ? '' : 'minLat', $pb.PbFieldType.OD)
    ..a<$core.double>(8, _omitFieldNames ? '' : 'minLon', $pb.PbFieldType.OD)
    ..a<$core.double>(9, _omitFieldNames ? '' : 'maxLat', $pb.PbFieldType.OD)
    ..a<$core.double>(10, _omitFieldNames ? '' : 'maxLon', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb03 clone() => cb03()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb03 copyWith(void Function(cb03) updates) => super.copyWith((message) => updates(message as cb03)) as cb03;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb03 create() => cb03._();
  cb03 createEmptyInstance() => create();
  static $pb.PbList<cb03> createRepeated() => $pb.PbList<cb03>();
  @$core.pragma('dart2js:noInline')
  static cb03 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb03>(create);
  static cb03? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(7)
  $core.double get minLat => $_getN(1);
  @$pb.TagNumber(7)
  set minLat($core.double v) { $_setDouble(1, v); }
  @$pb.TagNumber(7)
  $core.bool hasMinLat() => $_has(1);
  @$pb.TagNumber(7)
  void clearMinLat() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get minLon => $_getN(2);
  @$pb.TagNumber(8)
  set minLon($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(8)
  $core.bool hasMinLon() => $_has(2);
  @$pb.TagNumber(8)
  void clearMinLon() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get maxLat => $_getN(3);
  @$pb.TagNumber(9)
  set maxLat($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(9)
  $core.bool hasMaxLat() => $_has(3);
  @$pb.TagNumber(9)
  void clearMaxLat() => clearField(9);

  @$pb.TagNumber(10)
  $core.double get maxLon => $_getN(4);
  @$pb.TagNumber(10)
  set maxLon($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(10)
  $core.bool hasMaxLon() => $_has(4);
  @$pb.TagNumber(10)
  void clearMaxLon() => clearField(10);
}

class cb04 extends $pb.GeneratedMessage {
  factory cb04({
    cbxx_target? target,
    $core.int? yN,
    $core.int? penN,
    $core.int? time,
    $core.double? minLat,
    $core.double? minLon,
    $core.double? maxLat,
    $core.double? maxLon,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (penN != null) {
      $result.penN = penN;
    }
    if (time != null) {
      $result.time = time;
    }
    if (minLat != null) {
      $result.minLat = minLat;
    }
    if (minLon != null) {
      $result.minLon = minLon;
    }
    if (maxLat != null) {
      $result.maxLat = maxLat;
    }
    if (maxLon != null) {
      $result.maxLon = maxLon;
    }
    return $result;
  }
  cb04._() : super();
  factory cb04.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb04.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb04', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'penN', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.double>(7, _omitFieldNames ? '' : 'minLat', $pb.PbFieldType.OD)
    ..a<$core.double>(8, _omitFieldNames ? '' : 'minLon', $pb.PbFieldType.OD)
    ..a<$core.double>(9, _omitFieldNames ? '' : 'maxLat', $pb.PbFieldType.OD)
    ..a<$core.double>(10, _omitFieldNames ? '' : 'maxLon', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb04 clone() => cb04()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb04 copyWith(void Function(cb04) updates) => super.copyWith((message) => updates(message as cb04)) as cb04;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb04 create() => cb04._();
  cb04 createEmptyInstance() => create();
  static $pb.PbList<cb04> createRepeated() => $pb.PbList<cb04>();
  @$core.pragma('dart2js:noInline')
  static cb04 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb04>(create);
  static cb04? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(4)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(4)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(4)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(4)
  void clearYN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get penN => $_getIZ(2);
  @$pb.TagNumber(5)
  set penN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasPenN() => $_has(2);
  @$pb.TagNumber(5)
  void clearPenN() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get time => $_getIZ(3);
  @$pb.TagNumber(6)
  set time($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasTime() => $_has(3);
  @$pb.TagNumber(6)
  void clearTime() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get minLat => $_getN(4);
  @$pb.TagNumber(7)
  set minLat($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(7)
  $core.bool hasMinLat() => $_has(4);
  @$pb.TagNumber(7)
  void clearMinLat() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get minLon => $_getN(5);
  @$pb.TagNumber(8)
  set minLon($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(8)
  $core.bool hasMinLon() => $_has(5);
  @$pb.TagNumber(8)
  void clearMinLon() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get maxLat => $_getN(6);
  @$pb.TagNumber(9)
  set maxLat($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(9)
  $core.bool hasMaxLat() => $_has(6);
  @$pb.TagNumber(9)
  void clearMaxLat() => clearField(9);

  @$pb.TagNumber(10)
  $core.double get maxLon => $_getN(7);
  @$pb.TagNumber(10)
  set maxLon($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(10)
  $core.bool hasMaxLon() => $_has(7);
  @$pb.TagNumber(10)
  void clearMaxLon() => clearField(10);
}

class cb05 extends $pb.GeneratedMessage {
  factory cb05({
    cbxx_target? target,
    $core.int? yN,
    $core.int? time,
    $core.double? lat,
    $core.double? lon,
    $core.String? latDif,
    $core.String? lonDif,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (time != null) {
      $result.time = time;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (latDif != null) {
      $result.latDif = latDif;
    }
    if (lonDif != null) {
      $result.lonDif = lonDif;
    }
    return $result;
  }
  cb05._() : super();
  factory cb05.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb05.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb05', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.double>(6, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(7, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..aOS(8, _omitFieldNames ? '' : 'latDif')
    ..aOS(9, _omitFieldNames ? '' : 'lonDif')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb05 clone() => cb05()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb05 copyWith(void Function(cb05) updates) => super.copyWith((message) => updates(message as cb05)) as cb05;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb05 create() => cb05._();
  cb05 createEmptyInstance() => create();
  static $pb.PbList<cb05> createRepeated() => $pb.PbList<cb05>();
  @$core.pragma('dart2js:noInline')
  static cb05 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb05>(create);
  static cb05? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(4)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(4)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(4)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(4)
  void clearYN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get time => $_getIZ(2);
  @$pb.TagNumber(5)
  set time($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasTime() => $_has(2);
  @$pb.TagNumber(5)
  void clearTime() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get lat => $_getN(3);
  @$pb.TagNumber(6)
  set lat($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasLat() => $_has(3);
  @$pb.TagNumber(6)
  void clearLat() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get lon => $_getN(4);
  @$pb.TagNumber(7)
  set lon($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(7)
  $core.bool hasLon() => $_has(4);
  @$pb.TagNumber(7)
  void clearLon() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get latDif => $_getSZ(5);
  @$pb.TagNumber(8)
  set latDif($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(8)
  $core.bool hasLatDif() => $_has(5);
  @$pb.TagNumber(8)
  void clearLatDif() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get lonDif => $_getSZ(6);
  @$pb.TagNumber(9)
  set lonDif($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(9)
  $core.bool hasLonDif() => $_has(6);
  @$pb.TagNumber(9)
  void clearLonDif() => clearField(9);
}

class cb06 extends $pb.GeneratedMessage {
  factory cb06({
    cbxx_target? target,
    $core.int? yN,
    $core.int? time,
    $core.String? latDif,
    $core.String? lonDif,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (time != null) {
      $result.time = time;
    }
    if (latDif != null) {
      $result.latDif = latDif;
    }
    if (lonDif != null) {
      $result.lonDif = lonDif;
    }
    return $result;
  }
  cb06._() : super();
  factory cb06.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb06.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb06', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..aOS(8, _omitFieldNames ? '' : 'latDif')
    ..aOS(9, _omitFieldNames ? '' : 'lonDif')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb06 clone() => cb06()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb06 copyWith(void Function(cb06) updates) => super.copyWith((message) => updates(message as cb06)) as cb06;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb06 create() => cb06._();
  cb06 createEmptyInstance() => create();
  static $pb.PbList<cb06> createRepeated() => $pb.PbList<cb06>();
  @$core.pragma('dart2js:noInline')
  static cb06 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb06>(create);
  static cb06? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(4)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(4)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(4)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(4)
  void clearYN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get time => $_getIZ(2);
  @$pb.TagNumber(5)
  set time($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(5)
  $core.bool hasTime() => $_has(2);
  @$pb.TagNumber(5)
  void clearTime() => clearField(5);

  @$pb.TagNumber(8)
  $core.String get latDif => $_getSZ(3);
  @$pb.TagNumber(8)
  set latDif($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(8)
  $core.bool hasLatDif() => $_has(3);
  @$pb.TagNumber(8)
  void clearLatDif() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get lonDif => $_getSZ(4);
  @$pb.TagNumber(9)
  set lonDif($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(9)
  $core.bool hasLonDif() => $_has(4);
  @$pb.TagNumber(9)
  void clearLonDif() => clearField(9);
}

class cb07 extends $pb.GeneratedMessage {
  factory cb07({
    cbxx_target? target,
    $core.int? yN,
    $core.int? jtTime,
    $core.int? dwTime,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (jtTime != null) {
      $result.jtTime = jtTime;
    }
    if (dwTime != null) {
      $result.dwTime = dwTime;
    }
    return $result;
  }
  cb07._() : super();
  factory cb07.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb07.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb07', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'jtTime', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'dwTime', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb07 clone() => cb07()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb07 copyWith(void Function(cb07) updates) => super.copyWith((message) => updates(message as cb07)) as cb07;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb07 create() => cb07._();
  cb07 createEmptyInstance() => create();
  static $pb.PbList<cb07> createRepeated() => $pb.PbList<cb07>();
  @$core.pragma('dart2js:noInline')
  static cb07 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb07>(create);
  static cb07? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get jtTime => $_getIZ(2);
  @$pb.TagNumber(4)
  set jtTime($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasJtTime() => $_has(2);
  @$pb.TagNumber(4)
  void clearJtTime() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get dwTime => $_getIZ(3);
  @$pb.TagNumber(5)
  set dwTime($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasDwTime() => $_has(3);
  @$pb.TagNumber(5)
  void clearDwTime() => clearField(5);
}

class cb08 extends $pb.GeneratedMessage {
  factory cb08({
    cbxx_target? target,
    $core.String? src,
    $core.int? yN,
    $core.int? jtCh,
    $core.int? time,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (src != null) {
      $result.src = src;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (jtCh != null) {
      $result.jtCh = jtCh;
    }
    if (time != null) {
      $result.time = time;
    }
    return $result;
  }
  cb08._() : super();
  factory cb08.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb08.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb08', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..aOS(2, _omitFieldNames ? '' : 'src')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'jtCh', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb08 clone() => cb08()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb08 copyWith(void Function(cb08) updates) => super.copyWith((message) => updates(message as cb08)) as cb08;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb08 create() => cb08._();
  cb08 createEmptyInstance() => create();
  static $pb.PbList<cb08> createRepeated() => $pb.PbList<cb08>();
  @$core.pragma('dart2js:noInline')
  static cb08 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb08>(create);
  static cb08? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.String get src => $_getSZ(1);
  @$pb.TagNumber(2)
  set src($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSrc() => $_has(1);
  @$pb.TagNumber(2)
  void clearSrc() => clearField(2);

  @$pb.TagNumber(4)
  $core.int get yN => $_getIZ(2);
  @$pb.TagNumber(4)
  set yN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasYN() => $_has(2);
  @$pb.TagNumber(4)
  void clearYN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get jtCh => $_getIZ(3);
  @$pb.TagNumber(5)
  set jtCh($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasJtCh() => $_has(3);
  @$pb.TagNumber(5)
  void clearJtCh() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get time => $_getIZ(4);
  @$pb.TagNumber(6)
  set time($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasTime() => $_has(4);
  @$pb.TagNumber(6)
  void clearTime() => clearField(6);
}

class cb09 extends $pb.GeneratedMessage {
  factory cb09({
    cbxx_target? target,
    $core.int? yN,
    $core.int? st,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (st != null) {
      $result.st = st;
    }
    return $result;
  }
  cb09._() : super();
  factory cb09.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb09.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb09', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'st', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb09 clone() => cb09()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb09 copyWith(void Function(cb09) updates) => super.copyWith((message) => updates(message as cb09)) as cb09;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb09 create() => cb09._();
  cb09 createEmptyInstance() => create();
  static $pb.PbList<cb09> createRepeated() => $pb.PbList<cb09>();
  @$core.pragma('dart2js:noInline')
  static cb09 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb09>(create);
  static cb09? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get st => $_getIZ(2);
  @$pb.TagNumber(4)
  set st($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasSt() => $_has(2);
  @$pb.TagNumber(4)
  void clearSt() => clearField(4);
}

class cb10 extends $pb.GeneratedMessage {
  factory cb10({
    cbxx_target? target,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    return $result;
  }
  cb10._() : super();
  factory cb10.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb10.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb10', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb10 clone() => cb10()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb10 copyWith(void Function(cb10) updates) => super.copyWith((message) => updates(message as cb10)) as cb10;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb10 create() => cb10._();
  cb10 createEmptyInstance() => create();
  static $pb.PbList<cb10> createRepeated() => $pb.PbList<cb10>();
  @$core.pragma('dart2js:noInline')
  static cb10 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb10>(create);
  static cb10? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);
}

class cb11 extends $pb.GeneratedMessage {
  factory cb11({
    cbxx_target? target,
    $core.int? yN,
    $core.List<$core.int>? cTp,
    $core.int? aTp,
    $core.int? ddCh,
    $core.String? initiator,
    $core.List<$core.int>? origCmd,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (cTp != null) {
      $result.cTp = cTp;
    }
    if (aTp != null) {
      $result.aTp = aTp;
    }
    if (ddCh != null) {
      $result.ddCh = ddCh;
    }
    if (initiator != null) {
      $result.initiator = initiator;
    }
    if (origCmd != null) {
      $result.origCmd = origCmd;
    }
    return $result;
  }
  cb11._() : super();
  factory cb11.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb11.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb11', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'cTp', $pb.PbFieldType.OY)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'aTp', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'ddCh', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'initiator')
    ..a<$core.List<$core.int>>(7, _omitFieldNames ? '' : 'origCmd', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb11 clone() => cb11()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb11 copyWith(void Function(cb11) updates) => super.copyWith((message) => updates(message as cb11)) as cb11;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb11 create() => cb11._();
  cb11 createEmptyInstance() => create();
  static $pb.PbList<cb11> createRepeated() => $pb.PbList<cb11>();
  @$core.pragma('dart2js:noInline')
  static cb11 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb11>(create);
  static cb11? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.int> get cTp => $_getN(2);
  @$pb.TagNumber(3)
  set cTp($core.List<$core.int> v) { $_setBytes(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCTp() => $_has(2);
  @$pb.TagNumber(3)
  void clearCTp() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get aTp => $_getIZ(3);
  @$pb.TagNumber(4)
  set aTp($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasATp() => $_has(3);
  @$pb.TagNumber(4)
  void clearATp() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get ddCh => $_getIZ(4);
  @$pb.TagNumber(5)
  set ddCh($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDdCh() => $_has(4);
  @$pb.TagNumber(5)
  void clearDdCh() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get initiator => $_getSZ(5);
  @$pb.TagNumber(6)
  set initiator($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasInitiator() => $_has(5);
  @$pb.TagNumber(6)
  void clearInitiator() => clearField(6);

  @$pb.TagNumber(7)
  $core.List<$core.int> get origCmd => $_getN(6);
  @$pb.TagNumber(7)
  set origCmd($core.List<$core.int> v) { $_setBytes(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasOrigCmd() => $_has(6);
  @$pb.TagNumber(7)
  void clearOrigCmd() => clearField(7);
}

class cb12 extends $pb.GeneratedMessage {
  factory cb12({
    cbxx_target? target,
    $core.int? type,
    $core.int? bsId,
    $core.int? bsN,
    $core.int? chId,
    $core.int? chN,
    $core.String? conStatus,
    $core.int? myxbTT,
    $core.String? cBsN,
    $core.int? chDdSt,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (type != null) {
      $result.type = type;
    }
    if (bsId != null) {
      $result.bsId = bsId;
    }
    if (bsN != null) {
      $result.bsN = bsN;
    }
    if (chId != null) {
      $result.chId = chId;
    }
    if (chN != null) {
      $result.chN = chN;
    }
    if (conStatus != null) {
      $result.conStatus = conStatus;
    }
    if (myxbTT != null) {
      $result.myxbTT = myxbTT;
    }
    if (cBsN != null) {
      $result.cBsN = cBsN;
    }
    if (chDdSt != null) {
      $result.chDdSt = chDdSt;
    }
    return $result;
  }
  cb12._() : super();
  factory cb12.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb12.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb12', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'type', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'bsId', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'bsN', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'chId', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'chN', $pb.PbFieldType.O3)
    ..aOS(7, _omitFieldNames ? '' : 'conStatus')
    ..a<$core.int>(8, _omitFieldNames ? '' : 'myxbTT', $pb.PbFieldType.O3, protoName: 'myxb_TT')
    ..aOS(9, _omitFieldNames ? '' : 'cBsN')
    ..a<$core.int>(10, _omitFieldNames ? '' : 'chDdSt', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb12 clone() => cb12()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb12 copyWith(void Function(cb12) updates) => super.copyWith((message) => updates(message as cb12)) as cb12;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb12 create() => cb12._();
  cb12 createEmptyInstance() => create();
  static $pb.PbList<cb12> createRepeated() => $pb.PbList<cb12>();
  @$core.pragma('dart2js:noInline')
  static cb12 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb12>(create);
  static cb12? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get type => $_getIZ(1);
  @$pb.TagNumber(2)
  set type($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasType() => $_has(1);
  @$pb.TagNumber(2)
  void clearType() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get bsId => $_getIZ(2);
  @$pb.TagNumber(3)
  set bsId($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasBsId() => $_has(2);
  @$pb.TagNumber(3)
  void clearBsId() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get bsN => $_getIZ(3);
  @$pb.TagNumber(4)
  set bsN($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasBsN() => $_has(3);
  @$pb.TagNumber(4)
  void clearBsN() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get chId => $_getIZ(4);
  @$pb.TagNumber(5)
  set chId($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasChId() => $_has(4);
  @$pb.TagNumber(5)
  void clearChId() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get chN => $_getIZ(5);
  @$pb.TagNumber(6)
  set chN($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasChN() => $_has(5);
  @$pb.TagNumber(6)
  void clearChN() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get conStatus => $_getSZ(6);
  @$pb.TagNumber(7)
  set conStatus($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasConStatus() => $_has(6);
  @$pb.TagNumber(7)
  void clearConStatus() => clearField(7);

  @$pb.TagNumber(8)
  $core.int get myxbTT => $_getIZ(7);
  @$pb.TagNumber(8)
  set myxbTT($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasMyxbTT() => $_has(7);
  @$pb.TagNumber(8)
  void clearMyxbTT() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get cBsN => $_getSZ(8);
  @$pb.TagNumber(9)
  set cBsN($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasCBsN() => $_has(8);
  @$pb.TagNumber(9)
  void clearCBsN() => clearField(9);

  @$pb.TagNumber(10)
  $core.int get chDdSt => $_getIZ(9);
  @$pb.TagNumber(10)
  set chDdSt($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasChDdSt() => $_has(9);
  @$pb.TagNumber(10)
  void clearChDdSt() => clearField(10);
}

class cb17 extends $pb.GeneratedMessage {
  factory cb17({
    cbxx_target? target,
    $core.int? yN,
    $core.String? userSt,
    $core.int? userTp,
    $core.String? userAddrId,
    $core.String? license,
    $core.String? userShowId,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (userSt != null) {
      $result.userSt = userSt;
    }
    if (userTp != null) {
      $result.userTp = userTp;
    }
    if (userAddrId != null) {
      $result.userAddrId = userAddrId;
    }
    if (license != null) {
      $result.license = license;
    }
    if (userShowId != null) {
      $result.userShowId = userShowId;
    }
    return $result;
  }
  cb17._() : super();
  factory cb17.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb17.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb17', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..aOS(3, _omitFieldNames ? '' : 'userSt')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'userTp', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'userAddrId')
    ..aOS(6, _omitFieldNames ? '' : 'license')
    ..aOS(7, _omitFieldNames ? '' : 'userShowId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb17 clone() => cb17()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb17 copyWith(void Function(cb17) updates) => super.copyWith((message) => updates(message as cb17)) as cb17;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb17 create() => cb17._();
  cb17 createEmptyInstance() => create();
  static $pb.PbList<cb17> createRepeated() => $pb.PbList<cb17>();
  @$core.pragma('dart2js:noInline')
  static cb17 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb17>(create);
  static cb17? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get userSt => $_getSZ(2);
  @$pb.TagNumber(3)
  set userSt($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUserSt() => $_has(2);
  @$pb.TagNumber(3)
  void clearUserSt() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get userTp => $_getIZ(3);
  @$pb.TagNumber(4)
  set userTp($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasUserTp() => $_has(3);
  @$pb.TagNumber(4)
  void clearUserTp() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get userAddrId => $_getSZ(4);
  @$pb.TagNumber(5)
  set userAddrId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasUserAddrId() => $_has(4);
  @$pb.TagNumber(5)
  void clearUserAddrId() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get license => $_getSZ(5);
  @$pb.TagNumber(6)
  set license($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLicense() => $_has(5);
  @$pb.TagNumber(6)
  void clearLicense() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get userShowId => $_getSZ(6);
  @$pb.TagNumber(7)
  set userShowId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasUserShowId() => $_has(6);
  @$pb.TagNumber(7)
  void clearUserShowId() => clearField(7);
}

class cb19 extends $pb.GeneratedMessage {
  factory cb19({
    cbxx_target? target,
    $core.int? type,
    $core.String? newDuty,
    $core.String? oldDuty,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (type != null) {
      $result.type = type;
    }
    if (newDuty != null) {
      $result.newDuty = newDuty;
    }
    if (oldDuty != null) {
      $result.oldDuty = oldDuty;
    }
    return $result;
  }
  cb19._() : super();
  factory cb19.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb19.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb19', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'type', $pb.PbFieldType.O3)
    ..aOS(3, _omitFieldNames ? '' : 'newDuty')
    ..aOS(4, _omitFieldNames ? '' : 'oldDuty')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb19 clone() => cb19()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb19 copyWith(void Function(cb19) updates) => super.copyWith((message) => updates(message as cb19)) as cb19;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb19 create() => cb19._();
  cb19 createEmptyInstance() => create();
  static $pb.PbList<cb19> createRepeated() => $pb.PbList<cb19>();
  @$core.pragma('dart2js:noInline')
  static cb19 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb19>(create);
  static cb19? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get type => $_getIZ(1);
  @$pb.TagNumber(2)
  set type($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasType() => $_has(1);
  @$pb.TagNumber(2)
  void clearType() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get newDuty => $_getSZ(2);
  @$pb.TagNumber(3)
  set newDuty($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasNewDuty() => $_has(2);
  @$pb.TagNumber(3)
  void clearNewDuty() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get oldDuty => $_getSZ(3);
  @$pb.TagNumber(4)
  set oldDuty($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasOldDuty() => $_has(3);
  @$pb.TagNumber(4)
  void clearOldDuty() => clearField(4);
}

class cb20 extends $pb.GeneratedMessage {
  factory cb20({
    cbxx_target? target,
    $core.int? yN,
    $core.int? linkTp,
    $core.int? aTp,
    $core.String? netId,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (linkTp != null) {
      $result.linkTp = linkTp;
    }
    if (aTp != null) {
      $result.aTp = aTp;
    }
    if (netId != null) {
      $result.netId = netId;
    }
    return $result;
  }
  cb20._() : super();
  factory cb20.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb20.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb20', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'linkTp', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'aTp', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'netId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb20 clone() => cb20()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb20 copyWith(void Function(cb20) updates) => super.copyWith((message) => updates(message as cb20)) as cb20;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb20 create() => cb20._();
  cb20 createEmptyInstance() => create();
  static $pb.PbList<cb20> createRepeated() => $pb.PbList<cb20>();
  @$core.pragma('dart2js:noInline')
  static cb20 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb20>(create);
  static cb20? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get linkTp => $_getIZ(2);
  @$pb.TagNumber(4)
  set linkTp($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasLinkTp() => $_has(2);
  @$pb.TagNumber(4)
  void clearLinkTp() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get aTp => $_getIZ(3);
  @$pb.TagNumber(5)
  set aTp($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasATp() => $_has(3);
  @$pb.TagNumber(5)
  void clearATp() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get netId => $_getSZ(4);
  @$pb.TagNumber(6)
  set netId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasNetId() => $_has(4);
  @$pb.TagNumber(6)
  void clearNetId() => clearField(6);
}

class cb21 extends $pb.GeneratedMessage {
  factory cb21({
    cbxx_target? target,
    $core.int? yN,
    $core.int? ddCh,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (ddCh != null) {
      $result.ddCh = ddCh;
    }
    return $result;
  }
  cb21._() : super();
  factory cb21.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb21.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb21', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'ddCh', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb21 clone() => cb21()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb21 copyWith(void Function(cb21) updates) => super.copyWith((message) => updates(message as cb21)) as cb21;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb21 create() => cb21._();
  cb21 createEmptyInstance() => create();
  static $pb.PbList<cb21> createRepeated() => $pb.PbList<cb21>();
  @$core.pragma('dart2js:noInline')
  static cb21 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb21>(create);
  static cb21? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(3)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(3)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(3)
  void clearYN() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get ddCh => $_getIZ(2);
  @$pb.TagNumber(4)
  set ddCh($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasDdCh() => $_has(2);
  @$pb.TagNumber(4)
  void clearDdCh() => clearField(4);
}

class cb24 extends $pb.GeneratedMessage {
  factory cb24({
    cbxx_target? target,
    $core.int? yN,
    $core.String? data,
    $core.int? codeTp,
    $core.String? scheduleSendTime,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (data != null) {
      $result.data = data;
    }
    if (codeTp != null) {
      $result.codeTp = codeTp;
    }
    if (scheduleSendTime != null) {
      $result.scheduleSendTime = scheduleSendTime;
    }
    return $result;
  }
  cb24._() : super();
  factory cb24.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb24.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb24', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..aOS(3, _omitFieldNames ? '' : 'data')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'codeTp', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'scheduleSendTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb24 clone() => cb24()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb24 copyWith(void Function(cb24) updates) => super.copyWith((message) => updates(message as cb24)) as cb24;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb24 create() => cb24._();
  cb24 createEmptyInstance() => create();
  static $pb.PbList<cb24> createRepeated() => $pb.PbList<cb24>();
  @$core.pragma('dart2js:noInline')
  static cb24 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb24>(create);
  static cb24? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get data => $_getSZ(2);
  @$pb.TagNumber(3)
  set data($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasData() => $_has(2);
  @$pb.TagNumber(3)
  void clearData() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get codeTp => $_getIZ(3);
  @$pb.TagNumber(4)
  set codeTp($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCodeTp() => $_has(3);
  @$pb.TagNumber(4)
  void clearCodeTp() => clearField(4);

  /// 预定发送时间(utc),如果是立即发送,填空字符串
  @$pb.TagNumber(5)
  $core.String get scheduleSendTime => $_getSZ(4);
  @$pb.TagNumber(5)
  set scheduleSendTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasScheduleSendTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearScheduleSendTime() => clearField(5);
}

class cb25 extends $pb.GeneratedMessage {
  factory cb25({
    cbxx_target? target,
    $core.int? yN,
    $core.int? pointN,
    $core.String? pointCard,
    $core.double? lat,
    $core.double? lon,
    $core.String? latDif,
    $core.String? lonDif,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (pointN != null) {
      $result.pointN = pointN;
    }
    if (pointCard != null) {
      $result.pointCard = pointCard;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (latDif != null) {
      $result.latDif = latDif;
    }
    if (lonDif != null) {
      $result.lonDif = lonDif;
    }
    return $result;
  }
  cb25._() : super();
  factory cb25.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb25.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb25', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'pointN', $pb.PbFieldType.O3)
    ..aOS(4, _omitFieldNames ? '' : 'pointCard')
    ..a<$core.double>(5, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(6, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..aOS(7, _omitFieldNames ? '' : 'latDif')
    ..aOS(8, _omitFieldNames ? '' : 'lonDif')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb25 clone() => cb25()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb25 copyWith(void Function(cb25) updates) => super.copyWith((message) => updates(message as cb25)) as cb25;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb25 create() => cb25._();
  cb25 createEmptyInstance() => create();
  static $pb.PbList<cb25> createRepeated() => $pb.PbList<cb25>();
  @$core.pragma('dart2js:noInline')
  static cb25 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb25>(create);
  static cb25? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get pointN => $_getIZ(2);
  @$pb.TagNumber(3)
  set pointN($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasPointN() => $_has(2);
  @$pb.TagNumber(3)
  void clearPointN() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get pointCard => $_getSZ(3);
  @$pb.TagNumber(4)
  set pointCard($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPointCard() => $_has(3);
  @$pb.TagNumber(4)
  void clearPointCard() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get lat => $_getN(4);
  @$pb.TagNumber(5)
  set lat($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLat() => $_has(4);
  @$pb.TagNumber(5)
  void clearLat() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get lon => $_getN(5);
  @$pb.TagNumber(6)
  set lon($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLon() => $_has(5);
  @$pb.TagNumber(6)
  void clearLon() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get latDif => $_getSZ(6);
  @$pb.TagNumber(7)
  set latDif($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLatDif() => $_has(6);
  @$pb.TagNumber(7)
  void clearLatDif() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get lonDif => $_getSZ(7);
  @$pb.TagNumber(8)
  set lonDif($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLonDif() => $_has(7);
  @$pb.TagNumber(8)
  void clearLonDif() => clearField(8);
}

class cb26 extends $pb.GeneratedMessage {
  factory cb26({
    cbxx_target? target,
    $core.int? yN,
    $core.int? ch,
    $core.String? chName,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (yN != null) {
      $result.yN = yN;
    }
    if (ch != null) {
      $result.ch = ch;
    }
    if (chName != null) {
      $result.chName = chName;
    }
    return $result;
  }
  cb26._() : super();
  factory cb26.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb26.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb26', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'yN', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'ch', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'chName')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb26 clone() => cb26()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb26 copyWith(void Function(cb26) updates) => super.copyWith((message) => updates(message as cb26)) as cb26;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb26 create() => cb26._();
  cb26 createEmptyInstance() => create();
  static $pb.PbList<cb26> createRepeated() => $pb.PbList<cb26>();
  @$core.pragma('dart2js:noInline')
  static cb26 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb26>(create);
  static cb26? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.int get yN => $_getIZ(1);
  @$pb.TagNumber(2)
  set yN($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasYN() => $_has(1);
  @$pb.TagNumber(2)
  void clearYN() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get ch => $_getIZ(2);
  @$pb.TagNumber(3)
  set ch($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCh() => $_has(2);
  @$pb.TagNumber(3)
  void clearCh() => clearField(3);

  @$pb.TagNumber(5)
  $core.String get chName => $_getSZ(3);
  @$pb.TagNumber(5)
  set chName($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasChName() => $_has(3);
  @$pb.TagNumber(5)
  void clearChName() => clearField(5);
}

class cb42 extends $pb.GeneratedMessage {
  factory cb42({
    cbxx_target? target,
    $core.int? code,
  }) {
    final $result = create();
    if (target != null) {
      $result.target = target;
    }
    if (code != null) {
      $result.code = code;
    }
    return $result;
  }
  cb42._() : super();
  factory cb42.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cb42.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cb42', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOM<cbxx_target>(1, _omitFieldNames ? '' : 'target', subBuilder: cbxx_target.create)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'code', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cb42 clone() => cb42()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cb42 copyWith(void Function(cb42) updates) => super.copyWith((message) => updates(message as cb42)) as cb42;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cb42 create() => cb42._();
  cb42 createEmptyInstance() => create();
  static $pb.PbList<cb42> createRepeated() => $pb.PbList<cb42>();
  @$core.pragma('dart2js:noInline')
  static cb42 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cb42>(create);
  static cb42? _defaultInstance;

  @$pb.TagNumber(1)
  cbxx_target get target => $_getN(0);
  @$pb.TagNumber(1)
  set target(cbxx_target v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTarget() => $_has(0);
  @$pb.TagNumber(1)
  void clearTarget() => clearField(1);
  @$pb.TagNumber(1)
  cbxx_target ensureTarget() => $_ensure(0);

  /// code [uint8]: 10:关闭 11:开启 12:查询
  @$pb.TagNumber(2)
  $core.int get code => $_getIZ(1);
  @$pb.TagNumber(2)
  set code($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearCode() => clearField(2);
}

/// 客户端调用cbxx命令时
/// 服务器返回发送的命令和序号,以便客户端识别手台回应命令
class cbxx_send_stub extends $pb.GeneratedMessage {
  factory cbxx_send_stub({
    $core.String? cbxx,
    $core.Iterable<$core.String>? targetGroup,
    $core.Iterable<$core.String>? targetGroupSeqNo,
    $core.Iterable<$core.String>? targetDevice,
    $core.Iterable<$core.String>? targetDeviceSeqNo,
  }) {
    final $result = create();
    if (cbxx != null) {
      $result.cbxx = cbxx;
    }
    if (targetGroup != null) {
      $result.targetGroup.addAll(targetGroup);
    }
    if (targetGroupSeqNo != null) {
      $result.targetGroupSeqNo.addAll(targetGroupSeqNo);
    }
    if (targetDevice != null) {
      $result.targetDevice.addAll(targetDevice);
    }
    if (targetDeviceSeqNo != null) {
      $result.targetDeviceSeqNo.addAll(targetDeviceSeqNo);
    }
    return $result;
  }
  cbxx_send_stub._() : super();
  factory cbxx_send_stub.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cbxx_send_stub.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cbxx_send_stub', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'cbxx')
    ..pPS(2, _omitFieldNames ? '' : 'targetGroup')
    ..pPS(3, _omitFieldNames ? '' : 'targetGroupSeqNo')
    ..pPS(4, _omitFieldNames ? '' : 'targetDevice')
    ..pPS(5, _omitFieldNames ? '' : 'targetDeviceSeqNo')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cbxx_send_stub clone() => cbxx_send_stub()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cbxx_send_stub copyWith(void Function(cbxx_send_stub) updates) => super.copyWith((message) => updates(message as cbxx_send_stub)) as cbxx_send_stub;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cbxx_send_stub create() => cbxx_send_stub._();
  cbxx_send_stub createEmptyInstance() => create();
  static $pb.PbList<cbxx_send_stub> createRepeated() => $pb.PbList<cbxx_send_stub>();
  @$core.pragma('dart2js:noInline')
  static cbxx_send_stub getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cbxx_send_stub>(create);
  static cbxx_send_stub? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get cbxx => $_getSZ(0);
  @$pb.TagNumber(1)
  set cbxx($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCbxx() => $_has(0);
  @$pb.TagNumber(1)
  void clearCbxx() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.String> get targetGroup => $_getList(1);

  @$pb.TagNumber(3)
  $core.List<$core.String> get targetGroupSeqNo => $_getList(2);

  @$pb.TagNumber(4)
  $core.List<$core.String> get targetDevice => $_getList(3);

  @$pb.TagNumber(5)
  $core.List<$core.String> get targetDeviceSeqNo => $_getList(4);
}

/// 控制器交互命令
class cc01 extends $pb.GeneratedMessage {
  factory cc01({
    $core.int? actionCode,
    $core.String? dmrId,
    $core.String? ccxxStr,
  }) {
    final $result = create();
    if (actionCode != null) {
      $result.actionCode = actionCode;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (ccxxStr != null) {
      $result.ccxxStr = ccxxStr;
    }
    return $result;
  }
  cc01._() : super();
  factory cc01.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cc01.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cc01', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'actionCode', $pb.PbFieldType.O3)
    ..aOS(2, _omitFieldNames ? '' : 'dmrId')
    ..aOS(3, _omitFieldNames ? '' : 'ccxxStr')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cc01 clone() => cc01()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cc01 copyWith(void Function(cc01) updates) => super.copyWith((message) => updates(message as cc01)) as cc01;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cc01 create() => cc01._();
  cc01 createEmptyInstance() => create();
  static $pb.PbList<cc01> createRepeated() => $pb.PbList<cc01>();
  @$core.pragma('dart2js:noInline')
  static cc01 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cc01>(create);
  static cc01? _defaultInstance;

  /// =80,客户端查询控制器状态，此时dmr_id为逗号分隔的控制器 id列表
  /// =103, cc03
  @$pb.TagNumber(1)
  $core.int get actionCode => $_getIZ(0);
  @$pb.TagNumber(1)
  set actionCode($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasActionCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearActionCode() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get dmrId => $_getSZ(1);
  @$pb.TagNumber(2)
  set dmrId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDmrId() => $_has(1);
  @$pb.TagNumber(2)
  void clearDmrId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get ccxxStr => $_getSZ(2);
  @$pb.TagNumber(3)
  set ccxxStr($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCcxxStr() => $_has(2);
  @$pb.TagNumber(3)
  void clearCcxxStr() => clearField(3);
}

/// 8100广播app请求特定终端的gps数据广播
/// same as req_gps_permission in app_proto.proto
/// rpc.cmd=8181, server to web client
/// kcp rpc.cmd=81, app to server
class cc81 extends $pb.GeneratedMessage {
  factory cc81({
    $core.int? targetDmrId,
    $core.int? applyDmrId,
  }) {
    final $result = create();
    if (targetDmrId != null) {
      $result.targetDmrId = targetDmrId;
    }
    if (applyDmrId != null) {
      $result.applyDmrId = applyDmrId;
    }
    return $result;
  }
  cc81._() : super();
  factory cc81.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cc81.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cc81', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'targetDmrId', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'applyDmrId', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cc81 clone() => cc81()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cc81 copyWith(void Function(cc81) updates) => super.copyWith((message) => updates(message as cc81)) as cc81;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cc81 create() => cc81._();
  cc81 createEmptyInstance() => create();
  static $pb.PbList<cc81> createRepeated() => $pb.PbList<cc81>();
  @$core.pragma('dart2js:noInline')
  static cc81 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cc81>(create);
  static cc81? _defaultInstance;

  /// 目标终端dmr_id
  @$pb.TagNumber(1)
  $core.int get targetDmrId => $_getIZ(0);
  @$pb.TagNumber(1)
  set targetDmrId($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTargetDmrId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTargetDmrId() => clearField(1);

  /// 发起请求的终端dmr_id
  @$pb.TagNumber(2)
  $core.int get applyDmrId => $_getIZ(1);
  @$pb.TagNumber(2)
  set applyDmrId($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasApplyDmrId() => $_has(1);
  @$pb.TagNumber(2)
  void clearApplyDmrId() => clearField(2);
}

/// apply response for device gps info permission
/// kcp rpc.cmd=82, server to app
class res_gps_permission extends $pb.GeneratedMessage {
  factory res_gps_permission({
    $core.int? code,
    $core.int? dmrid,
    $core.String? grantUserRid,
    $core.String? grantUserName,
    $core.String? expireTime,
  }) {
    final $result = create();
    if (code != null) {
      $result.code = code;
    }
    if (dmrid != null) {
      $result.dmrid = dmrid;
    }
    if (grantUserRid != null) {
      $result.grantUserRid = grantUserRid;
    }
    if (grantUserName != null) {
      $result.grantUserName = grantUserName;
    }
    if (expireTime != null) {
      $result.expireTime = expireTime;
    }
    return $result;
  }
  res_gps_permission._() : super();
  factory res_gps_permission.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory res_gps_permission.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'res_gps_permission', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'code', $pb.PbFieldType.O3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'dmrid', $pb.PbFieldType.OU3)
    ..aOS(3, _omitFieldNames ? '' : 'grantUserRid')
    ..aOS(4, _omitFieldNames ? '' : 'grantUserName')
    ..aOS(5, _omitFieldNames ? '' : 'expireTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  res_gps_permission clone() => res_gps_permission()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  res_gps_permission copyWith(void Function(res_gps_permission) updates) => super.copyWith((message) => updates(message as res_gps_permission)) as res_gps_permission;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static res_gps_permission create() => res_gps_permission._();
  res_gps_permission createEmptyInstance() => create();
  static $pb.PbList<res_gps_permission> createRepeated() => $pb.PbList<res_gps_permission>();
  @$core.pragma('dart2js:noInline')
  static res_gps_permission getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<res_gps_permission>(create);
  static res_gps_permission? _defaultInstance;

  /// response code
  /// 0:ok 4:reject
  @$pb.TagNumber(1)
  $core.int get code => $_getIZ(0);
  @$pb.TagNumber(1)
  set code($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearCode() => clearField(1);

  /// device dmrid
  @$pb.TagNumber(2)
  $core.int get dmrid => $_getIZ(1);
  @$pb.TagNumber(2)
  set dmrid($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearDmrid() => clearField(2);

  /// who grant the permission, user rid
  @$pb.TagNumber(3)
  $core.String get grantUserRid => $_getSZ(2);
  @$pb.TagNumber(3)
  set grantUserRid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGrantUserRid() => $_has(2);
  @$pb.TagNumber(3)
  void clearGrantUserRid() => clearField(3);

  /// grant user name
  @$pb.TagNumber(4)
  $core.String get grantUserName => $_getSZ(3);
  @$pb.TagNumber(4)
  set grantUserName($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGrantUserName() => $_has(3);
  @$pb.TagNumber(4)
  void clearGrantUserName() => clearField(4);

  /// permission expire time
  @$pb.TagNumber(5)
  $core.String get expireTime => $_getSZ(4);
  @$pb.TagNumber(5)
  set expireTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasExpireTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearExpireTime() => clearField(5);
}

/// 8100中继状态查询/通知
/// rpc.cmd=83
class cc83 extends $pb.GeneratedMessage {
  factory cc83({
    $core.int? actionCode,
    $core.Iterable<$core.String>? hexDmrids,
    $core.Iterable<$core.String>? models,
    $core.Iterable<$core.String>? functions,
  }) {
    final $result = create();
    if (actionCode != null) {
      $result.actionCode = actionCode;
    }
    if (hexDmrids != null) {
      $result.hexDmrids.addAll(hexDmrids);
    }
    if (models != null) {
      $result.models.addAll(models);
    }
    if (functions != null) {
      $result.functions.addAll(functions);
    }
    return $result;
  }
  cc83._() : super();
  factory cc83.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cc83.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cc83', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'actionCode', $pb.PbFieldType.O3)
    ..pPS(2, _omitFieldNames ? '' : 'hexDmrids')
    ..pPS(3, _omitFieldNames ? '' : 'models')
    ..pPS(4, _omitFieldNames ? '' : 'functions')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cc83 clone() => cc83()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cc83 copyWith(void Function(cc83) updates) => super.copyWith((message) => updates(message as cc83)) as cc83;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cc83 create() => cc83._();
  cc83 createEmptyInstance() => create();
  static $pb.PbList<cc83> createRepeated() => $pb.PbList<cc83>();
  @$core.pragma('dart2js:noInline')
  static cc83 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cc83>(create);
  static cc83? _defaultInstance;

  /// 1:查询中继在线状态,
  /// 2:回应查询 1,返回在线的中继
  /// 3:中继上线通知
  /// 4:中继下线通知
  /// 11:查询手台在线状态
  /// 12:回应查询11,返回在线的手台
  /// 13:手台上线通知
  /// 14:手台下线通知
  @$pb.TagNumber(1)
  $core.int get actionCode => $_getIZ(0);
  @$pb.TagNumber(1)
  set actionCode($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasActionCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearActionCode() => clearField(1);

  /// action_code=2, 这里是在线的中继列表
  @$pb.TagNumber(2)
  $core.List<$core.String> get hexDmrids => $_getList(1);

  /// 相应的型号信息
  @$pb.TagNumber(3)
  $core.List<$core.String> get models => $_getList(2);

  /// 相应的额外功能，逗号分隔
  /// 8:中继支持监控数据查询的报告
  @$pb.TagNumber(4)
  $core.List<$core.String> get functions => $_getList(3);
}

/// 8100终端/中继 状态 查询/通知
/// rpc.cmd=183
class cc183 extends $pb.GeneratedMessage {
  factory cc183({
    $core.int? actionCode,
    $core.Iterable<$core.int>? dmrids,
    $core.Iterable<$fixnum.Int64>? lastDataTimes,
  }) {
    final $result = create();
    if (actionCode != null) {
      $result.actionCode = actionCode;
    }
    if (dmrids != null) {
      $result.dmrids.addAll(dmrids);
    }
    if (lastDataTimes != null) {
      $result.lastDataTimes.addAll(lastDataTimes);
    }
    return $result;
  }
  cc183._() : super();
  factory cc183.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory cc183.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'cc183', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'actionCode', $pb.PbFieldType.O3)
    ..p<$core.int>(2, _omitFieldNames ? '' : 'dmrids', $pb.PbFieldType.KU3)
    ..p<$fixnum.Int64>(3, _omitFieldNames ? '' : 'lastDataTimes', $pb.PbFieldType.K6)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  cc183 clone() => cc183()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  cc183 copyWith(void Function(cc183) updates) => super.copyWith((message) => updates(message as cc183)) as cc183;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static cc183 create() => cc183._();
  cc183 createEmptyInstance() => create();
  static $pb.PbList<cc183> createRepeated() => $pb.PbList<cc183>();
  @$core.pragma('dart2js:noInline')
  static cc183 getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<cc183>(create);
  static cc183? _defaultInstance;

  /// 1:查询中继在线状态,
  /// 2:回应查询 1,返回在线的中继
  /// 3:中继上线通知
  /// 4:中继下线通知
  /// 11:查询手台在线状态
  /// 12:回应查询11,返回在线的手台
  /// 13:手台上线通知
  /// 14:手台下线通知
  @$pb.TagNumber(1)
  $core.int get actionCode => $_getIZ(0);
  @$pb.TagNumber(1)
  set actionCode($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasActionCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearActionCode() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.int> get dmrids => $_getList(1);

  /// 终端/中继最后活动时间, UTC时间戳, 单位秒
  @$pb.TagNumber(3)
  $core.List<$fixnum.Int64> get lastDataTimes => $_getList(2);
}

/// 收到未知物联网设备上来的数据
/// rpc_cmd.command=1110
class unknown_iot_device_cmd extends $pb.GeneratedMessage {
  factory unknown_iot_device_cmd({
    $core.int? devType,
    $core.String? devId,
  }) {
    final $result = create();
    if (devType != null) {
      $result.devType = devType;
    }
    if (devId != null) {
      $result.devId = devId;
    }
    return $result;
  }
  unknown_iot_device_cmd._() : super();
  factory unknown_iot_device_cmd.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory unknown_iot_device_cmd.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'unknown_iot_device_cmd', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'devType', $pb.PbFieldType.O3)
    ..aOS(2, _omitFieldNames ? '' : 'devId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  unknown_iot_device_cmd clone() => unknown_iot_device_cmd()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  unknown_iot_device_cmd copyWith(void Function(unknown_iot_device_cmd) updates) => super.copyWith((message) => updates(message as unknown_iot_device_cmd)) as unknown_iot_device_cmd;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static unknown_iot_device_cmd create() => unknown_iot_device_cmd._();
  unknown_iot_device_cmd createEmptyInstance() => create();
  static $pb.PbList<unknown_iot_device_cmd> createRepeated() => $pb.PbList<unknown_iot_device_cmd>();
  @$core.pragma('dart2js:noInline')
  static unknown_iot_device_cmd getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<unknown_iot_device_cmd>(create);
  static unknown_iot_device_cmd? _defaultInstance;

  /// 未知设备类型  200:基站巡查点 1:对讲机 2:工牌 3:物卡 4：烟感  5：设备开关盒  6：信标开关盒
  @$pb.TagNumber(1)
  $core.int get devType => $_getIZ(0);
  @$pb.TagNumber(1)
  set devType($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDevType() => $_has(0);
  @$pb.TagNumber(1)
  void clearDevType() => clearField(1);

  /// 未知设备的dmrid或rid  基站巡查点->rfid  对讲机等->dmrid
  @$pb.TagNumber(2)
  $core.String get devId => $_getSZ(1);
  @$pb.TagNumber(2)
  set devId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDevId() => $_has(1);
  @$pb.TagNumber(2)
  void clearDevId() => clearField(2);
}

/// 越界报警.对应db_iot_restriction 1.离开只允许进入基站巡查点 2.进入禁止进入基站巡查点
class over_step_base_station extends $pb.GeneratedMessage {
  factory over_step_base_station({
    $core.int? devType,
    $core.String? devId,
    $core.int? overStepType,
  }) {
    final $result = create();
    if (devType != null) {
      $result.devType = devType;
    }
    if (devId != null) {
      $result.devId = devId;
    }
    if (overStepType != null) {
      $result.overStepType = overStepType;
    }
    return $result;
  }
  over_step_base_station._() : super();
  factory over_step_base_station.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory over_step_base_station.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'over_step_base_station', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'devType', $pb.PbFieldType.O3)
    ..aOS(2, _omitFieldNames ? '' : 'devId')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'overStepType', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  over_step_base_station clone() => over_step_base_station()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  over_step_base_station copyWith(void Function(over_step_base_station) updates) => super.copyWith((message) => updates(message as over_step_base_station)) as over_step_base_station;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static over_step_base_station create() => over_step_base_station._();
  over_step_base_station createEmptyInstance() => create();
  static $pb.PbList<over_step_base_station> createRepeated() => $pb.PbList<over_step_base_station>();
  @$core.pragma('dart2js:noInline')
  static over_step_base_station getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<over_step_base_station>(create);
  static over_step_base_station? _defaultInstance;

  /// 设备类型  1:对讲机 2:工牌 3:物卡 4：烟感  5：设备开关盒  6：信标开关盒
  @$pb.TagNumber(1)
  $core.int get devType => $_getIZ(0);
  @$pb.TagNumber(1)
  set devType($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasDevType() => $_has(0);
  @$pb.TagNumber(1)
  void clearDevType() => clearField(1);

  /// 设备id
  @$pb.TagNumber(2)
  $core.String get devId => $_getSZ(1);
  @$pb.TagNumber(2)
  set devId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDevId() => $_has(1);
  @$pb.TagNumber(2)
  void clearDevId() => clearField(2);

  /// 越界类型 1.离开只允许进入基站巡查点 2.进入禁止进入基站巡查点
  @$pb.TagNumber(4)
  $core.int get overStepType => $_getIZ(2);
  @$pb.TagNumber(4)
  set overStepType($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasOverStepType() => $_has(2);
  @$pb.TagNumber(4)
  void clearOverStepType() => clearField(4);
}

/// sip电话终端可以动态修改自己的收听组
class SipGroupSubscribeOperation extends $pb.GeneratedMessage {
  factory SipGroupSubscribeOperation({
    $core.int? operation,
    $core.Iterable<$core.String>? dmrids,
    $core.int? res,
    $core.String? resMsg,
  }) {
    final $result = create();
    if (operation != null) {
      $result.operation = operation;
    }
    if (dmrids != null) {
      $result.dmrids.addAll(dmrids);
    }
    if (res != null) {
      $result.res = res;
    }
    if (resMsg != null) {
      $result.resMsg = resMsg;
    }
    return $result;
  }
  SipGroupSubscribeOperation._() : super();
  factory SipGroupSubscribeOperation.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SipGroupSubscribeOperation.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SipGroupSubscribeOperation', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'operation', $pb.PbFieldType.O3)
    ..pPS(2, _omitFieldNames ? '' : 'dmrids')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'res', $pb.PbFieldType.O3)
    ..aOS(4, _omitFieldNames ? '' : 'resMsg')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SipGroupSubscribeOperation clone() => SipGroupSubscribeOperation()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SipGroupSubscribeOperation copyWith(void Function(SipGroupSubscribeOperation) updates) => super.copyWith((message) => updates(message as SipGroupSubscribeOperation)) as SipGroupSubscribeOperation;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SipGroupSubscribeOperation create() => SipGroupSubscribeOperation._();
  SipGroupSubscribeOperation createEmptyInstance() => create();
  static $pb.PbList<SipGroupSubscribeOperation> createRepeated() => $pb.PbList<SipGroupSubscribeOperation>();
  @$core.pragma('dart2js:noInline')
  static SipGroupSubscribeOperation getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SipGroupSubscribeOperation>(create);
  static SipGroupSubscribeOperation? _defaultInstance;

  /// 操作命令
  /// 1:添加
  /// 2:删除
  /// 3:查询当前收听组（内存）
  /// 4:查询当前收听组（数据库配置）
  /// 5:替换当前收听组为dmrids（内存）
  /// 6:替换当前收听组为dmrids（数据库配置）
  /// 7:清空当前收听组（内存）
  /// 101: response to 1
  /// 102: response to 2
  /// 103: response to 3
  /// 104: response to 4
  /// 105: response to 5
  /// 106: response to 6
  /// 107: response to 7
  @$pb.TagNumber(1)
  $core.int get operation => $_getIZ(0);
  @$pb.TagNumber(1)
  set operation($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasOperation() => $_has(0);
  @$pb.TagNumber(1)
  void clearOperation() => clearField(1);

  /// 收听组dmrid，每个为8位HEX字符串
  @$pb.TagNumber(2)
  $core.List<$core.String> get dmrids => $_getList(1);

  /// response code
  /// 0:成功 1:失败
  @$pb.TagNumber(3)
  $core.int get res => $_getIZ(2);
  @$pb.TagNumber(3)
  set res($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRes() => $_has(2);
  @$pb.TagNumber(3)
  void clearRes() => clearField(3);

  /// response message when err
  @$pb.TagNumber(4)
  $core.String get resMsg => $_getSZ(3);
  @$pb.TagNumber(4)
  set resMsg($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasResMsg() => $_has(3);
  @$pb.TagNumber(4)
  void clearResMsg() => clearField(4);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
