//
//  Generated code. Do not modify.
//  source: bf_radio.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use gps84Descriptor instead')
const gps84$json = {
  '1': 'gps84',
  '2': [
    {'1': 'gps_time', '3': 1, '4': 1, '5': 9, '10': 'gpsTime'},
    {'1': 'av', '3': 2, '4': 1, '5': 5, '10': 'av'},
    {'1': 'lat', '3': 3, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'lon', '3': 4, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'speed', '3': 5, '4': 1, '5': 1, '10': 'speed'},
    {'1': 'direction', '3': 6, '4': 1, '5': 5, '10': 'direction'},
    {'1': 'altitude', '3': 7, '4': 1, '5': 5, '10': 'altitude'},
  ],
};

/// Descriptor for `gps84`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List gps84Descriptor = $convert.base64Decode(
    'CgVncHM4NBIZCghncHNfdGltZRgBIAEoCVIHZ3BzVGltZRIOCgJhdhgCIAEoBVICYXYSEAoDbG'
    'F0GAMgASgBUgNsYXQSEAoDbG9uGAQgASgBUgNsb24SFAoFc3BlZWQYBSABKAFSBXNwZWVkEhwK'
    'CWRpcmVjdGlvbhgGIAEoBVIJZGlyZWN0aW9uEhoKCGFsdGl0dWRlGAcgASgFUghhbHRpdHVkZQ'
    '==');

@$core.Deprecated('Use bcxx_headDescriptor instead')
const bcxx_head$json = {
  '1': 'bcxx_head',
  '2': [
    {'1': 'xx', '3': 1, '4': 1, '5': 9, '10': 'xx'},
    {'1': 'b_dic', '3': 2, '4': 1, '5': 5, '10': 'bDic'},
    {'1': 'cmd', '3': 3, '4': 1, '5': 5, '10': 'cmd'},
    {'1': 'sys_id', '3': 4, '4': 1, '5': 9, '10': 'sysId'},
    {'1': 'gi_flag', '3': 5, '4': 1, '5': 5, '10': 'giFlag'},
    {'1': 'c_con_ch', '3': 6, '4': 1, '5': 9, '10': 'cConCh'},
    {'1': 'con_ch', '3': 7, '4': 1, '5': 9, '10': 'conCh'},
    {'1': 'm_id_t', '3': 8, '4': 1, '5': 9, '10': 'mIdT'},
    {'1': 'm_id_s', '3': 9, '4': 1, '5': 9, '10': 'mIdS'},
    {'1': 'cmd_time', '3': 10, '4': 1, '5': 9, '10': 'cmdTime'},
    {'1': 'm_tx_e', '3': 11, '4': 1, '5': 5, '10': 'mTxE'},
    {'1': 'ms_status', '3': 12, '4': 1, '5': 9, '10': 'msStatus'},
    {'1': 'orig_data', '3': 13, '4': 1, '5': 12, '10': 'origData'},
  ],
};

/// Descriptor for `bcxx_head`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bcxx_headDescriptor = $convert.base64Decode(
    'CgliY3h4X2hlYWQSDgoCeHgYASABKAlSAnh4EhMKBWJfZGljGAIgASgFUgRiRGljEhAKA2NtZB'
    'gDIAEoBVIDY21kEhUKBnN5c19pZBgEIAEoCVIFc3lzSWQSFwoHZ2lfZmxhZxgFIAEoBVIGZ2lG'
    'bGFnEhgKCGNfY29uX2NoGAYgASgJUgZjQ29uQ2gSFQoGY29uX2NoGAcgASgJUgVjb25DaBIUCg'
    'ZtX2lkX3QYCCABKAlSBG1JZFQSFAoGbV9pZF9zGAkgASgJUgRtSWRTEhkKCGNtZF90aW1lGAog'
    'ASgJUgdjbWRUaW1lEhQKBm1fdHhfZRgLIAEoBVIEbVR4RRIbCgltc19zdGF0dXMYDCABKAlSCG'
    '1zU3RhdHVzEhsKCW9yaWdfZGF0YRgNIAEoDFIIb3JpZ0RhdGE=');

@$core.Deprecated('Use cmd_repeate_receivedDescriptor instead')
const cmd_repeate_received$json = {
  '1': 'cmd_repeate_received',
  '2': [
    {'1': 'dmr_id', '3': 1, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'cmd', '3': 2, '4': 1, '5': 9, '10': 'cmd'},
    {'1': 'c_dic', '3': 3, '4': 1, '5': 9, '10': 'cDic'},
    {'1': 'sys_id', '3': 4, '4': 1, '5': 9, '10': 'sysId'},
    {'1': 'cmd_bytes', '3': 5, '4': 1, '5': 12, '10': 'cmdBytes'},
  ],
};

/// Descriptor for `cmd_repeate_received`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cmd_repeate_receivedDescriptor = $convert.base64Decode(
    'ChRjbWRfcmVwZWF0ZV9yZWNlaXZlZBIVCgZkbXJfaWQYASABKAlSBWRtcklkEhAKA2NtZBgCIA'
    'EoCVIDY21kEhMKBWNfZGljGAMgASgJUgRjRGljEhUKBnN5c19pZBgEIAEoCVIFc3lzSWQSGwoJ'
    'Y21kX2J5dGVzGAUgASgMUghjbWRCeXRlcw==');

@$core.Deprecated('Use not_register_device_cmdDescriptor instead')
const not_register_device_cmd$json = {
  '1': 'not_register_device_cmd',
  '2': [
    {'1': 'dmr_id', '3': 1, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'cmd_bytes', '3': 2, '4': 1, '5': 12, '10': 'cmdBytes'},
    {'1': 'received_controller_dmr_id', '3': 3, '4': 1, '5': 9, '10': 'receivedControllerDmrId'},
  ],
};

/// Descriptor for `not_register_device_cmd`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List not_register_device_cmdDescriptor = $convert.base64Decode(
    'Chdub3RfcmVnaXN0ZXJfZGV2aWNlX2NtZBIVCgZkbXJfaWQYASABKAlSBWRtcklkEhsKCWNtZF'
    '9ieXRlcxgCIAEoDFIIY21kQnl0ZXMSOwoacmVjZWl2ZWRfY29udHJvbGxlcl9kbXJfaWQYAyAB'
    'KAlSF3JlY2VpdmVkQ29udHJvbGxlckRtcklk');

@$core.Deprecated('Use new_controllerDescriptor instead')
const new_controller$json = {
  '1': 'new_controller',
  '2': [
    {'1': 'dmr_id', '3': 1, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'ip_info', '3': 2, '4': 1, '5': 9, '10': 'ipInfo'},
    {'1': 'model', '3': 3, '4': 1, '5': 9, '10': 'model'},
    {'1': 'device_name', '3': 4, '4': 1, '5': 9, '10': 'deviceName'},
  ],
};

/// Descriptor for `new_controller`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List new_controllerDescriptor = $convert.base64Decode(
    'Cg5uZXdfY29udHJvbGxlchIVCgZkbXJfaWQYASABKAlSBWRtcklkEhcKB2lwX2luZm8YAiABKA'
    'lSBmlwSW5mbxIUCgVtb2RlbBgDIAEoCVIFbW9kZWwSHwoLZGV2aWNlX25hbWUYBCABKAlSCmRl'
    'dmljZU5hbWU=');

@$core.Deprecated('Use bc00Descriptor instead')
const bc00$json = {
  '1': 'bc00',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'c_dic', '3': 3, '4': 1, '5': 5, '10': 'cDic'},
    {'1': 'cbxx', '3': 4, '4': 1, '5': 5, '10': 'cbxx'},
  ],
};

/// Descriptor for `bc00`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc00Descriptor = $convert.base64Decode(
    'CgRiYzAwEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSEwoFY19kaWMYAyABKAVSBGNEaWMSEgoE'
    'Y2J4eBgEIAEoBVIEY2J4eA==');

@$core.Deprecated('Use bc01Descriptor instead')
const bc01$json = {
  '1': 'bc01',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
  ],
};

/// Descriptor for `bc01`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc01Descriptor = $convert.base64Decode(
    'CgRiYzAxEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHM=');

@$core.Deprecated('Use bc02Descriptor instead')
const bc02$json = {
  '1': 'bc02',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'time', '3': 4, '4': 1, '5': 5, '10': 'time'},
    {'1': 'size', '3': 5, '4': 1, '5': 5, '10': 'size'},
  ],
};

/// Descriptor for `bc02`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc02Descriptor = $convert.base64Decode(
    'CgRiYzAyEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDwoDeV9uGAMgASgFUgJ5ThISCgR0aW1l'
    'GAQgASgFUgR0aW1lEhIKBHNpemUYBSABKAVSBHNpemU=');

@$core.Deprecated('Use bc03Descriptor instead')
const bc03$json = {
  '1': 'bc03',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
  ],
};

/// Descriptor for `bc03`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc03Descriptor = $convert.base64Decode(
    'CgRiYzAzEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHM=');

@$core.Deprecated('Use bc04Descriptor instead')
const bc04$json = {
  '1': 'bc04',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'tp', '3': 3, '4': 1, '5': 5, '10': 'tp'},
    {'1': 'y_n', '3': 4, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'pen_n', '3': 5, '4': 1, '5': 5, '10': 'penN'},
    {'1': 'time', '3': 6, '4': 1, '5': 5, '10': 'time'},
    {'1': 'min_lat', '3': 7, '4': 1, '5': 1, '10': 'minLat'},
    {'1': 'min_lon', '3': 8, '4': 1, '5': 1, '10': 'minLon'},
    {'1': 'max_lat', '3': 9, '4': 1, '5': 1, '10': 'maxLat'},
    {'1': 'max_lon', '3': 10, '4': 1, '5': 1, '10': 'maxLon'},
  ],
};

/// Descriptor for `bc04`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc04Descriptor = $convert.base64Decode(
    'CgRiYzA0EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDgoCdHAYAyABKAVSAnRwEg8KA3lfbhgE'
    'IAEoBVICeU4SEwoFcGVuX24YBSABKAVSBHBlbk4SEgoEdGltZRgGIAEoBVIEdGltZRIXCgdtaW'
    '5fbGF0GAcgASgBUgZtaW5MYXQSFwoHbWluX2xvbhgIIAEoAVIGbWluTG9uEhcKB21heF9sYXQY'
    'CSABKAFSBm1heExhdBIXCgdtYXhfbG9uGAogASgBUgZtYXhMb24=');

@$core.Deprecated('Use bc05Descriptor instead')
const bc05$json = {
  '1': 'bc05',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'tp', '3': 3, '4': 1, '5': 5, '10': 'tp'},
    {'1': 'y_n', '3': 4, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'time', '3': 5, '4': 1, '5': 5, '10': 'time'},
    {'1': 'lat', '3': 6, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'lon', '3': 7, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat_dif', '3': 8, '4': 1, '5': 9, '10': 'latDif'},
    {'1': 'lon_dif', '3': 9, '4': 1, '5': 9, '10': 'lonDif'},
  ],
};

/// Descriptor for `bc05`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc05Descriptor = $convert.base64Decode(
    'CgRiYzA1EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDgoCdHAYAyABKAVSAnRwEg8KA3lfbhgE'
    'IAEoBVICeU4SEgoEdGltZRgFIAEoBVIEdGltZRIQCgNsYXQYBiABKAFSA2xhdBIQCgNsb24YBy'
    'ABKAFSA2xvbhIXCgdsYXRfZGlmGAggASgJUgZsYXREaWYSFwoHbG9uX2RpZhgJIAEoCVIGbG9u'
    'RGlm');

@$core.Deprecated('Use bc06Descriptor instead')
const bc06$json = {
  '1': 'bc06',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'tp', '3': 3, '4': 1, '5': 5, '10': 'tp'},
    {'1': 'y_n', '3': 4, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'time', '3': 5, '4': 1, '5': 5, '10': 'time'},
  ],
};

/// Descriptor for `bc06`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc06Descriptor = $convert.base64Decode(
    'CgRiYzA2EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDgoCdHAYAyABKAVSAnRwEg8KA3lfbhgE'
    'IAEoBVICeU4SEgoEdGltZRgFIAEoBVIEdGltZQ==');

@$core.Deprecated('Use bc07Descriptor instead')
const bc07$json = {
  '1': 'bc07',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'jt_time', '3': 4, '4': 1, '5': 5, '10': 'jtTime'},
    {'1': 'dw_time', '3': 5, '4': 1, '5': 5, '10': 'dwTime'},
  ],
};

/// Descriptor for `bc07`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc07Descriptor = $convert.base64Decode(
    'CgRiYzA3EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDwoDeV9uGAMgASgFUgJ5ThIXCgdqdF90'
    'aW1lGAQgASgFUgZqdFRpbWUSFwoHZHdfdGltZRgFIAEoBVIGZHdUaW1l');

@$core.Deprecated('Use bc08Descriptor instead')
const bc08$json = {
  '1': 'bc08',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'y_n', '3': 4, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'jt_ch', '3': 5, '4': 1, '5': 5, '10': 'jtCh'},
    {'1': 'time', '3': 6, '4': 1, '5': 5, '10': 'time'},
  ],
};

/// Descriptor for `bc08`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc08Descriptor = $convert.base64Decode(
    'CgRiYzA4EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDwoDeV9uGAQgASgFUgJ5ThITCgVqdF9j'
    'aBgFIAEoBVIEanRDaBISCgR0aW1lGAYgASgFUgR0aW1l');

@$core.Deprecated('Use bc09Descriptor instead')
const bc09$json = {
  '1': 'bc09',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'st', '3': 4, '4': 1, '5': 5, '10': 'st'},
  ],
};

/// Descriptor for `bc09`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc09Descriptor = $convert.base64Decode(
    'CgRiYzA5EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDwoDeV9uGAMgASgFUgJ5ThIOCgJzdBgE'
    'IAEoBVICc3Q=');

@$core.Deprecated('Use bc10Descriptor instead')
const bc10$json = {
  '1': 'bc10',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'c_dic', '3': 3, '4': 1, '5': 5, '10': 'cDic'},
    {'1': 'cbxx', '3': 4, '4': 1, '5': 5, '10': 'cbxx'},
  ],
};

/// Descriptor for `bc10`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc10Descriptor = $convert.base64Decode(
    'CgRiYzEwEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSEwoFY19kaWMYAyABKAVSBGNEaWMSEgoE'
    'Y2J4eBgEIAEoBVIEY2J4eA==');

@$core.Deprecated('Use bc11Descriptor instead')
const bc11$json = {
  '1': 'bc11',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'c_tp', '3': 3, '4': 1, '5': 12, '10': 'cTp'},
    {'1': 'a_tp', '3': 4, '4': 1, '5': 5, '10': 'aTp'},
    {'1': 'dd_ch', '3': 5, '4': 1, '5': 5, '10': 'ddCh'},
  ],
};

/// Descriptor for `bc11`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc11Descriptor = $convert.base64Decode(
    'CgRiYzExEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIPCgN5X2'
    '4YAiABKAVSAnlOEhEKBGNfdHAYAyABKAxSA2NUcBIRCgRhX3RwGAQgASgFUgNhVHASEwoFZGRf'
    'Y2gYBSABKAVSBGRkQ2g=');

@$core.Deprecated('Use bc12Descriptor instead')
const bc12$json = {
  '1': 'bc12',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'p_s', '3': 3, '4': 1, '5': 5, '10': 'pS'},
    {'1': 'lp_s', '3': 4, '4': 1, '5': 5, '10': 'lpS'},
    {'1': 'ls_time', '3': 5, '4': 1, '5': 9, '10': 'lsTime'},
    {'1': 'sell_id', '3': 6, '4': 1, '5': 9, '10': 'sellId'},
    {'1': 'licence', '3': 7, '4': 1, '5': 9, '10': 'licence'},
    {'1': 'dev_group', '3': 8, '4': 1, '5': 7, '10': 'devGroup'},
    {'1': 'roaming', '3': 9, '4': 1, '5': 5, '10': 'roaming'},
    {'1': 'from_bc40', '3': 10, '4': 1, '5': 8, '10': 'fromBc40'},
  ],
};

/// Descriptor for `bc12`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc12Descriptor = $convert.base64Decode(
    'CgRiYzEyEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDwoDcF9zGAMgASgFUgJwUxIRCgRscF9z'
    'GAQgASgFUgNscFMSFwoHbHNfdGltZRgFIAEoCVIGbHNUaW1lEhcKB3NlbGxfaWQYBiABKAlSBn'
    'NlbGxJZBIYCgdsaWNlbmNlGAcgASgJUgdsaWNlbmNlEhsKCWRldl9ncm91cBgIIAEoB1IIZGV2'
    'R3JvdXASGAoHcm9hbWluZxgJIAEoBVIHcm9hbWluZxIbCglmcm9tX2JjNDAYCiABKAhSCGZyb2'
    '1CYzQw');

@$core.Deprecated('Use bc13Descriptor instead')
const bc13$json = {
  '1': 'bc13',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'type', '3': 3, '4': 1, '5': 5, '10': 'type'},
    {'1': 'land_bs', '3': 4, '4': 1, '5': 5, '10': 'landBs'},
    {'1': 'land_ch', '3': 5, '4': 1, '5': 5, '10': 'landCh'},
    {'1': 'dev_group', '3': 6, '4': 1, '5': 7, '10': 'devGroup'},
    {'1': 'roaming', '3': 7, '4': 1, '5': 5, '10': 'roaming'},
    {'1': 'from_bc40', '3': 8, '4': 1, '5': 8, '10': 'fromBc40'},
  ],
};

/// Descriptor for `bc13`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc13Descriptor = $convert.base64Decode(
    'CgRiYzEzEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSEgoEdHlwZRgDIAEoBVIEdHlwZRIXCgds'
    'YW5kX2JzGAQgASgFUgZsYW5kQnMSFwoHbGFuZF9jaBgFIAEoBVIGbGFuZENoEhsKCWRldl9ncm'
    '91cBgGIAEoB1IIZGV2R3JvdXASGAoHcm9hbWluZxgHIAEoBVIHcm9hbWluZxIbCglmcm9tX2Jj'
    'NDAYCCABKAhSCGZyb21CYzQw');

@$core.Deprecated('Use bc14Descriptor instead')
const bc14$json = {
  '1': 'bc14',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'land_bs', '3': 3, '4': 1, '5': 5, '10': 'landBs'},
    {'1': 'audio_cm', '3': 4, '4': 1, '5': 5, '10': 'audioCm'},
  ],
};

/// Descriptor for `bc14`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc14Descriptor = $convert.base64Decode(
    'CgRiYzE0EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSFwoHbGFuZF9icxgDIAEoBVIGbGFuZEJz'
    'EhkKCGF1ZGlvX2NtGAQgASgFUgdhdWRpb0Nt');

@$core.Deprecated('Use bc15Descriptor instead')
const bc15$json = {
  '1': 'bc15',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'voice_d_type', '3': 2, '4': 1, '5': 5, '10': 'voiceDType'},
    {'1': 'voice_a_type', '3': 3, '4': 1, '5': 5, '10': 'voiceAType'},
    {'1': 'call_type', '3': 4, '4': 1, '5': 5, '10': 'callType'},
    {'1': 'call_status', '3': 5, '4': 1, '5': 5, '10': 'callStatus'},
  ],
};

/// Descriptor for `bc15`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc15Descriptor = $convert.base64Decode(
    'CgRiYzE1EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIgCgx2b2'
    'ljZV9kX3R5cGUYAiABKAVSCnZvaWNlRFR5cGUSIAoMdm9pY2VfYV90eXBlGAMgASgFUgp2b2lj'
    'ZUFUeXBlEhsKCWNhbGxfdHlwZRgEIAEoBVIIY2FsbFR5cGUSHwoLY2FsbF9zdGF0dXMYBSABKA'
    'VSCmNhbGxTdGF0dXM=');

@$core.Deprecated('Use bc16Descriptor instead')
const bc16$json = {
  '1': 'bc16',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
  ],
};

/// Descriptor for `bc16`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc16Descriptor = $convert.base64Decode(
    'CgRiYzE2EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHM=');

@$core.Deprecated('Use bc17Descriptor instead')
const bc17$json = {
  '1': 'bc17',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'user_st', '3': 3, '4': 1, '5': 9, '10': 'userSt'},
    {'1': 'user_tp', '3': 4, '4': 1, '5': 5, '10': 'userTp'},
    {'1': 'user_addr_id', '3': 5, '4': 1, '5': 9, '10': 'userAddrId'},
    {'1': 'license', '3': 6, '4': 1, '5': 9, '10': 'license'},
    {'1': 'user_show_id', '3': 7, '4': 1, '5': 9, '10': 'userShowId'},
  ],
};

/// Descriptor for `bc17`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc17Descriptor = $convert.base64Decode(
    'CgRiYzE3EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIPCgN5X2'
    '4YAiABKAVSAnlOEhcKB3VzZXJfc3QYAyABKAlSBnVzZXJTdBIXCgd1c2VyX3RwGAQgASgFUgZ1'
    'c2VyVHASIAoMdXNlcl9hZGRyX2lkGAUgASgJUgp1c2VyQWRkcklkEhgKB2xpY2Vuc2UYBiABKA'
    'lSB2xpY2Vuc2USIAoMdXNlcl9zaG93X2lkGAcgASgJUgp1c2VyU2hvd0lk');

@$core.Deprecated('Use bc18Descriptor instead')
const bc18$json = {
  '1': 'bc18',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'alarm', '3': 3, '4': 1, '5': 5, '10': 'alarm'},
    {'1': 'db_rid', '3': 4, '4': 1, '5': 9, '10': 'dbRid'},
  ],
};

/// Descriptor for `bc18`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc18Descriptor = $convert.base64Decode(
    'CgRiYzE4EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSFAoFYWxhcm0YAyABKAVSBWFsYXJtEhUK'
    'BmRiX3JpZBgEIAEoCVIFZGJSaWQ=');

@$core.Deprecated('Use bc19Descriptor instead')
const bc19$json = {
  '1': 'bc19',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
  ],
};

/// Descriptor for `bc19`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc19Descriptor = $convert.base64Decode(
    'CgRiYzE5EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDwoDeV9uGAMgASgFUgJ5Tg==');

@$core.Deprecated('Use bc20Descriptor instead')
const bc20$json = {
  '1': 'bc20',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'link_tp', '3': 4, '4': 1, '5': 5, '10': 'linkTp'},
    {'1': 'a_tp', '3': 5, '4': 1, '5': 5, '10': 'aTp'},
    {'1': 'net_id', '3': 6, '4': 1, '5': 9, '10': 'netId'},
  ],
};

/// Descriptor for `bc20`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc20Descriptor = $convert.base64Decode(
    'CgRiYzIwEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSDwoDeV9uGAMgASgFUgJ5ThIXCgdsaW5r'
    'X3RwGAQgASgFUgZsaW5rVHASEQoEYV90cBgFIAEoBVIDYVRwEhUKBm5ldF9pZBgGIAEoCVIFbm'
    'V0SWQ=');

@$core.Deprecated('Use bc21Descriptor instead')
const bc21$json = {
  '1': 'bc21',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'c_dic', '3': 3, '4': 1, '5': 5, '10': 'cDic'},
    {'1': 'cbxx', '3': 4, '4': 1, '5': 5, '10': 'cbxx'},
  ],
};

/// Descriptor for `bc21`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc21Descriptor = $convert.base64Decode(
    'CgRiYzIxEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSEwoFY19kaWMYAyABKAVSBGNEaWMSEgoE'
    'Y2J4eBgEIAEoBVIEY2J4eA==');

@$core.Deprecated('Use bc22Descriptor instead')
const bc22$json = {
  '1': 'bc22',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'c_dic', '3': 3, '4': 1, '5': 5, '10': 'cDic'},
    {'1': 'cbxx', '3': 4, '4': 1, '5': 5, '10': 'cbxx'},
    {'1': 'y_n', '3': 5, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'check_st', '3': 6, '4': 1, '5': 5, '10': 'checkSt'},
  ],
};

/// Descriptor for `bc22`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc22Descriptor = $convert.base64Decode(
    'CgRiYzIyEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSEwoFY19kaWMYAyABKAVSBGNEaWMSEgoE'
    'Y2J4eBgEIAEoBVIEY2J4eBIPCgN5X24YBSABKAVSAnlOEhkKCGNoZWNrX3N0GAYgASgFUgdjaG'
    'Vja1N0');

@$core.Deprecated('Use bc23Descriptor instead')
const bc23$json = {
  '1': 'bc23',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'con_status', '3': 2, '4': 1, '5': 9, '10': 'conStatus'},
    {'1': 'c_dic', '3': 3, '4': 1, '5': 5, '10': 'cDic'},
    {'1': 'cbxx', '3': 4, '4': 1, '5': 5, '10': 'cbxx'},
  ],
};

/// Descriptor for `bc23`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc23Descriptor = $convert.base64Decode(
    'CgRiYzIzEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIdCgpjb2'
    '5fc3RhdHVzGAIgASgJUgljb25TdGF0dXMSEwoFY19kaWMYAyABKAVSBGNEaWMSEgoEY2J4eBgE'
    'IAEoBVIEY2J4eA==');

@$core.Deprecated('Use bc25Descriptor instead')
const bc25$json = {
  '1': 'bc25',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'point_n', '3': 3, '4': 1, '5': 5, '10': 'pointN'},
    {'1': 'point_card', '3': 4, '4': 1, '5': 9, '10': 'pointCard'},
    {'1': 'lat', '3': 5, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'lon', '3': 6, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat_dif', '3': 7, '4': 1, '5': 9, '10': 'latDif'},
    {'1': 'lon_dif', '3': 8, '4': 1, '5': 9, '10': 'lonDif'},
  ],
};

/// Descriptor for `bc25`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc25Descriptor = $convert.base64Decode(
    'CgRiYzI1EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIPCgN5X2'
    '4YAiABKAVSAnlOEhcKB3BvaW50X24YAyABKAVSBnBvaW50ThIdCgpwb2ludF9jYXJkGAQgASgJ'
    'Uglwb2ludENhcmQSEAoDbGF0GAUgASgBUgNsYXQSEAoDbG9uGAYgASgBUgNsb24SFwoHbGF0X2'
    'RpZhgHIAEoCVIGbGF0RGlmEhcKB2xvbl9kaWYYCCABKAlSBmxvbkRpZg==');

@$core.Deprecated('Use bc26Descriptor instead')
const bc26$json = {
  '1': 'bc26',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'ch', '3': 3, '4': 1, '5': 5, '10': 'ch'},
    {'1': 'code_tp', '3': 4, '4': 1, '5': 5, '10': 'codeTp'},
    {'1': 'ch_name', '3': 5, '4': 1, '5': 12, '10': 'chName'},
  ],
};

/// Descriptor for `bc26`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc26Descriptor = $convert.base64Decode(
    'CgRiYzI2EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIPCgN5X2'
    '4YAiABKAVSAnlOEg4KAmNoGAMgASgFUgJjaBIXCgdjb2RlX3RwGAQgASgFUgZjb2RlVHASFwoH'
    'Y2hfbmFtZRgFIAEoDFIGY2hOYW1l');

@$core.Deprecated('Use bc28Descriptor instead')
const bc28$json = {
  '1': 'bc28',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'code_tp', '3': 3, '4': 1, '5': 5, '10': 'codeTp'},
    {'1': 'c_byte', '3': 4, '4': 1, '5': 5, '10': 'cByte'},
    {'1': 'data', '3': 5, '4': 1, '5': 12, '10': 'data'},
  ],
};

/// Descriptor for `bc28`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc28Descriptor = $convert.base64Decode(
    'CgRiYzI4EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIPCgN5X2'
    '4YAiABKAVSAnlOEhcKB2NvZGVfdHAYAyABKAVSBmNvZGVUcBIVCgZjX2J5dGUYBCABKAVSBWNC'
    'eXRlEhIKBGRhdGEYBSABKAxSBGRhdGE=');

@$core.Deprecated('Use bc29Descriptor instead')
const bc29$json = {
  '1': 'bc29',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'key_id', '3': 3, '4': 1, '5': 9, '10': 'keyId'},
  ],
};

/// Descriptor for `bc29`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc29Descriptor = $convert.base64Decode(
    'CgRiYzI5EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIPCgN5X2'
    '4YAiABKAVSAnlOEhUKBmtleV9pZBgDIAEoCVIFa2V5SWQ=');

@$core.Deprecated('Use bc31Descriptor instead')
const bc31$json = {
  '1': 'bc31',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'sms_type', '3': 2, '4': 1, '5': 9, '10': 'smsType'},
    {'1': 'sms_content', '3': 3, '4': 1, '5': 9, '10': 'smsContent'},
    {'1': 'sms_no', '3': 4, '4': 1, '5': 9, '10': 'smsNo'},
  ],
};

/// Descriptor for `bc31`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc31Descriptor = $convert.base64Decode(
    'CgRiYzMxEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIZCghzbX'
    'NfdHlwZRgCIAEoCVIHc21zVHlwZRIfCgtzbXNfY29udGVudBgDIAEoCVIKc21zQ29udGVudBIV'
    'CgZzbXNfbm8YBCABKAlSBXNtc05v');

@$core.Deprecated('Use bc38Descriptor instead')
const bc38$json = {
  '1': 'bc38',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'm_group_id', '3': 3, '4': 1, '5': 9, '10': 'mGroupId'},
  ],
};

/// Descriptor for `bc38`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc38Descriptor = $convert.base64Decode(
    'CgRiYzM4EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIcCgptX2'
    'dyb3VwX2lkGAMgASgJUghtR3JvdXBJZA==');

@$core.Deprecated('Use bc39Descriptor instead')
const bc39$json = {
  '1': 'bc39',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'm_group_id', '3': 3, '4': 1, '5': 9, '10': 'mGroupId'},
  ],
};

/// Descriptor for `bc39`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc39Descriptor = $convert.base64Decode(
    'CgRiYzM5EikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIcCgptX2'
    'dyb3VwX2lkGAMgASgJUghtR3JvdXBJZA==');

@$core.Deprecated('Use bc42Descriptor instead')
const bc42$json = {
  '1': 'bc42',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'code', '3': 2, '4': 1, '5': 5, '10': 'code'},
  ],
};

/// Descriptor for `bc42`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List bc42Descriptor = $convert.base64Decode(
    'CgRiYzQyEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBISCgRjb2'
    'RlGAIgASgFUgRjb2Rl');

@$core.Deprecated('Use dc00Descriptor instead')
const dc00$json = {
  '1': 'dc00',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'gps', '3': 2, '4': 1, '5': 11, '6': '.bfdx_proto.gps84', '10': 'gps'},
    {'1': 'c_dic', '3': 3, '4': 1, '5': 5, '10': 'cDic'},
    {'1': 'cdxx', '3': 4, '4': 1, '5': 5, '10': 'cdxx'},
  ],
};

/// Descriptor for `dc00`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dc00Descriptor = $convert.base64Decode(
    'CgRkYzAwEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIjCgNncH'
    'MYAiABKAsyES5iZmR4X3Byb3RvLmdwczg0UgNncHMSEwoFY19kaWMYAyABKAVSBGNEaWMSEgoE'
    'Y2R4eBgEIAEoBVIEY2R4eA==');

@$core.Deprecated('Use dc01_one_rfidDescriptor instead')
const dc01_one_rfid$json = {
  '1': 'dc01_one_rfid',
  '2': [
    {'1': 'read_time', '3': 1, '4': 1, '5': 9, '10': 'readTime'},
    {'1': 'rfid_id', '3': 2, '4': 1, '5': 9, '10': 'rfidId'},
    {'1': 'rfid_type', '3': 3, '4': 1, '5': 5, '10': 'rfidType'},
    {'1': 'db_type', '3': 4, '4': 1, '5': 5, '10': 'dbType'},
  ],
};

/// Descriptor for `dc01_one_rfid`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dc01_one_rfidDescriptor = $convert.base64Decode(
    'Cg1kYzAxX29uZV9yZmlkEhsKCXJlYWRfdGltZRgBIAEoCVIIcmVhZFRpbWUSFwoHcmZpZF9pZB'
    'gCIAEoCVIGcmZpZElkEhsKCXJmaWRfdHlwZRgDIAEoBVIIcmZpZFR5cGUSFwoHZGJfdHlwZRgE'
    'IAEoBVIGZGJUeXBl');

@$core.Deprecated('Use dc01Descriptor instead')
const dc01$json = {
  '1': 'dc01',
  '2': [
    {'1': 'head', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.bcxx_head', '10': 'head'},
    {'1': 'backlog_points', '3': 2, '4': 1, '5': 5, '10': 'backlogPoints'},
    {'1': 'rfids', '3': 4, '4': 3, '5': 11, '6': '.bfdx_proto.dc01_one_rfid', '10': 'rfids'},
  ],
};

/// Descriptor for `dc01`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List dc01Descriptor = $convert.base64Decode(
    'CgRkYzAxEikKBGhlYWQYASABKAsyFS5iZmR4X3Byb3RvLmJjeHhfaGVhZFIEaGVhZBIlCg5iYW'
    'NrbG9nX3BvaW50cxgCIAEoBVINYmFja2xvZ1BvaW50cxIvCgVyZmlkcxgEIAMoCzIZLmJmZHhf'
    'cHJvdG8uZGMwMV9vbmVfcmZpZFIFcmZpZHM=');

@$core.Deprecated('Use cbxx_targetDescriptor instead')
const cbxx_target$json = {
  '1': 'cbxx_target',
  '2': [
    {'1': 'target_groud', '3': 1, '4': 3, '5': 9, '10': 'targetGroud'},
    {'1': 'target_device', '3': 2, '4': 3, '5': 9, '10': 'targetDevice'},
  ],
};

/// Descriptor for `cbxx_target`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cbxx_targetDescriptor = $convert.base64Decode(
    'CgtjYnh4X3RhcmdldBIhCgx0YXJnZXRfZ3JvdWQYASADKAlSC3RhcmdldEdyb3VkEiMKDXRhcm'
    'dldF9kZXZpY2UYAiADKAlSDHRhcmdldERldmljZQ==');

@$core.Deprecated('Use cb01Descriptor instead')
const cb01$json = {
  '1': 'cb01',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'time', '3': 2, '4': 1, '5': 5, '10': 'time'},
    {'1': 'size', '3': 3, '4': 1, '5': 5, '10': 'size'},
    {'1': 'count', '3': 4, '4': 1, '5': 5, '10': 'count'},
  ],
};

/// Descriptor for `cb01`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb01Descriptor = $convert.base64Decode(
    'CgRjYjAxEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'ISCgR0aW1lGAIgASgFUgR0aW1lEhIKBHNpemUYAyABKAVSBHNpemUSFAoFY291bnQYBCABKAVS'
    'BWNvdW50');

@$core.Deprecated('Use cb02Descriptor instead')
const cb02$json = {
  '1': 'cb02',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'time', '3': 4, '4': 1, '5': 5, '10': 'time'},
    {'1': 'size', '3': 5, '4': 1, '5': 5, '10': 'size'},
  ],
};

/// Descriptor for `cb02`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb02Descriptor = $convert.base64Decode(
    'CgRjYjAyEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAyABKAVSAnlOEhIKBHRpbWUYBCABKAVSBHRpbWUSEgoEc2l6ZRgFIAEoBVIEc2l6'
    'ZQ==');

@$core.Deprecated('Use cb03Descriptor instead')
const cb03$json = {
  '1': 'cb03',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'min_lat', '3': 7, '4': 1, '5': 1, '10': 'minLat'},
    {'1': 'min_lon', '3': 8, '4': 1, '5': 1, '10': 'minLon'},
    {'1': 'max_lat', '3': 9, '4': 1, '5': 1, '10': 'maxLat'},
    {'1': 'max_lon', '3': 10, '4': 1, '5': 1, '10': 'maxLon'},
  ],
};

/// Descriptor for `cb03`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb03Descriptor = $convert.base64Decode(
    'CgRjYjAzEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IXCgdtaW5fbGF0GAcgASgBUgZtaW5MYXQSFwoHbWluX2xvbhgIIAEoAVIGbWluTG9uEhcKB21h'
    'eF9sYXQYCSABKAFSBm1heExhdBIXCgdtYXhfbG9uGAogASgBUgZtYXhMb24=');

@$core.Deprecated('Use cb04Descriptor instead')
const cb04$json = {
  '1': 'cb04',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 4, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'pen_n', '3': 5, '4': 1, '5': 5, '10': 'penN'},
    {'1': 'time', '3': 6, '4': 1, '5': 5, '10': 'time'},
    {'1': 'min_lat', '3': 7, '4': 1, '5': 1, '10': 'minLat'},
    {'1': 'min_lon', '3': 8, '4': 1, '5': 1, '10': 'minLon'},
    {'1': 'max_lat', '3': 9, '4': 1, '5': 1, '10': 'maxLat'},
    {'1': 'max_lon', '3': 10, '4': 1, '5': 1, '10': 'maxLon'},
  ],
};

/// Descriptor for `cb04`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb04Descriptor = $convert.base64Decode(
    'CgRjYjA0Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YBCABKAVSAnlOEhMKBXBlbl9uGAUgASgFUgRwZW5OEhIKBHRpbWUYBiABKAVSBHRp'
    'bWUSFwoHbWluX2xhdBgHIAEoAVIGbWluTGF0EhcKB21pbl9sb24YCCABKAFSBm1pbkxvbhIXCg'
    'dtYXhfbGF0GAkgASgBUgZtYXhMYXQSFwoHbWF4X2xvbhgKIAEoAVIGbWF4TG9u');

@$core.Deprecated('Use cb05Descriptor instead')
const cb05$json = {
  '1': 'cb05',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 4, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'time', '3': 5, '4': 1, '5': 5, '10': 'time'},
    {'1': 'lat', '3': 6, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'lon', '3': 7, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat_dif', '3': 8, '4': 1, '5': 9, '10': 'latDif'},
    {'1': 'lon_dif', '3': 9, '4': 1, '5': 9, '10': 'lonDif'},
  ],
};

/// Descriptor for `cb05`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb05Descriptor = $convert.base64Decode(
    'CgRjYjA1Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YBCABKAVSAnlOEhIKBHRpbWUYBSABKAVSBHRpbWUSEAoDbGF0GAYgASgBUgNsYXQS'
    'EAoDbG9uGAcgASgBUgNsb24SFwoHbGF0X2RpZhgIIAEoCVIGbGF0RGlmEhcKB2xvbl9kaWYYCS'
    'ABKAlSBmxvbkRpZg==');

@$core.Deprecated('Use cb06Descriptor instead')
const cb06$json = {
  '1': 'cb06',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 4, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'time', '3': 5, '4': 1, '5': 5, '10': 'time'},
    {'1': 'lat_dif', '3': 8, '4': 1, '5': 9, '10': 'latDif'},
    {'1': 'lon_dif', '3': 9, '4': 1, '5': 9, '10': 'lonDif'},
  ],
};

/// Descriptor for `cb06`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb06Descriptor = $convert.base64Decode(
    'CgRjYjA2Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YBCABKAVSAnlOEhIKBHRpbWUYBSABKAVSBHRpbWUSFwoHbGF0X2RpZhgIIAEoCVIG'
    'bGF0RGlmEhcKB2xvbl9kaWYYCSABKAlSBmxvbkRpZg==');

@$core.Deprecated('Use cb07Descriptor instead')
const cb07$json = {
  '1': 'cb07',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'jt_time', '3': 4, '4': 1, '5': 5, '10': 'jtTime'},
    {'1': 'dw_time', '3': 5, '4': 1, '5': 5, '10': 'dwTime'},
  ],
};

/// Descriptor for `cb07`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb07Descriptor = $convert.base64Decode(
    'CgRjYjA3Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAyABKAVSAnlOEhcKB2p0X3RpbWUYBCABKAVSBmp0VGltZRIXCgdkd190aW1lGAUg'
    'ASgFUgZkd1RpbWU=');

@$core.Deprecated('Use cb08Descriptor instead')
const cb08$json = {
  '1': 'cb08',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'src', '3': 2, '4': 1, '5': 9, '10': 'src'},
    {'1': 'y_n', '3': 4, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'jt_ch', '3': 5, '4': 1, '5': 5, '10': 'jtCh'},
    {'1': 'time', '3': 6, '4': 1, '5': 5, '10': 'time'},
  ],
};

/// Descriptor for `cb08`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb08Descriptor = $convert.base64Decode(
    'CgRjYjA4Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IQCgNzcmMYAiABKAlSA3NyYxIPCgN5X24YBCABKAVSAnlOEhMKBWp0X2NoGAUgASgFUgRqdENo'
    'EhIKBHRpbWUYBiABKAVSBHRpbWU=');

@$core.Deprecated('Use cb09Descriptor instead')
const cb09$json = {
  '1': 'cb09',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'st', '3': 4, '4': 1, '5': 5, '10': 'st'},
  ],
};

/// Descriptor for `cb09`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb09Descriptor = $convert.base64Decode(
    'CgRjYjA5Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAyABKAVSAnlOEg4KAnN0GAQgASgFUgJzdA==');

@$core.Deprecated('Use cb10Descriptor instead')
const cb10$json = {
  '1': 'cb10',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
  ],
};

/// Descriptor for `cb10`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb10Descriptor = $convert.base64Decode(
    'CgRjYjEwEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldA'
    '==');

@$core.Deprecated('Use cb11Descriptor instead')
const cb11$json = {
  '1': 'cb11',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'c_tp', '3': 3, '4': 1, '5': 12, '10': 'cTp'},
    {'1': 'a_tp', '3': 4, '4': 1, '5': 5, '10': 'aTp'},
    {'1': 'dd_ch', '3': 5, '4': 1, '5': 5, '10': 'ddCh'},
    {'1': 'initiator', '3': 6, '4': 1, '5': 9, '10': 'initiator'},
    {'1': 'orig_cmd', '3': 7, '4': 1, '5': 12, '10': 'origCmd'},
  ],
};

/// Descriptor for `cb11`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb11Descriptor = $convert.base64Decode(
    'CgRjYjExEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAiABKAVSAnlOEhEKBGNfdHAYAyABKAxSA2NUcBIRCgRhX3RwGAQgASgFUgNhVHAS'
    'EwoFZGRfY2gYBSABKAVSBGRkQ2gSHAoJaW5pdGlhdG9yGAYgASgJUglpbml0aWF0b3ISGQoIb3'
    'JpZ19jbWQYByABKAxSB29yaWdDbWQ=');

@$core.Deprecated('Use cb12Descriptor instead')
const cb12$json = {
  '1': 'cb12',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'type', '3': 2, '4': 1, '5': 5, '10': 'type'},
    {'1': 'bs_id', '3': 3, '4': 1, '5': 5, '10': 'bsId'},
    {'1': 'bs_n', '3': 4, '4': 1, '5': 5, '10': 'bsN'},
    {'1': 'ch_id', '3': 5, '4': 1, '5': 5, '10': 'chId'},
    {'1': 'ch_n', '3': 6, '4': 1, '5': 5, '10': 'chN'},
    {'1': 'con_status', '3': 7, '4': 1, '5': 9, '10': 'conStatus'},
    {'1': 'myxb_TT', '3': 8, '4': 1, '5': 5, '10': 'myxbTT'},
    {'1': 'c_bs_n', '3': 9, '4': 1, '5': 9, '10': 'cBsN'},
    {'1': 'ch_dd_st', '3': 10, '4': 1, '5': 5, '10': 'chDdSt'},
  ],
};

/// Descriptor for `cb12`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb12Descriptor = $convert.base64Decode(
    'CgRjYjEyEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'ISCgR0eXBlGAIgASgFUgR0eXBlEhMKBWJzX2lkGAMgASgFUgRic0lkEhEKBGJzX24YBCABKAVS'
    'A2JzThITCgVjaF9pZBgFIAEoBVIEY2hJZBIRCgRjaF9uGAYgASgFUgNjaE4SHQoKY29uX3N0YX'
    'R1cxgHIAEoCVIJY29uU3RhdHVzEhcKB215eGJfVFQYCCABKAVSBm15eGJUVBIUCgZjX2JzX24Y'
    'CSABKAlSBGNCc04SGAoIY2hfZGRfc3QYCiABKAVSBmNoRGRTdA==');

@$core.Deprecated('Use cb17Descriptor instead')
const cb17$json = {
  '1': 'cb17',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'user_st', '3': 3, '4': 1, '5': 9, '10': 'userSt'},
    {'1': 'user_tp', '3': 4, '4': 1, '5': 5, '10': 'userTp'},
    {'1': 'user_addr_id', '3': 5, '4': 1, '5': 9, '10': 'userAddrId'},
    {'1': 'license', '3': 6, '4': 1, '5': 9, '10': 'license'},
    {'1': 'user_show_id', '3': 7, '4': 1, '5': 9, '10': 'userShowId'},
  ],
};

/// Descriptor for `cb17`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb17Descriptor = $convert.base64Decode(
    'CgRjYjE3Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAiABKAVSAnlOEhcKB3VzZXJfc3QYAyABKAlSBnVzZXJTdBIXCgd1c2VyX3RwGAQg'
    'ASgFUgZ1c2VyVHASIAoMdXNlcl9hZGRyX2lkGAUgASgJUgp1c2VyQWRkcklkEhgKB2xpY2Vuc2'
    'UYBiABKAlSB2xpY2Vuc2USIAoMdXNlcl9zaG93X2lkGAcgASgJUgp1c2VyU2hvd0lk');

@$core.Deprecated('Use cb19Descriptor instead')
const cb19$json = {
  '1': 'cb19',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'type', '3': 2, '4': 1, '5': 5, '10': 'type'},
    {'1': 'new_duty', '3': 3, '4': 1, '5': 9, '10': 'newDuty'},
    {'1': 'old_duty', '3': 4, '4': 1, '5': 9, '10': 'oldDuty'},
  ],
};

/// Descriptor for `cb19`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb19Descriptor = $convert.base64Decode(
    'CgRjYjE5Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'ISCgR0eXBlGAIgASgFUgR0eXBlEhkKCG5ld19kdXR5GAMgASgJUgduZXdEdXR5EhkKCG9sZF9k'
    'dXR5GAQgASgJUgdvbGREdXR5');

@$core.Deprecated('Use cb20Descriptor instead')
const cb20$json = {
  '1': 'cb20',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'link_tp', '3': 4, '4': 1, '5': 5, '10': 'linkTp'},
    {'1': 'a_tp', '3': 5, '4': 1, '5': 5, '10': 'aTp'},
    {'1': 'net_id', '3': 6, '4': 1, '5': 9, '10': 'netId'},
  ],
};

/// Descriptor for `cb20`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb20Descriptor = $convert.base64Decode(
    'CgRjYjIwEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAyABKAVSAnlOEhcKB2xpbmtfdHAYBCABKAVSBmxpbmtUcBIRCgRhX3RwGAUgASgF'
    'UgNhVHASFQoGbmV0X2lkGAYgASgJUgVuZXRJZA==');

@$core.Deprecated('Use cb21Descriptor instead')
const cb21$json = {
  '1': 'cb21',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 3, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'dd_ch', '3': 4, '4': 1, '5': 5, '10': 'ddCh'},
  ],
};

/// Descriptor for `cb21`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb21Descriptor = $convert.base64Decode(
    'CgRjYjIxEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAyABKAVSAnlOEhMKBWRkX2NoGAQgASgFUgRkZENo');

@$core.Deprecated('Use cb24Descriptor instead')
const cb24$json = {
  '1': 'cb24',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'data', '3': 3, '4': 1, '5': 9, '10': 'data'},
    {'1': 'code_tp', '3': 4, '4': 1, '5': 5, '10': 'codeTp'},
    {'1': 'schedule_send_time', '3': 5, '4': 1, '5': 9, '10': 'scheduleSendTime'},
  ],
};

/// Descriptor for `cb24`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb24Descriptor = $convert.base64Decode(
    'CgRjYjI0Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAiABKAVSAnlOEhIKBGRhdGEYAyABKAlSBGRhdGESFwoHY29kZV90cBgEIAEoBVIG'
    'Y29kZVRwEiwKEnNjaGVkdWxlX3NlbmRfdGltZRgFIAEoCVIQc2NoZWR1bGVTZW5kVGltZQ==');

@$core.Deprecated('Use cb25Descriptor instead')
const cb25$json = {
  '1': 'cb25',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'point_n', '3': 3, '4': 1, '5': 5, '10': 'pointN'},
    {'1': 'point_card', '3': 4, '4': 1, '5': 9, '10': 'pointCard'},
    {'1': 'lat', '3': 5, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'lon', '3': 6, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat_dif', '3': 7, '4': 1, '5': 9, '10': 'latDif'},
    {'1': 'lon_dif', '3': 8, '4': 1, '5': 9, '10': 'lonDif'},
  ],
};

/// Descriptor for `cb25`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb25Descriptor = $convert.base64Decode(
    'CgRjYjI1Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAiABKAVSAnlOEhcKB3BvaW50X24YAyABKAVSBnBvaW50ThIdCgpwb2ludF9jYXJk'
    'GAQgASgJUglwb2ludENhcmQSEAoDbGF0GAUgASgBUgNsYXQSEAoDbG9uGAYgASgBUgNsb24SFw'
    'oHbGF0X2RpZhgHIAEoCVIGbGF0RGlmEhcKB2xvbl9kaWYYCCABKAlSBmxvbkRpZg==');

@$core.Deprecated('Use cb26Descriptor instead')
const cb26$json = {
  '1': 'cb26',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'y_n', '3': 2, '4': 1, '5': 5, '10': 'yN'},
    {'1': 'ch', '3': 3, '4': 1, '5': 5, '10': 'ch'},
    {'1': 'ch_name', '3': 5, '4': 1, '5': 9, '10': 'chName'},
  ],
};

/// Descriptor for `cb26`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb26Descriptor = $convert.base64Decode(
    'CgRjYjI2Ei8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'IPCgN5X24YAiABKAVSAnlOEg4KAmNoGAMgASgFUgJjaBIXCgdjaF9uYW1lGAUgASgJUgZjaE5h'
    'bWU=');

@$core.Deprecated('Use cb42Descriptor instead')
const cb42$json = {
  '1': 'cb42',
  '2': [
    {'1': 'target', '3': 1, '4': 1, '5': 11, '6': '.bfdx_proto.cbxx_target', '10': 'target'},
    {'1': 'code', '3': 2, '4': 1, '5': 5, '10': 'code'},
  ],
};

/// Descriptor for `cb42`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cb42Descriptor = $convert.base64Decode(
    'CgRjYjQyEi8KBnRhcmdldBgBIAEoCzIXLmJmZHhfcHJvdG8uY2J4eF90YXJnZXRSBnRhcmdldB'
    'ISCgRjb2RlGAIgASgFUgRjb2Rl');

@$core.Deprecated('Use cbxx_send_stubDescriptor instead')
const cbxx_send_stub$json = {
  '1': 'cbxx_send_stub',
  '2': [
    {'1': 'cbxx', '3': 1, '4': 1, '5': 9, '10': 'cbxx'},
    {'1': 'target_group', '3': 2, '4': 3, '5': 9, '10': 'targetGroup'},
    {'1': 'target_group_seq_no', '3': 3, '4': 3, '5': 9, '10': 'targetGroupSeqNo'},
    {'1': 'target_device', '3': 4, '4': 3, '5': 9, '10': 'targetDevice'},
    {'1': 'target_device_seq_no', '3': 5, '4': 3, '5': 9, '10': 'targetDeviceSeqNo'},
  ],
};

/// Descriptor for `cbxx_send_stub`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cbxx_send_stubDescriptor = $convert.base64Decode(
    'Cg5jYnh4X3NlbmRfc3R1YhISCgRjYnh4GAEgASgJUgRjYnh4EiEKDHRhcmdldF9ncm91cBgCIA'
    'MoCVILdGFyZ2V0R3JvdXASLQoTdGFyZ2V0X2dyb3VwX3NlcV9ubxgDIAMoCVIQdGFyZ2V0R3Jv'
    'dXBTZXFObxIjCg10YXJnZXRfZGV2aWNlGAQgAygJUgx0YXJnZXREZXZpY2USLwoUdGFyZ2V0X2'
    'RldmljZV9zZXFfbm8YBSADKAlSEXRhcmdldERldmljZVNlcU5v');

@$core.Deprecated('Use cc01Descriptor instead')
const cc01$json = {
  '1': 'cc01',
  '2': [
    {'1': 'action_code', '3': 1, '4': 1, '5': 5, '10': 'actionCode'},
    {'1': 'dmr_id', '3': 2, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'ccxx_str', '3': 3, '4': 1, '5': 9, '10': 'ccxxStr'},
  ],
};

/// Descriptor for `cc01`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cc01Descriptor = $convert.base64Decode(
    'CgRjYzAxEh8KC2FjdGlvbl9jb2RlGAEgASgFUgphY3Rpb25Db2RlEhUKBmRtcl9pZBgCIAEoCV'
    'IFZG1ySWQSGQoIY2N4eF9zdHIYAyABKAlSB2NjeHhTdHI=');

@$core.Deprecated('Use cc81Descriptor instead')
const cc81$json = {
  '1': 'cc81',
  '2': [
    {'1': 'target_dmr_id', '3': 1, '4': 1, '5': 13, '10': 'targetDmrId'},
    {'1': 'apply_dmr_id', '3': 2, '4': 1, '5': 13, '10': 'applyDmrId'},
  ],
};

/// Descriptor for `cc81`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cc81Descriptor = $convert.base64Decode(
    'CgRjYzgxEiIKDXRhcmdldF9kbXJfaWQYASABKA1SC3RhcmdldERtcklkEiAKDGFwcGx5X2Rtcl'
    '9pZBgCIAEoDVIKYXBwbHlEbXJJZA==');

@$core.Deprecated('Use res_gps_permissionDescriptor instead')
const res_gps_permission$json = {
  '1': 'res_gps_permission',
  '2': [
    {'1': 'code', '3': 1, '4': 1, '5': 5, '10': 'code'},
    {'1': 'dmrid', '3': 2, '4': 1, '5': 13, '10': 'dmrid'},
    {'1': 'grant_user_rid', '3': 3, '4': 1, '5': 9, '10': 'grantUserRid'},
    {'1': 'grant_user_name', '3': 4, '4': 1, '5': 9, '10': 'grantUserName'},
    {'1': 'expire_time', '3': 5, '4': 1, '5': 9, '10': 'expireTime'},
  ],
};

/// Descriptor for `res_gps_permission`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List res_gps_permissionDescriptor = $convert.base64Decode(
    'ChJyZXNfZ3BzX3Blcm1pc3Npb24SEgoEY29kZRgBIAEoBVIEY29kZRIUCgVkbXJpZBgCIAEoDV'
    'IFZG1yaWQSJAoOZ3JhbnRfdXNlcl9yaWQYAyABKAlSDGdyYW50VXNlclJpZBImCg9ncmFudF91'
    'c2VyX25hbWUYBCABKAlSDWdyYW50VXNlck5hbWUSHwoLZXhwaXJlX3RpbWUYBSABKAlSCmV4cG'
    'lyZVRpbWU=');

@$core.Deprecated('Use cc83Descriptor instead')
const cc83$json = {
  '1': 'cc83',
  '2': [
    {'1': 'action_code', '3': 1, '4': 1, '5': 5, '10': 'actionCode'},
    {'1': 'hex_dmrids', '3': 2, '4': 3, '5': 9, '10': 'hexDmrids'},
    {'1': 'models', '3': 3, '4': 3, '5': 9, '10': 'models'},
    {'1': 'functions', '3': 4, '4': 3, '5': 9, '10': 'functions'},
  ],
};

/// Descriptor for `cc83`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cc83Descriptor = $convert.base64Decode(
    'CgRjYzgzEh8KC2FjdGlvbl9jb2RlGAEgASgFUgphY3Rpb25Db2RlEh0KCmhleF9kbXJpZHMYAi'
    'ADKAlSCWhleERtcmlkcxIWCgZtb2RlbHMYAyADKAlSBm1vZGVscxIcCglmdW5jdGlvbnMYBCAD'
    'KAlSCWZ1bmN0aW9ucw==');

@$core.Deprecated('Use cc183Descriptor instead')
const cc183$json = {
  '1': 'cc183',
  '2': [
    {'1': 'action_code', '3': 1, '4': 1, '5': 5, '10': 'actionCode'},
    {'1': 'dmrids', '3': 2, '4': 3, '5': 13, '10': 'dmrids'},
    {'1': 'last_data_times', '3': 3, '4': 3, '5': 3, '10': 'lastDataTimes'},
  ],
};

/// Descriptor for `cc183`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cc183Descriptor = $convert.base64Decode(
    'CgVjYzE4MxIfCgthY3Rpb25fY29kZRgBIAEoBVIKYWN0aW9uQ29kZRIWCgZkbXJpZHMYAiADKA'
    '1SBmRtcmlkcxImCg9sYXN0X2RhdGFfdGltZXMYAyADKANSDWxhc3REYXRhVGltZXM=');

@$core.Deprecated('Use unknown_iot_device_cmdDescriptor instead')
const unknown_iot_device_cmd$json = {
  '1': 'unknown_iot_device_cmd',
  '2': [
    {'1': 'dev_type', '3': 1, '4': 1, '5': 5, '10': 'devType'},
    {'1': 'dev_id', '3': 2, '4': 1, '5': 9, '10': 'devId'},
  ],
};

/// Descriptor for `unknown_iot_device_cmd`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List unknown_iot_device_cmdDescriptor = $convert.base64Decode(
    'ChZ1bmtub3duX2lvdF9kZXZpY2VfY21kEhkKCGRldl90eXBlGAEgASgFUgdkZXZUeXBlEhUKBm'
    'Rldl9pZBgCIAEoCVIFZGV2SWQ=');

@$core.Deprecated('Use over_step_base_stationDescriptor instead')
const over_step_base_station$json = {
  '1': 'over_step_base_station',
  '2': [
    {'1': 'dev_type', '3': 1, '4': 1, '5': 5, '10': 'devType'},
    {'1': 'dev_id', '3': 2, '4': 1, '5': 9, '10': 'devId'},
    {'1': 'over_step_type', '3': 4, '4': 1, '5': 5, '10': 'overStepType'},
  ],
};

/// Descriptor for `over_step_base_station`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List over_step_base_stationDescriptor = $convert.base64Decode(
    'ChZvdmVyX3N0ZXBfYmFzZV9zdGF0aW9uEhkKCGRldl90eXBlGAEgASgFUgdkZXZUeXBlEhUKBm'
    'Rldl9pZBgCIAEoCVIFZGV2SWQSJAoOb3Zlcl9zdGVwX3R5cGUYBCABKAVSDG92ZXJTdGVwVHlw'
    'ZQ==');

@$core.Deprecated('Use sipGroupSubscribeOperationDescriptor instead')
const SipGroupSubscribeOperation$json = {
  '1': 'SipGroupSubscribeOperation',
  '2': [
    {'1': 'operation', '3': 1, '4': 1, '5': 5, '10': 'operation'},
    {'1': 'dmrids', '3': 2, '4': 3, '5': 9, '10': 'dmrids'},
    {'1': 'res', '3': 3, '4': 1, '5': 5, '10': 'res'},
    {'1': 'res_msg', '3': 4, '4': 1, '5': 9, '10': 'resMsg'},
  ],
};

/// Descriptor for `SipGroupSubscribeOperation`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sipGroupSubscribeOperationDescriptor = $convert.base64Decode(
    'ChpTaXBHcm91cFN1YnNjcmliZU9wZXJhdGlvbhIcCglvcGVyYXRpb24YASABKAVSCW9wZXJhdG'
    'lvbhIWCgZkbXJpZHMYAiADKAlSBmRtcmlkcxIQCgNyZXMYAyABKAVSA3JlcxIXCgdyZXNfbXNn'
    'GAQgASgJUgZyZXNNc2c=');

