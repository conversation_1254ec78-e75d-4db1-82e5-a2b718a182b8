//
//  Generated code. Do not modify.
//  source: db.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

/// 系统设置表
class db_sys_config extends $pb.GeneratedMessage {
  factory db_sys_config({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? confKey,
    $core.String? confValue,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (confKey != null) {
      $result.confKey = confKey;
    }
    if (confValue != null) {
      $result.confValue = confValue;
    }
    return $result;
  }
  db_sys_config._() : super();
  factory db_sys_config.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_sys_config.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_sys_config', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'confKey')
    ..aOS(4, _omitFieldNames ? '' : 'confValue')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_sys_config clone() => db_sys_config()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_sys_config copyWith(void Function(db_sys_config) updates) => super.copyWith((message) => updates(message as db_sys_config)) as db_sys_config;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_sys_config create() => db_sys_config._();
  db_sys_config createEmptyInstance() => create();
  static $pb.PbList<db_sys_config> createRepeated() => $pb.PbList<db_sys_config>();
  @$core.pragma('dart2js:noInline')
  static db_sys_config getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_sys_config>(create);
  static db_sys_config? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table varchar(64) unique not null
  /// 配置名
  @$pb.TagNumber(3)
  $core.String get confKey => $_getSZ(2);
  @$pb.TagNumber(3)
  set confKey($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasConfKey() => $_has(2);
  @$pb.TagNumber(3)
  void clearConfKey() => clearField(3);

  /// @table text
  /// 配置的值
  @$pb.TagNumber(4)
  $core.String get confValue => $_getSZ(3);
  @$pb.TagNumber(4)
  set confValue($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasConfValue() => $_has(3);
  @$pb.TagNumber(4)
  void clearConfValue() => clearField(4);
}

/// 表各种操作时间
/// 客户端有时需要查询后台是否已经更新了数据,可以通过此表来得到初步的信息
class db_table_operate_time extends $pb.GeneratedMessage {
  factory db_table_operate_time({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? tableName,
    $core.String? lastModifyTime,
    $core.int? lastModifyOperation,
    $core.int? lastModifyRows,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (tableName != null) {
      $result.tableName = tableName;
    }
    if (lastModifyTime != null) {
      $result.lastModifyTime = lastModifyTime;
    }
    if (lastModifyOperation != null) {
      $result.lastModifyOperation = lastModifyOperation;
    }
    if (lastModifyRows != null) {
      $result.lastModifyRows = lastModifyRows;
    }
    return $result;
  }
  db_table_operate_time._() : super();
  factory db_table_operate_time.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_table_operate_time.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_table_operate_time', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'tableName')
    ..aOS(4, _omitFieldNames ? '' : 'lastModifyTime')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'lastModifyOperation', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'lastModifyRows', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_table_operate_time clone() => db_table_operate_time()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_table_operate_time copyWith(void Function(db_table_operate_time) updates) => super.copyWith((message) => updates(message as db_table_operate_time)) as db_table_operate_time;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_table_operate_time create() => db_table_operate_time._();
  db_table_operate_time createEmptyInstance() => create();
  static $pb.PbList<db_table_operate_time> createRepeated() => $pb.PbList<db_table_operate_time>();
  @$core.pragma('dart2js:noInline')
  static db_table_operate_time getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_table_operate_time>(create);
  static db_table_operate_time? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table varchar(64) unique
  /// 数据库表名
  @$pb.TagNumber(3)
  $core.String get tableName => $_getSZ(2);
  @$pb.TagNumber(3)
  set tableName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTableName() => $_has(2);
  @$pb.TagNumber(3)
  void clearTableName() => clearField(3);

  /// @table timestamp
  /// 最后修改时间
  @$pb.TagNumber(4)
  $core.String get lastModifyTime => $_getSZ(3);
  @$pb.TagNumber(4)
  set lastModifyTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastModifyTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearLastModifyTime() => clearField(4);

  /// @table int
  /// 最后修改的操作 1:insert 2:update 3:delete
  @$pb.TagNumber(5)
  $core.int get lastModifyOperation => $_getIZ(4);
  @$pb.TagNumber(5)
  set lastModifyOperation($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLastModifyOperation() => $_has(4);
  @$pb.TagNumber(5)
  void clearLastModifyOperation() => clearField(5);

  /// @table int
  /// 最后修改时影响的行数
  @$pb.TagNumber(6)
  $core.int get lastModifyRows => $_getIZ(5);
  @$pb.TagNumber(6)
  set lastModifyRows($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLastModifyRows() => $_has(5);
  @$pb.TagNumber(6)
  void clearLastModifyRows() => clearField(6);
}

/// 组织架构表
class db_org extends $pb.GeneratedMessage {
  factory db_org({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgSelfId,
    $core.int? orgSortValue,
    $core.String? orgShortName,
    $core.String? orgFullName,
    $core.String? note,
    $core.int? orgIsVirtual,
    $core.String? dmrId,
    $core.String? orgImg,
    $core.String? parentOrgId,
    $core.String? setting,
    $core.int? dynamicGroupState,
    $core.String? creator,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgSelfId != null) {
      $result.orgSelfId = orgSelfId;
    }
    if (orgSortValue != null) {
      $result.orgSortValue = orgSortValue;
    }
    if (orgShortName != null) {
      $result.orgShortName = orgShortName;
    }
    if (orgFullName != null) {
      $result.orgFullName = orgFullName;
    }
    if (note != null) {
      $result.note = note;
    }
    if (orgIsVirtual != null) {
      $result.orgIsVirtual = orgIsVirtual;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (orgImg != null) {
      $result.orgImg = orgImg;
    }
    if (parentOrgId != null) {
      $result.parentOrgId = parentOrgId;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    if (dynamicGroupState != null) {
      $result.dynamicGroupState = dynamicGroupState;
    }
    if (creator != null) {
      $result.creator = creator;
    }
    return $result;
  }
  db_org._() : super();
  factory db_org.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_org.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_org', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgSelfId')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'orgSortValue', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'orgShortName')
    ..aOS(6, _omitFieldNames ? '' : 'orgFullName')
    ..aOS(7, _omitFieldNames ? '' : 'note')
    ..a<$core.int>(8, _omitFieldNames ? '' : 'orgIsVirtual', $pb.PbFieldType.O3)
    ..aOS(9, _omitFieldNames ? '' : 'dmrId')
    ..aOS(10, _omitFieldNames ? '' : 'orgImg')
    ..aOS(11, _omitFieldNames ? '' : 'parentOrgId')
    ..aOS(12, _omitFieldNames ? '' : 'setting')
    ..a<$core.int>(13, _omitFieldNames ? '' : 'dynamicGroupState', $pb.PbFieldType.O3)
    ..aOS(14, _omitFieldNames ? '' : 'creator')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_org clone() => db_org()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_org copyWith(void Function(db_org) updates) => super.copyWith((message) => updates(message as db_org)) as db_org;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_org create() => db_org._();
  db_org createEmptyInstance() => create();
  static $pb.PbList<db_org> createRepeated() => $pb.PbList<db_org>();
  @$core.pragma('dart2js:noInline')
  static db_org getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_org>(create);
  static db_org? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table varchar(64) unique not null
  /// 组织机构编号
  @$pb.TagNumber(3)
  $core.String get orgSelfId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgSelfId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgSelfId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgSelfId() => clearField(3);

  /// @table int default 100
  /// 排序值,由用户指定,不用管拼音或者其它语种的排序
  @$pb.TagNumber(4)
  $core.int get orgSortValue => $_getIZ(3);
  @$pb.TagNumber(4)
  set orgSortValue($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasOrgSortValue() => $_has(3);
  @$pb.TagNumber(4)
  void clearOrgSortValue() => clearField(4);

  /// @table varchar(32) unique not null
  /// 机构名称,缩写
  @$pb.TagNumber(5)
  $core.String get orgShortName => $_getSZ(4);
  @$pb.TagNumber(5)
  set orgShortName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasOrgShortName() => $_has(4);
  @$pb.TagNumber(5)
  void clearOrgShortName() => clearField(5);

  /// @table varchar(256)
  /// 机构名称,全称
  @$pb.TagNumber(6)
  $core.String get orgFullName => $_getSZ(5);
  @$pb.TagNumber(6)
  set orgFullName($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasOrgFullName() => $_has(5);
  @$pb.TagNumber(6)
  void clearOrgFullName() => clearField(6);

  /// @table text
  /// 机构详细描述
  @$pb.TagNumber(7)
  $core.String get note => $_getSZ(6);
  @$pb.TagNumber(7)
  set note($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasNote() => $_has(6);
  @$pb.TagNumber(7)
  void clearNote() => clearField(7);

  /// @table int default 2
  /// 2:真实机构,1:虚拟机构 虚拟机构没有真实设备和用户,只是引用真实机构的数据,相当于额外的分组 100:临时组 101：任务组 102:自动失效的临时组
  @$pb.TagNumber(8)
  $core.int get orgIsVirtual => $_getIZ(7);
  @$pb.TagNumber(8)
  set orgIsVirtual($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasOrgIsVirtual() => $_has(7);
  @$pb.TagNumber(8)
  void clearOrgIsVirtual() => clearField(8);

  /// @table varchar(8) unique
  /// DMR ID,可用作组呼的ID
  @$pb.TagNumber(9)
  $core.String get dmrId => $_getSZ(8);
  @$pb.TagNumber(9)
  set dmrId($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasDmrId() => $_has(8);
  @$pb.TagNumber(9)
  void clearDmrId() => clearField(9);

  /// @table uuid default '11111111-1111-1111-1111-111111111111'
  /// 组织机构的图标
  @$pb.TagNumber(10)
  $core.String get orgImg => $_getSZ(9);
  @$pb.TagNumber(10)
  set orgImg($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasOrgImg() => $_has(9);
  @$pb.TagNumber(10)
  void clearOrgImg() => clearField(10);

  /// @table uuid not null default '11111111-1111-1111-1111-111111111111'
  /// 此组织的上级机构device
  @$pb.TagNumber(11)
  $core.String get parentOrgId => $_getSZ(10);
  @$pb.TagNumber(11)
  set parentOrgId($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasParentOrgId() => $_has(10);
  @$pb.TagNumber(11)
  void clearParentOrgId() => clearField(11);

  /// @table jsonb not null default '{}'::jsonb
  /// 如果是prochat网关产生的动态组,则有prochatID:{"prochatID":12345}
  /// org的配置信息
  @$pb.TagNumber(12)
  $core.String get setting => $_getSZ(11);
  @$pb.TagNumber(12)
  set setting($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasSetting() => $_has(11);
  @$pb.TagNumber(12)
  void clearSetting() => clearField(12);

  /// @table int default 0
  /// 动态组状态  1:正常 10:失效 / 删除中
  @$pb.TagNumber(13)
  $core.int get dynamicGroupState => $_getIZ(12);
  @$pb.TagNumber(13)
  set dynamicGroupState($core.int v) { $_setSignedInt32(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasDynamicGroupState() => $_has(12);
  @$pb.TagNumber(13)
  void clearDynamicGroupState() => clearField(13);

  /// @table uuid
  /// 创建者优先级
  @$pb.TagNumber(14)
  $core.String get creator => $_getSZ(13);
  @$pb.TagNumber(14)
  set creator($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasCreator() => $_has(13);
  @$pb.TagNumber(14)
  void clearCreator() => clearField(14);
}

/// 用户的一些图片数据,地图点icon等
class db_image extends $pb.GeneratedMessage {
  factory db_image({
    $core.String? rid,
    $core.String? orgId,
    $core.String? fileName,
    $core.String? fileContent,
    $core.String? hash,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (fileName != null) {
      $result.fileName = fileName;
    }
    if (fileContent != null) {
      $result.fileContent = fileContent;
    }
    if (hash != null) {
      $result.hash = hash;
    }
    return $result;
  }
  db_image._() : super();
  factory db_image.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_image.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_image', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'fileName')
    ..aOS(5, _omitFieldNames ? '' : 'fileContent')
    ..aOS(6, _omitFieldNames ? '' : 'hash')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_image clone() => db_image()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_image copyWith(void Function(db_image) updates) => super.copyWith((message) => updates(message as db_image)) as db_image;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_image create() => db_image._();
  db_image createEmptyInstance() => create();
  static $pb.PbList<db_image> createRepeated() => $pb.PbList<db_image>();
  @$core.pragma('dart2js:noInline')
  static db_image getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_image>(create);
  static db_image? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table text
  /// 文件名
  @$pb.TagNumber(4)
  $core.String get fileName => $_getSZ(2);
  @$pb.TagNumber(4)
  set fileName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasFileName() => $_has(2);
  @$pb.TagNumber(4)
  void clearFileName() => clearField(4);

  /// @table text
  /// 文件内容,经过base64编码,就是html img的dataurl
  @$pb.TagNumber(5)
  $core.String get fileContent => $_getSZ(3);
  @$pb.TagNumber(5)
  set fileContent($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasFileContent() => $_has(3);
  @$pb.TagNumber(5)
  void clearFileContent() => clearField(5);

  /// @table text
  /// 文件内容的base64(sha256(file_content)
  @$pb.TagNumber(6)
  $core.String get hash => $_getSZ(4);
  @$pb.TagNumber(6)
  set hash($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasHash() => $_has(4);
  @$pb.TagNumber(6)
  void clearHash() => clearField(6);
}

/// 基站列表
class db_base_station extends $pb.GeneratedMessage {
  factory db_base_station({
    $core.String? rid,
    $core.String? orgId,
    $core.String? updateAt,
    $core.String? selfId,
    $core.String? baseStationName,
    $core.double? lon,
    $core.double? lat,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (selfId != null) {
      $result.selfId = selfId;
    }
    if (baseStationName != null) {
      $result.baseStationName = baseStationName;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    return $result;
  }
  db_base_station._() : super();
  factory db_base_station.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_base_station.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_base_station', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'updateAt')
    ..aOS(4, _omitFieldNames ? '' : 'selfId')
    ..aOS(5, _omitFieldNames ? '' : 'baseStationName')
    ..a<$core.double>(6, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..a<$core.double>(7, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_base_station clone() => db_base_station()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_base_station copyWith(void Function(db_base_station) updates) => super.copyWith((message) => updates(message as db_base_station)) as db_base_station;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_base_station create() => db_base_station._();
  db_base_station createEmptyInstance() => create();
  static $pb.PbList<db_base_station> createRepeated() => $pb.PbList<db_base_station>();
  @$core.pragma('dart2js:noInline')
  static db_base_station getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_base_station>(create);
  static db_base_station? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(3)
  $core.String get updateAt => $_getSZ(2);
  @$pb.TagNumber(3)
  set updateAt($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUpdateAt() => $_has(2);
  @$pb.TagNumber(3)
  void clearUpdateAt() => clearField(3);

  /// @table varchar(16) not null
  /// 基站编号
  @$pb.TagNumber(4)
  $core.String get selfId => $_getSZ(3);
  @$pb.TagNumber(4)
  set selfId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSelfId() => $_has(3);
  @$pb.TagNumber(4)
  void clearSelfId() => clearField(4);

  /// @table varchar(16) not null
  @$pb.TagNumber(5)
  $core.String get baseStationName => $_getSZ(4);
  @$pb.TagNumber(5)
  set baseStationName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasBaseStationName() => $_has(4);
  @$pb.TagNumber(5)
  void clearBaseStationName() => clearField(5);

  /// @table double precision
  /// 经度
  @$pb.TagNumber(6)
  $core.double get lon => $_getN(5);
  @$pb.TagNumber(6)
  set lon($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLon() => $_has(5);
  @$pb.TagNumber(6)
  void clearLon() => clearField(6);

  /// @table double precision
  /// 纬度
  @$pb.TagNumber(7)
  $core.double get lat => $_getN(6);
  @$pb.TagNumber(7)
  set lat($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLat() => $_has(6);
  @$pb.TagNumber(7)
  void clearLat() => clearField(7);
}

/// 控制器设备表
class db_controller extends $pb.GeneratedMessage {
  factory db_controller({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? baseStationRid,
    $core.String? selfId,
    $core.String? dmrId,
    $core.double? lon,
    $core.double? lat,
    $core.String? note,
    $core.String? setting,
    $core.String? signalAera,
    $core.String? simInfo,
    $core.String? room,
    $core.String? sipNo,
    $core.int? netTimeSlot,
    $core.int? controllerType,
    $core.String? location,
    $core.bool? canTalk,
    $core.String? simulcastParent,
    $core.int? traditionalDmrAllowNetCall,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (baseStationRid != null) {
      $result.baseStationRid = baseStationRid;
    }
    if (selfId != null) {
      $result.selfId = selfId;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (note != null) {
      $result.note = note;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    if (signalAera != null) {
      $result.signalAera = signalAera;
    }
    if (simInfo != null) {
      $result.simInfo = simInfo;
    }
    if (room != null) {
      $result.room = room;
    }
    if (sipNo != null) {
      $result.sipNo = sipNo;
    }
    if (netTimeSlot != null) {
      $result.netTimeSlot = netTimeSlot;
    }
    if (controllerType != null) {
      $result.controllerType = controllerType;
    }
    if (location != null) {
      $result.location = location;
    }
    if (canTalk != null) {
      $result.canTalk = canTalk;
    }
    if (simulcastParent != null) {
      $result.simulcastParent = simulcastParent;
    }
    if (traditionalDmrAllowNetCall != null) {
      $result.traditionalDmrAllowNetCall = traditionalDmrAllowNetCall;
    }
    return $result;
  }
  db_controller._() : super();
  factory db_controller.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_controller.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_controller', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'baseStationRid')
    ..aOS(5, _omitFieldNames ? '' : 'selfId')
    ..aOS(6, _omitFieldNames ? '' : 'dmrId')
    ..a<$core.double>(7, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..a<$core.double>(8, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..aOS(9, _omitFieldNames ? '' : 'note')
    ..aOS(10, _omitFieldNames ? '' : 'setting')
    ..aOS(11, _omitFieldNames ? '' : 'signalAera')
    ..aOS(12, _omitFieldNames ? '' : 'simInfo')
    ..aOS(13, _omitFieldNames ? '' : 'room')
    ..aOS(14, _omitFieldNames ? '' : 'sipNo')
    ..a<$core.int>(15, _omitFieldNames ? '' : 'netTimeSlot', $pb.PbFieldType.O3)
    ..a<$core.int>(16, _omitFieldNames ? '' : 'controllerType', $pb.PbFieldType.O3)
    ..aOS(17, _omitFieldNames ? '' : 'location')
    ..aOB(18, _omitFieldNames ? '' : 'canTalk')
    ..aOS(19, _omitFieldNames ? '' : 'simulcastParent')
    ..a<$core.int>(20, _omitFieldNames ? '' : 'traditionalDmrAllowNetCall', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_controller clone() => db_controller()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_controller copyWith(void Function(db_controller) updates) => super.copyWith((message) => updates(message as db_controller)) as db_controller;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_controller create() => db_controller._();
  db_controller createEmptyInstance() => create();
  static $pb.PbList<db_controller> createRepeated() => $pb.PbList<db_controller>();
  @$core.pragma('dart2js:noInline')
  static db_controller getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_controller>(create);
  static db_controller? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  /// 设备所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table text default ''
  /// 所属的基站
  @$pb.TagNumber(4)
  $core.String get baseStationRid => $_getSZ(3);
  @$pb.TagNumber(4)
  set baseStationRid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasBaseStationRid() => $_has(3);
  @$pb.TagNumber(4)
  void clearBaseStationRid() => clearField(4);

  /// @table varchar(16) not null
  /// 控制器编号
  @$pb.TagNumber(5)
  $core.String get selfId => $_getSZ(4);
  @$pb.TagNumber(5)
  set selfId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSelfId() => $_has(4);
  @$pb.TagNumber(5)
  void clearSelfId() => clearField(5);

  /// @table text not null unique
  /// 控制器DMR-ID
  @$pb.TagNumber(6)
  $core.String get dmrId => $_getSZ(5);
  @$pb.TagNumber(6)
  set dmrId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDmrId() => $_has(5);
  @$pb.TagNumber(6)
  void clearDmrId() => clearField(6);

  /// @table double precision
  /// 经度
  @$pb.TagNumber(7)
  $core.double get lon => $_getN(6);
  @$pb.TagNumber(7)
  set lon($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLon() => $_has(6);
  @$pb.TagNumber(7)
  void clearLon() => clearField(7);

  /// @table double precision
  /// 纬度
  @$pb.TagNumber(8)
  $core.double get lat => $_getN(7);
  @$pb.TagNumber(8)
  set lat($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLat() => $_has(7);
  @$pb.TagNumber(8)
  void clearLat() => clearField(8);

  /// @table text
  /// 控制器的详细描述
  @$pb.TagNumber(9)
  $core.String get note => $_getSZ(8);
  @$pb.TagNumber(9)
  set note($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasNote() => $_has(8);
  @$pb.TagNumber(9)
  void clearNote() => clearField(9);

  /// @table jsonb not null default '{}'::jsonb
  /// 控制器设备的配置信息
  @$pb.TagNumber(10)
  $core.String get setting => $_getSZ(9);
  @$pb.TagNumber(10)
  set setting($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasSetting() => $_has(9);
  @$pb.TagNumber(10)
  void clearSetting() => clearField(10);

  /// @table jsonb not null default '{}'::jsonb
  /// 控制器信号覆盖范围,point_array,polyline
  @$pb.TagNumber(11)
  $core.String get signalAera => $_getSZ(10);
  @$pb.TagNumber(11)
  set signalAera($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasSignalAera() => $_has(10);
  @$pb.TagNumber(11)
  void clearSignalAera() => clearField(11);

  /// @table jsonb not null  default '{}'::jsonb
  /// {'valid':1,'phone_number':'123456789j0','fee_start':'2016-10-01','fee_end':'2017-01-01','alert_ahead_days':7}
  /// 控制器sim卡信息
  @$pb.TagNumber(12)
  $core.String get simInfo => $_getSZ(11);
  @$pb.TagNumber(12)
  set simInfo($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasSimInfo() => $_has(11);
  @$pb.TagNumber(12)
  void clearSimInfo() => clearField(12);

  /// @table text not null default '0'
  /// 默认的语音会议室
  @$pb.TagNumber(13)
  $core.String get room => $_getSZ(12);
  @$pb.TagNumber(13)
  set room($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasRoom() => $_has(12);
  @$pb.TagNumber(13)
  void clearRoom() => clearField(13);

  /// @table text  unique
  /// 预分配的voip电话号码
  @$pb.TagNumber(14)
  $core.String get sipNo => $_getSZ(13);
  @$pb.TagNumber(14)
  set sipNo($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasSipNo() => $_has(13);
  @$pb.TagNumber(14)
  void clearSipNo() => clearField(14);

  /// @table int
  /// 最大可用的联网时隙
  @$pb.TagNumber(15)
  $core.int get netTimeSlot => $_getIZ(14);
  @$pb.TagNumber(15)
  set netTimeSlot($core.int v) { $_setSignedInt32(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasNetTimeSlot() => $_has(14);
  @$pb.TagNumber(15)
  void clearNetTimeSlot() => clearField(15);

  /// @table int default 0
  /// 控制器类型 0:中继 1:同播中继 2:电话网关(tg810网关) 3:同播控制器 4: 虚拟集群控制器 5: 虚拟集群中继 10:sip网关 12:mesh网关 14:prochat网关 16:sip server网关
  /// prochat 网关rid固定为'14141414-1414-1414-1414-141414141414'，包括相应的终端和用户rid也一样
  /// sip server网关rid固定为'16161616-1616-1616-1616-161616161616'
  @$pb.TagNumber(16)
  $core.int get controllerType => $_getIZ(15);
  @$pb.TagNumber(16)
  set controllerType($core.int v) { $_setSignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasControllerType() => $_has(15);
  @$pb.TagNumber(16)
  void clearControllerType() => clearField(16);

  /// @table text
  /// 安装位置/地址
  @$pb.TagNumber(17)
  $core.String get location => $_getSZ(16);
  @$pb.TagNumber(17)
  set location($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasLocation() => $_has(16);
  @$pb.TagNumber(17)
  void clearLocation() => clearField(17);

  /// @table boolean default false
  /// 中继是否带插话功能,带的话需要相同dmrid的终端存在
  @$pb.TagNumber(18)
  $core.bool get canTalk => $_getBF(17);
  @$pb.TagNumber(18)
  set canTalk($core.bool v) { $_setBool(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasCanTalk() => $_has(17);
  @$pb.TagNumber(18)
  void clearCanTalk() => clearField(18);

  /// @table uuid default '00000000-0000-0000-0000-000000000000'
  ///  同播中继上级控制器rid
  @$pb.TagNumber(19)
  $core.String get simulcastParent => $_getSZ(18);
  @$pb.TagNumber(19)
  set simulcastParent($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasSimulcastParent() => $_has(18);
  @$pb.TagNumber(19)
  void clearSimulcastParent() => clearField(19);

  /// @table int not null default 0
  /// 常规dmr手台是否允许联网呼叫
  @$pb.TagNumber(20)
  $core.int get traditionalDmrAllowNetCall => $_getIZ(19);
  @$pb.TagNumber(20)
  set traditionalDmrAllowNetCall($core.int v) { $_setSignedInt32(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasTraditionalDmrAllowNetCall() => $_has(19);
  @$pb.TagNumber(20)
  void clearTraditionalDmrAllowNetCall() => clearField(20);
}

/// 控制器状态
class db_controller_last_info extends $pb.GeneratedMessage {
  factory db_controller_last_info({
    $core.String? rid,
    $core.String? orgId,
    $core.String? lastDataTime,
    $core.int? connected,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (lastDataTime != null) {
      $result.lastDataTime = lastDataTime;
    }
    if (connected != null) {
      $result.connected = connected;
    }
    return $result;
  }
  db_controller_last_info._() : super();
  factory db_controller_last_info.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_controller_last_info.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_controller_last_info', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'lastDataTime')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'connected', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_controller_last_info clone() => db_controller_last_info()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_controller_last_info copyWith(void Function(db_controller_last_info) updates) => super.copyWith((message) => updates(message as db_controller_last_info)) as db_controller_last_info;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_controller_last_info create() => db_controller_last_info._();
  db_controller_last_info createEmptyInstance() => create();
  static $pb.PbList<db_controller_last_info> createRepeated() => $pb.PbList<db_controller_last_info>();
  @$core.pragma('dart2js:noInline')
  static db_controller_last_info getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_controller_last_info>(create);
  static db_controller_last_info? _defaultInstance;

  /// @table uuid primary key
  /// 控制器ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  /// 设备所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table timestamp not null default '2000-01-01 00:00:00'
  /// 最后上来数据的时间
  @$pb.TagNumber(4)
  $core.String get lastDataTime => $_getSZ(2);
  @$pb.TagNumber(4)
  set lastDataTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastDataTime() => $_has(2);
  @$pb.TagNumber(4)
  void clearLastDataTime() => clearField(4);

  /// @table int default 0
  /// 当前连接状态 0:offline 1:online
  @$pb.TagNumber(5)
  $core.int get connected => $_getIZ(3);
  @$pb.TagNumber(5)
  set connected($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasConnected() => $_has(3);
  @$pb.TagNumber(5)
  void clearConnected() => clearField(5);
}

/// 控制器上线历史表,按月分表
class db_controller_online_history extends $pb.GeneratedMessage {
  factory db_controller_online_history({
    $core.String? rid,
    $core.String? controllerDmrId,
    $core.String? actionTime,
    $core.int? actionCode,
    $core.String? note,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (controllerDmrId != null) {
      $result.controllerDmrId = controllerDmrId;
    }
    if (actionTime != null) {
      $result.actionTime = actionTime;
    }
    if (actionCode != null) {
      $result.actionCode = actionCode;
    }
    if (note != null) {
      $result.note = note;
    }
    return $result;
  }
  db_controller_online_history._() : super();
  factory db_controller_online_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_controller_online_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_controller_online_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'controllerDmrId')
    ..aOS(3, _omitFieldNames ? '' : 'actionTime')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'actionCode', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'note')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_controller_online_history clone() => db_controller_online_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_controller_online_history copyWith(void Function(db_controller_online_history) updates) => super.copyWith((message) => updates(message as db_controller_online_history)) as db_controller_online_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_controller_online_history create() => db_controller_online_history._();
  db_controller_online_history createEmptyInstance() => create();
  static $pb.PbList<db_controller_online_history> createRepeated() => $pb.PbList<db_controller_online_history>();
  @$core.pragma('dart2js:noInline')
  static db_controller_online_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_controller_online_history>(create);
  static db_controller_online_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table varchar(8)
  /// dmr-id
  @$pb.TagNumber(2)
  $core.String get controllerDmrId => $_getSZ(1);
  @$pb.TagNumber(2)
  set controllerDmrId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasControllerDmrId() => $_has(1);
  @$pb.TagNumber(2)
  void clearControllerDmrId() => clearField(2);

  /// @table timestamp
  /// satation action time
  @$pb.TagNumber(3)
  $core.String get actionTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set actionTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasActionTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearActionTime() => clearField(3);

  /// @table int
  /// 1: register 2:disconnect refer to action_code of cc01
  @$pb.TagNumber(4)
  $core.int get actionCode => $_getIZ(3);
  @$pb.TagNumber(4)
  set actionCode($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasActionCode() => $_has(3);
  @$pb.TagNumber(4)
  void clearActionCode() => clearField(4);

  /// @table text
  /// action description
  @$pb.TagNumber(5)
  $core.String get note => $_getSZ(4);
  @$pb.TagNumber(5)
  set note($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasNote() => $_has(4);
  @$pb.TagNumber(5)
  void clearNote() => clearField(5);
}

/// 电话网关黑白名单
class db_phone_gateway_filter extends $pb.GeneratedMessage {
  factory db_phone_gateway_filter({
    $core.String? rid,
    $core.String? orgId,
    $core.String? name,
    $core.String? lastModifyTime,
    $core.String? inBlack,
    $core.bool? inBlackEnable,
    $core.String? inWhite,
    $core.bool? inWhiteEnable,
    $core.String? outBlack,
    $core.bool? outBlackEnable,
    $core.String? outWhite,
    $core.bool? outWhiteEnable,
    $core.String? note,
    $core.String? setting,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (name != null) {
      $result.name = name;
    }
    if (lastModifyTime != null) {
      $result.lastModifyTime = lastModifyTime;
    }
    if (inBlack != null) {
      $result.inBlack = inBlack;
    }
    if (inBlackEnable != null) {
      $result.inBlackEnable = inBlackEnable;
    }
    if (inWhite != null) {
      $result.inWhite = inWhite;
    }
    if (inWhiteEnable != null) {
      $result.inWhiteEnable = inWhiteEnable;
    }
    if (outBlack != null) {
      $result.outBlack = outBlack;
    }
    if (outBlackEnable != null) {
      $result.outBlackEnable = outBlackEnable;
    }
    if (outWhite != null) {
      $result.outWhite = outWhite;
    }
    if (outWhiteEnable != null) {
      $result.outWhiteEnable = outWhiteEnable;
    }
    if (note != null) {
      $result.note = note;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    return $result;
  }
  db_phone_gateway_filter._() : super();
  factory db_phone_gateway_filter.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_phone_gateway_filter.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_phone_gateway_filter', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'name')
    ..aOS(4, _omitFieldNames ? '' : 'lastModifyTime')
    ..aOS(5, _omitFieldNames ? '' : 'inBlack')
    ..aOB(6, _omitFieldNames ? '' : 'inBlackEnable')
    ..aOS(7, _omitFieldNames ? '' : 'inWhite')
    ..aOB(8, _omitFieldNames ? '' : 'inWhiteEnable')
    ..aOS(9, _omitFieldNames ? '' : 'outBlack')
    ..aOB(10, _omitFieldNames ? '' : 'outBlackEnable')
    ..aOS(11, _omitFieldNames ? '' : 'outWhite')
    ..aOB(12, _omitFieldNames ? '' : 'outWhiteEnable')
    ..aOS(13, _omitFieldNames ? '' : 'note')
    ..aOS(14, _omitFieldNames ? '' : 'setting')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_phone_gateway_filter clone() => db_phone_gateway_filter()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_phone_gateway_filter copyWith(void Function(db_phone_gateway_filter) updates) => super.copyWith((message) => updates(message as db_phone_gateway_filter)) as db_phone_gateway_filter;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_phone_gateway_filter create() => db_phone_gateway_filter._();
  db_phone_gateway_filter createEmptyInstance() => create();
  static $pb.PbList<db_phone_gateway_filter> createRepeated() => $pb.PbList<db_phone_gateway_filter>();
  @$core.pragma('dart2js:noInline')
  static db_phone_gateway_filter getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_phone_gateway_filter>(create);
  static db_phone_gateway_filter? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table text  unique not null
  /// 名称
  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  /// @table timestamp
  /// 最后修改时间(保存时间)
  @$pb.TagNumber(4)
  $core.String get lastModifyTime => $_getSZ(3);
  @$pb.TagNumber(4)
  set lastModifyTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastModifyTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearLastModifyTime() => clearField(4);

  /// @table jsonb default '[]'::jsonb
  /// 拨入黑名单
  @$pb.TagNumber(5)
  $core.String get inBlack => $_getSZ(4);
  @$pb.TagNumber(5)
  set inBlack($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasInBlack() => $_has(4);
  @$pb.TagNumber(5)
  void clearInBlack() => clearField(5);

  /// @table boolean default false
  /// 拨入黑名单是否启用
  @$pb.TagNumber(6)
  $core.bool get inBlackEnable => $_getBF(5);
  @$pb.TagNumber(6)
  set inBlackEnable($core.bool v) { $_setBool(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasInBlackEnable() => $_has(5);
  @$pb.TagNumber(6)
  void clearInBlackEnable() => clearField(6);

  /// @table jsonb default '[]'::jsonb
  /// 拨入白名单
  @$pb.TagNumber(7)
  $core.String get inWhite => $_getSZ(6);
  @$pb.TagNumber(7)
  set inWhite($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasInWhite() => $_has(6);
  @$pb.TagNumber(7)
  void clearInWhite() => clearField(7);

  /// @table boolean default false
  /// 拨入白名单是否启用
  @$pb.TagNumber(8)
  $core.bool get inWhiteEnable => $_getBF(7);
  @$pb.TagNumber(8)
  set inWhiteEnable($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasInWhiteEnable() => $_has(7);
  @$pb.TagNumber(8)
  void clearInWhiteEnable() => clearField(8);

  /// @table jsonb default '[]'::jsonb
  /// 拨出黑名单
  @$pb.TagNumber(9)
  $core.String get outBlack => $_getSZ(8);
  @$pb.TagNumber(9)
  set outBlack($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasOutBlack() => $_has(8);
  @$pb.TagNumber(9)
  void clearOutBlack() => clearField(9);

  /// @table boolean default false
  /// 拨出黑名单是否启用
  @$pb.TagNumber(10)
  $core.bool get outBlackEnable => $_getBF(9);
  @$pb.TagNumber(10)
  set outBlackEnable($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasOutBlackEnable() => $_has(9);
  @$pb.TagNumber(10)
  void clearOutBlackEnable() => clearField(10);

  /// @table jsonb default '[]'::jsonb
  /// 拨出白名单
  @$pb.TagNumber(11)
  $core.String get outWhite => $_getSZ(10);
  @$pb.TagNumber(11)
  set outWhite($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasOutWhite() => $_has(10);
  @$pb.TagNumber(11)
  void clearOutWhite() => clearField(11);

  /// @table boolean default false
  /// 拨出白名单是否启用
  @$pb.TagNumber(12)
  $core.bool get outWhiteEnable => $_getBF(11);
  @$pb.TagNumber(12)
  set outWhiteEnable($core.bool v) { $_setBool(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasOutWhiteEnable() => $_has(11);
  @$pb.TagNumber(12)
  void clearOutWhiteEnable() => clearField(12);

  /// @table text
  @$pb.TagNumber(13)
  $core.String get note => $_getSZ(12);
  @$pb.TagNumber(13)
  set note($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasNote() => $_has(12);
  @$pb.TagNumber(13)
  void clearNote() => clearField(13);

  /// @table jsonb default '{}'::jsonb
  /// json设置
  @$pb.TagNumber(14)
  $core.String get setting => $_getSZ(13);
  @$pb.TagNumber(14)
  set setting($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasSetting() => $_has(13);
  @$pb.TagNumber(14)
  void clearSetting() => clearField(14);
}

/// 对讲机设备表
class db_device extends $pb.GeneratedMessage {
  factory db_device({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? selfId,
    $core.String? dmrId,
    $core.String? virOrgs,
    $core.String? deviceUser,
    $core.String? note,
    $core.int? deviceType,
    $core.String? channelLastModifyTime,
    $core.String? channel,
    $core.int? priority,
    $core.String? gatewayFilterRid,
    $core.String? setting,
    $core.String? lastRfConfigTime,
    $core.String? lastRfWriteTime,
    $core.int? traditionalDmrAllowNetCall,
    $core.String? devGroup,
    $core.String? pocSetting,
    $core.String? pocSettingLastModifyTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (selfId != null) {
      $result.selfId = selfId;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (virOrgs != null) {
      $result.virOrgs = virOrgs;
    }
    if (deviceUser != null) {
      $result.deviceUser = deviceUser;
    }
    if (note != null) {
      $result.note = note;
    }
    if (deviceType != null) {
      $result.deviceType = deviceType;
    }
    if (channelLastModifyTime != null) {
      $result.channelLastModifyTime = channelLastModifyTime;
    }
    if (channel != null) {
      $result.channel = channel;
    }
    if (priority != null) {
      $result.priority = priority;
    }
    if (gatewayFilterRid != null) {
      $result.gatewayFilterRid = gatewayFilterRid;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    if (lastRfConfigTime != null) {
      $result.lastRfConfigTime = lastRfConfigTime;
    }
    if (lastRfWriteTime != null) {
      $result.lastRfWriteTime = lastRfWriteTime;
    }
    if (traditionalDmrAllowNetCall != null) {
      $result.traditionalDmrAllowNetCall = traditionalDmrAllowNetCall;
    }
    if (devGroup != null) {
      $result.devGroup = devGroup;
    }
    if (pocSetting != null) {
      $result.pocSetting = pocSetting;
    }
    if (pocSettingLastModifyTime != null) {
      $result.pocSettingLastModifyTime = pocSettingLastModifyTime;
    }
    return $result;
  }
  db_device._() : super();
  factory db_device.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'selfId')
    ..aOS(5, _omitFieldNames ? '' : 'dmrId')
    ..aOS(6, _omitFieldNames ? '' : 'virOrgs')
    ..aOS(7, _omitFieldNames ? '' : 'deviceUser')
    ..aOS(8, _omitFieldNames ? '' : 'note')
    ..a<$core.int>(9, _omitFieldNames ? '' : 'deviceType', $pb.PbFieldType.O3)
    ..aOS(10, _omitFieldNames ? '' : 'channelLastModifyTime')
    ..aOS(11, _omitFieldNames ? '' : 'channel')
    ..a<$core.int>(12, _omitFieldNames ? '' : 'priority', $pb.PbFieldType.O3)
    ..aOS(13, _omitFieldNames ? '' : 'gatewayFilterRid')
    ..aOS(14, _omitFieldNames ? '' : 'setting')
    ..aOS(15, _omitFieldNames ? '' : 'lastRfConfigTime')
    ..aOS(16, _omitFieldNames ? '' : 'lastRfWriteTime')
    ..a<$core.int>(17, _omitFieldNames ? '' : 'traditionalDmrAllowNetCall', $pb.PbFieldType.O3)
    ..aOS(18, _omitFieldNames ? '' : 'devGroup')
    ..aOS(19, _omitFieldNames ? '' : 'pocSetting')
    ..aOS(20, _omitFieldNames ? '' : 'pocSettingLastModifyTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device clone() => db_device()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device copyWith(void Function(db_device) updates) => super.copyWith((message) => updates(message as db_device)) as db_device;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device create() => db_device._();
  db_device createEmptyInstance() => create();
  static $pb.PbList<db_device> createRepeated() => $pb.PbList<db_device>();
  @$core.pragma('dart2js:noInline')
  static db_device getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device>(create);
  static db_device? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  /// 设备所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null unique
  /// 设备名称
  @$pb.TagNumber(4)
  $core.String get selfId => $_getSZ(3);
  @$pb.TagNumber(4)
  set selfId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSelfId() => $_has(3);
  @$pb.TagNumber(4)
  void clearSelfId() => clearField(4);

  /// @table varchar(16) not null unique
  /// 设备DMR-ID
  @$pb.TagNumber(5)
  $core.String get dmrId => $_getSZ(4);
  @$pb.TagNumber(5)
  set dmrId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDmrId() => $_has(4);
  @$pb.TagNumber(5)
  void clearDmrId() => clearField(5);

  /// @table text default ''
  /// 对讲机所属的虚拟群组,逗号分隔的群组rid
  @$pb.TagNumber(6)
  $core.String get virOrgs => $_getSZ(5);
  @$pb.TagNumber(6)
  set virOrgs($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasVirOrgs() => $_has(5);
  @$pb.TagNumber(6)
  void clearVirOrgs() => clearField(6);

  /// @table uuid default '00000000-0000-0000-0000-000000000000'
  /// 对讲机所属用户,db_user中的rid
  @$pb.TagNumber(7)
  $core.String get deviceUser => $_getSZ(6);
  @$pb.TagNumber(7)
  set deviceUser($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasDeviceUser() => $_has(6);
  @$pb.TagNumber(7)
  void clearDeviceUser() => clearField(7);

  /// @table text default ''
  /// 设备备注信息
  @$pb.TagNumber(8)
  $core.String get note => $_getSZ(7);
  @$pb.TagNumber(8)
  set note($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasNote() => $_has(7);
  @$pb.TagNumber(8)
  void clearNote() => clearField(8);

  /// @table int not null default 0
  ///  设备类型 0:对讲机手台 1：车台 2:指挥座席 3:电话网关设备(tg810) 4:中继虚拟终端
  ///  5:互联网关终端 6:模拟网关终端 7:数字网关终端 8:2.4G物联巡查终端
  ///  9:传统常规dmr手台 10:sip网关终端 11:虚拟集群对讲手台 12:mesh网关终端
  ///  13:mesh终端
  ///  14:prochat公网终端 setting中有相应的prochatID:{"prochatID":12345}
  ///  15:prochat网关终端，不能手动删除，只能通过prochat网关设备一起删除
  ///  16:sip 电话终端,直接sip接入8100系统，配置1信道为默认收听组，支持动态配置收听组
  ///  21:基地台  22:android模拟终端 23:公网poc终端
  @$pb.TagNumber(9)
  $core.int get deviceType => $_getIZ(8);
  @$pb.TagNumber(9)
  set deviceType($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasDeviceType() => $_has(8);
  @$pb.TagNumber(9)
  void clearDeviceType() => clearField(9);

  /// @table timestamp not null default now_utc()
  /// 频道数据最后修改时间
  @$pb.TagNumber(10)
  $core.String get channelLastModifyTime => $_getSZ(9);
  @$pb.TagNumber(10)
  set channelLastModifyTime($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasChannelLastModifyTime() => $_has(9);
  @$pb.TagNumber(10)
  void clearChannelLastModifyTime() => clearField(10);

  /// @table jsonb not null default '{"channels": []}'::jsonb
  /// 频道配置数据
  ///  type DeviceChannel []struct {
  ///      No          int      `json:"No"`
  ///      SendGroup   string   `json:"sendGroup"`
  ///      ListenGroup []string `json:"listenGroup"`
  ///  }
  @$pb.TagNumber(11)
  $core.String get channel => $_getSZ(10);
  @$pb.TagNumber(11)
  set channel($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasChannel() => $_has(10);
  @$pb.TagNumber(11)
  void clearChannel() => clearField(11);

  /// @table int
  /// 优先级
  @$pb.TagNumber(12)
  $core.int get priority => $_getIZ(11);
  @$pb.TagNumber(12)
  set priority($core.int v) { $_setSignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasPriority() => $_has(11);
  @$pb.TagNumber(12)
  void clearPriority() => clearField(12);

  /// @table uuid REFERENCES db_phone_gateway_filter(rid) ON DELETE set null
  /// 关联的电话黑白名单
  @$pb.TagNumber(13)
  $core.String get gatewayFilterRid => $_getSZ(12);
  @$pb.TagNumber(13)
  set gatewayFilterRid($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasGatewayFilterRid() => $_has(12);
  @$pb.TagNumber(13)
  void clearGatewayFilterRid() => clearField(13);

  /// @table jsonb not null default '{}'::jsonb
  /// 设备的配置信息
  @$pb.TagNumber(14)
  $core.String get setting => $_getSZ(13);
  @$pb.TagNumber(14)
  set setting($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasSetting() => $_has(13);
  @$pb.TagNumber(14)
  void clearSetting() => clearField(14);

  /// @table timestamp not null default now_utc()
  /// 写频配置最后更新时间
  @$pb.TagNumber(15)
  $core.String get lastRfConfigTime => $_getSZ(14);
  @$pb.TagNumber(15)
  set lastRfConfigTime($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasLastRfConfigTime() => $_has(14);
  @$pb.TagNumber(15)
  void clearLastRfConfigTime() => clearField(15);

  /// @table timestamp not null default now_utc()
  /// 写频配置最后写入对讲机时间
  @$pb.TagNumber(16)
  $core.String get lastRfWriteTime => $_getSZ(15);
  @$pb.TagNumber(16)
  set lastRfWriteTime($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasLastRfWriteTime() => $_has(15);
  @$pb.TagNumber(16)
  void clearLastRfWriteTime() => clearField(16);

  /// @table int not null default 0
  /// 常规dmr手台是否允许呼叫
  @$pb.TagNumber(17)
  $core.int get traditionalDmrAllowNetCall => $_getIZ(16);
  @$pb.TagNumber(17)
  set traditionalDmrAllowNetCall($core.int v) { $_setSignedInt32(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasTraditionalDmrAllowNetCall() => $_has(16);
  @$pb.TagNumber(17)
  void clearTraditionalDmrAllowNetCall() => clearField(17);

  /// @table text
  /// 集群机归属组dmrid
  @$pb.TagNumber(18)
  $core.String get devGroup => $_getSZ(17);
  @$pb.TagNumber(18)
  set devGroup($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasDevGroup() => $_has(17);
  @$pb.TagNumber(18)
  void clearDevGroup() => clearField(18);

  /// @table jsonb not null default '{}'::jsonb
  /// 公网终端的配置信息
  @$pb.TagNumber(19)
  $core.String get pocSetting => $_getSZ(18);
  @$pb.TagNumber(19)
  set pocSetting($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasPocSetting() => $_has(18);
  @$pb.TagNumber(19)
  void clearPocSetting() => clearField(19);

  /// @table timestamp not null default now_utc()
  /// poc_setting_last_modify_time
  @$pb.TagNumber(20)
  $core.String get pocSettingLastModifyTime => $_getSZ(19);
  @$pb.TagNumber(20)
  set pocSettingLastModifyTime($core.String v) { $_setString(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasPocSettingLastModifyTime() => $_has(19);
  @$pb.TagNumber(20)
  void clearPocSettingLastModifyTime() => clearField(20);
}

/// 对讲机最后的数据信息
class db_device_last_info extends $pb.GeneratedMessage {
  factory db_device_last_info({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? lastRfidPersonTime,
    $core.String? dmrId,
    $core.String? lastDataTime,
    $core.String? lastRfidPerson,
    $core.String? lastRfid,
    $core.String? lastRfidTime,
    $core.String? lastGpsTime,
    $core.double? lastLon,
    $core.double? lastLat,
    $core.int? deviceLockState,
    $core.String? msStatus,
    $core.String? lastController,
    $core.String? lastPoweronTime,
    $core.String? lastPoweroffTime,
    $core.String? lastGpsInvalidTime,
    $core.String? optStatus,
    $core.int? lastGpsSwitchState,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (lastRfidPersonTime != null) {
      $result.lastRfidPersonTime = lastRfidPersonTime;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (lastDataTime != null) {
      $result.lastDataTime = lastDataTime;
    }
    if (lastRfidPerson != null) {
      $result.lastRfidPerson = lastRfidPerson;
    }
    if (lastRfid != null) {
      $result.lastRfid = lastRfid;
    }
    if (lastRfidTime != null) {
      $result.lastRfidTime = lastRfidTime;
    }
    if (lastGpsTime != null) {
      $result.lastGpsTime = lastGpsTime;
    }
    if (lastLon != null) {
      $result.lastLon = lastLon;
    }
    if (lastLat != null) {
      $result.lastLat = lastLat;
    }
    if (deviceLockState != null) {
      $result.deviceLockState = deviceLockState;
    }
    if (msStatus != null) {
      $result.msStatus = msStatus;
    }
    if (lastController != null) {
      $result.lastController = lastController;
    }
    if (lastPoweronTime != null) {
      $result.lastPoweronTime = lastPoweronTime;
    }
    if (lastPoweroffTime != null) {
      $result.lastPoweroffTime = lastPoweroffTime;
    }
    if (lastGpsInvalidTime != null) {
      $result.lastGpsInvalidTime = lastGpsInvalidTime;
    }
    if (optStatus != null) {
      $result.optStatus = optStatus;
    }
    if (lastGpsSwitchState != null) {
      $result.lastGpsSwitchState = lastGpsSwitchState;
    }
    return $result;
  }
  db_device_last_info._() : super();
  factory db_device_last_info.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_last_info.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_last_info', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'lastRfidPersonTime')
    ..aOS(5, _omitFieldNames ? '' : 'dmrId')
    ..aOS(6, _omitFieldNames ? '' : 'lastDataTime')
    ..aOS(7, _omitFieldNames ? '' : 'lastRfidPerson')
    ..aOS(8, _omitFieldNames ? '' : 'lastRfid')
    ..aOS(9, _omitFieldNames ? '' : 'lastRfidTime')
    ..aOS(10, _omitFieldNames ? '' : 'lastGpsTime')
    ..a<$core.double>(11, _omitFieldNames ? '' : 'lastLon', $pb.PbFieldType.OD)
    ..a<$core.double>(12, _omitFieldNames ? '' : 'lastLat', $pb.PbFieldType.OD)
    ..a<$core.int>(13, _omitFieldNames ? '' : 'deviceLockState', $pb.PbFieldType.O3)
    ..aOS(14, _omitFieldNames ? '' : 'msStatus')
    ..aOS(15, _omitFieldNames ? '' : 'lastController')
    ..aOS(16, _omitFieldNames ? '' : 'lastPoweronTime')
    ..aOS(17, _omitFieldNames ? '' : 'lastPoweroffTime')
    ..aOS(18, _omitFieldNames ? '' : 'lastGpsInvalidTime')
    ..aOS(19, _omitFieldNames ? '' : 'optStatus')
    ..a<$core.int>(20, _omitFieldNames ? '' : 'lastGpsSwitchState', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_last_info clone() => db_device_last_info()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_last_info copyWith(void Function(db_device_last_info) updates) => super.copyWith((message) => updates(message as db_device_last_info)) as db_device_last_info;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_last_info create() => db_device_last_info._();
  db_device_last_info createEmptyInstance() => create();
  static $pb.PbList<db_device_last_info> createRepeated() => $pb.PbList<db_device_last_info>();
  @$core.pragma('dart2js:noInline')
  static db_device_last_info getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_last_info>(create);
  static db_device_last_info? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  /// 设备所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table timestamp  default '2000-01-01 00:00:00'
  /// 最后读取用户身份卡时的时间
  @$pb.TagNumber(4)
  $core.String get lastRfidPersonTime => $_getSZ(3);
  @$pb.TagNumber(4)
  set lastRfidPersonTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastRfidPersonTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearLastRfidPersonTime() => clearField(4);

  /// @table varchar(16) not null unique
  /// 设备DMR-ID
  @$pb.TagNumber(5)
  $core.String get dmrId => $_getSZ(4);
  @$pb.TagNumber(5)
  set dmrId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDmrId() => $_has(4);
  @$pb.TagNumber(5)
  void clearDmrId() => clearField(5);

  /// @table timestamp  default '2000-01-01 00:00:00'
  /// 最后数据时间
  @$pb.TagNumber(6)
  $core.String get lastDataTime => $_getSZ(5);
  @$pb.TagNumber(6)
  set lastDataTime($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLastDataTime() => $_has(5);
  @$pb.TagNumber(6)
  void clearLastDataTime() => clearField(6);

  /// @table uuid default '00000000-0000-0000-0000-000000000000'
  /// 最后打卡人员的rid
  @$pb.TagNumber(7)
  $core.String get lastRfidPerson => $_getSZ(6);
  @$pb.TagNumber(7)
  set lastRfidPerson($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLastRfidPerson() => $_has(6);
  @$pb.TagNumber(7)
  void clearLastRfidPerson() => clearField(7);

  /// @table varchar(16) default ''
  /// 最后读取的rfid卡号
  @$pb.TagNumber(8)
  $core.String get lastRfid => $_getSZ(7);
  @$pb.TagNumber(8)
  set lastRfid($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLastRfid() => $_has(7);
  @$pb.TagNumber(8)
  void clearLastRfid() => clearField(8);

  /// @table timestamp default '2000-01-01 00:00:00'
  /// 最后读取rfid的时间
  @$pb.TagNumber(9)
  $core.String get lastRfidTime => $_getSZ(8);
  @$pb.TagNumber(9)
  set lastRfidTime($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasLastRfidTime() => $_has(8);
  @$pb.TagNumber(9)
  void clearLastRfidTime() => clearField(9);

  /// @table timestamp default '2000-01-01 00:00:00'
  /// 最后定位的时间
  @$pb.TagNumber(10)
  $core.String get lastGpsTime => $_getSZ(9);
  @$pb.TagNumber(10)
  set lastGpsTime($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasLastGpsTime() => $_has(9);
  @$pb.TagNumber(10)
  void clearLastGpsTime() => clearField(10);

  /// @table double precision default 1000
  /// 最后定位的经度
  @$pb.TagNumber(11)
  $core.double get lastLon => $_getN(10);
  @$pb.TagNumber(11)
  set lastLon($core.double v) { $_setDouble(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasLastLon() => $_has(10);
  @$pb.TagNumber(11)
  void clearLastLon() => clearField(11);

  /// @table double precision default 1000
  /// 最后定位的纬度
  @$pb.TagNumber(12)
  $core.double get lastLat => $_getN(11);
  @$pb.TagNumber(12)
  set lastLat($core.double v) { $_setDouble(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasLastLat() => $_has(11);
  @$pb.TagNumber(12)
  void clearLastLat() => clearField(12);

  /// @table int default 0
  /// 设备状态 0=开机；1=禁听锁机,2=禁发锁机,3=禁发禁听锁机
  @$pb.TagNumber(13)
  $core.int get deviceLockState => $_getIZ(12);
  @$pb.TagNumber(13)
  set deviceLockState($core.int v) { $_setSignedInt32(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasDeviceLockState() => $_has(12);
  @$pb.TagNumber(13)
  void clearDeviceLockState() => clearField(13);

  /// @table text default ''
  /// 对讲机最后的状态信息
  @$pb.TagNumber(14)
  $core.String get msStatus => $_getSZ(13);
  @$pb.TagNumber(14)
  set msStatus($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasMsStatus() => $_has(13);
  @$pb.TagNumber(14)
  void clearMsStatus() => clearField(14);

  /// @table text default ''
  /// 最后接收的控制器id
  @$pb.TagNumber(15)
  $core.String get lastController => $_getSZ(14);
  @$pb.TagNumber(15)
  set lastController($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasLastController() => $_has(14);
  @$pb.TagNumber(15)
  void clearLastController() => clearField(15);

  /// @table timestamp not null default '2000-01-01 00:00:00'
  /// 设备的最后开机时间
  @$pb.TagNumber(16)
  $core.String get lastPoweronTime => $_getSZ(15);
  @$pb.TagNumber(16)
  set lastPoweronTime($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasLastPoweronTime() => $_has(15);
  @$pb.TagNumber(16)
  void clearLastPoweronTime() => clearField(16);

  /// @table timestamp not null default '2000-01-01 00:00:00'
  /// 设备的最后开机时间
  @$pb.TagNumber(17)
  $core.String get lastPoweroffTime => $_getSZ(16);
  @$pb.TagNumber(17)
  set lastPoweroffTime($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasLastPoweroffTime() => $_has(16);
  @$pb.TagNumber(17)
  void clearLastPoweroffTime() => clearField(17);

  /// @table timestamp not null default '2000-01-01 00:00:00'
  /// 设备的最后不定位时间，上传GPS数据为V
  @$pb.TagNumber(18)
  $core.String get lastGpsInvalidTime => $_getSZ(17);
  @$pb.TagNumber(18)
  set lastGpsInvalidTime($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasLastGpsInvalidTime() => $_has(17);
  @$pb.TagNumber(18)
  void clearLastGpsInvalidTime() => clearField(18);

  /// @table jsonb not null default '{}'::jsonb
  /// 额外的状态信息
  @$pb.TagNumber(19)
  $core.String get optStatus => $_getSZ(18);
  @$pb.TagNumber(19)
  set optStatus($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasOptStatus() => $_has(18);
  @$pb.TagNumber(19)
  void clearOptStatus() => clearField(19);

  /// @table int not null default 0
  /// 最后gps开关状态
  /// 0:未知 11:开 10:关 14:N/A
  @$pb.TagNumber(20)
  $core.int get lastGpsSwitchState => $_getIZ(19);
  @$pb.TagNumber(20)
  set lastGpsSwitchState($core.int v) { $_setSignedInt32(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasLastGpsSwitchState() => $_has(19);
  @$pb.TagNumber(20)
  void clearLastGpsSwitchState() => clearField(20);
}

/// 用户职称表
class db_user_title extends $pb.GeneratedMessage {
  factory db_user_title({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? titleName,
    $core.String? note,
    $core.int? titleSortValue,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (titleName != null) {
      $result.titleName = titleName;
    }
    if (note != null) {
      $result.note = note;
    }
    if (titleSortValue != null) {
      $result.titleSortValue = titleSortValue;
    }
    return $result;
  }
  db_user_title._() : super();
  factory db_user_title.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_title.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_title', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'titleName')
    ..aOS(4, _omitFieldNames ? '' : 'note')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'titleSortValue', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_title clone() => db_user_title()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_title copyWith(void Function(db_user_title) updates) => super.copyWith((message) => updates(message as db_user_title)) as db_user_title;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_title create() => db_user_title._();
  db_user_title createEmptyInstance() => create();
  static $pb.PbList<db_user_title> createRepeated() => $pb.PbList<db_user_title>();
  @$core.pragma('dart2js:noInline')
  static db_user_title getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_title>(create);
  static db_user_title? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table varchar(64) not null unique default ''
  /// 职称名
  @$pb.TagNumber(3)
  $core.String get titleName => $_getSZ(2);
  @$pb.TagNumber(3)
  set titleName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTitleName() => $_has(2);
  @$pb.TagNumber(3)
  void clearTitleName() => clearField(3);

  /// @table text default ''
  /// 职称备注
  @$pb.TagNumber(4)
  $core.String get note => $_getSZ(3);
  @$pb.TagNumber(4)
  set note($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasNote() => $_has(3);
  @$pb.TagNumber(4)
  void clearNote() => clearField(4);

  /// @table int
  /// 职称排序值
  @$pb.TagNumber(5)
  $core.int get titleSortValue => $_getIZ(4);
  @$pb.TagNumber(5)
  set titleSortValue($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasTitleSortValue() => $_has(4);
  @$pb.TagNumber(5)
  void clearTitleSortValue() => clearField(5);
}

/// 用户数据表
class db_user extends $pb.GeneratedMessage {
  factory db_user({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? selfId,
    $core.String? userName,
    $core.String? userTitle,
    $core.String? userPhone,
    $core.String? userImage,
    $core.String? userRfid,
    $core.String? userLoginName,
    $core.String? userLoginPass,
    $core.String? userSetting,
    $core.String? note,
    $core.bool? allowLoginManage,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (selfId != null) {
      $result.selfId = selfId;
    }
    if (userName != null) {
      $result.userName = userName;
    }
    if (userTitle != null) {
      $result.userTitle = userTitle;
    }
    if (userPhone != null) {
      $result.userPhone = userPhone;
    }
    if (userImage != null) {
      $result.userImage = userImage;
    }
    if (userRfid != null) {
      $result.userRfid = userRfid;
    }
    if (userLoginName != null) {
      $result.userLoginName = userLoginName;
    }
    if (userLoginPass != null) {
      $result.userLoginPass = userLoginPass;
    }
    if (userSetting != null) {
      $result.userSetting = userSetting;
    }
    if (note != null) {
      $result.note = note;
    }
    if (allowLoginManage != null) {
      $result.allowLoginManage = allowLoginManage;
    }
    return $result;
  }
  db_user._() : super();
  factory db_user.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'selfId')
    ..aOS(5, _omitFieldNames ? '' : 'userName')
    ..aOS(6, _omitFieldNames ? '' : 'userTitle')
    ..aOS(7, _omitFieldNames ? '' : 'userPhone')
    ..aOS(8, _omitFieldNames ? '' : 'userImage')
    ..aOS(9, _omitFieldNames ? '' : 'userRfid')
    ..aOS(10, _omitFieldNames ? '' : 'userLoginName')
    ..aOS(11, _omitFieldNames ? '' : 'userLoginPass')
    ..aOS(12, _omitFieldNames ? '' : 'userSetting')
    ..aOS(13, _omitFieldNames ? '' : 'note')
    ..aOB(14, _omitFieldNames ? '' : 'allowLoginManage')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user clone() => db_user()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user copyWith(void Function(db_user) updates) => super.copyWith((message) => updates(message as db_user)) as db_user;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user create() => db_user._();
  db_user createEmptyInstance() => create();
  static $pb.PbList<db_user> createRepeated() => $pb.PbList<db_user>();
  @$core.pragma('dart2js:noInline')
  static db_user getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user>(create);
  static db_user? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 用户所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null unique
  /// 用户自编号
  @$pb.TagNumber(4)
  $core.String get selfId => $_getSZ(3);
  @$pb.TagNumber(4)
  set selfId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSelfId() => $_has(3);
  @$pb.TagNumber(4)
  void clearSelfId() => clearField(4);

  /// @table varchar(32) not null default ''
  /// 用户名
  @$pb.TagNumber(5)
  $core.String get userName => $_getSZ(4);
  @$pb.TagNumber(5)
  set userName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasUserName() => $_has(4);
  @$pb.TagNumber(5)
  void clearUserName() => clearField(5);

  /// @table text default ''
  /// 用户职称,可能有个,职称rid的列表,逗号分隔
  @$pb.TagNumber(6)
  $core.String get userTitle => $_getSZ(5);
  @$pb.TagNumber(6)
  set userTitle($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasUserTitle() => $_has(5);
  @$pb.TagNumber(6)
  void clearUserTitle() => clearField(6);

  /// @table varchar(64) default ''
  /// 用户电话
  @$pb.TagNumber(7)
  $core.String get userPhone => $_getSZ(6);
  @$pb.TagNumber(7)
  set userPhone($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasUserPhone() => $_has(6);
  @$pb.TagNumber(7)
  void clearUserPhone() => clearField(7);

  /// @table uuid REFERENCES db_image(rid) ON DELETE set DEFAULT default '*************-2222-2222-************'
  /// 用户图片
  @$pb.TagNumber(8)
  $core.String get userImage => $_getSZ(7);
  @$pb.TagNumber(8)
  set userImage($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasUserImage() => $_has(7);
  @$pb.TagNumber(8)
  void clearUserImage() => clearField(8);

  /// @table varchar(16) default ''
  /// 用户的身份卡ID
  @$pb.TagNumber(9)
  $core.String get userRfid => $_getSZ(8);
  @$pb.TagNumber(9)
  set userRfid($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasUserRfid() => $_has(8);
  @$pb.TagNumber(9)
  void clearUserRfid() => clearField(9);

  /// @table varchar(32) unique
  /// 用户登录名
  @$pb.TagNumber(10)
  $core.String get userLoginName => $_getSZ(9);
  @$pb.TagNumber(10)
  set userLoginName($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasUserLoginName() => $_has(9);
  @$pb.TagNumber(10)
  void clearUserLoginName() => clearField(10);

  /// @table text default ''
  /// 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
  @$pb.TagNumber(11)
  $core.String get userLoginPass => $_getSZ(10);
  @$pb.TagNumber(11)
  set userLoginPass($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasUserLoginPass() => $_has(10);
  @$pb.TagNumber(11)
  void clearUserLoginPass() => clearField(11);

  /// @table jsonb not null default '{}'::jsonb
  /// 用户的一些个人配置
  @$pb.TagNumber(12)
  $core.String get userSetting => $_getSZ(11);
  @$pb.TagNumber(12)
  set userSetting($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasUserSetting() => $_has(11);
  @$pb.TagNumber(12)
  void clearUserSetting() => clearField(12);

  /// @table text default ''
  /// note
  @$pb.TagNumber(13)
  $core.String get note => $_getSZ(12);
  @$pb.TagNumber(13)
  set note($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasNote() => $_has(12);
  @$pb.TagNumber(13)
  void clearNote() => clearField(13);

  /// @table boolean default true
  /// 允许登录调度管理网页，像android app就不允许登录网页，但是可以只登录app
  @$pb.TagNumber(14)
  $core.bool get allowLoginManage => $_getBF(13);
  @$pb.TagNumber(14)
  set allowLoginManage($core.bool v) { $_setBool(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasAllowLoginManage() => $_has(13);
  @$pb.TagNumber(14)
  void clearAllowLoginManage() => clearField(14);
}

/// 用户群组权限表
class db_user_privelege extends $pb.GeneratedMessage {
  factory db_user_privelege({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? userRid,
    $core.String? userOrg,
    $core.int? includeChildren,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (userRid != null) {
      $result.userRid = userRid;
    }
    if (userOrg != null) {
      $result.userOrg = userOrg;
    }
    if (includeChildren != null) {
      $result.includeChildren = includeChildren;
    }
    return $result;
  }
  db_user_privelege._() : super();
  factory db_user_privelege.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_privelege.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_privelege', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'userRid')
    ..aOS(4, _omitFieldNames ? '' : 'userOrg')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'includeChildren', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_privelege clone() => db_user_privelege()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_privelege copyWith(void Function(db_user_privelege) updates) => super.copyWith((message) => updates(message as db_user_privelege)) as db_user_privelege;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_privelege create() => db_user_privelege._();
  db_user_privelege createEmptyInstance() => create();
  static $pb.PbList<db_user_privelege> createRepeated() => $pb.PbList<db_user_privelege>();
  @$core.pragma('dart2js:noInline')
  static db_user_privelege getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_privelege>(create);
  static db_user_privelege? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_user(rid) ON DELETE CASCADE
  /// 用户rid
  @$pb.TagNumber(3)
  $core.String get userRid => $_getSZ(2);
  @$pb.TagNumber(3)
  set userRid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUserRid() => $_has(2);
  @$pb.TagNumber(3)
  void clearUserRid() => clearField(3);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 有权限的群组
  @$pb.TagNumber(4)
  $core.String get userOrg => $_getSZ(3);
  @$pb.TagNumber(4)
  set userOrg($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasUserOrg() => $_has(3);
  @$pb.TagNumber(4)
  void clearUserOrg() => clearField(4);

  /// @table int default 0
  /// 是否包含下级群组
  @$pb.TagNumber(5)
  $core.int get includeChildren => $_getIZ(4);
  @$pb.TagNumber(5)
  set includeChildren($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasIncludeChildren() => $_has(4);
  @$pb.TagNumber(5)
  void clearIncludeChildren() => clearField(5);
}

/// 用户登录的session id表
class db_user_session_id extends $pb.GeneratedMessage {
  factory db_user_session_id({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? userRid,
    $core.int? loginWay,
    $core.String? sessionId,
    $core.String? effectiveTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (userRid != null) {
      $result.userRid = userRid;
    }
    if (loginWay != null) {
      $result.loginWay = loginWay;
    }
    if (sessionId != null) {
      $result.sessionId = sessionId;
    }
    if (effectiveTime != null) {
      $result.effectiveTime = effectiveTime;
    }
    return $result;
  }
  db_user_session_id._() : super();
  factory db_user_session_id.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_session_id.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_session_id', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'userRid')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'loginWay', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'sessionId')
    ..aOS(6, _omitFieldNames ? '' : 'effectiveTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_session_id clone() => db_user_session_id()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_session_id copyWith(void Function(db_user_session_id) updates) => super.copyWith((message) => updates(message as db_user_session_id)) as db_user_session_id;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_session_id create() => db_user_session_id._();
  db_user_session_id createEmptyInstance() => create();
  static $pb.PbList<db_user_session_id> createRepeated() => $pb.PbList<db_user_session_id>();
  @$core.pragma('dart2js:noInline')
  static db_user_session_id getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_session_id>(create);
  static db_user_session_id? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null
  /// 用户id
  @$pb.TagNumber(3)
  $core.String get userRid => $_getSZ(2);
  @$pb.TagNumber(3)
  set userRid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUserRid() => $_has(2);
  @$pb.TagNumber(3)
  void clearUserRid() => clearField(3);

  /// @table int not null default 1
  /// 登录渠道 1:web 2:phone 3:pc  4:android
  @$pb.TagNumber(4)
  $core.int get loginWay => $_getIZ(3);
  @$pb.TagNumber(4)
  set loginWay($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLoginWay() => $_has(3);
  @$pb.TagNumber(4)
  void clearLoginWay() => clearField(4);

  /// @table uuid not null
  /// session id
  @$pb.TagNumber(5)
  $core.String get sessionId => $_getSZ(4);
  @$pb.TagNumber(5)
  set sessionId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSessionId() => $_has(4);
  @$pb.TagNumber(5)
  void clearSessionId() => clearField(5);

  /// @table timestamp
  /// 有效期
  @$pb.TagNumber(6)
  $core.String get effectiveTime => $_getSZ(5);
  @$pb.TagNumber(6)
  set effectiveTime($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasEffectiveTime() => $_has(5);
  @$pb.TagNumber(6)
  void clearEffectiveTime() => clearField(6);
}

/// 虚拟群组信息表
class db_virtual_org extends $pb.GeneratedMessage {
  factory db_virtual_org({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? virtualUser,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (virtualUser != null) {
      $result.virtualUser = virtualUser;
    }
    return $result;
  }
  db_virtual_org._() : super();
  factory db_virtual_org.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_virtual_org.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_virtual_org', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'virtualUser')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_virtual_org clone() => db_virtual_org()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_virtual_org copyWith(void Function(db_virtual_org) updates) => super.copyWith((message) => updates(message as db_virtual_org)) as db_virtual_org;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_virtual_org create() => db_virtual_org._();
  db_virtual_org createEmptyInstance() => create();
  static $pb.PbList<db_virtual_org> createRepeated() => $pb.PbList<db_virtual_org>();
  @$core.pragma('dart2js:noInline')
  static db_virtual_org getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_virtual_org>(create);
  static db_virtual_org? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 虚拟群组rid
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table uuid not null REFERENCES db_user(rid) ON DELETE CASCADE
  /// 群组成员rid
  @$pb.TagNumber(4)
  $core.String get virtualUser => $_getSZ(3);
  @$pb.TagNumber(4)
  set virtualUser($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasVirtualUser() => $_has(3);
  @$pb.TagNumber(4)
  void clearVirtualUser() => clearField(4);
}

/// 用户自己的一些地图标志
class db_map_point extends $pb.GeneratedMessage {
  factory db_map_point({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? selfId,
    $core.String? pointName,
    $core.String? mapDisplayName,
    $core.String? note,
    $core.double? lon,
    $core.double? lat,
    $core.int? startShowLevel,
    $core.int? colorR,
    $core.int? colorG,
    $core.int? colorB,
    $core.String? pointImg,
    $core.int? imgOrColorPoint,
    $core.int? markerWidth,
    $core.int? markerHeight,
    $core.int? markerType,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (selfId != null) {
      $result.selfId = selfId;
    }
    if (pointName != null) {
      $result.pointName = pointName;
    }
    if (mapDisplayName != null) {
      $result.mapDisplayName = mapDisplayName;
    }
    if (note != null) {
      $result.note = note;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (startShowLevel != null) {
      $result.startShowLevel = startShowLevel;
    }
    if (colorR != null) {
      $result.colorR = colorR;
    }
    if (colorG != null) {
      $result.colorG = colorG;
    }
    if (colorB != null) {
      $result.colorB = colorB;
    }
    if (pointImg != null) {
      $result.pointImg = pointImg;
    }
    if (imgOrColorPoint != null) {
      $result.imgOrColorPoint = imgOrColorPoint;
    }
    if (markerWidth != null) {
      $result.markerWidth = markerWidth;
    }
    if (markerHeight != null) {
      $result.markerHeight = markerHeight;
    }
    if (markerType != null) {
      $result.markerType = markerType;
    }
    return $result;
  }
  db_map_point._() : super();
  factory db_map_point.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_map_point.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_map_point', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'selfId')
    ..aOS(5, _omitFieldNames ? '' : 'pointName')
    ..aOS(6, _omitFieldNames ? '' : 'mapDisplayName')
    ..aOS(7, _omitFieldNames ? '' : 'note')
    ..a<$core.double>(8, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..a<$core.double>(9, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'startShowLevel', $pb.PbFieldType.O3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'colorR', $pb.PbFieldType.O3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'colorG', $pb.PbFieldType.O3)
    ..a<$core.int>(13, _omitFieldNames ? '' : 'colorB', $pb.PbFieldType.O3)
    ..aOS(14, _omitFieldNames ? '' : 'pointImg')
    ..a<$core.int>(15, _omitFieldNames ? '' : 'imgOrColorPoint', $pb.PbFieldType.O3)
    ..a<$core.int>(16, _omitFieldNames ? '' : 'markerWidth', $pb.PbFieldType.O3)
    ..a<$core.int>(17, _omitFieldNames ? '' : 'markerHeight', $pb.PbFieldType.O3)
    ..a<$core.int>(18, _omitFieldNames ? '' : 'markerType', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_map_point clone() => db_map_point()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_map_point copyWith(void Function(db_map_point) updates) => super.copyWith((message) => updates(message as db_map_point)) as db_map_point;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_map_point create() => db_map_point._();
  db_map_point createEmptyInstance() => create();
  static $pb.PbList<db_map_point> createRepeated() => $pb.PbList<db_map_point>();
  @$core.pragma('dart2js:noInline')
  static db_map_point getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_map_point>(create);
  static db_map_point? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null unique
  /// 编号
  @$pb.TagNumber(4)
  $core.String get selfId => $_getSZ(3);
  @$pb.TagNumber(4)
  set selfId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSelfId() => $_has(3);
  @$pb.TagNumber(4)
  void clearSelfId() => clearField(4);

  /// @table varchar(16) not null
  /// 标志名称
  @$pb.TagNumber(5)
  $core.String get pointName => $_getSZ(4);
  @$pb.TagNumber(5)
  set pointName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPointName() => $_has(4);
  @$pb.TagNumber(5)
  void clearPointName() => clearField(5);

  /// @table varchar(16) not null
  /// 地图上显示的名称
  @$pb.TagNumber(6)
  $core.String get mapDisplayName => $_getSZ(5);
  @$pb.TagNumber(6)
  set mapDisplayName($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasMapDisplayName() => $_has(5);
  @$pb.TagNumber(6)
  void clearMapDisplayName() => clearField(6);

  /// @table text
  /// 备注
  @$pb.TagNumber(7)
  $core.String get note => $_getSZ(6);
  @$pb.TagNumber(7)
  set note($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasNote() => $_has(6);
  @$pb.TagNumber(7)
  void clearNote() => clearField(7);

  /// @table double precision
  /// longitude
  @$pb.TagNumber(8)
  $core.double get lon => $_getN(7);
  @$pb.TagNumber(8)
  set lon($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLon() => $_has(7);
  @$pb.TagNumber(8)
  void clearLon() => clearField(8);

  /// @table double precision
  /// latitude
  @$pb.TagNumber(9)
  $core.double get lat => $_getN(8);
  @$pb.TagNumber(9)
  set lat($core.double v) { $_setDouble(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasLat() => $_has(8);
  @$pb.TagNumber(9)
  void clearLat() => clearField(9);

  /// @table int
  /// 开始显示的级别
  @$pb.TagNumber(10)
  $core.int get startShowLevel => $_getIZ(9);
  @$pb.TagNumber(10)
  set startShowLevel($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasStartShowLevel() => $_has(9);
  @$pb.TagNumber(10)
  void clearStartShowLevel() => clearField(10);

  /// @table smallint
  /// 颜色R
  @$pb.TagNumber(11)
  $core.int get colorR => $_getIZ(10);
  @$pb.TagNumber(11)
  set colorR($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasColorR() => $_has(10);
  @$pb.TagNumber(11)
  void clearColorR() => clearField(11);

  /// @table smallint
  /// 颜色G
  @$pb.TagNumber(12)
  $core.int get colorG => $_getIZ(11);
  @$pb.TagNumber(12)
  set colorG($core.int v) { $_setSignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasColorG() => $_has(11);
  @$pb.TagNumber(12)
  void clearColorG() => clearField(12);

  /// @table smallint
  /// 颜色B
  @$pb.TagNumber(13)
  $core.int get colorB => $_getIZ(12);
  @$pb.TagNumber(13)
  set colorB($core.int v) { $_setSignedInt32(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasColorB() => $_has(12);
  @$pb.TagNumber(13)
  void clearColorB() => clearField(13);

  /// @table uuid REFERENCES db_image(rid) ON DELETE RESTRICT
  /// 点的图标信息
  @$pb.TagNumber(14)
  $core.String get pointImg => $_getSZ(13);
  @$pb.TagNumber(14)
  set pointImg($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasPointImg() => $_has(13);
  @$pb.TagNumber(14)
  void clearPointImg() => clearField(14);

  /// @table int default 0
  /// 点是颜色点=0,还是图标点=1
  @$pb.TagNumber(15)
  $core.int get imgOrColorPoint => $_getIZ(14);
  @$pb.TagNumber(15)
  set imgOrColorPoint($core.int v) { $_setSignedInt32(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasImgOrColorPoint() => $_has(14);
  @$pb.TagNumber(15)
  void clearImgOrColorPoint() => clearField(15);

  /// @table int not null default 16
  /// 地图显示的大小,限制最大128
  @$pb.TagNumber(16)
  $core.int get markerWidth => $_getIZ(15);
  @$pb.TagNumber(16)
  set markerWidth($core.int v) { $_setSignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasMarkerWidth() => $_has(15);
  @$pb.TagNumber(16)
  void clearMarkerWidth() => clearField(16);

  /// @table int not null default 16
  /// 地图显示的大小,限制最大128
  @$pb.TagNumber(17)
  $core.int get markerHeight => $_getIZ(16);
  @$pb.TagNumber(17)
  set markerHeight($core.int v) { $_setSignedInt32(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasMarkerHeight() => $_has(16);
  @$pb.TagNumber(17)
  void clearMarkerHeight() => clearField(17);

  /// @table int default 0
  /// 标记点类型,0:普通标记点 1:单位标记点
  @$pb.TagNumber(18)
  $core.int get markerType => $_getIZ(17);
  @$pb.TagNumber(18)
  set markerType($core.int v) { $_setSignedInt32(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasMarkerType() => $_has(17);
  @$pb.TagNumber(18)
  void clearMarkerType() => clearField(18);
}

/// 巡查线路点
class db_line_point extends $pb.GeneratedMessage {
  factory db_line_point({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? pointId,
    $core.String? pointName,
    $core.String? mapDisplayName,
    $core.String? note,
    $core.double? lon,
    $core.double? lat,
    $core.int? startShowLevel,
    $core.int? colorR,
    $core.int? colorG,
    $core.int? colorB,
    $core.String? pointImg,
    $core.int? imgOrColorPoint,
    $core.int? pointType,
    $core.String? pointRfid,
    $core.int? gpsPointRadius,
    $core.String? lastLpalarmTime,
    $core.bool? lastLpalarmState,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (pointId != null) {
      $result.pointId = pointId;
    }
    if (pointName != null) {
      $result.pointName = pointName;
    }
    if (mapDisplayName != null) {
      $result.mapDisplayName = mapDisplayName;
    }
    if (note != null) {
      $result.note = note;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (startShowLevel != null) {
      $result.startShowLevel = startShowLevel;
    }
    if (colorR != null) {
      $result.colorR = colorR;
    }
    if (colorG != null) {
      $result.colorG = colorG;
    }
    if (colorB != null) {
      $result.colorB = colorB;
    }
    if (pointImg != null) {
      $result.pointImg = pointImg;
    }
    if (imgOrColorPoint != null) {
      $result.imgOrColorPoint = imgOrColorPoint;
    }
    if (pointType != null) {
      $result.pointType = pointType;
    }
    if (pointRfid != null) {
      $result.pointRfid = pointRfid;
    }
    if (gpsPointRadius != null) {
      $result.gpsPointRadius = gpsPointRadius;
    }
    if (lastLpalarmTime != null) {
      $result.lastLpalarmTime = lastLpalarmTime;
    }
    if (lastLpalarmState != null) {
      $result.lastLpalarmState = lastLpalarmState;
    }
    return $result;
  }
  db_line_point._() : super();
  factory db_line_point.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_line_point.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_line_point', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'pointId')
    ..aOS(5, _omitFieldNames ? '' : 'pointName')
    ..aOS(6, _omitFieldNames ? '' : 'mapDisplayName')
    ..aOS(7, _omitFieldNames ? '' : 'note')
    ..a<$core.double>(8, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..a<$core.double>(9, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'startShowLevel', $pb.PbFieldType.O3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'colorR', $pb.PbFieldType.O3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'colorG', $pb.PbFieldType.O3)
    ..a<$core.int>(13, _omitFieldNames ? '' : 'colorB', $pb.PbFieldType.O3)
    ..aOS(14, _omitFieldNames ? '' : 'pointImg')
    ..a<$core.int>(15, _omitFieldNames ? '' : 'imgOrColorPoint', $pb.PbFieldType.O3)
    ..a<$core.int>(16, _omitFieldNames ? '' : 'pointType', $pb.PbFieldType.O3)
    ..aOS(17, _omitFieldNames ? '' : 'pointRfid')
    ..a<$core.int>(18, _omitFieldNames ? '' : 'gpsPointRadius', $pb.PbFieldType.O3)
    ..aOS(19, _omitFieldNames ? '' : 'lastLpalarmTime')
    ..aOB(20, _omitFieldNames ? '' : 'lastLpalarmState')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_line_point clone() => db_line_point()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_line_point copyWith(void Function(db_line_point) updates) => super.copyWith((message) => updates(message as db_line_point)) as db_line_point;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_line_point create() => db_line_point._();
  db_line_point createEmptyInstance() => create();
  static $pb.PbList<db_line_point> createRepeated() => $pb.PbList<db_line_point>();
  @$core.pragma('dart2js:noInline')
  static db_line_point getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_line_point>(create);
  static db_line_point? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null
  /// 编号
  @$pb.TagNumber(4)
  $core.String get pointId => $_getSZ(3);
  @$pb.TagNumber(4)
  set pointId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPointId() => $_has(3);
  @$pb.TagNumber(4)
  void clearPointId() => clearField(4);

  /// @table varchar(16) not null
  /// 标志名称
  @$pb.TagNumber(5)
  $core.String get pointName => $_getSZ(4);
  @$pb.TagNumber(5)
  set pointName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPointName() => $_has(4);
  @$pb.TagNumber(5)
  void clearPointName() => clearField(5);

  /// @table varchar(16) not null
  /// 地图上显示的名称
  @$pb.TagNumber(6)
  $core.String get mapDisplayName => $_getSZ(5);
  @$pb.TagNumber(6)
  set mapDisplayName($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasMapDisplayName() => $_has(5);
  @$pb.TagNumber(6)
  void clearMapDisplayName() => clearField(6);

  /// @table text
  /// 备注
  @$pb.TagNumber(7)
  $core.String get note => $_getSZ(6);
  @$pb.TagNumber(7)
  set note($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasNote() => $_has(6);
  @$pb.TagNumber(7)
  void clearNote() => clearField(7);

  /// @table double precision
  /// longitude
  @$pb.TagNumber(8)
  $core.double get lon => $_getN(7);
  @$pb.TagNumber(8)
  set lon($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLon() => $_has(7);
  @$pb.TagNumber(8)
  void clearLon() => clearField(8);

  /// @table double precision
  /// latitude
  @$pb.TagNumber(9)
  $core.double get lat => $_getN(8);
  @$pb.TagNumber(9)
  set lat($core.double v) { $_setDouble(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasLat() => $_has(8);
  @$pb.TagNumber(9)
  void clearLat() => clearField(9);

  /// @table int
  /// 开始显示的级别
  @$pb.TagNumber(10)
  $core.int get startShowLevel => $_getIZ(9);
  @$pb.TagNumber(10)
  set startShowLevel($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasStartShowLevel() => $_has(9);
  @$pb.TagNumber(10)
  void clearStartShowLevel() => clearField(10);

  /// @table smallint
  /// 颜色R
  @$pb.TagNumber(11)
  $core.int get colorR => $_getIZ(10);
  @$pb.TagNumber(11)
  set colorR($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasColorR() => $_has(10);
  @$pb.TagNumber(11)
  void clearColorR() => clearField(11);

  /// @table smallint
  /// 颜色G
  @$pb.TagNumber(12)
  $core.int get colorG => $_getIZ(11);
  @$pb.TagNumber(12)
  set colorG($core.int v) { $_setSignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasColorG() => $_has(11);
  @$pb.TagNumber(12)
  void clearColorG() => clearField(12);

  /// @table smallint
  /// 颜色B
  @$pb.TagNumber(13)
  $core.int get colorB => $_getIZ(12);
  @$pb.TagNumber(13)
  set colorB($core.int v) { $_setSignedInt32(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasColorB() => $_has(12);
  @$pb.TagNumber(13)
  void clearColorB() => clearField(13);

  /// @table uuid REFERENCES db_image(rid) ON DELETE RESTRICT
  /// 点的图标信息
  @$pb.TagNumber(14)
  $core.String get pointImg => $_getSZ(13);
  @$pb.TagNumber(14)
  set pointImg($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasPointImg() => $_has(13);
  @$pb.TagNumber(14)
  void clearPointImg() => clearField(14);

  /// @table int default 0
  /// 点是颜色点,还是图标点
  @$pb.TagNumber(15)
  $core.int get imgOrColorPoint => $_getIZ(14);
  @$pb.TagNumber(15)
  set imgOrColorPoint($core.int v) { $_setSignedInt32(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasImgOrColorPoint() => $_has(14);
  @$pb.TagNumber(15)
  void clearImgOrColorPoint() => clearField(15);

  /// @table int default 0
  /// 巡查点类型 1无源点，2有源点,3gps虚拟点,4基站巡查点
  @$pb.TagNumber(16)
  $core.int get pointType => $_getIZ(15);
  @$pb.TagNumber(16)
  set pointType($core.int v) { $_setSignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasPointType() => $_has(15);
  @$pb.TagNumber(16)
  void clearPointType() => clearField(16);

  /// @table varchar(16) unique
  /// 巡查点的rfid,虚拟点时也要写入一个唯一值
  @$pb.TagNumber(17)
  $core.String get pointRfid => $_getSZ(16);
  @$pb.TagNumber(17)
  set pointRfid($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasPointRfid() => $_has(16);
  @$pb.TagNumber(17)
  void clearPointRfid() => clearField(17);

  /// @table int default 50
  /// gps虚拟点的半径,单位M
  @$pb.TagNumber(18)
  $core.int get gpsPointRadius => $_getIZ(17);
  @$pb.TagNumber(18)
  set gpsPointRadius($core.int v) { $_setSignedInt32(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasGpsPointRadius() => $_has(17);
  @$pb.TagNumber(18)
  void clearGpsPointRadius() => clearField(18);

  /// @table timestamp
  /// 有源点最后低压报警时间,null为没有报警过
  @$pb.TagNumber(19)
  $core.String get lastLpalarmTime => $_getSZ(18);
  @$pb.TagNumber(19)
  set lastLpalarmTime($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasLastLpalarmTime() => $_has(18);
  @$pb.TagNumber(19)
  void clearLastLpalarmTime() => clearField(19);

  /// @table boolean default false
  /// 当前点是不是在低压报警状态
  @$pb.TagNumber(20)
  $core.bool get lastLpalarmState => $_getBF(19);
  @$pb.TagNumber(20)
  set lastLpalarmState($core.bool v) { $_setBool(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasLastLpalarmState() => $_has(19);
  @$pb.TagNumber(20)
  void clearLastLpalarmState() => clearField(20);
}

/// 巡查点最新信息
class db_line_point_latest_info extends $pb.GeneratedMessage {
  factory db_line_point_latest_info({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? lastCheckTime,
    $core.String? lastCheckDeviceId,
    $core.String? lastCheckUserId,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (lastCheckTime != null) {
      $result.lastCheckTime = lastCheckTime;
    }
    if (lastCheckDeviceId != null) {
      $result.lastCheckDeviceId = lastCheckDeviceId;
    }
    if (lastCheckUserId != null) {
      $result.lastCheckUserId = lastCheckUserId;
    }
    return $result;
  }
  db_line_point_latest_info._() : super();
  factory db_line_point_latest_info.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_line_point_latest_info.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_line_point_latest_info', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(8, _omitFieldNames ? '' : 'lastCheckTime')
    ..aOS(9, _omitFieldNames ? '' : 'lastCheckDeviceId')
    ..aOS(11, _omitFieldNames ? '' : 'lastCheckUserId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_line_point_latest_info clone() => db_line_point_latest_info()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_line_point_latest_info copyWith(void Function(db_line_point_latest_info) updates) => super.copyWith((message) => updates(message as db_line_point_latest_info)) as db_line_point_latest_info;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_line_point_latest_info create() => db_line_point_latest_info._();
  db_line_point_latest_info createEmptyInstance() => create();
  static $pb.PbList<db_line_point_latest_info> createRepeated() => $pb.PbList<db_line_point_latest_info>();
  @$core.pragma('dart2js:noInline')
  static db_line_point_latest_info getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_line_point_latest_info>(create);
  static db_line_point_latest_info? _defaultInstance;

  /// @table uuid primary key
  /// 巡查点ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table timestamp
  /// 最后巡查时间
  @$pb.TagNumber(8)
  $core.String get lastCheckTime => $_getSZ(3);
  @$pb.TagNumber(8)
  set lastCheckTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(8)
  $core.bool hasLastCheckTime() => $_has(3);
  @$pb.TagNumber(8)
  void clearLastCheckTime() => clearField(8);

  /// @table uuid
  /// 最后检查的设备rid
  @$pb.TagNumber(9)
  $core.String get lastCheckDeviceId => $_getSZ(4);
  @$pb.TagNumber(9)
  set lastCheckDeviceId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(9)
  $core.bool hasLastCheckDeviceId() => $_has(4);
  @$pb.TagNumber(9)
  void clearLastCheckDeviceId() => clearField(9);

  /// @table uuid
  /// 最后检查的用户rid
  @$pb.TagNumber(11)
  $core.String get lastCheckUserId => $_getSZ(5);
  @$pb.TagNumber(11)
  set lastCheckUserId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(11)
  $core.bool hasLastCheckUserId() => $_has(5);
  @$pb.TagNumber(11)
  void clearLastCheckUserId() => clearField(11);
}

/// 巡查线路主表
class db_line_master extends $pb.GeneratedMessage {
  factory db_line_master({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? lineId,
    $core.String? lineName,
    $core.String? note,
    $core.int? pointCount,
    $core.int? lineDetailModify,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (lineId != null) {
      $result.lineId = lineId;
    }
    if (lineName != null) {
      $result.lineName = lineName;
    }
    if (note != null) {
      $result.note = note;
    }
    if (pointCount != null) {
      $result.pointCount = pointCount;
    }
    if (lineDetailModify != null) {
      $result.lineDetailModify = lineDetailModify;
    }
    return $result;
  }
  db_line_master._() : super();
  factory db_line_master.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_line_master.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_line_master', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'lineId')
    ..aOS(5, _omitFieldNames ? '' : 'lineName')
    ..aOS(6, _omitFieldNames ? '' : 'note')
    ..a<$core.int>(7, _omitFieldNames ? '' : 'pointCount', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'lineDetailModify', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_line_master clone() => db_line_master()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_line_master copyWith(void Function(db_line_master) updates) => super.copyWith((message) => updates(message as db_line_master)) as db_line_master;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_line_master create() => db_line_master._();
  db_line_master createEmptyInstance() => create();
  static $pb.PbList<db_line_master> createRepeated() => $pb.PbList<db_line_master>();
  @$core.pragma('dart2js:noInline')
  static db_line_master getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_line_master>(create);
  static db_line_master? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null
  /// 编号
  @$pb.TagNumber(4)
  $core.String get lineId => $_getSZ(3);
  @$pb.TagNumber(4)
  set lineId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLineId() => $_has(3);
  @$pb.TagNumber(4)
  void clearLineId() => clearField(4);

  /// @table varchar(16) not null
  /// 名称
  @$pb.TagNumber(5)
  $core.String get lineName => $_getSZ(4);
  @$pb.TagNumber(5)
  set lineName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLineName() => $_has(4);
  @$pb.TagNumber(5)
  void clearLineName() => clearField(5);

  /// @table text
  /// note
  @$pb.TagNumber(6)
  $core.String get note => $_getSZ(5);
  @$pb.TagNumber(6)
  set note($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasNote() => $_has(5);
  @$pb.TagNumber(6)
  void clearNote() => clearField(6);

  /// @table int default 0
  /// 线路包含的点数量
  @$pb.TagNumber(7)
  $core.int get pointCount => $_getIZ(6);
  @$pb.TagNumber(7)
  set pointCount($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasPointCount() => $_has(6);
  @$pb.TagNumber(7)
  void clearPointCount() => clearField(7);

  /// @table int not null default 0
  /// 此线路已经删除过点时为1,需要提示用户再确认线路是否完整,如果修改完成要设置为0
  @$pb.TagNumber(8)
  $core.int get lineDetailModify => $_getIZ(7);
  @$pb.TagNumber(8)
  set lineDetailModify($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasLineDetailModify() => $_has(7);
  @$pb.TagNumber(8)
  void clearLineDetailModify() => clearField(8);
}

/// 巡查线路细表
class db_line_detail extends $pb.GeneratedMessage {
  factory db_line_detail({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? lineId,
    $core.String? pointId,
    $core.int? pointNo,
    $core.int? aheadTime,
    $core.int? delayTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (lineId != null) {
      $result.lineId = lineId;
    }
    if (pointId != null) {
      $result.pointId = pointId;
    }
    if (pointNo != null) {
      $result.pointNo = pointNo;
    }
    if (aheadTime != null) {
      $result.aheadTime = aheadTime;
    }
    if (delayTime != null) {
      $result.delayTime = delayTime;
    }
    return $result;
  }
  db_line_detail._() : super();
  factory db_line_detail.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_line_detail.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_line_detail', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'lineId')
    ..aOS(4, _omitFieldNames ? '' : 'pointId')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'pointNo', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'aheadTime', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'delayTime', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_line_detail clone() => db_line_detail()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_line_detail copyWith(void Function(db_line_detail) updates) => super.copyWith((message) => updates(message as db_line_detail)) as db_line_detail;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_line_detail create() => db_line_detail._();
  db_line_detail createEmptyInstance() => create();
  static $pb.PbList<db_line_detail> createRepeated() => $pb.PbList<db_line_detail>();
  @$core.pragma('dart2js:noInline')
  static db_line_detail getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_line_detail>(create);
  static db_line_detail? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid REFERENCES db_line_master(rid) ON DELETE CASCADE
  /// 线路的rid
  @$pb.TagNumber(3)
  $core.String get lineId => $_getSZ(2);
  @$pb.TagNumber(3)
  set lineId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLineId() => $_has(2);
  @$pb.TagNumber(3)
  void clearLineId() => clearField(3);

  /// @table uuid REFERENCES db_line_point(rid) ON DELETE CASCADE
  /// 点的rid
  @$pb.TagNumber(4)
  $core.String get pointId => $_getSZ(3);
  @$pb.TagNumber(4)
  set pointId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPointId() => $_has(3);
  @$pb.TagNumber(4)
  void clearPointId() => clearField(4);

  /// @table int
  /// 点在线路中的序号
  @$pb.TagNumber(5)
  $core.int get pointNo => $_getIZ(4);
  @$pb.TagNumber(5)
  set pointNo($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPointNo() => $_has(4);
  @$pb.TagNumber(5)
  void clearPointNo() => clearField(5);

  /// @table int not null default 10
  /// 最快到达时间,单位为分钟
  @$pb.TagNumber(6)
  $core.int get aheadTime => $_getIZ(5);
  @$pb.TagNumber(6)
  set aheadTime($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasAheadTime() => $_has(5);
  @$pb.TagNumber(6)
  void clearAheadTime() => clearField(6);

  /// @table int not null default 10
  /// 最迟到达时间,单位为分钟
  @$pb.TagNumber(7)
  $core.int get delayTime => $_getIZ(6);
  @$pb.TagNumber(7)
  set delayTime($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasDelayTime() => $_has(6);
  @$pb.TagNumber(7)
  void clearDelayTime() => clearField(7);
}

/// 巡查规则表
class db_rfid_rule_master extends $pb.GeneratedMessage {
  factory db_rfid_rule_master({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? ruleId,
    $core.String? ruleName,
    $core.String? note,
    $core.bool? day1,
    $core.bool? day2,
    $core.bool? day3,
    $core.bool? day4,
    $core.bool? day5,
    $core.bool? day6,
    $core.bool? day7,
    $core.String? checkStartTime,
    $core.int? checkAllTime,
    $core.int? checkCount,
    $core.int? ruleEffectiveType,
    $core.String? ruleEffectiveStart,
    $core.String? ruleEffectiveEnd,
    $core.String? ruleLineRid,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (ruleId != null) {
      $result.ruleId = ruleId;
    }
    if (ruleName != null) {
      $result.ruleName = ruleName;
    }
    if (note != null) {
      $result.note = note;
    }
    if (day1 != null) {
      $result.day1 = day1;
    }
    if (day2 != null) {
      $result.day2 = day2;
    }
    if (day3 != null) {
      $result.day3 = day3;
    }
    if (day4 != null) {
      $result.day4 = day4;
    }
    if (day5 != null) {
      $result.day5 = day5;
    }
    if (day6 != null) {
      $result.day6 = day6;
    }
    if (day7 != null) {
      $result.day7 = day7;
    }
    if (checkStartTime != null) {
      $result.checkStartTime = checkStartTime;
    }
    if (checkAllTime != null) {
      $result.checkAllTime = checkAllTime;
    }
    if (checkCount != null) {
      $result.checkCount = checkCount;
    }
    if (ruleEffectiveType != null) {
      $result.ruleEffectiveType = ruleEffectiveType;
    }
    if (ruleEffectiveStart != null) {
      $result.ruleEffectiveStart = ruleEffectiveStart;
    }
    if (ruleEffectiveEnd != null) {
      $result.ruleEffectiveEnd = ruleEffectiveEnd;
    }
    if (ruleLineRid != null) {
      $result.ruleLineRid = ruleLineRid;
    }
    return $result;
  }
  db_rfid_rule_master._() : super();
  factory db_rfid_rule_master.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_rfid_rule_master.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_rfid_rule_master', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'ruleId')
    ..aOS(5, _omitFieldNames ? '' : 'ruleName')
    ..aOS(6, _omitFieldNames ? '' : 'note')
    ..aOB(7, _omitFieldNames ? '' : 'day1', protoName: 'day_1')
    ..aOB(8, _omitFieldNames ? '' : 'day2', protoName: 'day_2')
    ..aOB(9, _omitFieldNames ? '' : 'day3', protoName: 'day_3')
    ..aOB(10, _omitFieldNames ? '' : 'day4', protoName: 'day_4')
    ..aOB(11, _omitFieldNames ? '' : 'day5', protoName: 'day_5')
    ..aOB(12, _omitFieldNames ? '' : 'day6', protoName: 'day_6')
    ..aOB(13, _omitFieldNames ? '' : 'day7', protoName: 'day_7')
    ..aOS(14, _omitFieldNames ? '' : 'checkStartTime')
    ..a<$core.int>(15, _omitFieldNames ? '' : 'checkAllTime', $pb.PbFieldType.O3)
    ..a<$core.int>(16, _omitFieldNames ? '' : 'checkCount', $pb.PbFieldType.O3)
    ..a<$core.int>(17, _omitFieldNames ? '' : 'ruleEffectiveType', $pb.PbFieldType.O3)
    ..aOS(18, _omitFieldNames ? '' : 'ruleEffectiveStart')
    ..aOS(19, _omitFieldNames ? '' : 'ruleEffectiveEnd')
    ..aOS(20, _omitFieldNames ? '' : 'ruleLineRid')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_rfid_rule_master clone() => db_rfid_rule_master()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_rfid_rule_master copyWith(void Function(db_rfid_rule_master) updates) => super.copyWith((message) => updates(message as db_rfid_rule_master)) as db_rfid_rule_master;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_rfid_rule_master create() => db_rfid_rule_master._();
  db_rfid_rule_master createEmptyInstance() => create();
  static $pb.PbList<db_rfid_rule_master> createRepeated() => $pb.PbList<db_rfid_rule_master>();
  @$core.pragma('dart2js:noInline')
  static db_rfid_rule_master getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_rfid_rule_master>(create);
  static db_rfid_rule_master? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null
  /// 编号
  @$pb.TagNumber(4)
  $core.String get ruleId => $_getSZ(3);
  @$pb.TagNumber(4)
  set ruleId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasRuleId() => $_has(3);
  @$pb.TagNumber(4)
  void clearRuleId() => clearField(4);

  /// @table varchar(16) not null
  /// 标志名称
  @$pb.TagNumber(5)
  $core.String get ruleName => $_getSZ(4);
  @$pb.TagNumber(5)
  set ruleName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasRuleName() => $_has(4);
  @$pb.TagNumber(5)
  void clearRuleName() => clearField(5);

  /// @table text
  /// note
  @$pb.TagNumber(6)
  $core.String get note => $_getSZ(5);
  @$pb.TagNumber(6)
  set note($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasNote() => $_has(5);
  @$pb.TagNumber(6)
  void clearNote() => clearField(6);

  /// @table boolean default false
  /// 星期一
  @$pb.TagNumber(7)
  $core.bool get day1 => $_getBF(6);
  @$pb.TagNumber(7)
  set day1($core.bool v) { $_setBool(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasDay1() => $_has(6);
  @$pb.TagNumber(7)
  void clearDay1() => clearField(7);

  /// @table boolean default false
  /// 星期二
  @$pb.TagNumber(8)
  $core.bool get day2 => $_getBF(7);
  @$pb.TagNumber(8)
  set day2($core.bool v) { $_setBool(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasDay2() => $_has(7);
  @$pb.TagNumber(8)
  void clearDay2() => clearField(8);

  /// @table boolean default false
  /// 星期三
  @$pb.TagNumber(9)
  $core.bool get day3 => $_getBF(8);
  @$pb.TagNumber(9)
  set day3($core.bool v) { $_setBool(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasDay3() => $_has(8);
  @$pb.TagNumber(9)
  void clearDay3() => clearField(9);

  /// @table boolean default false
  /// 星期四
  @$pb.TagNumber(10)
  $core.bool get day4 => $_getBF(9);
  @$pb.TagNumber(10)
  set day4($core.bool v) { $_setBool(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasDay4() => $_has(9);
  @$pb.TagNumber(10)
  void clearDay4() => clearField(10);

  /// @table boolean default false
  /// 星期五
  @$pb.TagNumber(11)
  $core.bool get day5 => $_getBF(10);
  @$pb.TagNumber(11)
  set day5($core.bool v) { $_setBool(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasDay5() => $_has(10);
  @$pb.TagNumber(11)
  void clearDay5() => clearField(11);

  /// @table boolean default false
  /// 星期六
  @$pb.TagNumber(12)
  $core.bool get day6 => $_getBF(11);
  @$pb.TagNumber(12)
  set day6($core.bool v) { $_setBool(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasDay6() => $_has(11);
  @$pb.TagNumber(12)
  void clearDay6() => clearField(12);

  /// @table boolean default false
  /// 星期七
  @$pb.TagNumber(13)
  $core.bool get day7 => $_getBF(12);
  @$pb.TagNumber(13)
  set day7($core.bool v) { $_setBool(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasDay7() => $_has(12);
  @$pb.TagNumber(13)
  void clearDay7() => clearField(13);

  /// @table time
  /// 巡查开始的时间
  @$pb.TagNumber(14)
  $core.String get checkStartTime => $_getSZ(13);
  @$pb.TagNumber(14)
  set checkStartTime($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasCheckStartTime() => $_has(13);
  @$pb.TagNumber(14)
  void clearCheckStartTime() => clearField(14);

  /// @table int
  /// 巡查一次需要的时间,单位为分钟
  @$pb.TagNumber(15)
  $core.int get checkAllTime => $_getIZ(14);
  @$pb.TagNumber(15)
  set checkAllTime($core.int v) { $_setSignedInt32(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasCheckAllTime() => $_has(14);
  @$pb.TagNumber(15)
  void clearCheckAllTime() => clearField(15);

  /// @table int not null default 1
  /// 总共需要巡查的次数
  @$pb.TagNumber(16)
  $core.int get checkCount => $_getIZ(15);
  @$pb.TagNumber(16)
  set checkCount($core.int v) { $_setSignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasCheckCount() => $_has(15);
  @$pb.TagNumber(16)
  void clearCheckCount() => clearField(16);

  /// @table int default 0
  /// 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
  @$pb.TagNumber(17)
  $core.int get ruleEffectiveType => $_getIZ(16);
  @$pb.TagNumber(17)
  set ruleEffectiveType($core.int v) { $_setSignedInt32(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasRuleEffectiveType() => $_has(16);
  @$pb.TagNumber(17)
  void clearRuleEffectiveType() => clearField(17);

  /// @table timestamp
  /// 规则开始生效时间
  @$pb.TagNumber(18)
  $core.String get ruleEffectiveStart => $_getSZ(17);
  @$pb.TagNumber(18)
  set ruleEffectiveStart($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasRuleEffectiveStart() => $_has(17);
  @$pb.TagNumber(18)
  void clearRuleEffectiveStart() => clearField(18);

  /// @table timestamp
  /// 规则生效结束时间
  @$pb.TagNumber(19)
  $core.String get ruleEffectiveEnd => $_getSZ(18);
  @$pb.TagNumber(19)
  set ruleEffectiveEnd($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasRuleEffectiveEnd() => $_has(18);
  @$pb.TagNumber(19)
  void clearRuleEffectiveEnd() => clearField(19);

  /// @table uuid  REFERENCES db_line_master(rid) ON DELETE SET NULL
  /// 巡查规则对应的线路
  @$pb.TagNumber(20)
  $core.String get ruleLineRid => $_getSZ(19);
  @$pb.TagNumber(20)
  set ruleLineRid($core.String v) { $_setString(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasRuleLineRid() => $_has(19);
  @$pb.TagNumber(20)
  void clearRuleLineRid() => clearField(20);
}

/// 开关机数据表,按月分表
class db_device_power_onoff extends $pb.GeneratedMessage {
  factory db_device_power_onoff({
    $core.String? rid,
    $core.String? orgId,
    $core.String? deviceId,
    $core.String? userId,
    $core.String? actionTime,
    $core.int? actionType,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (deviceId != null) {
      $result.deviceId = deviceId;
    }
    if (userId != null) {
      $result.userId = userId;
    }
    if (actionTime != null) {
      $result.actionTime = actionTime;
    }
    if (actionType != null) {
      $result.actionType = actionType;
    }
    return $result;
  }
  db_device_power_onoff._() : super();
  factory db_device_power_onoff.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_power_onoff.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_power_onoff', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'deviceId')
    ..aOS(6, _omitFieldNames ? '' : 'userId')
    ..aOS(8, _omitFieldNames ? '' : 'actionTime')
    ..a<$core.int>(9, _omitFieldNames ? '' : 'actionType', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_power_onoff clone() => db_device_power_onoff()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_power_onoff copyWith(void Function(db_device_power_onoff) updates) => super.copyWith((message) => updates(message as db_device_power_onoff)) as db_device_power_onoff;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_power_onoff create() => db_device_power_onoff._();
  db_device_power_onoff createEmptyInstance() => create();
  static $pb.PbList<db_device_power_onoff> createRepeated() => $pb.PbList<db_device_power_onoff>();
  @$core.pragma('dart2js:noInline')
  static db_device_power_onoff getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_power_onoff>(create);
  static db_device_power_onoff? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 对讲机所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table uuid REFERENCES db_device(rid) ON DELETE CASCADE
  /// device rid
  @$pb.TagNumber(4)
  $core.String get deviceId => $_getSZ(2);
  @$pb.TagNumber(4)
  set deviceId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeviceId() => $_has(2);
  @$pb.TagNumber(4)
  void clearDeviceId() => clearField(4);

  /// @table uuid
  /// 对讲机使用人员id
  @$pb.TagNumber(6)
  $core.String get userId => $_getSZ(3);
  @$pb.TagNumber(6)
  set userId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasUserId() => $_has(3);
  @$pb.TagNumber(6)
  void clearUserId() => clearField(6);

  /// @table timestamp not null
  /// 动作时间
  @$pb.TagNumber(8)
  $core.String get actionTime => $_getSZ(4);
  @$pb.TagNumber(8)
  set actionTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(8)
  $core.bool hasActionTime() => $_has(4);
  @$pb.TagNumber(8)
  void clearActionTime() => clearField(8);

  /// @table int
  /// 动作类型 0=关机；1=正常开机；2=电池开机；3=欠压复位开机
  @$pb.TagNumber(9)
  $core.int get actionType => $_getIZ(5);
  @$pb.TagNumber(9)
  set actionType($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(9)
  $core.bool hasActionType() => $_has(5);
  @$pb.TagNumber(9)
  void clearActionType() => clearField(9);
}

/// 上班下班打卡数据表,按月分表
class db_user_check_in_history extends $pb.GeneratedMessage {
  factory db_user_check_in_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? deviceId,
    $core.String? userId,
    $core.String? actionTime,
    $core.int? actionType,
    $core.String? rfidId,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (deviceId != null) {
      $result.deviceId = deviceId;
    }
    if (userId != null) {
      $result.userId = userId;
    }
    if (actionTime != null) {
      $result.actionTime = actionTime;
    }
    if (actionType != null) {
      $result.actionType = actionType;
    }
    if (rfidId != null) {
      $result.rfidId = rfidId;
    }
    return $result;
  }
  db_user_check_in_history._() : super();
  factory db_user_check_in_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_check_in_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_check_in_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'deviceId')
    ..aOS(6, _omitFieldNames ? '' : 'userId')
    ..aOS(8, _omitFieldNames ? '' : 'actionTime')
    ..a<$core.int>(9, _omitFieldNames ? '' : 'actionType', $pb.PbFieldType.O3)
    ..aOS(10, _omitFieldNames ? '' : 'rfidId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_check_in_history clone() => db_user_check_in_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_check_in_history copyWith(void Function(db_user_check_in_history) updates) => super.copyWith((message) => updates(message as db_user_check_in_history)) as db_user_check_in_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_check_in_history create() => db_user_check_in_history._();
  db_user_check_in_history createEmptyInstance() => create();
  static $pb.PbList<db_user_check_in_history> createRepeated() => $pb.PbList<db_user_check_in_history>();
  @$core.pragma('dart2js:noInline')
  static db_user_check_in_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_check_in_history>(create);
  static db_user_check_in_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 对讲机所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table uuid REFERENCES db_device(rid) ON DELETE CASCADE
  /// device rid
  @$pb.TagNumber(4)
  $core.String get deviceId => $_getSZ(2);
  @$pb.TagNumber(4)
  set deviceId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeviceId() => $_has(2);
  @$pb.TagNumber(4)
  void clearDeviceId() => clearField(4);

  /// @table uuid
  /// 对讲机使用人员id
  @$pb.TagNumber(6)
  $core.String get userId => $_getSZ(3);
  @$pb.TagNumber(6)
  set userId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(6)
  $core.bool hasUserId() => $_has(3);
  @$pb.TagNumber(6)
  void clearUserId() => clearField(6);

  /// @table timestamp not null
  /// 读卡时间
  @$pb.TagNumber(8)
  $core.String get actionTime => $_getSZ(4);
  @$pb.TagNumber(8)
  set actionTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(8)
  $core.bool hasActionTime() => $_has(4);
  @$pb.TagNumber(8)
  void clearActionTime() => clearField(8);

  /// @table int
  /// 动作类型 1上班,2上班中打卡,3下班,
  @$pb.TagNumber(9)
  $core.int get actionType => $_getIZ(5);
  @$pb.TagNumber(9)
  set actionType($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(9)
  $core.bool hasActionType() => $_has(5);
  @$pb.TagNumber(9)
  void clearActionType() => clearField(9);

  /// @table varchar(10)
  /// 身份卡的id
  @$pb.TagNumber(10)
  $core.String get rfidId => $_getSZ(6);
  @$pb.TagNumber(10)
  set rfidId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(10)
  $core.bool hasRfidId() => $_has(6);
  @$pb.TagNumber(10)
  void clearRfidId() => clearField(10);
}

/// rfid巡查历史表,按月分表
class db_rfid_history extends $pb.GeneratedMessage {
  factory db_rfid_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? checkTime,
    $core.String? checkerId,
    $core.int? devType,
    $core.String? deviceId,
    $core.String? receiveTime,
    $core.String? receiver,
    $core.String? pointId,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (checkTime != null) {
      $result.checkTime = checkTime;
    }
    if (checkerId != null) {
      $result.checkerId = checkerId;
    }
    if (devType != null) {
      $result.devType = devType;
    }
    if (deviceId != null) {
      $result.deviceId = deviceId;
    }
    if (receiveTime != null) {
      $result.receiveTime = receiveTime;
    }
    if (receiver != null) {
      $result.receiver = receiver;
    }
    if (pointId != null) {
      $result.pointId = pointId;
    }
    return $result;
  }
  db_rfid_history._() : super();
  factory db_rfid_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_rfid_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_rfid_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'checkTime')
    ..aOS(4, _omitFieldNames ? '' : 'checkerId')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'devType', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'deviceId')
    ..aOS(8, _omitFieldNames ? '' : 'receiveTime')
    ..aOS(9, _omitFieldNames ? '' : 'receiver')
    ..aOS(10, _omitFieldNames ? '' : 'pointId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_rfid_history clone() => db_rfid_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_rfid_history copyWith(void Function(db_rfid_history) updates) => super.copyWith((message) => updates(message as db_rfid_history)) as db_rfid_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_rfid_history create() => db_rfid_history._();
  db_rfid_history createEmptyInstance() => create();
  static $pb.PbList<db_rfid_history> createRepeated() => $pb.PbList<db_rfid_history>();
  @$core.pragma('dart2js:noInline')
  static db_rfid_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_rfid_history>(create);
  static db_rfid_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 对讲机所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 巡查时间
  @$pb.TagNumber(3)
  $core.String get checkTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set checkTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCheckTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearCheckTime() => clearField(3);

  /// @table uuid
  /// 巡查人员rid
  @$pb.TagNumber(4)
  $core.String get checkerId => $_getSZ(3);
  @$pb.TagNumber(4)
  set checkerId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCheckerId() => $_has(3);
  @$pb.TagNumber(4)
  void clearCheckerId() => clearField(4);

  /// @table int not null default 0
  /// 打卡设备类型 0:对讲终端 1:对讲机 2:工牌 3:物卡 4：烟感  5：设备开关盒  6：信标开关盒
  @$pb.TagNumber(5)
  $core.int get devType => $_getIZ(4);
  @$pb.TagNumber(5)
  set devType($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDevType() => $_has(4);
  @$pb.TagNumber(5)
  void clearDevType() => clearField(5);

  /// @table uuid
  /// 对讲机rid
  @$pb.TagNumber(6)
  $core.String get deviceId => $_getSZ(5);
  @$pb.TagNumber(6)
  set deviceId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDeviceId() => $_has(5);
  @$pb.TagNumber(6)
  void clearDeviceId() => clearField(6);

  /// @table timestamp
  /// 接收时间,不一定是实时的
  @$pb.TagNumber(8)
  $core.String get receiveTime => $_getSZ(6);
  @$pb.TagNumber(8)
  set receiveTime($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(8)
  $core.bool hasReceiveTime() => $_has(6);
  @$pb.TagNumber(8)
  void clearReceiveTime() => clearField(8);

  /// @table varchar(16)
  /// 接收的控制器名称
  @$pb.TagNumber(9)
  $core.String get receiver => $_getSZ(7);
  @$pb.TagNumber(9)
  set receiver($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(9)
  $core.bool hasReceiver() => $_has(7);
  @$pb.TagNumber(9)
  void clearReceiver() => clearField(9);

  /// @table uuid REFERENCES db_line_point(rid) ON DELETE CASCADE
  /// 点的标识
  @$pb.TagNumber(10)
  $core.String get pointId => $_getSZ(8);
  @$pb.TagNumber(10)
  set pointId($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(10)
  $core.bool hasPointId() => $_has(8);
  @$pb.TagNumber(10)
  void clearPointId() => clearField(10);
}

/// gps位置历史表,按月分表
class db_gps_history extends $pb.GeneratedMessage {
  factory db_gps_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? gpsTime,
    $core.bool? gpsFixed,
    $core.double? lon,
    $core.double? lat,
    $core.double? speed,
    $core.int? direction,
    $core.int? altitude,
    $core.String? deviceId,
    $core.String? personId,
    $core.String? deviceStatus,
    $core.String? upCmd,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (gpsTime != null) {
      $result.gpsTime = gpsTime;
    }
    if (gpsFixed != null) {
      $result.gpsFixed = gpsFixed;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (speed != null) {
      $result.speed = speed;
    }
    if (direction != null) {
      $result.direction = direction;
    }
    if (altitude != null) {
      $result.altitude = altitude;
    }
    if (deviceId != null) {
      $result.deviceId = deviceId;
    }
    if (personId != null) {
      $result.personId = personId;
    }
    if (deviceStatus != null) {
      $result.deviceStatus = deviceStatus;
    }
    if (upCmd != null) {
      $result.upCmd = upCmd;
    }
    return $result;
  }
  db_gps_history._() : super();
  factory db_gps_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_gps_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_gps_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'gpsTime')
    ..aOB(4, _omitFieldNames ? '' : 'gpsFixed')
    ..a<$core.double>(5, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..a<$core.double>(6, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..a<$core.double>(7, _omitFieldNames ? '' : 'speed', $pb.PbFieldType.OD)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'direction', $pb.PbFieldType.O3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'altitude', $pb.PbFieldType.O3)
    ..aOS(10, _omitFieldNames ? '' : 'deviceId')
    ..aOS(11, _omitFieldNames ? '' : 'personId')
    ..aOS(12, _omitFieldNames ? '' : 'deviceStatus')
    ..aOS(13, _omitFieldNames ? '' : 'upCmd')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_gps_history clone() => db_gps_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_gps_history copyWith(void Function(db_gps_history) updates) => super.copyWith((message) => updates(message as db_gps_history)) as db_gps_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_gps_history create() => db_gps_history._();
  db_gps_history createEmptyInstance() => create();
  static $pb.PbList<db_gps_history> createRepeated() => $pb.PbList<db_gps_history>();
  @$core.pragma('dart2js:noInline')
  static db_gps_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_gps_history>(create);
  static db_gps_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 对讲机所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// gps time
  @$pb.TagNumber(3)
  $core.String get gpsTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set gpsTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGpsTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearGpsTime() => clearField(3);

  /// @table boolean
  /// gps valid
  @$pb.TagNumber(4)
  $core.bool get gpsFixed => $_getBF(3);
  @$pb.TagNumber(4)
  set gpsFixed($core.bool v) { $_setBool(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGpsFixed() => $_has(3);
  @$pb.TagNumber(4)
  void clearGpsFixed() => clearField(4);

  /// @table double precision
  /// longitude
  @$pb.TagNumber(5)
  $core.double get lon => $_getN(4);
  @$pb.TagNumber(5)
  set lon($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasLon() => $_has(4);
  @$pb.TagNumber(5)
  void clearLon() => clearField(5);

  /// @table double precision
  /// latitude
  @$pb.TagNumber(6)
  $core.double get lat => $_getN(5);
  @$pb.TagNumber(6)
  set lat($core.double v) { $_setDouble(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLat() => $_has(5);
  @$pb.TagNumber(6)
  void clearLat() => clearField(6);

  /// @table double precision
  /// gps speed
  @$pb.TagNumber(7)
  $core.double get speed => $_getN(6);
  @$pb.TagNumber(7)
  set speed($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSpeed() => $_has(6);
  @$pb.TagNumber(7)
  void clearSpeed() => clearField(7);

  /// @table int
  /// gps direction, north=0
  @$pb.TagNumber(8)
  $core.int get direction => $_getIZ(7);
  @$pb.TagNumber(8)
  set direction($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasDirection() => $_has(7);
  @$pb.TagNumber(8)
  void clearDirection() => clearField(8);

  /// @table int
  /// gps altitude
  @$pb.TagNumber(9)
  $core.int get altitude => $_getIZ(8);
  @$pb.TagNumber(9)
  set altitude($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasAltitude() => $_has(8);
  @$pb.TagNumber(9)
  void clearAltitude() => clearField(9);

  /// @table uuid
  /// device rid
  @$pb.TagNumber(10)
  $core.String get deviceId => $_getSZ(9);
  @$pb.TagNumber(10)
  set deviceId($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasDeviceId() => $_has(9);
  @$pb.TagNumber(10)
  void clearDeviceId() => clearField(10);

  /// @table uuid
  /// 相关用户rid
  @$pb.TagNumber(11)
  $core.String get personId => $_getSZ(10);
  @$pb.TagNumber(11)
  set personId($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasPersonId() => $_has(10);
  @$pb.TagNumber(11)
  void clearPersonId() => clearField(11);

  /// @table text
  /// 对讲机状态
  @$pb.TagNumber(12)
  $core.String get deviceStatus => $_getSZ(11);
  @$pb.TagNumber(12)
  set deviceStatus($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasDeviceStatus() => $_has(11);
  @$pb.TagNumber(12)
  void clearDeviceStatus() => clearField(12);

  /// @table varchar(12)
  /// 是什么命令上来的gps数据
  @$pb.TagNumber(13)
  $core.String get upCmd => $_getSZ(12);
  @$pb.TagNumber(13)
  set upCmd($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasUpCmd() => $_has(12);
  @$pb.TagNumber(13)
  void clearUpCmd() => clearField(13);
}

/// 报警历史,要分表了,因为报警可能会非常多,客户端需要编辑此表,分表客户端处理需要特殊处理
class db_alarm_history extends $pb.GeneratedMessage {
  factory db_alarm_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? alarmTime,
    $core.String? deviceId,
    $core.int? alarmDevType,
    $core.String? personId,
    $core.String? deallerRid,
    $core.String? deallerTime,
    $core.String? deallerResult,
    $core.String? alarmType,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (alarmTime != null) {
      $result.alarmTime = alarmTime;
    }
    if (deviceId != null) {
      $result.deviceId = deviceId;
    }
    if (alarmDevType != null) {
      $result.alarmDevType = alarmDevType;
    }
    if (personId != null) {
      $result.personId = personId;
    }
    if (deallerRid != null) {
      $result.deallerRid = deallerRid;
    }
    if (deallerTime != null) {
      $result.deallerTime = deallerTime;
    }
    if (deallerResult != null) {
      $result.deallerResult = deallerResult;
    }
    if (alarmType != null) {
      $result.alarmType = alarmType;
    }
    return $result;
  }
  db_alarm_history._() : super();
  factory db_alarm_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_alarm_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_alarm_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'alarmTime')
    ..aOS(4, _omitFieldNames ? '' : 'deviceId')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'alarmDevType', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'personId')
    ..aOS(8, _omitFieldNames ? '' : 'deallerRid')
    ..aOS(9, _omitFieldNames ? '' : 'deallerTime')
    ..aOS(10, _omitFieldNames ? '' : 'deallerResult')
    ..aOS(11, _omitFieldNames ? '' : 'alarmType')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_alarm_history clone() => db_alarm_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_alarm_history copyWith(void Function(db_alarm_history) updates) => super.copyWith((message) => updates(message as db_alarm_history)) as db_alarm_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_alarm_history create() => db_alarm_history._();
  db_alarm_history createEmptyInstance() => create();
  static $pb.PbList<db_alarm_history> createRepeated() => $pb.PbList<db_alarm_history>();
  @$core.pragma('dart2js:noInline')
  static db_alarm_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_alarm_history>(create);
  static db_alarm_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 对讲机所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 报警时间
  @$pb.TagNumber(3)
  $core.String get alarmTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set alarmTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasAlarmTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearAlarmTime() => clearField(3);

  /// @table uuid
  /// 对讲机rid
  @$pb.TagNumber(4)
  $core.String get deviceId => $_getSZ(3);
  @$pb.TagNumber(4)
  set deviceId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeviceId() => $_has(3);
  @$pb.TagNumber(4)
  void clearDeviceId() => clearField(4);

  /// @table int not null default 0
  /// 报警设备类型 0:对讲终端 1:对讲机 2:工牌 3:物卡 4：烟感  5：节能灯  6：温湿度
  @$pb.TagNumber(5)
  $core.int get alarmDevType => $_getIZ(4);
  @$pb.TagNumber(5)
  set alarmDevType($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasAlarmDevType() => $_has(4);
  @$pb.TagNumber(5)
  void clearAlarmDevType() => clearField(5);

  /// @table uuid
  /// 对讲机使用人员id
  @$pb.TagNumber(6)
  $core.String get personId => $_getSZ(5);
  @$pb.TagNumber(6)
  set personId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPersonId() => $_has(5);
  @$pb.TagNumber(6)
  void clearPersonId() => clearField(6);

  /// @table text
  /// 处理人员rid
  @$pb.TagNumber(8)
  $core.String get deallerRid => $_getSZ(6);
  @$pb.TagNumber(8)
  set deallerRid($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(8)
  $core.bool hasDeallerRid() => $_has(6);
  @$pb.TagNumber(8)
  void clearDeallerRid() => clearField(8);

  /// @table timestamp
  /// 处理时间
  @$pb.TagNumber(9)
  $core.String get deallerTime => $_getSZ(7);
  @$pb.TagNumber(9)
  set deallerTime($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(9)
  $core.bool hasDeallerTime() => $_has(7);
  @$pb.TagNumber(9)
  void clearDeallerTime() => clearField(9);

  /// @table jsonb  not null default '{}'::jsonb
  /// 处理内容
  @$pb.TagNumber(10)
  $core.String get deallerResult => $_getSZ(8);
  @$pb.TagNumber(10)
  set deallerResult($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(10)
  $core.bool hasDeallerResult() => $_has(8);
  @$pb.TagNumber(10)
  void clearDeallerResult() => clearField(10);

  /// @table text
  /// 报警列表,数值列表，逗号分隔, 1=紧急报警；2=强行脱网报警；3=发生欠压报警；4=GPS故障报警;5=GPS遮挡;6:防拆报警;7:节能灯人感应报警;8:节能灯声音感应报警
  /// bcxx类报警 41=入界报警;42=出界报警;51=入界回岗;52=出界离岗;61=移动监控走动提示;62=移动监控停留报警
  @$pb.TagNumber(11)
  $core.String get alarmType => $_getSZ(9);
  @$pb.TagNumber(11)
  set alarmType($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(11)
  $core.bool hasAlarmType() => $_has(9);
  @$pb.TagNumber(11)
  void clearAlarmType() => clearField(11);
}

/// 对讲机通话历史,按月分表
class db_sound_history extends $pb.GeneratedMessage {
  factory db_sound_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? deviceId,
    $core.String? sourceInfo,
    $core.String? soundTime,
    $core.int? soundLen,
    $core.int? channel,
    $core.String? controller,
    $core.String? fileName,
    $core.String? personId,
    $core.String? target,
    $core.String? targetInfo,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (deviceId != null) {
      $result.deviceId = deviceId;
    }
    if (sourceInfo != null) {
      $result.sourceInfo = sourceInfo;
    }
    if (soundTime != null) {
      $result.soundTime = soundTime;
    }
    if (soundLen != null) {
      $result.soundLen = soundLen;
    }
    if (channel != null) {
      $result.channel = channel;
    }
    if (controller != null) {
      $result.controller = controller;
    }
    if (fileName != null) {
      $result.fileName = fileName;
    }
    if (personId != null) {
      $result.personId = personId;
    }
    if (target != null) {
      $result.target = target;
    }
    if (targetInfo != null) {
      $result.targetInfo = targetInfo;
    }
    return $result;
  }
  db_sound_history._() : super();
  factory db_sound_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_sound_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_sound_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'deviceId')
    ..aOS(4, _omitFieldNames ? '' : 'sourceInfo')
    ..aOS(5, _omitFieldNames ? '' : 'soundTime')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'soundLen', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'channel', $pb.PbFieldType.O3)
    ..aOS(8, _omitFieldNames ? '' : 'controller')
    ..aOS(9, _omitFieldNames ? '' : 'fileName')
    ..aOS(10, _omitFieldNames ? '' : 'personId')
    ..aOS(11, _omitFieldNames ? '' : 'target')
    ..aOS(12, _omitFieldNames ? '' : 'targetInfo')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_sound_history clone() => db_sound_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_sound_history copyWith(void Function(db_sound_history) updates) => super.copyWith((message) => updates(message as db_sound_history)) as db_sound_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_sound_history create() => db_sound_history._();
  db_sound_history createEmptyInstance() => create();
  static $pb.PbList<db_sound_history> createRepeated() => $pb.PbList<db_sound_history>();
  @$core.pragma('dart2js:noInline')
  static db_sound_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_sound_history>(create);
  static db_sound_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 对讲机所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table uuid
  /// 对讲机rid
  @$pb.TagNumber(3)
  $core.String get deviceId => $_getSZ(2);
  @$pb.TagNumber(3)
  set deviceId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDeviceId() => $_has(2);
  @$pb.TagNumber(3)
  void clearDeviceId() => clearField(3);

  /// @table text
  /// 发起者额外信息,类似电话网关打电话时的电话号码
  @$pb.TagNumber(4)
  $core.String get sourceInfo => $_getSZ(3);
  @$pb.TagNumber(4)
  set sourceInfo($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSourceInfo() => $_has(3);
  @$pb.TagNumber(4)
  void clearSourceInfo() => clearField(4);

  /// @table timestamp
  /// 通话开始时间
  @$pb.TagNumber(5)
  $core.String get soundTime => $_getSZ(4);
  @$pb.TagNumber(5)
  set soundTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSoundTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearSoundTime() => clearField(5);

  /// @table int default 0
  /// 对讲时长,秒
  @$pb.TagNumber(6)
  $core.int get soundLen => $_getIZ(5);
  @$pb.TagNumber(6)
  set soundLen($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSoundLen() => $_has(5);
  @$pb.TagNumber(6)
  void clearSoundLen() => clearField(6);

  /// @table int
  /// 对讲所在信道
  @$pb.TagNumber(7)
  $core.int get channel => $_getIZ(6);
  @$pb.TagNumber(7)
  set channel($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasChannel() => $_has(6);
  @$pb.TagNumber(7)
  void clearChannel() => clearField(7);

  /// @table varchar(16)
  /// 对讲所在控制器dmr-id
  @$pb.TagNumber(8)
  $core.String get controller => $_getSZ(7);
  @$pb.TagNumber(8)
  set controller($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasController() => $_has(7);
  @$pb.TagNumber(8)
  void clearController() => clearField(8);

  /// @table text
  /// 声音文件名
  @$pb.TagNumber(9)
  $core.String get fileName => $_getSZ(8);
  @$pb.TagNumber(9)
  set fileName($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasFileName() => $_has(8);
  @$pb.TagNumber(9)
  void clearFileName() => clearField(9);

  /// @table uuid
  /// 对讲机使用人员id
  @$pb.TagNumber(10)
  $core.String get personId => $_getSZ(9);
  @$pb.TagNumber(10)
  set personId($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasPersonId() => $_has(9);
  @$pb.TagNumber(10)
  void clearPersonId() => clearField(10);

  /// @table text not null default ''
  /// 通话目标dmrid
  @$pb.TagNumber(11)
  $core.String get target => $_getSZ(10);
  @$pb.TagNumber(11)
  set target($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasTarget() => $_has(10);
  @$pb.TagNumber(11)
  void clearTarget() => clearField(11);

  /// @table text
  /// 通话目标额外信息,可能是电话号码
  @$pb.TagNumber(12)
  $core.String get targetInfo => $_getSZ(11);
  @$pb.TagNumber(12)
  set targetInfo($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasTargetInfo() => $_has(11);
  @$pb.TagNumber(12)
  void clearTargetInfo() => clearField(12);
}

/// 还没发送的命令
class db_not_send_cmd extends $pb.GeneratedMessage {
  factory db_not_send_cmd({
    $core.String? rid,
    $core.String? orgId,
    $core.String? scheduleTime,
    $core.String? stopTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (scheduleTime != null) {
      $result.scheduleTime = scheduleTime;
    }
    if (stopTime != null) {
      $result.stopTime = stopTime;
    }
    return $result;
  }
  db_not_send_cmd._() : super();
  factory db_not_send_cmd.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_not_send_cmd.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_not_send_cmd', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(6, _omitFieldNames ? '' : 'scheduleTime')
    ..aOS(7, _omitFieldNames ? '' : 'stopTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_not_send_cmd clone() => db_not_send_cmd()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_not_send_cmd copyWith(void Function(db_not_send_cmd) updates) => super.copyWith((message) => updates(message as db_not_send_cmd)) as db_not_send_cmd;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_not_send_cmd create() => db_not_send_cmd._();
  db_not_send_cmd createEmptyInstance() => create();
  static $pb.PbList<db_not_send_cmd> createRepeated() => $pb.PbList<db_not_send_cmd>();
  @$core.pragma('dart2js:noInline')
  static db_not_send_cmd getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_not_send_cmd>(create);
  static db_not_send_cmd? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 用户/对讲机所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 预定发送时间
  @$pb.TagNumber(6)
  $core.String get scheduleTime => $_getSZ(2);
  @$pb.TagNumber(6)
  set scheduleTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(6)
  $core.bool hasScheduleTime() => $_has(2);
  @$pb.TagNumber(6)
  void clearScheduleTime() => clearField(6);

  /// @table timestamp
  /// 截止日期,(有效期)
  @$pb.TagNumber(7)
  $core.String get stopTime => $_getSZ(3);
  @$pb.TagNumber(7)
  set stopTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(7)
  $core.bool hasStopTime() => $_has(3);
  @$pb.TagNumber(7)
  void clearStopTime() => clearField(7);
}

/// 已经发送的命令列表
class db_sent_cmd_history extends $pb.GeneratedMessage {
  factory db_sent_cmd_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? inputTime,
    $core.String? inputUserId,
    $core.int? senderType,
    $core.String? scheduleTime,
    $core.String? stopTime,
    $core.String? cmdTarget,
    $core.String? cmdTargetConSeq,
    $core.String? sendCmd,
    $core.String? origCbxx,
    $core.String? cmdParams,
    $core.String? sendTimeList,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (inputTime != null) {
      $result.inputTime = inputTime;
    }
    if (inputUserId != null) {
      $result.inputUserId = inputUserId;
    }
    if (senderType != null) {
      $result.senderType = senderType;
    }
    if (scheduleTime != null) {
      $result.scheduleTime = scheduleTime;
    }
    if (stopTime != null) {
      $result.stopTime = stopTime;
    }
    if (cmdTarget != null) {
      $result.cmdTarget = cmdTarget;
    }
    if (cmdTargetConSeq != null) {
      $result.cmdTargetConSeq = cmdTargetConSeq;
    }
    if (sendCmd != null) {
      $result.sendCmd = sendCmd;
    }
    if (origCbxx != null) {
      $result.origCbxx = origCbxx;
    }
    if (cmdParams != null) {
      $result.cmdParams = cmdParams;
    }
    if (sendTimeList != null) {
      $result.sendTimeList = sendTimeList;
    }
    return $result;
  }
  db_sent_cmd_history._() : super();
  factory db_sent_cmd_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_sent_cmd_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_sent_cmd_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'inputTime')
    ..aOS(4, _omitFieldNames ? '' : 'inputUserId')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'senderType', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'scheduleTime')
    ..aOS(7, _omitFieldNames ? '' : 'stopTime')
    ..aOS(8, _omitFieldNames ? '' : 'cmdTarget')
    ..aOS(9, _omitFieldNames ? '' : 'cmdTargetConSeq')
    ..aOS(10, _omitFieldNames ? '' : 'sendCmd')
    ..aOS(11, _omitFieldNames ? '' : 'origCbxx')
    ..aOS(12, _omitFieldNames ? '' : 'cmdParams')
    ..aOS(13, _omitFieldNames ? '' : 'sendTimeList')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_sent_cmd_history clone() => db_sent_cmd_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_sent_cmd_history copyWith(void Function(db_sent_cmd_history) updates) => super.copyWith((message) => updates(message as db_sent_cmd_history)) as db_sent_cmd_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_sent_cmd_history create() => db_sent_cmd_history._();
  db_sent_cmd_history createEmptyInstance() => create();
  static $pb.PbList<db_sent_cmd_history> createRepeated() => $pb.PbList<db_sent_cmd_history>();
  @$core.pragma('dart2js:noInline')
  static db_sent_cmd_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_sent_cmd_history>(create);
  static db_sent_cmd_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// (发起者)用户/对讲机所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 命令接收的时间
  @$pb.TagNumber(3)
  $core.String get inputTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set inputTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasInputTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearInputTime() => clearField(3);

  /// @table uuid
  /// 输入的用户id,可能是对讲机设备,不一定就是系统软件的用户
  @$pb.TagNumber(4)
  $core.String get inputUserId => $_getSZ(3);
  @$pb.TagNumber(4)
  set inputUserId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasInputUserId() => $_has(3);
  @$pb.TagNumber(4)
  void clearInputUserId() => clearField(4);

  /// @table int not null default 0
  /// 0:系统用户发起的命令,1:对讲机发起的命令
  @$pb.TagNumber(5)
  $core.int get senderType => $_getIZ(4);
  @$pb.TagNumber(5)
  set senderType($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSenderType() => $_has(4);
  @$pb.TagNumber(5)
  void clearSenderType() => clearField(5);

  /// @table timestamp
  /// 预定发送时间
  @$pb.TagNumber(6)
  $core.String get scheduleTime => $_getSZ(5);
  @$pb.TagNumber(6)
  set scheduleTime($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasScheduleTime() => $_has(5);
  @$pb.TagNumber(6)
  void clearScheduleTime() => clearField(6);

  /// @table timestamp
  /// 截止日期,(有效期)
  @$pb.TagNumber(7)
  $core.String get stopTime => $_getSZ(6);
  @$pb.TagNumber(7)
  set stopTime($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasStopTime() => $_has(6);
  @$pb.TagNumber(7)
  void clearStopTime() => clearField(7);

  /// @table jsonb not null default '{"group":[],"device":[],"response":{}}'::jsonb
  /// 发送目标列表,dmr_id数组
  /// 回应状况为dmr_id:{res_time:xxxx,res_con:dmr_id}
  @$pb.TagNumber(8)
  $core.String get cmdTarget => $_getSZ(7);
  @$pb.TagNumber(8)
  set cmdTarget($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasCmdTarget() => $_has(7);
  @$pb.TagNumber(8)
  void clearCmdTarget() => clearField(8);

  /// @table jsonb not null default '{}'::jsonb
  /// 每一个发送目标对应要发送控制器列表和序号
  @$pb.TagNumber(9)
  $core.String get cmdTargetConSeq => $_getSZ(8);
  @$pb.TagNumber(9)
  set cmdTargetConSeq($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasCmdTargetConSeq() => $_has(8);
  @$pb.TagNumber(9)
  void clearCmdTargetConSeq() => clearField(9);

  /// @table text
  /// 发送的命令,如CB01/CB24
  @$pb.TagNumber(10)
  $core.String get sendCmd => $_getSZ(9);
  @$pb.TagNumber(10)
  set sendCmd($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasSendCmd() => $_has(9);
  @$pb.TagNumber(10)
  void clearSendCmd() => clearField(10);

  /// @table jsonb not null default '{}'::jsonb
  /// cbxx json
  @$pb.TagNumber(11)
  $core.String get origCbxx => $_getSZ(10);
  @$pb.TagNumber(11)
  set origCbxx($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasOrigCbxx() => $_has(10);
  @$pb.TagNumber(11)
  void clearOrigCbxx() => clearField(11);

  /// @table jsonb not null default '{}'::jsonb
  /// 一些命令相关的参数
  @$pb.TagNumber(12)
  $core.String get cmdParams => $_getSZ(11);
  @$pb.TagNumber(12)
  set cmdParams($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasCmdParams() => $_has(11);
  @$pb.TagNumber(12)
  void clearCmdParams() => clearField(12);

  /// @table jsonb not null default '{}'::jsonb
  /// 发送时间列表
  @$pb.TagNumber(13)
  $core.String get sendTimeList => $_getSZ(12);
  @$pb.TagNumber(13)
  set sendTimeList($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasSendTimeList() => $_has(12);
  @$pb.TagNumber(13)
  void clearSendTimeList() => clearField(13);
}

/// 对讲机注册信息
class db_device_register_info extends $pb.GeneratedMessage {
  factory db_device_register_info({
    $core.String? rid,
    $core.String? receiveTime,
    $core.String? conCh,
    $core.String? dmrId,
    $core.String? sellerId,
    $core.String? sn,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (receiveTime != null) {
      $result.receiveTime = receiveTime;
    }
    if (conCh != null) {
      $result.conCh = conCh;
    }
    if (dmrId != null) {
      $result.dmrId = dmrId;
    }
    if (sellerId != null) {
      $result.sellerId = sellerId;
    }
    if (sn != null) {
      $result.sn = sn;
    }
    return $result;
  }
  db_device_register_info._() : super();
  factory db_device_register_info.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_register_info.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_register_info', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(3, _omitFieldNames ? '' : 'receiveTime')
    ..aOS(4, _omitFieldNames ? '' : 'conCh')
    ..aOS(5, _omitFieldNames ? '' : 'dmrId')
    ..aOS(6, _omitFieldNames ? '' : 'sellerId')
    ..aOS(7, _omitFieldNames ? '' : 'sn')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_register_info clone() => db_device_register_info()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_register_info copyWith(void Function(db_device_register_info) updates) => super.copyWith((message) => updates(message as db_device_register_info)) as db_device_register_info;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_register_info create() => db_device_register_info._();
  db_device_register_info createEmptyInstance() => create();
  static $pb.PbList<db_device_register_info> createRepeated() => $pb.PbList<db_device_register_info>();
  @$core.pragma('dart2js:noInline')
  static db_device_register_info getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_register_info>(create);
  static db_device_register_info? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp
  /// 命令接收的时间
  @$pb.TagNumber(3)
  $core.String get receiveTime => $_getSZ(1);
  @$pb.TagNumber(3)
  set receiveTime($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(3)
  $core.bool hasReceiveTime() => $_has(1);
  @$pb.TagNumber(3)
  void clearReceiveTime() => clearField(3);

  /// @table text
  /// 接收控制器id
  @$pb.TagNumber(4)
  $core.String get conCh => $_getSZ(2);
  @$pb.TagNumber(4)
  set conCh($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasConCh() => $_has(2);
  @$pb.TagNumber(4)
  void clearConCh() => clearField(4);

  /// @table text
  /// 设备id
  @$pb.TagNumber(5)
  $core.String get dmrId => $_getSZ(3);
  @$pb.TagNumber(5)
  set dmrId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasDmrId() => $_has(3);
  @$pb.TagNumber(5)
  void clearDmrId() => clearField(5);

  /// @table text
  /// 经销商id
  @$pb.TagNumber(6)
  $core.String get sellerId => $_getSZ(4);
  @$pb.TagNumber(6)
  set sellerId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasSellerId() => $_has(4);
  @$pb.TagNumber(6)
  void clearSellerId() => clearField(6);

  /// @table text
  /// 注册码
  @$pb.TagNumber(7)
  $core.String get sn => $_getSZ(5);
  @$pb.TagNumber(7)
  set sn($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(7)
  $core.bool hasSn() => $_has(5);
  @$pb.TagNumber(7)
  void clearSn() => clearField(7);
}

/// 通话调度/切换信道历史表,按月分表
class db_call_dispatch_history extends $pb.GeneratedMessage {
  factory db_call_dispatch_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? actionTime,
    $core.String? personId,
    $core.String? deviceId,
    $core.String? controllerDmrid,
    $core.String? dispatchTargetDmrid,
    $core.int? dispatchCode,
    $core.int? dispatchType,
    $core.int? targetChannel,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (actionTime != null) {
      $result.actionTime = actionTime;
    }
    if (personId != null) {
      $result.personId = personId;
    }
    if (deviceId != null) {
      $result.deviceId = deviceId;
    }
    if (controllerDmrid != null) {
      $result.controllerDmrid = controllerDmrid;
    }
    if (dispatchTargetDmrid != null) {
      $result.dispatchTargetDmrid = dispatchTargetDmrid;
    }
    if (dispatchCode != null) {
      $result.dispatchCode = dispatchCode;
    }
    if (dispatchType != null) {
      $result.dispatchType = dispatchType;
    }
    if (targetChannel != null) {
      $result.targetChannel = targetChannel;
    }
    return $result;
  }
  db_call_dispatch_history._() : super();
  factory db_call_dispatch_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_call_dispatch_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_call_dispatch_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'actionTime')
    ..aOS(4, _omitFieldNames ? '' : 'personId')
    ..aOS(6, _omitFieldNames ? '' : 'deviceId')
    ..aOS(7, _omitFieldNames ? '' : 'controllerDmrid')
    ..aOS(8, _omitFieldNames ? '' : 'dispatchTargetDmrid')
    ..a<$core.int>(10, _omitFieldNames ? '' : 'dispatchCode', $pb.PbFieldType.O3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'dispatchType', $pb.PbFieldType.O3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'targetChannel', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_call_dispatch_history clone() => db_call_dispatch_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_call_dispatch_history copyWith(void Function(db_call_dispatch_history) updates) => super.copyWith((message) => updates(message as db_call_dispatch_history)) as db_call_dispatch_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_call_dispatch_history create() => db_call_dispatch_history._();
  db_call_dispatch_history createEmptyInstance() => create();
  static $pb.PbList<db_call_dispatch_history> createRepeated() => $pb.PbList<db_call_dispatch_history>();
  @$core.pragma('dart2js:noInline')
  static db_call_dispatch_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_call_dispatch_history>(create);
  static db_call_dispatch_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 发起对讲机/用户所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 调度时间
  @$pb.TagNumber(3)
  $core.String get actionTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set actionTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasActionTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearActionTime() => clearField(3);

  /// @table uuid
  /// 调度人员rid,如果是中心软件发起,则此为发起用户的rid
  @$pb.TagNumber(4)
  $core.String get personId => $_getSZ(3);
  @$pb.TagNumber(4)
  set personId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPersonId() => $_has(3);
  @$pb.TagNumber(4)
  void clearPersonId() => clearField(4);

  /// @table varchar(16)
  /// 发起调度的对讲机dmrid,如果是中心软件发起的则为00000000
  @$pb.TagNumber(6)
  $core.String get deviceId => $_getSZ(4);
  @$pb.TagNumber(6)
  set deviceId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasDeviceId() => $_has(4);
  @$pb.TagNumber(6)
  void clearDeviceId() => clearField(6);

  /// @table varchar(16)
  /// 接收调度命令的控制器DMR-ID,
  @$pb.TagNumber(7)
  $core.String get controllerDmrid => $_getSZ(5);
  @$pb.TagNumber(7)
  set controllerDmrid($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(7)
  $core.bool hasControllerDmrid() => $_has(5);
  @$pb.TagNumber(7)
  void clearControllerDmrid() => clearField(7);

  /// @table text
  /// 调度命令中的目标dmrid列表,逗号分隔,级别调度时为一个,如果是信道切换则可能有多个
  @$pb.TagNumber(8)
  $core.String get dispatchTargetDmrid => $_getSZ(6);
  @$pb.TagNumber(8)
  set dispatchTargetDmrid($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(8)
  $core.bool hasDispatchTargetDmrid() => $_has(6);
  @$pb.TagNumber(8)
  void clearDispatchTargetDmrid() => clearField(8);

  /// @table int
  /// 调度类型bc11: 01=开启紧急信道调度	02=开启紧急基站调度	00=取消紧急信道调度,  cb21: 111=切换信道 110=取消切换信道
  @$pb.TagNumber(10)
  $core.int get dispatchCode => $_getIZ(7);
  @$pb.TagNumber(10)
  set dispatchCode($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(10)
  $core.bool hasDispatchCode() => $_has(7);
  @$pb.TagNumber(10)
  void clearDispatchCode() => clearField(10);

  /// @table int
  /// 调度类型,参考bc11 C_TP
  @$pb.TagNumber(11)
  $core.int get dispatchType => $_getIZ(8);
  @$pb.TagNumber(11)
  set dispatchType($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(11)
  $core.bool hasDispatchType() => $_has(8);
  @$pb.TagNumber(11)
  void clearDispatchType() => clearField(11);

  /// @table int
  /// 目标信道
  @$pb.TagNumber(12)
  $core.int get targetChannel => $_getIZ(9);
  @$pb.TagNumber(12)
  set targetChannel($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(12)
  $core.bool hasTargetChannel() => $_has(9);
  @$pb.TagNumber(12)
  void clearTargetChannel() => clearField(12);
}

/// 基站调度历史
class db_conf_dispatch_history extends $pb.GeneratedMessage {
  factory db_conf_dispatch_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? startTime,
    $core.String? startPersonRid,
    $core.String? controllerIds,
    $core.String? conferenceNo,
    $core.String? endOrgId,
    $core.String? endTime,
    $core.String? endPersonRid,
    $core.int? dispatchCode,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (startTime != null) {
      $result.startTime = startTime;
    }
    if (startPersonRid != null) {
      $result.startPersonRid = startPersonRid;
    }
    if (controllerIds != null) {
      $result.controllerIds = controllerIds;
    }
    if (conferenceNo != null) {
      $result.conferenceNo = conferenceNo;
    }
    if (endOrgId != null) {
      $result.endOrgId = endOrgId;
    }
    if (endTime != null) {
      $result.endTime = endTime;
    }
    if (endPersonRid != null) {
      $result.endPersonRid = endPersonRid;
    }
    if (dispatchCode != null) {
      $result.dispatchCode = dispatchCode;
    }
    return $result;
  }
  db_conf_dispatch_history._() : super();
  factory db_conf_dispatch_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_conf_dispatch_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_conf_dispatch_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'startTime')
    ..aOS(5, _omitFieldNames ? '' : 'startPersonRid')
    ..aOS(6, _omitFieldNames ? '' : 'controllerIds')
    ..aOS(7, _omitFieldNames ? '' : 'conferenceNo')
    ..aOS(8, _omitFieldNames ? '' : 'endOrgId')
    ..aOS(9, _omitFieldNames ? '' : 'endTime')
    ..aOS(10, _omitFieldNames ? '' : 'endPersonRid')
    ..a<$core.int>(11, _omitFieldNames ? '' : 'dispatchCode', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_conf_dispatch_history clone() => db_conf_dispatch_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_conf_dispatch_history copyWith(void Function(db_conf_dispatch_history) updates) => super.copyWith((message) => updates(message as db_conf_dispatch_history)) as db_conf_dispatch_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_conf_dispatch_history create() => db_conf_dispatch_history._();
  db_conf_dispatch_history createEmptyInstance() => create();
  static $pb.PbList<db_conf_dispatch_history> createRepeated() => $pb.PbList<db_conf_dispatch_history>();
  @$core.pragma('dart2js:noInline')
  static db_conf_dispatch_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_conf_dispatch_history>(create);
  static db_conf_dispatch_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 发起对讲机/用户所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 调度开始时间
  @$pb.TagNumber(3)
  $core.String get startTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set startTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasStartTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearStartTime() => clearField(3);

  /// @table uuid
  /// 调度人员rid,如果是中心后台发起的则为'00000000-0000-0000-0000-000000000000'
  @$pb.TagNumber(5)
  $core.String get startPersonRid => $_getSZ(3);
  @$pb.TagNumber(5)
  set startPersonRid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasStartPersonRid() => $_has(3);
  @$pb.TagNumber(5)
  void clearStartPersonRid() => clearField(5);

  /// @table text
  /// 被调度的控制器dmrid,逗号分隔
  @$pb.TagNumber(6)
  $core.String get controllerIds => $_getSZ(4);
  @$pb.TagNumber(6)
  set controllerIds($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasControllerIds() => $_has(4);
  @$pb.TagNumber(6)
  void clearControllerIds() => clearField(6);

  /// @table text
  /// 调度的会议室号
  @$pb.TagNumber(7)
  $core.String get conferenceNo => $_getSZ(5);
  @$pb.TagNumber(7)
  set conferenceNo($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(7)
  $core.bool hasConferenceNo() => $_has(5);
  @$pb.TagNumber(7)
  void clearConferenceNo() => clearField(7);

  /// @table uuid
  /// 结束会议对讲机/用户所属的群组
  @$pb.TagNumber(8)
  $core.String get endOrgId => $_getSZ(6);
  @$pb.TagNumber(8)
  set endOrgId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(8)
  $core.bool hasEndOrgId() => $_has(6);
  @$pb.TagNumber(8)
  void clearEndOrgId() => clearField(8);

  /// @table timestamp
  /// 调度结束时间
  @$pb.TagNumber(9)
  $core.String get endTime => $_getSZ(7);
  @$pb.TagNumber(9)
  set endTime($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(9)
  $core.bool hasEndTime() => $_has(7);
  @$pb.TagNumber(9)
  void clearEndTime() => clearField(9);

  /// @table uuid
  /// 结束调度人员rid,如果是中心后台发起的则为'00000000-0000-0000-0000-000000000000'
  @$pb.TagNumber(10)
  $core.String get endPersonRid => $_getSZ(8);
  @$pb.TagNumber(10)
  set endPersonRid($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(10)
  $core.bool hasEndPersonRid() => $_has(8);
  @$pb.TagNumber(10)
  void clearEndPersonRid() => clearField(10);

  /// @table int
  /// 调度类型
  /// 1:基站互联，全部通话互通
  /// 0：仅仅信道互联，通话目标使用发话对讲机的目标id，类似信道补点
  @$pb.TagNumber(11)
  $core.int get dispatchCode => $_getIZ(9);
  @$pb.TagNumber(11)
  set dispatchCode($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(11)
  $core.bool hasDispatchCode() => $_has(9);
  @$pb.TagNumber(11)
  void clearDispatchCode() => clearField(11);
}

/// 未确认短信表
class db_not_confirm_sms extends $pb.GeneratedMessage {
  factory db_not_confirm_sms({
    $core.String? rid,
    $core.String? orgId,
    $core.String? startTime,
    $core.String? senderDmrid,
    $core.String? targetDmrid,
    $core.String? receiveRepeater,
    $core.String? smsContent,
    $core.String? smsNo,
    $core.String? senderUserRid,
    $core.String? note,
    $core.String? smsType,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (startTime != null) {
      $result.startTime = startTime;
    }
    if (senderDmrid != null) {
      $result.senderDmrid = senderDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (receiveRepeater != null) {
      $result.receiveRepeater = receiveRepeater;
    }
    if (smsContent != null) {
      $result.smsContent = smsContent;
    }
    if (smsNo != null) {
      $result.smsNo = smsNo;
    }
    if (senderUserRid != null) {
      $result.senderUserRid = senderUserRid;
    }
    if (note != null) {
      $result.note = note;
    }
    if (smsType != null) {
      $result.smsType = smsType;
    }
    return $result;
  }
  db_not_confirm_sms._() : super();
  factory db_not_confirm_sms.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_not_confirm_sms.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_not_confirm_sms', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'startTime')
    ..aOS(4, _omitFieldNames ? '' : 'senderDmrid')
    ..aOS(5, _omitFieldNames ? '' : 'targetDmrid')
    ..aOS(6, _omitFieldNames ? '' : 'receiveRepeater')
    ..aOS(7, _omitFieldNames ? '' : 'smsContent')
    ..aOS(8, _omitFieldNames ? '' : 'smsNo')
    ..aOS(9, _omitFieldNames ? '' : 'senderUserRid')
    ..aOS(10, _omitFieldNames ? '' : 'note')
    ..aOS(11, _omitFieldNames ? '' : 'smsType')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_not_confirm_sms clone() => db_not_confirm_sms()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_not_confirm_sms copyWith(void Function(db_not_confirm_sms) updates) => super.copyWith((message) => updates(message as db_not_confirm_sms)) as db_not_confirm_sms;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_not_confirm_sms create() => db_not_confirm_sms._();
  db_not_confirm_sms createEmptyInstance() => create();
  static $pb.PbList<db_not_confirm_sms> createRepeated() => $pb.PbList<db_not_confirm_sms>();
  @$core.pragma('dart2js:noInline')
  static db_not_confirm_sms getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_not_confirm_sms>(create);
  static db_not_confirm_sms? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 发起对讲机/用户所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 短信发起时间
  @$pb.TagNumber(3)
  $core.String get startTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set startTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasStartTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearStartTime() => clearField(3);

  /// @table text
  /// 发起者dmrid
  @$pb.TagNumber(4)
  $core.String get senderDmrid => $_getSZ(3);
  @$pb.TagNumber(4)
  set senderDmrid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSenderDmrid() => $_has(3);
  @$pb.TagNumber(4)
  void clearSenderDmrid() => clearField(4);

  /// @table text
  /// 接收方dmrid
  @$pb.TagNumber(5)
  $core.String get targetDmrid => $_getSZ(4);
  @$pb.TagNumber(5)
  set targetDmrid($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasTargetDmrid() => $_has(4);
  @$pb.TagNumber(5)
  void clearTargetDmrid() => clearField(5);

  /// @table text
  /// 接收中继
  @$pb.TagNumber(6)
  $core.String get receiveRepeater => $_getSZ(5);
  @$pb.TagNumber(6)
  set receiveRepeater($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasReceiveRepeater() => $_has(5);
  @$pb.TagNumber(6)
  void clearReceiveRepeater() => clearField(6);

  /// @table text
  /// 短信内容
  @$pb.TagNumber(7)
  $core.String get smsContent => $_getSZ(6);
  @$pb.TagNumber(7)
  set smsContent($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSmsContent() => $_has(6);
  @$pb.TagNumber(7)
  void clearSmsContent() => clearField(7);

  /// @table text
  /// 短信序号
  @$pb.TagNumber(8)
  $core.String get smsNo => $_getSZ(7);
  @$pb.TagNumber(8)
  set smsNo($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasSmsNo() => $_has(7);
  @$pb.TagNumber(8)
  void clearSmsNo() => clearField(8);

  /// @table text
  /// 发送设备的用户rid
  @$pb.TagNumber(9)
  $core.String get senderUserRid => $_getSZ(8);
  @$pb.TagNumber(9)
  set senderUserRid($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasSenderUserRid() => $_has(8);
  @$pb.TagNumber(9)
  void clearSenderUserRid() => clearField(9);

  /// @table text
  /// 一些其它记录,如多次发送记录
  @$pb.TagNumber(10)
  $core.String get note => $_getSZ(9);
  @$pb.TagNumber(10)
  set note($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasNote() => $_has(9);
  @$pb.TagNumber(10)
  void clearNote() => clearField(10);

  /// @table text default '02'
  /// 短信类型 02:普通短信 12:重要短信
  @$pb.TagNumber(11)
  $core.String get smsType => $_getSZ(10);
  @$pb.TagNumber(11)
  set smsType($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasSmsType() => $_has(10);
  @$pb.TagNumber(11)
  void clearSmsType() => clearField(11);
}

/// 短信历史表,短信一般很少,不分表处理了
class db_sms_history extends $pb.GeneratedMessage {
  factory db_sms_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? startTime,
    $core.String? senderDmrid,
    $core.String? targetDmrid,
    $core.String? receiveRepeater,
    $core.String? smsContent,
    $core.String? smsNo,
    $core.String? confirmTime,
    $core.String? note,
    $core.String? senderUserRid,
    $core.String? smsType,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (startTime != null) {
      $result.startTime = startTime;
    }
    if (senderDmrid != null) {
      $result.senderDmrid = senderDmrid;
    }
    if (targetDmrid != null) {
      $result.targetDmrid = targetDmrid;
    }
    if (receiveRepeater != null) {
      $result.receiveRepeater = receiveRepeater;
    }
    if (smsContent != null) {
      $result.smsContent = smsContent;
    }
    if (smsNo != null) {
      $result.smsNo = smsNo;
    }
    if (confirmTime != null) {
      $result.confirmTime = confirmTime;
    }
    if (note != null) {
      $result.note = note;
    }
    if (senderUserRid != null) {
      $result.senderUserRid = senderUserRid;
    }
    if (smsType != null) {
      $result.smsType = smsType;
    }
    return $result;
  }
  db_sms_history._() : super();
  factory db_sms_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_sms_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_sms_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'startTime')
    ..aOS(4, _omitFieldNames ? '' : 'senderDmrid')
    ..aOS(5, _omitFieldNames ? '' : 'targetDmrid')
    ..aOS(6, _omitFieldNames ? '' : 'receiveRepeater')
    ..aOS(7, _omitFieldNames ? '' : 'smsContent')
    ..aOS(8, _omitFieldNames ? '' : 'smsNo')
    ..aOS(9, _omitFieldNames ? '' : 'confirmTime')
    ..aOS(10, _omitFieldNames ? '' : 'note')
    ..aOS(11, _omitFieldNames ? '' : 'senderUserRid')
    ..aOS(12, _omitFieldNames ? '' : 'smsType')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_sms_history clone() => db_sms_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_sms_history copyWith(void Function(db_sms_history) updates) => super.copyWith((message) => updates(message as db_sms_history)) as db_sms_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_sms_history create() => db_sms_history._();
  db_sms_history createEmptyInstance() => create();
  static $pb.PbList<db_sms_history> createRepeated() => $pb.PbList<db_sms_history>();
  @$core.pragma('dart2js:noInline')
  static db_sms_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_sms_history>(create);
  static db_sms_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 发起对讲机/用户所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 短信发起时间
  @$pb.TagNumber(3)
  $core.String get startTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set startTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasStartTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearStartTime() => clearField(3);

  /// @table text
  /// 发起者dmrid
  @$pb.TagNumber(4)
  $core.String get senderDmrid => $_getSZ(3);
  @$pb.TagNumber(4)
  set senderDmrid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasSenderDmrid() => $_has(3);
  @$pb.TagNumber(4)
  void clearSenderDmrid() => clearField(4);

  /// @table text
  /// 接收方dmrid
  @$pb.TagNumber(5)
  $core.String get targetDmrid => $_getSZ(4);
  @$pb.TagNumber(5)
  set targetDmrid($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasTargetDmrid() => $_has(4);
  @$pb.TagNumber(5)
  void clearTargetDmrid() => clearField(5);

  /// @table text
  /// 接收中继
  @$pb.TagNumber(6)
  $core.String get receiveRepeater => $_getSZ(5);
  @$pb.TagNumber(6)
  set receiveRepeater($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasReceiveRepeater() => $_has(5);
  @$pb.TagNumber(6)
  void clearReceiveRepeater() => clearField(6);

  /// @table text
  /// 短信内容
  @$pb.TagNumber(7)
  $core.String get smsContent => $_getSZ(6);
  @$pb.TagNumber(7)
  set smsContent($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasSmsContent() => $_has(6);
  @$pb.TagNumber(7)
  void clearSmsContent() => clearField(7);

  /// @table text
  /// 短信序号
  @$pb.TagNumber(8)
  $core.String get smsNo => $_getSZ(7);
  @$pb.TagNumber(8)
  set smsNo($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasSmsNo() => $_has(7);
  @$pb.TagNumber(8)
  void clearSmsNo() => clearField(8);

  /// @table timestamp
  /// 如果是单呼短信,这个是接收方确认时间
  @$pb.TagNumber(9)
  $core.String get confirmTime => $_getSZ(8);
  @$pb.TagNumber(9)
  set confirmTime($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasConfirmTime() => $_has(8);
  @$pb.TagNumber(9)
  void clearConfirmTime() => clearField(9);

  /// @table text
  /// 一些其它记录,如多次发送记录
  @$pb.TagNumber(10)
  $core.String get note => $_getSZ(9);
  @$pb.TagNumber(10)
  set note($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasNote() => $_has(9);
  @$pb.TagNumber(10)
  void clearNote() => clearField(10);

  /// @table text
  /// 发送设备的用户rid
  @$pb.TagNumber(11)
  $core.String get senderUserRid => $_getSZ(10);
  @$pb.TagNumber(11)
  set senderUserRid($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasSenderUserRid() => $_has(10);
  @$pb.TagNumber(11)
  void clearSenderUserRid() => clearField(11);

  /// @table text
  /// 短信类型 02:普通短信 12:重要短信
  @$pb.TagNumber(12)
  $core.String get smsType => $_getSZ(11);
  @$pb.TagNumber(12)
  set smsType($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasSmsType() => $_has(11);
  @$pb.TagNumber(12)
  void clearSmsType() => clearField(12);
}

/// 频道物理数据
/// todo 此处信息还需要商议下才能确定
class db_ch_rf_setting extends $pb.GeneratedMessage {
  factory db_ch_rf_setting({
    $core.String? rid,
    $core.String? orgId,
    $core.String? name,
    $core.String? rfSetting,
    $core.String? settings,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (name != null) {
      $result.name = name;
    }
    if (rfSetting != null) {
      $result.rfSetting = rfSetting;
    }
    if (settings != null) {
      $result.settings = settings;
    }
    return $result;
  }
  db_ch_rf_setting._() : super();
  factory db_ch_rf_setting.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_ch_rf_setting.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_ch_rf_setting', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'name')
    ..aOS(4, _omitFieldNames ? '' : 'rfSetting')
    ..aOS(14, _omitFieldNames ? '' : 'settings')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_ch_rf_setting clone() => db_ch_rf_setting()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_ch_rf_setting copyWith(void Function(db_ch_rf_setting) updates) => super.copyWith((message) => updates(message as db_ch_rf_setting)) as db_ch_rf_setting;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_ch_rf_setting create() => db_ch_rf_setting._();
  db_ch_rf_setting createEmptyInstance() => create();
  static $pb.PbList<db_ch_rf_setting> createRepeated() => $pb.PbList<db_ch_rf_setting>();
  @$core.pragma('dart2js:noInline')
  static db_ch_rf_setting getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_ch_rf_setting>(create);
  static db_ch_rf_setting? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table text  not null
  /// 名称
  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  /// @table text
  /// 配置参数,一些公式的可以提取出来,或者json放settings里面
  @$pb.TagNumber(4)
  $core.String get rfSetting => $_getSZ(3);
  @$pb.TagNumber(4)
  set rfSetting($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasRfSetting() => $_has(3);
  @$pb.TagNumber(4)
  void clearRfSetting() => clearField(4);

  /// @table jsonb default '{}'::jsonb
  /// 辅助设置数据,Json格式
  @$pb.TagNumber(14)
  $core.String get settings => $_getSZ(4);
  @$pb.TagNumber(14)
  set settings($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(14)
  $core.bool hasSettings() => $_has(4);
  @$pb.TagNumber(14)
  void clearSettings() => clearField(14);
}

/// 写频配置文件
class db_device_setting_conf extends $pb.GeneratedMessage {
  factory db_device_setting_conf({
    $core.String? rid,
    $core.String? orgId,
    $core.String? confName,
    $core.String? lastModifyTime,
    $core.String? userName,
    $core.String? conf,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (confName != null) {
      $result.confName = confName;
    }
    if (lastModifyTime != null) {
      $result.lastModifyTime = lastModifyTime;
    }
    if (userName != null) {
      $result.userName = userName;
    }
    if (conf != null) {
      $result.conf = conf;
    }
    return $result;
  }
  db_device_setting_conf._() : super();
  factory db_device_setting_conf.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_setting_conf.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_setting_conf', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'confName')
    ..aOS(4, _omitFieldNames ? '' : 'lastModifyTime')
    ..aOS(5, _omitFieldNames ? '' : 'userName')
    ..aOS(14, _omitFieldNames ? '' : 'conf')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_setting_conf clone() => db_device_setting_conf()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_setting_conf copyWith(void Function(db_device_setting_conf) updates) => super.copyWith((message) => updates(message as db_device_setting_conf)) as db_device_setting_conf;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_setting_conf create() => db_device_setting_conf._();
  db_device_setting_conf createEmptyInstance() => create();
  static $pb.PbList<db_device_setting_conf> createRepeated() => $pb.PbList<db_device_setting_conf>();
  @$core.pragma('dart2js:noInline')
  static db_device_setting_conf getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_setting_conf>(create);
  static db_device_setting_conf? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table text  not null
  /// 名称
  @$pb.TagNumber(3)
  $core.String get confName => $_getSZ(2);
  @$pb.TagNumber(3)
  set confName($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasConfName() => $_has(2);
  @$pb.TagNumber(3)
  void clearConfName() => clearField(3);

  /// @table timestamp
  /// 最后修改时间(保存时间)
  @$pb.TagNumber(4)
  $core.String get lastModifyTime => $_getSZ(3);
  @$pb.TagNumber(4)
  set lastModifyTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastModifyTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearLastModifyTime() => clearField(4);

  /// @table text
  /// 保存此配置的用户名
  @$pb.TagNumber(5)
  $core.String get userName => $_getSZ(4);
  @$pb.TagNumber(5)
  set userName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasUserName() => $_has(4);
  @$pb.TagNumber(5)
  void clearUserName() => clearField(5);

  /// @table jsonb default '[]'::jsonb
  /// 写频
  @$pb.TagNumber(14)
  $core.String get conf => $_getSZ(5);
  @$pb.TagNumber(14)
  set conf($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(14)
  $core.bool hasConf() => $_has(5);
  @$pb.TagNumber(14)
  void clearConf() => clearField(14);
}

/// 电话网关短号
class db_phone_short_no extends $pb.GeneratedMessage {
  factory db_phone_short_no({
    $core.String? rid,
    $core.String? orgId,
    $core.String? shortNo,
    $core.String? lastModifyTime,
    $core.String? refOrgId,
    $core.String? refDevId,
    $core.String? note,
    $core.String? setting,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (shortNo != null) {
      $result.shortNo = shortNo;
    }
    if (lastModifyTime != null) {
      $result.lastModifyTime = lastModifyTime;
    }
    if (refOrgId != null) {
      $result.refOrgId = refOrgId;
    }
    if (refDevId != null) {
      $result.refDevId = refDevId;
    }
    if (note != null) {
      $result.note = note;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    return $result;
  }
  db_phone_short_no._() : super();
  factory db_phone_short_no.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_phone_short_no.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_phone_short_no', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'shortNo')
    ..aOS(4, _omitFieldNames ? '' : 'lastModifyTime')
    ..aOS(5, _omitFieldNames ? '' : 'refOrgId')
    ..aOS(6, _omitFieldNames ? '' : 'refDevId')
    ..aOS(8, _omitFieldNames ? '' : 'note')
    ..aOS(14, _omitFieldNames ? '' : 'setting')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_phone_short_no clone() => db_phone_short_no()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_phone_short_no copyWith(void Function(db_phone_short_no) updates) => super.copyWith((message) => updates(message as db_phone_short_no)) as db_phone_short_no;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_phone_short_no create() => db_phone_short_no._();
  db_phone_short_no createEmptyInstance() => create();
  static $pb.PbList<db_phone_short_no> createRepeated() => $pb.PbList<db_phone_short_no>();
  @$core.pragma('dart2js:noInline')
  static db_phone_short_no getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_phone_short_no>(create);
  static db_phone_short_no? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table text  unique not null
  /// 名称
  @$pb.TagNumber(3)
  $core.String get shortNo => $_getSZ(2);
  @$pb.TagNumber(3)
  set shortNo($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasShortNo() => $_has(2);
  @$pb.TagNumber(3)
  void clearShortNo() => clearField(3);

  /// @table timestamp
  /// 最后修改时间(保存时间)
  @$pb.TagNumber(4)
  $core.String get lastModifyTime => $_getSZ(3);
  @$pb.TagNumber(4)
  set lastModifyTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastModifyTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearLastModifyTime() => clearField(4);

  /// @table uuid REFERENCES db_org(rid) ON DELETE set null
  /// 对应的群组rid
  @$pb.TagNumber(5)
  $core.String get refOrgId => $_getSZ(4);
  @$pb.TagNumber(5)
  set refOrgId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasRefOrgId() => $_has(4);
  @$pb.TagNumber(5)
  void clearRefOrgId() => clearField(5);

  /// @table uuid REFERENCES db_device(rid) ON DELETE set null
  /// 对应的设备rid
  @$pb.TagNumber(6)
  $core.String get refDevId => $_getSZ(5);
  @$pb.TagNumber(6)
  set refDevId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasRefDevId() => $_has(5);
  @$pb.TagNumber(6)
  void clearRefDevId() => clearField(6);

  /// @table text
  @$pb.TagNumber(8)
  $core.String get note => $_getSZ(6);
  @$pb.TagNumber(8)
  set note($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(8)
  $core.bool hasNote() => $_has(6);
  @$pb.TagNumber(8)
  void clearNote() => clearField(8);

  /// @table jsonb default '{}'::jsonb
  /// json设置
  @$pb.TagNumber(14)
  $core.String get setting => $_getSZ(7);
  @$pb.TagNumber(14)
  set setting($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(14)
  $core.bool hasSetting() => $_has(7);
  @$pb.TagNumber(14)
  void clearSetting() => clearField(14);
}

/// 电话网关使用授权
class db_phone_gateway_permission extends $pb.GeneratedMessage {
  factory db_phone_gateway_permission({
    $core.String? rid,
    $core.String? orgId,
    $core.String? name,
    $core.String? lastModifyTime,
    $core.String? permOrgId,
    $core.String? permDevId,
    $core.String? gatewayRid,
    $core.String? note,
    $core.String? setting,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (name != null) {
      $result.name = name;
    }
    if (lastModifyTime != null) {
      $result.lastModifyTime = lastModifyTime;
    }
    if (permOrgId != null) {
      $result.permOrgId = permOrgId;
    }
    if (permDevId != null) {
      $result.permDevId = permDevId;
    }
    if (gatewayRid != null) {
      $result.gatewayRid = gatewayRid;
    }
    if (note != null) {
      $result.note = note;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    return $result;
  }
  db_phone_gateway_permission._() : super();
  factory db_phone_gateway_permission.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_phone_gateway_permission.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_phone_gateway_permission', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'name')
    ..aOS(4, _omitFieldNames ? '' : 'lastModifyTime')
    ..aOS(5, _omitFieldNames ? '' : 'permOrgId')
    ..aOS(6, _omitFieldNames ? '' : 'permDevId')
    ..aOS(7, _omitFieldNames ? '' : 'gatewayRid')
    ..aOS(8, _omitFieldNames ? '' : 'note')
    ..aOS(14, _omitFieldNames ? '' : 'setting')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_phone_gateway_permission clone() => db_phone_gateway_permission()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_phone_gateway_permission copyWith(void Function(db_phone_gateway_permission) updates) => super.copyWith((message) => updates(message as db_phone_gateway_permission)) as db_phone_gateway_permission;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_phone_gateway_permission create() => db_phone_gateway_permission._();
  db_phone_gateway_permission createEmptyInstance() => create();
  static $pb.PbList<db_phone_gateway_permission> createRepeated() => $pb.PbList<db_phone_gateway_permission>();
  @$core.pragma('dart2js:noInline')
  static db_phone_gateway_permission getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_phone_gateway_permission>(create);
  static db_phone_gateway_permission? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table text
  /// 名称
  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  /// @table timestamp
  /// 最后修改时间(保存时间)
  @$pb.TagNumber(4)
  $core.String get lastModifyTime => $_getSZ(3);
  @$pb.TagNumber(4)
  set lastModifyTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastModifyTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearLastModifyTime() => clearField(4);

  /// @table uuid REFERENCES db_org(rid) ON DELETE CASCADE
  /// 授权可以使用的群组rid
  @$pb.TagNumber(5)
  $core.String get permOrgId => $_getSZ(4);
  @$pb.TagNumber(5)
  set permOrgId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPermOrgId() => $_has(4);
  @$pb.TagNumber(5)
  void clearPermOrgId() => clearField(5);

  /// @table uuid REFERENCES db_device(rid) ON DELETE CASCADE
  /// 授权可以使用的设备rid
  @$pb.TagNumber(6)
  $core.String get permDevId => $_getSZ(5);
  @$pb.TagNumber(6)
  set permDevId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasPermDevId() => $_has(5);
  @$pb.TagNumber(6)
  void clearPermDevId() => clearField(6);

  /// @table uuid REFERENCES db_device(rid) ON DELETE CASCADE
  /// 网关rid
  @$pb.TagNumber(7)
  $core.String get gatewayRid => $_getSZ(6);
  @$pb.TagNumber(7)
  set gatewayRid($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasGatewayRid() => $_has(6);
  @$pb.TagNumber(7)
  void clearGatewayRid() => clearField(7);

  /// @table text
  @$pb.TagNumber(8)
  $core.String get note => $_getSZ(7);
  @$pb.TagNumber(8)
  set note($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasNote() => $_has(7);
  @$pb.TagNumber(8)
  void clearNote() => clearField(8);

  /// @table jsonb default '{}'::jsonb
  ///  json设置
  @$pb.TagNumber(14)
  $core.String get setting => $_getSZ(8);
  @$pb.TagNumber(14)
  set setting($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(14)
  $core.bool hasSetting() => $_has(8);
  @$pb.TagNumber(14)
  void clearSetting() => clearField(14);
}

/// 电话网关设备关系管理
class db_controller_gateway_manage extends $pb.GeneratedMessage {
  factory db_controller_gateway_manage({
    $core.String? rid,
    $core.String? orgId,
    $core.String? refControllerId,
    $core.int? phonePos,
    $core.String? phoneNo,
    $core.String? refDevId,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (refControllerId != null) {
      $result.refControllerId = refControllerId;
    }
    if (phonePos != null) {
      $result.phonePos = phonePos;
    }
    if (phoneNo != null) {
      $result.phoneNo = phoneNo;
    }
    if (refDevId != null) {
      $result.refDevId = refDevId;
    }
    return $result;
  }
  db_controller_gateway_manage._() : super();
  factory db_controller_gateway_manage.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_controller_gateway_manage.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_controller_gateway_manage', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'refControllerId')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'phonePos', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'phoneNo')
    ..aOS(6, _omitFieldNames ? '' : 'refDevId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_controller_gateway_manage clone() => db_controller_gateway_manage()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_controller_gateway_manage copyWith(void Function(db_controller_gateway_manage) updates) => super.copyWith((message) => updates(message as db_controller_gateway_manage)) as db_controller_gateway_manage;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_controller_gateway_manage create() => db_controller_gateway_manage._();
  db_controller_gateway_manage createEmptyInstance() => create();
  static $pb.PbList<db_controller_gateway_manage> createRepeated() => $pb.PbList<db_controller_gateway_manage>();
  @$core.pragma('dart2js:noInline')
  static db_controller_gateway_manage getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_controller_gateway_manage>(create);
  static db_controller_gateway_manage? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table uuid REFERENCES db_controller(rid) ON DELETE CASCADE
  /// 电话网关控制器 rid
  @$pb.TagNumber(3)
  $core.String get refControllerId => $_getSZ(2);
  @$pb.TagNumber(3)
  set refControllerId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRefControllerId() => $_has(2);
  @$pb.TagNumber(3)
  void clearRefControllerId() => clearField(3);

  /// @table int
  /// 控制器板上电话接口位置,1,2,3
  @$pb.TagNumber(4)
  $core.int get phonePos => $_getIZ(3);
  @$pb.TagNumber(4)
  set phonePos($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPhonePos() => $_has(3);
  @$pb.TagNumber(4)
  void clearPhonePos() => clearField(4);

  /// @table text
  /// 电话号码
  @$pb.TagNumber(5)
  $core.String get phoneNo => $_getSZ(4);
  @$pb.TagNumber(5)
  set phoneNo($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasPhoneNo() => $_has(4);
  @$pb.TagNumber(5)
  void clearPhoneNo() => clearField(5);

  /// @table uuid unique REFERENCES db_device(rid) ON DELETE CASCADE
  /// 电话网关dev rid
  @$pb.TagNumber(6)
  $core.String get refDevId => $_getSZ(5);
  @$pb.TagNumber(6)
  set refDevId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasRefDevId() => $_has(5);
  @$pb.TagNumber(6)
  void clearRefDevId() => clearField(6);
}

/// 预定义电话号码本
class db_phone_no_list extends $pb.GeneratedMessage {
  factory db_phone_no_list({
    $core.String? rid,
    $core.String? orgId,
    $core.String? lastModifyTime,
    $core.String? phoneName,
    $core.String? phoneNo,
    $core.String? setting,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (lastModifyTime != null) {
      $result.lastModifyTime = lastModifyTime;
    }
    if (phoneName != null) {
      $result.phoneName = phoneName;
    }
    if (phoneNo != null) {
      $result.phoneNo = phoneNo;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    return $result;
  }
  db_phone_no_list._() : super();
  factory db_phone_no_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_phone_no_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_phone_no_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(4, _omitFieldNames ? '' : 'lastModifyTime')
    ..aOS(7, _omitFieldNames ? '' : 'phoneName')
    ..aOS(8, _omitFieldNames ? '' : 'phoneNo')
    ..aOS(14, _omitFieldNames ? '' : 'setting')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_phone_no_list clone() => db_phone_no_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_phone_no_list copyWith(void Function(db_phone_no_list) updates) => super.copyWith((message) => updates(message as db_phone_no_list)) as db_phone_no_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_phone_no_list create() => db_phone_no_list._();
  db_phone_no_list createEmptyInstance() => create();
  static $pb.PbList<db_phone_no_list> createRepeated() => $pb.PbList<db_phone_no_list>();
  @$core.pragma('dart2js:noInline')
  static db_phone_no_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_phone_no_list>(create);
  static db_phone_no_list? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 最后修改时间(保存时间)
  @$pb.TagNumber(4)
  $core.String get lastModifyTime => $_getSZ(2);
  @$pb.TagNumber(4)
  set lastModifyTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(4)
  $core.bool hasLastModifyTime() => $_has(2);
  @$pb.TagNumber(4)
  void clearLastModifyTime() => clearField(4);

  /// @table text  unique not null
  /// 名称
  @$pb.TagNumber(7)
  $core.String get phoneName => $_getSZ(3);
  @$pb.TagNumber(7)
  set phoneName($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(7)
  $core.bool hasPhoneName() => $_has(3);
  @$pb.TagNumber(7)
  void clearPhoneName() => clearField(7);

  /// @table text
  @$pb.TagNumber(8)
  $core.String get phoneNo => $_getSZ(4);
  @$pb.TagNumber(8)
  set phoneNo($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(8)
  $core.bool hasPhoneNo() => $_has(4);
  @$pb.TagNumber(8)
  void clearPhoneNo() => clearField(8);

  /// @table jsonb default '{}'::jsonb
  ///  json设置
  @$pb.TagNumber(14)
  $core.String get setting => $_getSZ(5);
  @$pb.TagNumber(14)
  set setting($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(14)
  $core.bool hasSetting() => $_has(5);
  @$pb.TagNumber(14)
  void clearSetting() => clearField(14);
}

/// 有源点报警历史
class db_linepoint_alarm_history extends $pb.GeneratedMessage {
  factory db_linepoint_alarm_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? checkTime,
    $core.String? checkerId,
    $core.String? deviceId,
    $core.String? receiveTime,
    $core.String? receiver,
    $core.String? pointId,
    $core.int? alarmCode,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (checkTime != null) {
      $result.checkTime = checkTime;
    }
    if (checkerId != null) {
      $result.checkerId = checkerId;
    }
    if (deviceId != null) {
      $result.deviceId = deviceId;
    }
    if (receiveTime != null) {
      $result.receiveTime = receiveTime;
    }
    if (receiver != null) {
      $result.receiver = receiver;
    }
    if (pointId != null) {
      $result.pointId = pointId;
    }
    if (alarmCode != null) {
      $result.alarmCode = alarmCode;
    }
    return $result;
  }
  db_linepoint_alarm_history._() : super();
  factory db_linepoint_alarm_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_linepoint_alarm_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_linepoint_alarm_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'checkTime')
    ..aOS(4, _omitFieldNames ? '' : 'checkerId')
    ..aOS(6, _omitFieldNames ? '' : 'deviceId')
    ..aOS(8, _omitFieldNames ? '' : 'receiveTime')
    ..aOS(9, _omitFieldNames ? '' : 'receiver')
    ..aOS(10, _omitFieldNames ? '' : 'pointId')
    ..a<$core.int>(11, _omitFieldNames ? '' : 'alarmCode', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_linepoint_alarm_history clone() => db_linepoint_alarm_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_linepoint_alarm_history copyWith(void Function(db_linepoint_alarm_history) updates) => super.copyWith((message) => updates(message as db_linepoint_alarm_history)) as db_linepoint_alarm_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_linepoint_alarm_history create() => db_linepoint_alarm_history._();
  db_linepoint_alarm_history createEmptyInstance() => create();
  static $pb.PbList<db_linepoint_alarm_history> createRepeated() => $pb.PbList<db_linepoint_alarm_history>();
  @$core.pragma('dart2js:noInline')
  static db_linepoint_alarm_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_linepoint_alarm_history>(create);
  static db_linepoint_alarm_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// 对讲机所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 巡查时间
  @$pb.TagNumber(3)
  $core.String get checkTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set checkTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCheckTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearCheckTime() => clearField(3);

  /// @table uuid
  /// 巡查人员rid
  @$pb.TagNumber(4)
  $core.String get checkerId => $_getSZ(3);
  @$pb.TagNumber(4)
  set checkerId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCheckerId() => $_has(3);
  @$pb.TagNumber(4)
  void clearCheckerId() => clearField(4);

  /// @table uuid
  /// 对讲机rid
  @$pb.TagNumber(6)
  $core.String get deviceId => $_getSZ(4);
  @$pb.TagNumber(6)
  set deviceId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasDeviceId() => $_has(4);
  @$pb.TagNumber(6)
  void clearDeviceId() => clearField(6);

  /// @table timestamp
  /// 接收时间,不一定是实时的
  @$pb.TagNumber(8)
  $core.String get receiveTime => $_getSZ(5);
  @$pb.TagNumber(8)
  set receiveTime($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(8)
  $core.bool hasReceiveTime() => $_has(5);
  @$pb.TagNumber(8)
  void clearReceiveTime() => clearField(8);

  /// @table varchar(16)
  /// 接收的控制器名称
  @$pb.TagNumber(9)
  $core.String get receiver => $_getSZ(6);
  @$pb.TagNumber(9)
  set receiver($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(9)
  $core.bool hasReceiver() => $_has(6);
  @$pb.TagNumber(9)
  void clearReceiver() => clearField(9);

  /// @table uuid REFERENCES db_line_point(rid) ON DELETE CASCADE
  /// 点的标识
  @$pb.TagNumber(10)
  $core.String get pointId => $_getSZ(7);
  @$pb.TagNumber(10)
  set pointId($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(10)
  $core.bool hasPointId() => $_has(7);
  @$pb.TagNumber(10)
  void clearPointId() => clearField(10);

  /// @table int
  /// 报警码,低压报警为4
  @$pb.TagNumber(11)
  $core.int get alarmCode => $_getIZ(8);
  @$pb.TagNumber(11)
  set alarmCode($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(11)
  $core.bool hasAlarmCode() => $_has(8);
  @$pb.TagNumber(11)
  void clearAlarmCode() => clearField(11);
}

class db_device_channel_zone extends $pb.GeneratedMessage {
  factory db_device_channel_zone({
    $core.String? rid,
    $core.int? zoneLevel,
    $core.int? zoneNo,
    $core.String? zoneTitle,
    $core.String? zoneParent,
    $core.String? setting,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (zoneLevel != null) {
      $result.zoneLevel = zoneLevel;
    }
    if (zoneNo != null) {
      $result.zoneNo = zoneNo;
    }
    if (zoneTitle != null) {
      $result.zoneTitle = zoneTitle;
    }
    if (zoneParent != null) {
      $result.zoneParent = zoneParent;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    return $result;
  }
  db_device_channel_zone._() : super();
  factory db_device_channel_zone.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_channel_zone.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_channel_zone', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'zoneLevel', $pb.PbFieldType.O3)
    ..a<$core.int>(3, _omitFieldNames ? '' : 'zoneNo', $pb.PbFieldType.O3)
    ..aOS(4, _omitFieldNames ? '' : 'zoneTitle')
    ..aOS(5, _omitFieldNames ? '' : 'zoneParent')
    ..aOS(6, _omitFieldNames ? '' : 'setting')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_channel_zone clone() => db_device_channel_zone()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_channel_zone copyWith(void Function(db_device_channel_zone) updates) => super.copyWith((message) => updates(message as db_device_channel_zone)) as db_device_channel_zone;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_channel_zone create() => db_device_channel_zone._();
  db_device_channel_zone createEmptyInstance() => create();
  static $pb.PbList<db_device_channel_zone> createRepeated() => $pb.PbList<db_device_channel_zone>();
  @$core.pragma('dart2js:noInline')
  static db_device_channel_zone getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_channel_zone>(create);
  static db_device_channel_zone? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table int default 0
  /// 区域所在层级,0,1,2,3,0为最顶级(root),用户看不见
  @$pb.TagNumber(2)
  $core.int get zoneLevel => $_getIZ(1);
  @$pb.TagNumber(2)
  set zoneLevel($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasZoneLevel() => $_has(1);
  @$pb.TagNumber(2)
  void clearZoneLevel() => clearField(2);

  /// @table int
  /// 区域在所在层级下的序号,一个父区域下可以有多个子区域,这些子区域必须按顺序编号,从0开始
  @$pb.TagNumber(3)
  $core.int get zoneNo => $_getIZ(2);
  @$pb.TagNumber(3)
  set zoneNo($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasZoneNo() => $_has(2);
  @$pb.TagNumber(3)
  void clearZoneNo() => clearField(3);

  /// @table text
  /// 区域名称
  @$pb.TagNumber(4)
  $core.String get zoneTitle => $_getSZ(3);
  @$pb.TagNumber(4)
  set zoneTitle($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasZoneTitle() => $_has(3);
  @$pb.TagNumber(4)
  void clearZoneTitle() => clearField(4);

  /// @table uuid REFERENCES db_device_channel_zone(rid) ON DELETE CASCADE
  /// 区域的上级,最顶级时为null
  @$pb.TagNumber(5)
  $core.String get zoneParent => $_getSZ(4);
  @$pb.TagNumber(5)
  set zoneParent($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasZoneParent() => $_has(4);
  @$pb.TagNumber(5)
  void clearZoneParent() => clearField(5);

  /// @table jsonb not null default '{}'::jsonb
  /// setting
  @$pb.TagNumber(6)
  $core.String get setting => $_getSZ(5);
  @$pb.TagNumber(6)
  set setting($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSetting() => $_has(5);
  @$pb.TagNumber(6)
  void clearSetting() => clearField(6);
}

/// 用户crud log表
class db_crud_log extends $pb.GeneratedMessage {
  factory db_crud_log({
    $core.String? rid,
    $core.String? orgRid,
    $core.String? userRid,
    $core.String? operation,
    $core.String? req,
    $core.String? reqOption,
    $core.String? ipInfo,
    $core.String? note,
    $core.String? updateAt,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgRid != null) {
      $result.orgRid = orgRid;
    }
    if (userRid != null) {
      $result.userRid = userRid;
    }
    if (operation != null) {
      $result.operation = operation;
    }
    if (req != null) {
      $result.req = req;
    }
    if (reqOption != null) {
      $result.reqOption = reqOption;
    }
    if (ipInfo != null) {
      $result.ipInfo = ipInfo;
    }
    if (note != null) {
      $result.note = note;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    return $result;
  }
  db_crud_log._() : super();
  factory db_crud_log.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_crud_log.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_crud_log', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgRid')
    ..aOS(3, _omitFieldNames ? '' : 'userRid')
    ..aOS(4, _omitFieldNames ? '' : 'operation')
    ..aOS(5, _omitFieldNames ? '' : 'req')
    ..aOS(6, _omitFieldNames ? '' : 'reqOption')
    ..aOS(7, _omitFieldNames ? '' : 'ipInfo')
    ..aOS(8, _omitFieldNames ? '' : 'note')
    ..aOS(14, _omitFieldNames ? '' : 'updateAt')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_crud_log clone() => db_crud_log()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_crud_log copyWith(void Function(db_crud_log) updates) => super.copyWith((message) => updates(message as db_crud_log)) as db_crud_log;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_crud_log create() => db_crud_log._();
  db_crud_log createEmptyInstance() => create();
  static $pb.PbList<db_crud_log> createRepeated() => $pb.PbList<db_crud_log>();
  @$core.pragma('dart2js:noInline')
  static db_crud_log getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_crud_log>(create);
  static db_crud_log? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid
  /// 用户所属的群组
  @$pb.TagNumber(2)
  $core.String get orgRid => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgRid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgRid() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgRid() => clearField(2);

  /// @table uuid
  /// 用户rid
  @$pb.TagNumber(3)
  $core.String get userRid => $_getSZ(2);
  @$pb.TagNumber(3)
  set userRid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasUserRid() => $_has(2);
  @$pb.TagNumber(3)
  void clearUserRid() => clearField(3);

  /// @table text
  /// 操作
  @$pb.TagNumber(4)
  $core.String get operation => $_getSZ(3);
  @$pb.TagNumber(4)
  set operation($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasOperation() => $_has(3);
  @$pb.TagNumber(4)
  void clearOperation() => clearField(4);

  /// @table jsonb
  /// req proto json
  @$pb.TagNumber(5)
  $core.String get req => $_getSZ(4);
  @$pb.TagNumber(5)
  set req($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasReq() => $_has(4);
  @$pb.TagNumber(5)
  void clearReq() => clearField(5);

  /// @table text
  /// req option
  @$pb.TagNumber(6)
  $core.String get reqOption => $_getSZ(5);
  @$pb.TagNumber(6)
  set reqOption($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasReqOption() => $_has(5);
  @$pb.TagNumber(6)
  void clearReqOption() => clearField(6);

  /// @table text
  /// ipinfo
  @$pb.TagNumber(7)
  $core.String get ipInfo => $_getSZ(6);
  @$pb.TagNumber(7)
  set ipInfo($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasIpInfo() => $_has(6);
  @$pb.TagNumber(7)
  void clearIpInfo() => clearField(7);

  /// @table jsonb
  /// note
  @$pb.TagNumber(8)
  $core.String get note => $_getSZ(7);
  @$pb.TagNumber(8)
  set note($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasNote() => $_has(7);
  @$pb.TagNumber(8)
  void clearNote() => clearField(8);

  /// @table timestamp not null default now_utc()
  /// 数据最后修改时间
  @$pb.TagNumber(14)
  $core.String get updateAt => $_getSZ(8);
  @$pb.TagNumber(14)
  set updateAt($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(14)
  $core.bool hasUpdateAt() => $_has(8);
  @$pb.TagNumber(14)
  void clearUpdateAt() => clearField(14);
}

/// 动态组成员详细信息
class db_dynamic_group_detail extends $pb.GeneratedMessage {
  factory db_dynamic_group_detail({
    $core.String? rid,
    $core.String? orgId,
    $core.String? deviceRid,
    $core.String? deviceDmrid,
    $core.String? groupRid,
    $core.String? groupDmrid,
    $core.int? isDeviceGroup,
    $core.int? memberState,
    $core.int? dynamicGroupType,
    $core.String? memberOrgId,
    $core.int? dynamicGroupState,
    $core.String? taskConfirmTime,
    $core.String? creator,
    $core.String? createTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (deviceRid != null) {
      $result.deviceRid = deviceRid;
    }
    if (deviceDmrid != null) {
      $result.deviceDmrid = deviceDmrid;
    }
    if (groupRid != null) {
      $result.groupRid = groupRid;
    }
    if (groupDmrid != null) {
      $result.groupDmrid = groupDmrid;
    }
    if (isDeviceGroup != null) {
      $result.isDeviceGroup = isDeviceGroup;
    }
    if (memberState != null) {
      $result.memberState = memberState;
    }
    if (dynamicGroupType != null) {
      $result.dynamicGroupType = dynamicGroupType;
    }
    if (memberOrgId != null) {
      $result.memberOrgId = memberOrgId;
    }
    if (dynamicGroupState != null) {
      $result.dynamicGroupState = dynamicGroupState;
    }
    if (taskConfirmTime != null) {
      $result.taskConfirmTime = taskConfirmTime;
    }
    if (creator != null) {
      $result.creator = creator;
    }
    if (createTime != null) {
      $result.createTime = createTime;
    }
    return $result;
  }
  db_dynamic_group_detail._() : super();
  factory db_dynamic_group_detail.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_dynamic_group_detail.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_dynamic_group_detail', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'deviceRid')
    ..aOS(4, _omitFieldNames ? '' : 'deviceDmrid')
    ..aOS(5, _omitFieldNames ? '' : 'groupRid')
    ..aOS(6, _omitFieldNames ? '' : 'groupDmrid')
    ..a<$core.int>(7, _omitFieldNames ? '' : 'isDeviceGroup', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'memberState', $pb.PbFieldType.O3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'dynamicGroupType', $pb.PbFieldType.O3)
    ..aOS(10, _omitFieldNames ? '' : 'memberOrgId')
    ..a<$core.int>(11, _omitFieldNames ? '' : 'dynamicGroupState', $pb.PbFieldType.O3)
    ..aOS(12, _omitFieldNames ? '' : 'taskConfirmTime')
    ..aOS(13, _omitFieldNames ? '' : 'creator')
    ..aOS(15, _omitFieldNames ? '' : 'createTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_dynamic_group_detail clone() => db_dynamic_group_detail()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_dynamic_group_detail copyWith(void Function(db_dynamic_group_detail) updates) => super.copyWith((message) => updates(message as db_dynamic_group_detail)) as db_dynamic_group_detail;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_dynamic_group_detail create() => db_dynamic_group_detail._();
  db_dynamic_group_detail createEmptyInstance() => create();
  static $pb.PbList<db_dynamic_group_detail> createRepeated() => $pb.PbList<db_dynamic_group_detail>();
  @$core.pragma('dart2js:noInline')
  static db_dynamic_group_detail getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_dynamic_group_detail>(create);
  static db_dynamic_group_detail? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid  REFERENCES db_org(rid) ON DELETE CASCADE
  /// 组成员所属的动态组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table uuid  REFERENCES db_device(rid) ON DELETE CASCADE
  /// 终端rid
  @$pb.TagNumber(3)
  $core.String get deviceRid => $_getSZ(2);
  @$pb.TagNumber(3)
  set deviceRid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDeviceRid() => $_has(2);
  @$pb.TagNumber(3)
  void clearDeviceRid() => clearField(3);

  /// @table varchar(8)
  /// 终端DMR ID
  @$pb.TagNumber(4)
  $core.String get deviceDmrid => $_getSZ(3);
  @$pb.TagNumber(4)
  set deviceDmrid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDeviceDmrid() => $_has(3);
  @$pb.TagNumber(4)
  void clearDeviceDmrid() => clearField(4);

  /// @table uuid  REFERENCES db_org(rid) ON DELETE CASCADE
  /// 组呼rid
  @$pb.TagNumber(5)
  $core.String get groupRid => $_getSZ(4);
  @$pb.TagNumber(5)
  set groupRid($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasGroupRid() => $_has(4);
  @$pb.TagNumber(5)
  void clearGroupRid() => clearField(5);

  /// @table varchar(8)
  /// 组呼DMR ID
  @$pb.TagNumber(6)
  $core.String get groupDmrid => $_getSZ(5);
  @$pb.TagNumber(6)
  set groupDmrid($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasGroupDmrid() => $_has(5);
  @$pb.TagNumber(6)
  void clearGroupDmrid() => clearField(6);

  /// @table int
  /// 此成员是终端还是组呼 1:终端 2:组呼 3:因任务组呼加入的终端
  @$pb.TagNumber(7)
  $core.int get isDeviceGroup => $_getIZ(6);
  @$pb.TagNumber(7)
  set isDeviceGroup($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasIsDeviceGroup() => $_has(6);
  @$pb.TagNumber(7)
  void clearIsDeviceGroup() => clearField(7);

  /// @table int default 0
  /// 在动态组中的状态
  /// 临时组：1：正常 2：被优先级高的抢占 10:已失效 11:已删除(用于回应客户端)
  /// 任务组：1:正常/已应答加入，2：被优先级高的抢占， 4:未应答加入 5:已应答退出 6:未应答退出
  @$pb.TagNumber(8)
  $core.int get memberState => $_getIZ(7);
  @$pb.TagNumber(8)
  set memberState($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasMemberState() => $_has(7);
  @$pb.TagNumber(8)
  void clearMemberState() => clearField(8);

  /// @table int default 0
  /// 动态组类型 0:临时组 1：任务组
  @$pb.TagNumber(9)
  $core.int get dynamicGroupType => $_getIZ(8);
  @$pb.TagNumber(9)
  set dynamicGroupType($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasDynamicGroupType() => $_has(8);
  @$pb.TagNumber(9)
  void clearDynamicGroupType() => clearField(9);

  /// @table uuid
  /// 组成员所属的单位rid
  @$pb.TagNumber(10)
  $core.String get memberOrgId => $_getSZ(9);
  @$pb.TagNumber(10)
  set memberOrgId($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasMemberOrgId() => $_has(9);
  @$pb.TagNumber(10)
  void clearMemberOrgId() => clearField(10);

  /// @table int default 0
  /// 动态组状态
  /// 临时组：1：正常 10：失效
  /// 任务组：1：正常 10：删除中
  @$pb.TagNumber(11)
  $core.int get dynamicGroupState => $_getIZ(10);
  @$pb.TagNumber(11)
  set dynamicGroupState($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasDynamicGroupState() => $_has(10);
  @$pb.TagNumber(11)
  void clearDynamicGroupState() => clearField(11);

  /// @table timestamp
  /// 任务组中终端的确认时间
  @$pb.TagNumber(12)
  $core.String get taskConfirmTime => $_getSZ(11);
  @$pb.TagNumber(12)
  set taskConfirmTime($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasTaskConfirmTime() => $_has(11);
  @$pb.TagNumber(12)
  void clearTaskConfirmTime() => clearField(12);

  /// @table text
  /// 创建者的rid
  @$pb.TagNumber(13)
  $core.String get creator => $_getSZ(12);
  @$pb.TagNumber(13)
  set creator($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasCreator() => $_has(12);
  @$pb.TagNumber(13)
  void clearCreator() => clearField(13);

  /// @table timestamp
  @$pb.TagNumber(15)
  $core.String get createTime => $_getSZ(13);
  @$pb.TagNumber(15)
  set createTime($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(15)
  $core.bool hasCreateTime() => $_has(13);
  @$pb.TagNumber(15)
  void clearCreateTime() => clearField(15);
}

/// 物联网终端
class db_iot_device extends $pb.GeneratedMessage {
  factory db_iot_device({
    $core.String? rid,
    $core.String? orgId,
    $core.String? devId,
    $core.int? devType,
    $core.String? devName,
    $core.String? note,
    $core.double? lon,
    $core.double? lat,
    $core.String? setting,
    $core.String? creator,
    $core.String? createTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (devId != null) {
      $result.devId = devId;
    }
    if (devType != null) {
      $result.devType = devType;
    }
    if (devName != null) {
      $result.devName = devName;
    }
    if (note != null) {
      $result.note = note;
    }
    if (lon != null) {
      $result.lon = lon;
    }
    if (lat != null) {
      $result.lat = lat;
    }
    if (setting != null) {
      $result.setting = setting;
    }
    if (creator != null) {
      $result.creator = creator;
    }
    if (createTime != null) {
      $result.createTime = createTime;
    }
    return $result;
  }
  db_iot_device._() : super();
  factory db_iot_device.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_iot_device.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_iot_device', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'devId')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'devType', $pb.PbFieldType.OS3)
    ..aOS(6, _omitFieldNames ? '' : 'devName')
    ..aOS(7, _omitFieldNames ? '' : 'note')
    ..a<$core.double>(8, _omitFieldNames ? '' : 'lon', $pb.PbFieldType.OD)
    ..a<$core.double>(9, _omitFieldNames ? '' : 'lat', $pb.PbFieldType.OD)
    ..aOS(10, _omitFieldNames ? '' : 'setting')
    ..aOS(13, _omitFieldNames ? '' : 'creator')
    ..aOS(15, _omitFieldNames ? '' : 'createTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_iot_device clone() => db_iot_device()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_iot_device copyWith(void Function(db_iot_device) updates) => super.copyWith((message) => updates(message as db_iot_device)) as db_iot_device;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_iot_device create() => db_iot_device._();
  db_iot_device createEmptyInstance() => create();
  static $pb.PbList<db_iot_device> createRepeated() => $pb.PbList<db_iot_device>();
  @$core.pragma('dart2js:noInline')
  static db_iot_device getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_iot_device>(create);
  static db_iot_device? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid  REFERENCES db_org(rid) ON DELETE CASCADE
  /// 所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table text not null unique
  /// 终端id
  @$pb.TagNumber(3)
  $core.String get devId => $_getSZ(2);
  @$pb.TagNumber(3)
  set devId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDevId() => $_has(2);
  @$pb.TagNumber(3)
  void clearDevId() => clearField(3);

  /// @table int
  /// 终端类型 1:对讲机 2:工牌 3:物卡 4：烟感  5：节能灯  6：温湿度
  @$pb.TagNumber(4)
  $core.int get devType => $_getIZ(3);
  @$pb.TagNumber(4)
  set devType($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDevType() => $_has(3);
  @$pb.TagNumber(4)
  void clearDevType() => clearField(4);

  /// @table text
  /// 名称
  @$pb.TagNumber(6)
  $core.String get devName => $_getSZ(4);
  @$pb.TagNumber(6)
  set devName($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasDevName() => $_has(4);
  @$pb.TagNumber(6)
  void clearDevName() => clearField(6);

  /// @table text
  /// 详细描述
  @$pb.TagNumber(7)
  $core.String get note => $_getSZ(5);
  @$pb.TagNumber(7)
  set note($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(7)
  $core.bool hasNote() => $_has(5);
  @$pb.TagNumber(7)
  void clearNote() => clearField(7);

  /// @table double precision
  /// 经度
  @$pb.TagNumber(8)
  $core.double get lon => $_getN(6);
  @$pb.TagNumber(8)
  set lon($core.double v) { $_setDouble(6, v); }
  @$pb.TagNumber(8)
  $core.bool hasLon() => $_has(6);
  @$pb.TagNumber(8)
  void clearLon() => clearField(8);

  /// @table double precision
  /// 纬度
  @$pb.TagNumber(9)
  $core.double get lat => $_getN(7);
  @$pb.TagNumber(9)
  set lat($core.double v) { $_setDouble(7, v); }
  @$pb.TagNumber(9)
  $core.bool hasLat() => $_has(7);
  @$pb.TagNumber(9)
  void clearLat() => clearField(9);

  /// @table jsonb not null default '{}'::jsonb
  /// 配置信息
  @$pb.TagNumber(10)
  $core.String get setting => $_getSZ(8);
  @$pb.TagNumber(10)
  set setting($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(10)
  $core.bool hasSetting() => $_has(8);
  @$pb.TagNumber(10)
  void clearSetting() => clearField(10);

  /// @table text
  /// 创建者的rid
  @$pb.TagNumber(13)
  $core.String get creator => $_getSZ(9);
  @$pb.TagNumber(13)
  set creator($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(13)
  $core.bool hasCreator() => $_has(9);
  @$pb.TagNumber(13)
  void clearCreator() => clearField(13);

  /// @table timestamp
  @$pb.TagNumber(15)
  $core.String get createTime => $_getSZ(10);
  @$pb.TagNumber(15)
  set createTime($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(15)
  $core.bool hasCreateTime() => $_has(10);
  @$pb.TagNumber(15)
  void clearCreateTime() => clearField(15);
}

/// iot限制
class db_iot_restriction extends $pb.GeneratedMessage {
  factory db_iot_restriction({
    $core.String? rid,
    $core.String? orgId,
    $core.String? iotId,
    $core.int? restrictType,
    $core.String? restrictStationRid,
    $core.int? restrictAliveInterval,
    $core.String? creator,
    $core.String? createTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (iotId != null) {
      $result.iotId = iotId;
    }
    if (restrictType != null) {
      $result.restrictType = restrictType;
    }
    if (restrictStationRid != null) {
      $result.restrictStationRid = restrictStationRid;
    }
    if (restrictAliveInterval != null) {
      $result.restrictAliveInterval = restrictAliveInterval;
    }
    if (creator != null) {
      $result.creator = creator;
    }
    if (createTime != null) {
      $result.createTime = createTime;
    }
    return $result;
  }
  db_iot_restriction._() : super();
  factory db_iot_restriction.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_iot_restriction.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_iot_restriction', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'iotId')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'restrictType', $pb.PbFieldType.O3)
    ..aOS(5, _omitFieldNames ? '' : 'restrictStationRid')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'restrictAliveInterval', $pb.PbFieldType.OS3)
    ..aOS(13, _omitFieldNames ? '' : 'creator')
    ..aOS(15, _omitFieldNames ? '' : 'createTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_iot_restriction clone() => db_iot_restriction()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_iot_restriction copyWith(void Function(db_iot_restriction) updates) => super.copyWith((message) => updates(message as db_iot_restriction)) as db_iot_restriction;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_iot_restriction create() => db_iot_restriction._();
  db_iot_restriction createEmptyInstance() => create();
  static $pb.PbList<db_iot_restriction> createRepeated() => $pb.PbList<db_iot_restriction>();
  @$core.pragma('dart2js:noInline')
  static db_iot_restriction getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_iot_restriction>(create);
  static db_iot_restriction? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid  REFERENCES db_org(rid) ON DELETE CASCADE
  /// iot所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table text not null unique
  /// iot id
  @$pb.TagNumber(3)
  $core.String get iotId => $_getSZ(2);
  @$pb.TagNumber(3)
  set iotId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasIotId() => $_has(2);
  @$pb.TagNumber(3)
  void clearIotId() => clearField(3);

  /// @table int not null default 0
  /// 限制类型 0:只允许进入基站 1：禁止进入基站 2:定时上报
  @$pb.TagNumber(4)
  $core.int get restrictType => $_getIZ(3);
  @$pb.TagNumber(4)
  set restrictType($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasRestrictType() => $_has(3);
  @$pb.TagNumber(4)
  void clearRestrictType() => clearField(4);

  /// @table uuid  REFERENCES db_line_point(rid) ON DELETE CASCADE
  /// 限制的基站 restrict_type=0/1时用
  @$pb.TagNumber(5)
  $core.String get restrictStationRid => $_getSZ(4);
  @$pb.TagNumber(5)
  set restrictStationRid($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasRestrictStationRid() => $_has(4);
  @$pb.TagNumber(5)
  void clearRestrictStationRid() => clearField(5);

  /// @table int not null default 0
  /// 定时上报间隔(秒)
  @$pb.TagNumber(6)
  $core.int get restrictAliveInterval => $_getIZ(5);
  @$pb.TagNumber(6)
  set restrictAliveInterval($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasRestrictAliveInterval() => $_has(5);
  @$pb.TagNumber(6)
  void clearRestrictAliveInterval() => clearField(6);

  /// @table text
  /// 创建者的rid
  @$pb.TagNumber(13)
  $core.String get creator => $_getSZ(6);
  @$pb.TagNumber(13)
  set creator($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(13)
  $core.bool hasCreator() => $_has(6);
  @$pb.TagNumber(13)
  void clearCreator() => clearField(13);

  /// @table timestamp
  @$pb.TagNumber(15)
  $core.String get createTime => $_getSZ(7);
  @$pb.TagNumber(15)
  set createTime($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(15)
  $core.bool hasCreateTime() => $_has(7);
  @$pb.TagNumber(15)
  void clearCreateTime() => clearField(15);
}

/// 物联终端最后的数据信息
class db_iot_device_last_info extends $pb.GeneratedMessage {
  factory db_iot_device_last_info({
    $core.String? rid,
    $core.String? updateAt,
    $core.String? orgId,
    $core.String? devId,
    $core.String? lastDataTime,
    $core.int? lastCmd,
    $core.String? devStatus,
    $core.String? lastController,
    $core.String? optStatus,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (updateAt != null) {
      $result.updateAt = updateAt;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (devId != null) {
      $result.devId = devId;
    }
    if (lastDataTime != null) {
      $result.lastDataTime = lastDataTime;
    }
    if (lastCmd != null) {
      $result.lastCmd = lastCmd;
    }
    if (devStatus != null) {
      $result.devStatus = devStatus;
    }
    if (lastController != null) {
      $result.lastController = lastController;
    }
    if (optStatus != null) {
      $result.optStatus = optStatus;
    }
    return $result;
  }
  db_iot_device_last_info._() : super();
  factory db_iot_device_last_info.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_iot_device_last_info.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_iot_device_last_info', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'updateAt')
    ..aOS(3, _omitFieldNames ? '' : 'orgId')
    ..aOS(5, _omitFieldNames ? '' : 'devId')
    ..aOS(6, _omitFieldNames ? '' : 'lastDataTime')
    ..a<$core.int>(7, _omitFieldNames ? '' : 'lastCmd', $pb.PbFieldType.OS3)
    ..aOS(14, _omitFieldNames ? '' : 'devStatus')
    ..aOS(15, _omitFieldNames ? '' : 'lastController')
    ..aOS(19, _omitFieldNames ? '' : 'optStatus')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_iot_device_last_info clone() => db_iot_device_last_info()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_iot_device_last_info copyWith(void Function(db_iot_device_last_info) updates) => super.copyWith((message) => updates(message as db_iot_device_last_info)) as db_iot_device_last_info;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_iot_device_last_info create() => db_iot_device_last_info._();
  db_iot_device_last_info createEmptyInstance() => create();
  static $pb.PbList<db_iot_device_last_info> createRepeated() => $pb.PbList<db_iot_device_last_info>();
  @$core.pragma('dart2js:noInline')
  static db_iot_device_last_info getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_iot_device_last_info>(create);
  static db_iot_device_last_info? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table timestamp not null default now_utc()
  /// 最后修改时间
  @$pb.TagNumber(2)
  $core.String get updateAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set updateAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasUpdateAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearUpdateAt() => clearField(2);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  /// 设备所属的群组
  @$pb.TagNumber(3)
  $core.String get orgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set orgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrgId() => clearField(3);

  /// @table varchar(16) not null unique
  /// iot设备ID
  @$pb.TagNumber(5)
  $core.String get devId => $_getSZ(3);
  @$pb.TagNumber(5)
  set devId($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(5)
  $core.bool hasDevId() => $_has(3);
  @$pb.TagNumber(5)
  void clearDevId() => clearField(5);

  /// @table timestamp  default '2000-01-01 00:00:00'
  /// 最后数据时间
  @$pb.TagNumber(6)
  $core.String get lastDataTime => $_getSZ(4);
  @$pb.TagNumber(6)
  set lastDataTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(6)
  $core.bool hasLastDataTime() => $_has(4);
  @$pb.TagNumber(6)
  void clearLastDataTime() => clearField(6);

  /// @table int not null default 0
  /// 最后命令值
  @$pb.TagNumber(7)
  $core.int get lastCmd => $_getIZ(5);
  @$pb.TagNumber(7)
  set lastCmd($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(7)
  $core.bool hasLastCmd() => $_has(5);
  @$pb.TagNumber(7)
  void clearLastCmd() => clearField(7);

  /// @table text default ''
  /// 最后的状态信息
  @$pb.TagNumber(14)
  $core.String get devStatus => $_getSZ(6);
  @$pb.TagNumber(14)
  set devStatus($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(14)
  $core.bool hasDevStatus() => $_has(6);
  @$pb.TagNumber(14)
  void clearDevStatus() => clearField(14);

  /// @table text default ''
  /// 最后接收的控制器id
  @$pb.TagNumber(15)
  $core.String get lastController => $_getSZ(7);
  @$pb.TagNumber(15)
  set lastController($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(15)
  $core.bool hasLastController() => $_has(7);
  @$pb.TagNumber(15)
  void clearLastController() => clearField(15);

  /// @table jsonb not null default '{}'::jsonb
  /// 额外的状态信息
  @$pb.TagNumber(19)
  $core.String get optStatus => $_getSZ(8);
  @$pb.TagNumber(19)
  set optStatus($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(19)
  $core.bool hasOptStatus() => $_has(8);
  @$pb.TagNumber(19)
  void clearOptStatus() => clearField(19);
}

/// iot_data历史表,按月分表
class db_iot_data_history extends $pb.GeneratedMessage {
  factory db_iot_data_history({
    $core.String? rid,
    $core.String? orgId,
    $core.String? cmdTime,
    $core.int? cmd,
    $core.int? devType,
    $core.String? devId,
    $core.String? recvStationId,
    $core.String? recvTime,
    $core.String? receiver,
    $core.String? cmdParam,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (orgId != null) {
      $result.orgId = orgId;
    }
    if (cmdTime != null) {
      $result.cmdTime = cmdTime;
    }
    if (cmd != null) {
      $result.cmd = cmd;
    }
    if (devType != null) {
      $result.devType = devType;
    }
    if (devId != null) {
      $result.devId = devId;
    }
    if (recvStationId != null) {
      $result.recvStationId = recvStationId;
    }
    if (recvTime != null) {
      $result.recvTime = recvTime;
    }
    if (receiver != null) {
      $result.receiver = receiver;
    }
    if (cmdParam != null) {
      $result.cmdParam = cmdParam;
    }
    return $result;
  }
  db_iot_data_history._() : super();
  factory db_iot_data_history.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_iot_data_history.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_iot_data_history', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'orgId')
    ..aOS(3, _omitFieldNames ? '' : 'cmdTime')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'cmd', $pb.PbFieldType.OS3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'devType', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'devId')
    ..aOS(7, _omitFieldNames ? '' : 'recvStationId')
    ..aOS(8, _omitFieldNames ? '' : 'recvTime')
    ..aOS(9, _omitFieldNames ? '' : 'receiver')
    ..aOS(10, _omitFieldNames ? '' : 'cmdParam')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_iot_data_history clone() => db_iot_data_history()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_iot_data_history copyWith(void Function(db_iot_data_history) updates) => super.copyWith((message) => updates(message as db_iot_data_history)) as db_iot_data_history;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_iot_data_history create() => db_iot_data_history._();
  db_iot_data_history createEmptyInstance() => create();
  static $pb.PbList<db_iot_data_history> createRepeated() => $pb.PbList<db_iot_data_history>();
  @$core.pragma('dart2js:noInline')
  static db_iot_data_history getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_iot_data_history>(create);
  static db_iot_data_history? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE
  /// iot dev所属的群组
  @$pb.TagNumber(2)
  $core.String get orgId => $_getSZ(1);
  @$pb.TagNumber(2)
  set orgId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasOrgId() => $_has(1);
  @$pb.TagNumber(2)
  void clearOrgId() => clearField(2);

  /// @table timestamp
  /// 接收时间
  @$pb.TagNumber(3)
  $core.String get cmdTime => $_getSZ(2);
  @$pb.TagNumber(3)
  set cmdTime($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasCmdTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearCmdTime() => clearField(3);

  /// @table int not null default 0
  /// 命令字
  @$pb.TagNumber(4)
  $core.int get cmd => $_getIZ(3);
  @$pb.TagNumber(4)
  set cmd($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCmd() => $_has(3);
  @$pb.TagNumber(4)
  void clearCmd() => clearField(4);

  /// @table int not null default 0
  /// 设备类型  3:物卡 4:烟感  5:节能灯  6:温湿度
  @$pb.TagNumber(5)
  $core.int get devType => $_getIZ(4);
  @$pb.TagNumber(5)
  set devType($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDevType() => $_has(4);
  @$pb.TagNumber(5)
  void clearDevType() => clearField(5);

  /// @table text
  /// iot dev id
  @$pb.TagNumber(6)
  $core.String get devId => $_getSZ(5);
  @$pb.TagNumber(6)
  set devId($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasDevId() => $_has(5);
  @$pb.TagNumber(6)
  void clearDevId() => clearField(6);

  /// @table text
  /// 接收基站ID
  @$pb.TagNumber(7)
  $core.String get recvStationId => $_getSZ(6);
  @$pb.TagNumber(7)
  set recvStationId($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasRecvStationId() => $_has(6);
  @$pb.TagNumber(7)
  void clearRecvStationId() => clearField(7);

  /// @table timestamp
  /// 后台接收时间
  @$pb.TagNumber(8)
  $core.String get recvTime => $_getSZ(7);
  @$pb.TagNumber(8)
  set recvTime($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasRecvTime() => $_has(7);
  @$pb.TagNumber(8)
  void clearRecvTime() => clearField(8);

  /// @table varchar(16)
  /// 接收的控制器id
  @$pb.TagNumber(9)
  $core.String get receiver => $_getSZ(8);
  @$pb.TagNumber(9)
  set receiver($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasReceiver() => $_has(8);
  @$pb.TagNumber(9)
  void clearReceiver() => clearField(9);

  /// @table text not null default ''
  /// 接收的其它参数
  @$pb.TagNumber(10)
  $core.String get cmdParam => $_getSZ(9);
  @$pb.TagNumber(10)
  set cmdParam($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasCmdParam() => $_has(9);
  @$pb.TagNumber(10)
  void clearCmdParam() => clearField(10);
}

/// 设备固定订阅/静态收听表
class db_static_subscribes extends $pb.GeneratedMessage {
  factory db_static_subscribes({
    $core.String? rid,
    $core.String? controllerDmrId,
    $core.String? groupDmrId,
    $core.String? creator,
    $core.String? createTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (controllerDmrId != null) {
      $result.controllerDmrId = controllerDmrId;
    }
    if (groupDmrId != null) {
      $result.groupDmrId = groupDmrId;
    }
    if (creator != null) {
      $result.creator = creator;
    }
    if (createTime != null) {
      $result.createTime = createTime;
    }
    return $result;
  }
  db_static_subscribes._() : super();
  factory db_static_subscribes.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_static_subscribes.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_static_subscribes', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'controllerDmrId')
    ..aOS(3, _omitFieldNames ? '' : 'groupDmrId')
    ..aOS(13, _omitFieldNames ? '' : 'creator')
    ..aOS(15, _omitFieldNames ? '' : 'createTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_static_subscribes clone() => db_static_subscribes()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_static_subscribes copyWith(void Function(db_static_subscribes) updates) => super.copyWith((message) => updates(message as db_static_subscribes)) as db_static_subscribes;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_static_subscribes create() => db_static_subscribes._();
  db_static_subscribes createEmptyInstance() => create();
  static $pb.PbList<db_static_subscribes> createRepeated() => $pb.PbList<db_static_subscribes>();
  @$core.pragma('dart2js:noInline')
  static db_static_subscribes getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_static_subscribes>(create);
  static db_static_subscribes? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table text not null references db_controller(dmr_id) on update cascade ON DELETE CASCADE
  /// 控制器dmrid
  @$pb.TagNumber(2)
  $core.String get controllerDmrId => $_getSZ(1);
  @$pb.TagNumber(2)
  set controllerDmrId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasControllerDmrId() => $_has(1);
  @$pb.TagNumber(2)
  void clearControllerDmrId() => clearField(2);

  /// @table varchar(8) not null REFERENCES db_org(dmr_id) ON update CASCADE ON DELETE CASCADE
  /// 群组dmrid
  @$pb.TagNumber(3)
  $core.String get groupDmrId => $_getSZ(2);
  @$pb.TagNumber(3)
  set groupDmrId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGroupDmrId() => $_has(2);
  @$pb.TagNumber(3)
  void clearGroupDmrId() => clearField(3);

  /// @table text
  /// 创建者的rid
  @$pb.TagNumber(13)
  $core.String get creator => $_getSZ(3);
  @$pb.TagNumber(13)
  set creator($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(13)
  $core.bool hasCreator() => $_has(3);
  @$pb.TagNumber(13)
  void clearCreator() => clearField(13);

  /// @table timestamp
  @$pb.TagNumber(15)
  $core.String get createTime => $_getSZ(4);
  @$pb.TagNumber(15)
  set createTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(15)
  $core.bool hasCreateTime() => $_has(4);
  @$pb.TagNumber(15)
  void clearCreateTime() => clearField(15);
}

/// app用户地图显示中特别许可的其它终端列表
class db_app_map_privilege_device extends $pb.GeneratedMessage {
  factory db_app_map_privilege_device({
    $core.String? rid,
    $core.String? appDmrid,
    $core.String? grantDeviceDmrid,
    $core.String? grantUserRid,
    $core.String? expireTime,
    $core.int? isSetExpire,
    $core.String? applyTime,
    $core.String? grantTime,
    $core.String? grantUserName,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (appDmrid != null) {
      $result.appDmrid = appDmrid;
    }
    if (grantDeviceDmrid != null) {
      $result.grantDeviceDmrid = grantDeviceDmrid;
    }
    if (grantUserRid != null) {
      $result.grantUserRid = grantUserRid;
    }
    if (expireTime != null) {
      $result.expireTime = expireTime;
    }
    if (isSetExpire != null) {
      $result.isSetExpire = isSetExpire;
    }
    if (applyTime != null) {
      $result.applyTime = applyTime;
    }
    if (grantTime != null) {
      $result.grantTime = grantTime;
    }
    if (grantUserName != null) {
      $result.grantUserName = grantUserName;
    }
    return $result;
  }
  db_app_map_privilege_device._() : super();
  factory db_app_map_privilege_device.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_app_map_privilege_device.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_app_map_privilege_device', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'appDmrid')
    ..aOS(3, _omitFieldNames ? '' : 'grantDeviceDmrid')
    ..aOS(4, _omitFieldNames ? '' : 'grantUserRid')
    ..aOS(5, _omitFieldNames ? '' : 'expireTime')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'isSetExpire', $pb.PbFieldType.O3)
    ..aOS(7, _omitFieldNames ? '' : 'applyTime')
    ..aOS(8, _omitFieldNames ? '' : 'grantTime')
    ..aOS(9, _omitFieldNames ? '' : 'grantUserName')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_app_map_privilege_device clone() => db_app_map_privilege_device()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_app_map_privilege_device copyWith(void Function(db_app_map_privilege_device) updates) => super.copyWith((message) => updates(message as db_app_map_privilege_device)) as db_app_map_privilege_device;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_app_map_privilege_device create() => db_app_map_privilege_device._();
  db_app_map_privilege_device createEmptyInstance() => create();
  static $pb.PbList<db_app_map_privilege_device> createRepeated() => $pb.PbList<db_app_map_privilege_device>();
  @$core.pragma('dart2js:noInline')
  static db_app_map_privilege_device getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_app_map_privilege_device>(create);
  static db_app_map_privilege_device? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table varchar(16) not null REFERENCES db_device(dmr_id) ON DELETE CASCADE
  /// app终端dmrid
  @$pb.TagNumber(2)
  $core.String get appDmrid => $_getSZ(1);
  @$pb.TagNumber(2)
  set appDmrid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAppDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearAppDmrid() => clearField(2);

  /// @table varchar(16) not null REFERENCES db_device(dmr_id) ON DELETE CASCADE
  /// 许可的终端dmrid
  @$pb.TagNumber(3)
  $core.String get grantDeviceDmrid => $_getSZ(2);
  @$pb.TagNumber(3)
  set grantDeviceDmrid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGrantDeviceDmrid() => $_has(2);
  @$pb.TagNumber(3)
  void clearGrantDeviceDmrid() => clearField(3);

  /// @table text
  /// 许可人rid
  @$pb.TagNumber(4)
  $core.String get grantUserRid => $_getSZ(3);
  @$pb.TagNumber(4)
  set grantUserRid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGrantUserRid() => $_has(3);
  @$pb.TagNumber(4)
  void clearGrantUserRid() => clearField(4);

  /// @table timestamp default '2000-01-01 00:00:00'
  /// 许可到期时间
  @$pb.TagNumber(5)
  $core.String get expireTime => $_getSZ(4);
  @$pb.TagNumber(5)
  set expireTime($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasExpireTime() => $_has(4);
  @$pb.TagNumber(5)
  void clearExpireTime() => clearField(5);

  /// @table int default 0
  /// 是否设置了许可超时时间
  @$pb.TagNumber(6)
  $core.int get isSetExpire => $_getIZ(5);
  @$pb.TagNumber(6)
  set isSetExpire($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasIsSetExpire() => $_has(5);
  @$pb.TagNumber(6)
  void clearIsSetExpire() => clearField(6);

  /// @table timestamp
  /// 申请许可时间
  @$pb.TagNumber(7)
  $core.String get applyTime => $_getSZ(6);
  @$pb.TagNumber(7)
  set applyTime($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasApplyTime() => $_has(6);
  @$pb.TagNumber(7)
  void clearApplyTime() => clearField(7);

  /// @table timestamp
  /// 授予许可时间
  @$pb.TagNumber(8)
  $core.String get grantTime => $_getSZ(7);
  @$pb.TagNumber(8)
  set grantTime($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasGrantTime() => $_has(7);
  @$pb.TagNumber(8)
  void clearGrantTime() => clearField(8);

  /// @table text
  /// 许可人名称
  @$pb.TagNumber(9)
  $core.String get grantUserName => $_getSZ(8);
  @$pb.TagNumber(9)
  set grantUserName($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasGrantUserName() => $_has(8);
  @$pb.TagNumber(9)
  void clearGrantUserName() => clearField(9);
}

/// poc session id表
class db_poc_session extends $pb.GeneratedMessage {
  factory db_poc_session({
    $core.String? rid,
    $core.String? pocDmrid,
    $core.String? sessionId,
    $core.String? loginTime,
    $core.String? ipInfo,
    $core.String? lastUpdateTime,
  }) {
    final $result = create();
    if (rid != null) {
      $result.rid = rid;
    }
    if (pocDmrid != null) {
      $result.pocDmrid = pocDmrid;
    }
    if (sessionId != null) {
      $result.sessionId = sessionId;
    }
    if (loginTime != null) {
      $result.loginTime = loginTime;
    }
    if (ipInfo != null) {
      $result.ipInfo = ipInfo;
    }
    if (lastUpdateTime != null) {
      $result.lastUpdateTime = lastUpdateTime;
    }
    return $result;
  }
  db_poc_session._() : super();
  factory db_poc_session.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_poc_session.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_poc_session', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'rid')
    ..aOS(2, _omitFieldNames ? '' : 'pocDmrid')
    ..aOS(3, _omitFieldNames ? '' : 'sessionId')
    ..aOS(4, _omitFieldNames ? '' : 'loginTime')
    ..aOS(5, _omitFieldNames ? '' : 'ipInfo')
    ..aOS(6, _omitFieldNames ? '' : 'lastUpdateTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_poc_session clone() => db_poc_session()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_poc_session copyWith(void Function(db_poc_session) updates) => super.copyWith((message) => updates(message as db_poc_session)) as db_poc_session;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_poc_session create() => db_poc_session._();
  db_poc_session createEmptyInstance() => create();
  static $pb.PbList<db_poc_session> createRepeated() => $pb.PbList<db_poc_session>();
  @$core.pragma('dart2js:noInline')
  static db_poc_session getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_poc_session>(create);
  static db_poc_session? _defaultInstance;

  /// @table uuid primary key
  /// 行ID
  @$pb.TagNumber(1)
  $core.String get rid => $_getSZ(0);
  @$pb.TagNumber(1)
  set rid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasRid() => $_has(0);
  @$pb.TagNumber(1)
  void clearRid() => clearField(1);

  /// @table text not null references db_device(dmr_id) on update cascade ON DELETE CASCADE
  /// poc终端dmrid
  @$pb.TagNumber(2)
  $core.String get pocDmrid => $_getSZ(1);
  @$pb.TagNumber(2)
  set pocDmrid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPocDmrid() => $_has(1);
  @$pb.TagNumber(2)
  void clearPocDmrid() => clearField(2);

  /// @table uuid not null
  /// session id
  @$pb.TagNumber(3)
  $core.String get sessionId => $_getSZ(2);
  @$pb.TagNumber(3)
  set sessionId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSessionId() => $_has(2);
  @$pb.TagNumber(3)
  void clearSessionId() => clearField(3);

  /// @table timestamp
  /// login时间
  @$pb.TagNumber(4)
  $core.String get loginTime => $_getSZ(3);
  @$pb.TagNumber(4)
  set loginTime($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasLoginTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearLoginTime() => clearField(4);

  /// @table text
  /// ip info
  @$pb.TagNumber(5)
  $core.String get ipInfo => $_getSZ(4);
  @$pb.TagNumber(5)
  set ipInfo($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasIpInfo() => $_has(4);
  @$pb.TagNumber(5)
  void clearIpInfo() => clearField(5);

  /// @table timestamp
  /// last update time
  @$pb.TagNumber(6)
  $core.String get lastUpdateTime => $_getSZ(5);
  @$pb.TagNumber(6)
  set lastUpdateTime($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasLastUpdateTime() => $_has(5);
  @$pb.TagNumber(6)
  void clearLastUpdateTime() => clearField(6);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
