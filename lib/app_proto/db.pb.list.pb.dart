//
//  Generated code. Do not modify.
//  source: db.pb.list.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'db.pb.dart' as $1;

/// 系统设置表
class db_sys_config_list extends $pb.GeneratedMessage {
  factory db_sys_config_list({
    $core.Iterable<$1.db_sys_config>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_sys_config_list._() : super();
  factory db_sys_config_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_sys_config_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_sys_config_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_sys_config>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_sys_config.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_sys_config_list clone() => db_sys_config_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_sys_config_list copyWith(void Function(db_sys_config_list) updates) => super.copyWith((message) => updates(message as db_sys_config_list)) as db_sys_config_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_sys_config_list create() => db_sys_config_list._();
  db_sys_config_list createEmptyInstance() => create();
  static $pb.PbList<db_sys_config_list> createRepeated() => $pb.PbList<db_sys_config_list>();
  @$core.pragma('dart2js:noInline')
  static db_sys_config_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_sys_config_list>(create);
  static db_sys_config_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_sys_config> get rows => $_getList(0);
}

/// 表各种操作时间// 客户端有时需要查询后台是否已经更新了数据,可以通过此表来得到初步的信息
class db_table_operate_time_list extends $pb.GeneratedMessage {
  factory db_table_operate_time_list({
    $core.Iterable<$1.db_table_operate_time>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_table_operate_time_list._() : super();
  factory db_table_operate_time_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_table_operate_time_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_table_operate_time_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_table_operate_time>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_table_operate_time.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_table_operate_time_list clone() => db_table_operate_time_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_table_operate_time_list copyWith(void Function(db_table_operate_time_list) updates) => super.copyWith((message) => updates(message as db_table_operate_time_list)) as db_table_operate_time_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_table_operate_time_list create() => db_table_operate_time_list._();
  db_table_operate_time_list createEmptyInstance() => create();
  static $pb.PbList<db_table_operate_time_list> createRepeated() => $pb.PbList<db_table_operate_time_list>();
  @$core.pragma('dart2js:noInline')
  static db_table_operate_time_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_table_operate_time_list>(create);
  static db_table_operate_time_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_table_operate_time> get rows => $_getList(0);
}

/// 组织架构表
class db_org_list extends $pb.GeneratedMessage {
  factory db_org_list({
    $core.Iterable<$1.db_org>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_org_list._() : super();
  factory db_org_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_org_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_org_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_org>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_org.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_org_list clone() => db_org_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_org_list copyWith(void Function(db_org_list) updates) => super.copyWith((message) => updates(message as db_org_list)) as db_org_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_org_list create() => db_org_list._();
  db_org_list createEmptyInstance() => create();
  static $pb.PbList<db_org_list> createRepeated() => $pb.PbList<db_org_list>();
  @$core.pragma('dart2js:noInline')
  static db_org_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_org_list>(create);
  static db_org_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_org> get rows => $_getList(0);
}

/// 用户的一些图片数据,地图点icon等
class db_image_list extends $pb.GeneratedMessage {
  factory db_image_list({
    $core.Iterable<$1.db_image>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_image_list._() : super();
  factory db_image_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_image_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_image_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_image>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_image.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_image_list clone() => db_image_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_image_list copyWith(void Function(db_image_list) updates) => super.copyWith((message) => updates(message as db_image_list)) as db_image_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_image_list create() => db_image_list._();
  db_image_list createEmptyInstance() => create();
  static $pb.PbList<db_image_list> createRepeated() => $pb.PbList<db_image_list>();
  @$core.pragma('dart2js:noInline')
  static db_image_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_image_list>(create);
  static db_image_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_image> get rows => $_getList(0);
}

/// 基站列表
class db_base_station_list extends $pb.GeneratedMessage {
  factory db_base_station_list({
    $core.Iterable<$1.db_base_station>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_base_station_list._() : super();
  factory db_base_station_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_base_station_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_base_station_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_base_station>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_base_station.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_base_station_list clone() => db_base_station_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_base_station_list copyWith(void Function(db_base_station_list) updates) => super.copyWith((message) => updates(message as db_base_station_list)) as db_base_station_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_base_station_list create() => db_base_station_list._();
  db_base_station_list createEmptyInstance() => create();
  static $pb.PbList<db_base_station_list> createRepeated() => $pb.PbList<db_base_station_list>();
  @$core.pragma('dart2js:noInline')
  static db_base_station_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_base_station_list>(create);
  static db_base_station_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_base_station> get rows => $_getList(0);
}

/// 控制器设备表
class db_controller_list extends $pb.GeneratedMessage {
  factory db_controller_list({
    $core.Iterable<$1.db_controller>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_controller_list._() : super();
  factory db_controller_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_controller_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_controller_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_controller>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_controller.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_controller_list clone() => db_controller_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_controller_list copyWith(void Function(db_controller_list) updates) => super.copyWith((message) => updates(message as db_controller_list)) as db_controller_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_controller_list create() => db_controller_list._();
  db_controller_list createEmptyInstance() => create();
  static $pb.PbList<db_controller_list> createRepeated() => $pb.PbList<db_controller_list>();
  @$core.pragma('dart2js:noInline')
  static db_controller_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_controller_list>(create);
  static db_controller_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_controller> get rows => $_getList(0);
}

/// 控制器状态
class db_controller_last_info_list extends $pb.GeneratedMessage {
  factory db_controller_last_info_list({
    $core.Iterable<$1.db_controller_last_info>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_controller_last_info_list._() : super();
  factory db_controller_last_info_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_controller_last_info_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_controller_last_info_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_controller_last_info>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_controller_last_info.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_controller_last_info_list clone() => db_controller_last_info_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_controller_last_info_list copyWith(void Function(db_controller_last_info_list) updates) => super.copyWith((message) => updates(message as db_controller_last_info_list)) as db_controller_last_info_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_controller_last_info_list create() => db_controller_last_info_list._();
  db_controller_last_info_list createEmptyInstance() => create();
  static $pb.PbList<db_controller_last_info_list> createRepeated() => $pb.PbList<db_controller_last_info_list>();
  @$core.pragma('dart2js:noInline')
  static db_controller_last_info_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_controller_last_info_list>(create);
  static db_controller_last_info_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_controller_last_info> get rows => $_getList(0);
}

/// 控制器上线历史表,按月分表
class db_controller_online_history_list extends $pb.GeneratedMessage {
  factory db_controller_online_history_list({
    $core.Iterable<$1.db_controller_online_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_controller_online_history_list._() : super();
  factory db_controller_online_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_controller_online_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_controller_online_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_controller_online_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_controller_online_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_controller_online_history_list clone() => db_controller_online_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_controller_online_history_list copyWith(void Function(db_controller_online_history_list) updates) => super.copyWith((message) => updates(message as db_controller_online_history_list)) as db_controller_online_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_controller_online_history_list create() => db_controller_online_history_list._();
  db_controller_online_history_list createEmptyInstance() => create();
  static $pb.PbList<db_controller_online_history_list> createRepeated() => $pb.PbList<db_controller_online_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_controller_online_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_controller_online_history_list>(create);
  static db_controller_online_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_controller_online_history> get rows => $_getList(0);
}

/// 电话网关黑白名单
class db_phone_gateway_filter_list extends $pb.GeneratedMessage {
  factory db_phone_gateway_filter_list({
    $core.Iterable<$1.db_phone_gateway_filter>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_phone_gateway_filter_list._() : super();
  factory db_phone_gateway_filter_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_phone_gateway_filter_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_phone_gateway_filter_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_phone_gateway_filter>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_phone_gateway_filter.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_phone_gateway_filter_list clone() => db_phone_gateway_filter_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_phone_gateway_filter_list copyWith(void Function(db_phone_gateway_filter_list) updates) => super.copyWith((message) => updates(message as db_phone_gateway_filter_list)) as db_phone_gateway_filter_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_phone_gateway_filter_list create() => db_phone_gateway_filter_list._();
  db_phone_gateway_filter_list createEmptyInstance() => create();
  static $pb.PbList<db_phone_gateway_filter_list> createRepeated() => $pb.PbList<db_phone_gateway_filter_list>();
  @$core.pragma('dart2js:noInline')
  static db_phone_gateway_filter_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_phone_gateway_filter_list>(create);
  static db_phone_gateway_filter_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_phone_gateway_filter> get rows => $_getList(0);
}

/// 对讲机设备表
class db_device_list extends $pb.GeneratedMessage {
  factory db_device_list({
    $core.Iterable<$1.db_device>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_device_list._() : super();
  factory db_device_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_device>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_device.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_list clone() => db_device_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_list copyWith(void Function(db_device_list) updates) => super.copyWith((message) => updates(message as db_device_list)) as db_device_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_list create() => db_device_list._();
  db_device_list createEmptyInstance() => create();
  static $pb.PbList<db_device_list> createRepeated() => $pb.PbList<db_device_list>();
  @$core.pragma('dart2js:noInline')
  static db_device_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_list>(create);
  static db_device_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_device> get rows => $_getList(0);
}

/// 对讲机最后的数据信息
class db_device_last_info_list extends $pb.GeneratedMessage {
  factory db_device_last_info_list({
    $core.Iterable<$1.db_device_last_info>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_device_last_info_list._() : super();
  factory db_device_last_info_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_last_info_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_last_info_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_device_last_info>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_device_last_info.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_last_info_list clone() => db_device_last_info_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_last_info_list copyWith(void Function(db_device_last_info_list) updates) => super.copyWith((message) => updates(message as db_device_last_info_list)) as db_device_last_info_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_last_info_list create() => db_device_last_info_list._();
  db_device_last_info_list createEmptyInstance() => create();
  static $pb.PbList<db_device_last_info_list> createRepeated() => $pb.PbList<db_device_last_info_list>();
  @$core.pragma('dart2js:noInline')
  static db_device_last_info_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_last_info_list>(create);
  static db_device_last_info_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_device_last_info> get rows => $_getList(0);
}

/// 用户职称表
class db_user_title_list extends $pb.GeneratedMessage {
  factory db_user_title_list({
    $core.Iterable<$1.db_user_title>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_user_title_list._() : super();
  factory db_user_title_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_title_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_title_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_user_title>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_user_title.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_title_list clone() => db_user_title_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_title_list copyWith(void Function(db_user_title_list) updates) => super.copyWith((message) => updates(message as db_user_title_list)) as db_user_title_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_title_list create() => db_user_title_list._();
  db_user_title_list createEmptyInstance() => create();
  static $pb.PbList<db_user_title_list> createRepeated() => $pb.PbList<db_user_title_list>();
  @$core.pragma('dart2js:noInline')
  static db_user_title_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_title_list>(create);
  static db_user_title_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_user_title> get rows => $_getList(0);
}

/// 用户数据表
class db_user_list extends $pb.GeneratedMessage {
  factory db_user_list({
    $core.Iterable<$1.db_user>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_user_list._() : super();
  factory db_user_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_user>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_user.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_list clone() => db_user_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_list copyWith(void Function(db_user_list) updates) => super.copyWith((message) => updates(message as db_user_list)) as db_user_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_list create() => db_user_list._();
  db_user_list createEmptyInstance() => create();
  static $pb.PbList<db_user_list> createRepeated() => $pb.PbList<db_user_list>();
  @$core.pragma('dart2js:noInline')
  static db_user_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_list>(create);
  static db_user_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_user> get rows => $_getList(0);
}

/// 用户群组权限表
class db_user_privelege_list extends $pb.GeneratedMessage {
  factory db_user_privelege_list({
    $core.Iterable<$1.db_user_privelege>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_user_privelege_list._() : super();
  factory db_user_privelege_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_privelege_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_privelege_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_user_privelege>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_user_privelege.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_privelege_list clone() => db_user_privelege_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_privelege_list copyWith(void Function(db_user_privelege_list) updates) => super.copyWith((message) => updates(message as db_user_privelege_list)) as db_user_privelege_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_privelege_list create() => db_user_privelege_list._();
  db_user_privelege_list createEmptyInstance() => create();
  static $pb.PbList<db_user_privelege_list> createRepeated() => $pb.PbList<db_user_privelege_list>();
  @$core.pragma('dart2js:noInline')
  static db_user_privelege_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_privelege_list>(create);
  static db_user_privelege_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_user_privelege> get rows => $_getList(0);
}

/// 用户登录的session id表
class db_user_session_id_list extends $pb.GeneratedMessage {
  factory db_user_session_id_list({
    $core.Iterable<$1.db_user_session_id>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_user_session_id_list._() : super();
  factory db_user_session_id_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_session_id_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_session_id_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_user_session_id>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_user_session_id.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_session_id_list clone() => db_user_session_id_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_session_id_list copyWith(void Function(db_user_session_id_list) updates) => super.copyWith((message) => updates(message as db_user_session_id_list)) as db_user_session_id_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_session_id_list create() => db_user_session_id_list._();
  db_user_session_id_list createEmptyInstance() => create();
  static $pb.PbList<db_user_session_id_list> createRepeated() => $pb.PbList<db_user_session_id_list>();
  @$core.pragma('dart2js:noInline')
  static db_user_session_id_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_session_id_list>(create);
  static db_user_session_id_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_user_session_id> get rows => $_getList(0);
}

/// 虚拟群组信息表
class db_virtual_org_list extends $pb.GeneratedMessage {
  factory db_virtual_org_list({
    $core.Iterable<$1.db_virtual_org>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_virtual_org_list._() : super();
  factory db_virtual_org_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_virtual_org_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_virtual_org_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_virtual_org>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_virtual_org.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_virtual_org_list clone() => db_virtual_org_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_virtual_org_list copyWith(void Function(db_virtual_org_list) updates) => super.copyWith((message) => updates(message as db_virtual_org_list)) as db_virtual_org_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_virtual_org_list create() => db_virtual_org_list._();
  db_virtual_org_list createEmptyInstance() => create();
  static $pb.PbList<db_virtual_org_list> createRepeated() => $pb.PbList<db_virtual_org_list>();
  @$core.pragma('dart2js:noInline')
  static db_virtual_org_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_virtual_org_list>(create);
  static db_virtual_org_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_virtual_org> get rows => $_getList(0);
}

/// 用户自己的一些地图标志
class db_map_point_list extends $pb.GeneratedMessage {
  factory db_map_point_list({
    $core.Iterable<$1.db_map_point>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_map_point_list._() : super();
  factory db_map_point_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_map_point_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_map_point_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_map_point>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_map_point.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_map_point_list clone() => db_map_point_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_map_point_list copyWith(void Function(db_map_point_list) updates) => super.copyWith((message) => updates(message as db_map_point_list)) as db_map_point_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_map_point_list create() => db_map_point_list._();
  db_map_point_list createEmptyInstance() => create();
  static $pb.PbList<db_map_point_list> createRepeated() => $pb.PbList<db_map_point_list>();
  @$core.pragma('dart2js:noInline')
  static db_map_point_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_map_point_list>(create);
  static db_map_point_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_map_point> get rows => $_getList(0);
}

/// 巡查线路点
class db_line_point_list extends $pb.GeneratedMessage {
  factory db_line_point_list({
    $core.Iterable<$1.db_line_point>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_line_point_list._() : super();
  factory db_line_point_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_line_point_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_line_point_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_line_point>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_line_point.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_line_point_list clone() => db_line_point_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_line_point_list copyWith(void Function(db_line_point_list) updates) => super.copyWith((message) => updates(message as db_line_point_list)) as db_line_point_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_line_point_list create() => db_line_point_list._();
  db_line_point_list createEmptyInstance() => create();
  static $pb.PbList<db_line_point_list> createRepeated() => $pb.PbList<db_line_point_list>();
  @$core.pragma('dart2js:noInline')
  static db_line_point_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_line_point_list>(create);
  static db_line_point_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_line_point> get rows => $_getList(0);
}

/// 巡查点最新信息
class db_line_point_latest_info_list extends $pb.GeneratedMessage {
  factory db_line_point_latest_info_list({
    $core.Iterable<$1.db_line_point_latest_info>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_line_point_latest_info_list._() : super();
  factory db_line_point_latest_info_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_line_point_latest_info_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_line_point_latest_info_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_line_point_latest_info>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_line_point_latest_info.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_line_point_latest_info_list clone() => db_line_point_latest_info_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_line_point_latest_info_list copyWith(void Function(db_line_point_latest_info_list) updates) => super.copyWith((message) => updates(message as db_line_point_latest_info_list)) as db_line_point_latest_info_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_line_point_latest_info_list create() => db_line_point_latest_info_list._();
  db_line_point_latest_info_list createEmptyInstance() => create();
  static $pb.PbList<db_line_point_latest_info_list> createRepeated() => $pb.PbList<db_line_point_latest_info_list>();
  @$core.pragma('dart2js:noInline')
  static db_line_point_latest_info_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_line_point_latest_info_list>(create);
  static db_line_point_latest_info_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_line_point_latest_info> get rows => $_getList(0);
}

/// 巡查线路主表
class db_line_master_list extends $pb.GeneratedMessage {
  factory db_line_master_list({
    $core.Iterable<$1.db_line_master>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_line_master_list._() : super();
  factory db_line_master_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_line_master_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_line_master_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_line_master>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_line_master.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_line_master_list clone() => db_line_master_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_line_master_list copyWith(void Function(db_line_master_list) updates) => super.copyWith((message) => updates(message as db_line_master_list)) as db_line_master_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_line_master_list create() => db_line_master_list._();
  db_line_master_list createEmptyInstance() => create();
  static $pb.PbList<db_line_master_list> createRepeated() => $pb.PbList<db_line_master_list>();
  @$core.pragma('dart2js:noInline')
  static db_line_master_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_line_master_list>(create);
  static db_line_master_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_line_master> get rows => $_getList(0);
}

/// 巡查线路细表
class db_line_detail_list extends $pb.GeneratedMessage {
  factory db_line_detail_list({
    $core.Iterable<$1.db_line_detail>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_line_detail_list._() : super();
  factory db_line_detail_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_line_detail_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_line_detail_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_line_detail>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_line_detail.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_line_detail_list clone() => db_line_detail_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_line_detail_list copyWith(void Function(db_line_detail_list) updates) => super.copyWith((message) => updates(message as db_line_detail_list)) as db_line_detail_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_line_detail_list create() => db_line_detail_list._();
  db_line_detail_list createEmptyInstance() => create();
  static $pb.PbList<db_line_detail_list> createRepeated() => $pb.PbList<db_line_detail_list>();
  @$core.pragma('dart2js:noInline')
  static db_line_detail_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_line_detail_list>(create);
  static db_line_detail_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_line_detail> get rows => $_getList(0);
}

/// 巡查规则表
class db_rfid_rule_master_list extends $pb.GeneratedMessage {
  factory db_rfid_rule_master_list({
    $core.Iterable<$1.db_rfid_rule_master>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_rfid_rule_master_list._() : super();
  factory db_rfid_rule_master_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_rfid_rule_master_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_rfid_rule_master_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_rfid_rule_master>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_rfid_rule_master.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_rfid_rule_master_list clone() => db_rfid_rule_master_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_rfid_rule_master_list copyWith(void Function(db_rfid_rule_master_list) updates) => super.copyWith((message) => updates(message as db_rfid_rule_master_list)) as db_rfid_rule_master_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_rfid_rule_master_list create() => db_rfid_rule_master_list._();
  db_rfid_rule_master_list createEmptyInstance() => create();
  static $pb.PbList<db_rfid_rule_master_list> createRepeated() => $pb.PbList<db_rfid_rule_master_list>();
  @$core.pragma('dart2js:noInline')
  static db_rfid_rule_master_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_rfid_rule_master_list>(create);
  static db_rfid_rule_master_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_rfid_rule_master> get rows => $_getList(0);
}

/// 开关机数据表,按月分表
class db_device_power_onoff_list extends $pb.GeneratedMessage {
  factory db_device_power_onoff_list({
    $core.Iterable<$1.db_device_power_onoff>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_device_power_onoff_list._() : super();
  factory db_device_power_onoff_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_power_onoff_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_power_onoff_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_device_power_onoff>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_device_power_onoff.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_power_onoff_list clone() => db_device_power_onoff_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_power_onoff_list copyWith(void Function(db_device_power_onoff_list) updates) => super.copyWith((message) => updates(message as db_device_power_onoff_list)) as db_device_power_onoff_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_power_onoff_list create() => db_device_power_onoff_list._();
  db_device_power_onoff_list createEmptyInstance() => create();
  static $pb.PbList<db_device_power_onoff_list> createRepeated() => $pb.PbList<db_device_power_onoff_list>();
  @$core.pragma('dart2js:noInline')
  static db_device_power_onoff_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_power_onoff_list>(create);
  static db_device_power_onoff_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_device_power_onoff> get rows => $_getList(0);
}

/// 上班下班打卡数据表,按月分表
class db_user_check_in_history_list extends $pb.GeneratedMessage {
  factory db_user_check_in_history_list({
    $core.Iterable<$1.db_user_check_in_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_user_check_in_history_list._() : super();
  factory db_user_check_in_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_user_check_in_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_user_check_in_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_user_check_in_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_user_check_in_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_user_check_in_history_list clone() => db_user_check_in_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_user_check_in_history_list copyWith(void Function(db_user_check_in_history_list) updates) => super.copyWith((message) => updates(message as db_user_check_in_history_list)) as db_user_check_in_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_user_check_in_history_list create() => db_user_check_in_history_list._();
  db_user_check_in_history_list createEmptyInstance() => create();
  static $pb.PbList<db_user_check_in_history_list> createRepeated() => $pb.PbList<db_user_check_in_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_user_check_in_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_user_check_in_history_list>(create);
  static db_user_check_in_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_user_check_in_history> get rows => $_getList(0);
}

/// rfid巡查历史表,按月分表
class db_rfid_history_list extends $pb.GeneratedMessage {
  factory db_rfid_history_list({
    $core.Iterable<$1.db_rfid_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_rfid_history_list._() : super();
  factory db_rfid_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_rfid_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_rfid_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_rfid_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_rfid_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_rfid_history_list clone() => db_rfid_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_rfid_history_list copyWith(void Function(db_rfid_history_list) updates) => super.copyWith((message) => updates(message as db_rfid_history_list)) as db_rfid_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_rfid_history_list create() => db_rfid_history_list._();
  db_rfid_history_list createEmptyInstance() => create();
  static $pb.PbList<db_rfid_history_list> createRepeated() => $pb.PbList<db_rfid_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_rfid_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_rfid_history_list>(create);
  static db_rfid_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_rfid_history> get rows => $_getList(0);
}

/// gps位置历史表,按月分表
class db_gps_history_list extends $pb.GeneratedMessage {
  factory db_gps_history_list({
    $core.Iterable<$1.db_gps_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_gps_history_list._() : super();
  factory db_gps_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_gps_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_gps_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_gps_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_gps_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_gps_history_list clone() => db_gps_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_gps_history_list copyWith(void Function(db_gps_history_list) updates) => super.copyWith((message) => updates(message as db_gps_history_list)) as db_gps_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_gps_history_list create() => db_gps_history_list._();
  db_gps_history_list createEmptyInstance() => create();
  static $pb.PbList<db_gps_history_list> createRepeated() => $pb.PbList<db_gps_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_gps_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_gps_history_list>(create);
  static db_gps_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_gps_history> get rows => $_getList(0);
}

/// 报警历史,要分表了,因为报警可能会非常多,客户端需要编辑此表,分表客户端处理需要特殊处理
class db_alarm_history_list extends $pb.GeneratedMessage {
  factory db_alarm_history_list({
    $core.Iterable<$1.db_alarm_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_alarm_history_list._() : super();
  factory db_alarm_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_alarm_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_alarm_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_alarm_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_alarm_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_alarm_history_list clone() => db_alarm_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_alarm_history_list copyWith(void Function(db_alarm_history_list) updates) => super.copyWith((message) => updates(message as db_alarm_history_list)) as db_alarm_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_alarm_history_list create() => db_alarm_history_list._();
  db_alarm_history_list createEmptyInstance() => create();
  static $pb.PbList<db_alarm_history_list> createRepeated() => $pb.PbList<db_alarm_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_alarm_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_alarm_history_list>(create);
  static db_alarm_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_alarm_history> get rows => $_getList(0);
}

/// 对讲机通话历史,按月分表
class db_sound_history_list extends $pb.GeneratedMessage {
  factory db_sound_history_list({
    $core.Iterable<$1.db_sound_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_sound_history_list._() : super();
  factory db_sound_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_sound_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_sound_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_sound_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_sound_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_sound_history_list clone() => db_sound_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_sound_history_list copyWith(void Function(db_sound_history_list) updates) => super.copyWith((message) => updates(message as db_sound_history_list)) as db_sound_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_sound_history_list create() => db_sound_history_list._();
  db_sound_history_list createEmptyInstance() => create();
  static $pb.PbList<db_sound_history_list> createRepeated() => $pb.PbList<db_sound_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_sound_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_sound_history_list>(create);
  static db_sound_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_sound_history> get rows => $_getList(0);
}

/// 还没发送的命令
class db_not_send_cmd_list extends $pb.GeneratedMessage {
  factory db_not_send_cmd_list({
    $core.Iterable<$1.db_not_send_cmd>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_not_send_cmd_list._() : super();
  factory db_not_send_cmd_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_not_send_cmd_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_not_send_cmd_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_not_send_cmd>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_not_send_cmd.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_not_send_cmd_list clone() => db_not_send_cmd_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_not_send_cmd_list copyWith(void Function(db_not_send_cmd_list) updates) => super.copyWith((message) => updates(message as db_not_send_cmd_list)) as db_not_send_cmd_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_not_send_cmd_list create() => db_not_send_cmd_list._();
  db_not_send_cmd_list createEmptyInstance() => create();
  static $pb.PbList<db_not_send_cmd_list> createRepeated() => $pb.PbList<db_not_send_cmd_list>();
  @$core.pragma('dart2js:noInline')
  static db_not_send_cmd_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_not_send_cmd_list>(create);
  static db_not_send_cmd_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_not_send_cmd> get rows => $_getList(0);
}

/// 已经发送的命令列表
class db_sent_cmd_history_list extends $pb.GeneratedMessage {
  factory db_sent_cmd_history_list({
    $core.Iterable<$1.db_sent_cmd_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_sent_cmd_history_list._() : super();
  factory db_sent_cmd_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_sent_cmd_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_sent_cmd_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_sent_cmd_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_sent_cmd_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_sent_cmd_history_list clone() => db_sent_cmd_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_sent_cmd_history_list copyWith(void Function(db_sent_cmd_history_list) updates) => super.copyWith((message) => updates(message as db_sent_cmd_history_list)) as db_sent_cmd_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_sent_cmd_history_list create() => db_sent_cmd_history_list._();
  db_sent_cmd_history_list createEmptyInstance() => create();
  static $pb.PbList<db_sent_cmd_history_list> createRepeated() => $pb.PbList<db_sent_cmd_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_sent_cmd_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_sent_cmd_history_list>(create);
  static db_sent_cmd_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_sent_cmd_history> get rows => $_getList(0);
}

/// 对讲机注册信息
class db_device_register_info_list extends $pb.GeneratedMessage {
  factory db_device_register_info_list({
    $core.Iterable<$1.db_device_register_info>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_device_register_info_list._() : super();
  factory db_device_register_info_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_register_info_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_register_info_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_device_register_info>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_device_register_info.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_register_info_list clone() => db_device_register_info_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_register_info_list copyWith(void Function(db_device_register_info_list) updates) => super.copyWith((message) => updates(message as db_device_register_info_list)) as db_device_register_info_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_register_info_list create() => db_device_register_info_list._();
  db_device_register_info_list createEmptyInstance() => create();
  static $pb.PbList<db_device_register_info_list> createRepeated() => $pb.PbList<db_device_register_info_list>();
  @$core.pragma('dart2js:noInline')
  static db_device_register_info_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_register_info_list>(create);
  static db_device_register_info_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_device_register_info> get rows => $_getList(0);
}

/// 通话调度/切换信道历史表,按月分表
class db_call_dispatch_history_list extends $pb.GeneratedMessage {
  factory db_call_dispatch_history_list({
    $core.Iterable<$1.db_call_dispatch_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_call_dispatch_history_list._() : super();
  factory db_call_dispatch_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_call_dispatch_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_call_dispatch_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_call_dispatch_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_call_dispatch_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_call_dispatch_history_list clone() => db_call_dispatch_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_call_dispatch_history_list copyWith(void Function(db_call_dispatch_history_list) updates) => super.copyWith((message) => updates(message as db_call_dispatch_history_list)) as db_call_dispatch_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_call_dispatch_history_list create() => db_call_dispatch_history_list._();
  db_call_dispatch_history_list createEmptyInstance() => create();
  static $pb.PbList<db_call_dispatch_history_list> createRepeated() => $pb.PbList<db_call_dispatch_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_call_dispatch_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_call_dispatch_history_list>(create);
  static db_call_dispatch_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_call_dispatch_history> get rows => $_getList(0);
}

/// 基站调度历史
class db_conf_dispatch_history_list extends $pb.GeneratedMessage {
  factory db_conf_dispatch_history_list({
    $core.Iterable<$1.db_conf_dispatch_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_conf_dispatch_history_list._() : super();
  factory db_conf_dispatch_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_conf_dispatch_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_conf_dispatch_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_conf_dispatch_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_conf_dispatch_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_conf_dispatch_history_list clone() => db_conf_dispatch_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_conf_dispatch_history_list copyWith(void Function(db_conf_dispatch_history_list) updates) => super.copyWith((message) => updates(message as db_conf_dispatch_history_list)) as db_conf_dispatch_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_conf_dispatch_history_list create() => db_conf_dispatch_history_list._();
  db_conf_dispatch_history_list createEmptyInstance() => create();
  static $pb.PbList<db_conf_dispatch_history_list> createRepeated() => $pb.PbList<db_conf_dispatch_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_conf_dispatch_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_conf_dispatch_history_list>(create);
  static db_conf_dispatch_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_conf_dispatch_history> get rows => $_getList(0);
}

/// 未确认短信表
class db_not_confirm_sms_list extends $pb.GeneratedMessage {
  factory db_not_confirm_sms_list({
    $core.Iterable<$1.db_not_confirm_sms>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_not_confirm_sms_list._() : super();
  factory db_not_confirm_sms_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_not_confirm_sms_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_not_confirm_sms_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_not_confirm_sms>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_not_confirm_sms.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_not_confirm_sms_list clone() => db_not_confirm_sms_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_not_confirm_sms_list copyWith(void Function(db_not_confirm_sms_list) updates) => super.copyWith((message) => updates(message as db_not_confirm_sms_list)) as db_not_confirm_sms_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_not_confirm_sms_list create() => db_not_confirm_sms_list._();
  db_not_confirm_sms_list createEmptyInstance() => create();
  static $pb.PbList<db_not_confirm_sms_list> createRepeated() => $pb.PbList<db_not_confirm_sms_list>();
  @$core.pragma('dart2js:noInline')
  static db_not_confirm_sms_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_not_confirm_sms_list>(create);
  static db_not_confirm_sms_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_not_confirm_sms> get rows => $_getList(0);
}

/// 短信历史表,短信一般很少,不分表处理了
class db_sms_history_list extends $pb.GeneratedMessage {
  factory db_sms_history_list({
    $core.Iterable<$1.db_sms_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_sms_history_list._() : super();
  factory db_sms_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_sms_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_sms_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_sms_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_sms_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_sms_history_list clone() => db_sms_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_sms_history_list copyWith(void Function(db_sms_history_list) updates) => super.copyWith((message) => updates(message as db_sms_history_list)) as db_sms_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_sms_history_list create() => db_sms_history_list._();
  db_sms_history_list createEmptyInstance() => create();
  static $pb.PbList<db_sms_history_list> createRepeated() => $pb.PbList<db_sms_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_sms_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_sms_history_list>(create);
  static db_sms_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_sms_history> get rows => $_getList(0);
}

/// 频道物理数据// todo 此处信息还需要商议下才能确定
class db_ch_rf_setting_list extends $pb.GeneratedMessage {
  factory db_ch_rf_setting_list({
    $core.Iterable<$1.db_ch_rf_setting>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_ch_rf_setting_list._() : super();
  factory db_ch_rf_setting_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_ch_rf_setting_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_ch_rf_setting_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_ch_rf_setting>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_ch_rf_setting.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_ch_rf_setting_list clone() => db_ch_rf_setting_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_ch_rf_setting_list copyWith(void Function(db_ch_rf_setting_list) updates) => super.copyWith((message) => updates(message as db_ch_rf_setting_list)) as db_ch_rf_setting_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_ch_rf_setting_list create() => db_ch_rf_setting_list._();
  db_ch_rf_setting_list createEmptyInstance() => create();
  static $pb.PbList<db_ch_rf_setting_list> createRepeated() => $pb.PbList<db_ch_rf_setting_list>();
  @$core.pragma('dart2js:noInline')
  static db_ch_rf_setting_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_ch_rf_setting_list>(create);
  static db_ch_rf_setting_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_ch_rf_setting> get rows => $_getList(0);
}

/// 写频配置文件
class db_device_setting_conf_list extends $pb.GeneratedMessage {
  factory db_device_setting_conf_list({
    $core.Iterable<$1.db_device_setting_conf>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_device_setting_conf_list._() : super();
  factory db_device_setting_conf_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_setting_conf_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_setting_conf_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_device_setting_conf>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_device_setting_conf.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_setting_conf_list clone() => db_device_setting_conf_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_setting_conf_list copyWith(void Function(db_device_setting_conf_list) updates) => super.copyWith((message) => updates(message as db_device_setting_conf_list)) as db_device_setting_conf_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_setting_conf_list create() => db_device_setting_conf_list._();
  db_device_setting_conf_list createEmptyInstance() => create();
  static $pb.PbList<db_device_setting_conf_list> createRepeated() => $pb.PbList<db_device_setting_conf_list>();
  @$core.pragma('dart2js:noInline')
  static db_device_setting_conf_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_setting_conf_list>(create);
  static db_device_setting_conf_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_device_setting_conf> get rows => $_getList(0);
}

/// 电话网关短号
class db_phone_short_no_list extends $pb.GeneratedMessage {
  factory db_phone_short_no_list({
    $core.Iterable<$1.db_phone_short_no>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_phone_short_no_list._() : super();
  factory db_phone_short_no_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_phone_short_no_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_phone_short_no_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_phone_short_no>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_phone_short_no.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_phone_short_no_list clone() => db_phone_short_no_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_phone_short_no_list copyWith(void Function(db_phone_short_no_list) updates) => super.copyWith((message) => updates(message as db_phone_short_no_list)) as db_phone_short_no_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_phone_short_no_list create() => db_phone_short_no_list._();
  db_phone_short_no_list createEmptyInstance() => create();
  static $pb.PbList<db_phone_short_no_list> createRepeated() => $pb.PbList<db_phone_short_no_list>();
  @$core.pragma('dart2js:noInline')
  static db_phone_short_no_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_phone_short_no_list>(create);
  static db_phone_short_no_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_phone_short_no> get rows => $_getList(0);
}

/// 电话网关使用授权
class db_phone_gateway_permission_list extends $pb.GeneratedMessage {
  factory db_phone_gateway_permission_list({
    $core.Iterable<$1.db_phone_gateway_permission>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_phone_gateway_permission_list._() : super();
  factory db_phone_gateway_permission_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_phone_gateway_permission_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_phone_gateway_permission_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_phone_gateway_permission>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_phone_gateway_permission.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_phone_gateway_permission_list clone() => db_phone_gateway_permission_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_phone_gateway_permission_list copyWith(void Function(db_phone_gateway_permission_list) updates) => super.copyWith((message) => updates(message as db_phone_gateway_permission_list)) as db_phone_gateway_permission_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_phone_gateway_permission_list create() => db_phone_gateway_permission_list._();
  db_phone_gateway_permission_list createEmptyInstance() => create();
  static $pb.PbList<db_phone_gateway_permission_list> createRepeated() => $pb.PbList<db_phone_gateway_permission_list>();
  @$core.pragma('dart2js:noInline')
  static db_phone_gateway_permission_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_phone_gateway_permission_list>(create);
  static db_phone_gateway_permission_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_phone_gateway_permission> get rows => $_getList(0);
}

/// 电话网关设备关系管理
class db_controller_gateway_manage_list extends $pb.GeneratedMessage {
  factory db_controller_gateway_manage_list({
    $core.Iterable<$1.db_controller_gateway_manage>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_controller_gateway_manage_list._() : super();
  factory db_controller_gateway_manage_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_controller_gateway_manage_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_controller_gateway_manage_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_controller_gateway_manage>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_controller_gateway_manage.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_controller_gateway_manage_list clone() => db_controller_gateway_manage_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_controller_gateway_manage_list copyWith(void Function(db_controller_gateway_manage_list) updates) => super.copyWith((message) => updates(message as db_controller_gateway_manage_list)) as db_controller_gateway_manage_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_controller_gateway_manage_list create() => db_controller_gateway_manage_list._();
  db_controller_gateway_manage_list createEmptyInstance() => create();
  static $pb.PbList<db_controller_gateway_manage_list> createRepeated() => $pb.PbList<db_controller_gateway_manage_list>();
  @$core.pragma('dart2js:noInline')
  static db_controller_gateway_manage_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_controller_gateway_manage_list>(create);
  static db_controller_gateway_manage_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_controller_gateway_manage> get rows => $_getList(0);
}

/// 预定义电话号码本
class db_phone_no_list_list extends $pb.GeneratedMessage {
  factory db_phone_no_list_list({
    $core.Iterable<$1.db_phone_no_list>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_phone_no_list_list._() : super();
  factory db_phone_no_list_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_phone_no_list_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_phone_no_list_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_phone_no_list>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_phone_no_list.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_phone_no_list_list clone() => db_phone_no_list_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_phone_no_list_list copyWith(void Function(db_phone_no_list_list) updates) => super.copyWith((message) => updates(message as db_phone_no_list_list)) as db_phone_no_list_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_phone_no_list_list create() => db_phone_no_list_list._();
  db_phone_no_list_list createEmptyInstance() => create();
  static $pb.PbList<db_phone_no_list_list> createRepeated() => $pb.PbList<db_phone_no_list_list>();
  @$core.pragma('dart2js:noInline')
  static db_phone_no_list_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_phone_no_list_list>(create);
  static db_phone_no_list_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_phone_no_list> get rows => $_getList(0);
}

/// 有源点报警历史
class db_linepoint_alarm_history_list extends $pb.GeneratedMessage {
  factory db_linepoint_alarm_history_list({
    $core.Iterable<$1.db_linepoint_alarm_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_linepoint_alarm_history_list._() : super();
  factory db_linepoint_alarm_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_linepoint_alarm_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_linepoint_alarm_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_linepoint_alarm_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_linepoint_alarm_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_linepoint_alarm_history_list clone() => db_linepoint_alarm_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_linepoint_alarm_history_list copyWith(void Function(db_linepoint_alarm_history_list) updates) => super.copyWith((message) => updates(message as db_linepoint_alarm_history_list)) as db_linepoint_alarm_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_linepoint_alarm_history_list create() => db_linepoint_alarm_history_list._();
  db_linepoint_alarm_history_list createEmptyInstance() => create();
  static $pb.PbList<db_linepoint_alarm_history_list> createRepeated() => $pb.PbList<db_linepoint_alarm_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_linepoint_alarm_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_linepoint_alarm_history_list>(create);
  static db_linepoint_alarm_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_linepoint_alarm_history> get rows => $_getList(0);
}

class db_device_channel_zone_list extends $pb.GeneratedMessage {
  factory db_device_channel_zone_list({
    $core.Iterable<$1.db_device_channel_zone>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_device_channel_zone_list._() : super();
  factory db_device_channel_zone_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_device_channel_zone_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_device_channel_zone_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_device_channel_zone>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_device_channel_zone.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_device_channel_zone_list clone() => db_device_channel_zone_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_device_channel_zone_list copyWith(void Function(db_device_channel_zone_list) updates) => super.copyWith((message) => updates(message as db_device_channel_zone_list)) as db_device_channel_zone_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_device_channel_zone_list create() => db_device_channel_zone_list._();
  db_device_channel_zone_list createEmptyInstance() => create();
  static $pb.PbList<db_device_channel_zone_list> createRepeated() => $pb.PbList<db_device_channel_zone_list>();
  @$core.pragma('dart2js:noInline')
  static db_device_channel_zone_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_device_channel_zone_list>(create);
  static db_device_channel_zone_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_device_channel_zone> get rows => $_getList(0);
}

/// 用户crud log表
class db_crud_log_list extends $pb.GeneratedMessage {
  factory db_crud_log_list({
    $core.Iterable<$1.db_crud_log>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_crud_log_list._() : super();
  factory db_crud_log_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_crud_log_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_crud_log_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_crud_log>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_crud_log.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_crud_log_list clone() => db_crud_log_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_crud_log_list copyWith(void Function(db_crud_log_list) updates) => super.copyWith((message) => updates(message as db_crud_log_list)) as db_crud_log_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_crud_log_list create() => db_crud_log_list._();
  db_crud_log_list createEmptyInstance() => create();
  static $pb.PbList<db_crud_log_list> createRepeated() => $pb.PbList<db_crud_log_list>();
  @$core.pragma('dart2js:noInline')
  static db_crud_log_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_crud_log_list>(create);
  static db_crud_log_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_crud_log> get rows => $_getList(0);
}

/// 动态组成员详细信息
class db_dynamic_group_detail_list extends $pb.GeneratedMessage {
  factory db_dynamic_group_detail_list({
    $core.Iterable<$1.db_dynamic_group_detail>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_dynamic_group_detail_list._() : super();
  factory db_dynamic_group_detail_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_dynamic_group_detail_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_dynamic_group_detail_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_dynamic_group_detail>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_dynamic_group_detail.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_dynamic_group_detail_list clone() => db_dynamic_group_detail_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_dynamic_group_detail_list copyWith(void Function(db_dynamic_group_detail_list) updates) => super.copyWith((message) => updates(message as db_dynamic_group_detail_list)) as db_dynamic_group_detail_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_dynamic_group_detail_list create() => db_dynamic_group_detail_list._();
  db_dynamic_group_detail_list createEmptyInstance() => create();
  static $pb.PbList<db_dynamic_group_detail_list> createRepeated() => $pb.PbList<db_dynamic_group_detail_list>();
  @$core.pragma('dart2js:noInline')
  static db_dynamic_group_detail_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_dynamic_group_detail_list>(create);
  static db_dynamic_group_detail_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_dynamic_group_detail> get rows => $_getList(0);
}

/// 物联网终端
class db_iot_device_list extends $pb.GeneratedMessage {
  factory db_iot_device_list({
    $core.Iterable<$1.db_iot_device>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_iot_device_list._() : super();
  factory db_iot_device_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_iot_device_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_iot_device_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_iot_device>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_iot_device.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_iot_device_list clone() => db_iot_device_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_iot_device_list copyWith(void Function(db_iot_device_list) updates) => super.copyWith((message) => updates(message as db_iot_device_list)) as db_iot_device_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_iot_device_list create() => db_iot_device_list._();
  db_iot_device_list createEmptyInstance() => create();
  static $pb.PbList<db_iot_device_list> createRepeated() => $pb.PbList<db_iot_device_list>();
  @$core.pragma('dart2js:noInline')
  static db_iot_device_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_iot_device_list>(create);
  static db_iot_device_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_iot_device> get rows => $_getList(0);
}

/// iot限制
class db_iot_restriction_list extends $pb.GeneratedMessage {
  factory db_iot_restriction_list({
    $core.Iterable<$1.db_iot_restriction>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_iot_restriction_list._() : super();
  factory db_iot_restriction_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_iot_restriction_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_iot_restriction_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_iot_restriction>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_iot_restriction.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_iot_restriction_list clone() => db_iot_restriction_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_iot_restriction_list copyWith(void Function(db_iot_restriction_list) updates) => super.copyWith((message) => updates(message as db_iot_restriction_list)) as db_iot_restriction_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_iot_restriction_list create() => db_iot_restriction_list._();
  db_iot_restriction_list createEmptyInstance() => create();
  static $pb.PbList<db_iot_restriction_list> createRepeated() => $pb.PbList<db_iot_restriction_list>();
  @$core.pragma('dart2js:noInline')
  static db_iot_restriction_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_iot_restriction_list>(create);
  static db_iot_restriction_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_iot_restriction> get rows => $_getList(0);
}

/// 物联终端最后的数据信息
class db_iot_device_last_info_list extends $pb.GeneratedMessage {
  factory db_iot_device_last_info_list({
    $core.Iterable<$1.db_iot_device_last_info>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_iot_device_last_info_list._() : super();
  factory db_iot_device_last_info_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_iot_device_last_info_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_iot_device_last_info_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_iot_device_last_info>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_iot_device_last_info.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_iot_device_last_info_list clone() => db_iot_device_last_info_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_iot_device_last_info_list copyWith(void Function(db_iot_device_last_info_list) updates) => super.copyWith((message) => updates(message as db_iot_device_last_info_list)) as db_iot_device_last_info_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_iot_device_last_info_list create() => db_iot_device_last_info_list._();
  db_iot_device_last_info_list createEmptyInstance() => create();
  static $pb.PbList<db_iot_device_last_info_list> createRepeated() => $pb.PbList<db_iot_device_last_info_list>();
  @$core.pragma('dart2js:noInline')
  static db_iot_device_last_info_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_iot_device_last_info_list>(create);
  static db_iot_device_last_info_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_iot_device_last_info> get rows => $_getList(0);
}

/// iot_data历史表,按月分表
class db_iot_data_history_list extends $pb.GeneratedMessage {
  factory db_iot_data_history_list({
    $core.Iterable<$1.db_iot_data_history>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_iot_data_history_list._() : super();
  factory db_iot_data_history_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_iot_data_history_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_iot_data_history_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_iot_data_history>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_iot_data_history.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_iot_data_history_list clone() => db_iot_data_history_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_iot_data_history_list copyWith(void Function(db_iot_data_history_list) updates) => super.copyWith((message) => updates(message as db_iot_data_history_list)) as db_iot_data_history_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_iot_data_history_list create() => db_iot_data_history_list._();
  db_iot_data_history_list createEmptyInstance() => create();
  static $pb.PbList<db_iot_data_history_list> createRepeated() => $pb.PbList<db_iot_data_history_list>();
  @$core.pragma('dart2js:noInline')
  static db_iot_data_history_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_iot_data_history_list>(create);
  static db_iot_data_history_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_iot_data_history> get rows => $_getList(0);
}

/// 设备固定订阅/静态收听表
class db_static_subscribes_list extends $pb.GeneratedMessage {
  factory db_static_subscribes_list({
    $core.Iterable<$1.db_static_subscribes>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_static_subscribes_list._() : super();
  factory db_static_subscribes_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_static_subscribes_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_static_subscribes_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_static_subscribes>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_static_subscribes.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_static_subscribes_list clone() => db_static_subscribes_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_static_subscribes_list copyWith(void Function(db_static_subscribes_list) updates) => super.copyWith((message) => updates(message as db_static_subscribes_list)) as db_static_subscribes_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_static_subscribes_list create() => db_static_subscribes_list._();
  db_static_subscribes_list createEmptyInstance() => create();
  static $pb.PbList<db_static_subscribes_list> createRepeated() => $pb.PbList<db_static_subscribes_list>();
  @$core.pragma('dart2js:noInline')
  static db_static_subscribes_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_static_subscribes_list>(create);
  static db_static_subscribes_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_static_subscribes> get rows => $_getList(0);
}

/// app用户地图显示中特别许可的其它终端列表
class db_app_map_privilege_device_list extends $pb.GeneratedMessage {
  factory db_app_map_privilege_device_list({
    $core.Iterable<$1.db_app_map_privilege_device>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_app_map_privilege_device_list._() : super();
  factory db_app_map_privilege_device_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_app_map_privilege_device_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_app_map_privilege_device_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_app_map_privilege_device>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_app_map_privilege_device.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_app_map_privilege_device_list clone() => db_app_map_privilege_device_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_app_map_privilege_device_list copyWith(void Function(db_app_map_privilege_device_list) updates) => super.copyWith((message) => updates(message as db_app_map_privilege_device_list)) as db_app_map_privilege_device_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_app_map_privilege_device_list create() => db_app_map_privilege_device_list._();
  db_app_map_privilege_device_list createEmptyInstance() => create();
  static $pb.PbList<db_app_map_privilege_device_list> createRepeated() => $pb.PbList<db_app_map_privilege_device_list>();
  @$core.pragma('dart2js:noInline')
  static db_app_map_privilege_device_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_app_map_privilege_device_list>(create);
  static db_app_map_privilege_device_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_app_map_privilege_device> get rows => $_getList(0);
}

/// poc session id表
class db_poc_session_list extends $pb.GeneratedMessage {
  factory db_poc_session_list({
    $core.Iterable<$1.db_poc_session>? rows,
  }) {
    final $result = create();
    if (rows != null) {
      $result.rows.addAll(rows);
    }
    return $result;
  }
  db_poc_session_list._() : super();
  factory db_poc_session_list.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory db_poc_session_list.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'db_poc_session_list', package: const $pb.PackageName(_omitMessageNames ? '' : 'bfdx_proto'), createEmptyInstance: create)
    ..pc<$1.db_poc_session>(1, _omitFieldNames ? '' : 'rows', $pb.PbFieldType.PM, subBuilder: $1.db_poc_session.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  db_poc_session_list clone() => db_poc_session_list()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  db_poc_session_list copyWith(void Function(db_poc_session_list) updates) => super.copyWith((message) => updates(message as db_poc_session_list)) as db_poc_session_list;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static db_poc_session_list create() => db_poc_session_list._();
  db_poc_session_list createEmptyInstance() => create();
  static $pb.PbList<db_poc_session_list> createRepeated() => $pb.PbList<db_poc_session_list>();
  @$core.pragma('dart2js:noInline')
  static db_poc_session_list getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<db_poc_session_list>(create);
  static db_poc_session_list? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$1.db_poc_session> get rows => $_getList(0);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
