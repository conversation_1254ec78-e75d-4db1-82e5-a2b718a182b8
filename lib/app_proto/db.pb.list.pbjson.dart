//
//  Generated code. Do not modify.
//  source: db.pb.list.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use db_sys_config_listDescriptor instead')
const db_sys_config_list$json = {
  '1': 'db_sys_config_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_sys_config', '10': 'rows'},
  ],
};

/// Descriptor for `db_sys_config_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_sys_config_listDescriptor = $convert.base64Decode(
    'ChJkYl9zeXNfY29uZmlnX2xpc3QSLQoEcm93cxgBIAMoCzIZLmJmZHhfcHJvdG8uZGJfc3lzX2'
    'NvbmZpZ1IEcm93cw==');

@$core.Deprecated('Use db_table_operate_time_listDescriptor instead')
const db_table_operate_time_list$json = {
  '1': 'db_table_operate_time_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_table_operate_time', '10': 'rows'},
  ],
};

/// Descriptor for `db_table_operate_time_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_table_operate_time_listDescriptor = $convert.base64Decode(
    'ChpkYl90YWJsZV9vcGVyYXRlX3RpbWVfbGlzdBI1CgRyb3dzGAEgAygLMiEuYmZkeF9wcm90by'
    '5kYl90YWJsZV9vcGVyYXRlX3RpbWVSBHJvd3M=');

@$core.Deprecated('Use db_org_listDescriptor instead')
const db_org_list$json = {
  '1': 'db_org_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_org', '10': 'rows'},
  ],
};

/// Descriptor for `db_org_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_org_listDescriptor = $convert.base64Decode(
    'CgtkYl9vcmdfbGlzdBImCgRyb3dzGAEgAygLMhIuYmZkeF9wcm90by5kYl9vcmdSBHJvd3M=');

@$core.Deprecated('Use db_image_listDescriptor instead')
const db_image_list$json = {
  '1': 'db_image_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_image', '10': 'rows'},
  ],
};

/// Descriptor for `db_image_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_image_listDescriptor = $convert.base64Decode(
    'Cg1kYl9pbWFnZV9saXN0EigKBHJvd3MYASADKAsyFC5iZmR4X3Byb3RvLmRiX2ltYWdlUgRyb3'
    'dz');

@$core.Deprecated('Use db_base_station_listDescriptor instead')
const db_base_station_list$json = {
  '1': 'db_base_station_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_base_station', '10': 'rows'},
  ],
};

/// Descriptor for `db_base_station_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_base_station_listDescriptor = $convert.base64Decode(
    'ChRkYl9iYXNlX3N0YXRpb25fbGlzdBIvCgRyb3dzGAEgAygLMhsuYmZkeF9wcm90by5kYl9iYX'
    'NlX3N0YXRpb25SBHJvd3M=');

@$core.Deprecated('Use db_controller_listDescriptor instead')
const db_controller_list$json = {
  '1': 'db_controller_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_controller', '10': 'rows'},
  ],
};

/// Descriptor for `db_controller_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_controller_listDescriptor = $convert.base64Decode(
    'ChJkYl9jb250cm9sbGVyX2xpc3QSLQoEcm93cxgBIAMoCzIZLmJmZHhfcHJvdG8uZGJfY29udH'
    'JvbGxlclIEcm93cw==');

@$core.Deprecated('Use db_controller_last_info_listDescriptor instead')
const db_controller_last_info_list$json = {
  '1': 'db_controller_last_info_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_controller_last_info', '10': 'rows'},
  ],
};

/// Descriptor for `db_controller_last_info_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_controller_last_info_listDescriptor = $convert.base64Decode(
    'ChxkYl9jb250cm9sbGVyX2xhc3RfaW5mb19saXN0EjcKBHJvd3MYASADKAsyIy5iZmR4X3Byb3'
    'RvLmRiX2NvbnRyb2xsZXJfbGFzdF9pbmZvUgRyb3dz');

@$core.Deprecated('Use db_controller_online_history_listDescriptor instead')
const db_controller_online_history_list$json = {
  '1': 'db_controller_online_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_controller_online_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_controller_online_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_controller_online_history_listDescriptor = $convert.base64Decode(
    'CiFkYl9jb250cm9sbGVyX29ubGluZV9oaXN0b3J5X2xpc3QSPAoEcm93cxgBIAMoCzIoLmJmZH'
    'hfcHJvdG8uZGJfY29udHJvbGxlcl9vbmxpbmVfaGlzdG9yeVIEcm93cw==');

@$core.Deprecated('Use db_phone_gateway_filter_listDescriptor instead')
const db_phone_gateway_filter_list$json = {
  '1': 'db_phone_gateway_filter_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_phone_gateway_filter', '10': 'rows'},
  ],
};

/// Descriptor for `db_phone_gateway_filter_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_phone_gateway_filter_listDescriptor = $convert.base64Decode(
    'ChxkYl9waG9uZV9nYXRld2F5X2ZpbHRlcl9saXN0EjcKBHJvd3MYASADKAsyIy5iZmR4X3Byb3'
    'RvLmRiX3Bob25lX2dhdGV3YXlfZmlsdGVyUgRyb3dz');

@$core.Deprecated('Use db_device_listDescriptor instead')
const db_device_list$json = {
  '1': 'db_device_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_device', '10': 'rows'},
  ],
};

/// Descriptor for `db_device_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_listDescriptor = $convert.base64Decode(
    'Cg5kYl9kZXZpY2VfbGlzdBIpCgRyb3dzGAEgAygLMhUuYmZkeF9wcm90by5kYl9kZXZpY2VSBH'
    'Jvd3M=');

@$core.Deprecated('Use db_device_last_info_listDescriptor instead')
const db_device_last_info_list$json = {
  '1': 'db_device_last_info_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_device_last_info', '10': 'rows'},
  ],
};

/// Descriptor for `db_device_last_info_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_last_info_listDescriptor = $convert.base64Decode(
    'ChhkYl9kZXZpY2VfbGFzdF9pbmZvX2xpc3QSMwoEcm93cxgBIAMoCzIfLmJmZHhfcHJvdG8uZG'
    'JfZGV2aWNlX2xhc3RfaW5mb1IEcm93cw==');

@$core.Deprecated('Use db_user_title_listDescriptor instead')
const db_user_title_list$json = {
  '1': 'db_user_title_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_user_title', '10': 'rows'},
  ],
};

/// Descriptor for `db_user_title_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_title_listDescriptor = $convert.base64Decode(
    'ChJkYl91c2VyX3RpdGxlX2xpc3QSLQoEcm93cxgBIAMoCzIZLmJmZHhfcHJvdG8uZGJfdXNlcl'
    '90aXRsZVIEcm93cw==');

@$core.Deprecated('Use db_user_listDescriptor instead')
const db_user_list$json = {
  '1': 'db_user_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_user', '10': 'rows'},
  ],
};

/// Descriptor for `db_user_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_listDescriptor = $convert.base64Decode(
    'CgxkYl91c2VyX2xpc3QSJwoEcm93cxgBIAMoCzITLmJmZHhfcHJvdG8uZGJfdXNlclIEcm93cw'
    '==');

@$core.Deprecated('Use db_user_privelege_listDescriptor instead')
const db_user_privelege_list$json = {
  '1': 'db_user_privelege_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_user_privelege', '10': 'rows'},
  ],
};

/// Descriptor for `db_user_privelege_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_privelege_listDescriptor = $convert.base64Decode(
    'ChZkYl91c2VyX3ByaXZlbGVnZV9saXN0EjEKBHJvd3MYASADKAsyHS5iZmR4X3Byb3RvLmRiX3'
    'VzZXJfcHJpdmVsZWdlUgRyb3dz');

@$core.Deprecated('Use db_user_session_id_listDescriptor instead')
const db_user_session_id_list$json = {
  '1': 'db_user_session_id_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_user_session_id', '10': 'rows'},
  ],
};

/// Descriptor for `db_user_session_id_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_session_id_listDescriptor = $convert.base64Decode(
    'ChdkYl91c2VyX3Nlc3Npb25faWRfbGlzdBIyCgRyb3dzGAEgAygLMh4uYmZkeF9wcm90by5kYl'
    '91c2VyX3Nlc3Npb25faWRSBHJvd3M=');

@$core.Deprecated('Use db_virtual_org_listDescriptor instead')
const db_virtual_org_list$json = {
  '1': 'db_virtual_org_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_virtual_org', '10': 'rows'},
  ],
};

/// Descriptor for `db_virtual_org_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_virtual_org_listDescriptor = $convert.base64Decode(
    'ChNkYl92aXJ0dWFsX29yZ19saXN0Ei4KBHJvd3MYASADKAsyGi5iZmR4X3Byb3RvLmRiX3Zpcn'
    'R1YWxfb3JnUgRyb3dz');

@$core.Deprecated('Use db_map_point_listDescriptor instead')
const db_map_point_list$json = {
  '1': 'db_map_point_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_map_point', '10': 'rows'},
  ],
};

/// Descriptor for `db_map_point_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_map_point_listDescriptor = $convert.base64Decode(
    'ChFkYl9tYXBfcG9pbnRfbGlzdBIsCgRyb3dzGAEgAygLMhguYmZkeF9wcm90by5kYl9tYXBfcG'
    '9pbnRSBHJvd3M=');

@$core.Deprecated('Use db_line_point_listDescriptor instead')
const db_line_point_list$json = {
  '1': 'db_line_point_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_line_point', '10': 'rows'},
  ],
};

/// Descriptor for `db_line_point_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_line_point_listDescriptor = $convert.base64Decode(
    'ChJkYl9saW5lX3BvaW50X2xpc3QSLQoEcm93cxgBIAMoCzIZLmJmZHhfcHJvdG8uZGJfbGluZV'
    '9wb2ludFIEcm93cw==');

@$core.Deprecated('Use db_line_point_latest_info_listDescriptor instead')
const db_line_point_latest_info_list$json = {
  '1': 'db_line_point_latest_info_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_line_point_latest_info', '10': 'rows'},
  ],
};

/// Descriptor for `db_line_point_latest_info_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_line_point_latest_info_listDescriptor = $convert.base64Decode(
    'Ch5kYl9saW5lX3BvaW50X2xhdGVzdF9pbmZvX2xpc3QSOQoEcm93cxgBIAMoCzIlLmJmZHhfcH'
    'JvdG8uZGJfbGluZV9wb2ludF9sYXRlc3RfaW5mb1IEcm93cw==');

@$core.Deprecated('Use db_line_master_listDescriptor instead')
const db_line_master_list$json = {
  '1': 'db_line_master_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_line_master', '10': 'rows'},
  ],
};

/// Descriptor for `db_line_master_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_line_master_listDescriptor = $convert.base64Decode(
    'ChNkYl9saW5lX21hc3Rlcl9saXN0Ei4KBHJvd3MYASADKAsyGi5iZmR4X3Byb3RvLmRiX2xpbm'
    'VfbWFzdGVyUgRyb3dz');

@$core.Deprecated('Use db_line_detail_listDescriptor instead')
const db_line_detail_list$json = {
  '1': 'db_line_detail_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_line_detail', '10': 'rows'},
  ],
};

/// Descriptor for `db_line_detail_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_line_detail_listDescriptor = $convert.base64Decode(
    'ChNkYl9saW5lX2RldGFpbF9saXN0Ei4KBHJvd3MYASADKAsyGi5iZmR4X3Byb3RvLmRiX2xpbm'
    'VfZGV0YWlsUgRyb3dz');

@$core.Deprecated('Use db_rfid_rule_master_listDescriptor instead')
const db_rfid_rule_master_list$json = {
  '1': 'db_rfid_rule_master_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_rfid_rule_master', '10': 'rows'},
  ],
};

/// Descriptor for `db_rfid_rule_master_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_rfid_rule_master_listDescriptor = $convert.base64Decode(
    'ChhkYl9yZmlkX3J1bGVfbWFzdGVyX2xpc3QSMwoEcm93cxgBIAMoCzIfLmJmZHhfcHJvdG8uZG'
    'JfcmZpZF9ydWxlX21hc3RlclIEcm93cw==');

@$core.Deprecated('Use db_device_power_onoff_listDescriptor instead')
const db_device_power_onoff_list$json = {
  '1': 'db_device_power_onoff_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_device_power_onoff', '10': 'rows'},
  ],
};

/// Descriptor for `db_device_power_onoff_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_power_onoff_listDescriptor = $convert.base64Decode(
    'ChpkYl9kZXZpY2VfcG93ZXJfb25vZmZfbGlzdBI1CgRyb3dzGAEgAygLMiEuYmZkeF9wcm90by'
    '5kYl9kZXZpY2VfcG93ZXJfb25vZmZSBHJvd3M=');

@$core.Deprecated('Use db_user_check_in_history_listDescriptor instead')
const db_user_check_in_history_list$json = {
  '1': 'db_user_check_in_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_user_check_in_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_user_check_in_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_check_in_history_listDescriptor = $convert.base64Decode(
    'Ch1kYl91c2VyX2NoZWNrX2luX2hpc3RvcnlfbGlzdBI4CgRyb3dzGAEgAygLMiQuYmZkeF9wcm'
    '90by5kYl91c2VyX2NoZWNrX2luX2hpc3RvcnlSBHJvd3M=');

@$core.Deprecated('Use db_rfid_history_listDescriptor instead')
const db_rfid_history_list$json = {
  '1': 'db_rfid_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_rfid_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_rfid_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_rfid_history_listDescriptor = $convert.base64Decode(
    'ChRkYl9yZmlkX2hpc3RvcnlfbGlzdBIvCgRyb3dzGAEgAygLMhsuYmZkeF9wcm90by5kYl9yZm'
    'lkX2hpc3RvcnlSBHJvd3M=');

@$core.Deprecated('Use db_gps_history_listDescriptor instead')
const db_gps_history_list$json = {
  '1': 'db_gps_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_gps_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_gps_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_gps_history_listDescriptor = $convert.base64Decode(
    'ChNkYl9ncHNfaGlzdG9yeV9saXN0Ei4KBHJvd3MYASADKAsyGi5iZmR4X3Byb3RvLmRiX2dwc1'
    '9oaXN0b3J5UgRyb3dz');

@$core.Deprecated('Use db_alarm_history_listDescriptor instead')
const db_alarm_history_list$json = {
  '1': 'db_alarm_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_alarm_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_alarm_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_alarm_history_listDescriptor = $convert.base64Decode(
    'ChVkYl9hbGFybV9oaXN0b3J5X2xpc3QSMAoEcm93cxgBIAMoCzIcLmJmZHhfcHJvdG8uZGJfYW'
    'xhcm1faGlzdG9yeVIEcm93cw==');

@$core.Deprecated('Use db_sound_history_listDescriptor instead')
const db_sound_history_list$json = {
  '1': 'db_sound_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_sound_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_sound_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_sound_history_listDescriptor = $convert.base64Decode(
    'ChVkYl9zb3VuZF9oaXN0b3J5X2xpc3QSMAoEcm93cxgBIAMoCzIcLmJmZHhfcHJvdG8uZGJfc2'
    '91bmRfaGlzdG9yeVIEcm93cw==');

@$core.Deprecated('Use db_not_send_cmd_listDescriptor instead')
const db_not_send_cmd_list$json = {
  '1': 'db_not_send_cmd_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_not_send_cmd', '10': 'rows'},
  ],
};

/// Descriptor for `db_not_send_cmd_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_not_send_cmd_listDescriptor = $convert.base64Decode(
    'ChRkYl9ub3Rfc2VuZF9jbWRfbGlzdBIvCgRyb3dzGAEgAygLMhsuYmZkeF9wcm90by5kYl9ub3'
    'Rfc2VuZF9jbWRSBHJvd3M=');

@$core.Deprecated('Use db_sent_cmd_history_listDescriptor instead')
const db_sent_cmd_history_list$json = {
  '1': 'db_sent_cmd_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_sent_cmd_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_sent_cmd_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_sent_cmd_history_listDescriptor = $convert.base64Decode(
    'ChhkYl9zZW50X2NtZF9oaXN0b3J5X2xpc3QSMwoEcm93cxgBIAMoCzIfLmJmZHhfcHJvdG8uZG'
    'Jfc2VudF9jbWRfaGlzdG9yeVIEcm93cw==');

@$core.Deprecated('Use db_device_register_info_listDescriptor instead')
const db_device_register_info_list$json = {
  '1': 'db_device_register_info_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_device_register_info', '10': 'rows'},
  ],
};

/// Descriptor for `db_device_register_info_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_register_info_listDescriptor = $convert.base64Decode(
    'ChxkYl9kZXZpY2VfcmVnaXN0ZXJfaW5mb19saXN0EjcKBHJvd3MYASADKAsyIy5iZmR4X3Byb3'
    'RvLmRiX2RldmljZV9yZWdpc3Rlcl9pbmZvUgRyb3dz');

@$core.Deprecated('Use db_call_dispatch_history_listDescriptor instead')
const db_call_dispatch_history_list$json = {
  '1': 'db_call_dispatch_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_call_dispatch_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_call_dispatch_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_call_dispatch_history_listDescriptor = $convert.base64Decode(
    'Ch1kYl9jYWxsX2Rpc3BhdGNoX2hpc3RvcnlfbGlzdBI4CgRyb3dzGAEgAygLMiQuYmZkeF9wcm'
    '90by5kYl9jYWxsX2Rpc3BhdGNoX2hpc3RvcnlSBHJvd3M=');

@$core.Deprecated('Use db_conf_dispatch_history_listDescriptor instead')
const db_conf_dispatch_history_list$json = {
  '1': 'db_conf_dispatch_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_conf_dispatch_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_conf_dispatch_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_conf_dispatch_history_listDescriptor = $convert.base64Decode(
    'Ch1kYl9jb25mX2Rpc3BhdGNoX2hpc3RvcnlfbGlzdBI4CgRyb3dzGAEgAygLMiQuYmZkeF9wcm'
    '90by5kYl9jb25mX2Rpc3BhdGNoX2hpc3RvcnlSBHJvd3M=');

@$core.Deprecated('Use db_not_confirm_sms_listDescriptor instead')
const db_not_confirm_sms_list$json = {
  '1': 'db_not_confirm_sms_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_not_confirm_sms', '10': 'rows'},
  ],
};

/// Descriptor for `db_not_confirm_sms_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_not_confirm_sms_listDescriptor = $convert.base64Decode(
    'ChdkYl9ub3RfY29uZmlybV9zbXNfbGlzdBIyCgRyb3dzGAEgAygLMh4uYmZkeF9wcm90by5kYl'
    '9ub3RfY29uZmlybV9zbXNSBHJvd3M=');

@$core.Deprecated('Use db_sms_history_listDescriptor instead')
const db_sms_history_list$json = {
  '1': 'db_sms_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_sms_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_sms_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_sms_history_listDescriptor = $convert.base64Decode(
    'ChNkYl9zbXNfaGlzdG9yeV9saXN0Ei4KBHJvd3MYASADKAsyGi5iZmR4X3Byb3RvLmRiX3Ntc1'
    '9oaXN0b3J5UgRyb3dz');

@$core.Deprecated('Use db_ch_rf_setting_listDescriptor instead')
const db_ch_rf_setting_list$json = {
  '1': 'db_ch_rf_setting_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_ch_rf_setting', '10': 'rows'},
  ],
};

/// Descriptor for `db_ch_rf_setting_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_ch_rf_setting_listDescriptor = $convert.base64Decode(
    'ChVkYl9jaF9yZl9zZXR0aW5nX2xpc3QSMAoEcm93cxgBIAMoCzIcLmJmZHhfcHJvdG8uZGJfY2'
    'hfcmZfc2V0dGluZ1IEcm93cw==');

@$core.Deprecated('Use db_device_setting_conf_listDescriptor instead')
const db_device_setting_conf_list$json = {
  '1': 'db_device_setting_conf_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_device_setting_conf', '10': 'rows'},
  ],
};

/// Descriptor for `db_device_setting_conf_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_setting_conf_listDescriptor = $convert.base64Decode(
    'ChtkYl9kZXZpY2Vfc2V0dGluZ19jb25mX2xpc3QSNgoEcm93cxgBIAMoCzIiLmJmZHhfcHJvdG'
    '8uZGJfZGV2aWNlX3NldHRpbmdfY29uZlIEcm93cw==');

@$core.Deprecated('Use db_phone_short_no_listDescriptor instead')
const db_phone_short_no_list$json = {
  '1': 'db_phone_short_no_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_phone_short_no', '10': 'rows'},
  ],
};

/// Descriptor for `db_phone_short_no_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_phone_short_no_listDescriptor = $convert.base64Decode(
    'ChZkYl9waG9uZV9zaG9ydF9ub19saXN0EjEKBHJvd3MYASADKAsyHS5iZmR4X3Byb3RvLmRiX3'
    'Bob25lX3Nob3J0X25vUgRyb3dz');

@$core.Deprecated('Use db_phone_gateway_permission_listDescriptor instead')
const db_phone_gateway_permission_list$json = {
  '1': 'db_phone_gateway_permission_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_phone_gateway_permission', '10': 'rows'},
  ],
};

/// Descriptor for `db_phone_gateway_permission_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_phone_gateway_permission_listDescriptor = $convert.base64Decode(
    'CiBkYl9waG9uZV9nYXRld2F5X3Blcm1pc3Npb25fbGlzdBI7CgRyb3dzGAEgAygLMicuYmZkeF'
    '9wcm90by5kYl9waG9uZV9nYXRld2F5X3Blcm1pc3Npb25SBHJvd3M=');

@$core.Deprecated('Use db_controller_gateway_manage_listDescriptor instead')
const db_controller_gateway_manage_list$json = {
  '1': 'db_controller_gateway_manage_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_controller_gateway_manage', '10': 'rows'},
  ],
};

/// Descriptor for `db_controller_gateway_manage_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_controller_gateway_manage_listDescriptor = $convert.base64Decode(
    'CiFkYl9jb250cm9sbGVyX2dhdGV3YXlfbWFuYWdlX2xpc3QSPAoEcm93cxgBIAMoCzIoLmJmZH'
    'hfcHJvdG8uZGJfY29udHJvbGxlcl9nYXRld2F5X21hbmFnZVIEcm93cw==');

@$core.Deprecated('Use db_phone_no_list_listDescriptor instead')
const db_phone_no_list_list$json = {
  '1': 'db_phone_no_list_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_phone_no_list', '10': 'rows'},
  ],
};

/// Descriptor for `db_phone_no_list_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_phone_no_list_listDescriptor = $convert.base64Decode(
    'ChVkYl9waG9uZV9ub19saXN0X2xpc3QSMAoEcm93cxgBIAMoCzIcLmJmZHhfcHJvdG8uZGJfcG'
    'hvbmVfbm9fbGlzdFIEcm93cw==');

@$core.Deprecated('Use db_linepoint_alarm_history_listDescriptor instead')
const db_linepoint_alarm_history_list$json = {
  '1': 'db_linepoint_alarm_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_linepoint_alarm_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_linepoint_alarm_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_linepoint_alarm_history_listDescriptor = $convert.base64Decode(
    'Ch9kYl9saW5lcG9pbnRfYWxhcm1faGlzdG9yeV9saXN0EjoKBHJvd3MYASADKAsyJi5iZmR4X3'
    'Byb3RvLmRiX2xpbmVwb2ludF9hbGFybV9oaXN0b3J5UgRyb3dz');

@$core.Deprecated('Use db_device_channel_zone_listDescriptor instead')
const db_device_channel_zone_list$json = {
  '1': 'db_device_channel_zone_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_device_channel_zone', '10': 'rows'},
  ],
};

/// Descriptor for `db_device_channel_zone_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_channel_zone_listDescriptor = $convert.base64Decode(
    'ChtkYl9kZXZpY2VfY2hhbm5lbF96b25lX2xpc3QSNgoEcm93cxgBIAMoCzIiLmJmZHhfcHJvdG'
    '8uZGJfZGV2aWNlX2NoYW5uZWxfem9uZVIEcm93cw==');

@$core.Deprecated('Use db_crud_log_listDescriptor instead')
const db_crud_log_list$json = {
  '1': 'db_crud_log_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_crud_log', '10': 'rows'},
  ],
};

/// Descriptor for `db_crud_log_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_crud_log_listDescriptor = $convert.base64Decode(
    'ChBkYl9jcnVkX2xvZ19saXN0EisKBHJvd3MYASADKAsyFy5iZmR4X3Byb3RvLmRiX2NydWRfbG'
    '9nUgRyb3dz');

@$core.Deprecated('Use db_dynamic_group_detail_listDescriptor instead')
const db_dynamic_group_detail_list$json = {
  '1': 'db_dynamic_group_detail_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_dynamic_group_detail', '10': 'rows'},
  ],
};

/// Descriptor for `db_dynamic_group_detail_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_dynamic_group_detail_listDescriptor = $convert.base64Decode(
    'ChxkYl9keW5hbWljX2dyb3VwX2RldGFpbF9saXN0EjcKBHJvd3MYASADKAsyIy5iZmR4X3Byb3'
    'RvLmRiX2R5bmFtaWNfZ3JvdXBfZGV0YWlsUgRyb3dz');

@$core.Deprecated('Use db_iot_device_listDescriptor instead')
const db_iot_device_list$json = {
  '1': 'db_iot_device_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_iot_device', '10': 'rows'},
  ],
};

/// Descriptor for `db_iot_device_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_iot_device_listDescriptor = $convert.base64Decode(
    'ChJkYl9pb3RfZGV2aWNlX2xpc3QSLQoEcm93cxgBIAMoCzIZLmJmZHhfcHJvdG8uZGJfaW90X2'
    'RldmljZVIEcm93cw==');

@$core.Deprecated('Use db_iot_restriction_listDescriptor instead')
const db_iot_restriction_list$json = {
  '1': 'db_iot_restriction_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_iot_restriction', '10': 'rows'},
  ],
};

/// Descriptor for `db_iot_restriction_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_iot_restriction_listDescriptor = $convert.base64Decode(
    'ChdkYl9pb3RfcmVzdHJpY3Rpb25fbGlzdBIyCgRyb3dzGAEgAygLMh4uYmZkeF9wcm90by5kYl'
    '9pb3RfcmVzdHJpY3Rpb25SBHJvd3M=');

@$core.Deprecated('Use db_iot_device_last_info_listDescriptor instead')
const db_iot_device_last_info_list$json = {
  '1': 'db_iot_device_last_info_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_iot_device_last_info', '10': 'rows'},
  ],
};

/// Descriptor for `db_iot_device_last_info_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_iot_device_last_info_listDescriptor = $convert.base64Decode(
    'ChxkYl9pb3RfZGV2aWNlX2xhc3RfaW5mb19saXN0EjcKBHJvd3MYASADKAsyIy5iZmR4X3Byb3'
    'RvLmRiX2lvdF9kZXZpY2VfbGFzdF9pbmZvUgRyb3dz');

@$core.Deprecated('Use db_iot_data_history_listDescriptor instead')
const db_iot_data_history_list$json = {
  '1': 'db_iot_data_history_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_iot_data_history', '10': 'rows'},
  ],
};

/// Descriptor for `db_iot_data_history_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_iot_data_history_listDescriptor = $convert.base64Decode(
    'ChhkYl9pb3RfZGF0YV9oaXN0b3J5X2xpc3QSMwoEcm93cxgBIAMoCzIfLmJmZHhfcHJvdG8uZG'
    'JfaW90X2RhdGFfaGlzdG9yeVIEcm93cw==');

@$core.Deprecated('Use db_static_subscribes_listDescriptor instead')
const db_static_subscribes_list$json = {
  '1': 'db_static_subscribes_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_static_subscribes', '10': 'rows'},
  ],
};

/// Descriptor for `db_static_subscribes_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_static_subscribes_listDescriptor = $convert.base64Decode(
    'ChlkYl9zdGF0aWNfc3Vic2NyaWJlc19saXN0EjQKBHJvd3MYASADKAsyIC5iZmR4X3Byb3RvLm'
    'RiX3N0YXRpY19zdWJzY3JpYmVzUgRyb3dz');

@$core.Deprecated('Use db_app_map_privilege_device_listDescriptor instead')
const db_app_map_privilege_device_list$json = {
  '1': 'db_app_map_privilege_device_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_app_map_privilege_device', '10': 'rows'},
  ],
};

/// Descriptor for `db_app_map_privilege_device_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_app_map_privilege_device_listDescriptor = $convert.base64Decode(
    'CiBkYl9hcHBfbWFwX3ByaXZpbGVnZV9kZXZpY2VfbGlzdBI7CgRyb3dzGAEgAygLMicuYmZkeF'
    '9wcm90by5kYl9hcHBfbWFwX3ByaXZpbGVnZV9kZXZpY2VSBHJvd3M=');

@$core.Deprecated('Use db_poc_session_listDescriptor instead')
const db_poc_session_list$json = {
  '1': 'db_poc_session_list',
  '2': [
    {'1': 'rows', '3': 1, '4': 3, '5': 11, '6': '.bfdx_proto.db_poc_session', '10': 'rows'},
  ],
};

/// Descriptor for `db_poc_session_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_poc_session_listDescriptor = $convert.base64Decode(
    'ChNkYl9wb2Nfc2Vzc2lvbl9saXN0Ei4KBHJvd3MYASADKAsyGi5iZmR4X3Byb3RvLmRiX3BvY1'
    '9zZXNzaW9uUgRyb3dz');

