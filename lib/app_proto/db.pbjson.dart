//
//  Generated code. Do not modify.
//  source: db.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use db_sys_configDescriptor instead')
const db_sys_config$json = {
  '1': 'db_sys_config',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'conf_key', '3': 3, '4': 1, '5': 9, '10': 'confKey'},
    {'1': 'conf_value', '3': 4, '4': 1, '5': 9, '10': 'confValue'},
  ],
};

/// Descriptor for `db_sys_config`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_sys_configDescriptor = $convert.base64Decode(
    'Cg1kYl9zeXNfY29uZmlnEhAKA3JpZBgBIAEoCVIDcmlkEhsKCXVwZGF0ZV9hdBgCIAEoCVIIdX'
    'BkYXRlQXQSGQoIY29uZl9rZXkYAyABKAlSB2NvbmZLZXkSHQoKY29uZl92YWx1ZRgEIAEoCVIJ'
    'Y29uZlZhbHVl');

@$core.Deprecated('Use db_table_operate_timeDescriptor instead')
const db_table_operate_time$json = {
  '1': 'db_table_operate_time',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'table_name', '3': 3, '4': 1, '5': 9, '10': 'tableName'},
    {'1': 'last_modify_time', '3': 4, '4': 1, '5': 9, '10': 'lastModifyTime'},
    {'1': 'last_modify_operation', '3': 5, '4': 1, '5': 5, '10': 'lastModifyOperation'},
    {'1': 'last_modify_rows', '3': 6, '4': 1, '5': 5, '10': 'lastModifyRows'},
  ],
};

/// Descriptor for `db_table_operate_time`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_table_operate_timeDescriptor = $convert.base64Decode(
    'ChVkYl90YWJsZV9vcGVyYXRlX3RpbWUSEAoDcmlkGAEgASgJUgNyaWQSGwoJdXBkYXRlX2F0GA'
    'IgASgJUgh1cGRhdGVBdBIdCgp0YWJsZV9uYW1lGAMgASgJUgl0YWJsZU5hbWUSKAoQbGFzdF9t'
    'b2RpZnlfdGltZRgEIAEoCVIObGFzdE1vZGlmeVRpbWUSMgoVbGFzdF9tb2RpZnlfb3BlcmF0aW'
    '9uGAUgASgFUhNsYXN0TW9kaWZ5T3BlcmF0aW9uEigKEGxhc3RfbW9kaWZ5X3Jvd3MYBiABKAVS'
    'Dmxhc3RNb2RpZnlSb3dz');

@$core.Deprecated('Use db_orgDescriptor instead')
const db_org$json = {
  '1': 'db_org',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_self_id', '3': 3, '4': 1, '5': 9, '10': 'orgSelfId'},
    {'1': 'org_sort_value', '3': 4, '4': 1, '5': 5, '10': 'orgSortValue'},
    {'1': 'org_short_name', '3': 5, '4': 1, '5': 9, '10': 'orgShortName'},
    {'1': 'org_full_name', '3': 6, '4': 1, '5': 9, '10': 'orgFullName'},
    {'1': 'note', '3': 7, '4': 1, '5': 9, '10': 'note'},
    {'1': 'org_is_virtual', '3': 8, '4': 1, '5': 5, '10': 'orgIsVirtual'},
    {'1': 'dmr_id', '3': 9, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'org_img', '3': 10, '4': 1, '5': 9, '10': 'orgImg'},
    {'1': 'parent_org_id', '3': 11, '4': 1, '5': 9, '10': 'parentOrgId'},
    {'1': 'setting', '3': 12, '4': 1, '5': 9, '10': 'setting'},
    {'1': 'dynamic_group_state', '3': 13, '4': 1, '5': 5, '10': 'dynamicGroupState'},
    {'1': 'creator', '3': 14, '4': 1, '5': 9, '10': 'creator'},
  ],
};

/// Descriptor for `db_org`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_orgDescriptor = $convert.base64Decode(
    'CgZkYl9vcmcSEAoDcmlkGAEgASgJUgNyaWQSGwoJdXBkYXRlX2F0GAIgASgJUgh1cGRhdGVBdB'
    'IeCgtvcmdfc2VsZl9pZBgDIAEoCVIJb3JnU2VsZklkEiQKDm9yZ19zb3J0X3ZhbHVlGAQgASgF'
    'UgxvcmdTb3J0VmFsdWUSJAoOb3JnX3Nob3J0X25hbWUYBSABKAlSDG9yZ1Nob3J0TmFtZRIiCg'
    '1vcmdfZnVsbF9uYW1lGAYgASgJUgtvcmdGdWxsTmFtZRISCgRub3RlGAcgASgJUgRub3RlEiQK'
    'Dm9yZ19pc192aXJ0dWFsGAggASgFUgxvcmdJc1ZpcnR1YWwSFQoGZG1yX2lkGAkgASgJUgVkbX'
    'JJZBIXCgdvcmdfaW1nGAogASgJUgZvcmdJbWcSIgoNcGFyZW50X29yZ19pZBgLIAEoCVILcGFy'
    'ZW50T3JnSWQSGAoHc2V0dGluZxgMIAEoCVIHc2V0dGluZxIuChNkeW5hbWljX2dyb3VwX3N0YX'
    'RlGA0gASgFUhFkeW5hbWljR3JvdXBTdGF0ZRIYCgdjcmVhdG9yGA4gASgJUgdjcmVhdG9y');

@$core.Deprecated('Use db_imageDescriptor instead')
const db_image$json = {
  '1': 'db_image',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'file_name', '3': 4, '4': 1, '5': 9, '10': 'fileName'},
    {'1': 'file_content', '3': 5, '4': 1, '5': 9, '10': 'fileContent'},
    {'1': 'hash', '3': 6, '4': 1, '5': 9, '10': 'hash'},
  ],
};

/// Descriptor for `db_image`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_imageDescriptor = $convert.base64Decode(
    'CghkYl9pbWFnZRIQCgNyaWQYASABKAlSA3JpZBIVCgZvcmdfaWQYAyABKAlSBW9yZ0lkEhsKCW'
    'ZpbGVfbmFtZRgEIAEoCVIIZmlsZU5hbWUSIQoMZmlsZV9jb250ZW50GAUgASgJUgtmaWxlQ29u'
    'dGVudBISCgRoYXNoGAYgASgJUgRoYXNo');

@$core.Deprecated('Use db_base_stationDescriptor instead')
const db_base_station$json = {
  '1': 'db_base_station',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'update_at', '3': 3, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'self_id', '3': 4, '4': 1, '5': 9, '10': 'selfId'},
    {'1': 'base_station_name', '3': 5, '4': 1, '5': 9, '10': 'baseStationName'},
    {'1': 'lon', '3': 6, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat', '3': 7, '4': 1, '5': 1, '10': 'lat'},
  ],
};

/// Descriptor for `db_base_station`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_base_stationDescriptor = $convert.base64Decode(
    'Cg9kYl9iYXNlX3N0YXRpb24SEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGAIgASgJUgVvcm'
    'dJZBIbCgl1cGRhdGVfYXQYAyABKAlSCHVwZGF0ZUF0EhcKB3NlbGZfaWQYBCABKAlSBnNlbGZJ'
    'ZBIqChFiYXNlX3N0YXRpb25fbmFtZRgFIAEoCVIPYmFzZVN0YXRpb25OYW1lEhAKA2xvbhgGIA'
    'EoAVIDbG9uEhAKA2xhdBgHIAEoAVIDbGF0');

@$core.Deprecated('Use db_controllerDescriptor instead')
const db_controller$json = {
  '1': 'db_controller',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'base_station_rid', '3': 4, '4': 1, '5': 9, '10': 'baseStationRid'},
    {'1': 'self_id', '3': 5, '4': 1, '5': 9, '10': 'selfId'},
    {'1': 'dmr_id', '3': 6, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'lon', '3': 7, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat', '3': 8, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'note', '3': 9, '4': 1, '5': 9, '10': 'note'},
    {'1': 'setting', '3': 10, '4': 1, '5': 9, '10': 'setting'},
    {'1': 'signal_aera', '3': 11, '4': 1, '5': 9, '10': 'signalAera'},
    {'1': 'sim_info', '3': 12, '4': 1, '5': 9, '10': 'simInfo'},
    {'1': 'room', '3': 13, '4': 1, '5': 9, '10': 'room'},
    {'1': 'sip_no', '3': 14, '4': 1, '5': 9, '10': 'sipNo'},
    {'1': 'net_time_slot', '3': 15, '4': 1, '5': 5, '10': 'netTimeSlot'},
    {'1': 'controller_type', '3': 16, '4': 1, '5': 5, '10': 'controllerType'},
    {'1': 'location', '3': 17, '4': 1, '5': 9, '10': 'location'},
    {'1': 'can_talk', '3': 18, '4': 1, '5': 8, '10': 'canTalk'},
    {'1': 'simulcast_parent', '3': 19, '4': 1, '5': 9, '10': 'simulcastParent'},
    {'1': 'traditional_dmr_allow_net_call', '3': 20, '4': 1, '5': 5, '10': 'traditionalDmrAllowNetCall'},
  ],
};

/// Descriptor for `db_controller`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_controllerDescriptor = $convert.base64Decode(
    'Cg1kYl9jb250cm9sbGVyEhAKA3JpZBgBIAEoCVIDcmlkEhsKCXVwZGF0ZV9hdBgCIAEoCVIIdX'
    'BkYXRlQXQSFQoGb3JnX2lkGAMgASgJUgVvcmdJZBIoChBiYXNlX3N0YXRpb25fcmlkGAQgASgJ'
    'Ug5iYXNlU3RhdGlvblJpZBIXCgdzZWxmX2lkGAUgASgJUgZzZWxmSWQSFQoGZG1yX2lkGAYgAS'
    'gJUgVkbXJJZBIQCgNsb24YByABKAFSA2xvbhIQCgNsYXQYCCABKAFSA2xhdBISCgRub3RlGAkg'
    'ASgJUgRub3RlEhgKB3NldHRpbmcYCiABKAlSB3NldHRpbmcSHwoLc2lnbmFsX2FlcmEYCyABKA'
    'lSCnNpZ25hbEFlcmESGQoIc2ltX2luZm8YDCABKAlSB3NpbUluZm8SEgoEcm9vbRgNIAEoCVIE'
    'cm9vbRIVCgZzaXBfbm8YDiABKAlSBXNpcE5vEiIKDW5ldF90aW1lX3Nsb3QYDyABKAVSC25ldF'
    'RpbWVTbG90EicKD2NvbnRyb2xsZXJfdHlwZRgQIAEoBVIOY29udHJvbGxlclR5cGUSGgoIbG9j'
    'YXRpb24YESABKAlSCGxvY2F0aW9uEhkKCGNhbl90YWxrGBIgASgIUgdjYW5UYWxrEikKEHNpbX'
    'VsY2FzdF9wYXJlbnQYEyABKAlSD3NpbXVsY2FzdFBhcmVudBJCCh50cmFkaXRpb25hbF9kbXJf'
    'YWxsb3dfbmV0X2NhbGwYFCABKAVSGnRyYWRpdGlvbmFsRG1yQWxsb3dOZXRDYWxs');

@$core.Deprecated('Use db_controller_last_infoDescriptor instead')
const db_controller_last_info$json = {
  '1': 'db_controller_last_info',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'last_data_time', '3': 4, '4': 1, '5': 9, '10': 'lastDataTime'},
    {'1': 'connected', '3': 5, '4': 1, '5': 5, '10': 'connected'},
  ],
};

/// Descriptor for `db_controller_last_info`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_controller_last_infoDescriptor = $convert.base64Decode(
    'ChdkYl9jb250cm9sbGVyX2xhc3RfaW5mbxIQCgNyaWQYASABKAlSA3JpZBIVCgZvcmdfaWQYAy'
    'ABKAlSBW9yZ0lkEiQKDmxhc3RfZGF0YV90aW1lGAQgASgJUgxsYXN0RGF0YVRpbWUSHAoJY29u'
    'bmVjdGVkGAUgASgFUgljb25uZWN0ZWQ=');

@$core.Deprecated('Use db_controller_online_historyDescriptor instead')
const db_controller_online_history$json = {
  '1': 'db_controller_online_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'controller_dmr_id', '3': 2, '4': 1, '5': 9, '10': 'controllerDmrId'},
    {'1': 'action_time', '3': 3, '4': 1, '5': 9, '10': 'actionTime'},
    {'1': 'action_code', '3': 4, '4': 1, '5': 5, '10': 'actionCode'},
    {'1': 'note', '3': 5, '4': 1, '5': 9, '10': 'note'},
  ],
};

/// Descriptor for `db_controller_online_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_controller_online_historyDescriptor = $convert.base64Decode(
    'ChxkYl9jb250cm9sbGVyX29ubGluZV9oaXN0b3J5EhAKA3JpZBgBIAEoCVIDcmlkEioKEWNvbn'
    'Ryb2xsZXJfZG1yX2lkGAIgASgJUg9jb250cm9sbGVyRG1ySWQSHwoLYWN0aW9uX3RpbWUYAyAB'
    'KAlSCmFjdGlvblRpbWUSHwoLYWN0aW9uX2NvZGUYBCABKAVSCmFjdGlvbkNvZGUSEgoEbm90ZR'
    'gFIAEoCVIEbm90ZQ==');

@$core.Deprecated('Use db_phone_gateway_filterDescriptor instead')
const db_phone_gateway_filter$json = {
  '1': 'db_phone_gateway_filter',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'name', '3': 3, '4': 1, '5': 9, '10': 'name'},
    {'1': 'last_modify_time', '3': 4, '4': 1, '5': 9, '10': 'lastModifyTime'},
    {'1': 'in_black', '3': 5, '4': 1, '5': 9, '10': 'inBlack'},
    {'1': 'in_black_enable', '3': 6, '4': 1, '5': 8, '10': 'inBlackEnable'},
    {'1': 'in_white', '3': 7, '4': 1, '5': 9, '10': 'inWhite'},
    {'1': 'in_white_enable', '3': 8, '4': 1, '5': 8, '10': 'inWhiteEnable'},
    {'1': 'out_black', '3': 9, '4': 1, '5': 9, '10': 'outBlack'},
    {'1': 'out_black_enable', '3': 10, '4': 1, '5': 8, '10': 'outBlackEnable'},
    {'1': 'out_white', '3': 11, '4': 1, '5': 9, '10': 'outWhite'},
    {'1': 'out_white_enable', '3': 12, '4': 1, '5': 8, '10': 'outWhiteEnable'},
    {'1': 'note', '3': 13, '4': 1, '5': 9, '10': 'note'},
    {'1': 'setting', '3': 14, '4': 1, '5': 9, '10': 'setting'},
  ],
};

/// Descriptor for `db_phone_gateway_filter`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_phone_gateway_filterDescriptor = $convert.base64Decode(
    'ChdkYl9waG9uZV9nYXRld2F5X2ZpbHRlchIQCgNyaWQYASABKAlSA3JpZBIVCgZvcmdfaWQYAi'
    'ABKAlSBW9yZ0lkEhIKBG5hbWUYAyABKAlSBG5hbWUSKAoQbGFzdF9tb2RpZnlfdGltZRgEIAEo'
    'CVIObGFzdE1vZGlmeVRpbWUSGQoIaW5fYmxhY2sYBSABKAlSB2luQmxhY2sSJgoPaW5fYmxhY2'
    'tfZW5hYmxlGAYgASgIUg1pbkJsYWNrRW5hYmxlEhkKCGluX3doaXRlGAcgASgJUgdpbldoaXRl'
    'EiYKD2luX3doaXRlX2VuYWJsZRgIIAEoCFINaW5XaGl0ZUVuYWJsZRIbCglvdXRfYmxhY2sYCS'
    'ABKAlSCG91dEJsYWNrEigKEG91dF9ibGFja19lbmFibGUYCiABKAhSDm91dEJsYWNrRW5hYmxl'
    'EhsKCW91dF93aGl0ZRgLIAEoCVIIb3V0V2hpdGUSKAoQb3V0X3doaXRlX2VuYWJsZRgMIAEoCF'
    'IOb3V0V2hpdGVFbmFibGUSEgoEbm90ZRgNIAEoCVIEbm90ZRIYCgdzZXR0aW5nGA4gASgJUgdz'
    'ZXR0aW5n');

@$core.Deprecated('Use db_deviceDescriptor instead')
const db_device$json = {
  '1': 'db_device',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'self_id', '3': 4, '4': 1, '5': 9, '10': 'selfId'},
    {'1': 'dmr_id', '3': 5, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'vir_orgs', '3': 6, '4': 1, '5': 9, '10': 'virOrgs'},
    {'1': 'device_user', '3': 7, '4': 1, '5': 9, '10': 'deviceUser'},
    {'1': 'note', '3': 8, '4': 1, '5': 9, '10': 'note'},
    {'1': 'device_type', '3': 9, '4': 1, '5': 5, '10': 'deviceType'},
    {'1': 'channel_last_modify_time', '3': 10, '4': 1, '5': 9, '10': 'channelLastModifyTime'},
    {'1': 'channel', '3': 11, '4': 1, '5': 9, '10': 'channel'},
    {'1': 'priority', '3': 12, '4': 1, '5': 5, '10': 'priority'},
    {'1': 'gateway_filter_rid', '3': 13, '4': 1, '5': 9, '10': 'gatewayFilterRid'},
    {'1': 'setting', '3': 14, '4': 1, '5': 9, '10': 'setting'},
    {'1': 'last_rf_config_time', '3': 15, '4': 1, '5': 9, '10': 'lastRfConfigTime'},
    {'1': 'last_rf_write_time', '3': 16, '4': 1, '5': 9, '10': 'lastRfWriteTime'},
    {'1': 'traditional_dmr_allow_net_call', '3': 17, '4': 1, '5': 5, '10': 'traditionalDmrAllowNetCall'},
    {'1': 'dev_group', '3': 18, '4': 1, '5': 9, '10': 'devGroup'},
    {'1': 'poc_setting', '3': 19, '4': 1, '5': 9, '10': 'pocSetting'},
    {'1': 'poc_setting_last_modify_time', '3': 20, '4': 1, '5': 9, '10': 'pocSettingLastModifyTime'},
  ],
};

/// Descriptor for `db_device`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_deviceDescriptor = $convert.base64Decode(
    'CglkYl9kZXZpY2USEAoDcmlkGAEgASgJUgNyaWQSGwoJdXBkYXRlX2F0GAIgASgJUgh1cGRhdG'
    'VBdBIVCgZvcmdfaWQYAyABKAlSBW9yZ0lkEhcKB3NlbGZfaWQYBCABKAlSBnNlbGZJZBIVCgZk'
    'bXJfaWQYBSABKAlSBWRtcklkEhkKCHZpcl9vcmdzGAYgASgJUgd2aXJPcmdzEh8KC2RldmljZV'
    '91c2VyGAcgASgJUgpkZXZpY2VVc2VyEhIKBG5vdGUYCCABKAlSBG5vdGUSHwoLZGV2aWNlX3R5'
    'cGUYCSABKAVSCmRldmljZVR5cGUSNwoYY2hhbm5lbF9sYXN0X21vZGlmeV90aW1lGAogASgJUh'
    'VjaGFubmVsTGFzdE1vZGlmeVRpbWUSGAoHY2hhbm5lbBgLIAEoCVIHY2hhbm5lbBIaCghwcmlv'
    'cml0eRgMIAEoBVIIcHJpb3JpdHkSLAoSZ2F0ZXdheV9maWx0ZXJfcmlkGA0gASgJUhBnYXRld2'
    'F5RmlsdGVyUmlkEhgKB3NldHRpbmcYDiABKAlSB3NldHRpbmcSLQoTbGFzdF9yZl9jb25maWdf'
    'dGltZRgPIAEoCVIQbGFzdFJmQ29uZmlnVGltZRIrChJsYXN0X3JmX3dyaXRlX3RpbWUYECABKA'
    'lSD2xhc3RSZldyaXRlVGltZRJCCh50cmFkaXRpb25hbF9kbXJfYWxsb3dfbmV0X2NhbGwYESAB'
    'KAVSGnRyYWRpdGlvbmFsRG1yQWxsb3dOZXRDYWxsEhsKCWRldl9ncm91cBgSIAEoCVIIZGV2R3'
    'JvdXASHwoLcG9jX3NldHRpbmcYEyABKAlSCnBvY1NldHRpbmcSPgoccG9jX3NldHRpbmdfbGFz'
    'dF9tb2RpZnlfdGltZRgUIAEoCVIYcG9jU2V0dGluZ0xhc3RNb2RpZnlUaW1l');

@$core.Deprecated('Use db_device_last_infoDescriptor instead')
const db_device_last_info$json = {
  '1': 'db_device_last_info',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'dmr_id', '3': 5, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'last_data_time', '3': 6, '4': 1, '5': 9, '10': 'lastDataTime'},
    {'1': 'last_rfid_person', '3': 7, '4': 1, '5': 9, '10': 'lastRfidPerson'},
    {'1': 'last_rfid_person_time', '3': 4, '4': 1, '5': 9, '10': 'lastRfidPersonTime'},
    {'1': 'last_rfid', '3': 8, '4': 1, '5': 9, '10': 'lastRfid'},
    {'1': 'last_rfid_time', '3': 9, '4': 1, '5': 9, '10': 'lastRfidTime'},
    {'1': 'last_gps_time', '3': 10, '4': 1, '5': 9, '10': 'lastGpsTime'},
    {'1': 'last_lon', '3': 11, '4': 1, '5': 1, '10': 'lastLon'},
    {'1': 'last_lat', '3': 12, '4': 1, '5': 1, '10': 'lastLat'},
    {'1': 'device_lock_state', '3': 13, '4': 1, '5': 5, '10': 'deviceLockState'},
    {'1': 'ms_status', '3': 14, '4': 1, '5': 9, '10': 'msStatus'},
    {'1': 'last_controller', '3': 15, '4': 1, '5': 9, '10': 'lastController'},
    {'1': 'last_poweron_time', '3': 16, '4': 1, '5': 9, '10': 'lastPoweronTime'},
    {'1': 'last_poweroff_time', '3': 17, '4': 1, '5': 9, '10': 'lastPoweroffTime'},
    {'1': 'last_gps_invalid_time', '3': 18, '4': 1, '5': 9, '10': 'lastGpsInvalidTime'},
    {'1': 'opt_status', '3': 19, '4': 1, '5': 9, '10': 'optStatus'},
    {'1': 'last_gps_switch_state', '3': 20, '4': 1, '5': 5, '10': 'lastGpsSwitchState'},
  ],
};

/// Descriptor for `db_device_last_info`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_last_infoDescriptor = $convert.base64Decode(
    'ChNkYl9kZXZpY2VfbGFzdF9pbmZvEhAKA3JpZBgBIAEoCVIDcmlkEhsKCXVwZGF0ZV9hdBgCIA'
    'EoCVIIdXBkYXRlQXQSFQoGb3JnX2lkGAMgASgJUgVvcmdJZBIVCgZkbXJfaWQYBSABKAlSBWRt'
    'cklkEiQKDmxhc3RfZGF0YV90aW1lGAYgASgJUgxsYXN0RGF0YVRpbWUSKAoQbGFzdF9yZmlkX3'
    'BlcnNvbhgHIAEoCVIObGFzdFJmaWRQZXJzb24SMQoVbGFzdF9yZmlkX3BlcnNvbl90aW1lGAQg'
    'ASgJUhJsYXN0UmZpZFBlcnNvblRpbWUSGwoJbGFzdF9yZmlkGAggASgJUghsYXN0UmZpZBIkCg'
    '5sYXN0X3JmaWRfdGltZRgJIAEoCVIMbGFzdFJmaWRUaW1lEiIKDWxhc3RfZ3BzX3RpbWUYCiAB'
    'KAlSC2xhc3RHcHNUaW1lEhkKCGxhc3RfbG9uGAsgASgBUgdsYXN0TG9uEhkKCGxhc3RfbGF0GA'
    'wgASgBUgdsYXN0TGF0EioKEWRldmljZV9sb2NrX3N0YXRlGA0gASgFUg9kZXZpY2VMb2NrU3Rh'
    'dGUSGwoJbXNfc3RhdHVzGA4gASgJUghtc1N0YXR1cxInCg9sYXN0X2NvbnRyb2xsZXIYDyABKA'
    'lSDmxhc3RDb250cm9sbGVyEioKEWxhc3RfcG93ZXJvbl90aW1lGBAgASgJUg9sYXN0UG93ZXJv'
    'blRpbWUSLAoSbGFzdF9wb3dlcm9mZl90aW1lGBEgASgJUhBsYXN0UG93ZXJvZmZUaW1lEjEKFW'
    'xhc3RfZ3BzX2ludmFsaWRfdGltZRgSIAEoCVISbGFzdEdwc0ludmFsaWRUaW1lEh0KCm9wdF9z'
    'dGF0dXMYEyABKAlSCW9wdFN0YXR1cxIxChVsYXN0X2dwc19zd2l0Y2hfc3RhdGUYFCABKAVSEm'
    'xhc3RHcHNTd2l0Y2hTdGF0ZQ==');

@$core.Deprecated('Use db_user_titleDescriptor instead')
const db_user_title$json = {
  '1': 'db_user_title',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'title_name', '3': 3, '4': 1, '5': 9, '10': 'titleName'},
    {'1': 'note', '3': 4, '4': 1, '5': 9, '10': 'note'},
    {'1': 'title_sort_value', '3': 5, '4': 1, '5': 5, '10': 'titleSortValue'},
  ],
};

/// Descriptor for `db_user_title`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_titleDescriptor = $convert.base64Decode(
    'Cg1kYl91c2VyX3RpdGxlEhAKA3JpZBgBIAEoCVIDcmlkEhsKCXVwZGF0ZV9hdBgCIAEoCVIIdX'
    'BkYXRlQXQSHQoKdGl0bGVfbmFtZRgDIAEoCVIJdGl0bGVOYW1lEhIKBG5vdGUYBCABKAlSBG5v'
    'dGUSKAoQdGl0bGVfc29ydF92YWx1ZRgFIAEoBVIOdGl0bGVTb3J0VmFsdWU=');

@$core.Deprecated('Use db_userDescriptor instead')
const db_user$json = {
  '1': 'db_user',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'self_id', '3': 4, '4': 1, '5': 9, '10': 'selfId'},
    {'1': 'user_name', '3': 5, '4': 1, '5': 9, '10': 'userName'},
    {'1': 'user_title', '3': 6, '4': 1, '5': 9, '10': 'userTitle'},
    {'1': 'user_phone', '3': 7, '4': 1, '5': 9, '10': 'userPhone'},
    {'1': 'user_image', '3': 8, '4': 1, '5': 9, '10': 'userImage'},
    {'1': 'user_rfid', '3': 9, '4': 1, '5': 9, '10': 'userRfid'},
    {'1': 'user_login_name', '3': 10, '4': 1, '5': 9, '10': 'userLoginName'},
    {'1': 'user_login_pass', '3': 11, '4': 1, '5': 9, '10': 'userLoginPass'},
    {'1': 'user_setting', '3': 12, '4': 1, '5': 9, '10': 'userSetting'},
    {'1': 'note', '3': 13, '4': 1, '5': 9, '10': 'note'},
    {'1': 'allow_login_manage', '3': 14, '4': 1, '5': 8, '10': 'allowLoginManage'},
  ],
};

/// Descriptor for `db_user`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_userDescriptor = $convert.base64Decode(
    'CgdkYl91c2VyEhAKA3JpZBgBIAEoCVIDcmlkEhsKCXVwZGF0ZV9hdBgCIAEoCVIIdXBkYXRlQX'
    'QSFQoGb3JnX2lkGAMgASgJUgVvcmdJZBIXCgdzZWxmX2lkGAQgASgJUgZzZWxmSWQSGwoJdXNl'
    'cl9uYW1lGAUgASgJUgh1c2VyTmFtZRIdCgp1c2VyX3RpdGxlGAYgASgJUgl1c2VyVGl0bGUSHQ'
    'oKdXNlcl9waG9uZRgHIAEoCVIJdXNlclBob25lEh0KCnVzZXJfaW1hZ2UYCCABKAlSCXVzZXJJ'
    'bWFnZRIbCgl1c2VyX3JmaWQYCSABKAlSCHVzZXJSZmlkEiYKD3VzZXJfbG9naW5fbmFtZRgKIA'
    'EoCVINdXNlckxvZ2luTmFtZRImCg91c2VyX2xvZ2luX3Bhc3MYCyABKAlSDXVzZXJMb2dpblBh'
    'c3MSIQoMdXNlcl9zZXR0aW5nGAwgASgJUgt1c2VyU2V0dGluZxISCgRub3RlGA0gASgJUgRub3'
    'RlEiwKEmFsbG93X2xvZ2luX21hbmFnZRgOIAEoCFIQYWxsb3dMb2dpbk1hbmFnZQ==');

@$core.Deprecated('Use db_user_privelegeDescriptor instead')
const db_user_privelege$json = {
  '1': 'db_user_privelege',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'user_rid', '3': 3, '4': 1, '5': 9, '10': 'userRid'},
    {'1': 'user_org', '3': 4, '4': 1, '5': 9, '10': 'userOrg'},
    {'1': 'include_children', '3': 5, '4': 1, '5': 5, '10': 'includeChildren'},
  ],
};

/// Descriptor for `db_user_privelege`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_privelegeDescriptor = $convert.base64Decode(
    'ChFkYl91c2VyX3ByaXZlbGVnZRIQCgNyaWQYASABKAlSA3JpZBIbCgl1cGRhdGVfYXQYAiABKA'
    'lSCHVwZGF0ZUF0EhkKCHVzZXJfcmlkGAMgASgJUgd1c2VyUmlkEhkKCHVzZXJfb3JnGAQgASgJ'
    'Ugd1c2VyT3JnEikKEGluY2x1ZGVfY2hpbGRyZW4YBSABKAVSD2luY2x1ZGVDaGlsZHJlbg==');

@$core.Deprecated('Use db_user_session_idDescriptor instead')
const db_user_session_id$json = {
  '1': 'db_user_session_id',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'user_rid', '3': 3, '4': 1, '5': 9, '10': 'userRid'},
    {'1': 'login_way', '3': 4, '4': 1, '5': 5, '10': 'loginWay'},
    {'1': 'session_id', '3': 5, '4': 1, '5': 9, '10': 'sessionId'},
    {'1': 'effective_time', '3': 6, '4': 1, '5': 9, '10': 'effectiveTime'},
  ],
};

/// Descriptor for `db_user_session_id`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_session_idDescriptor = $convert.base64Decode(
    'ChJkYl91c2VyX3Nlc3Npb25faWQSEAoDcmlkGAEgASgJUgNyaWQSGwoJdXBkYXRlX2F0GAIgAS'
    'gJUgh1cGRhdGVBdBIZCgh1c2VyX3JpZBgDIAEoCVIHdXNlclJpZBIbCglsb2dpbl93YXkYBCAB'
    'KAVSCGxvZ2luV2F5Eh0KCnNlc3Npb25faWQYBSABKAlSCXNlc3Npb25JZBIlCg5lZmZlY3Rpdm'
    'VfdGltZRgGIAEoCVINZWZmZWN0aXZlVGltZQ==');

@$core.Deprecated('Use db_virtual_orgDescriptor instead')
const db_virtual_org$json = {
  '1': 'db_virtual_org',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'virtual_user', '3': 4, '4': 1, '5': 9, '10': 'virtualUser'},
  ],
};

/// Descriptor for `db_virtual_org`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_virtual_orgDescriptor = $convert.base64Decode(
    'Cg5kYl92aXJ0dWFsX29yZxIQCgNyaWQYASABKAlSA3JpZBIbCgl1cGRhdGVfYXQYAiABKAlSCH'
    'VwZGF0ZUF0EhUKBm9yZ19pZBgDIAEoCVIFb3JnSWQSIQoMdmlydHVhbF91c2VyGAQgASgJUgt2'
    'aXJ0dWFsVXNlcg==');

@$core.Deprecated('Use db_map_pointDescriptor instead')
const db_map_point$json = {
  '1': 'db_map_point',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'self_id', '3': 4, '4': 1, '5': 9, '10': 'selfId'},
    {'1': 'point_name', '3': 5, '4': 1, '5': 9, '10': 'pointName'},
    {'1': 'map_display_name', '3': 6, '4': 1, '5': 9, '10': 'mapDisplayName'},
    {'1': 'note', '3': 7, '4': 1, '5': 9, '10': 'note'},
    {'1': 'lon', '3': 8, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat', '3': 9, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'start_show_level', '3': 10, '4': 1, '5': 5, '10': 'startShowLevel'},
    {'1': 'color_r', '3': 11, '4': 1, '5': 5, '10': 'colorR'},
    {'1': 'color_g', '3': 12, '4': 1, '5': 5, '10': 'colorG'},
    {'1': 'color_b', '3': 13, '4': 1, '5': 5, '10': 'colorB'},
    {'1': 'point_img', '3': 14, '4': 1, '5': 9, '10': 'pointImg'},
    {'1': 'img_or_color_point', '3': 15, '4': 1, '5': 5, '10': 'imgOrColorPoint'},
    {'1': 'marker_width', '3': 16, '4': 1, '5': 5, '10': 'markerWidth'},
    {'1': 'marker_height', '3': 17, '4': 1, '5': 5, '10': 'markerHeight'},
    {'1': 'marker_type', '3': 18, '4': 1, '5': 5, '10': 'markerType'},
  ],
};

/// Descriptor for `db_map_point`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_map_pointDescriptor = $convert.base64Decode(
    'CgxkYl9tYXBfcG9pbnQSEAoDcmlkGAEgASgJUgNyaWQSGwoJdXBkYXRlX2F0GAIgASgJUgh1cG'
    'RhdGVBdBIVCgZvcmdfaWQYAyABKAlSBW9yZ0lkEhcKB3NlbGZfaWQYBCABKAlSBnNlbGZJZBId'
    'Cgpwb2ludF9uYW1lGAUgASgJUglwb2ludE5hbWUSKAoQbWFwX2Rpc3BsYXlfbmFtZRgGIAEoCV'
    'IObWFwRGlzcGxheU5hbWUSEgoEbm90ZRgHIAEoCVIEbm90ZRIQCgNsb24YCCABKAFSA2xvbhIQ'
    'CgNsYXQYCSABKAFSA2xhdBIoChBzdGFydF9zaG93X2xldmVsGAogASgFUg5zdGFydFNob3dMZX'
    'ZlbBIXCgdjb2xvcl9yGAsgASgFUgZjb2xvclISFwoHY29sb3JfZxgMIAEoBVIGY29sb3JHEhcK'
    'B2NvbG9yX2IYDSABKAVSBmNvbG9yQhIbCglwb2ludF9pbWcYDiABKAlSCHBvaW50SW1nEisKEm'
    'ltZ19vcl9jb2xvcl9wb2ludBgPIAEoBVIPaW1nT3JDb2xvclBvaW50EiEKDG1hcmtlcl93aWR0'
    'aBgQIAEoBVILbWFya2VyV2lkdGgSIwoNbWFya2VyX2hlaWdodBgRIAEoBVIMbWFya2VySGVpZ2'
    'h0Eh8KC21hcmtlcl90eXBlGBIgASgFUgptYXJrZXJUeXBl');

@$core.Deprecated('Use db_line_pointDescriptor instead')
const db_line_point$json = {
  '1': 'db_line_point',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'point_id', '3': 4, '4': 1, '5': 9, '10': 'pointId'},
    {'1': 'point_name', '3': 5, '4': 1, '5': 9, '10': 'pointName'},
    {'1': 'map_display_name', '3': 6, '4': 1, '5': 9, '10': 'mapDisplayName'},
    {'1': 'note', '3': 7, '4': 1, '5': 9, '10': 'note'},
    {'1': 'lon', '3': 8, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat', '3': 9, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'start_show_level', '3': 10, '4': 1, '5': 5, '10': 'startShowLevel'},
    {'1': 'color_r', '3': 11, '4': 1, '5': 5, '10': 'colorR'},
    {'1': 'color_g', '3': 12, '4': 1, '5': 5, '10': 'colorG'},
    {'1': 'color_b', '3': 13, '4': 1, '5': 5, '10': 'colorB'},
    {'1': 'point_img', '3': 14, '4': 1, '5': 9, '10': 'pointImg'},
    {'1': 'img_or_color_point', '3': 15, '4': 1, '5': 5, '10': 'imgOrColorPoint'},
    {'1': 'point_type', '3': 16, '4': 1, '5': 5, '10': 'pointType'},
    {'1': 'point_rfid', '3': 17, '4': 1, '5': 9, '10': 'pointRfid'},
    {'1': 'gps_point_radius', '3': 18, '4': 1, '5': 5, '10': 'gpsPointRadius'},
    {'1': 'last_lpalarm_time', '3': 19, '4': 1, '5': 9, '10': 'lastLpalarmTime'},
    {'1': 'last_lpalarm_state', '3': 20, '4': 1, '5': 8, '10': 'lastLpalarmState'},
  ],
};

/// Descriptor for `db_line_point`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_line_pointDescriptor = $convert.base64Decode(
    'Cg1kYl9saW5lX3BvaW50EhAKA3JpZBgBIAEoCVIDcmlkEhsKCXVwZGF0ZV9hdBgCIAEoCVIIdX'
    'BkYXRlQXQSFQoGb3JnX2lkGAMgASgJUgVvcmdJZBIZCghwb2ludF9pZBgEIAEoCVIHcG9pbnRJ'
    'ZBIdCgpwb2ludF9uYW1lGAUgASgJUglwb2ludE5hbWUSKAoQbWFwX2Rpc3BsYXlfbmFtZRgGIA'
    'EoCVIObWFwRGlzcGxheU5hbWUSEgoEbm90ZRgHIAEoCVIEbm90ZRIQCgNsb24YCCABKAFSA2xv'
    'bhIQCgNsYXQYCSABKAFSA2xhdBIoChBzdGFydF9zaG93X2xldmVsGAogASgFUg5zdGFydFNob3'
    'dMZXZlbBIXCgdjb2xvcl9yGAsgASgFUgZjb2xvclISFwoHY29sb3JfZxgMIAEoBVIGY29sb3JH'
    'EhcKB2NvbG9yX2IYDSABKAVSBmNvbG9yQhIbCglwb2ludF9pbWcYDiABKAlSCHBvaW50SW1nEi'
    'sKEmltZ19vcl9jb2xvcl9wb2ludBgPIAEoBVIPaW1nT3JDb2xvclBvaW50Eh0KCnBvaW50X3R5'
    'cGUYECABKAVSCXBvaW50VHlwZRIdCgpwb2ludF9yZmlkGBEgASgJUglwb2ludFJmaWQSKAoQZ3'
    'BzX3BvaW50X3JhZGl1cxgSIAEoBVIOZ3BzUG9pbnRSYWRpdXMSKgoRbGFzdF9scGFsYXJtX3Rp'
    'bWUYEyABKAlSD2xhc3RMcGFsYXJtVGltZRIsChJsYXN0X2xwYWxhcm1fc3RhdGUYFCABKAhSEG'
    'xhc3RMcGFsYXJtU3RhdGU=');

@$core.Deprecated('Use db_line_point_latest_infoDescriptor instead')
const db_line_point_latest_info$json = {
  '1': 'db_line_point_latest_info',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'last_check_time', '3': 8, '4': 1, '5': 9, '10': 'lastCheckTime'},
    {'1': 'last_check_device_id', '3': 9, '4': 1, '5': 9, '10': 'lastCheckDeviceId'},
    {'1': 'last_check_user_id', '3': 11, '4': 1, '5': 9, '10': 'lastCheckUserId'},
  ],
};

/// Descriptor for `db_line_point_latest_info`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_line_point_latest_infoDescriptor = $convert.base64Decode(
    'ChlkYl9saW5lX3BvaW50X2xhdGVzdF9pbmZvEhAKA3JpZBgBIAEoCVIDcmlkEhsKCXVwZGF0ZV'
    '9hdBgCIAEoCVIIdXBkYXRlQXQSFQoGb3JnX2lkGAMgASgJUgVvcmdJZBImCg9sYXN0X2NoZWNr'
    'X3RpbWUYCCABKAlSDWxhc3RDaGVja1RpbWUSLwoUbGFzdF9jaGVja19kZXZpY2VfaWQYCSABKA'
    'lSEWxhc3RDaGVja0RldmljZUlkEisKEmxhc3RfY2hlY2tfdXNlcl9pZBgLIAEoCVIPbGFzdENo'
    'ZWNrVXNlcklk');

@$core.Deprecated('Use db_line_masterDescriptor instead')
const db_line_master$json = {
  '1': 'db_line_master',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'line_id', '3': 4, '4': 1, '5': 9, '10': 'lineId'},
    {'1': 'line_name', '3': 5, '4': 1, '5': 9, '10': 'lineName'},
    {'1': 'note', '3': 6, '4': 1, '5': 9, '10': 'note'},
    {'1': 'point_count', '3': 7, '4': 1, '5': 5, '10': 'pointCount'},
    {'1': 'line_detail_modify', '3': 8, '4': 1, '5': 5, '10': 'lineDetailModify'},
  ],
};

/// Descriptor for `db_line_master`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_line_masterDescriptor = $convert.base64Decode(
    'Cg5kYl9saW5lX21hc3RlchIQCgNyaWQYASABKAlSA3JpZBIbCgl1cGRhdGVfYXQYAiABKAlSCH'
    'VwZGF0ZUF0EhUKBm9yZ19pZBgDIAEoCVIFb3JnSWQSFwoHbGluZV9pZBgEIAEoCVIGbGluZUlk'
    'EhsKCWxpbmVfbmFtZRgFIAEoCVIIbGluZU5hbWUSEgoEbm90ZRgGIAEoCVIEbm90ZRIfCgtwb2'
    'ludF9jb3VudBgHIAEoBVIKcG9pbnRDb3VudBIsChJsaW5lX2RldGFpbF9tb2RpZnkYCCABKAVS'
    'EGxpbmVEZXRhaWxNb2RpZnk=');

@$core.Deprecated('Use db_line_detailDescriptor instead')
const db_line_detail$json = {
  '1': 'db_line_detail',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'line_id', '3': 3, '4': 1, '5': 9, '10': 'lineId'},
    {'1': 'point_id', '3': 4, '4': 1, '5': 9, '10': 'pointId'},
    {'1': 'point_no', '3': 5, '4': 1, '5': 5, '10': 'pointNo'},
    {'1': 'ahead_time', '3': 6, '4': 1, '5': 5, '10': 'aheadTime'},
    {'1': 'delay_time', '3': 7, '4': 1, '5': 5, '10': 'delayTime'},
  ],
};

/// Descriptor for `db_line_detail`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_line_detailDescriptor = $convert.base64Decode(
    'Cg5kYl9saW5lX2RldGFpbBIQCgNyaWQYASABKAlSA3JpZBIbCgl1cGRhdGVfYXQYAiABKAlSCH'
    'VwZGF0ZUF0EhcKB2xpbmVfaWQYAyABKAlSBmxpbmVJZBIZCghwb2ludF9pZBgEIAEoCVIHcG9p'
    'bnRJZBIZCghwb2ludF9ubxgFIAEoBVIHcG9pbnRObxIdCgphaGVhZF90aW1lGAYgASgFUglhaG'
    'VhZFRpbWUSHQoKZGVsYXlfdGltZRgHIAEoBVIJZGVsYXlUaW1l');

@$core.Deprecated('Use db_rfid_rule_masterDescriptor instead')
const db_rfid_rule_master$json = {
  '1': 'db_rfid_rule_master',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'rule_id', '3': 4, '4': 1, '5': 9, '10': 'ruleId'},
    {'1': 'rule_name', '3': 5, '4': 1, '5': 9, '10': 'ruleName'},
    {'1': 'note', '3': 6, '4': 1, '5': 9, '10': 'note'},
    {'1': 'day_1', '3': 7, '4': 1, '5': 8, '10': 'day1'},
    {'1': 'day_2', '3': 8, '4': 1, '5': 8, '10': 'day2'},
    {'1': 'day_3', '3': 9, '4': 1, '5': 8, '10': 'day3'},
    {'1': 'day_4', '3': 10, '4': 1, '5': 8, '10': 'day4'},
    {'1': 'day_5', '3': 11, '4': 1, '5': 8, '10': 'day5'},
    {'1': 'day_6', '3': 12, '4': 1, '5': 8, '10': 'day6'},
    {'1': 'day_7', '3': 13, '4': 1, '5': 8, '10': 'day7'},
    {'1': 'check_start_time', '3': 14, '4': 1, '5': 9, '10': 'checkStartTime'},
    {'1': 'check_all_time', '3': 15, '4': 1, '5': 5, '10': 'checkAllTime'},
    {'1': 'check_count', '3': 16, '4': 1, '5': 5, '10': 'checkCount'},
    {'1': 'rule_effective_type', '3': 17, '4': 1, '5': 5, '10': 'ruleEffectiveType'},
    {'1': 'rule_effective_start', '3': 18, '4': 1, '5': 9, '10': 'ruleEffectiveStart'},
    {'1': 'rule_effective_end', '3': 19, '4': 1, '5': 9, '10': 'ruleEffectiveEnd'},
    {'1': 'rule_line_rid', '3': 20, '4': 1, '5': 9, '10': 'ruleLineRid'},
  ],
};

/// Descriptor for `db_rfid_rule_master`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_rfid_rule_masterDescriptor = $convert.base64Decode(
    'ChNkYl9yZmlkX3J1bGVfbWFzdGVyEhAKA3JpZBgBIAEoCVIDcmlkEhsKCXVwZGF0ZV9hdBgCIA'
    'EoCVIIdXBkYXRlQXQSFQoGb3JnX2lkGAMgASgJUgVvcmdJZBIXCgdydWxlX2lkGAQgASgJUgZy'
    'dWxlSWQSGwoJcnVsZV9uYW1lGAUgASgJUghydWxlTmFtZRISCgRub3RlGAYgASgJUgRub3RlEh'
    'MKBWRheV8xGAcgASgIUgRkYXkxEhMKBWRheV8yGAggASgIUgRkYXkyEhMKBWRheV8zGAkgASgI'
    'UgRkYXkzEhMKBWRheV80GAogASgIUgRkYXk0EhMKBWRheV81GAsgASgIUgRkYXk1EhMKBWRheV'
    '82GAwgASgIUgRkYXk2EhMKBWRheV83GA0gASgIUgRkYXk3EigKEGNoZWNrX3N0YXJ0X3RpbWUY'
    'DiABKAlSDmNoZWNrU3RhcnRUaW1lEiQKDmNoZWNrX2FsbF90aW1lGA8gASgFUgxjaGVja0FsbF'
    'RpbWUSHwoLY2hlY2tfY291bnQYECABKAVSCmNoZWNrQ291bnQSLgoTcnVsZV9lZmZlY3RpdmVf'
    'dHlwZRgRIAEoBVIRcnVsZUVmZmVjdGl2ZVR5cGUSMAoUcnVsZV9lZmZlY3RpdmVfc3RhcnQYEi'
    'ABKAlSEnJ1bGVFZmZlY3RpdmVTdGFydBIsChJydWxlX2VmZmVjdGl2ZV9lbmQYEyABKAlSEHJ1'
    'bGVFZmZlY3RpdmVFbmQSIgoNcnVsZV9saW5lX3JpZBgUIAEoCVILcnVsZUxpbmVSaWQ=');

@$core.Deprecated('Use db_device_power_onoffDescriptor instead')
const db_device_power_onoff$json = {
  '1': 'db_device_power_onoff',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'device_id', '3': 4, '4': 1, '5': 9, '10': 'deviceId'},
    {'1': 'user_id', '3': 6, '4': 1, '5': 9, '10': 'userId'},
    {'1': 'action_time', '3': 8, '4': 1, '5': 9, '10': 'actionTime'},
    {'1': 'action_type', '3': 9, '4': 1, '5': 5, '10': 'actionType'},
  ],
};

/// Descriptor for `db_device_power_onoff`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_power_onoffDescriptor = $convert.base64Decode(
    'ChVkYl9kZXZpY2VfcG93ZXJfb25vZmYSEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGAMgAS'
    'gJUgVvcmdJZBIbCglkZXZpY2VfaWQYBCABKAlSCGRldmljZUlkEhcKB3VzZXJfaWQYBiABKAlS'
    'BnVzZXJJZBIfCgthY3Rpb25fdGltZRgIIAEoCVIKYWN0aW9uVGltZRIfCgthY3Rpb25fdHlwZR'
    'gJIAEoBVIKYWN0aW9uVHlwZQ==');

@$core.Deprecated('Use db_user_check_in_historyDescriptor instead')
const db_user_check_in_history$json = {
  '1': 'db_user_check_in_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'device_id', '3': 4, '4': 1, '5': 9, '10': 'deviceId'},
    {'1': 'user_id', '3': 6, '4': 1, '5': 9, '10': 'userId'},
    {'1': 'action_time', '3': 8, '4': 1, '5': 9, '10': 'actionTime'},
    {'1': 'action_type', '3': 9, '4': 1, '5': 5, '10': 'actionType'},
    {'1': 'rfid_id', '3': 10, '4': 1, '5': 9, '10': 'rfidId'},
  ],
};

/// Descriptor for `db_user_check_in_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_user_check_in_historyDescriptor = $convert.base64Decode(
    'ChhkYl91c2VyX2NoZWNrX2luX2hpc3RvcnkSEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGA'
    'MgASgJUgVvcmdJZBIbCglkZXZpY2VfaWQYBCABKAlSCGRldmljZUlkEhcKB3VzZXJfaWQYBiAB'
    'KAlSBnVzZXJJZBIfCgthY3Rpb25fdGltZRgIIAEoCVIKYWN0aW9uVGltZRIfCgthY3Rpb25fdH'
    'lwZRgJIAEoBVIKYWN0aW9uVHlwZRIXCgdyZmlkX2lkGAogASgJUgZyZmlkSWQ=');

@$core.Deprecated('Use db_rfid_historyDescriptor instead')
const db_rfid_history$json = {
  '1': 'db_rfid_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'check_time', '3': 3, '4': 1, '5': 9, '10': 'checkTime'},
    {'1': 'checker_id', '3': 4, '4': 1, '5': 9, '10': 'checkerId'},
    {'1': 'dev_type', '3': 5, '4': 1, '5': 5, '10': 'devType'},
    {'1': 'device_id', '3': 6, '4': 1, '5': 9, '10': 'deviceId'},
    {'1': 'receive_time', '3': 8, '4': 1, '5': 9, '10': 'receiveTime'},
    {'1': 'receiver', '3': 9, '4': 1, '5': 9, '10': 'receiver'},
    {'1': 'point_id', '3': 10, '4': 1, '5': 9, '10': 'pointId'},
  ],
};

/// Descriptor for `db_rfid_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_rfid_historyDescriptor = $convert.base64Decode(
    'Cg9kYl9yZmlkX2hpc3RvcnkSEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGAIgASgJUgVvcm'
    'dJZBIdCgpjaGVja190aW1lGAMgASgJUgljaGVja1RpbWUSHQoKY2hlY2tlcl9pZBgEIAEoCVIJ'
    'Y2hlY2tlcklkEhkKCGRldl90eXBlGAUgASgFUgdkZXZUeXBlEhsKCWRldmljZV9pZBgGIAEoCV'
    'IIZGV2aWNlSWQSIQoMcmVjZWl2ZV90aW1lGAggASgJUgtyZWNlaXZlVGltZRIaCghyZWNlaXZl'
    'chgJIAEoCVIIcmVjZWl2ZXISGQoIcG9pbnRfaWQYCiABKAlSB3BvaW50SWQ=');

@$core.Deprecated('Use db_gps_historyDescriptor instead')
const db_gps_history$json = {
  '1': 'db_gps_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'gps_time', '3': 3, '4': 1, '5': 9, '10': 'gpsTime'},
    {'1': 'gps_fixed', '3': 4, '4': 1, '5': 8, '10': 'gpsFixed'},
    {'1': 'lon', '3': 5, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat', '3': 6, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'speed', '3': 7, '4': 1, '5': 1, '10': 'speed'},
    {'1': 'direction', '3': 8, '4': 1, '5': 5, '10': 'direction'},
    {'1': 'altitude', '3': 9, '4': 1, '5': 5, '10': 'altitude'},
    {'1': 'device_id', '3': 10, '4': 1, '5': 9, '10': 'deviceId'},
    {'1': 'person_id', '3': 11, '4': 1, '5': 9, '10': 'personId'},
    {'1': 'device_status', '3': 12, '4': 1, '5': 9, '10': 'deviceStatus'},
    {'1': 'up_cmd', '3': 13, '4': 1, '5': 9, '10': 'upCmd'},
  ],
};

/// Descriptor for `db_gps_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_gps_historyDescriptor = $convert.base64Decode(
    'Cg5kYl9ncHNfaGlzdG9yeRIQCgNyaWQYASABKAlSA3JpZBIVCgZvcmdfaWQYAiABKAlSBW9yZ0'
    'lkEhkKCGdwc190aW1lGAMgASgJUgdncHNUaW1lEhsKCWdwc19maXhlZBgEIAEoCFIIZ3BzRml4'
    'ZWQSEAoDbG9uGAUgASgBUgNsb24SEAoDbGF0GAYgASgBUgNsYXQSFAoFc3BlZWQYByABKAFSBX'
    'NwZWVkEhwKCWRpcmVjdGlvbhgIIAEoBVIJZGlyZWN0aW9uEhoKCGFsdGl0dWRlGAkgASgFUghh'
    'bHRpdHVkZRIbCglkZXZpY2VfaWQYCiABKAlSCGRldmljZUlkEhsKCXBlcnNvbl9pZBgLIAEoCV'
    'IIcGVyc29uSWQSIwoNZGV2aWNlX3N0YXR1cxgMIAEoCVIMZGV2aWNlU3RhdHVzEhUKBnVwX2Nt'
    'ZBgNIAEoCVIFdXBDbWQ=');

@$core.Deprecated('Use db_alarm_historyDescriptor instead')
const db_alarm_history$json = {
  '1': 'db_alarm_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'alarm_time', '3': 3, '4': 1, '5': 9, '10': 'alarmTime'},
    {'1': 'device_id', '3': 4, '4': 1, '5': 9, '10': 'deviceId'},
    {'1': 'alarm_dev_type', '3': 5, '4': 1, '5': 5, '10': 'alarmDevType'},
    {'1': 'person_id', '3': 6, '4': 1, '5': 9, '10': 'personId'},
    {'1': 'dealler_rid', '3': 8, '4': 1, '5': 9, '10': 'deallerRid'},
    {'1': 'dealler_time', '3': 9, '4': 1, '5': 9, '10': 'deallerTime'},
    {'1': 'dealler_result', '3': 10, '4': 1, '5': 9, '10': 'deallerResult'},
    {'1': 'alarm_type', '3': 11, '4': 1, '5': 9, '10': 'alarmType'},
  ],
};

/// Descriptor for `db_alarm_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_alarm_historyDescriptor = $convert.base64Decode(
    'ChBkYl9hbGFybV9oaXN0b3J5EhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ19pZBgCIAEoCVIFb3'
    'JnSWQSHQoKYWxhcm1fdGltZRgDIAEoCVIJYWxhcm1UaW1lEhsKCWRldmljZV9pZBgEIAEoCVII'
    'ZGV2aWNlSWQSJAoOYWxhcm1fZGV2X3R5cGUYBSABKAVSDGFsYXJtRGV2VHlwZRIbCglwZXJzb2'
    '5faWQYBiABKAlSCHBlcnNvbklkEh8KC2RlYWxsZXJfcmlkGAggASgJUgpkZWFsbGVyUmlkEiEK'
    'DGRlYWxsZXJfdGltZRgJIAEoCVILZGVhbGxlclRpbWUSJQoOZGVhbGxlcl9yZXN1bHQYCiABKA'
    'lSDWRlYWxsZXJSZXN1bHQSHQoKYWxhcm1fdHlwZRgLIAEoCVIJYWxhcm1UeXBl');

@$core.Deprecated('Use db_sound_historyDescriptor instead')
const db_sound_history$json = {
  '1': 'db_sound_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'device_id', '3': 3, '4': 1, '5': 9, '10': 'deviceId'},
    {'1': 'source_info', '3': 4, '4': 1, '5': 9, '10': 'sourceInfo'},
    {'1': 'sound_time', '3': 5, '4': 1, '5': 9, '10': 'soundTime'},
    {'1': 'sound_len', '3': 6, '4': 1, '5': 5, '10': 'soundLen'},
    {'1': 'channel', '3': 7, '4': 1, '5': 5, '10': 'channel'},
    {'1': 'controller', '3': 8, '4': 1, '5': 9, '10': 'controller'},
    {'1': 'file_name', '3': 9, '4': 1, '5': 9, '10': 'fileName'},
    {'1': 'person_id', '3': 10, '4': 1, '5': 9, '10': 'personId'},
    {'1': 'target', '3': 11, '4': 1, '5': 9, '10': 'target'},
    {'1': 'target_info', '3': 12, '4': 1, '5': 9, '10': 'targetInfo'},
  ],
};

/// Descriptor for `db_sound_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_sound_historyDescriptor = $convert.base64Decode(
    'ChBkYl9zb3VuZF9oaXN0b3J5EhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ19pZBgCIAEoCVIFb3'
    'JnSWQSGwoJZGV2aWNlX2lkGAMgASgJUghkZXZpY2VJZBIfCgtzb3VyY2VfaW5mbxgEIAEoCVIK'
    'c291cmNlSW5mbxIdCgpzb3VuZF90aW1lGAUgASgJUglzb3VuZFRpbWUSGwoJc291bmRfbGVuGA'
    'YgASgFUghzb3VuZExlbhIYCgdjaGFubmVsGAcgASgFUgdjaGFubmVsEh4KCmNvbnRyb2xsZXIY'
    'CCABKAlSCmNvbnRyb2xsZXISGwoJZmlsZV9uYW1lGAkgASgJUghmaWxlTmFtZRIbCglwZXJzb2'
    '5faWQYCiABKAlSCHBlcnNvbklkEhYKBnRhcmdldBgLIAEoCVIGdGFyZ2V0Eh8KC3RhcmdldF9p'
    'bmZvGAwgASgJUgp0YXJnZXRJbmZv');

@$core.Deprecated('Use db_not_send_cmdDescriptor instead')
const db_not_send_cmd$json = {
  '1': 'db_not_send_cmd',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'schedule_time', '3': 6, '4': 1, '5': 9, '10': 'scheduleTime'},
    {'1': 'stop_time', '3': 7, '4': 1, '5': 9, '10': 'stopTime'},
  ],
};

/// Descriptor for `db_not_send_cmd`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_not_send_cmdDescriptor = $convert.base64Decode(
    'Cg9kYl9ub3Rfc2VuZF9jbWQSEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGAIgASgJUgVvcm'
    'dJZBIjCg1zY2hlZHVsZV90aW1lGAYgASgJUgxzY2hlZHVsZVRpbWUSGwoJc3RvcF90aW1lGAcg'
    'ASgJUghzdG9wVGltZQ==');

@$core.Deprecated('Use db_sent_cmd_historyDescriptor instead')
const db_sent_cmd_history$json = {
  '1': 'db_sent_cmd_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'input_time', '3': 3, '4': 1, '5': 9, '10': 'inputTime'},
    {'1': 'input_user_id', '3': 4, '4': 1, '5': 9, '10': 'inputUserId'},
    {'1': 'sender_type', '3': 5, '4': 1, '5': 5, '10': 'senderType'},
    {'1': 'schedule_time', '3': 6, '4': 1, '5': 9, '10': 'scheduleTime'},
    {'1': 'stop_time', '3': 7, '4': 1, '5': 9, '10': 'stopTime'},
    {'1': 'cmd_target', '3': 8, '4': 1, '5': 9, '10': 'cmdTarget'},
    {'1': 'cmd_target_con_seq', '3': 9, '4': 1, '5': 9, '10': 'cmdTargetConSeq'},
    {'1': 'send_cmd', '3': 10, '4': 1, '5': 9, '10': 'sendCmd'},
    {'1': 'orig_cbxx', '3': 11, '4': 1, '5': 9, '10': 'origCbxx'},
    {'1': 'cmd_params', '3': 12, '4': 1, '5': 9, '10': 'cmdParams'},
    {'1': 'send_time_list', '3': 13, '4': 1, '5': 9, '10': 'sendTimeList'},
  ],
};

/// Descriptor for `db_sent_cmd_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_sent_cmd_historyDescriptor = $convert.base64Decode(
    'ChNkYl9zZW50X2NtZF9oaXN0b3J5EhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ19pZBgCIAEoCV'
    'IFb3JnSWQSHQoKaW5wdXRfdGltZRgDIAEoCVIJaW5wdXRUaW1lEiIKDWlucHV0X3VzZXJfaWQY'
    'BCABKAlSC2lucHV0VXNlcklkEh8KC3NlbmRlcl90eXBlGAUgASgFUgpzZW5kZXJUeXBlEiMKDX'
    'NjaGVkdWxlX3RpbWUYBiABKAlSDHNjaGVkdWxlVGltZRIbCglzdG9wX3RpbWUYByABKAlSCHN0'
    'b3BUaW1lEh0KCmNtZF90YXJnZXQYCCABKAlSCWNtZFRhcmdldBIrChJjbWRfdGFyZ2V0X2Nvbl'
    '9zZXEYCSABKAlSD2NtZFRhcmdldENvblNlcRIZCghzZW5kX2NtZBgKIAEoCVIHc2VuZENtZBIb'
    'CglvcmlnX2NieHgYCyABKAlSCG9yaWdDYnh4Eh0KCmNtZF9wYXJhbXMYDCABKAlSCWNtZFBhcm'
    'FtcxIkCg5zZW5kX3RpbWVfbGlzdBgNIAEoCVIMc2VuZFRpbWVMaXN0');

@$core.Deprecated('Use db_device_register_infoDescriptor instead')
const db_device_register_info$json = {
  '1': 'db_device_register_info',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'receive_time', '3': 3, '4': 1, '5': 9, '10': 'receiveTime'},
    {'1': 'con_ch', '3': 4, '4': 1, '5': 9, '10': 'conCh'},
    {'1': 'dmr_id', '3': 5, '4': 1, '5': 9, '10': 'dmrId'},
    {'1': 'seller_id', '3': 6, '4': 1, '5': 9, '10': 'sellerId'},
    {'1': 'sn', '3': 7, '4': 1, '5': 9, '10': 'sn'},
  ],
};

/// Descriptor for `db_device_register_info`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_register_infoDescriptor = $convert.base64Decode(
    'ChdkYl9kZXZpY2VfcmVnaXN0ZXJfaW5mbxIQCgNyaWQYASABKAlSA3JpZBIhCgxyZWNlaXZlX3'
    'RpbWUYAyABKAlSC3JlY2VpdmVUaW1lEhUKBmNvbl9jaBgEIAEoCVIFY29uQ2gSFQoGZG1yX2lk'
    'GAUgASgJUgVkbXJJZBIbCglzZWxsZXJfaWQYBiABKAlSCHNlbGxlcklkEg4KAnNuGAcgASgJUg'
    'Jzbg==');

@$core.Deprecated('Use db_call_dispatch_historyDescriptor instead')
const db_call_dispatch_history$json = {
  '1': 'db_call_dispatch_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'action_time', '3': 3, '4': 1, '5': 9, '10': 'actionTime'},
    {'1': 'person_id', '3': 4, '4': 1, '5': 9, '10': 'personId'},
    {'1': 'device_id', '3': 6, '4': 1, '5': 9, '10': 'deviceId'},
    {'1': 'controller_dmrid', '3': 7, '4': 1, '5': 9, '10': 'controllerDmrid'},
    {'1': 'dispatch_target_dmrid', '3': 8, '4': 1, '5': 9, '10': 'dispatchTargetDmrid'},
    {'1': 'dispatch_code', '3': 10, '4': 1, '5': 5, '10': 'dispatchCode'},
    {'1': 'dispatch_type', '3': 11, '4': 1, '5': 5, '10': 'dispatchType'},
    {'1': 'target_channel', '3': 12, '4': 1, '5': 5, '10': 'targetChannel'},
  ],
};

/// Descriptor for `db_call_dispatch_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_call_dispatch_historyDescriptor = $convert.base64Decode(
    'ChhkYl9jYWxsX2Rpc3BhdGNoX2hpc3RvcnkSEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGA'
    'IgASgJUgVvcmdJZBIfCgthY3Rpb25fdGltZRgDIAEoCVIKYWN0aW9uVGltZRIbCglwZXJzb25f'
    'aWQYBCABKAlSCHBlcnNvbklkEhsKCWRldmljZV9pZBgGIAEoCVIIZGV2aWNlSWQSKQoQY29udH'
    'JvbGxlcl9kbXJpZBgHIAEoCVIPY29udHJvbGxlckRtcmlkEjIKFWRpc3BhdGNoX3RhcmdldF9k'
    'bXJpZBgIIAEoCVITZGlzcGF0Y2hUYXJnZXREbXJpZBIjCg1kaXNwYXRjaF9jb2RlGAogASgFUg'
    'xkaXNwYXRjaENvZGUSIwoNZGlzcGF0Y2hfdHlwZRgLIAEoBVIMZGlzcGF0Y2hUeXBlEiUKDnRh'
    'cmdldF9jaGFubmVsGAwgASgFUg10YXJnZXRDaGFubmVs');

@$core.Deprecated('Use db_conf_dispatch_historyDescriptor instead')
const db_conf_dispatch_history$json = {
  '1': 'db_conf_dispatch_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'start_time', '3': 3, '4': 1, '5': 9, '10': 'startTime'},
    {'1': 'start_person_rid', '3': 5, '4': 1, '5': 9, '10': 'startPersonRid'},
    {'1': 'controller_ids', '3': 6, '4': 1, '5': 9, '10': 'controllerIds'},
    {'1': 'conference_no', '3': 7, '4': 1, '5': 9, '10': 'conferenceNo'},
    {'1': 'end_org_id', '3': 8, '4': 1, '5': 9, '10': 'endOrgId'},
    {'1': 'end_time', '3': 9, '4': 1, '5': 9, '10': 'endTime'},
    {'1': 'end_person_rid', '3': 10, '4': 1, '5': 9, '10': 'endPersonRid'},
    {'1': 'dispatch_code', '3': 11, '4': 1, '5': 5, '10': 'dispatchCode'},
  ],
};

/// Descriptor for `db_conf_dispatch_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_conf_dispatch_historyDescriptor = $convert.base64Decode(
    'ChhkYl9jb25mX2Rpc3BhdGNoX2hpc3RvcnkSEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGA'
    'IgASgJUgVvcmdJZBIdCgpzdGFydF90aW1lGAMgASgJUglzdGFydFRpbWUSKAoQc3RhcnRfcGVy'
    'c29uX3JpZBgFIAEoCVIOc3RhcnRQZXJzb25SaWQSJQoOY29udHJvbGxlcl9pZHMYBiABKAlSDW'
    'NvbnRyb2xsZXJJZHMSIwoNY29uZmVyZW5jZV9ubxgHIAEoCVIMY29uZmVyZW5jZU5vEhwKCmVu'
    'ZF9vcmdfaWQYCCABKAlSCGVuZE9yZ0lkEhkKCGVuZF90aW1lGAkgASgJUgdlbmRUaW1lEiQKDm'
    'VuZF9wZXJzb25fcmlkGAogASgJUgxlbmRQZXJzb25SaWQSIwoNZGlzcGF0Y2hfY29kZRgLIAEo'
    'BVIMZGlzcGF0Y2hDb2Rl');

@$core.Deprecated('Use db_not_confirm_smsDescriptor instead')
const db_not_confirm_sms$json = {
  '1': 'db_not_confirm_sms',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'start_time', '3': 3, '4': 1, '5': 9, '10': 'startTime'},
    {'1': 'sender_dmrid', '3': 4, '4': 1, '5': 9, '10': 'senderDmrid'},
    {'1': 'target_dmrid', '3': 5, '4': 1, '5': 9, '10': 'targetDmrid'},
    {'1': 'receive_repeater', '3': 6, '4': 1, '5': 9, '10': 'receiveRepeater'},
    {'1': 'sms_content', '3': 7, '4': 1, '5': 9, '10': 'smsContent'},
    {'1': 'sms_no', '3': 8, '4': 1, '5': 9, '10': 'smsNo'},
    {'1': 'sender_user_rid', '3': 9, '4': 1, '5': 9, '10': 'senderUserRid'},
    {'1': 'note', '3': 10, '4': 1, '5': 9, '10': 'note'},
    {'1': 'sms_type', '3': 11, '4': 1, '5': 9, '10': 'smsType'},
  ],
};

/// Descriptor for `db_not_confirm_sms`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_not_confirm_smsDescriptor = $convert.base64Decode(
    'ChJkYl9ub3RfY29uZmlybV9zbXMSEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGAIgASgJUg'
    'VvcmdJZBIdCgpzdGFydF90aW1lGAMgASgJUglzdGFydFRpbWUSIQoMc2VuZGVyX2RtcmlkGAQg'
    'ASgJUgtzZW5kZXJEbXJpZBIhCgx0YXJnZXRfZG1yaWQYBSABKAlSC3RhcmdldERtcmlkEikKEH'
    'JlY2VpdmVfcmVwZWF0ZXIYBiABKAlSD3JlY2VpdmVSZXBlYXRlchIfCgtzbXNfY29udGVudBgH'
    'IAEoCVIKc21zQ29udGVudBIVCgZzbXNfbm8YCCABKAlSBXNtc05vEiYKD3NlbmRlcl91c2VyX3'
    'JpZBgJIAEoCVINc2VuZGVyVXNlclJpZBISCgRub3RlGAogASgJUgRub3RlEhkKCHNtc190eXBl'
    'GAsgASgJUgdzbXNUeXBl');

@$core.Deprecated('Use db_sms_historyDescriptor instead')
const db_sms_history$json = {
  '1': 'db_sms_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'start_time', '3': 3, '4': 1, '5': 9, '10': 'startTime'},
    {'1': 'sender_dmrid', '3': 4, '4': 1, '5': 9, '10': 'senderDmrid'},
    {'1': 'target_dmrid', '3': 5, '4': 1, '5': 9, '10': 'targetDmrid'},
    {'1': 'receive_repeater', '3': 6, '4': 1, '5': 9, '10': 'receiveRepeater'},
    {'1': 'sms_content', '3': 7, '4': 1, '5': 9, '10': 'smsContent'},
    {'1': 'sms_no', '3': 8, '4': 1, '5': 9, '10': 'smsNo'},
    {'1': 'confirm_time', '3': 9, '4': 1, '5': 9, '10': 'confirmTime'},
    {'1': 'note', '3': 10, '4': 1, '5': 9, '10': 'note'},
    {'1': 'sender_user_rid', '3': 11, '4': 1, '5': 9, '10': 'senderUserRid'},
    {'1': 'sms_type', '3': 12, '4': 1, '5': 9, '10': 'smsType'},
  ],
};

/// Descriptor for `db_sms_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_sms_historyDescriptor = $convert.base64Decode(
    'Cg5kYl9zbXNfaGlzdG9yeRIQCgNyaWQYASABKAlSA3JpZBIVCgZvcmdfaWQYAiABKAlSBW9yZ0'
    'lkEh0KCnN0YXJ0X3RpbWUYAyABKAlSCXN0YXJ0VGltZRIhCgxzZW5kZXJfZG1yaWQYBCABKAlS'
    'C3NlbmRlckRtcmlkEiEKDHRhcmdldF9kbXJpZBgFIAEoCVILdGFyZ2V0RG1yaWQSKQoQcmVjZW'
    'l2ZV9yZXBlYXRlchgGIAEoCVIPcmVjZWl2ZVJlcGVhdGVyEh8KC3Ntc19jb250ZW50GAcgASgJ'
    'UgpzbXNDb250ZW50EhUKBnNtc19ubxgIIAEoCVIFc21zTm8SIQoMY29uZmlybV90aW1lGAkgAS'
    'gJUgtjb25maXJtVGltZRISCgRub3RlGAogASgJUgRub3RlEiYKD3NlbmRlcl91c2VyX3JpZBgL'
    'IAEoCVINc2VuZGVyVXNlclJpZBIZCghzbXNfdHlwZRgMIAEoCVIHc21zVHlwZQ==');

@$core.Deprecated('Use db_ch_rf_settingDescriptor instead')
const db_ch_rf_setting$json = {
  '1': 'db_ch_rf_setting',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'name', '3': 3, '4': 1, '5': 9, '10': 'name'},
    {'1': 'rf_setting', '3': 4, '4': 1, '5': 9, '10': 'rfSetting'},
    {'1': 'settings', '3': 14, '4': 1, '5': 9, '10': 'settings'},
  ],
};

/// Descriptor for `db_ch_rf_setting`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_ch_rf_settingDescriptor = $convert.base64Decode(
    'ChBkYl9jaF9yZl9zZXR0aW5nEhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ19pZBgCIAEoCVIFb3'
    'JnSWQSEgoEbmFtZRgDIAEoCVIEbmFtZRIdCgpyZl9zZXR0aW5nGAQgASgJUglyZlNldHRpbmcS'
    'GgoIc2V0dGluZ3MYDiABKAlSCHNldHRpbmdz');

@$core.Deprecated('Use db_device_setting_confDescriptor instead')
const db_device_setting_conf$json = {
  '1': 'db_device_setting_conf',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'conf_name', '3': 3, '4': 1, '5': 9, '10': 'confName'},
    {'1': 'last_modify_time', '3': 4, '4': 1, '5': 9, '10': 'lastModifyTime'},
    {'1': 'user_name', '3': 5, '4': 1, '5': 9, '10': 'userName'},
    {'1': 'conf', '3': 14, '4': 1, '5': 9, '10': 'conf'},
  ],
};

/// Descriptor for `db_device_setting_conf`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_setting_confDescriptor = $convert.base64Decode(
    'ChZkYl9kZXZpY2Vfc2V0dGluZ19jb25mEhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ19pZBgCIA'
    'EoCVIFb3JnSWQSGwoJY29uZl9uYW1lGAMgASgJUghjb25mTmFtZRIoChBsYXN0X21vZGlmeV90'
    'aW1lGAQgASgJUg5sYXN0TW9kaWZ5VGltZRIbCgl1c2VyX25hbWUYBSABKAlSCHVzZXJOYW1lEh'
    'IKBGNvbmYYDiABKAlSBGNvbmY=');

@$core.Deprecated('Use db_phone_short_noDescriptor instead')
const db_phone_short_no$json = {
  '1': 'db_phone_short_no',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'short_no', '3': 3, '4': 1, '5': 9, '10': 'shortNo'},
    {'1': 'last_modify_time', '3': 4, '4': 1, '5': 9, '10': 'lastModifyTime'},
    {'1': 'ref_org_id', '3': 5, '4': 1, '5': 9, '10': 'refOrgId'},
    {'1': 'ref_dev_id', '3': 6, '4': 1, '5': 9, '10': 'refDevId'},
    {'1': 'note', '3': 8, '4': 1, '5': 9, '10': 'note'},
    {'1': 'setting', '3': 14, '4': 1, '5': 9, '10': 'setting'},
  ],
};

/// Descriptor for `db_phone_short_no`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_phone_short_noDescriptor = $convert.base64Decode(
    'ChFkYl9waG9uZV9zaG9ydF9ubxIQCgNyaWQYASABKAlSA3JpZBIVCgZvcmdfaWQYAiABKAlSBW'
    '9yZ0lkEhkKCHNob3J0X25vGAMgASgJUgdzaG9ydE5vEigKEGxhc3RfbW9kaWZ5X3RpbWUYBCAB'
    'KAlSDmxhc3RNb2RpZnlUaW1lEhwKCnJlZl9vcmdfaWQYBSABKAlSCHJlZk9yZ0lkEhwKCnJlZl'
    '9kZXZfaWQYBiABKAlSCHJlZkRldklkEhIKBG5vdGUYCCABKAlSBG5vdGUSGAoHc2V0dGluZxgO'
    'IAEoCVIHc2V0dGluZw==');

@$core.Deprecated('Use db_phone_gateway_permissionDescriptor instead')
const db_phone_gateway_permission$json = {
  '1': 'db_phone_gateway_permission',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'name', '3': 3, '4': 1, '5': 9, '10': 'name'},
    {'1': 'last_modify_time', '3': 4, '4': 1, '5': 9, '10': 'lastModifyTime'},
    {'1': 'perm_org_id', '3': 5, '4': 1, '5': 9, '10': 'permOrgId'},
    {'1': 'perm_dev_id', '3': 6, '4': 1, '5': 9, '10': 'permDevId'},
    {'1': 'gateway_rid', '3': 7, '4': 1, '5': 9, '10': 'gatewayRid'},
    {'1': 'note', '3': 8, '4': 1, '5': 9, '10': 'note'},
    {'1': 'setting', '3': 14, '4': 1, '5': 9, '10': 'setting'},
  ],
};

/// Descriptor for `db_phone_gateway_permission`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_phone_gateway_permissionDescriptor = $convert.base64Decode(
    'ChtkYl9waG9uZV9nYXRld2F5X3Blcm1pc3Npb24SEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2'
    'lkGAIgASgJUgVvcmdJZBISCgRuYW1lGAMgASgJUgRuYW1lEigKEGxhc3RfbW9kaWZ5X3RpbWUY'
    'BCABKAlSDmxhc3RNb2RpZnlUaW1lEh4KC3Blcm1fb3JnX2lkGAUgASgJUglwZXJtT3JnSWQSHg'
    'oLcGVybV9kZXZfaWQYBiABKAlSCXBlcm1EZXZJZBIfCgtnYXRld2F5X3JpZBgHIAEoCVIKZ2F0'
    'ZXdheVJpZBISCgRub3RlGAggASgJUgRub3RlEhgKB3NldHRpbmcYDiABKAlSB3NldHRpbmc=');

@$core.Deprecated('Use db_controller_gateway_manageDescriptor instead')
const db_controller_gateway_manage$json = {
  '1': 'db_controller_gateway_manage',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'ref_controller_id', '3': 3, '4': 1, '5': 9, '10': 'refControllerId'},
    {'1': 'phone_pos', '3': 4, '4': 1, '5': 5, '10': 'phonePos'},
    {'1': 'phone_no', '3': 5, '4': 1, '5': 9, '10': 'phoneNo'},
    {'1': 'ref_dev_id', '3': 6, '4': 1, '5': 9, '10': 'refDevId'},
  ],
};

/// Descriptor for `db_controller_gateway_manage`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_controller_gateway_manageDescriptor = $convert.base64Decode(
    'ChxkYl9jb250cm9sbGVyX2dhdGV3YXlfbWFuYWdlEhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ1'
    '9pZBgCIAEoCVIFb3JnSWQSKgoRcmVmX2NvbnRyb2xsZXJfaWQYAyABKAlSD3JlZkNvbnRyb2xs'
    'ZXJJZBIbCglwaG9uZV9wb3MYBCABKAVSCHBob25lUG9zEhkKCHBob25lX25vGAUgASgJUgdwaG'
    '9uZU5vEhwKCnJlZl9kZXZfaWQYBiABKAlSCHJlZkRldklk');

@$core.Deprecated('Use db_phone_no_listDescriptor instead')
const db_phone_no_list$json = {
  '1': 'db_phone_no_list',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'last_modify_time', '3': 4, '4': 1, '5': 9, '10': 'lastModifyTime'},
    {'1': 'phone_name', '3': 7, '4': 1, '5': 9, '10': 'phoneName'},
    {'1': 'phone_no', '3': 8, '4': 1, '5': 9, '10': 'phoneNo'},
    {'1': 'setting', '3': 14, '4': 1, '5': 9, '10': 'setting'},
  ],
};

/// Descriptor for `db_phone_no_list`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_phone_no_listDescriptor = $convert.base64Decode(
    'ChBkYl9waG9uZV9ub19saXN0EhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ19pZBgCIAEoCVIFb3'
    'JnSWQSKAoQbGFzdF9tb2RpZnlfdGltZRgEIAEoCVIObGFzdE1vZGlmeVRpbWUSHQoKcGhvbmVf'
    'bmFtZRgHIAEoCVIJcGhvbmVOYW1lEhkKCHBob25lX25vGAggASgJUgdwaG9uZU5vEhgKB3NldH'
    'RpbmcYDiABKAlSB3NldHRpbmc=');

@$core.Deprecated('Use db_linepoint_alarm_historyDescriptor instead')
const db_linepoint_alarm_history$json = {
  '1': 'db_linepoint_alarm_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'check_time', '3': 3, '4': 1, '5': 9, '10': 'checkTime'},
    {'1': 'checker_id', '3': 4, '4': 1, '5': 9, '10': 'checkerId'},
    {'1': 'device_id', '3': 6, '4': 1, '5': 9, '10': 'deviceId'},
    {'1': 'receive_time', '3': 8, '4': 1, '5': 9, '10': 'receiveTime'},
    {'1': 'receiver', '3': 9, '4': 1, '5': 9, '10': 'receiver'},
    {'1': 'point_id', '3': 10, '4': 1, '5': 9, '10': 'pointId'},
    {'1': 'alarm_code', '3': 11, '4': 1, '5': 5, '10': 'alarmCode'},
  ],
};

/// Descriptor for `db_linepoint_alarm_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_linepoint_alarm_historyDescriptor = $convert.base64Decode(
    'ChpkYl9saW5lcG9pbnRfYWxhcm1faGlzdG9yeRIQCgNyaWQYASABKAlSA3JpZBIVCgZvcmdfaW'
    'QYAiABKAlSBW9yZ0lkEh0KCmNoZWNrX3RpbWUYAyABKAlSCWNoZWNrVGltZRIdCgpjaGVja2Vy'
    'X2lkGAQgASgJUgljaGVja2VySWQSGwoJZGV2aWNlX2lkGAYgASgJUghkZXZpY2VJZBIhCgxyZW'
    'NlaXZlX3RpbWUYCCABKAlSC3JlY2VpdmVUaW1lEhoKCHJlY2VpdmVyGAkgASgJUghyZWNlaXZl'
    'chIZCghwb2ludF9pZBgKIAEoCVIHcG9pbnRJZBIdCgphbGFybV9jb2RlGAsgASgFUglhbGFybU'
    'NvZGU=');

@$core.Deprecated('Use db_device_channel_zoneDescriptor instead')
const db_device_channel_zone$json = {
  '1': 'db_device_channel_zone',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'zone_level', '3': 2, '4': 1, '5': 5, '10': 'zoneLevel'},
    {'1': 'zone_no', '3': 3, '4': 1, '5': 5, '10': 'zoneNo'},
    {'1': 'zone_title', '3': 4, '4': 1, '5': 9, '10': 'zoneTitle'},
    {'1': 'zone_parent', '3': 5, '4': 1, '5': 9, '10': 'zoneParent'},
    {'1': 'setting', '3': 6, '4': 1, '5': 9, '10': 'setting'},
  ],
};

/// Descriptor for `db_device_channel_zone`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_device_channel_zoneDescriptor = $convert.base64Decode(
    'ChZkYl9kZXZpY2VfY2hhbm5lbF96b25lEhAKA3JpZBgBIAEoCVIDcmlkEh0KCnpvbmVfbGV2ZW'
    'wYAiABKAVSCXpvbmVMZXZlbBIXCgd6b25lX25vGAMgASgFUgZ6b25lTm8SHQoKem9uZV90aXRs'
    'ZRgEIAEoCVIJem9uZVRpdGxlEh8KC3pvbmVfcGFyZW50GAUgASgJUgp6b25lUGFyZW50EhgKB3'
    'NldHRpbmcYBiABKAlSB3NldHRpbmc=');

@$core.Deprecated('Use db_crud_logDescriptor instead')
const db_crud_log$json = {
  '1': 'db_crud_log',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_rid', '3': 2, '4': 1, '5': 9, '10': 'orgRid'},
    {'1': 'user_rid', '3': 3, '4': 1, '5': 9, '10': 'userRid'},
    {'1': 'operation', '3': 4, '4': 1, '5': 9, '10': 'operation'},
    {'1': 'req', '3': 5, '4': 1, '5': 9, '10': 'req'},
    {'1': 'req_option', '3': 6, '4': 1, '5': 9, '10': 'reqOption'},
    {'1': 'ip_info', '3': 7, '4': 1, '5': 9, '10': 'ipInfo'},
    {'1': 'note', '3': 8, '4': 1, '5': 9, '10': 'note'},
    {'1': 'update_at', '3': 14, '4': 1, '5': 9, '10': 'updateAt'},
  ],
};

/// Descriptor for `db_crud_log`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_crud_logDescriptor = $convert.base64Decode(
    'CgtkYl9jcnVkX2xvZxIQCgNyaWQYASABKAlSA3JpZBIXCgdvcmdfcmlkGAIgASgJUgZvcmdSaW'
    'QSGQoIdXNlcl9yaWQYAyABKAlSB3VzZXJSaWQSHAoJb3BlcmF0aW9uGAQgASgJUglvcGVyYXRp'
    'b24SEAoDcmVxGAUgASgJUgNyZXESHQoKcmVxX29wdGlvbhgGIAEoCVIJcmVxT3B0aW9uEhcKB2'
    'lwX2luZm8YByABKAlSBmlwSW5mbxISCgRub3RlGAggASgJUgRub3RlEhsKCXVwZGF0ZV9hdBgO'
    'IAEoCVIIdXBkYXRlQXQ=');

@$core.Deprecated('Use db_dynamic_group_detailDescriptor instead')
const db_dynamic_group_detail$json = {
  '1': 'db_dynamic_group_detail',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'device_rid', '3': 3, '4': 1, '5': 9, '10': 'deviceRid'},
    {'1': 'device_dmrid', '3': 4, '4': 1, '5': 9, '10': 'deviceDmrid'},
    {'1': 'group_rid', '3': 5, '4': 1, '5': 9, '10': 'groupRid'},
    {'1': 'group_dmrid', '3': 6, '4': 1, '5': 9, '10': 'groupDmrid'},
    {'1': 'is_device_group', '3': 7, '4': 1, '5': 5, '10': 'isDeviceGroup'},
    {'1': 'member_state', '3': 8, '4': 1, '5': 5, '10': 'memberState'},
    {'1': 'dynamic_group_type', '3': 9, '4': 1, '5': 5, '10': 'dynamicGroupType'},
    {'1': 'member_org_id', '3': 10, '4': 1, '5': 9, '10': 'memberOrgId'},
    {'1': 'dynamic_group_state', '3': 11, '4': 1, '5': 5, '10': 'dynamicGroupState'},
    {'1': 'task_confirm_time', '3': 12, '4': 1, '5': 9, '10': 'taskConfirmTime'},
    {'1': 'creator', '3': 13, '4': 1, '5': 9, '10': 'creator'},
    {'1': 'create_time', '3': 15, '4': 1, '5': 9, '10': 'createTime'},
  ],
};

/// Descriptor for `db_dynamic_group_detail`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_dynamic_group_detailDescriptor = $convert.base64Decode(
    'ChdkYl9keW5hbWljX2dyb3VwX2RldGFpbBIQCgNyaWQYASABKAlSA3JpZBIVCgZvcmdfaWQYAi'
    'ABKAlSBW9yZ0lkEh0KCmRldmljZV9yaWQYAyABKAlSCWRldmljZVJpZBIhCgxkZXZpY2VfZG1y'
    'aWQYBCABKAlSC2RldmljZURtcmlkEhsKCWdyb3VwX3JpZBgFIAEoCVIIZ3JvdXBSaWQSHwoLZ3'
    'JvdXBfZG1yaWQYBiABKAlSCmdyb3VwRG1yaWQSJgoPaXNfZGV2aWNlX2dyb3VwGAcgASgFUg1p'
    'c0RldmljZUdyb3VwEiEKDG1lbWJlcl9zdGF0ZRgIIAEoBVILbWVtYmVyU3RhdGUSLAoSZHluYW'
    '1pY19ncm91cF90eXBlGAkgASgFUhBkeW5hbWljR3JvdXBUeXBlEiIKDW1lbWJlcl9vcmdfaWQY'
    'CiABKAlSC21lbWJlck9yZ0lkEi4KE2R5bmFtaWNfZ3JvdXBfc3RhdGUYCyABKAVSEWR5bmFtaW'
    'NHcm91cFN0YXRlEioKEXRhc2tfY29uZmlybV90aW1lGAwgASgJUg90YXNrQ29uZmlybVRpbWUS'
    'GAoHY3JlYXRvchgNIAEoCVIHY3JlYXRvchIfCgtjcmVhdGVfdGltZRgPIAEoCVIKY3JlYXRlVG'
    'ltZQ==');

@$core.Deprecated('Use db_iot_deviceDescriptor instead')
const db_iot_device$json = {
  '1': 'db_iot_device',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'dev_id', '3': 3, '4': 1, '5': 9, '10': 'devId'},
    {'1': 'dev_type', '3': 4, '4': 1, '5': 17, '10': 'devType'},
    {'1': 'dev_name', '3': 6, '4': 1, '5': 9, '10': 'devName'},
    {'1': 'note', '3': 7, '4': 1, '5': 9, '10': 'note'},
    {'1': 'lon', '3': 8, '4': 1, '5': 1, '10': 'lon'},
    {'1': 'lat', '3': 9, '4': 1, '5': 1, '10': 'lat'},
    {'1': 'setting', '3': 10, '4': 1, '5': 9, '10': 'setting'},
    {'1': 'creator', '3': 13, '4': 1, '5': 9, '10': 'creator'},
    {'1': 'create_time', '3': 15, '4': 1, '5': 9, '10': 'createTime'},
  ],
};

/// Descriptor for `db_iot_device`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_iot_deviceDescriptor = $convert.base64Decode(
    'Cg1kYl9pb3RfZGV2aWNlEhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ19pZBgCIAEoCVIFb3JnSW'
    'QSFQoGZGV2X2lkGAMgASgJUgVkZXZJZBIZCghkZXZfdHlwZRgEIAEoEVIHZGV2VHlwZRIZCghk'
    'ZXZfbmFtZRgGIAEoCVIHZGV2TmFtZRISCgRub3RlGAcgASgJUgRub3RlEhAKA2xvbhgIIAEoAV'
    'IDbG9uEhAKA2xhdBgJIAEoAVIDbGF0EhgKB3NldHRpbmcYCiABKAlSB3NldHRpbmcSGAoHY3Jl'
    'YXRvchgNIAEoCVIHY3JlYXRvchIfCgtjcmVhdGVfdGltZRgPIAEoCVIKY3JlYXRlVGltZQ==');

@$core.Deprecated('Use db_iot_restrictionDescriptor instead')
const db_iot_restriction$json = {
  '1': 'db_iot_restriction',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'iot_id', '3': 3, '4': 1, '5': 9, '10': 'iotId'},
    {'1': 'restrict_type', '3': 4, '4': 1, '5': 5, '10': 'restrictType'},
    {'1': 'restrict_station_rid', '3': 5, '4': 1, '5': 9, '10': 'restrictStationRid'},
    {'1': 'restrict_alive_interval', '3': 6, '4': 1, '5': 17, '10': 'restrictAliveInterval'},
    {'1': 'creator', '3': 13, '4': 1, '5': 9, '10': 'creator'},
    {'1': 'create_time', '3': 15, '4': 1, '5': 9, '10': 'createTime'},
  ],
};

/// Descriptor for `db_iot_restriction`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_iot_restrictionDescriptor = $convert.base64Decode(
    'ChJkYl9pb3RfcmVzdHJpY3Rpb24SEAoDcmlkGAEgASgJUgNyaWQSFQoGb3JnX2lkGAIgASgJUg'
    'VvcmdJZBIVCgZpb3RfaWQYAyABKAlSBWlvdElkEiMKDXJlc3RyaWN0X3R5cGUYBCABKAVSDHJl'
    'c3RyaWN0VHlwZRIwChRyZXN0cmljdF9zdGF0aW9uX3JpZBgFIAEoCVIScmVzdHJpY3RTdGF0aW'
    '9uUmlkEjYKF3Jlc3RyaWN0X2FsaXZlX2ludGVydmFsGAYgASgRUhVyZXN0cmljdEFsaXZlSW50'
    'ZXJ2YWwSGAoHY3JlYXRvchgNIAEoCVIHY3JlYXRvchIfCgtjcmVhdGVfdGltZRgPIAEoCVIKY3'
    'JlYXRlVGltZQ==');

@$core.Deprecated('Use db_iot_device_last_infoDescriptor instead')
const db_iot_device_last_info$json = {
  '1': 'db_iot_device_last_info',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'update_at', '3': 2, '4': 1, '5': 9, '10': 'updateAt'},
    {'1': 'org_id', '3': 3, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'dev_id', '3': 5, '4': 1, '5': 9, '10': 'devId'},
    {'1': 'last_data_time', '3': 6, '4': 1, '5': 9, '10': 'lastDataTime'},
    {'1': 'last_cmd', '3': 7, '4': 1, '5': 17, '10': 'lastCmd'},
    {'1': 'dev_status', '3': 14, '4': 1, '5': 9, '10': 'devStatus'},
    {'1': 'last_controller', '3': 15, '4': 1, '5': 9, '10': 'lastController'},
    {'1': 'opt_status', '3': 19, '4': 1, '5': 9, '10': 'optStatus'},
  ],
};

/// Descriptor for `db_iot_device_last_info`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_iot_device_last_infoDescriptor = $convert.base64Decode(
    'ChdkYl9pb3RfZGV2aWNlX2xhc3RfaW5mbxIQCgNyaWQYASABKAlSA3JpZBIbCgl1cGRhdGVfYX'
    'QYAiABKAlSCHVwZGF0ZUF0EhUKBm9yZ19pZBgDIAEoCVIFb3JnSWQSFQoGZGV2X2lkGAUgASgJ'
    'UgVkZXZJZBIkCg5sYXN0X2RhdGFfdGltZRgGIAEoCVIMbGFzdERhdGFUaW1lEhkKCGxhc3RfY2'
    '1kGAcgASgRUgdsYXN0Q21kEh0KCmRldl9zdGF0dXMYDiABKAlSCWRldlN0YXR1cxInCg9sYXN0'
    'X2NvbnRyb2xsZXIYDyABKAlSDmxhc3RDb250cm9sbGVyEh0KCm9wdF9zdGF0dXMYEyABKAlSCW'
    '9wdFN0YXR1cw==');

@$core.Deprecated('Use db_iot_data_historyDescriptor instead')
const db_iot_data_history$json = {
  '1': 'db_iot_data_history',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'org_id', '3': 2, '4': 1, '5': 9, '10': 'orgId'},
    {'1': 'cmd_time', '3': 3, '4': 1, '5': 9, '10': 'cmdTime'},
    {'1': 'cmd', '3': 4, '4': 1, '5': 17, '10': 'cmd'},
    {'1': 'dev_type', '3': 5, '4': 1, '5': 5, '10': 'devType'},
    {'1': 'dev_id', '3': 6, '4': 1, '5': 9, '10': 'devId'},
    {'1': 'recv_station_id', '3': 7, '4': 1, '5': 9, '10': 'recvStationId'},
    {'1': 'recv_time', '3': 8, '4': 1, '5': 9, '10': 'recvTime'},
    {'1': 'receiver', '3': 9, '4': 1, '5': 9, '10': 'receiver'},
    {'1': 'cmd_param', '3': 10, '4': 1, '5': 9, '10': 'cmdParam'},
  ],
};

/// Descriptor for `db_iot_data_history`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_iot_data_historyDescriptor = $convert.base64Decode(
    'ChNkYl9pb3RfZGF0YV9oaXN0b3J5EhAKA3JpZBgBIAEoCVIDcmlkEhUKBm9yZ19pZBgCIAEoCV'
    'IFb3JnSWQSGQoIY21kX3RpbWUYAyABKAlSB2NtZFRpbWUSEAoDY21kGAQgASgRUgNjbWQSGQoI'
    'ZGV2X3R5cGUYBSABKAVSB2RldlR5cGUSFQoGZGV2X2lkGAYgASgJUgVkZXZJZBImCg9yZWN2X3'
    'N0YXRpb25faWQYByABKAlSDXJlY3ZTdGF0aW9uSWQSGwoJcmVjdl90aW1lGAggASgJUghyZWN2'
    'VGltZRIaCghyZWNlaXZlchgJIAEoCVIIcmVjZWl2ZXISGwoJY21kX3BhcmFtGAogASgJUghjbW'
    'RQYXJhbQ==');

@$core.Deprecated('Use db_static_subscribesDescriptor instead')
const db_static_subscribes$json = {
  '1': 'db_static_subscribes',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'controller_dmr_id', '3': 2, '4': 1, '5': 9, '10': 'controllerDmrId'},
    {'1': 'group_dmr_id', '3': 3, '4': 1, '5': 9, '10': 'groupDmrId'},
    {'1': 'creator', '3': 13, '4': 1, '5': 9, '10': 'creator'},
    {'1': 'create_time', '3': 15, '4': 1, '5': 9, '10': 'createTime'},
  ],
};

/// Descriptor for `db_static_subscribes`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_static_subscribesDescriptor = $convert.base64Decode(
    'ChRkYl9zdGF0aWNfc3Vic2NyaWJlcxIQCgNyaWQYASABKAlSA3JpZBIqChFjb250cm9sbGVyX2'
    'Rtcl9pZBgCIAEoCVIPY29udHJvbGxlckRtcklkEiAKDGdyb3VwX2Rtcl9pZBgDIAEoCVIKZ3Jv'
    'dXBEbXJJZBIYCgdjcmVhdG9yGA0gASgJUgdjcmVhdG9yEh8KC2NyZWF0ZV90aW1lGA8gASgJUg'
    'pjcmVhdGVUaW1l');

@$core.Deprecated('Use db_app_map_privilege_deviceDescriptor instead')
const db_app_map_privilege_device$json = {
  '1': 'db_app_map_privilege_device',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'app_dmrid', '3': 2, '4': 1, '5': 9, '10': 'appDmrid'},
    {'1': 'grant_device_dmrid', '3': 3, '4': 1, '5': 9, '10': 'grantDeviceDmrid'},
    {'1': 'grant_user_rid', '3': 4, '4': 1, '5': 9, '10': 'grantUserRid'},
    {'1': 'expire_time', '3': 5, '4': 1, '5': 9, '10': 'expireTime'},
    {'1': 'is_set_expire', '3': 6, '4': 1, '5': 5, '10': 'isSetExpire'},
    {'1': 'apply_time', '3': 7, '4': 1, '5': 9, '10': 'applyTime'},
    {'1': 'grant_time', '3': 8, '4': 1, '5': 9, '10': 'grantTime'},
    {'1': 'grant_user_name', '3': 9, '4': 1, '5': 9, '10': 'grantUserName'},
  ],
};

/// Descriptor for `db_app_map_privilege_device`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_app_map_privilege_deviceDescriptor = $convert.base64Decode(
    'ChtkYl9hcHBfbWFwX3ByaXZpbGVnZV9kZXZpY2USEAoDcmlkGAEgASgJUgNyaWQSGwoJYXBwX2'
    'RtcmlkGAIgASgJUghhcHBEbXJpZBIsChJncmFudF9kZXZpY2VfZG1yaWQYAyABKAlSEGdyYW50'
    'RGV2aWNlRG1yaWQSJAoOZ3JhbnRfdXNlcl9yaWQYBCABKAlSDGdyYW50VXNlclJpZBIfCgtleH'
    'BpcmVfdGltZRgFIAEoCVIKZXhwaXJlVGltZRIiCg1pc19zZXRfZXhwaXJlGAYgASgFUgtpc1Nl'
    'dEV4cGlyZRIdCgphcHBseV90aW1lGAcgASgJUglhcHBseVRpbWUSHQoKZ3JhbnRfdGltZRgIIA'
    'EoCVIJZ3JhbnRUaW1lEiYKD2dyYW50X3VzZXJfbmFtZRgJIAEoCVINZ3JhbnRVc2VyTmFtZQ==');

@$core.Deprecated('Use db_poc_sessionDescriptor instead')
const db_poc_session$json = {
  '1': 'db_poc_session',
  '2': [
    {'1': 'rid', '3': 1, '4': 1, '5': 9, '10': 'rid'},
    {'1': 'poc_dmrid', '3': 2, '4': 1, '5': 9, '10': 'pocDmrid'},
    {'1': 'session_id', '3': 3, '4': 1, '5': 9, '10': 'sessionId'},
    {'1': 'login_time', '3': 4, '4': 1, '5': 9, '10': 'loginTime'},
    {'1': 'ip_info', '3': 5, '4': 1, '5': 9, '10': 'ipInfo'},
    {'1': 'last_update_time', '3': 6, '4': 1, '5': 9, '10': 'lastUpdateTime'},
  ],
};

/// Descriptor for `db_poc_session`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List db_poc_sessionDescriptor = $convert.base64Decode(
    'Cg5kYl9wb2Nfc2Vzc2lvbhIQCgNyaWQYASABKAlSA3JpZBIbCglwb2NfZG1yaWQYAiABKAlSCH'
    'BvY0RtcmlkEh0KCnNlc3Npb25faWQYAyABKAlSCXNlc3Npb25JZBIdCgpsb2dpbl90aW1lGAQg'
    'ASgJUglsb2dpblRpbWUSFwoHaXBfaW5mbxgFIAEoCVIGaXBJbmZvEigKEGxhc3RfdXBkYXRlX3'
    'RpbWUYBiABKAlSDmxhc3RVcGRhdGVUaW1l');

