import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:i18n_extension/i18n_extension.dart';

const Iterable<LocalizationsDelegate<dynamic>> localizationsDelegates = [
  GlobalMaterialLocalizations.delegate,
  GlobalWidgetsLocalizations.delegate,
  GlobalCupertinoLocalizations.delegate,
];

const enUS = Locale('en', "US");
const zhCN = Locale('zh', "CN");

const Iterable<Locale> supportedLocales = [enUS, zhCN];

void useLocaleZh(BuildContext context) {
  I18n.of(context).locale = zhCN;
}

void useLocaleEn(BuildContext context) {
  I18n.of(context).locale = enUS;
}

void toggleLocale(BuildContext context) {
  if (I18n.localeStr == "zh_cn") {
    useLocaleEn(context);
  } else {
    useLocaleZh(context);
  }
}
