import 'package:flutter/widgets.dart';
import 'package:i18n_extension/i18n_extension.dart';

String localeUniqueKey() {
  return UniqueKey().toString();
}

final systemNumber = localeUniqueKey();
final userInfoTitle = localeUniqueKey();
final terminalName = localeUniqueKey();
final versionString = localeUniqueKey();
final priority = localeUniqueKey();
final low = localeUniqueKey();
final medium = localeUniqueKey();
final high = localeUniqueKey();
final serverSettingTitle = localeUniqueKey();
final address = localeUniqueKey();
final port = localeUniqueKey();
final confirm = localeUniqueKey();
final noChangesDetected = localeUniqueKey();
final serverAddressChangedAndLoginAgain = localeUniqueKey();
final loginTitle = localeUniqueKey();
final username = localeUniqueKey();
final password = localeUniqueKey();
final loginButton = localeUniqueKey();
final loginSuccess = localeUniqueKey();
final systemSetting = localeUniqueKey();
final settingTitle = localeUniqueKey();
final logout = localeUniqueKey();
final speechCurrentUser = localeUniqueKey();
final speechCurrentTxGroup = localeUniqueKey();
final cancel = localeUniqueKey();
final cancelListenGroup = localeUniqueKey();
final isListenGroup = localeUniqueKey();
final actionFailed = localeUniqueKey();
final actionSuccess = localeUniqueKey();
final updateServerFailed = localeUniqueKey();
final autoLoginFailed = localeUniqueKey();
final autoLogining = localeUniqueKey();
final loginRepeat = localeUniqueKey();
final loginPasswordError = localeUniqueKey();
final loginNoPassword = localeUniqueKey();
final loginNoDevice = localeUniqueKey();
final loginBadParam = localeUniqueKey();
final loginUserNoDevice = localeUniqueKey();
final loginSessionIdNotExist = localeUniqueKey();
final loginServerError = localeUniqueKey();
final requestTimeout = localeUniqueKey();
final updateRxGroupsFailed = localeUniqueKey();
final queryRXTXGroupFailed = localeUniqueKey();
final loginQuitAlert = localeUniqueKey();
final loginQuitFailed = localeUniqueKey();
final targetNotOnline = localeUniqueKey();
final targetInCalling = localeUniqueKey();
final relayChannelBusy = localeUniqueKey();
final preemptedTimeSloResources = localeUniqueKey();
final higherPriorityTerminalCalling = localeUniqueKey();
final callResourcesHaveReleased = localeUniqueKey();
final loginFirst = localeUniqueKey();
final noTelephoneGatewayAvailable = localeUniqueKey();
final telephoneGatewayBusy = localeUniqueKey();
final inPhoneBlacklist = localeUniqueKey();
final sysAuthorExpired = localeUniqueKey();
final databaseQueryError = localeUniqueKey();
final wrongPhoneNumber = localeUniqueKey();
final loginNumberInconsistentWithCallRightNumber = localeUniqueKey();
final temporaryOrTaskGroupHasExpired = localeUniqueKey();
final groupCall = localeUniqueKey();
final singleCall = localeUniqueKey();
final talkingToSomeone = localeUniqueKey();
final callSingle = localeUniqueKey();
final callGroup = localeUniqueKey();
final callBackInfo = localeUniqueKey();
final serverUpdateSettingAlert = localeUniqueKey();
final alarming = localeUniqueKey();
final alarmFailed = localeUniqueKey();
final systemCenterName = localeUniqueKey();
final shortMessageTitle = localeUniqueKey();
final smsContent = localeUniqueKey();
final reply = localeUniqueKey();
final repost = localeUniqueKey();
final sendSuccess = localeUniqueKey();
final sendFailed = localeUniqueKey();
final pleaseSelect = localeUniqueKey();
final inbox = localeUniqueKey();
final writeSms = localeUniqueKey();
final pleaseSelectSmsRecipient = localeUniqueKey();
final normalSms = localeUniqueKey();
final autoPlaySms = localeUniqueKey();
final smsType = localeUniqueKey();
final deleteSms = localeUniqueKey();
final required = localeUniqueKey();
final maxLengthLimit = localeUniqueKey();
final rangeValueLimit = localeUniqueKey();
final emergencyAlarmIsNotEnable = localeUniqueKey();
final voiceInterrupt = localeUniqueKey();
final retrieveDataFrmServer = localeUniqueKey();
final syncServerData = localeUniqueKey();
final removeCallRestriction = localeUniqueKey();
final removeLockListening = localeUniqueKey();
final removeLockCalling = localeUniqueKey();
final bannedFromListening = localeUniqueKey();
final bannedFromCalling = localeUniqueKey();
final deviceIsLocked = localeUniqueKey();
final initialization = localeUniqueKey();
final joinTaskGroup = localeUniqueKey();
final joinTempGroup = localeUniqueKey();
final exitTaskGroup = localeUniqueKey();
final exitTempGroup = localeUniqueKey();
final taskGroupCannotEdit = localeUniqueKey();
final syncServerSetting = localeUniqueKey();
final forcedOffline = localeUniqueKey();
final logining = localeUniqueKey();
final serverDisconnected = localeUniqueKey();
final serverReconnected = localeUniqueKey();
final serverReconnectedLoginFailed = localeUniqueKey();
final locationTitle = localeUniqueKey();
final locationTimeout = localeUniqueKey();
final locationTime = localeUniqueKey();
final locating = localeUniqueKey();
final positioningSuccess = localeUniqueKey();
final lonTitle = localeUniqueKey();
final latTitle = localeUniqueKey();
final speedTitle = localeUniqueKey();
final directionTitle = localeUniqueKey();
final altitudeTitle = localeUniqueKey();
final enterTxGroup = localeUniqueKey();

extension Localization on String {
  static final Translations _t = Translations.byId('en_US', {
    enterTxGroup: {'en_US': 'Enter %s', 'zh_CN': '进入%s'},
    systemNumber: {'en_US': 'System number', 'zh_CN': '系统号'},
    userInfoTitle: {'en_US': 'User info', 'zh_CN': '用户信息'},
    terminalName: {'en_US': 'Terminal Name', 'zh_CN': '终端名称'},
    versionString: {'en_US': 'Version', 'zh_CN': '版本'},
    priority: {'en_US': 'Priority', 'zh_CN': '优先级'},
    low: {'en_US': 'Low', 'zh_CN': '低'},
    medium: {'en_US': 'Medium', 'zh_CN': '中'},
    high: {'en_US': 'High', 'zh_CN': '高'},
    serverSettingTitle: {'en_US': 'Server settings', 'zh_CN': '服务器设置'},
    address: {'en_US': 'Host', 'zh_CN': '地址'},
    port: {'en_US': 'Port', 'zh_CN': '端口'},
    confirm: {'en_US': 'Confirm', 'zh_CN': '确认'},
    noChangesDetected: {'en_US': 'No changes detected', 'zh_CN': '未检测到变化'},
    serverAddressChangedAndLoginAgain: {'en_US': 'Server address set successfully, please log in again', 'zh_CN': '服务器地址已更改，请重新登录'},
    loginTitle: {'en_US': 'Login', 'zh_CN': '系统登录'},
    username: {'en_US': 'Username', 'zh_CN': '用户名'},
    password: {'en_US': 'Password', 'zh_CN': '密码'},
    loginButton: {'en_US': 'Login', 'zh_CN': '登录'},
    loginSuccess: {'en_US': 'Login success', 'zh_CN': '登录成功'},
    settingTitle: {'en_US': 'Setting', 'zh_CN': '设置'},
    systemSetting: {'en_US': 'System setting', 'zh_CN': '系统设置'},
    logout: {'en_US': 'Logout', 'zh_CN': '退出登录'},
    speechCurrentUser: {'en_US': 'The current user is %s', 'zh_CN': '当前用户为%s'},
    speechCurrentTxGroup: {'en_US': 'The transmission group is %s', 'zh_CN': '发射组为%s'},
    cancel: {'en_US': 'Cancel', 'zh_CN': '取消'},
    cancelListenGroup: {'en_US': 'Unsubscribe from this group?', 'zh_CN': '是否取消收听该组?'},
    isListenGroup: {'en_US': 'Listening to this group?', 'zh_CN': '是否收听该组?'},
    actionFailed: {'en_US': 'Action failed', 'zh_CN': '操作失败'},
    actionSuccess: {'en_US': 'Action success', 'zh_CN': '操作成功'},
    updateServerFailed: {'en_US': 'Server address synchronization failed, please reset the server', 'zh_CN': '服务器地址同步失败，请重新设置服务器'},
    autoLoginFailed: {'en_US': 'Auto login failed, please login manually', 'zh_CN': '自动登录失败，请输入账号密码重新登录'},
    autoLogining: {'en_US': 'Auto logining...', 'zh_CN': '自动登录中...'},
    loginRepeat: {'en_US': 'Repeat login', 'zh_CN': '重复登录'},
    loginPasswordError: {'en_US': 'Password error', 'zh_CN': '密码错误'},
    loginNoPassword: {'en_US': 'No password', 'zh_CN': '没有指定密码登录'},
    loginNoDevice: {'en_US': 'Login failed', 'zh_CN': '不存在此设备'},
    loginBadParam: {'en_US': 'Bad param', 'zh_CN': '参数错误'},
    loginUserNoDevice: {'en_US': 'User no device', 'zh_CN': '用户没有指定设备'},
    loginSessionIdNotExist: {'en_US': 'Session id not exist', 'zh_CN': '用户凭证不存在,请使用账号密码登录'},
    loginServerError: {'en_US': 'Server error', 'zh_CN': '服务器内部错误'},
    requestTimeout: {'en_US': 'Request timeout', 'zh_CN': '请求超时'},
    updateRxGroupsFailed: {'en_US': 'Update receive group failed', 'zh_CN': '更新收听组失败'},
    queryRXTXGroupFailed: {'en_US': 'Unable to request transmit group and listen group, log in again or check system configuration', 'zh_CN': '无法请求发射组和收听组，重新登录或检查系统配置'},
    loginQuitAlert: {'en_US': 'Are you sure you want to log out?', 'zh_CN': '手动退出登录将会清除密码，是否退出登录？'},
    loginQuitFailed: {'en_US': 'Logout failed', 'zh_CN': '退出登录失败'},
    targetNotOnline: {'en_US': 'Target is not online', 'zh_CN': '目标不在线'},
    targetInCalling: {'en_US': 'Target is in call', 'zh_CN': '目标通话中'},
    relayChannelBusy: {'en_US': 'Relay channel busy, please try again later', 'zh_CN': '中继信道繁忙'},
    preemptedTimeSloResources: {'en_US': 'Preempted time slot resources', 'zh_CN': '被抢占时隙资源'},
    higherPriorityTerminalCalling: {'en_US': 'Higher priority terminal is calling', 'zh_CN': '更高优先级手台在通话中'},
    callResourcesHaveReleased: {'en_US': 'Call resources have been released', 'zh_CN': '通话资源已释放'},
    loginFirst: {'en_US': 'Please login first', 'zh_CN': '请先登录'},
    noTelephoneGatewayAvailable: {'en_US': 'No telephone gateway available', 'zh_CN': '无电话网关可用'},
    telephoneGatewayBusy: {'en_US': 'Telephone gateway busy', 'zh_CN': '电话网关忙'},
    inPhoneBlacklist: {'en_US': 'In phone blacklist', 'zh_CN': '电话黑名单'},
    sysAuthorExpired: {'en_US': 'System authorization has expired', 'zh_CN': '系统授权已经过期'},
    databaseQueryError: {'en_US': 'Database query error', 'zh_CN': '数据库查询错误'},
    wrongPhoneNumber: {'en_US': 'Wrong phone number', 'zh_CN': '电话号码错误'},
    loginNumberInconsistentWithCallRightNumber: {'en_US': 'Login number is inconsistent with call right number', 'zh_CN': '登录号码和申请话权号码不一致'},
    temporaryOrTaskGroupHasExpired: {'en_US': 'Temporary or task group has expired', 'zh_CN': '临时组或任务组已失效'},
    groupCall: {'en_US': 'Group call', 'zh_CN': '组呼'},
    singleCall: {'en_US': 'Single call', 'zh_CN': '单呼'},
    talkingToSomeone: {'en_US': 'Talking to %s', 'zh_CN': '与%s通话中'},
    callSingle: {'en_US': 'Single call in progress', 'zh_CN': '正在单呼'},
    callGroup: {'en_US': 'Group call in progress', 'zh_CN': '正在组呼'},
    callBackInfo: {'en_US': 'Return key to cancel callback (%s seconds)', 'zh_CN': '返回键取消回呼(%s秒)'},
    serverUpdateSettingAlert: {'en_US': 'Current terminal settings has been updated, shall we sync?', 'zh_CN': '当前终端设置已更新，是否同步?'},
    alarming: {'en_US': 'Emergency call: %s', 'zh_CN': '紧急报警呼叫: %s'},
    alarmFailed: {'en_US': 'Alarm failed', 'zh_CN': '紧急报警失败'},
    serverUpdateSettingAlert: {'en_US': 'Current terminal settings has been updated, shall we sync?', 'zh_CN': '当前终端设置已更新，是否同步?'},
    systemCenterName: {'en_US': 'System Center', 'zh_CN': '系统中心'},
    shortMessageTitle: {'en_US': 'Short Message', 'zh_CN': '短信'},
    smsContent: {'en_US': 'SMS content', 'zh_CN': '短信内容'},
    reply: {'en_US': 'Reply', 'zh_CN': '回复'},
    repost: {'en_US': 'Repost', 'zh_CN': '转发'},
    sendSuccess: {'en_US': 'Send successfully', 'zh_CN': '发送成功'},
    sendFailed: {'en_US': 'Send failed', 'zh_CN': '发送失败'},
    pleaseSelect: {'en_US': 'Please select', 'zh_CN': '请选择'},
    inbox: {'en_US': 'Inbox', 'zh_CN': '收件箱'},
    writeSms: {'en_US': 'Write SMS', 'zh_CN': '写短信'},
    pleaseSelectSmsRecipient: {'en_US': 'Please select recipient', 'zh_CN': '请选择收件人'},
    normalSms: {'en_US': 'Normal SMS', 'zh_CN': '普通短信'},
    autoPlaySms: {'en_US': 'Auto play SMS', 'zh_CN': '自动播放短信'},
    smsType: {'en_US': 'SMS type', 'zh_CN': '短信类型'},
    deleteSms: {'en_US': 'Delete SMS?', 'zh_CN': '是否删除短信？'},
    required: {'en_US': 'Required', 'zh_CN': '必填'},
    maxLengthLimit: {'en_US': 'Maximum length: %s', 'zh_CN': '最大长度：%s'},
    rangeValueLimit: {'en_US': 'Available range: %s ~ %s', 'zh_CN': '可用范围：%s ~ %s'},
    emergencyAlarmIsNotEnable: {'en_US': 'Emergency alarm is not activated', 'zh_CN': '未开启紧急报警'},
    voiceInterrupt: {'en_US': 'Interrupted', 'zh_CN': '被打断'},
    retrieveDataFrmServer: {'en_US': 'Do you want to retrieve data from the server again?', 'zh_CN': '是否重新从服务器获取数据?'},
    syncServerData: {'en_US': 'Synchronize server data', 'zh_CN': '同步服务器数据'},
    removeCallRestriction: {'en_US': 'Unblock call restrictions', 'zh_CN': '解除通话限制'},
    removeLockListening: {'en_US': 'Unblock listening', 'zh_CN': '解除禁听'},
    removeLockCalling: {'en_US': 'Unblock calling', 'zh_CN': '解除禁发'},
    bannedFromListening: {'en_US': 'Banned from listening', 'zh_CN': '已被禁听'},
    bannedFromCalling: {'en_US': 'Banned from calling', 'zh_CN': '已被禁发'},
    deviceIsLocked: {'en_US': 'Device is locked', 'zh_CN': '已锁机'},
    initialization: {'en_US': 'initialization...', 'zh_CN': '初始化中...'},
    joinTaskGroup: {'en_US': 'Join task group', 'zh_CN': '加入任务组'},
    joinTempGroup: {'en_US': 'Join temporary group', 'zh_CN': '加入临时组'},
    exitTaskGroup: {'en_US': 'Exit task group', 'zh_CN': '退出任务组'},
    exitTempGroup: {'en_US': 'Exit temporary group', 'zh_CN': '退出临时组'},
    taskGroupCannotEdit: {'en_US': 'Task group cannot be edited', 'zh_CN': '任务组不可被编辑'},
    syncServerSetting: {'en_US': 'Synchronized server configuration', 'zh_CN': '已同步服务器配置'},
    forcedOffline: {'en_US': 'Forced offline', 'zh_CN': '已被强制下线'},
    logining: {'en_US': 'Logining...', 'zh_CN': '登录中...'},
    serverDisconnected: {'en_US': 'Server disconnected', 'zh_CN': '服务器已断开连接'},
    serverReconnected: {'en_US': 'Server reconnected', 'zh_CN': '服务器已重新连接'},
    serverReconnectedLoginFailed: {'en_US': 'Server connected, login failed', 'zh_CN': '服务器已连接，自动登录失败'},
    locationTitle: {'en_US': 'Location', 'zh_CN': '卫星定位数据'},
    locationTimeout: {'en_US': 'Positioning timed out, please try again later', 'zh_CN': '定位超时，请稍候再试'},
    locationTime: {'en_US': 'Time', 'zh_CN': '时间'},
    locating: {'en_US': 'Locating...', 'zh_CN': '正在定位...'},
    positioningSuccess: {'en_US': 'Positioning success', 'zh_CN': '定位成功'},
    lonTitle: {'en_US': 'Lon', 'zh_CN': '经度'},
    latTitle: {'en_US': 'Lat', 'zh_CN': '纬度'},
    speedTitle: {'en_US': 'SPD', 'zh_CN': '速度'},
    directionTitle: {'en_US': 'HDG', 'zh_CN': '方向'},
    altitudeTitle: {'en_US': 'Alt', 'zh_CN': '海拔'},
  });

  String get i18n => localize(this, _t);

  String fill(List<Object> params) => localizeFill(this, params);

  String plural(value) => localizePlural(value, this, _t);

  String version(Object modifier) => localizeVersion(modifier, this, _t);
}
