/** 
* Generated code: do not hand-edit!!!
* Sat Jan 18 2025 11:17:45 GMT+0800 (中国标准时间)
* @auth Linfl
*/

import 'package:flutter/material.dart' show IconData;

// iconfont project address: https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2592144&keyword=&project_type=&page=
// poc app
class Iconfont {
  // This class is not meant to be instantiated or extended; this constructor
  // prevents instantiation and extension.
  Iconfont._();
  
  
  /// <i class="aliyunIconfont icon-disconnect2"></i>;
  /// icon named "disconnect2".
  static const IconData disconnect2 = IconData(0xe645, fontFamily: 'AliyunIconfont');

  /// <i class="aliyunIconfont icon-dynamic-group"></i>;
  /// icon named "dynamic-group".
  static const IconData dynamicGroup = IconData(0xe6bc, fontFamily: 'AliyunIconfont');
}
