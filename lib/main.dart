import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:i18n_extension/i18n_extension.dart';

import 'i18n/i18n.dart';
import 'router.dart';
import 'services/rpcSocket.dart';
import 'util/config.dart';
import 'util/logger.dart';
import 'util/permissions.dart';
import 'util/platformChannel.dart';
import 'util/sqlite.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 强制横屏
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
  ]);
  // 初始化平台通道
  PlatformChannel.initChannel();
  // 初始化日志
  await initLogger();
  // 加载配置文件
  await loadDotenv();
  // 监听i18n语言变化，同步TTS引擎的语言
  I18n.observeLocale = ({required Locale oldLocale, required Locale newLocale}) {
    logger.d('observeLocale: oldLocale=$oldLocale, newLocale=$newLocale');
    PlatformChannel.setSpeechLanguage(newLocale.toString());
  };

  initRpcSocket();
  initPermissions();
  initSqlite();

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'bf8100 Device',
      theme: ThemeData(
        visualDensity: VisualDensity.compact,
        colorScheme: ColorScheme.fromSeed(primary: Colors.lightBlue, seedColor: Colors.lightBlue, secondary: Colors.orange),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
            backgroundColor: Colors.lightBlue,
            foregroundColor: Colors.white,
            centerTitle: true,
            toolbarHeight: 36.0,
            titleTextStyle: TextStyle(fontSize: 20.0)),
      ),
      localizationsDelegates: localizationsDelegates,
      supportedLocales: supportedLocales,
      routerConfig: router,
      // 需要在祖先元素添加一个Overlay,否则会导致子元素overlay无法插入
      builder: (context, child) => Overlay(
        initialEntries: [
          OverlayEntry(builder: (context) {
            return I18n(child: child!);
          })
        ],
      ),
    );
  }
}
