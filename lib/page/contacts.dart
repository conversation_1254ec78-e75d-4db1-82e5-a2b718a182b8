import 'dart:async';

import 'package:bf8100deviceapp/riverpod/pocConfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../app_proto/bf8100.pb.dart';
import '../i18n/locale.dart';
import '../iconfont/iconfont.dart';
import '../riverpod/contacts.dart';
import '../router.dart';
import '../services/rpcCmd.dart';
import '../util/bf8100Util.dart';
import '../util/component.dart';
import '../util/customKey.dart';
import '../util/logger.dart';
import '../util/snackBarMessage.dart';

typedef ContactListItemBuilder = Widget? Function(BuildContext ctx, Contact contact, int index, {bool isFocused});

enum ContactListViewType {
  group,
  single,
}

// 联系人列表的一些状态缓存，如滚动位置，聚焦的节点等
class CustomContactListState {
  final _scrollOffsetMap = <GlobalKey, double>{};
  final _focusIndexMap = <GlobalKey, int>{};

  void updateScrollOffset(GlobalKey key, double offset) {
    _scrollOffsetMap[key] = offset;
  }

  double getScrollOffset(GlobalKey key) {
    return _scrollOffsetMap[key] ?? 0.0;
  }

  void updateFocusIndex(GlobalKey key, int index) {
    _focusIndexMap[key] = index;
  }

  int getFocusIndex(GlobalKey key, int Function() fallbackFunction) {
    return _focusIndexMap[key] ?? fallbackFunction();
  }
}

final _customContactListState = CustomContactListState();

// 联系人列表
class ContactListView extends ConsumerStatefulWidget {
  const ContactListView({
    super.key,
    required this.contact,
    this.titleBuilder,
    this.leadingBuilder,
    this.trailingBuilder,
    this.backgroundColor,
    this.selectedColor,
    this.onBeforeSelect,
    this.listViewKey,
    this.listViewType,
  });

  final List<Contact> contact;
  final Color? backgroundColor;
  final Color? selectedColor;
  final bool Function(Contact value)? onBeforeSelect;
  final ContactListItemBuilder? titleBuilder;
  final ContactListItemBuilder? leadingBuilder;
  final ContactListItemBuilder? trailingBuilder;
  final GlobalKey? listViewKey;
  final ContactListViewType? listViewType;

  @override
  ConsumerState<ContactListView> createState() => ContactListViewState();
}

final _pageKeyInitMap = <GlobalKey, bool>{};

class ContactListViewState extends ConsumerState<ContactListView> {
  late final ScrollController _scrollController;

  double get _itemHeight => 36.0;

  late Contact chooseContact;

  Completer<bool>? selectContactCompleter;
  bool _releaseSelectKey = true;

  GlobalKey get listViewKey => widget.listViewKey ?? GlobalKey();

  ContactListViewType get listViewType => widget.listViewType ?? ContactListViewType.group;

  final FocusNode _rootFocusNode = FocusNode();
  late List<FocusNode> _listItemFocusNodes;
  late List<GlobalKey> _listItemKeys;
  ScaffoldFeatureController? _taskNotEditFeatureController;

  @override
  void initState() {
    super.initState();

    var offset = _customContactListState.getScrollOffset(listViewKey);
    _scrollController = ScrollController(initialScrollOffset: offset);
    _scrollController.addListener(() {
      _customContactListState.updateScrollOffset(listViewKey, _scrollController.position.pixels);
    });

    _listItemFocusNodes = widget.contact.map((e) => FocusNode()).toList();
    _listItemKeys = widget.contact.map((e) => GlobalKey()).toList();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (_pageKeyInitMap[listViewKey] != null) {
        return;
      }
      _pageKeyInitMap[listViewKey] = true;
      var callTargetInfo = ref.read(callTargetProvider);
      var index = widget.contact.indexWhere((e) => e.dmrId == callTargetInfo.defaultCallDmrId);
      if (index != -1) {
        // 滚动到默认发射，并且默认发射处于三个可见的中间位置
        var scrollHeight = _itemHeight * (index - 1);
        _scrollController.jumpTo(scrollHeight);
      }
    });
  }

  @override
  void didUpdateWidget(covariant ContactListView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.contact.length != widget.contact.length) {
      _listItemFocusNodes = widget.contact.map((e) => FocusNode()).toList();
      _listItemKeys = widget.contact.map((e) => GlobalKey()).toList();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _rootFocusNode.dispose();
    for (var e in _listItemFocusNodes) {
      e.dispose();
    }
    super.dispose();
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }
          var callTargetNotifier = ref.read(callTargetProvider.notifier);
          var callTarget = ref.read(callTargetProvider);
          // 发射组和默认发射组为同一个 按下返回键盘为跳转到home页面 优先判断任务组
          if ((currentTaskGroupDmrId.isNotEmpty && callTarget.defaultCallDmrId == currentTaskGroupDmrId) || (callTarget.defaultCallDmrId == toHexDmrId(defaultGroup!.defaultSendGroupDmrid))) {
            context.go(homeRouterPath);
            return KeyEventResult.handled;
          }
          callTargetNotifier.resetDefaultCallDmrId();
          callTargetNotifier.ttsSpeakCurrentDefaultCallDmrId();
          return KeyEventResult.handled;
        case CustomLogicalKeyboardKey.home:
          context.go(homeRouterPath);
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    } else if (event is KeyUpEvent) {
      switch (event.logicalKey) {
        case CustomLogicalKeyboardKey.select:
          selectContactCompleter?.complete(true);
          _releaseSelectKey = true;
          return KeyEventResult.handled;
        case LogicalKeyboardKey.goBack:
          _releaseSelectKey = true;
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    } else if (event is KeyRepeatEvent) {
      switch (event.logicalKey) {
        case CustomLogicalKeyboardKey.select:
          final pocConfigNotifier = ref.read(pocConfigStoreProvider.notifier);
          // 终端是否可以本地编辑收听组
          if (listViewType == ContactListViewType.group && pocConfigNotifier.canEditSubscriptionLocal) {
            if (chooseContact.isDynamicGroup()) {
              if (_taskNotEditFeatureController != null) {
                _taskNotEditFeatureController!.closed.then((_) {
                  _taskNotEditFeatureController = null;
                });
                return KeyEventResult.handled;
              }
              _taskNotEditFeatureController = showSnackBarWithOption(Text(taskGroupCannotEdit.i18n), type: SnackBarMessageType.warning);
              return KeyEventResult.handled;
            }
            selectContactCompleter?.complete(false);
            if (!_releaseSelectKey) {
              return KeyEventResult.ignored;
            }
            _releaseSelectKey = false;
            String title = chooseContact.name;
            var titleWidget = Text(
              title,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: const TextStyle(fontSize: 20),
            );
            var listenGroups = [...ref.read(listenGroupsProvider)];
            var dmrId = chooseContact.intDmrId;
            var isListen = listenGroups.contains(dmrId);
            String content = isListen ? cancelListenGroup.i18n : isListenGroup.i18n;
            var contentWidget = Text(
              content,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: const TextStyle(fontSize: 16),
            );
            openAlertDialog(_scaffoldKey.currentContext!, contentWidget, titleWidget: titleWidget).then((isConfirm) async {
              if (!isConfirm) {
                return;
              }

              if (isListen) {
                listenGroups.remove(dmrId);
              } else {
                listenGroups.add(dmrId);
              }
              _updateListenGroups(listenGroups);
            });
            return KeyEventResult.handled;
          }
          return KeyEventResult.ignored;
        default:
          return KeyEventResult.ignored;
      }
    }
    return KeyEventResult.ignored;
  }

  Future<void> _updateListenGroups(List<int> newListenGroups) async {
    logger.i('change listenGroups: $newListenGroups');
    var err = await updatePocListenGroup(PocDefaultGroup(
      defaultSendGroupDmrid: defaultSendGroupDmrid,
      defaultListenGroupDmrids: newListenGroups,
    ));
    if (err != null) {
      showSnackBarWithOption(Text(updateRxGroupsFailed.i18n), type: SnackBarMessageType.warning);
      return;
    }
    ref.read(listenGroupsProvider.notifier).updateGroups(newListenGroups);
  }

  void _onSelectContact(Contact contact, int index) {
    // 不需要重复设置
    var callTarget = ref.read(callTargetProvider);
    if (contact.dmrId == callTarget.defaultCallDmrId) {
      return;
    }

    // 如果有选择前的回调校验，则先校验
    if (widget.onBeforeSelect != null) {
      if (!widget.onBeforeSelect!(contact)) {
        return;
      }
    }

    var callTargetNotifier = ref.read(callTargetProvider.notifier);
    callTargetNotifier.updateDefaultCallDmrId(contact.dmrId);
    callTargetNotifier.ttsSpeakGroupName(contact.name);
  }

  Widget? _leadingBuilder(BuildContext ctx, Contact contact, int index, {bool isFocused = false}) {
    if (widget.leadingBuilder != null) {
      return widget.leadingBuilder!(ctx, contact, index, isFocused: isFocused);
    }
    return null;
  }

  Widget? _trailingBuilder(BuildContext ctx, Contact contact, int index, {bool isFocused = false}) {
    if (widget.trailingBuilder != null) {
      return widget.trailingBuilder!(ctx, contact, index, isFocused: isFocused);
    }
    return null;
  }

  Widget? _titleBuilder(BuildContext ctx, Contact contact, int index, {bool isFocused = false}) {
    if (widget.titleBuilder != null) {
      return widget.titleBuilder!(ctx, contact, index, isFocused: isFocused);
    }
    return Row(
      children: [
        MarqueeText(
          width: 160,
          text: contact.name,
          isScroll: isFocused,
        ),
      ],
    );
  }

  int _getDefaultFocusIndex() {
    var callTargetInfo = ref.read(callTargetProvider);
    var index = widget.contact.indexWhere((e) => e.dmrId == callTargetInfo.defaultCallDmrId);
    return index == -1 ? 0 : index;
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _rootFocusNode,
      canRequestFocus: false,
      onKeyEvent: _onKeyEvent,
      child: ListView.separated(
        key: listViewKey,
        controller: _scrollController,
        itemBuilder: (BuildContext context, int index) {
          var contact = widget.contact[index];
          var focusIndex = _customContactListState.getFocusIndex(listViewKey, _getDefaultFocusIndex);
          var focused = focusIndex == index;
          var focusNode = _listItemFocusNodes[index];

          return Container(
            key: _listItemKeys[index],
            height: _itemHeight,
            alignment: Alignment.center,
            child: ListTile(
              onTap: () {
                if (!_releaseSelectKey) {
                  return;
                }
                chooseContact = contact;
                selectContactCompleter = Completer<bool>();
                selectContactCompleter!.future.then((val) {
                  if (val) {
                    _onSelectContact(contact, index);
                  }
                  selectContactCompleter = null;
                });
              },
              focusNode: focusNode,
              autofocus: focused,
              onFocusChange: (bool focused) {
                // 切换聚焦时, 强制更新一次, 文本长度超长时滚动展示
                setState(() {});
                if (focused) {
                  _customContactListState.updateFocusIndex(listViewKey, index);
                  var callTargetNotifier = ref.read(callTargetProvider.notifier);
                  callTargetNotifier.updateFocusCallDmrId(contact.dmrId);
                }
              },
              visualDensity: const VisualDensity(horizontal: VisualDensity.minimumDensity, vertical: VisualDensity.minimumDensity),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8.0),
              leading: _leadingBuilder(context, contact, index, isFocused: focused),
              title: _titleBuilder(context, contact, index, isFocused: focused),
              trailing: _trailingBuilder(context, contact, index, isFocused: focused),
              minTileHeight: _itemHeight,
            ),
          );
        },
        separatorBuilder: (BuildContext context, int index) => const Divider(height: 0),
        itemCount: widget.contact.length,
      ),
    );
  }
}

// 单呼联系人页面
class SingleCallContactPage extends ConsumerStatefulWidget {
  const SingleCallContactPage({super.key});

  @override
  ConsumerState<SingleCallContactPage> createState() => _SingleCallContactPageState();
}

final GlobalKey _singleCallContactListViewKey = GlobalKey();

class _SingleCallContactPageState extends ConsumerState<SingleCallContactPage> {
  @override
  Widget build(BuildContext context) {
    var contacts = ref.watch(singleCallContactListProvider);
    var onlineDeviceDmrIds = ref.watch(onlineDeviceDmrIdsProvider);
    defaultSortContacts(contacts, onlineDevices: onlineDeviceDmrIds);

    return SafeArea(
      child: Scaffold(
        appBar: const AppBarWithState(),
        body: ContactListView(
          listViewKey: _singleCallContactListViewKey,
          listViewType: ContactListViewType.single,
          contact: contacts,
          // onBeforeSelect: _onBeforeSelect,
          leadingBuilder: (BuildContext ctx, Contact contact, int index, {bool isFocused = false, bool selected = false}) {
            // var isOnline = _onlineDmrIds.contains(contact.dmrId);
            // return Icon(Icons.person, color: isOnline ? Colors.green : null);
            var color = onlineDeviceDmrIds.contains(contact.intDmrId) ? Colors.green : Colors.grey;
            return Icon(
              Icons.person,
              color: color,
            );
          },
          // trailingBuilder: (BuildContext ctx, Contact contact, int index, {bool isFocused = false, bool selected = false}) {
          //   var isOnline = _onlineDmrIds.contains(contact.dmrId);
          //   if (!isOnline) {
          //     return null;
          //   }
          //
          //   if (selected) {
          //     return const Icon(Icons.check_box_rounded);
          //   }
          //   return const Icon(Icons.check_box_outline_blank);
          // },
        ),
      ),
    );
  }
}

// 组呼联系人页面
class GroupCallContactPage extends ConsumerStatefulWidget {
  const GroupCallContactPage({super.key});

  @override
  ConsumerState<GroupCallContactPage> createState() => _GroupCallContactPageState();
}

final GlobalKey _groupCallContactListViewKey = GlobalKey();
final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

class _GroupCallContactPageState extends ConsumerState<GroupCallContactPage> {
  @override
  Widget build(BuildContext context) {
    // 收听组
    var listenGroups = ref.watch(listenGroupsProvider);
    // 组呼联系人，包括发射组和收听组，收听组优先排序在前面
    var contacts = ref.watch(groupCallContactListProvider);
    var notTempContacts = contacts.where((c) => !c.isTempGroup() && !c.isInvalidTempGroup()).toList();
    defaultSortContacts(notTempContacts, listenGroups: listenGroups);

    return SafeArea(
      child: Scaffold(
        key: _scaffoldKey,
        appBar: const AppBarWithState(),
        body: ContactListView(
          listViewKey: _groupCallContactListViewKey,
          listViewType: ContactListViewType.group,
          contact: notTempContacts,
          leadingBuilder: (BuildContext ctx, Contact contact, int index, {bool isFocused = false}) {
            var isListenGroup = listenGroups.contains(contact.intDmrId);
            var icon = contact.isDynamicGroup() ? Iconfont.dynamicGroup : Icons.groups;
            var color = isListenGroup ? Colors.green : Colors.grey;
            if (contact.isTempGroup() || contact.isInvalidTempGroup()) {
              color = Colors.orange;
            }
            return Icon(
              icon,
              color: color,
            );
          },
          // trailingBuilder: (BuildContext ctx, Contact contact, int index, {bool isFocused = false}) {
          //   return Icon(Icons.headset_mic_rounded, color: selected ? Colors.green : null);
          // },
        ),
      ),
      // ),
    );
  }
}
