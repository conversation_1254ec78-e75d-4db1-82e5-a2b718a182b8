import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../app_proto/app_proto.pb.dart';
import '../app_proto/bf8100.pb.dart';
import '../i18n/locale.dart';
import '../riverpod/account.dart';
import '../riverpod/contacts.dart';
import '../riverpod/serverConnect.dart';
import '../riverpod/serverSetting.dart';
import '../router.dart';
import '../services/rpcCmd.dart';
import '../services/rpcSocket.dart';
import '../util/component.dart';
import '../util/logger.dart';
import '../util/loginUtil.dart';
import '../util/styles.dart';

// 登录结果枚举
enum LoginResult {
  success, // 登录成功
  sessionInvalid, // session 失效
  networkError, // 网络错误
  otherError, // 其他错误
  cancelled, // 用户取消
}

class POCHome extends ConsumerStatefulWidget {
  const POCHome({super.key});

  @override
  ConsumerState<POCHome> createState() => _POCHomeState();
}

class _POCHomeState extends ConsumerState<POCHome> {
  final _pocHomeFocusNode = FocusNode();
  // 标志是否登录成功
  final Completer<bool> _isLoginCompleter = Completer<bool>();
  bool _hasLoginInfo = false;

  @override
  void initState() {
    super.initState();
    var account = ref.read(accountProvider);
    if (account.isLogin && !_isLoginCompleter.isCompleted) {
      _isLoginCompleter.complete(true);
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        // 登录页面主动登录成功后 跳转到主页在将logging状态设置为false
        // 防止出现登录成功后 loading跳转到主页中，登录页表单一闪而过
        var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
        loginInfoNotifier.updateIsLogging(false);
      });
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        _initHomeState();
      });
    }
  }

  @override
  void dispose() {
    // 取消自动登录
    _cancelAutoLogin = true;
    // 停止连接状态监听
    _stopConnectionStatusListening();
    // 清理焦点节点
    _pocHomeFocusNode.dispose();
    super.dispose();
  }

  Future<void> _initHomeState() async {
    final accountNotifier = ref.read(accountProvider.notifier);
    // 进入首页, 判断是否初始化服务器配置
    final serverSettingNotifier = ref.read(serverSettingProvider.notifier);
    await serverSettingNotifier.initSetting();
    if (serverSettingNotifier.isEmpty) {
      context.go(serverSettingRouterPath);
      return;
    }
    var isOk = await rpcSocket.awaitReady();
    await accountNotifier.initAccount();
    // 首次进入home页面，将存储在本机的account的dmrid和pw同步到登录页面的loginInfo riverpod
    // 登录失败跳转到登录页面时， 也可以看到dmrid和密码
    var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
    loginInfoNotifier.initLoginInfo();
    if (!isOk) {
      context.go(loginRouterPath);
      return;
    }
    await setServerSetting();
    var account = ref.read(accountProvider);
    if (account.dmrId.isEmpty || (account.sessionId.isEmpty && account.password.isEmpty)) {
      context.go(loginRouterPath);
      return;
    }
    _hasLoginInfo = true;
    _autoLogin();
  }

  // 取消自动登录的标志
  bool _cancelAutoLogin = false;
  // WebSocket 状态监听订阅
  StreamSubscription<int>? _webSocketStatusSubscription;
  // 服务器连接状态监听订阅
  StreamSubscription<bool>? _serverConnectSubscription;

  Future<void> _autoLogin() async {
    var p = ref.read(accountProvider.notifier);
    // 判断是否可以自动登录
    if (p.isLogout) {
      return;
    }

    // 重置取消标志
    _cancelAutoLogin = false;

    var account = ref.read(accountProvider);
    void successHandler() {
      setState(() {
        _isLoginCompleter.complete(true); // 登录成功后，完成 Completer
      });
    }
    var passwordCanLogin = account.dmrId.isNotEmpty && account.password.isNotEmpty;

    // 开始监听 WebSocket 和服务器连接状态
    _startConnectionStatusListening();

    // 主登录循环
    while (true) {
      // 检查是否被取消
      if (_cancelAutoLogin) {
        logger.i('Auto login cancelled by user');
        break;
      }

      // 优先尝试 sessionId 登录
      if (account.sessionId.isNotEmpty) {
        var loginResult = await _attemptSessionLoginWithCancelCheck(account, passwordCanLogin);

        if (loginResult == LoginResult.success) {
          // session 登录成功，完成完整的登录流程
          var isOk = await _performLoginHandlerWithCancelCheck(account, passwordCanLogin, true);
          if (isOk) {
            successHandler();
            _stopConnectionStatusListening();
            return;
          }
          // 如果 loginHandler 失败，继续重试
        } else if (loginResult == LoginResult.sessionInvalid) {
          // session 失效，清空 sessionId 并切换到密码登录
          logger.i('Session invalid (404), clearing sessionId and switching to password login');
          account.sessionId = '';
          continue;
        } else if (loginResult == LoginResult.cancelled) {
          // 用户取消了登录
          break;
        }
        // 其他错误（网络问题等）继续重试
      } else if (passwordCanLogin) {
        // 密码登录
        var isOk = await _performLoginHandlerWithCancelCheck(account, passwordCanLogin, false);
        if (isOk) {
          successHandler();
          _stopConnectionStatusListening();
          return;
        }
        // 密码登录失败，继续重试
      } else {
        // 既没有 sessionId 也没有密码，无法登录
        logger.w('No sessionId or password available for login');
        break;
      }

      // 等待一段时间后重试
      if (!await _delayWithCancelCheck(const Duration(seconds: 2))) {
        break; // 用户取消了登录
      }
    }

    // 清理和跳转
    await _cleanupAndNavigateToLogin(p);
  }

  // 带取消检查的延时方法
  Future<bool> _delayWithCancelCheck(Duration duration) async {
    const checkInterval = Duration(milliseconds: 100);
    int totalMs = duration.inMilliseconds;
    int elapsedMs = 0;

    while (elapsedMs < totalMs) {
      if (_cancelAutoLogin) {
        return false; // 用户取消了
      }

      int remainingMs = totalMs - elapsedMs;
      int waitMs = remainingMs < checkInterval.inMilliseconds ? remainingMs : checkInterval.inMilliseconds;

      await Future.delayed(Duration(milliseconds: waitMs));
      elapsedMs += waitMs;
    }

    return true; // 延时完成，没有被取消
  }

  // 带取消检查的 session 登录尝试
  Future<LoginResult> _attemptSessionLoginWithCancelCheck(UserInfo account, bool passwordCanLogin) async {
    if (_cancelAutoLogin) {
      return LoginResult.cancelled;
    }

    return await _attemptSessionLogin(account, passwordCanLogin);
  }

  // 带多重监听的登录方法
  Future<bool> _performLoginHandlerWithCancelCheck(UserInfo account, bool passwordCanLogin, bool useSession) async {
    if (_cancelAutoLogin) {
      return false;
    }

    // 创建登录 Future，但不立即 await
    Future<bool> loginFuture;
    if (useSession) {
      loginFuture = loginHandler(account.dmrId,
          ignoreFalseMsg: true,
          sendLoginTimeOut: !passwordCanLogin,
          sessionId: account.sessionId,
          pwd: account.password);
    } else {
      loginFuture =
          loginHandler(account.dmrId, pwd: account.password, ignoreSessionId: true, sendLoginTimeOut: passwordCanLogin);
    }

    // 同时监听多个事件
    return await _waitForLoginWithMultipleListeners(loginFuture);
  }

  // 同时监听登录结果、WebSocket 状态变化、服务器连接状态和用户取消
  Future<bool> _waitForLoginWithMultipleListeners(Future<bool> loginFuture) async {
    final completer = Completer<bool>();

    // 监听登录结果
    loginFuture.then((result) {
      if (!completer.isCompleted) {
        completer.complete(result);
      }
    }).catchError((error) {
      if (!completer.isCompleted) {
        logger.e('Login error: $error');
        completer.complete(false);
      }
    });

    // 监听 WebSocket 状态变化
    StreamSubscription<int>? wsSubscription;
    wsSubscription = rpcSocket.statusStream.listen((status) {
      if (status == WebSocket.closed && !completer.isCompleted) {
        logger.i('WebSocket disconnected during login, may need to retry');
        // WebSocket 断开不直接失败，让登录继续尝试
      }
    });

    // 监听服务器连接状态变化
    StreamSubscription<bool>? serverSubscription;
    serverSubscription = ref.read(serverConnectProvider.notifier).stream.listen((isConnected) {
      if (!isConnected && !completer.isCompleted) {
        logger.i('Server disconnected during login, may need to retry');
        // 服务器断开不直接失败，让登录继续尝试
      }
    });

    // 定期检查用户是否取消
    Timer? cancelCheckTimer;
    cancelCheckTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_cancelAutoLogin && !completer.isCompleted) {
        logger.i('Login cancelled by user');
        completer.complete(false);
        timer.cancel();
      }
    });

    // 等待结果
    final result = await completer.future;

    // 清理监听器
    wsSubscription.cancel();
    serverSubscription.cancel();
    cancelCheckTimer.cancel();

    return result;
  }

  // 清理资源并跳转到登录页
  Future<void> _cleanupAndNavigateToLogin(Account p) async {
    // 清除掉本地的登录信息
    await p.logout(syncLocal: false);
    _stopConnectionStatusListening();

    // 如果 completer 还没有完成，完成它以避免内存泄漏
    if (!_isLoginCompleter.isCompleted) {
      _isLoginCompleter.complete(false);
    }

    // 自动登录没有成功, 跳转到登录页
    context.go(loginRouterPath);
  }

  // 开始监听连接状态
  void _startConnectionStatusListening() {
    // 监听 WebSocket 状态
    _webSocketStatusSubscription = rpcSocket.statusStream.listen((status) {
      logger.d('WebSocket status changed: $status');
      // 当 WebSocket 断开连接时，可能需要重新尝试 session 登录
      if (status == WebSocket.closed && !_cancelAutoLogin) {
        logger.i('WebSocket disconnected, may need to retry session login');
      }
    });

    // 监听服务器连接状态
    // 注意：Riverpod 的 provider 没有直接的 stream，我们可以通过 watch 来监听变化
    // 这里暂时注释掉，因为在 initState 中无法直接使用 watch
    // 如果需要监听状态变化，可以在 build 方法中处理
  }

  // 停止监听连接状态
  void _stopConnectionStatusListening() {
    _webSocketStatusSubscription?.cancel();
    _webSocketStatusSubscription = null;
    _serverConnectSubscription?.cancel();
    _serverConnectSubscription = null;
  }

  // 尝试 session 登录
  Future<LoginResult> _attemptSessionLogin(UserInfo account, bool passwordCanLogin) async {
    try {
      // 检查 WebSocket 连接状态
      if (!rpcSocket.isOpen) {
        logger.w('WebSocket is not open, waiting for connection...');
        var isReady = await rpcSocket.awaitReady(timeout: const Duration(seconds: 5));
        if (!isReady) {
          logger.e('WebSocket connection failed');
          return LoginResult.networkError;
        }
      }

      // 检查服务器连接状态
      var serverConnect = ref.read(serverConnectProvider);
      if (!serverConnect) {
        logger.w('Server is not connected');
        return LoginResult.networkError;
      }

      // 尝试 session 登录
      var loginResult = await _performSessionLoginWithErrorCheck(account, passwordCanLogin);
      return loginResult;
    } catch (e) {
      logger.e('Session login error: $e');
      if (e is TimeoutException) {
        return LoginResult.networkError;
      }
      return LoginResult.otherError;
    }
  }

  // 执行 session 登录并检查错误类型
  Future<LoginResult> _performSessionLoginWithErrorCheck(UserInfo account, bool passwordCanLogin) async {
    try {
      // 直接调用底层的 sendRpcCmd 来获取具体的错误码
      final req = req_login();
      req.userName = account.dmrId.toUpperCase();
      req.userPass = account.sessionId;
      req.loginMethod = 1; // session 登录
      req.canDisplayMap = false;

      final data = req.writeToBuffer();
      var rpc = rpcCmdWithSeqNo();
      rpc.body = data;
      rpc.cmd = cmd_code.cmd_req_login.value;

      logger.i('Session login attempt for: ${account.dmrId}');

      // 发送登录请求并获取响应
      rpc_cmd loginResp = await sendRpcCmd(rpc, responseCmdCode: cmd_code.cmd_resp_login);
      logger.i('Session login response: res=${loginResp.res}');

      switch (loginResp.res) {
        case LoginResCode.success:
          // 登录成功，调用 loginUtil 中的成功处理逻辑
          // 由于 _loginSuccessHandler 是私有的，我们需要通过 loginHandler 来处理
          // 这里我们直接返回成功，让上层调用 loginHandler 来完成后续处理
          logger.i('Session login successful');
          return LoginResult.success;

        case LoginResCode.sessionIdNotExist:
          // session 失效，明确返回 sessionInvalid
          logger.i('Session ID does not exist (404), session invalid');
          return LoginResult.sessionInvalid;

        case LoginResCode.repeatLogin:
        case LoginResCode.passwordError:
        case LoginResCode.noPassword:
        case LoginResCode.noDevice:
        case LoginResCode.badParam:
        case LoginResCode.userNoDevice:
        case LoginResCode.serverError:
        default:
          // 其他错误，可能是网络问题或其他临时性错误
          logger.w('Session login failed with code: ${loginResp.res}');
          return LoginResult.networkError;
      }
    } on TimeoutException catch (e) {
      logger.e('Session login timeout: $e');
      return LoginResult.networkError;
    } catch (e) {
      logger.e('Session login error: $e');
      return LoginResult.otherError;
    }
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }
          var callTargetNotifier = ref.read(callTargetProvider.notifier);
          callTargetNotifier.resetDefaultCallDmrId();
          callTargetNotifier.ttsSpeakCurrentDefaultCallDmrId();
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }

    if (event is KeyRepeatEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          if (!_isLoginCompleter.isCompleted) {
            // 长按返回键时，取消当前正在进行的自动登录过程
            logger.i('Long press back key, cancelling auto login');
            _cancelAutoLogin = true;
            _stopConnectionStatusListening();

            // 不在这里直接跳转，让 _autoLogin 方法检测到取消标志后自行处理跳转
            return KeyEventResult.handled;
          }
      }
    }

    return KeyEventResult.ignored;
  }

  @override
  Widget build(BuildContext context) {
    var account = ref.watch(accountProvider);
    // SafeArea防止状态栏遮挡
    return SafeArea(
      child: Focus(
        focusNode: _pocHomeFocusNode,
        onKeyEvent: account.isLogin ? _onKeyEvent : null,
        autofocus: true,
        child: FutureBuilder<bool>(
            future: _isLoginCompleter.future,
            builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
              Widget childWidget;
              if (snapshot.data == true) {
                childWidget = ListTile(
                  visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                  title: Text(account.name, style: listTileTitleStyle, textAlign: TextAlign.center, maxLines: 2, overflow: TextOverflow.ellipsis),
                  subtitle: Text(account.dmrIdLabel, style: listTileSubtitleStyle.copyWith(fontSize: 16.0), textAlign: TextAlign.center, maxLines: 2, overflow: TextOverflow.ellipsis),
                );
              }
              // else if (snapshot.hasError) {
              //   childWidget = const Text('Error');
              // }
              else {
                var txt = _hasLoginInfo ? autoLogining.i18n : initialization.i18n;
                childWidget = buildLoadAnimation(txt);
              }

              return Scaffold(
                appBar: account.isLogin ? const AppBarWithState() : null,
                body: Center(child: childWidget),
              );
            }),
      ),
    );
  }
}
