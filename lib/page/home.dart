import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../i18n/locale.dart';
import '../riverpod/account.dart';
import '../riverpod/contacts.dart';
import '../riverpod/serverSetting.dart';
import '../router.dart';
import '../services/rpcSocket.dart';
import '../util/component.dart';
import '../util/loginUtil.dart';
import '../util/styles.dart';

class POCHome extends ConsumerStatefulWidget {
  const POCHome({super.key});

  @override
  ConsumerState<POCHome> createState() => _POCHomeState();
}

class _POCHomeState extends ConsumerState<POCHome> {
  final _pocHomeFocusNode = FocusNode();
  // 标志是否登录成功
  final Completer<bool> _isLoginCompleter = Completer<bool>();
  bool _hasLoginInfo = false;

  @override
  void initState() {
    super.initState();
    var account = ref.read(accountProvider);
    if (account.isLogin && !_isLoginCompleter.isCompleted) {
      _isLoginCompleter.complete(true);
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        // 登录页面主动登录成功后 跳转到主页在将logging状态设置为false
        // 防止出现登录成功后 loading跳转到主页中，登录页表单一闪而过
        var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
        loginInfoNotifier.updateIsLogging(false);
      });
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        _initHomeState();
      });
    }
  }

  Future<void> _initHomeState() async {
    final accountNotifier = ref.read(accountProvider.notifier);
    // 进入首页, 判断是否初始化服务器配置
    final serverSettingNotifier = ref.read(serverSettingProvider.notifier);
    await serverSettingNotifier.initSetting();
    if (serverSettingNotifier.isEmpty) {
      context.go(serverSettingRouterPath);
      return;
    }
    var isOk = await rpcSocket.awaitReady();
    await accountNotifier.initAccount();
    // 首次进入home页面，将存储在本机的account的dmrid和pw同步到登录页面的loginInfo riverpod
    // 登录失败跳转到登录页面时， 也可以看到dmrid和密码
    var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
    loginInfoNotifier.initLoginInfo();
    if (!isOk) {
      context.go(loginRouterPath);
      return;
    }
    await setServerSetting();
    var account = ref.read(accountProvider);
    if (account.dmrId.isEmpty || (account.sessionId.isEmpty && account.password.isEmpty)) {
      context.go(loginRouterPath);
      return;
    }
    _hasLoginInfo = true;
    _autoLogin();
  }

  Future<void> _autoLogin() async {
    var p = ref.read(accountProvider.notifier);
    // 判断是否可以自动登录
    if (p.isLogout) {
      return;
    }
    var account = ref.read(accountProvider);
    void successHandler() {
      setState(() {
        _isLoginCompleter.complete(true); // 登录成功后，完成 Completer
      });
    }
    var passwordCanLogin = account.dmrId.isNotEmpty && account.password.isNotEmpty;


    for (;;) {
      // sessionId自动登录
      if (account.sessionId.isNotEmpty) {
        var isOk = await loginHandler(account.dmrId,
            ignoreFalseMsg: true,
            sendLoginTimeOut: !passwordCanLogin,
            sessionId: account.sessionId,
            pwd: account.password);
        if (isOk) {
          successHandler();
          return;
        }
      }


      // 密码自动登录
      if (account.dmrId.isNotEmpty && account.password.isNotEmpty) {
        var isOk = await loginHandler(account.dmrId,
            pwd: account.password, ignoreSessionId: true, sendLoginTimeOut: passwordCanLogin);
        if (isOk) {
          successHandler();
          return;
        }
      }
    }

    // 清除掉本地的登录信息
    await p.logout(syncLocal: false);
    // 自动登录没有成功, 跳转到登录页
    context.go(loginRouterPath);
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }
          var callTargetNotifier = ref.read(callTargetProvider.notifier);
          callTargetNotifier.resetDefaultCallDmrId();
          callTargetNotifier.ttsSpeakCurrentDefaultCallDmrId();
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }

    if (event is KeyRepeatEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          if (!_isLoginCompleter.isCompleted) {
            // TODO: cancel login handle
          }
      }
    }

    return KeyEventResult.ignored;
  }

  @override
  Widget build(BuildContext context) {
    var account = ref.watch(accountProvider);
    // SafeArea防止状态栏遮挡
    return SafeArea(
      child: Focus(
        focusNode: _pocHomeFocusNode,
        onKeyEvent: account.isLogin ? _onKeyEvent : null,
        autofocus: true,
        child: FutureBuilder<bool>(
            future: _isLoginCompleter.future,
            builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
              Widget childWidget;
              if (snapshot.data == true) {
                childWidget = ListTile(
                  visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                  title: Text(account.name, style: listTileTitleStyle, textAlign: TextAlign.center, maxLines: 2, overflow: TextOverflow.ellipsis),
                  subtitle: Text(account.dmrIdLabel, style: listTileSubtitleStyle.copyWith(fontSize: 16.0), textAlign: TextAlign.center, maxLines: 2, overflow: TextOverflow.ellipsis),
                );
              }
              // else if (snapshot.hasError) {
              //   childWidget = const Text('Error');
              // }
              else {
                var txt = _hasLoginInfo ? autoLogining.i18n : initialization.i18n;
                childWidget = buildLoadAnimation(txt);
              }

              return Scaffold(
                appBar: account.isLogin ? const AppBarWithState() : null,
                body: Center(child: childWidget),
              );
            }),
      ),
    );
  }
}
