import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../app_proto/app_proto.pb.dart';
import '../i18n/locale.dart';
import '../riverpod/contacts.dart';
import '../router.dart';
import '../util/dateTime.dart';

bool isLocationPageOpen = false;

final StreamController<gps84> locationUpdateController = StreamController<gps84>.broadcast();

class LocationPage extends ConsumerStatefulWidget {
  const LocationPage({super.key, required this.extra});

  final LocationRouterExtra extra;

  @override
  ConsumerState<LocationPage> createState() => _LocationPageState();
}

class _LocationPageState extends ConsumerState<LocationPage> {
  final FocusNode _rootFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();

  late gps84 location;

  @override
  void initState() {
    super.initState();

    location = widget.extra.location;

    locationUpdateController.stream.listen((newLocation) {
      setState(() {
        location = newLocation;
      });
    });
  }

  @override
  void dispose() {
    _rootFocusNode.dispose();
    _scrollController.dispose();
    isLocationPageOpen = false;

    super.dispose();
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          // 存在回呼时，返回建优先删除回呼
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }
          Navigator.of(context).pop();

          return KeyEventResult.handled;

        case LogicalKeyboardKey.arrowDown:
          if (_scrollController.offset >= _scrollController.position.maxScrollExtent) {
            return KeyEventResult.ignored;
          }

          _scrollController.animateTo(
            _scrollController.offset + 72.0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          return KeyEventResult.handled;

        case LogicalKeyboardKey.arrowUp:
          if (_scrollController.offset <= 0) {
            return KeyEventResult.ignored;
          }

          _scrollController.animateTo(
            _scrollController.offset - 72.0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }
    return KeyEventResult.ignored;
  }

  @override
  Widget build(BuildContext context) {
    var speedLabel = location.speed.toStringAsFixed(2);
    if (speedLabel == '0.00') {
      speedLabel = '0';
    }

    return SafeArea(
      child: Focus(
        focusNode: _rootFocusNode,
        autofocus: true,
        onKeyEvent: _onKeyEvent,
        child: Scaffold(
          appBar: AppBar(
            centerTitle: true,
            title: Text(locationTitle.i18n, style: const TextStyle(fontSize: 18)),
          ),
          body: Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${locationTime.i18n}: ${utcToLocal(location.gpsTime)}'),
                  Text('${lonTitle.i18n}: ${location.lon}'),
                  Text('${latTitle.i18n}: ${location.lat}'),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${altitudeTitle.i18n}: ${location.altitude}m'),
                      const SizedBox(width: 12),
                      Text('${directionTitle.i18n}: ${location.direction}°'),
                    ],
                  ),
                  Text('${speedTitle.i18n}: ${speedLabel}km/h'),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
