import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../i18n/locale.dart';
import '../riverpod/account.dart';
import '../router.dart';
import '../util/component.dart';
import '../util/loginUtil.dart';
import '../util/validate.dart';

class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key, this.isFromHomePage = false});

  final bool isFromHomePage;

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

bool jumpMenu = false;

class _LoginPageState extends ConsumerState<LoginPage> {
  late final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final ScrollController _scrollController = ScrollController();
  final GlobalKey _confirmButtonKey = GlobalKey();

  final TextEditingController _dmrIdController = TextEditingController(text: '');
  final TextEditingController _passwordController = TextEditingController(text: '');

  final FocusNode _rootFocusNode = FocusNode(debugLabel: 'root');
  final FocusNode _dmrIdFocusNode = FocusNode(debugLabel: 'dmrId');
  final FocusNode _passwordFocusNode = FocusNode(debugLabel: 'password');
  final FocusNode _confirmButtonFocusNode = FocusNode(debugLabel: 'confirmButton');

  List<FocusNode> get _focusNodes => [_dmrIdFocusNode, _passwordFocusNode, _confirmButtonFocusNode];

  FocusNode? get currentFocusNode => _focusNodes.where((fn) => fn.hasFocus).firstOrNull;

  int get currentFocusNodeIndex => currentFocusNode == null ? -1 : _focusNodes.indexOf(currentFocusNode!);

  bool get hasFocus => currentFocusNode?.hasFocus ?? false;

  String get dmrId => _dmrIdController.text;

  String get pwd => _passwordController.text;

  bool get isEmpty => dmrId.isEmpty || pwd.isEmpty;

  bool get hidePassword => !_passwordFocusNode.hasFocus;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      var ref = createContainerWithRooContext();
      var canEditLoginParam = ref.read(accountProvider).canEditLoginParam;
      if (!canEditLoginParam) {
        _dmrIdFocusNode.canRequestFocus = false;
        _passwordFocusNode.canRequestFocus = false;
      }
    });
  }

  @override
  void dispose() {
    for (var n in _focusNodes) {
      n.dispose();
    }
    _rootFocusNode.dispose();
    _dmrIdController.dispose();
    _passwordController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    var ref = createContainerWithRooContext();
    var canEditLoginParam = ref.read(accountProvider).canEditLoginParam;
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          if (!canEditLoginParam) {
            return KeyEventResult.handled;
          }
          currentFocusNode?.unfocus();
          FocusScope.of(context).requestFocus(_rootFocusNode);
          return KeyEventResult.handled;

        case LogicalKeyboardKey.arrowDown:
          // 聚集到到另外一个节点，则取消当前节点的焦点
          // 否则出现：按下登录按钮后自动聚焦到了密码输入框
          if (currentFocusNode?.hasFocus == true) {
            currentFocusNode!.unfocus();
          }
          var nextFocusNodeIndex = (currentFocusNodeIndex + 1) % _focusNodes.length;
          FocusScope.of(context).requestFocus(_focusNodes[nextFocusNodeIndex]);
          return KeyEventResult.handled;
        case LogicalKeyboardKey.arrowUp:
          if (currentFocusNode?.hasFocus == true) {
            currentFocusNode!.unfocus();
          }
          var nextFocusNodeIndex = (currentFocusNodeIndex - 1) % _focusNodes.length;
          FocusScope.of(context).requestFocus(_focusNodes[nextFocusNodeIndex]);
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }

    return KeyEventResult.ignored;
  }

  void _startLogin() async {
    var serverInfoFormState = _formKey.currentState;
    if (serverInfoFormState == null) {
      return;
    }
    if (!serverInfoFormState.validate()) {
      return;
    }
    await setServerSetting();
    var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
    loginInfoNotifier.updateIsLogging(true);
    var isOk = await loginHandler(
      dmrId,
      ignoreSessionId: true,
      pwd: pwd,
    );
    if (isOk) {
      context.go(homeRouterPath);
      return;
    }
    loginInfoNotifier.updateIsLogging(false);
  }

  Widget _buildFormItem(
    Widget formItem, {
    EdgeInsets margin = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    EdgeInsets? padding,
    double? width,
  }) {
    return Container(
      // decoration: BoxDecoration(
      //   border: Border.all(color: Theme.of(context).colorScheme.primary),
      //   borderRadius: BorderRadius.circular(8),
      // ),
      width: width,
      margin: margin,
      padding: padding,
      child: formItem,
    );
  }

  Widget _buildFormView(bool isLogging, bool canEditLoginParam) {
    var disableLoginBtn = isEmpty || isLogging;

    var children = [
      // 用户名
      _buildFormItem(
        TextFormField(
          enabled: canEditLoginParam,
          // readOnly: !_dmrIdFocusNode.hasFocus,
          controller: _dmrIdController,
          focusNode: _dmrIdFocusNode,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'DMRID',
            prefixIcon: Icon(
              Icons.smartphone,
            ),
            prefix: Padding(
              padding: EdgeInsets.only(right: 4.0), // 调整 "0x" 与图标的间距
              child: Text("0x"),
            ),
          ),
          keyboardType: TextInputType.text,
          inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.singleLineFormatter],
          textInputAction: TextInputAction.next,
          validator: (String? value) {
            var res = requiredHintText(value);
            if (res != null) {
              return res;
            }

            res = maxLengthHintText(value, len: 16);
            if (res != null) {
              return res;
            }

            return res;
          },
          onFieldSubmitted: (_) {
            if (currentFocusNode?.hasFocus == true) {
              currentFocusNode!.unfocus();
            }
            FocusScope.of(context).requestFocus(_passwordFocusNode);
          },
          onChanged: (String value) {
            var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
            var loginInfo = ref.read(loginInfoProvider);
            loginInfoNotifier.update(LoginState(dmrId: value, password: loginInfo.password));
          },
        ),
      ),

      // 密码
      if (canEditLoginParam)
        _buildFormItem(
          TextFormField(
            enabled: canEditLoginParam,
            // readOnly: !_passwordFocusNode.hasFocus,
            controller: _passwordController,
            focusNode: _passwordFocusNode,
            obscureText: hidePassword,
            decoration: InputDecoration(
              border: const OutlineInputBorder(),
              labelText: password.i18n,
              prefixIcon: const Icon(
                Icons.lock,
              ),
            ),
            keyboardType: TextInputType.text,
            inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.singleLineFormatter],
            textInputAction: TextInputAction.done,
            validator: (String? value) {
              var res = requiredHintText(value);
              if (res != null) {
                return res;
              }

              res = maxLengthHintText(value, len: 16);
              if (res != null) {
                return res;
              }

              return res;
            },
            onFieldSubmitted: (_) {
              // 更新密码隐藏状态
              if (currentFocusNode?.hasFocus == true) {
                currentFocusNode!.unfocus();
              }
              FocusScope.of(context).requestFocus(_confirmButtonFocusNode);
              setState(() {});
            },
            onChanged: (String value) {
              var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
              var loginInfo = ref.read(loginInfoProvider);
              loginInfoNotifier.update(LoginState(dmrId: loginInfo.dmrId, password: value));
            },
          ),
        ),

      // 确定按钮
      _buildFormItem(
        ElevatedButton(
          key: _confirmButtonKey,
          focusNode: _confirmButtonFocusNode,
          onFocusChange: _onFocusChange,
          onPressed: disableLoginBtn ? null : _startLogin,
          style: defaultAlertDialogButtonStyle,
          child: Text(loginButton.i18n),
        ),
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
        width: double.infinity,
      ),
    ];
    return ListView(
      controller: _scrollController,
      children: children,
    );
  }

  void _onFocusChange(bool hasFocus) {
    if (hasFocus) {
      RenderBox renderBox = _confirmButtonKey.currentContext!.findRenderObject() as RenderBox;
      Offset renderOffset = renderBox.localToGlobal(Offset.zero);
      var offset = renderOffset.dy + renderBox.size.height;
      _scrollController.animateTo(
        // MediaQuery.of(context).size.height,
        offset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.ease,
      );
    }
  }

  void _focusConfirmButton() async {
    if (!mounted) {
      return;
    }
    if (_confirmButtonKey.currentState == null) {
      await Future.delayed(const Duration(milliseconds: 5));
      _focusConfirmButton();
      return;
    }
    if (currentFocusNode == _confirmButtonFocusNode && currentFocusNode!.hasFocus) {
      // _confirmButtonKey.currentContext != null ? _onFocusChange(true) : null;
      return;
    }
    currentFocusNode?.unfocus();
    // _confirmButtonKey.currentContext != null ? _onFocusChange(true) : null;
    FocusScope.of(context).requestFocus(_confirmButtonFocusNode);
  }

  @override
  Widget build(BuildContext context) {
    var loginInfo = ref.watch(loginInfoProvider);
    _dmrIdController.text = loginInfo.dmrId;
    _passwordController.text = loginInfo.password;
    var isLogging = loginInfo.isLogging;
    var account = ref.watch(accountProvider);
    var canEditLoginParam = account.canEditLoginParam;

    if (!jumpMenu && !canEditLoginParam) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusConfirmButton();
      });
    }
    var loginAnimation = buildLoadAnimation(logining.i18n);
    return SafeArea(
      child: KeyboardVisibilityBuilder(builder: (context, isVisible) {
        return Focus(
          focusNode: _rootFocusNode,
          autofocus: true,
          onKeyEvent: _onKeyEvent,
          child: Scaffold(
            appBar: isVisible || isLogging
                ? null
                : AppBar(
                    title: Text(loginTitle.i18n),
                  ),
            body: isLogging
                ? loginAnimation
                : Form(
                    key: _formKey,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    child: _buildFormView(isLogging, canEditLoginParam),
                  ),
          ),
        );
      }),
    );
  }
}
