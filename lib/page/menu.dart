import 'dart:async';

import 'package:bf8100deviceapp/page/login.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../i18n/locale.dart';
import '../riverpod/account.dart';
import '../riverpod/contacts.dart';
import '../riverpod/serverConnect.dart';
import '../riverpod/serverSetting.dart';
import '../router.dart';
import '../services/rpcCmd.dart';
import '../util/bf8100Util.dart';
import '../util/component.dart';
import '../util/customKey.dart';
import '../util/logger.dart';
import '../util/platformChannel.dart';
import '../util/request.dart';
import '../util/snackBarMessage.dart';
import '../util/styles.dart';

class POCMenu extends ConsumerStatefulWidget {
  const POCMenu({super.key});

  @override
  ConsumerState<POCMenu> createState() => _POCMenuState();
}

class _POCMenuState extends ConsumerState<POCMenu> {
  final _menuFocusNode = FocusNode();
  final Future<AppBuildInfo> _appBuildInfo = PlatformChannel.getAppBuildInfo();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      FocusScope.of(context).requestFocus();
    });
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          jumpMenu = false;
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }
          context.pop();
          return KeyEventResult.handled;
        case CustomLogicalKeyboardKey.home:
          jumpMenu = false;
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }

    return KeyEventResult.ignored;
  }

  // 打开系统设置
  Future<void> _openSystemSettingIntent() async {
    await PlatformChannel.openUniproSettings();
  }

  // 打开服务器设置
  void _openServerSetting() {
    context.push(serverSettingRouterPath, extra: ServerSettingRouterExtra(isFromHomePage: true));
  }

  // 打开用户信息
  void _openUserInfo() {
    context.push(userInfoRouterPath);
  }

  void _openSmsInbox() {
    context.push(smsInboxRouterPath);
  }

  void _openSmsEditor() {
    context.push(editSmsRouterPath, extra: SmsRouterExtra(isNewSms: true));
  }

  // Future<void> _syncPocDataFromServer() async {
  //   // 二次确认
  //   var isConfirm = await openAlertDialog(context, Text(retrieveDataFrmServer.i18n, textAlign: TextAlign.center));
  //   if (!isConfirm) return;
  //   // 清除已存在联系人
  //   var contactsNotifier = ref.read(contactsProvider.notifier);
  //   contactsNotifier.clearContacts();
  //   // 清除收听组
  //   var listenGroupsNotifier = ref.read(listenGroupsProvider.notifier);
  //   listenGroupsNotifier.clearListenGroups();
  //   // 清除默认发射组
  //   var defaultCallDmrIdNotifier = ref.read(defaultCallDmrIdProvider.notifier);
  //   defaultCallDmrIdNotifier.clearMyDefaultSendGroupDmrId();
  //   var accountNotifier = ref.read(accountProvider.notifier);
  //   // 重新初始化数据
  //   await initPocData(accountNotifier.settingLastUpdateTime);
  //
  //   // 向服务器上报当前收听组
  //   var currentListenGroup = ref.read(listenGroupsProvider);
  //   var listenGroupInfo = PocDefaultGroup(
  //     defaultListenGroupDmrids: currentListenGroup,
  //     defaultSendGroupDmrid: defaultSendGroupDmrid,
  //   );
  //   updatePocListenGroup(listenGroupInfo);
  //   context.go(homeRouterPath);
  // }

  // 退出登录
  Future<void> _logout() async {
    // 二次确认
    var confirmBtnStyle = ButtonStyle(
      overlayColor: WidgetStateProperty.resolveWith((Set<WidgetState> state) {
        if (state.contains(WidgetState.focused)) {
          return Colors.red;
        }
        return null;
      }),
      foregroundColor: WidgetStateProperty.resolveWith((Set<WidgetState> state) {
        if (state.contains(WidgetState.focused)) {
          return Colors.white;
        }
        return null;
      }),
    );
    var isConfirm = await openAlertDialog(context, Text(loginQuitAlert.i18n, textAlign: TextAlign.center), confirmBtnStyle: confirmBtnStyle);
    if (!isConfirm) return;

    // 发送退出登录请求
    var err = await loginQuit();
    if (err != null) {
      showSnackBarWithOption(Text(err.message), type: SnackBarMessageType.error);
      PlatformChannel.speakText(loginQuitFailed.i18n);
      return;
    }

    var account = ref.read(accountProvider);
    // 主动退出登录，缓存该操作，以便开机自动登录失败后，是否需要向goproxy发送事件
    storeUserActiveLogout(account.dmrId, true);

    // 正在播放语音时，退出登录清除提示框
    removeCurrentSnackBar();
    // 清除任务组
    setCurrentTaskGroupDmrId('');
    // 刷新登录状态
    await ref.read(accountProvider.notifier).logout();
    // 清除登录页面的登录信息
    var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
    loginInfoNotifier.removePW();
    // 清除网络未连接状态
    var serverConnectNotifier = ref.read(serverConnectProvider.notifier);
    serverConnectNotifier.update(true);
    if (!mounted) return;
    var p = ref.read(serverSettingProvider.notifier);
    p.updateIsUpdateServerSetting(false);
    context.go(loginRouterPath);
  }

  // 检查最新版本
  // ignore: unused_element
  Future<void> _checkAppLatestVersion() async {
    var info = await _appBuildInfo;
    var latestVersion = await Bf8100Request.requestAppLatestVersion(info.version);
    logger.d('_checkAppLatestVersion latestVersion: $latestVersion');
    if (latestVersion == null) {
      return;
    }

    // 下载app
    var appPath = await Bf8100Request.downloadApp(latestVersion);
    logger.d('_checkAppLatestVersion appPath: $appPath');
    if (appPath == null) {
      return;
    }

    // 提示是否安装
    var needInstall = await openAlertDialog(rootNavigatorKey.currentContext!, const Text("是否安装？"));
    if (!needInstall) {
      return;
    }

    // 安装app
    await PlatformChannel.silentInstallApp(appPath);
  }

  @override
  Widget build(BuildContext context) {
    bool isLogin = ref.watch(accountProvider).isLogin;
    var account = ref.watch(accountProvider);
    var canEditLoginParam = account.canEditLoginParam;
    // SafeArea防止状态栏遮挡
    return SafeArea(
      child: Focus(
        focusNode: _menuFocusNode,
        onKeyEvent: _onKeyEvent,
        child: Scaffold(
          appBar: AppBar(
            title: Text(settingTitle.i18n),
          ),
          body: ListView(
            children: <Widget>[
              // 短信收件箱
              Visibility(
                visible: isLogin,
                child: ListTile(
                  autofocus: isLogin,
                  onTap: _openSmsInbox,
                  visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                  title: Text(inbox.i18n),
                  leading: const Icon(Icons.inbox, color: Colors.teal),
                ),
              ),
              Visibility(
                visible: isLogin,
                child: Divider(height: 0, color: Colors.grey.shade300),
              ),

              // 写短信
              Visibility(
                visible: isLogin,
                child: ListTile(
                  onTap: _openSmsEditor,
                  visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                  title: Text(writeSms.i18n),
                  leading: const Icon(Icons.sms, color: Colors.teal),
                ),
              ),
              Visibility(
                visible: isLogin,
                child: Divider(height: 0, color: Colors.grey.shade300),
              ),

              // 用户信息
              Visibility(
                visible: isLogin,
                child: ListTile(
                  onTap: _openUserInfo,
                  visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                  title: Text(userInfoTitle.i18n),
                  leading: Icon(Icons.person, color: Theme.of(context).colorScheme.primary),
                ),
              ),
              Visibility(
                visible: isLogin,
                child: Divider(height: 0, color: Colors.grey.shade300),
              ),

              // 系统设置
              ListTile(
                autofocus: !isLogin,
                onTap: _openSystemSettingIntent,
                visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                title: Text(systemSetting.i18n),
                leading: Icon(Icons.settings_applications, color: Theme.of(context).colorScheme.error),
              ),
              Divider(height: 0, color: Colors.grey.shade300),

              // 服务器设置
              Visibility(
                visible: canEditLoginParam,
                child: ListTile(
                  onTap: _openServerSetting,
                  visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                  title: Text(serverSettingTitle.i18n),
                  leading: Icon(Icons.dns, color: Theme.of(context).colorScheme.secondary),
                ),
              ),
              Visibility(
                visible: canEditLoginParam,
                child: Divider(height: 0, color: Colors.grey.shade300),
              ),

              // 同步服务器数据
              // Visibility(
              //   visible: isLogin,
              //   child: ListTile(
              //     onTap: _syncPocDataFromServer,
              //     visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
              //     title: Text(syncServerData.i18n),
              //     leading: Icon(Icons.sync, color: Theme.of(context).colorScheme.secondary),
              //   ),
              // ),
              // Visibility(
              //   visible: isLogin,
              //   child: Divider(height: 0, color: Colors.grey.shade300),
              // ),

              // 退出登录
              Visibility(
                visible: isLogin && canEditLoginParam,
                child: ListTile(
                  onTap: _logout,
                  visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                  title: Text(logout.i18n),
                  leading: Icon(Icons.logout, color: Theme.of(context).colorScheme.error),
                  enabled: canEditLoginParam,
                ),
              ),

              Visibility(
                visible: isLogin && canEditLoginParam,
                child: Divider(height: 0, color: Colors.grey.shade300),
              ),
              // 版本信息
              FutureBuilder(
                future: _appBuildInfo,
                builder: (context, snapshot) {
                  Widget trailing = const SizedBox.shrink();
                  if (snapshot.hasData) {
                    trailing = Text('v${snapshot.data!.version}');
                  }

                  return ListTile(
                    onTap: () {},
                    visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
                    title: Text(versionString.i18n),
                    leading: Icon(Icons.info, color: Theme.of(context).colorScheme.primary),
                    trailing: trailing,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

enum Priority {
  low(1),
  medium(2),
  high(3);

  final int value;

  const Priority(this.value);
}

Map<int, String> _priorityMap = {
  1: low.i18n,
  2: medium.i18n,
  3: high.i18n,
};

class UserInfoPage extends ConsumerStatefulWidget {
  const UserInfoPage({super.key});

  @override
  ConsumerState<UserInfoPage> createState() => _UserInfoPageState();
}

class _UserInfoPageState extends ConsumerState<UserInfoPage> {
  final _userInfoFocusNode = FocusNode();

  Widget _buildListTile(Widget title, {Widget? subtitle, bool autofocus = false}) {
    return ListTile(
      onTap: () {},
      dense: true,
      visualDensity: const VisualDensity(vertical: VisualDensity.minimumDensity),
      title: title,
      subtitle: subtitle,
      autofocus: autofocus,
    );
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }
          Navigator.of(context).pop();
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }

    return KeyEventResult.ignored;
  }

  @override
  Widget build(BuildContext context) {
    var userInfo = ref.watch(accountProvider);
    var listChildren = <Widget>[
      _buildListTile(
        autofocus: true,
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text(systemNumber.i18n, style: listTileSubtitleStyle),
          Text(extractSysIdFromDmrId(int.parse(userInfo.dmrId, radix: 16)), style: listTileSubtitleStyle),
        ]),
      ),
      _buildListTile(
        const Text('DMRID', style: listTileSubtitleStyle),
        subtitle: Text(userInfo.dmrIdLabel, maxLines: 2, overflow: TextOverflow.ellipsis, style: listTileSubtitleStyle),
      ),
      _buildListTile(
        Text(terminalName.i18n, style: listTileSubtitleStyle),
        subtitle: Text(userInfo.name, maxLines: 2, overflow: TextOverflow.ellipsis, style: listTileSubtitleStyle),
      ),
      _buildListTile(
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text(priority.i18n, style: listTileSubtitleStyle),
          Text(_priorityMap[userInfo.priority]!, style: listTileSubtitleStyle),
        ]),
      ),
    ];

    return SafeArea(
      child: Focus(
        focusNode: _userInfoFocusNode,
        onKeyEvent: _onKeyEvent,
        child: Scaffold(
          appBar: AppBar(
            title: Text(userInfoTitle.i18n),
          ),
          body: ListView.separated(
            itemCount: listChildren.length,
            itemBuilder: (context, index) {
              return listChildren[index];
            },
            separatorBuilder: (BuildContext context, int index) {
              return Divider(height: 0, color: Colors.grey.shade300);
            },
          ),
        ),
      ),
    );
  }
}
