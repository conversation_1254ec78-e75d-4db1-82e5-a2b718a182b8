import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../app_proto/app_proto.pb.dart';
import '../i18n/locale.dart';
import '../riverpod/account.dart';
import '../riverpod/contacts.dart';
import '../riverpod/serverSetting.dart';
import '../router.dart';
import '../services/rpcCmd.dart';
import '../services/rpcSocket.dart';
import '../util/component.dart';
import '../util/logger.dart';
import '../util/snackBarMessage.dart';
import '../util/validate.dart';

class ServerSettingPage extends ConsumerStatefulWidget {
  const ServerSettingPage({super.key, this.isFromHomePage = false});

  final bool isFromHomePage;

  @override
  ConsumerState<ServerSettingPage> createState() => _ServerSettingPageState();
}

class _ServerSettingPageState extends ConsumerState<ServerSettingPage> {
  late final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final ScrollController _scrollController = ScrollController();
  final GlobalKey _confirmButtonKey = GlobalKey();

  // todo: 默认服务器地址
  final TextEditingController _hostController = TextEditingController();
  final TextEditingController _portController = TextEditingController();

  final FocusNode _rootFocusNode = FocusNode();
  final FocusNode _hostFocusNode = FocusNode();
  final FocusNode _portFocusNode = FocusNode();
  final FocusNode _confirmButtonFocusNode = FocusNode();

  List<FocusNode> get _focusNodes => [_hostFocusNode, _portFocusNode, _confirmButtonFocusNode];

  FocusNode? get currentFocusNode => _focusNodes.where((fn) => fn.hasFocus).firstOrNull;

  int get currentFocusNodeIndex => currentFocusNode == null ? -1 : _focusNodes.indexOf(currentFocusNode!);

  bool get editorHasFocus => _hostFocusNode.hasFocus || _portFocusNode.hasFocus;

  String get _host => _hostController.text;

  String get _port => _portController.text;

  bool get isEmpty => _host.isEmpty || _port.isEmpty;

  bool _isOpen = false;

  bool get confirmBtnDisabled => isEmpty || !_isOpen;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final serverInfo = ref.read(serverSettingProvider);
      _hostController.text = serverInfo.host;
      _portController.text = serverInfo.port == 0 ? "" : serverInfo.port.toString();

      await rpcSocket.awaitReady();
      setState(() {
        _isOpen = rpcSocket.isOpen;
      });
    });
  }

  @override
  void dispose() {
    _rootFocusNode.dispose();
    _hostController.dispose();
    _portController.dispose();
    _scrollController.dispose();
    for (var n in _focusNodes) {
      n.dispose();
    }
    super.dispose();
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          // 存在回呼时，返回建优先删除回呼
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }
          if (currentFocusNode != null) {
            currentFocusNode!.unfocus();
            FocusScope.of(context).requestFocus(_rootFocusNode);
          } else if (widget.isFromHomePage) {
            Navigator.of(context).pop();
          }

          return KeyEventResult.handled;

        case LogicalKeyboardKey.arrowDown:
          var nextFocusNodeIndex = (currentFocusNodeIndex + 1) % _focusNodes.length;
          FocusScope.of(context).requestFocus(_focusNodes[nextFocusNodeIndex]);
          return KeyEventResult.handled;
        case LogicalKeyboardKey.arrowUp:
          var nextFocusNodeIndex = (currentFocusNodeIndex - 1) % _focusNodes.length;
          FocusScope.of(context).requestFocus(_focusNodes[nextFocusNodeIndex]);
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }
    return KeyEventResult.ignored;
  }

  void _setServerAddress() async {
    var serverInfoFormState = _formKey.currentState;
    if (serverInfoFormState == null) {
      return;
    }
    if (!serverInfoFormState.validate()) {
      return;
    }

    var portInt = int.parse(_port);
    var originServerInfo = ref.read(serverSettingProvider);
    if (originServerInfo.host == _host && originServerInfo.port == portInt) {
      showSnackBarWithOption(Text(noChangesDetected.i18n));
      return;
    }

    // update serverInfo
    var isOk = await updateServerSetting(_host, portInt);
    if (!isOk) {
      showSnackBarWithOption(Text(actionFailed.i18n), type: SnackBarMessageType.warning);
      return;
    }

    // store serverInfo
    var p = ref.read(serverSettingProvider.notifier);
    p.update(server_addr(host: _host, port: portInt));
    // 已经更新过服务器设置标记
    p.updateIsUpdateServerSetting(true);
    logger.i('update server setting in server setting page');
    showSnackBarWithOption(Text(serverAddressChangedAndLoginAgain.i18n), type: SnackBarMessageType.success);
    context.go(loginRouterPath);
  }

  Widget _buildFormItem(
    Widget formItem, {
    EdgeInsets margin = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    EdgeInsets? padding,
    double? width,
  }) {
    return Container(
      // decoration: BoxDecoration(
      //   border: Border.all(color: Theme.of(context).colorScheme.primary),
      //   borderRadius: BorderRadius.circular(8),
      // ),
      width: width,
      margin: margin,
      padding: padding,
      child: formItem,
    );
  }

  Widget _buildFormView(bool canEditLoginParam) {
    var children = [
      // 域名或IP
      _buildFormItem(
        TextFormField(
          enabled: canEditLoginParam,
          controller: _hostController,
          focusNode: _hostFocusNode,
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            labelText: address.i18n,
            prefixIcon: const Icon(
              Icons.dns,
            ),
          ),
          keyboardType: TextInputType.text,
          inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.singleLineFormatter],
          textInputAction: TextInputAction.next,
          validator: (String? value) {
            var res = requiredHintText(value);
            if (res != null) {
              return res;
            }

            res = maxLengthHintText(value, len: 128);
            if (res != null) {
              return res;
            }

            return res;
          },
          onFieldSubmitted: (_) {
            FocusScope.of(context).requestFocus(_portFocusNode);
          },
        ),
      ),

      // 端口
      _buildFormItem(
        TextFormField(
          enabled: canEditLoginParam,
          controller: _portController,
          focusNode: _portFocusNode,
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            labelText: port.i18n,
            prefixIcon: const Icon(
              Icons.numbers,
            ),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.digitsOnly],
          textInputAction: TextInputAction.done,
          validator: (String? value) {
            var res = requiredHintText(value);
            if (res != null) {
              return res;
            }

            res = rangeValue(int.parse(value!), min: 1, max: 0xFFFF);
            if (res != null) {
              return res;
            }

            return res;
          },
          onFieldSubmitted: (_) {
            FocusScope.of(context).requestFocus(_confirmButtonFocusNode);
          },
        ),
      ),

      // 确定按钮
      _buildFormItem(
        ElevatedButton(
          key: _confirmButtonKey,
          focusNode: _confirmButtonFocusNode,
          onFocusChange: _onFocusChange,
          onPressed: canEditLoginParam ? _setServerAddress : null,
          style: defaultAlertDialogButtonStyle,
          child: Text(confirm.i18n),
        ),
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
        width: double.infinity,
      ),
    ];
    return ListView(
      controller: _scrollController,
      children: children,
    );
  }

  void _onFocusChange(bool hasFocus) {
    if (hasFocus) {
      RenderBox renderBox = _confirmButtonKey.currentContext!.findRenderObject() as RenderBox;
      Offset renderOffset = renderBox.localToGlobal(Offset.zero);
      var offset = renderOffset.dy + renderBox.size.height;
      _scrollController.animateTo(
        // MediaQuery.of(context).size.height,
        offset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.ease,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    var canEditLoginParam = ref.watch(accountProvider).canEditLoginParam;

    return KeyboardVisibilityBuilder(builder: (context, isVisible) {
      return SafeArea(
        child: Focus(
          focusNode: _rootFocusNode,
          autofocus: true,
          onKeyEvent: _onKeyEvent,
          child: Scaffold(
            appBar: isVisible
                ? null
                : AppBar(
                    title: Text(serverSettingTitle.i18n),
                  ),
            body: Form(
              key: _formKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: _buildFormView(canEditLoginParam),
            ),
          ),
        ),
      );
    });
  }
}
