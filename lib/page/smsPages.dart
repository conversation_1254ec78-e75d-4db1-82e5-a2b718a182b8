import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:fixnum/fixnum.dart' as $fixnum;

import '../app_proto/app_proto.pb.dart';
import '../i18n/locale.dart';
import '../iconfont/iconfont.dart';
import '../riverpod/account.dart';
import '../riverpod/contacts.dart';
import '../riverpod/shortMessage.dart';
import '../router.dart';
import '../services/rpcCmd.dart';
import '../util/bf8100Util.dart';
import '../util/component.dart';
import '../util/snackBarMessage.dart';
import '../util/validate.dart';

class DisplaySmsPage extends ConsumerStatefulWidget {
  const DisplaySmsPage({super.key, required this.sms});

  final short_messages sms;

  @override
  ConsumerState<DisplaySmsPage> createState() => _DisplaySmsPageState();
}

class _DisplaySmsPageState extends ConsumerState<DisplaySmsPage> {
  final FocusNode _rootFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  double _lastScrollPosition = 0;

  short_messages get sms => widget.sms;

  final GlobalKey _actionRowKey = GlobalKey();
  final FocusNode replyBtnFocusNode = FocusNode();
  final FocusNode repostBtnFocusNode = FocusNode();

  List<FocusNode> get _focusNodes => [replyBtnFocusNode, repostBtnFocusNode];

  FocusNode? get currentFocusNode => _focusNodes.where((fn) => fn.hasFocus).firstOrNull;

  int get currentFocusNodeIndex => currentFocusNode == null ? -1 : _focusNodes.indexOf(currentFocusNode!);

  @override
  initState() {
    super.initState();

    _scrollController.addListener(() {
      _lastScrollPosition = _scrollController.position.pixels;
    });
  }

  // 向上滚一屏，保留一行重复内容
  void _scrollUpOnePage() {
    _scrollController.animateTo(_lastScrollPosition - _scrollController.position.viewportDimension + 24,
        duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
  }

  // 向下滚一屏，保留一行重复内容
  void _scrollDownOnePage() {
    _scrollController.animateTo(_lastScrollPosition + _scrollController.position.viewportDimension - 24,
        duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
  }

  KeyEventResult? _previousBtnFocus() {
    // 如果不是最后一个按键聚焦，则进入上一个按键聚焦逻辑
    if (currentFocusNodeIndex > 0) {
      var node = findNextFocusNode(_focusNodes, currentFocusNodeIndex, reverse: true);
      if (node != null) {
        FocusScope.of(context).requestFocus(node);
        return KeyEventResult.handled;
      }
    }

    // 如果是第一个按键聚焦，则取消按键聚焦
    currentFocusNode?.unfocus();
    FocusScope.of(context).requestFocus(_rootFocusNode);
    return null;
  }

  KeyEventResult? _nextBtnFocus() {
    var node = findNextFocusNode(_focusNodes, currentFocusNodeIndex);
    if (node == null) {
      return KeyEventResult.ignored;
    }
    FocusScope.of(context).requestFocus(node);
    return KeyEventResult.handled;
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }

          if (currentFocusNode != null) {
            currentFocusNode!.unfocus();
            FocusScope.of(context).requestFocus(_rootFocusNode);
            return KeyEventResult.handled;
          }

          Navigator.of(context).pop();
          return KeyEventResult.handled;

        case LogicalKeyboardKey.arrowUp:
          // 到顶了，不再向上滚
          if (_scrollController.position.pixels <= 0) {
            // 页面不可滚动，直接处理按键聚焦事件
            if (_scrollController.position.maxScrollExtent == 0) {
              var r = _previousBtnFocus();
              if (r != null) {
                return r;
              }
            }
            return KeyEventResult.ignored;
          }

          var r = _previousBtnFocus();
          if (r != null) {
            return r;
          }

          // 没有按键聚焦，则向上滚
          _scrollUpOnePage();
          return KeyEventResult.handled;

        case LogicalKeyboardKey.arrowDown:
          // 到底部了，不再向下滚
          if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent) {
            var r = _nextBtnFocus();
            if (r != null) {
              return r;
            }
            return KeyEventResult.handled;
          }
          _scrollDownOnePage();
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }
    return KeyEventResult.ignored;
  }

  // 转发短信时，选择目标联系人回调
  Future<void> _onSelectTargetForRepost(String? targetDmrId) async {
    if (targetDmrId == null) {
      return;
    }

    // 开始发送短信
    var account = ref.read(accountProvider);
    var newSms = short_messages(
      targetDmrid: targetDmrId,
      senderDmrid: account.dmrId,
      smsNo: nextSmsNo(),
      smsType: sms.smsType,
      smsContent: sms.smsContent,
      codec: sms.codec,
      time: $fixnum.Int64(DateTime.timestamp().millisecondsSinceEpoch ~/ 1000),
    );
    var err = await sendShortMessage(newSms);
    if (err != null) {
      showSnackBarWithOption(Text('${sendFailed.i18n} ${err.toString()}'), type: SnackBarMessageType.error);
      return;
    }

    showSnackBarWithOption(Text(sendSuccess.i18n), type: SnackBarMessageType.success);
  }

  @override
  Widget build(BuildContext context) {
    var contactsNotifier = ref.read(contactsProvider.notifier);
    var time = DateTime.fromMillisecondsSinceEpoch(sms.time.toInt() * 1000);
    var timeStr = DateFormat('HH:mm:ss').format(time);

    var senderName = sms.senderDmrid;
    var isSystemCenter = sms.senderDmrid == systemCenterDmrId;
    if (isSystemCenter) {
      senderName = systemCenterName.i18n;
    } else {
      var senderDevice = contactsNotifier.lookupDbDeviceWithOutPermission(sms.senderDmrid);
      if (senderDevice != null) {
        senderName = senderDevice.selfId;
      }
    }

    return SafeArea(
      child: Focus(
        focusNode: _rootFocusNode,
        autofocus: true,
        onKeyEvent: _onKeyEvent,
        child: Scaffold(
          appBar: AppBar(
            title: Text(shortMessageTitle.i18n),
          ),
          body: SingleChildScrollView(
            controller: _scrollController,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  // 发信人：
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(senderName, maxLines: 1, overflow: TextOverflow.ellipsis),
                      Text(timeStr, style: const TextStyle(fontSize: 12)),
                    ],
                  ),
                  Divider(height: 4, color: Colors.grey.shade200),
                  // 内容：
                  Text(sms.smsContent),
                  // 操作按钮组
                  Row(
                    key: _actionRowKey,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        focusNode: replyBtnFocusNode,
                        onPressed: isSystemCenter
                            ? null
                            : () {
                                context.push(editSmsRouterPath, extra: SmsRouterExtra(sms: sms));
                              },
                        style: defaultAlertDialogButtonStyle,
                        child: Text(reply.i18n),
                      ),
                      ElevatedButton(
                        focusNode: repostBtnFocusNode,
                        onPressed: () {
                          context.push(sendSmsTargetSelectionRouterPath, extra: SmsRouterExtra(onSelectTarget: _onSelectTargetForRepost));
                        },
                        style: defaultAlertDialogButtonStyle,
                        child: Text(repost.i18n),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// 回复短信页面
class EditSmsPage extends ConsumerStatefulWidget {
  const EditSmsPage({super.key, required this.extra});

  final SmsRouterExtra extra;

  @override
  ConsumerState<EditSmsPage> createState() => _EditSmsPageState();
}

class _EditSmsPageState extends ConsumerState<EditSmsPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _confirmButtonKey = GlobalKey();
  final GlobalKey<FormFieldState<String>> _contentKey = GlobalKey<FormFieldState<String>>();
  final TextEditingController _contentController = TextEditingController();
  final FocusNode _rootFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();
  final FocusNode _confirmButtonFocusNode = FocusNode();
  final FocusNode _smsTypeFocusNode = FocusNode();
  final GlobalKey _smsTypeKey = GlobalKey();
  int _smsType = 0;

  bool get isNewSms => widget.extra.isNewSms;

  short_messages? get originSms => widget.extra.sms;

  List<FocusNode> get _focusNodes => isNewSms ? [_contentFocusNode, _smsTypeFocusNode, _confirmButtonFocusNode] : [_contentFocusNode, _confirmButtonFocusNode];

  FocusNode? get currentFocusNode => _focusNodes.where((fn) => fn.hasFocus).firstOrNull;

  int get currentFocusNodeIndex => currentFocusNode == null ? -1 : _focusNodes.indexOf(currentFocusNode!);

  @override
  initState() {
    super.initState();

    _smsTypeFocusNode.addListener(_smsTypeFocusNodeListener);
  }

  @override
  dispose() {
    _smsTypeFocusNode.removeListener(_smsTypeFocusNodeListener);
    super.dispose();
  }

  void _smsTypeFocusNodeListener() {
    var hasFocus = _smsTypeFocusNode.hasFocus;
    if (hasFocus) {
      RenderBox renderBox = _smsTypeKey.currentContext!.findRenderObject() as RenderBox;
      Offset renderOffset = renderBox.localToGlobal(Offset.zero);
      var offset = renderOffset.dy + renderBox.size.height;
      _scrollController.animateTo(
        // MediaQuery.of(context).size.height,
        offset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.ease,
      );
    }
  }

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }

          if (currentFocusNode != null) {
            currentFocusNode!.unfocus();
            FocusScope.of(context).requestFocus(_rootFocusNode);
          } else {
            Navigator.of(context).pop();
          }
          return KeyEventResult.handled;

        case LogicalKeyboardKey.arrowDown:
          var nextFocusNodeIndex = (currentFocusNodeIndex + 1) % _focusNodes.length;
          FocusScope.of(context).requestFocus(_focusNodes[nextFocusNodeIndex]);
          return KeyEventResult.handled;
        case LogicalKeyboardKey.arrowUp:
          var nextFocusNodeIndex = (currentFocusNodeIndex - 1) % _focusNodes.length;
          FocusScope.of(context).requestFocus(_focusNodes[nextFocusNodeIndex]);
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }
    return KeyEventResult.ignored;
  }

  bool _validateForm() {
    var smsFormState = _formKey.currentState;
    if (smsFormState == null) {
      return false;
    }
    return smsFormState.validate();
  }

  Future<void> _confirmSendSms() async {
    if (!_validateForm()) {
      return;
    }

    // 开始发送短信
    var account = ref.read(accountProvider);
    var sms = short_messages(
      targetDmrid: originSms!.senderDmrid,
      senderDmrid: account.dmrId,
      smsNo: nextSmsNo(),
      smsType: originSms!.smsType,
      smsContent: _contentController.text,
      codec: 2,
      time: $fixnum.Int64(DateTime.timestamp().millisecondsSinceEpoch ~/ 1000),
    );
    var err = await sendShortMessage(sms);
    if (err != null) {
      showSnackBarWithOption(Text('${sendFailed.i18n} ${err.toString()}'), type: SnackBarMessageType.error);
      return;
    }

    showSnackBarWithOption(Text(sendSuccess.i18n), type: SnackBarMessageType.success);
    // 回复短信后，返回显示短信的页面
    Navigator.of(context).pop();
  }

  Future<void> _onSelectTargetForNewSms(String? targetDmrId) async {
    if (targetDmrId == null) {
      return;
    }

    // 开始发送短信
    var account = ref.read(accountProvider);
    var newSms = short_messages(
      targetDmrid: targetDmrId,
      senderDmrid: account.dmrId,
      smsNo: nextSmsNo(),
      smsType: _smsType,
      smsContent: _contentController.text,
      codec: 2,
      // 只支持utf-16编码
      time: $fixnum.Int64(DateTime.timestamp().millisecondsSinceEpoch ~/ 1000),
    );
    var err = await sendShortMessage(newSms);
    if (err != null) {
      showSnackBarWithOption(Text('${sendFailed.i18n} ${err.toString()}'), type: SnackBarMessageType.error);
      return;
    }

    showSnackBarWithOption(Text(sendSuccess.i18n), type: SnackBarMessageType.success);
  }

  void _selectTargetForNewSms() {
    if (!_validateForm()) {
      var errorText = _contentKey.currentState!.errorText;
      showSnackBarWithOption(Text('${smsContent.i18n} $errorText'), type: SnackBarMessageType.error);
      return;
    }
    context.push(sendSmsTargetSelectionRouterPath, extra: SmsRouterExtra(onSelectTarget: _onSelectTargetForNewSms));
  }

  Widget _buildFormItem(
    Widget formItem, {
    EdgeInsets margin = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    EdgeInsets? padding,
    double? width,
  }) {
    return Container(
      width: width,
      margin: margin,
      padding: padding,
      child: formItem,
    );
  }

  void _onConfirmButtonFocusChange(bool hasFocus) {
    if (hasFocus) {
      RenderBox renderBox = _confirmButtonKey.currentContext!.findRenderObject() as RenderBox;
      Offset renderOffset = renderBox.localToGlobal(Offset.zero);
      var offset = renderOffset.dy + renderBox.size.height;
      _scrollController.animateTo(
        // MediaQuery.of(context).size.height,
        offset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.ease,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    var appBarTitle = writeSms.i18n;
    var contactsNotifier = ref.read(contactsProvider.notifier);

    // 不是新知信，则显示发信人
    if (!isNewSms) {
      var senderDmrid = originSms!.senderDmrid;
      var senderDevice = contactsNotifier.lookupDbDeviceWithOutPermission(senderDmrid);
      if (senderDevice != null) {
        appBarTitle = senderDevice.selfId;
      }
    }

    return KeyboardVisibilityBuilder(builder: (context, isVisible) {
      return SafeArea(
        child: Focus(
          focusNode: _rootFocusNode,
          autofocus: false,
          onKeyEvent: _onKeyEvent,
          child: Scaffold(
            appBar: isVisible
                ? null
                : AppBar(
                    title: Text(appBarTitle),
                  ),
            body: Form(
              key: _formKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: ListView(
                controller: _scrollController,
                children: [
                  // 短信编辑
                  _buildFormItem(
                    TextFormField(
                      key: _contentKey,
                      controller: _contentController,
                      focusNode: _contentFocusNode,
                      autofocus: true,
                      maxLines: 2,
                      maxLength: 140,
                      decoration: InputDecoration(
                        border: const OutlineInputBorder(),
                        labelText: smsContent.i18n,
                        prefixIcon: const Icon(
                          Icons.text_fields,
                        ),
                      ),
                      keyboardType: TextInputType.text,
                      inputFormatters: <TextInputFormatter>[FilteringTextInputFormatter.singleLineFormatter],
                      textInputAction: TextInputAction.next,
                      validator: (String? value) {
                        var res = requiredHintText(value);
                        if (res != null) {
                          return res;
                        }

                        res = maxLengthHintText(value, len: 140);
                        if (res != null) {
                          return res;
                        }

                        return res;
                      },
                      onFieldSubmitted: (_) {
                        FocusScope.of(context).requestFocus(_confirmButtonFocusNode);
                      },
                    ),
                  ),

                  // 短信类型
                  Visibility(
                    visible: isNewSms,
                    child: _buildFormItem(
                      DropdownButtonFormField(
                        key: _smsTypeKey,
                        focusNode: _smsTypeFocusNode,
                        value: _smsType,
                        menuMaxHeight: MediaQuery.of(context).size.height,
                        items: [
                          DropdownMenuItem<int>(
                            value: SmsType.normal,
                            child: Text(normalSms.i18n),
                          ),
                          DropdownMenuItem<int>(
                            value: SmsType.autoPlay,
                            child: Text(autoPlaySms.i18n),
                          ),
                        ],
                        onChanged: (int? value) {
                          setState(() {
                            _smsType = value!;
                          });
                        },
                        hint: Text(smsType.i18n),
                        decoration: InputDecoration(
                          border: const OutlineInputBorder(),
                          labelText: smsType.i18n,
                          prefixIcon: const Icon(
                            Icons.type_specimen,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // 确定按钮
                  _buildFormItem(
                    ElevatedButton(
                      key: _confirmButtonKey,
                      focusNode: _confirmButtonFocusNode,
                      onFocusChange: _onConfirmButtonFocusChange,
                      onPressed: isNewSms ? _selectTargetForNewSms : _confirmSendSms,
                      style: defaultAlertDialogButtonStyle,
                      child: Text(isNewSms ? pleaseSelectSmsRecipient.i18n : confirm.i18n),
                    ),
                    margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                    width: double.infinity,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}

// 发送短信目标选择页面
class SendSmsTargetSelectionPage extends ConsumerStatefulWidget {
  const SendSmsTargetSelectionPage({super.key, required this.extra});

  final SmsRouterExtra extra;

  @override
  ConsumerState<SendSmsTargetSelectionPage> createState() => _SendSmsTargetSelectionPageState();
}

class _SendSmsTargetSelectionPageState extends ConsumerState<SendSmsTargetSelectionPage> {
  final GlobalKey _listViewKey = GlobalKey();
  final FocusNode _rootFocusNode = FocusNode();

  double get _itemHeight => 36.0;

  void Function(String? dmrId)? get onSelectTarget => widget.extra.onSelectTarget;

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }

          if (onSelectTarget != null) {
            onSelectTarget!(null);
          }
          Navigator.of(context).pop();
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }
    return KeyEventResult.ignored;
  }

  Future<void> _selectTarget(String targetDmrId) async {
    if (onSelectTarget != null) {
      onSelectTarget!(targetDmrId);
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    var contacts = ref.watch(contactsProvider);
    var listenGroups = ref.watch(listenGroupsProvider);
    var onlineDeviceDmrIds = ref.watch(onlineDeviceDmrIdsProvider);
    defaultSortContacts(contacts, listenGroups: listenGroups, onlineDevices: onlineDeviceDmrIds);

    return  Focus(
        focusNode: _rootFocusNode,
        autofocus: false,
        onKeyEvent: _onKeyEvent,
        child: Scaffold(
          appBar: AppBar(
            title: Text(pleaseSelect.i18n),
          ),
          body: ListView.separated(
            key: _listViewKey,
            itemBuilder: (BuildContext context, int index) {
              var contact = contacts[index];
              var isGroup = contact.isGroup;
              var isTaskGroup = isGroup ? contact.isTaskGroup() : false;
              var isListenGroup = listenGroups.contains(contact.intDmrId);
              var isOnline = onlineDeviceDmrIds.contains(contact.intDmrId);
              return Container(
                height: _itemHeight,
                alignment: Alignment.center,
                child: ListTile(
                  autofocus: index == 0,
                  onTap: () {
                    _selectTarget(contact.dmrId);
                  },
                  visualDensity: const VisualDensity(horizontal: VisualDensity.minimumDensity, vertical: VisualDensity.minimumDensity),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                  leading: isGroup
                      ? Icon(
                          isTaskGroup ? Iconfont.dynamicGroup : Icons.groups,
                          color: isListenGroup ? Colors.green : Colors.grey,
                        )
                      : Icon(
                          Icons.person,
                          color: isOnline ? Colors.green : Colors.grey,
                        ),
                  title: Text(
                    contact.name,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    softWrap: false,
                  ),
                  minTileHeight: _itemHeight,
                ),
              );
            },
            separatorBuilder: (BuildContext context, int index) => const Divider(height: 0),
            itemCount: contacts.length,
          ),
        ),
      );
  }
}

// 收件箱页面
class SmsInboxPage extends ConsumerStatefulWidget {
  const SmsInboxPage({super.key});

  @override
  ConsumerState<SmsInboxPage> createState() => _SmsInboxPageState();
}

class _SmsInboxPageState extends ConsumerState<SmsInboxPage> {
  final FocusNode _rootFocusNode = FocusNode();

  int _smsCount = 0;
  late short_messages selectSms;
  Completer<bool>? selectSmsCompleter;

  double get _itemHeight => 36.0;

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.goBack:
          var r = defaultGoBackOnKeyDownEvent(ref);
          if (r != null) {
            return r;
          }

          Navigator.of(context).pop();
          return KeyEventResult.handled;

        default:
          return KeyEventResult.ignored;
      }
    }

    if (event is KeyUpEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.select:
          selectSmsCompleter?.complete(true);
          return KeyEventResult.handled;
        default:
          return KeyEventResult.ignored;
      }
    }

    if (event is KeyRepeatEvent) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.select:
          selectSmsCompleter?.complete(false);
          var contactsNotifier = ref.read(contactsProvider.notifier);
          var account = ref.read(accountProvider);
          var targetName = selectSms.targetDmrid;
          var targetIsSelf = selectSms.targetDmrid == account.dmrId;
          // 收件目标是自己
          if (targetIsSelf) {
            targetName = account.name;
          } else {
            // 能收到短信，目标一定是自己或自己收听的组，理论上必定存在于联系人中
            var targetContact = contactsNotifier.lookupContact(selectSms.targetDmrid);
            if (targetContact != null) {
              targetName = targetContact.name;
            }
          }
          var titleWidget = Text(
            targetName,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: const TextStyle(fontSize: 20),
          );
          var contentWidget = Text(
            deleteSms.i18n,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: const TextStyle(fontSize: 16),
          );
          openAlertDialog(context, contentWidget, titleWidget: titleWidget).then((isConfirm) async {
            if (!isConfirm) {
              return;
            }

            _removeSms(selectSms);
          });
          break;
        default:
          return KeyEventResult.ignored;
      }
    }

    return KeyEventResult.ignored;
  }

  void _removeSms(short_messages sms) {
    var shortMessageNotifier = ref.read(shortMessageProvider.notifier);
    shortMessageNotifier.removeSms(sms);
  }

  // 进入显示短信页面
  void _showSms(short_messages sms) {
    var extra = SmsRouterExtra(sms: sms);
    context.push(displaySmsRouterPath, extra: extra);
  }

  @override
  Widget build(BuildContext context) {
    var smsList = ref.watch(shortMessageProvider);
    // 按时间倒序排序，即最新的放在最前面
    smsList.sort((a, b) => b.time.compareTo(a.time));
    _smsCount = smsList.length;

    var contactsNotifier = ref.read(contactsProvider.notifier);
    var account = ref.read(accountProvider);

    var appBarTitle = inbox.i18n;
    if (smsList.isNotEmpty) {
      appBarTitle += ' ($_smsCount)';
    }

    return Focus(
        focusNode: _rootFocusNode,
        autofocus: smsList.isEmpty,
        onKeyEvent: _onKeyEvent,
        child: Scaffold(
          appBar: AppBar(
            title: Text(appBarTitle),
          ),
          body: ListView.separated(
            restorationId: "sms_inbox_list_view",
            itemBuilder: (BuildContext context, int index) {
              var sms = smsList[index];
              var time = DateTime.fromMillisecondsSinceEpoch(sms.time.toInt() * 1000);
              var timeStr = DateFormat('HH:mm:ss').format(time);
              var targetIsGroup = checkDmrIdIsGroup(toIntDmrId(sms.targetDmrid));
              var targetName = sms.targetDmrid;
              var targetIsSelf = sms.targetDmrid == account.dmrId;
              // 收件目标是自己
              if (targetIsSelf) {
                targetName = account.name;
              } else {
                // 能收到短信，目标一定是自己或自己收听的组，理论上必定存在于联系人中
                var targetContact = contactsNotifier.lookupContact(sms.targetDmrid);
                if (targetContact != null) {
                  targetName = targetContact.name;
                }
              }

              return Container(
                height: _itemHeight,
                alignment: Alignment.center,
                child: ListTile(
                  autofocus: index == 0,
                  onTap: () {
                    if (selectSmsCompleter != null) {
                      return;
                    }
                    selectSms = sms;
                    selectSmsCompleter = Completer<bool>();
                    selectSmsCompleter?.future.then((val) {
                      selectSmsCompleter = null;
                      if (!val) {
                        return;
                      }
                      _showSms(sms);
                    });
                  },
                  visualDensity: const VisualDensity(horizontal: VisualDensity.minimumDensity, vertical: VisualDensity.minimumDensity),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                  minTileHeight: _itemHeight,
                  leading: Icon(
                    targetIsGroup ? Icons.groups : Icons.person,
                    color: Colors.grey,
                  ),
                  title: Text(
                    targetName,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    softWrap: false,
                  ),
                  trailing: Text(timeStr, style: const TextStyle(fontSize: 12)),
                ),
              );
            },
            separatorBuilder: (BuildContext context, int index) => const Divider(height: 0),
            itemCount: smsList.length,
          ),
        ),
      );
  }
}
