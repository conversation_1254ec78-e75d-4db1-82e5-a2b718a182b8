import 'dart:async';
import 'dart:convert';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../util/logger.dart';

part 'account.g.dart';

class UserInfo {
  String dmrId;
  String name;
  String sessionId;
  bool isLogin;
  String sysId;
  String password;
  int priority;
  String orgRid;
  bool canEditLoginParam;

  String get dmrIdLabel => '$dmrId / ${int.parse(dmrId, radix: 16)}';

  UserInfo({
    this.dmrId = '',
    this.name = '',
    this.sessionId = '',
    this.isLogin = false,
    this.sysId = '',
    this.password = '',
    this.priority = 1,
    this.orgRid = '',
    this.canEditLoginParam = true,
  });

  UserInfo.fromMap(Map<String, dynamic> map)
      : sysId = map['sysId'],
        name = map['name'],
        password = map['password'],
        sessionId = map['sessionId'],
        isLogin = map['isLogin'] ?? false,
        dmrId = map['dmrId'] ?? '',
        priority = map['priority'] ?? 0,
        orgRid = map['orgRid'] ?? '',
        canEditLoginParam = map['canEditLoginParam'] ?? true;

  Map<String, dynamic> toMap() {
    return {
      'sysId': sysId,
      'name': name,
      'dmrId': dmrId,
      'password': password,
      'sessionId': sessionId,
      'priority': priority,
      'canEditLoginParam': canEditLoginParam,
    };
  }

  String toJson() => jsonEncode(toMap());

  @override
  String toString() {
    return 'UserInfo{sysId: $sysId, name: $name, password: $password, sessionId: $sessionId, priority: $priority, canEditLoginParam: $canEditLoginParam}';
  }

  UserInfo copyWith({String? dmrId, String? name, String? sessionId, bool? isLogin, String? sysId, String? password, int? priority, String? orgRid, bool? canEditLoginParam}) {
    return UserInfo(
      dmrId: dmrId ?? this.dmrId,
      name: name ?? this.name,
      sessionId: sessionId ?? this.sessionId,
      isLogin: isLogin ?? this.isLogin,
      sysId: sysId ?? this.sysId,
      password: password ?? this.password,
      priority: priority ?? this.priority,
      orgRid: orgRid ?? this.orgRid,
      canEditLoginParam: canEditLoginParam ?? this.canEditLoginParam,
    );
  }
}

const _loginAccountKey = 'loginAccount';

Future<bool> _store(UserInfo info) async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setString(_loginAccountKey, info.toJson());
}

Future<UserInfo?> _load() async {
  try {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? json = prefs.getString(_loginAccountKey);
    if (json == null) {
      return null;
    }
    return UserInfo.fromMap(jsonDecode(json));
  } catch (e) {
    logger.e('UserInfo _load error: $e');
    return null;
  }
}

// Future<bool> _remove() async {
//   final SharedPreferences prefs = await SharedPreferences.getInstance();
//   return prefs.remove(_loginAccountKey);
// }

const _settingLastUpdateTimeKey = 'settingLastUpdateTime';

Future<String> loadSettingLastUpdateTime(String dmrId) async {
  const invalidTime = "2000-01-01 00:00:00";
  try {
    String key = '$_settingLastUpdateTimeKey-$dmrId';
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(key) ?? invalidTime;
  } catch (e) {
    return invalidTime;
  }
}

Future<bool> storeSettingLastUpdateTime(String dateTime, String dmrId) async {
  String key = '$_settingLastUpdateTimeKey-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setString(key, dateTime);
}

const _activeLogoutKeyPrefix = 'activeLogout';

Future<bool> isUserActiveLogout(String dmrId) async {
  try {
    String key = '$_activeLogoutKeyPrefix-$dmrId';
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? false;
  } catch (e) {
    return false;
  }
}

Future<bool> storeUserActiveLogout(String dmrId, bool value) async {
  String key = '$_activeLogoutKeyPrefix-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setBool(key, value);
}

bool _isInit = false;

@Riverpod(keepAlive: true)
class Account extends _$Account {
  bool get isInit => _isInit;
  bool _isLogout = false;

  bool get isLogout => _isLogout;

  // 是否需要播报登录用户语音
  bool needSpeakAccount = false;

  // POC配置更新时间
  String _settingLastUpdateTime = '';

  String get settingLastUpdateTime => _settingLastUpdateTime;

  void updateSettingLastUpdateTime(String value) {
    _settingLastUpdateTime = value;
    storeSettingLastUpdateTime(value, state.dmrId);
  }

  @override
  UserInfo build() {
    return UserInfo();
  }

  Future<void> logout({bool syncLocal = true, bool isInit = false}) async {
    state = UserInfo(isLogin: false, password: '', sessionId: '', dmrId: state.dmrId, name: state.name);
    _isLogout = true;
    _isInit = isInit;
    // 清除密码跟sessionId并同步本地
    if (syncLocal) {
      await _store(state);
    }
  }

  Future<void> login(UserInfo info) async {
    info.isLogin = true;
    _isLogout = false;
    needSpeakAccount = true;
    state = info;
    await _store(info);
    storeUserActiveLogout(state.dmrId, false);
  }

  // 初始化登录账号信息，从本地存储中读取
  Future<void> initAccount() async {
    if (_isInit) {
      return;
    }

    // 如果已经登录，则不再初始化
    if (state.isLogin) {
      return;
    }

    _isInit = true;
    UserInfo? info = await _load();
    logger.d('initAccount info: $info');
    if (info != null) {
      state = info;
    }
  }

  // 并保存在本地
  Future<void> updateAccount(UserInfo info) async {
    state = info;
    await _store(info);
  }
}

class LoginState {
  String dmrId;
  String password;
  bool isLogging = false;

  LoginState({
    this.dmrId = '',
    this.password = '',
    this.isLogging = false,
  });

  LoginState copyWith({String? dmrId, String? password, bool? isLogging}) {
    return LoginState(
      dmrId: dmrId ?? this.dmrId,
      password: password ?? this.password,
      isLogging: isLogging ?? this.isLogging,
    );
  }
}

bool _isInitLoginInfo = false;

@Riverpod(keepAlive: true)
class LoginInfo extends _$LoginInfo {
  @override
  LoginState build() {
    return LoginState();
  }

  void update(LoginState info) {
    state = info;
  }

  void removePW() {
    state = state.copyWith(password: '');
  }

  void updateIsLogging(bool isLogging) {
    state = state.copyWith(isLogging: isLogging);
  }

  Future<void> initLoginInfo() async {
    if (_isInitLoginInfo) {
      return;
    }

    _isInitLoginInfo = true;
    UserInfo? info = await _load();
    if (info != null) {
      state = LoginState(dmrId: info.dmrId, password: info.password, isLogging: false);
    }
  }
}
