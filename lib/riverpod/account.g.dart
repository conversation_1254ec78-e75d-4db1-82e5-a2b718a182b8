// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$accountHash() => r'fd30955580c4881bb91a1a64965f7a8b3695e851';

/// See also [Account].
@ProviderFor(Account)
final accountProvider = NotifierProvider<Account, UserInfo>.internal(
  Account.new,
  name: r'accountProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$accountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Account = Notifier<UserInfo>;
String _$loginInfoHash() => r'7082d5308a838fca2cfc12c1a013b83277a9e03f';

/// See also [LoginInfo].
@ProviderFor(LoginInfo)
final loginInfoProvider = NotifierProvider<LoginInfo, LoginState>.internal(
  LoginInfo.new,
  name: r'loginInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$loginInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoginInfo = Notifier<LoginState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
