import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../i18n/locale.dart';
import '../riverpod/contacts.dart';
import '../router.dart';
import '../util/snackBarMessage.dart';

part 'alarm.g.dart';

// 报警状态，主要为了更新渲染AppBar的报警图标
@Riverpod(keepAlive: true)
class Alarming extends _$Alarming {
  @override
  bool build() {
    return false;
  }

  void update(bool isAlarming) {
    state = isAlarming;
  }
}

class AlarmController {
  // 是否正在报警呼叫，已经呼出去了，默认为true
  bool _isAlarmingCalling = false;

  bool get isAlarmingCall => _isAlarmingCalling;

  // 正在报警，报警标识
  bool _isAlarming = false;

  bool get isAlarming => _isAlarming;

  // 处于报警状态但是没有进行了报警呼叫 或者没有报警
  // 用来区分已经报警了且报警语音已经停了, 此时可以发送新的报警呼叫, 用于文本提示
  bool _isAlarmingButNotCallOrNotAlarm = true;

  bool get isAlarmingButNotCallOrNotAlarm => _isAlarmingButNotCallOrNotAlarm;

  ScaffoldFeatureController<SnackBar, SnackBarClosedReason>? _snackBarController;

  // 本地取消报警
  void cancelLocalSos() {
    _isAlarmingButNotCallOrNotAlarm = true;
    if (!_isAlarming) {
      return;
    }
    if (_isAlarmingCalling) {
      _snackBarController?.close();
      _isAlarmingCalling = false;
    }
    _isAlarming = false;
    var ref = createContainerWithRooContext();
    var alarmingNotifier = ref.read(alarmingProvider.notifier);
    alarmingNotifier.update(_isAlarming);
  }

  // 展示紧急呼叫提示
  void showAlarmOverlay() {
    _isAlarmingCalling = true;
    var ref = createContainerWithRooContext();
    var contactsProviderNotifier = ref.read(contactsProvider.notifier);
    var callTargetInfo = ref.read(callTargetProvider);
    var txContact = contactsProviderNotifier.lookupContact(callTargetInfo.defaultCallDmrId);

    // 时长默认为99秒，系统上报警设置最大为99秒，取消报警和监听时间到了会自动关闭消息提示
    _snackBarController = showSnackBarWithOption(Text(alarming.i18n.fill([txContact?.name ?? callTargetInfo.defaultCallDmrId]), style: const TextStyle(color: Colors.white), textAlign: TextAlign
        .center),
        duration: const Duration(seconds: 99), type: SnackBarMessageType.error);
    _snackBarController!.closed.then((value) {
      _isAlarmingCalling = false;
      _snackBarController = null;
    });
  }

  // 移除紧急报警呼叫提示
  void removeAlarmOverlay() {
    _snackBarController?.close();
    _isAlarmingCalling = false;
    _isAlarmingButNotCallOrNotAlarm = true;
  }

  // 发送的紧急报警指令受到了回复
  // 更新报警状态为正在报警和即将紧急语音呼叫
  void startAlarm() {
    _isAlarming = true;
    _isAlarmingButNotCallOrNotAlarm = false;
    var ref = createContainerWithRooContext();
    var alarmingNotifier = ref.read(alarmingProvider.notifier);
    alarmingNotifier.update(_isAlarming);
  }
}
