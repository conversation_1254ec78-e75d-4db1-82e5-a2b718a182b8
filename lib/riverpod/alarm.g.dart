// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alarm.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$alarmingHash() => r'5d19b72e33744e955503ed1286ca4d9e29a4d39c';

/// See also [Alarming].
@ProviderFor(Alarming)
final alarmingProvider = NotifierProvider<Alarming, bool>.internal(
  Alarming.new,
  name: r'alarmingProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$alarmingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Alarming = Notifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
