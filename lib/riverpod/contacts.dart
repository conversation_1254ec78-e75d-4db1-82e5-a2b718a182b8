import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../app_proto/app_db.pb.dart';
import '../app_proto/bf8100.pb.dart';
import '../i18n/locale.dart';
import '../router.dart';
import '../util/bf8100Util.dart';
import '../util/logger.dart';
import '../util/platformChannel.dart';
import '../util/snackBarMessage.dart';
import 'account.dart';

part 'contacts.g.dart';

class Contact<T> {
  T originData;

  // 1: db_org, 2: db_device
  // 100:临时组  101：任务组  102:自动失效的临时组
  final int dataType;
  late final int intDmrId;
  late final String dmrId;
  late final String name;
  late final bool isGroup;

  bool isDbOrg() => dataType == 1 || dataType == 100 || dataType == 101 || dataType == 102;

  bool isDbDevice() => dataType == 2;

  bool isTaskGroup() => dataType == 101;

  bool isTempGroup() => dataType == 100;

  bool isInvalidTempGroup() => dataType == 102;

  bool isDynamicGroup() => dataType == 101 || dataType == 101 || dataType == 102;

  // hex
  // final String dmrId;
  // String get dmrId => isDbOrg() ? (originData as db_org).dmrId : (originData as db_device).dmrId;

  // int get intDmrId => toIntDmrId(dmrId);

  // bool get isGroup => checkDmrIdIsGroup(intDmrId);

  // String get name => originData is db_org ? (originData as db_org).orgShortName : (originData as db_device).selfId;

  Contact({required this.originData, required this.dataType}) {
    dmrId = isDbOrg() ? (originData as db_org).dmrId : (originData as db_device).dmrId;
    intDmrId = toIntDmrId(dmrId);
    isGroup = checkDmrIdIsGroup(intDmrId);
    name = originData is db_org ? (originData as db_org).orgShortName : (originData as db_device).selfId;
  }

  static Contact fromMap(Map<String, dynamic> map) {
    int dataType = map['dataType'];
    List<int> m = (map['originData'] as List).cast<int>();
    if (dataType == 2) {
      var device = db_device.fromBuffer(m);
      return Contact<db_device>(originData: device, dataType: dataType);
    }
    var org = db_org.fromBuffer(m);
    return Contact<db_org>(originData: org, dataType: dataType);
  }

  Map<String, dynamic> toMap() {
    late Uint8List buffer;
    // 将originData转换为json字符串
    if (isDbOrg()) {
      buffer = (originData as db_org).writeToBuffer();
    } else {
      buffer = (originData as db_device).writeToBuffer();
    }
    return {
      'originData': buffer,
      'dataType': dataType,
    };
  }
}

const _pocContacts = 'pocContacts';

Future<List<Contact>> loadPocContacts(String dmrId) async {
  try {
    String key = '$_pocContacts-$dmrId';
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? contactString = prefs.getString(key);
    if (contactString == null) {
      return [];
    }
    List<Map<String, dynamic>> contactStringList = jsonDecode(contactString).cast<Map<String, dynamic>>();
    return contactStringList.map((contactMap) => Contact.fromMap(contactMap)).toList();
  } catch (e) {
    logger.e('PocData _load error: $e');
    return [];
  }
}

Future<bool> storePocContacts(List<Contact> contacts, String dmrId) async {
  String key = '$_pocContacts-$dmrId';
  String pocContacts = jsonEncode(contacts.map((c) => c.toMap()).toList());
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setString(key, pocContacts);
}

Future<bool> _removePocContacts(String dmrId) async {
  String key = '$_pocContacts-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.remove(key);
}

// 缓存无权限联系人的信息
Map<String, db_device> noPermissionDevices = {};

@Riverpod(keepAlive: true)
class Contacts extends _$Contacts {
  @override
  List<Contact> build() {
    return <Contact>[];
  }

  void _storeContacts() {
    var account = ref.read(accountProvider);
    storePocContacts(state, account.dmrId);
  }

  void addContact(Contact contact) {
    state = [...state, contact];
    _storeContacts();
  }

  void addContacts(List<Contact> contacts) {
    // 使用addAll方法, ConsumerWidget检测不到contacts的更新,整个替换state才可以检测到
    // state.addAll(contacts);
    state = [...state, ...contacts];
    _storeContacts();
  }

  void removeContact(Contact contact) {
    removeContactWithDmrId(contact.dmrId);
  }

  void removeContactWithDmrId(String dmrId) {
    state = state.where((c) => c.dmrId != dmrId).toList();
    _storeContacts();
  }

  void updateContact(Contact contact) {
    state = [
      for (Contact c in state)
        if (c.dmrId == contact.dmrId) contact else c
    ];
    _storeContacts();
  }

  Contact? lookupContact(String dmrId) {
    try {
      return state.firstWhere((Contact c) => c.dmrId == dmrId);
    } catch (e) {
      return null;
    }
  }

  Contact? lookupContactFromIntDmrId(int dmrId) {
    try {
      return state.firstWhere((Contact c) => c.intDmrId == dmrId);
    } catch (e) {
      return null;
    }
  }

  // 先从通讯录中查找，如果没有则从权限外的联系人中查找
  db_device? lookupDbDeviceWithOutPermission(String dmrId) {
    // 查找通讯录
    for (var c in state) {
      if (c.dmrId == dmrId && c.isDbDevice()) {
        return c.originData as db_device;
      }
    }

    // 查找权限外的联系人
    return noPermissionDevices[dmrId];
  }

  void clearContacts() {
    state.clear();
    var account = ref.read(accountProvider);
    _removePocContacts(account.dmrId);
  }
}

@riverpod
// ignore: deprecated_member_use_from_same_package
List<Contact> singleCallContactList(SingleCallContactListRef ref) {
  return ref.watch(contactsProvider.select((contacts) => contacts.where((c) => !c.isGroup).toList()));
}

@riverpod
// ignore: deprecated_member_use_from_same_package
List<Contact> groupCallContactList(GroupCallContactListRef ref) {
  // 包含组呼联系人和任务组, 过滤掉临时组
  return ref.watch(contactsProvider.select((contacts) => contacts.where((c) => c.isGroup && !(c.isInvalidTempGroup() || c.isInvalidTempGroup())).toList()));
}

const _pocCurrentListenKey = '_pocCurrentListenKey';

Future<List<int>> loadPocCurrentListenGroup(String dmrId) async {
  try {
    String key = '$_pocCurrentListenKey-$dmrId';
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return (prefs.getStringList(key) ?? []).map((e) => toIntDmrId((e))).toList();
  } catch (e) {
    logger.e('PocData _load error: $e');
    return [];
  }
}

Future<bool> _storePocCurrentListenGroup(List<int> data, String dmrId) async {
  String key = '$_pocCurrentListenKey-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setStringList(key, data.map((e) => toHexDmrId(e)).toList());
}

Future<bool> _removePocCurrentListenGroup(String dmrId) async {
  String key = '$_pocCurrentListenKey-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.remove(key);
}

// 当前收听的接收组
@Riverpod(keepAlive: true)
class ListenGroups extends _$ListenGroups {
  @override
  List<int> build() {
    return <int>[];
  }

  void _storeListenGroups() {
    var account = ref.read(accountProvider);
    _storePocCurrentListenGroup(state, account.dmrId);
  }

  void updateGroups(List<int> groups) {
    state = groups;
    _storeListenGroups();
  }

  void addOneListenGroup(int intDmrId) {
    state = [...state, intDmrId];
    _storeListenGroups();
  }

  void removeOneListenGroup(int intDmrId) {
    state = state.where((e) => e != intDmrId).toList();
    _storeListenGroups();
  }

  void clearListenGroups() {
    state.clear();
    var account = ref.read(accountProvider);
    _removePocCurrentListenGroup(account.dmrId);
  }
}

const _pocDefaultSendAndListenGroup = 'pocDefaultSendAndListenGroup';

Future<PocDefaultGroup?> loadPocDefaultSendAndListenGroup(String dmrId) async {
  try {
    String key = '$_pocDefaultSendAndListenGroup-$dmrId';
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? json = prefs.getString(key);
    if (json == null) {
      return null;
    }
    return PocDefaultGroup.fromJson(json);
  } catch (e) {
    logger.e('PocData _load error: $e');
    return null;
  }
}

Future<bool> storePocDefaultSendAndListenGroup(PocDefaultGroup data, String dmrId) async {
  String key = '$_pocDefaultSendAndListenGroup-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setString(key, data.writeToJson());
}

PocDefaultGroup? _defaultGroup;

PocDefaultGroup? get defaultGroup => _defaultGroup;

int get defaultSendGroupDmrid => _currentTaskGroupIntDmrId == 0 ? (_defaultGroup?.defaultSendGroupDmrid ?? 0) : _currentTaskGroupIntDmrId;

String get defaultCallDmrId => toHexDmrId(defaultSendGroupDmrid);

void updatePocDefaultGroup(PocDefaultGroup group, String dmrId) {
  _defaultGroup = group;
  storePocDefaultSendAndListenGroup(group, dmrId);
}

// 当前加入的任务组dmrId
String _currentTaskGroupDmrId = '';

String get currentTaskGroupDmrId => _currentTaskGroupDmrId;
int _currentTaskGroupIntDmrId = 0;

int get currentTaskGroupIntDmrId => _currentTaskGroupIntDmrId;

void setCurrentTaskGroupDmrId(String dmrId) {
  _currentTaskGroupDmrId = dmrId;
  _currentTaskGroupIntDmrId = dmrId.isEmpty ? 0 : toIntDmrId(dmrId);
  var ref = createContainerWithRooContext();
  var account = ref.read(accountProvider);
  if (_currentTaskGroupIntDmrId == 0) {
    _removeTaskGroup(account.dmrId);
    return;
  }
  _storeTaskGroup(_currentTaskGroupIntDmrId, account.dmrId);
}

const _pocTaskGroupsKey = '_pocTaskGroupsKey';

Future<int?> loadTaskGroup(String dmrId) async {
  try {
    String key = '$_pocTaskGroupsKey-$dmrId';
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key);
  } catch (e) {
    logger.e('taskGroup loadTaskGroup error: $e');
    return null;
  }
}

Future<bool> _storeTaskGroup(int data, String dmrId) async {
  String key = '$_pocTaskGroupsKey-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setInt(key, data);
}

Future<bool> _removeTaskGroup(String dmrId) async {
  String key = '$_pocTaskGroupsKey-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.remove(key);
}

const _myDefaultSendGroupDmrIdKey = 'myDefaultSendGroupDmrId';

Future<bool> _storeMyDefaultSendGroupDmrId(int defaultSendGroupDmrId, String dmrId) async {
  String key = '$_myDefaultSendGroupDmrIdKey-$dmrId';
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setString(key, '$defaultSendGroupDmrId');
}

Future<int> loadMyDefaultSendGroupDmrId(String dmrId) async {
  try {
    String key = '$_myDefaultSendGroupDmrIdKey-$dmrId';
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? json = prefs.getString(key);
    if (json == null) {
      return 0;
    }
    return int.parse(json);
  } catch (e) {
    logger.e('load myDefaultSendGroupDmrId error: $e');
    return 0;
  }
}

// 默认的返回键事件点击处理
// 当前只处理了取消回呼目标
KeyEventResult? defaultGoBackOnKeyDownEvent(WidgetRef ref, [KeyEvent? event]) {
  var callTargetNotifier = ref.read(callTargetProvider.notifier);
  var callTargetInfo = ref.read(callTargetProvider);
  if (callTargetInfo.callBackSpeakTarget.isNotEmpty) {
    callTargetNotifier.removeCallBackTargetSnackBar();
    return KeyEventResult.handled;
  }
  return null;
}

int getPriority(Contact contact, List<int> priorityList) {
  if (priorityList.contains(contact.intDmrId)) {
    return 1;
  }
  return 0;
}

// 默认的排序联系人
void defaultSortContacts(List<Contact> contacts, {List<int> listenGroups = const [], List<int> onlineDevices = const []}) {
  contacts.sort((a, b) {
    // 根据优先级列表排序，优先显示在线
    int priorityA = getPriority(a, a.isGroup ? listenGroups : onlineDevices);
    int priorityB = getPriority(b, b.isGroup ? listenGroups : onlineDevices);

    if (priorityA != priorityB) {
      return priorityB - priorityA;
    }

    // 优先根据是否是组呼排序（组呼优先）
    if (a.isGroup != b.isGroup) {
      return a.isGroup ? -1 : 1;
    }

    // 根据 dmrId 排序
    return a.dmrId.compareTo(b.dmrId);
  });
}

// 在线终端
@Riverpod(keepAlive: true)
class OnlineDeviceDmrIds extends _$OnlineDeviceDmrIds {
  @override
  List<int> build() {
    return <int>[];
  }

  void update(List<int> dmrIds) {
    state = dmrIds;
  }

  void add(int dmrId) {
    state = [...state, dmrId];
  }

  void addAll(List<int> dmrIds) {
    Set<int> set = {...state, ...dmrIds};
    state = set.toList();
  }

  void remove(int dmrId) {
    state = state.where((id) => id != dmrId).toList();
  }

  void clear() {
    state = [];
  }
}

class CallTargetInfo {
  String focusCallDmrId;
  String defaultCallDmrId;
  String callBackSpeakTarget;

  CallTargetInfo({
    required this.focusCallDmrId,
    required this.defaultCallDmrId,
    required this.callBackSpeakTarget,
  });

  String get callTarget => callBackSpeakTarget.isEmpty ? (focusCallDmrId.isEmpty ? defaultCallDmrId : focusCallDmrId) : callBackSpeakTarget;

  int get defaultCallDmrIdInt => toIntDmrId(defaultCallDmrId);

  int get callBackSpeakTargetInt => toIntDmrId(callBackSpeakTarget);

  CallTargetInfo copyWithCallTarget({String? focusCallDmrId, String? defaultCallDmrId, String? callBackSpeakTarget}) {
    return CallTargetInfo(
      focusCallDmrId: focusCallDmrId ?? this.focusCallDmrId,
      defaultCallDmrId: defaultCallDmrId ?? this.defaultCallDmrId,
      callBackSpeakTarget: callBackSpeakTarget ?? this.callBackSpeakTarget,
    );
  }
}

// 呼叫目标
@Riverpod(keepAlive: true)
class CallTarget extends _$CallTarget {
  Timer? _timer;
  ScaffoldFeatureController<SnackBar, SnackBarClosedReason>? _snackBarController;

  @override
  CallTargetInfo build() {
    return CallTargetInfo(
      focusCallDmrId: '',
      defaultCallDmrId: '',
      callBackSpeakTarget: '',
    );
  }

  void update(CallTargetInfo callTargetInfo) {
    state = callTargetInfo;
  }

  void updateDefaultCallDmrId(String dmrId) {
    state = state.copyWithCallTarget(defaultCallDmrId: dmrId);
    var account = ref.read(accountProvider);
    _storeMyDefaultSendGroupDmrId(toIntDmrId(dmrId), account.dmrId);
  }

  void ttsSpeakGroupName(String groupName) {
    // 播放切换组呼联系人的语音
    PlatformChannel.speakText(enterTxGroup.i18n.fill([groupName]), mediaIdle: true, queueMode: TTSQueueMode.flush);
  }

  void ttsSpeakCurrentDefaultCallDmrId() {
    var allContactsProvider = ref.read(contactsProvider.notifier);
    var contact = allContactsProvider.lookupContact(state.defaultCallDmrId);
    if (contact == null) {
      return;
    }
    ttsSpeakGroupName(contact.name);
  }

  void resetDefaultCallDmrId() {
    // 任务组
    if (currentTaskGroupDmrId.isNotEmpty) {
      updateDefaultCallDmrId(currentTaskGroupDmrId);
      return;
    }

    if (defaultGroup?.defaultSendGroupDmrid == null) {
      return;
    }

    updateDefaultCallDmrId(toHexDmrId(defaultGroup!.defaultSendGroupDmrid));
  }

  // 以下是回呼叫目标相关方法
  void updateCallBackSpeakTarget(int sourceDmrId, int targetDmrId, String sourceName, String targetName) {
    var targetHexDmrId = toHexDmrId(targetDmrId);
    // 如果target是组呼，则回呼目标为target
    if (checkDmrIdIsGroup(targetDmrId)) {
      state = state.copyWithCallTarget(callBackSpeakTarget: targetHexDmrId);
      _setRemoveByExpire(targetName);
      return;
    }
    // 不是组呼，就是本机被呼，则回呼目标为source
    state = state.copyWithCallTarget(callBackSpeakTarget: toHexDmrId(sourceDmrId));
    _setRemoveByExpire(sourceName);
  }

  void _setRemoveByExpire(String callBackName) {
    _showCountdownSnackBar(rootNavigatorKey.currentContext!);
  }

  void _showCountdownSnackBar(BuildContext context) {
    int countdown = 5; // 倒计时秒数
    _timer?.cancel();
    _snackBarController = showSnackBarWithOption(StatefulBuilder(
      builder: (BuildContext context, StateSetter setState) {
        // 使用 Timer 每秒更新倒计时
        if (_timer != null) {
          return Text(callBackInfo.i18n.fill([countdown]));
        }

        _timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
          if (countdown > 0) {
            setState(() {
              countdown--;
            });
          } else {
            // 倒计时结束，取消 Timer
            timer.cancel();
            _timer = null;
            removeCallBackTargetSnackBar();
          }
        });

        return Text(callBackInfo.i18n.fill([countdown]));
      },
    ), duration: Duration(seconds: countdown), type: SnackBarMessageType.info);
    _snackBarController!.closed.then((value) {
      _snackBarController = null;
      _timer?.cancel();
      _timer = null;
      state = state.copyWithCallTarget(callBackSpeakTarget: '');
    });
  }

  void removeCallBackTargetSnackBar() {
    state = state.copyWithCallTarget(callBackSpeakTarget: '');
    _timer?.cancel();
    _timer = null;
    _snackBarController?.close();
    _snackBarController = null;
  }

  void updateFocusCallDmrId(String dmrId) {
    state = state.copyWithCallTarget(focusCallDmrId: dmrId);
  }
}
