// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contacts.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$singleCallContactListHash() =>
    r'4376829cf33607d7c8bf368a7db5cc28161b6d66';

/// See also [singleCallContactList].
@ProviderFor(singleCallContactList)
final singleCallContactListProvider =
    AutoDisposeProvider<List<Contact>>.internal(
  singleCallContactList,
  name: r'singleCallContactListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$singleCallContactListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SingleCallContactListRef = AutoDisposeProviderRef<List<Contact>>;
String _$groupCallContactListHash() =>
    r'0c824502eed1e6170027443238d712af6ba03937';

/// See also [groupCallContactList].
@ProviderFor(groupCallContactList)
final groupCallContactListProvider =
    AutoDisposeProvider<List<Contact>>.internal(
  groupCallContactList,
  name: r'groupCallContactListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$groupCallContactListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef GroupCallContactListRef = AutoDisposeProviderRef<List<Contact>>;
String _$contactsHash() => r'9439545b555f41c294d9c1dbf299726f1d4667be';

/// See also [Contacts].
@ProviderFor(Contacts)
final contactsProvider = NotifierProvider<Contacts, List<Contact>>.internal(
  Contacts.new,
  name: r'contactsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$contactsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Contacts = Notifier<List<Contact>>;
String _$listenGroupsHash() => r'8adf9baf6308581f5269312dd5e7e7d94489dd54';

/// See also [ListenGroups].
@ProviderFor(ListenGroups)
final listenGroupsProvider = NotifierProvider<ListenGroups, List<int>>.internal(
  ListenGroups.new,
  name: r'listenGroupsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$listenGroupsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ListenGroups = Notifier<List<int>>;
String _$onlineDeviceDmrIdsHash() =>
    r'cf041618b401e***************************';

/// See also [OnlineDeviceDmrIds].
@ProviderFor(OnlineDeviceDmrIds)
final onlineDeviceDmrIdsProvider =
    NotifierProvider<OnlineDeviceDmrIds, List<int>>.internal(
  OnlineDeviceDmrIds.new,
  name: r'onlineDeviceDmrIdsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$onlineDeviceDmrIdsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OnlineDeviceDmrIds = Notifier<List<int>>;
String _$callTargetHash() => r'35bea3a1815feb0d7e8e2b0e63536f92dfdbc286';

/// See also [CallTarget].
@ProviderFor(CallTarget)
final callTargetProvider =
    NotifierProvider<CallTarget, CallTargetInfo>.internal(
  CallTarget.new,
  name: r'callTargetProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$callTargetHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CallTarget = Notifier<CallTargetInfo>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
