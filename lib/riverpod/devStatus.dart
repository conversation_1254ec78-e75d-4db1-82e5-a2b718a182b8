import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../util/devStatus.dart';

part 'devStatus.g.dart';

@Riverpod(keepAlive: true)
class DevStatusManager extends _$DevStatusManager {
  DevStatus get devStatus => state;

  @override
  DevStatus build() {
    return DevStatus.createDefaultDevStatus();
  }

  Future<void> update(DevStatus devStatus) async {
    state = devStatus;
  }

  Future<void> updateWithBytes(List<int> bytes) async {
    await update(DevStatus.fromBytes(bytes));
  }
}
