// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'devStatus.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$devStatusManagerHash() => r'da4f7f520686205f77790644e027bc81799552b7';

/// See also [DevStatusManager].
@ProviderFor(DevStatusManager)
final devStatusManagerProvider =
    NotifierProvider<DevStatusManager, DevStatus>.internal(
  DevStatusManager.new,
  name: r'devStatusManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$devStatusManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DevStatusManager = Notifier<DevStatus>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
