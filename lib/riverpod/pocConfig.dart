import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../app_proto/bf8100.pb.dart';
import '../util/sqlite.dart';

part 'pocConfig.g.dart';

@Riverpod(keepAlive: true)
class PocConfigStore extends _$PocConfigStore {
  PocConfig get config => state;

  bool get canEditSubscriptionLocal => state.canEditSubscriptionLocal == 1;

  @override
  PocConfig build() {
    // 查询数据库中是否存在poc配置，没有使用默认的配置
    var config = PocConfigTable.query();
    if (config == null) {
      return PocConfig(canEditSubscriptionLocal: 0);
    }
    return config;
  }

  void update(PocConfig pocConfig) {
    state = pocConfig;
    PocConfigTable.updateOrInsert(pocConfig);
  }
}
