// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pocConfig.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pocConfigStoreHash() => r'9fcd2de83448d7fe70f8d5c4fc71c15fe632fec4';

/// See also [PocConfigStore].
@ProviderFor(PocConfigStore)
final pocConfigStoreProvider =
    NotifierProvider<PocConfigStore, PocConfig>.internal(
  PocConfigStore.new,
  name: r'pocConfigStoreProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pocConfigStoreHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PocConfigStore = Notifier<PocConfig>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
