// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'serverConnect.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$serverConnectHash() => r'd922930f443ec3413887eaf028314c00b1079b59';

/// See also [ServerConnect].
@ProviderFor(ServerConnect)
final serverConnectProvider = NotifierProvider<ServerConnect, bool>.internal(
  ServerConnect.new,
  name: r'serverConnectProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$serverConnectHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ServerConnect = Notifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
