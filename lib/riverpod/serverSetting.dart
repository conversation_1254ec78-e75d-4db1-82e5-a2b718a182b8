import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../app_proto/app_proto.pb.dart';
import '../util/logger.dart';

part 'serverSetting.g.dart';

const _serverInfoKey = 'server_addr';

Future<bool> _store(server_addr info) async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.setString(_serverInfoKey, info.writeToJson());
}

Future<server_addr?> _load() async {
  try {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? json = prefs.getString(_serverInfoKey);
    if (json == null) {
      return null;
    }
    return server_addr.fromJson(json);
  } catch (e) {
    logger.e('server_addr _load error: $e');
    return null;
  }
}

Future<bool> _remove() async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.remove(_serverInfoKey);
}

bool _isInit = false;

// 默认的发射组，十六进制DMRID
@Riverpod(keepAlive: true)
class ServerSetting extends _$ServerSetting {
  bool get isInit => _isInit;

  bool get isEmpty => state.host.isEmpty || state.port == 0;

  bool _isUpdateServerSetting = false;

  bool get isUpdateServerSetting => _isUpdateServerSetting;

  @override
  server_addr build() {
    return server_addr(
      host: 't2.bfdx.net',
      port: 2235,
    );
  }

  // 缓存到本地，以便读取
  void update(server_addr info) {
    state = info;
    _store(info);
  }

  void reset() {
    state = server_addr();
    _remove();
  }

  // 从本地存储中读取
  Future<void> initSetting() async {
    if (_isInit) {
      return;
    }

    _isInit = true;
    server_addr? info = await _load();
    logger.d('init server setting info: $info');
    if (info != null) {
      state = info;
    }
  }

  void updateIsUpdateServerSetting(bool isUpdate) {
    _isUpdateServerSetting = isUpdate;
  }
}
