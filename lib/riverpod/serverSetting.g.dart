// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'serverSetting.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$serverSettingHash() => r'bf0605f41d2e9ed7338e12cd2d64935223948fd7';

/// See also [ServerSetting].
@ProviderFor(ServerSetting)
final serverSettingProvider =
    NotifierProvider<ServerSetting, server_addr>.internal(
  ServerSetting.new,
  name: r'serverSettingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$serverSettingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ServerSetting = Notifier<server_addr>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
