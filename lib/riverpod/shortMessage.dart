import 'dart:math';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../app_proto/app_proto.pb.dart';
import '../util/sqlite.dart';

part 'shortMessage.g.dart';

@Riverpod(keepAlive: true)
class ShortMessage extends _$ShortMessage {
  @override
  List<short_messages> build() {
    return DevSmsTable.query();
  }

  void addSms(short_messages sms) {
    state = [...state, sms];
    DevSmsTable.insert(sms);
  }

  void removeSms(short_messages sms) {
    state = [...state.where((item) => item != sms)];
    DevSmsTable.delete(sms);
  }
}

// 初始化请求序号为随机数，后面递增
const int maxSmsNo = 0xFFFF;
int _seqNo = Random().nextInt(maxSmsNo);

int nextSmsNo() {
  if (_seqNo >= maxSmsNo) {
    _seqNo = 0;
  }
  return _seqNo++;
}

// 短信类型
class SmsType {
  static const int normal = 0;
  static const int autoPlay = 1;
}
