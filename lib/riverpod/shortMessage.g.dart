// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shortMessage.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$shortMessageHash() => r'e4a1c885adf1cfc00db8f768761968fe0799c326';

/// See also [ShortMessage].
@ProviderFor(ShortMessage)
final shortMessageProvider =
    NotifierProvider<ShortMessage, List<short_messages>>.internal(
  ShortMessage.new,
  name: r'shortMessageProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$shortMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ShortMessage = Notifier<List<short_messages>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
