import 'dart:async';

import 'package:bf8100deviceapp/riverpod/contacts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'app_proto/app_proto.pb.dart';
import 'page/contacts.dart';
import 'page/home.dart';
import 'page/location.dart';
import 'page/login.dart';
import 'page/menu.dart';
import 'page/serverSetting.dart';
import 'page/smsPages.dart';
import 'riverpod/account.dart';
import 'util/customKey.dart';
import 'util/logger.dart';
import 'util/platformChannel.dart';

final rootNavigatorKey = GlobalKey<NavigatorState>();
const loginRouterPath = '/login';
const homeRouterPath = '/home';
const userInfoRouterPath = '/userInfo';
const singleCallContactRouterPath = '/singleCallContact';
const groupCallContactRouterPath = '/groupCallContact';
const serverSettingRouterPath = '/serverSetting';
const menuRouterPath = '/menu';
const displaySmsRouterPath = '/displaySms';
const editSmsRouterPath = '/editSms';
const sendSmsTargetSelectionRouterPath = '/sendSmsTargetSelection';
const smsInboxRouterPath = '/smsInbox';
const locationRouterPath = '/location';

String currentRouterPath = homeRouterPath;

// 通讯录页面List
List<String> addressBookPathList = [singleCallContactRouterPath, groupCallContactRouterPath];

final GoRouter router = GoRouter(
  navigatorKey: rootNavigatorKey,
  initialLocation: homeRouterPath,
  routes: <GoRoute>[
    GoRoute(
      path: loginRouterPath,
      name: 'login',
      builder: (context, state) => OnKeyEventWrapper(key: state.pageKey, child: const LoginPage()),
    ),
    GoRoute(
      path: serverSettingRouterPath,
      name: 'serverSetting',
      builder: (context, state) {
        var extra = state.extra as ServerSettingRouterExtra?;
        return OnKeyEventWrapper(key: state.pageKey, child: ServerSettingPage(isFromHomePage: extra?.isFromHomePage ?? false));
      },
    ),
    GoRoute(
      path: homeRouterPath,
      name: 'home',
      builder: (context, state) => OnKeyEventWrapper(key: state.pageKey, child: const POCHome()),
    ),
    GoRoute(
      path: userInfoRouterPath,
      name: 'userInfo',
      builder: (context, state) => OnKeyEventWrapper(key: state.pageKey, child: const UserInfoPage()),
    ),
    GoRoute(
      path: singleCallContactRouterPath,
      name: 'singleCallContact',
      builder: (context, state) => OnKeyEventWrapper(key: state.pageKey, child: const SingleCallContactPage()),
    ),
    GoRoute(
      path: groupCallContactRouterPath,
      name: 'groupCallContact',
      builder: (context, state) => OnKeyEventWrapper(key: state.pageKey, child: const GroupCallContactPage()),
    ),
    GoRoute(
      path: menuRouterPath,
      name: 'menu',
      builder: (context, state) => OnKeyEventWrapper(key: state.pageKey, child: const POCMenu()),
    ),
    GoRoute(
      path: displaySmsRouterPath,
      name: displaySmsRouterPath.substring(1),
      builder: (context, state) {
        var extra = state.extra as SmsRouterExtra;
        return OnKeyEventWrapper(key: state.pageKey, child: DisplaySmsPage(sms: extra.sms!));
      },
    ),
    GoRoute(
      path: editSmsRouterPath,
      name: editSmsRouterPath.substring(1),
      builder: (context, state) {
        return OnKeyEventWrapper(key: state.pageKey, child: EditSmsPage(extra: state.extra as SmsRouterExtra));
      },
    ),
    GoRoute(
      path: sendSmsTargetSelectionRouterPath,
      name: sendSmsTargetSelectionRouterPath.substring(1),
      builder: (context, state) {
        return OnKeyEventWrapper(key: state.pageKey, child: SendSmsTargetSelectionPage(extra: state.extra as SmsRouterExtra));
      },
    ),
    GoRoute(
      path: smsInboxRouterPath,
      name: smsInboxRouterPath.substring(1),
      builder: (context, state) {
        // var extra = state.extra as SmsRouterExtra;
        return OnKeyEventWrapper(key: state.pageKey, child: const SmsInboxPage());
      },
    ),
    GoRoute(
      path: locationRouterPath,
      name: locationRouterPath.substring(1),
      builder: (context, state) {
        var extra = state.extra as LocationRouterExtra;
        return OnKeyEventWrapper(key: state.pageKey, child: LocationPage(extra: extra));
      },
    ),
  ],
  redirect: (BuildContext context, GoRouterState state) async {
    final ref = ProviderScope.containerOf(context);

    // 当前页面为通讯录页面，跳转到其他页面，需要清除"聚焦呼叫目标",
    if (addressBookPathList.contains(currentRouterPath) && !addressBookPathList.contains(state.matchedLocation)) {
      ref.read(callTargetProvider.notifier).updateFocusCallDmrId('');
    }

    final accountNotifier = ref.read(accountProvider.notifier);
    await accountNotifier.initAccount();
    final account = ref.read(accountProvider);
    // 未登录且满足自动登录的条件即为正在自动登录
    if (!account.isLogin && account.dmrId.isNotEmpty && (account.password.isNotEmpty || account.sessionId.isNotEmpty)) {
      currentRouterPath = state.matchedLocation;
      return null;
    }
    // 未登录 不满足自动登录的条件 点击首页切换到登录页
    if (currentRouterPath != homeRouterPath && !account.isLogin && state.matchedLocation == homeRouterPath) {
      currentRouterPath = loginRouterPath;
      return loginRouterPath;
    }
    currentRouterPath = state.matchedLocation;
    return null;
  },
);

// 通过context创建一个container
ProviderContainer createContainerWithRooContext() {
  final ProviderContainer container = ProviderScope.containerOf(rootNavigatorKey.currentContext!);
  return container;
}

void _onUniproHotkeyAction(BuildContext context, UniproHotkeyAction action) {
  switch (action) {
    case UniproHotkeyAction.homeDown:
      var ref = createContainerWithRooContext();
      var account = ref.read(accountProvider);
      if (account.isLogin) {
        context.go(homeRouterPath);
        break;
      }
      String currentRoute = ModalRoute.of(context)?.settings.name ?? '';
      if ("/$currentRoute" != homeRouterPath) {
        context.go(loginRouterPath);
      }
      break;
    case UniproHotkeyAction.callDown:
      jumpMenu = true;
      context.push(menuRouterPath);
      break;
    case UniproHotkeyAction.p1Down:
      break;
    case UniproHotkeyAction.p2Down:
      try {
        final container = ProviderScope.containerOf(context);
        final isLogin = container.read(accountProvider).isLogin;
        if (!isLogin) {
          return;
        }
        context.go(singleCallContactRouterPath);
      } catch (e) {
        logger.w("_onUniproHotkeyAction p2Down catch: $e");
      }
      break;
    case UniproHotkeyAction.p3Down:
      try {
        final container = ProviderScope.containerOf(context);
        final isLogin = container.read(accountProvider).isLogin;
        if (!isLogin) {
          return;
        }
        context.go(groupCallContactRouterPath);
      } catch (e) {
        logger.w("_onUniproHotkeyAction p3Down catch: $e");
      }
      break;
    default:
    // logger.w("_onUniproHotkeyAction unknown action: $action");
  }
}

// 封装按键定义的组件
class OnKeyEventWrapper extends StatefulWidget {
  const OnKeyEventWrapper({super.key, required this.child});

  final Widget child;

  @override
  State<OnKeyEventWrapper> createState() => _OnKeyEventWrapperState();
}

class _OnKeyEventWrapperState extends State<OnKeyEventWrapper> {
  late final StreamSubscription<UniproHotkeyAction> _subscription;

  @override
  void initState() {
    super.initState();

    _initAndroidKeyEvent();
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }

  _initAndroidKeyEvent() {
    _subscription = PlatformChannel.hotkeyActionStream.listen((action) {
      if (!mounted) return;
      _onUniproHotkeyAction(context, action);
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

class ServerSettingRouterExtra {
  ServerSettingRouterExtra({this.isFromHomePage = false});

  final bool isFromHomePage;
}

class SmsRouterExtra {
  SmsRouterExtra({this.sms, this.onSelectTarget, this.isNewSms = false});

  final short_messages? sms;
  final bool isNewSms;

  final void Function(String? dmrId)? onSelectTarget;
}

class LocationRouterExtra {
  LocationRouterExtra({required this.location});

  final gps84 location;
}
