import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../app_proto/bf8100.pb.dart';
import '../i18n/locale.dart';
import '../page/location.dart' show isLocationPageOpen, locationUpdateController;
import '../riverpod/alarm.dart';
import '../riverpod/contacts.dart';
import '../riverpod/serverConnect.dart';
import '../router.dart';
import '../util/bf8100Util.dart';
import '../util/customKey.dart';
import '../util/logger.dart';
import '../util/platformChannel.dart';
import '../util/snackBarMessage.dart';
import 'rpcCmd.dart';

StreamSubscription<UniproHotkeyAction>? _funcKeyEventSubscription;

class PttCallStatus {
  // 是否正在呼叫，从ppt按住为true，到松开才会变为false
  bool isCalling = false;

  // 正在收听bc15指令的语音标识
  bool isBc15Calling = false;

  String bc15CallInfo = '';

  // bc15呼叫的超时定时器
  Timer? bc15CallTimer;

  ScaffoldFeatureController<SnackBar, SnackBarClosedReason>? bc15SnackBar;

  // 当前语音已经收到了cb75,且已经执行了打断提示
  bool cb75IsInterruptOk = false;
  ScaffoldFeatureController<SnackBar, SnackBarClosedReason>? cb75SnackBarController;

  void cancelCb75Message() {
    cb75SnackBarController?.close();
  }

  void clearBc15CallTimer() {
    bc15CallTimer?.cancel();
    bc15CallTimer = null;
  }

  void callEnd() {
    isCalling = false;
    cb75IsInterruptOk = false;
  }

  void callStart() {
    isCalling = true;
    cb75IsInterruptOk = false;
  }

  void reset() {
    isCalling = false;

    isBc15Calling = false;
    bc15CallInfo = '';
    bc15SnackBar = null;
    clearBc15CallTimer();

    cb75IsInterruptOk = false;
    cancelCb75Message();
  }
}

final PttCallStatus pttCallStatus = PttCallStatus();

// 开始呼叫，先判断是否被禁发
Future<void> _onPttDown() async {
  if (alarmController.isAlarmingCall) {
    return;
  }

  // 如果已经在呼叫中，则忽略
  if (pttCallStatus.isCalling) {
    return;
  }

  var ref = createContainerWithRooContext();
  var callTargetInfo = ref.read(callTargetProvider);
  // 先标记正在呼叫，得到请求结果后再判断是否呼叫成功
  pttCallStatus.callStart();
  var (cb71Result, err) = await reqSpeakStart(callTargetInfo.callTarget);
  if (err != null) {
    logger.e('reqSpeakStart error: $err');
    showSnackBarWithOption(Text(err.message), type: SnackBarMessageType.error);
    PlatformChannel.speakText(err.message, queueMode: TTSQueueMode.flush);
    pttCallStatus.callEnd();
    return;
  }

  logger.d('cb71Result: $cb71Result');
  pttCallStatus.bc15SnackBar = null;
  var isCalling = _processSpeakResult(cb71Result!);
  if (!pttCallStatus.isCalling && isCalling) {
    pttCallStatus.isCalling = true;
    onPttUp();
    return;
  }
  pttCallStatus.isCalling = isCalling;
}

// 结束呼叫
Future<void> onPttUp() async {
  // 不在呼叫中，则不处理
  if (!pttCallStatus.isCalling) {
    return;
  }

  // 结束呼叫
  var err = await reqSpeakStop();
  if (err != null) {
    logger.w('reqSpeakStop error: $err');
    return;
  }

  // 清除当前呼叫的状态提示
  // 如果是否被抢断后松开ptt，则显示抢断的呼叫信息
  if (pttCallStatus.isBc15Calling) {
    showSnackBarWithBc15CallInfo();
  } else {
    removeCurrentSnackBar();
  }
  pttCallStatus.callEnd();
}

// sos点击次数
int _sosCount = 0;
// 1s内点击两次才算双击
Timer? _sosTimer;
// 报警状态控制器
AlarmController alarmController = AlarmController();

Future<void> _onSosDown() async {
  if (alarmController.isAlarmingCall) {
    return;
  }
  _sosCount++;
  if (_sosCount == 1) {
    _sosTimer = Timer(const Duration(seconds: 1), () {
      _sosCount = 0;
    });
  }

  if (_sosCount == 2) {
    var network = checkServerConnectAndShowSnackBar();
    if (!network) {
      return;
    }
    var err = await sendAlarm();
    if (err != null) {
      if (err.message == 'isNotEnable') {
        PlatformChannel.speakText(emergencyAlarmIsNotEnable.i18n);
        showSnackBarWithOption(Text('${alarmFailed.i18n}：${emergencyAlarmIsNotEnable.i18n}'), type: SnackBarMessageType.warning);
      } else {
        showSnackBarWithOption(Text('${alarmFailed.i18n}：$err'), type: SnackBarMessageType.warning);
      }
      return;
    }
    // 收到报警回复，开始报警状态
    alarmController.startAlarm();
    _sosCount = 0;
    _sosTimer?.cancel();
  }
}

Future<void> _cancelSos() async {
  if (alarmController.isAlarming) {
    var err = await reqSpeakStop();
    if (err != null) {
      logger.w('reqSpeakStop error: $err');
      return;
    }
    alarmController.cancelLocalSos();
  }
}

bool checkServerConnectAndShowSnackBar({bool showBar = true}) {
  var ref = createContainerWithRooContext();
  var serverConnect = ref.read(serverConnectProvider);
  if (showBar && !serverConnect) {
    showSnackBarWithOption(Text(serverDisconnected.i18n), type: SnackBarMessageType.error);
  }
  return serverConnect;
}

// 上报位置信息，force=1表示重新定位后上报
Future<void> _onReportLocation({int force = 1}) async {
  var timeout = const Duration(seconds: 3 * 60);
  showSnackBarWithOption(Text(locating.i18n), type: SnackBarMessageType.info, duration: timeout);
  final location = await reportLocationOnce(force: force, timeout: timeout);
  if (location== null) {
    showSnackBarWithOption(Text(locationTimeout.i18n), type: SnackBarMessageType.error);
    PlatformChannel.speakText(locationTimeout.i18n);
    return;
  }

  showSnackBarWithOption(Text(positioningSuccess.i18n), type: SnackBarMessageType.success);
  PlatformChannel.speakText(positioningSuccess.i18n);

  // 打开一个路由页面，展示当前位置信息
  if (!isLocationPageOpen) {
    isLocationPageOpen = true;
    var extra = LocationRouterExtra(location: location);
    rootNavigatorKey.currentContext!.push(locationRouterPath, extra: extra).then((_) {
      isLocationPageOpen = false; // Reset the flag when the page is closed
    });
  } else {
    locationUpdateController.add(location);
  }
}

void _onUniproHotkeyAction(UniproHotkeyAction action) {
  switch (action) {
    case UniproHotkeyAction.headsetPttDown:
    case UniproHotkeyAction.pttDown:
      var network = checkServerConnectAndShowSnackBar();
      if (!network) {
        return;
      }
      _onPttDown();
      break;
    case UniproHotkeyAction.headsetPttUp:
    case UniproHotkeyAction.pttUp:
      var network = checkServerConnectAndShowSnackBar(showBar: false);
      if (!network) {
        return;
      }
      onPttUp();
      break;
    case UniproHotkeyAction.vbDown:
      _onReportLocation(force: 1);
      break;
    case UniproHotkeyAction.vbLong:
      break;
    case UniproHotkeyAction.sosDown:
      _onSosDown();
      break;
    case UniproHotkeyAction.sosLong:
      var network = checkServerConnectAndShowSnackBar(showBar: false);
      if (!network) {
        return;
      }
      _cancelSos();
      break;
    default:
    // logger.w("_onUniproHotkeyAction unknown action: $action");
  }
}

// 登录后，再处理ptt，sos, vb等功能按键事件
void onFuncKeyEvent() {
  _funcKeyEventSubscription?.cancel();
  _funcKeyEventSubscription = PlatformChannel.hotkeyActionStream.listen((action) {
    _onUniproHotkeyAction(action);
  });
}

final Map<int, String> _rejectReason = {
  0x80: targetNotOnline.i18n,
  0x81: targetInCalling.i18n,
  0x82: relayChannelBusy.i18n,
  0x83: preemptedTimeSloResources.i18n,
  0x84: higherPriorityTerminalCalling.i18n,
  0x85: callResourcesHaveReleased.i18n,
  0x86: loginFirst.i18n,
  0x87: noTelephoneGatewayAvailable.i18n,
  0x88: telephoneGatewayBusy.i18n,
  0x89: inPhoneBlacklist.i18n,
  0x8A: sysAuthorExpired.i18n,
  0x90: databaseQueryError.i18n,
  0x91: databaseQueryError.i18n,
  0x92: wrongPhoneNumber.i18n,
  0x93: loginNumberInconsistentWithCallRightNumber.i18n,
  0x94: temporaryOrTaskGroupHasExpired.i18n,
};

// bool _isCanSpeaking(int result) {
//   return [0x00, 0x01, 0x02, 0x03].contains(result);
// }

String _getSpeakResultLabel(int result) {
  var resultLabel = _rejectReason[result];
  return '$resultLabel';
}

void showSnackBarWithBc15CallInfo() {
  if (pttCallStatus.bc15SnackBar != null || pttCallStatus.bc15CallInfo.isEmpty) {
    return;
  }
  pttCallStatus.bc15SnackBar = showSnackBarWithOptionWithNLines(pttCallStatus.bc15CallInfo, type: SnackBarMessageType.success, duration: const Duration(seconds: 60));
}

// 可以呼叫返回true，不可以呼叫返回false
bool _processSpeakResult(cb71 cb71Result) {
  switch (cb71Result.result) {
    case 0x00:
    case 0x01:
    case 0x02:
    case 0x03:
      var ref = createContainerWithRooContext();
      var callTargetNotifier = ref.read(callTargetProvider.notifier);
      var callTargetInfo = ref.read(callTargetProvider);
      if (callTargetInfo.callBackSpeakTarget.isNotEmpty) {
        callTargetNotifier.removeCallBackTargetSnackBar();
      }
      String targetDmrId = toHexDmrId(cb71Result.targetDmrid);
      var contactProvider = ref.read(contactsProvider.notifier);
      var target = contactProvider.lookupContact(targetDmrId);
      String text = '';
      String targetName = '';
      if (target != null) {
        text = target.isGroup ? callGroup.i18n : callSingle.i18n;
        targetName = target.name;
      } else {
        var noPermissionDevice = noPermissionDevices[targetDmrId];
        text = callSingle.i18n;
        targetName = noPermissionDevice?.selfId ?? targetDmrId;
      }
      // todo duration可以修改为单次讲话超时时间，cmd_req_set_speak_time_out_duration
      showSnackBarWithOptionWithNLines('$text: $targetName', type: SnackBarMessageType.success, duration: const Duration(seconds: 60));
      // 新的一轮呼叫开始,可以接收新一轮的cb75指令
      pttCallStatus.cb75IsInterruptOk = false;
      return true;
    default:
      var errMsg = _getSpeakResultLabel(cb71Result.result);
      showSnackBarWithOption(Text(errMsg), type: SnackBarMessageType.error, duration: const Duration(seconds: 3));
      // 如果是抢断呼叫申请话权失败结果，则不播放声音
      if (!pttCallStatus.isBc15Calling) {
        PlatformChannel.speakText(errMsg);
      } else {
        // 恢复当前呼叫的状态提示
        Future.delayed(const Duration(seconds: 3), () {
          showSnackBarWithBc15CallInfo();
        });
      }
      return false;
  }
}
