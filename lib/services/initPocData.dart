import 'package:bf8100deviceapp/riverpod/pocConfig.dart';
import 'package:flutter/material.dart';

import '../app_proto/app_db.pb.dart';
import '../app_proto/bf8100.pb.dart';
import '../i18n/locale.dart';
import '../riverpod/account.dart';
import '../riverpod/contacts.dart';
import '../router.dart';
import '../util/bf8100Util.dart';
import '../util/platformChannel.dart';
import '../util/snackBarMessage.dart';
import 'rpcCmd.dart';

Future<bool> initPocData(String settingLastUpdateTime) async {
  var ref = createContainerWithRooContext();
  final accountNotifier = ref.read(accountProvider.notifier);
  final account = ref.read(accountProvider);
  var localSettingLastUpdateTime = await loadSettingLastUpdateTime(account.dmrId);

  // 比较配置时间，是否需要请求通讯录和默认收听组
  var localLastUpdateTime = DateTime.parse(localSettingLastUpdateTime);
  var serverLastUpdateTime = DateTime.parse(settingLastUpdateTime);
  if (!serverLastUpdateTime.isAfter(localLastUpdateTime)) {
    accountNotifier.updateSettingLastUpdateTime(settingLastUpdateTime);
    return _initLocalPocData(settingLastUpdateTime);
  }

  // 需要请求数据
  return _initPocDataWithRequestServer(settingLastUpdateTime);
}

Future<bool> _initLocalPocData(String settingLastUpdateTime) async {
  var ref = createContainerWithRooContext();
  final account = ref.read(accountProvider);
  // 本地默认发射组和收听组
  var pocDefaultSendAndListenGroup = await loadPocDefaultSendAndListenGroup(account.dmrId);
  // 没有数据，重新从服务器请求
  if (pocDefaultSendAndListenGroup == null) {
    return _initPocDataWithRequestServer(settingLastUpdateTime);
  }

  updatePocDefaultGroup(pocDefaultSendAndListenGroup, account.dmrId);

  // 本地默认通讯录，如果没有则重新请求
  var pocContacts = await loadPocContacts(account.dmrId);
  if (pocContacts.isEmpty) {
    return _initPocDataWithRequestServer(settingLastUpdateTime);
  }
  var contacts = ref.read(contactsProvider.notifier);
  contacts.clearContacts();
  contacts.addContacts(pocContacts);

  // 读取本地pocConfig
  final pocConfigNotifier = ref.read(pocConfigStoreProvider.notifier);
  // 将pocConfig同步到goproxy中
  syncPocConfigToProxy(pocConfigNotifier.config);

  // 设置当前收听组
  List<int> finalListenGroup = pocDefaultSendAndListenGroup.defaultListenGroupDmrids;
  if (pocConfigNotifier.canEditSubscriptionLocal) {
    // 本地当前收听组
    List<int> localListenGroup = await loadPocCurrentListenGroup(account.dmrId);
    if (!localListenGroup.isNotEmpty) {
      finalListenGroup = localListenGroup;
    }
  }

  // 读取本地的任务组
  var taskGroup = await loadTaskGroup(account.dmrId);
  if (taskGroup != null) {
    pocDefaultSendAndListenGroup.defaultListenGroupDmrids.add(taskGroup);
    setCurrentTaskGroupDmrId(toHexDmrId(taskGroup));
  }

  var listenGroupNotifier = ref.read(listenGroupsProvider.notifier);
  listenGroupNotifier.updateGroups(finalListenGroup);

  return true;
}

Future<bool> _initPocDataWithRequestServer(String settingLastUpdateTime) async {
  var ref = createContainerWithRooContext();
  final accountNotifier = ref.read(accountProvider.notifier);
  accountNotifier.updateSettingLastUpdateTime(settingLastUpdateTime);
  // 请求默认收发组并保存在本地
  var (pocDefaultGroup, err) = await queryPocDefaultGroup();
  if (err != null) {
    PlatformChannel.speakText(queryRXTXGroupFailed.i18n);
    showSnackBarWithOption(Text(queryRXTXGroupFailed.i18n), type: SnackBarMessageType.warning);
    return false;
  }

  final account = ref.read(accountProvider);
  updatePocDefaultGroup(pocDefaultGroup!, account.dmrId);
  var callTargetNotifier = ref.read(callTargetProvider.notifier);
  var txDmrId = toHexDmrId(pocDefaultGroup.defaultSendGroupDmrid);
  callTargetNotifier.updateDefaultCallDmrId(txDmrId);

  Contact? taskGroupData;
  var contactNotifier = ref.read(contactsProvider.notifier);
  if (currentTaskGroupDmrId.isNotEmpty) {
    taskGroupData = contactNotifier.lookupContact(currentTaskGroupDmrId);
  }

  // 请求默认通讯录
  await queryContactOnData();

  // 请求POC配置，请求不到，则使用默认的配置
  final (pocConfig, err2) = await queryPocConfig();
  if (err2 == null) {
    final pocConfigNotifier = ref.read(pocConfigStoreProvider.notifier);
    pocConfigNotifier.update(pocConfig!);
  }
  // 本地存在任务组，但是没有在通讯录中，则添加到通讯录
  // 8100系统的poc配置中, 可以把任务组选中加入到通讯录中
  var taskGroupFromContacts = contactNotifier.lookupContact(currentTaskGroupDmrId);
  if (taskGroupData != null && taskGroupFromContacts == null) {
    contactNotifier.addContact(taskGroupData);
    pocDefaultGroup.defaultListenGroupDmrids.add(toIntDmrId(taskGroupData.originData.dmrId));
    setCurrentTaskGroupDmrId(taskGroupData.originData.dmrId);
  }
  // 退出登录后，服务器已经清除当前收听组数据，直接使用默认的收听组
  var listenGroupNotifier = ref.read(listenGroupsProvider.notifier);
  listenGroupNotifier.updateGroups(pocDefaultGroup.defaultListenGroupDmrids);

  return true;
}

// 请求通讯录
Future<void> queryContactOnData() async {
  Set<String> contactsDmrIdSet = <String>{};
  var isClear = false;
  var ref = createContainerWithRooContext();
  var contacts = ref.read(contactsProvider.notifier);

  void processContact(rpc_cmd rpcCmd) {
    if (!isClear) {
      contacts.clearContacts();
      isClear = true;
    }

    List<Contact> c = [];
    if (rpcCmd.paraInt == 1) {
      c = db_org_list.fromBuffer(rpcCmd.body).rows.map<Contact>((e) => Contact<db_org>(originData: e, dataType: 1)).where((e) => !contactsDmrIdSet.contains(e.dmrId)).toList();
    }
    if (rpcCmd.paraInt == 2) {
      c = db_device_list.fromBuffer(rpcCmd.body).rows.map<Contact>((e) => Contact<db_device>(originData: e, dataType: 2)).where((e) => !contactsDmrIdSet.contains(e.dmrId)).toList();
    }
    contactsDmrIdSet.addAll(c.map((e) => e.dmrId));
    contacts.addContacts(c);
  }

  await queryContact(processContact);
}
