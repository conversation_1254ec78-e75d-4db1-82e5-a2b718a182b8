import 'dart:async';
import 'dart:math' show Random;

import 'package:bf8100deviceapp/app_proto/app_config.pb.dart';
import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../app_proto/app_db.pb.dart';
import '../app_proto/app_proto.pb.dart';
import '../app_proto/app_proto.pb.dart' as app_proto;
import '../app_proto/bf8100.pb.dart';
import '../app_proto/bf_radio.pb.dart' hide bc15;
import '../i18n/locale.dart';
import '../riverpod/account.dart';
import '../riverpod/contacts.dart';
import '../riverpod/devStatus.dart';
import '../riverpod/pocConfig.dart';
import '../riverpod/serverConnect.dart';
import '../riverpod/serverSetting.dart';
import '../riverpod/shortMessage.dart';
import '../router.dart';
import '../util/bf8100Util.dart';
import '../util/config.dart';
import '../util/logger.dart';
import '../util/loginUtil.dart';
import '../util/platformChannel.dart';
import '../util/snackBarMessage.dart';
import 'funcKeyEvent.dart';
import 'initPocData.dart';
import 'rpcSocket.dart' show rpcMessageStream, sendRpcCmd, sendRpcCmdWithCallback;

// 定义一个查询请求的结果类型
typedef QueryResult<T> = (T?, StateError?);

// 初始化请求序号为随机数，后面递增
const int maxSeqNo = 0x7FFFFFFF;
int _seqNo = Random().nextInt(maxSeqNo);

int nextSeqNo() {
  if (_seqNo >= maxSeqNo) {
    _seqNo = 0;
  }
  return _seqNo++;
}

// 统一创建rpc_cmd，使用默认的seqNo和sid
rpc_cmd rpcCmdWithSeqNo({cmd_code? cmd, int? sid, List<int>? body, String? paraStr}) {
  return rpc_cmd(
    cmd: cmd?.value ?? 0,
    seqNo: nextSeqNo(),
    sid: $fixnum.Int64(sid ?? 0),
    body: body,
    paraStr: paraStr ?? '',
  );
}

// 更新服务器设置
Future<bool> updateServerSetting(String host, int port) async {
  var addr = server_addr(host: host, port: port);
  var rpcCmd = rpcCmdWithSeqNo(
    cmd: cmd_code.cmd_req_update_server_addr,
    body: addr.writeToBuffer(),
  );
  final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_update_server_addr);
  return respRpcCmd.res == res_code.success.value;
}

// 请求poc通讯录
Future<StateError?> queryContact(void Function(rpc_cmd) onData) async {
  // 收到resRpcCMd.paraBin为[0],表示结束
  bool checkIsEnd(rpc_cmd rpcCmd) {
    if (rpcCmd.paraInt == 2 && rpcCmd.paraBin.isNotEmpty && rpcCmd.paraBin[0] == 0) {
      return true;
    }
    return false;
  }

  try {
    var rpcCmd = rpcCmdWithSeqNo(cmd: cmd_code.cmd_req_query_contact);
    return await sendRpcCmdWithCallback(rpcCmd, onData, responseCmdCode: cmd_code.cmd_resp_query_contact, checkIsEnd: checkIsEnd);
  } catch (e) {
    return StateError(e.toString());
  }
}

// poc 查询PocConfig
Future<QueryResult<PocConfig>> queryPocConfig() async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(cmd: cmd_code.cmd_req_query_poc_config);
    final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_query_poc_config);
    if (respRpcCmd.res != res_code.success.value) {
      return (null, StateError(respRpcCmd.paraStr));
    }
    final pocConfig = PocConfig.fromBuffer(respRpcCmd.body);
    return (pocConfig, null);
  } catch (e) {
    return (null, StateError(e.toString()));
  }
}

// 查询默认发射组和收听组
Future<QueryResult<PocDefaultGroup>> queryPocDefaultGroup() async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_req_query_poc_default_group,
    );
    final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_query_poc_default_group);
    if (respRpcCmd.res != res_code.success.value) {
      return (null, StateError(respRpcCmd.paraStr));
    }
    final pocDefaultGroup = PocDefaultGroup.fromBuffer(respRpcCmd.body);
    return (pocDefaultGroup, null);
  } catch (e) {
    return (null, StateError(e.toString()));
  }
}

// poc更新收听组
Future<StateError?> updatePocListenGroup(PocDefaultGroup pocDefaultGroup) async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_req_update_poc_listen_group,
      body: pocDefaultGroup.writeToBuffer(),
    );
    // 一次性更新
    rpcCmd.paraBin = PocSubscribleUpdateOption(frameNo: 0, frameType: 0).writeToBuffer();
    final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_update_poc_listen_group);
    if (respRpcCmd.res != res_code.success.value) {
      return StateError(respRpcCmd.paraStr);
    }
    return null;
  } catch (e) {
    return StateError(e.toString());
  }
}

// poc查询当前收听组
Future<QueryResult<PocDefaultGroup>> queryPocCurrentListenGroup() async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_req_query_poc_listen_group,
    );
    final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_query_poc_listen_group);
    if (respRpcCmd.res != res_code.success.value) {
      return (null, StateError(respRpcCmd.paraStr));
    }
    final pocDefaultGroup = PocDefaultGroup.fromBuffer(respRpcCmd.body);
    return (pocDefaultGroup, null);
  } catch (e) {
    return (null, StateError(e.toString()));
  }
}

// 申请放权结果枚举
enum Cb71ResultCode {
  // 0x00：时隙占有，准备接收语音呼叫
  slotBusyAndReadyReceiveVoice(0x00),
  // 0x01：允许联网呼叫
  allowNetCall(0x01),
  // 0x02：允许呼叫，中继与服务器断开
  allowCallButRepeaterDisconnect(0x02),
  // 0x03: 手台电话申请成功
  devicePhoneCallAccepted(0x03),
  // 0x10: 组呼并入成功，集群模式下才有
  groupCallMergedSuccessfully(0x10),
  // 0x80=128：拒绝呼叫，对方不在线
  targetNotOnline(0x80),
  // 0x81=129：拒绝呼叫，对方在通话中
  targetInCall(0x81),
  // 0x82=130：拒绝呼叫，中继信道忙
  repeaterChannelBusy(0x82),
  // 0x83=131：拒绝呼叫，被优先级更高手台抢占时隙资源
  slotIsOccupiedByHigherPriorityDevice(0x83),
  // 0x84=132: 拒绝呼叫，当前有更高优先级手台在通话中
  targetIsCallingByHigherPriorityDevice(0x84),
  // 0x85=133: 拒绝呼叫,后台已经释放了此手台的通话资源
  targetCallReleasedByServer(0x85),
  // 0x86=134: 未登录,请先登录
  notLoggedIn(0x86),
  // 0x87=135: 无电话网关可用,电话申请失败
  noPhoneGatewayAvailable(0x87),
  // 0x88=136: 电话网关忙,电话申请失败
  phoneGatewayBusy(0x88),
  // 0x89=137: 电话黑名单,电话申请失败
  phoneInBlackList(0x89),
  // 0x8A=138: 系统授权已经过期，呼叫失败
  systemAuthorizationExpired(0x8A),
  // 0x90=144:
  unknownErrorWith144(0x90),
  // 0x91=145: 后台数据库查询错误
  sqlQueryError(0x91),
  // 0x92=146: 电话号码错误
  phoneNumberError(0x92),
  // 0x93=147: 控制台登录号码和申请话权号码不一致,申请失败
  loginAccountAndPhoneNumberNotMatch(0x93),
  // 0x94=148: 拒绝呼叫，临时组或任务组已失效
  tempGroupOrTaskGroupExpired(0x94),
  // 0xA0=160: 拒绝呼叫, 归属组无其它成员在线，集群模式下才有
  svtGroupNoOtherMemberOnline(0xA0),
  // 0xA1=161: 拒绝呼叫, 手台所属中继错误，集群模式下才有
  deviceSvtGroupError161(0xA1),
  // 0xA2=162: 拒绝呼叫, 手台所属中继错误，集群模式下才有
  deviceSvtGroupError162(0x80);

  const Cb71ResultCode(this.code);

  final int code;
}

class ErrorReasonString {
  static const unknownError = "Unknown Error";
  static const invalidTarget = "Invalid Target";
  static const unsetSn = "Unset Sn";
}

// 发起呼叫
Future<QueryResult<cb71>> reqSpeakStart(String targetDmrId) async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_req_speak_start,
    );
    rpcCmd.paraStr = targetDmrId;
    final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_speak_start);
    switch (respRpcCmd.res) {
      case 0:
        return (cb71.fromBuffer(respRpcCmd.body), null);
      // case 1: // 无效的通话目标
      //   return (null, StateError(ErrorReasonString.invalidTarget));
      case 2: // 已经开始录音，cb71中仅source/target两个字段有效
        return (cb71.fromBuffer(respRpcCmd.body), null);
      case 3: // 未设置sn
        return (null, StateError(ErrorReasonString.unsetSn));
      case 4: // 当前被禁发
        return (null, StateError(bannedFromCalling.i18n));
    }
    return (null, StateError(ErrorReasonString.unknownError));
  } catch (e) {
    return (null, StateError(e.toString()));
  }
}

Future<StateError?> reqSpeakStop() async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_req_speak_stop,
    );
    final respRpcCmd = await sendRpcCmd(rpcCmd);
    if (respRpcCmd.res != res_code.success.value) {
      return StateError(respRpcCmd.paraStr);
    }
    return null;
  } catch (e) {
    return StateError(e.toString());
  }
}

// 向goproxy退出登录
Future<StateError?> loginQuit() async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_req_login_quit,
    );
    final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_login_quit);
    if (respRpcCmd.res != res_code.success.value) {
      return StateError(respRpcCmd.paraStr);
    }
    return null;
  } catch (e) {
    return StateError(e.toString());
  }
}

// 向goproxy请求不在权限内的联系人的信息
Future<QueryResult<db_device>> queryOutsidePermissionContact(String dmrId) async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_req_query_outside_permission_contact,
      paraStr: dmrId,
    );
    final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_query_outside_permission_contact);
    if (respRpcCmd.res != res_code.success.value || respRpcCmd.paraStr != dmrId) {
      return (null, StateError(respRpcCmd.paraStr));
    }
    return (db_device.fromBuffer(respRpcCmd.body), null);
  } catch (e) {
    return (null, StateError(e.toString()));
  }
}

// 发送报警
Future<StateError?> sendAlarm() async {
  try {
    var ref = createContainerWithRooContext();
    var callTargetInfo = ref.read(callTargetProvider);
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_req_send_alarm,
      paraStr: callTargetInfo.defaultCallDmrId,
    );
    var resp = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_send_alarm);
    if (resp.res != res_code.success.value) {
      return StateError(resp.paraStr);
    }
    return null;
  } catch (e) {
    return StateError(e.toString());
  }
}

// poc 查询在线终端
// cc183为空时, 查询所有的在线终端
// cc183.dmrids为需要查询在线的终端
Future<StateError?> queryOnlineDevice(void Function(rpc_cmd) onData, {cc183? c183}) async {
  bool checkIsEnd(rpc_cmd rpcCmd) {
    if (rpcCmd.paraBin.isNotEmpty && rpcCmd.paraBin[0] == 0) {
      return true;
    }
    return false;
  }

  try {
    var rpcCmd = rpcCmdWithSeqNo(cmd: cmd_code.cmd_req_query_online_contact);
    if (c183 != null) {
      rpcCmd.body = c183.writeToBuffer();
    }
    return await sendRpcCmdWithCallback(rpcCmd, onData, responseCmdCode: cmd_code.cmd_resp_query_online_contact, checkIsEnd: checkIsEnd);
  } catch (e) {
    return StateError(e.toString());
  }
}

Map<int, Function(rpc_cmd)> _globalPushEvent = {
  // 代理推送通知
  cmd_code.cmd_notify.value: _serverNotify,
  // 接收系统是否在播放语音状态
  cmd_code.cmd_resp_media_play_status.value: _processMediaPlayStatus,
  // 收到bc15指令
  cmd_code.cmd_bc15.value: _processBc15,
  // 查询通话状态
  cmd_code.cmd_resp_speak_status.value: _processSpeakStatus,
  // 收到poc通讯录更新通知
  cmd_code.cmd_notify_poc_setting_changed.value: _processPocSettingChanged,
  // 解除报警
  cmd_code.cmd_cb10.value: _processCb10,
  // 收到短信消息
  cmd_code.cmd_short_messages.value: _processShortMessages,
  // 收到服务器发送的175指令, 清除当前正在呼叫的语音信息
  175: _process175,
  // 设备状态通知
  cmd_code.cmd_notify_device_status.value: _processDeviceStatus,
  // 设备锁机状态通知
  cmd_code.cmd_notify_lock_device_status.value: _processLockDeviceStatus,
  // 强制下线通知
  cmd_code.cmd_force_exit.value: _processForceExit,
  // kcp断开连接后自动登录的结果
  cmd_code.cmd_resp_login.value: _processKcpAutoLoginResult,
};

// 0xcb36.加入临时组  0xcb37.退出临时组 0xcb38.加入任务组  0xcb39.退出任务组
const Map<int, void Function(Notify notify)> _notifyHandler = {
  0xcb36: _handlerJoinTempGroup,
  0xcb37: _handlerExitTempGroup,
  0xcb38: _handlerJoinTaskGroup,
  0xcb39: _handlerExitTaskGroup,
  334: _handlerSyncOnlineOffline,
  5: _handleKcpClose,
  20: _handleGpsInfo,
};

void _handleKcpClose(Notify notify) {
  var ref = createContainerWithRooContext();
  var serverConnect = ref.read(serverConnectProvider);
  if (serverConnect) {
    var networkNotifier = ref.read(serverConnectProvider.notifier);
    networkNotifier.update(false);
    showSnackBarWithOption(Text(serverDisconnected.i18n), type: SnackBarMessageType.error);
    PlatformChannel.speakText(serverDisconnected.i18n);
  }
}

void _serverNotify(rpc_cmd rpc) {
  var data = Notify.fromBuffer(rpc.body);
  // var cmd = rpc.cmd;
  // logger.d('serverNotify: $cmd, $data');
  _notifyHandler[data.code]?.call(data);
}

// 0xcb36.加入临时组
void _handlerJoinTempGroup(Notify notify) async {
  var book = address_book.fromBuffer(notify.body);
  logger.i('_handlerJoinTempGroup: $book');
  var ref = createContainerWithRooContext();
  var listenGroups = ref.read(listenGroupsProvider);
  var intDmrId = toIntDmrId(book.dmrid);
  var isJoined = listenGroups.any((element) => element == intDmrId);

  if (isJoined) {
    return;
  }
  var org = db_org(
    dmrId: book.dmrid,
    orgShortName: book.name,
  );
  var contact = Contact<db_org>(originData: org, dataType: 100);

  var contactsNotifier = ref.read(contactsProvider.notifier);
  // 等待登录成功后通讯录获取成功在添加收听组
  await contactIsInitCompleter.future;
  var c = contactsNotifier.lookupContact(org.dmrId);
  if (c == null) {
    contactsNotifier.addContact(contact);
  } else {
    contactsNotifier.updateContact(contact);
  }

  var listenGroupsNotifier = ref.read(listenGroupsProvider.notifier);
  listenGroupsNotifier.addOneListenGroup(toIntDmrId(org.dmrId));

  // showSnackBarWithOption(Text('${joinTempGroup.i18n}: ${org.orgShortName}'), type: SnackBarMessageType.info);
}

// 0xcb37.退出临时组
void _handlerExitTempGroup(Notify notify) {
  logger.i('_handlerExitTempGroup: $notify');
  var dmrId = notify.paramStr;
  var ref = createContainerWithRooContext();
  var contactsNotifier = ref.read(contactsProvider.notifier);
  var contact = contactsNotifier.lookupContact(dmrId);
  var name = contact?.name ?? dmrId;
  contactsNotifier.removeContactWithDmrId(dmrId);

  var listenGroupsNotifier = ref.read(listenGroupsProvider.notifier);
  listenGroupsNotifier.removeOneListenGroup(toIntDmrId(dmrId));

  showSnackBarWithOption(Text('${exitTempGroup.i18n}: $name'), type: SnackBarMessageType.info);
}

// 0xcb38.加入任务组
void _handlerJoinTaskGroup(Notify notify) async {
  var book = address_book.fromBuffer(notify.body);
  logger.i('_handlerJoinTaskGroup: $book');
  var org = db_org(
    dmrId: book.dmrid,
    orgShortName: book.name,
  );
  var contact = Contact<db_org>(originData: org, dataType: 101);

  var ref = createContainerWithRooContext();
  var contactsNotifier = ref.read(contactsProvider.notifier);
  // 等待登录成功后通讯录获取成功在添加收听组
  await contactIsInitCompleter.future;
  var c = contactsNotifier.lookupContact(org.dmrId);
  if (c == null) {
    contactsNotifier.addContact(contact);
  } else {
    contactsNotifier.updateContact(contact);
  }

  setCurrentTaskGroupDmrId(org.dmrId);
  var callTargetNotifier = ref.read(callTargetProvider.notifier);
  callTargetNotifier.updateDefaultCallDmrId(org.dmrId);

  var listenGroupsNotifier = ref.read(listenGroupsProvider.notifier);
  listenGroupsNotifier.addOneListenGroup(toIntDmrId(org.dmrId));

  showSnackBarWithOption(Text('${joinTaskGroup.i18n}: ${org.orgShortName}'), type: SnackBarMessageType.info);
}

// 0xcb39.退出任务组
void _handlerExitTaskGroup(Notify notify) {
  logger.i('_handlerExitTaskGroup: $notify');
  var dmrId = notify.paramStr;
  var ref = createContainerWithRooContext();

  var contactsNotifier = ref.read(contactsProvider.notifier);
  var contact = contactsNotifier.lookupContact(dmrId);
  var name = contact?.name ?? dmrId;
  contactsNotifier.removeContactWithDmrId(dmrId);

  setCurrentTaskGroupDmrId('');
  var callTargetNotifier = ref.read(callTargetProvider.notifier);
  callTargetNotifier.updateDefaultCallDmrId(defaultCallDmrId);

  var listenGroupsNotifier = ref.read(listenGroupsProvider.notifier);
  listenGroupsNotifier.removeOneListenGroup(toIntDmrId(dmrId));

  var callTarget = ref.read(callTargetProvider);
  if (callTarget.callBackSpeakTarget == dmrId) {
    var callTargetNotify = ref.read(callTargetProvider.notifier);
    callTargetNotify.removeCallBackTargetSnackBar();
  }

  showSnackBarWithOption(Text('${exitTaskGroup.i18n}: $name'), type: SnackBarMessageType.info);
}

// 334 同步在线/离线终端
void _handlerSyncOnlineOffline(Notify notify) {
  var devDataInfo = dev_data_info.fromBuffer(notify.body);
  var ref = createContainerWithRooContext();
  var onlineDeviceDmrIdsNotifier = ref.read(onlineDeviceDmrIdsProvider.notifier);
  switch (devDataInfo.code) {
    case 0:
    case 8:
      onlineDeviceDmrIdsNotifier.add(devDataInfo.srcDmrid);
      break;
    case 1:
    case 3:
    case 14:
      onlineDeviceDmrIdsNotifier.remove(devDataInfo.srcDmrid);
      break;
    default:
      break;
  }
}

// 处理其他终端的bc15呼叫
Future<void> _processBc15(rpc_cmd rpc) async {
  var data = bc15.fromBuffer(rpc.body);
  var ref = createContainerWithRooContext();
  var contactProvider = ref.read(contactsProvider.notifier);
  var account = ref.read(accountProvider);
  var sourceHexDmrId = toHexDmrId(data.sourceDmrid);
  var targetHexDmrId = toHexDmrId(data.targetDmrid);
  var source = contactProvider.lookupContact(sourceHexDmrId);
  String sourceName = source?.name ?? sourceHexDmrId;
  // 权限内没有源信息，向goproxy请求
  if (source == null) {
    // 查找缓存的_noPermissionDevices，是否之前请求过无权限的联系人
    var noPermissionDevice = noPermissionDevices[sourceHexDmrId];
    if (noPermissionDevice != null) {
      sourceName = noPermissionDevice.selfId;
    } else {
      var (sourceDevice, err) = await queryOutsidePermissionContact(sourceHexDmrId);
      if (err != null) {
        sourceName = sourceHexDmrId;
      } else {
        noPermissionDevices[sourceHexDmrId] = sourceDevice!;
        sourceName = sourceDevice.selfId;
      }
    }
  }
  var targetName = contactProvider.lookupContact(targetHexDmrId)?.name ?? targetHexDmrId;

  // callStatus == 0: 呼叫结束，1: 呼叫开始
  final isCallEnd = data.callStatus == 0;
  pttCallStatus.isBc15Calling = !isCallEnd;
  if (isCallEnd) {
    removeCurrentSnackBar();
    pttCallStatus.bc15CallInfo = '';
    pttCallStatus.cancelCb75Message();

    if (alarmController.isAlarmingCall) {
      // 存在报警呼叫状态，清除
      alarmController.removeAlarmOverlay();
      return;
    }
    var ref = createContainerWithRooContext();
    var callTargetNotifier = ref.read(callTargetProvider.notifier);
    callTargetNotifier.updateCallBackSpeakTarget(data.sourceDmrid, data.targetDmrid, sourceName, targetName);
    return;
  }

  // 呼叫开始
  var callTargetNotifier = ref.read(callTargetProvider.notifier);
  var callTargetInfo = ref.read(callTargetProvider);
  if (callTargetInfo.callBackSpeakTarget.isNotEmpty) {
    callTargetNotifier.removeCallBackTargetSnackBar();
  }
  String text = '$sourceName ${groupCall.i18n} $targetName';
  if (account.dmrId == targetHexDmrId) {
    text = talkingToSomeone.i18n.fill([sourceName]);
  }
  pttCallStatus.bc15CallInfo = text;
  // todo duration可以修改为单次讲话超时时间，cmd_req_set_speak_time_out_duration
  showSnackBarWithOptionWithNLines(text, type: SnackBarMessageType.success, duration: const Duration(seconds: 60));
  // 设置定时器，3s内没有收到语音数据则结束bc15的呼叫提示
  pttCallStatus.bc15CallTimer = Timer(const Duration(seconds: 3), () {
    pttCallStatus.clearBc15CallTimer();
    pttCallStatus.isBc15Calling = false;
    removeCurrentSnackBar();
  });
}

// goproxy通知当前正在呼叫录音状态变更
void _processSpeakStatus(rpc_cmd rpc) {
  var isSpeakCalling = rpc.res == media_status.start.value;
  if (isSpeakCalling) {
    // 如果正在报警，则不处理呼叫状态
    if (alarmController.isAlarming && !alarmController.isAlarmingCall && !alarmController.isAlarmingButNotCallOrNotAlarm) {
      // 正在报警，发出去的是紧急报警呼叫
      alarmController.showAlarmOverlay();
      return;
    }

    // 处于正在录音状态，但是ptt已经松开了，在调用一次挂断
    if (!pttCallStatus.isCalling) {
      logger.d('_processSpeakStatus onPttUp');
      pttCallStatus.isCalling = true;
      onPttUp();
    }

    // 呼叫开始，存在回呼目标，则删除回呼目标
    var ref = createContainerWithRooContext();
    var callTargetNotifier = ref.read(callTargetProvider.notifier);
    var callTargetInfo = ref.read(callTargetProvider);
    if (callTargetInfo.callBackSpeakTarget.isNotEmpty) {
      callTargetNotifier.removeCallBackTargetSnackBar();
    }
    return;
  }

  if (alarmController.isAlarming) {
    alarmController.removeAlarmOverlay();
    return;
  }

  // pttCallStatus.isCalling = false;
  if (pttCallStatus.isBc15Calling) {
    removeCurrentSnackBar();
  }
}

Future<void> _processPocSettingChanged(rpc_cmd rpc) async {
  if (rpc.paraStr == '') return;
  var ref = createContainerWithRooContext();
  // 更新之前的收听组
  var listenCache = ref.read(listenGroupsProvider);
  var pocConfigNotifier = ref.read(pocConfigStoreProvider.notifier);
  // if (pocConfigNotifier.canEditSubscriptionLocal) {
  //   var isConfirm = await openAlertDialog(rootNavigatorKey.currentContext!, Text(serverUpdateSettingAlert.i18n, textAlign: TextAlign.center));
  //   if (!isConfirm) return;
  // }
  var isOk = await initPocData(rpc.paraStr);
  if (!isOk) {
    return;
  }
  // 更新之后的收听组
  var listenGroup = ref.read(listenGroupsProvider);
  var finalListenGroup = <int>[];
  if (pocConfigNotifier.canEditSubscriptionLocal) {
    // 有编辑权限的情况下，合并请求和缓存的收听组
    Set<int> listenGroupSet = {...listenGroup, ...listenCache};
    var contactNotifier = ref.read(contactsProvider.notifier);
    // 收听组是否都存在于联系人中
    finalListenGroup = listenGroupSet.where((listenGroup) => contactNotifier.lookupContactFromIntDmrId(listenGroup) != null).toList();
  } else {
    // 没有编辑权限的情况下，只更新请求的收听组
    finalListenGroup = listenGroup;
  }
  var callTargetInfo = ref.read(callTargetProvider);
  var newPocDefaultGroups = PocDefaultGroup(
    defaultSendGroupDmrid: toIntDmrId(callTargetInfo.defaultCallDmrId),
    defaultListenGroupDmrids: finalListenGroup,
  );
  var err = await updatePocListenGroup(newPocDefaultGroups);
  if (err == null) {
    var listenGroupNotifier = ref.read(listenGroupsProvider.notifier);
    listenGroupNotifier.updateGroups(finalListenGroup);
  }
  await queryOnlineDevice(processOnlineDevices);
  showSnackBarWithOption(Text(syncServerSetting.i18n), type: SnackBarMessageType.success);
}

// 解除报警
void _processCb10(rpc_cmd rpc) {
  alarmController.cancelLocalSos();
}

// 接收系统是否在播放语音状态
void _processMediaPlayStatus(rpc_cmd rpc) {
  var isStart = rpc.res == media_status.start.value;
  if (isStart) {
    pttCallStatus.clearBc15CallTimer();
  }
}

// 接收服务器发送的cb75指令
void _process175(rpc_cmd rpc) {
  // 当前语音已经被打断了,不再接收同一个语音的cb75指令
  if (pttCallStatus.cb75IsInterruptOk) {
    return;
  }

  var data = cb75.fromBuffer(rpc.body);
  var ref = createContainerWithRooContext();
  var account = ref.read(accountProvider);
  if (data.sourceDmrid != toIntDmrId(account.dmrId)) {
    return;
  }

  pttCallStatus.cb75IsInterruptOk = true;
  pttCallStatus.isCalling = false;
  var disableMonitoringFunc = ref.read(devStatusManagerProvider).getEnableMonitoringFunc() == 0;
  if (!pttCallStatus.isBc15Calling && disableMonitoringFunc) {
    PlatformChannel.speakText(voiceInterrupt.i18n);
  }

  pttCallStatus.cb75SnackBarController = showSnackBarWithOption(
    Text(voiceInterrupt.i18n, style: const TextStyle(color: Colors.white), textAlign: TextAlign.center),
    type: SnackBarMessageType.error,
    duration: const Duration(milliseconds: 2500),
  );
  pttCallStatus.cb75SnackBarController!.closed.then((value) {
    pttCallStatus.cb75SnackBarController = null;
    if (pttCallStatus.isBc15Calling) {
      showSnackBarWithBc15CallInfo();
    }
  });
}

// 发送短信
Future<StateError?> sendShortMessage(short_messages sms) async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_send_short_messages,
      body: sms.writeToBuffer(),
    );
    final respRpcCmd = await sendRpcCmd(rpcCmd, responseCmdCode: cmd_code.cmd_resp_send_short_messages);
    // 短信序号不对则返回错误
    if (respRpcCmd.res != sms.smsNo) {
      return StateError(respRpcCmd.paraStr);
    }
    return null;
  } catch (e) {
    return StateError(e.toString());
  }
}

// 回复单呼短信
Future<StateError?> replayShortMessage(short_messages sms) async {
  try {
    var rpcCmd = rpcCmdWithSeqNo(
      cmd: cmd_code.cmd_resp_confirm_short_messages,
      body: sms.writeToBuffer(),
    );
    await sendRpcCmd(rpcCmd, noResponse: true);
    return null;
  } catch (e) {
    return StateError(e.toString());
  }
}

// 接收到短信消息
Future<void> _processShortMessages(rpc_cmd rpc) async {
  var data = short_messages.fromBuffer(rpc.body);
  // 如果是单呼本机的短信，则需要回复应答
  var ref = createContainerWithRooContext();
  var account = ref.read(accountProvider);
  if (data.targetDmrid == account.dmrId) {
    await replayShortMessage(short_messages(senderDmrid: data.targetDmrid, targetDmrid: data.senderDmrid, smsNo: data.smsNo));
  }

  // 可能没有发信人的权限，需要查询
  var contactsNotifier = ref.read(contactsProvider.notifier);
  if (data.senderDmrid != systemCenterDmrId && contactsNotifier.lookupDbDeviceWithOutPermission(data.senderDmrid) == null) {
    // 发信人不在通讯录中，则查询
    var (senderDevice, err) = await queryOutsidePermissionContact(data.senderDmrid);
    if (err == null) {
      noPermissionDevices[data.senderDmrid] = senderDevice!;
    } else {
      logger.w('queryOutsidePermissionContact error with short_messages: $err');
    }
  }

  try {
    // 亮屏提醒
    PlatformChannel.wakeUp();
    // 播放短信提示声
    PlatformChannel.playSoundEffects("sms_receive");
  } catch (e) {
    logger.e("_processShortMessages playSoundEffects error: $e");
  }

  // 展示短信
  var smsNotifier = ref.read(shortMessageProvider.notifier);
  smsNotifier.addSms(data);
  rootNavigatorKey.currentContext!.push(displaySmsRouterPath, extra: SmsRouterExtra(sms: data));

  // 自动播放短信，只有在设备空闲时才播放
  if (data.smsType == SmsType.autoPlay && !pttCallStatus.isBc15Calling) {
    PlatformChannel.speakText(data.smsContent, mediaIdle: true);
  }
}

Future<void> _processLockDeviceStatus(rpc_cmd rpc) async {
  // 上次锁机的状态
  var lastSt = AppConfig.lockDeviceSt;
  // 当前的状态
  var yn = rpc.paraBin[0];
  var st = DeviceLockStatus.fromValue(rpc.paraBin[1]);
  AppConfig.lockDeviceSt = st;
  AppConfig.lockDeviceStIsInit = true;

  // 播报锁机状态
  void speakLockDeviceStatus() {
    switch (st) {
      case DeviceLockStatus.lockListen:
        // 禁听锁机
        PlatformChannel.speakText(bannedFromListening.i18n);
        break;
      case DeviceLockStatus.lockCall:
        // 禁发锁机
        PlatformChannel.speakText(bannedFromCalling.i18n);
        break;
      case DeviceLockStatus.lockListenAndCall:
        // 禁发禁听锁机
        PlatformChannel.speakText(deviceIsLocked.i18n);
        break;
      default:
        break;
    }
  }

  // 播报开机状态
  void speakOpenDeviceStatus() {
    // 解除禁听
    if (lastSt == DeviceLockStatus.lockListen) {
      PlatformChannel.speakText(removeLockListening.i18n);
      return;
    }
    // 解除禁发
    if (lastSt == DeviceLockStatus.lockCall) {
      PlatformChannel.speakText(removeLockCalling.i18n);
      return;
    }
    // 解除禁听/禁发
    PlatformChannel.speakText(removeCallRestriction.i18n);
  }

  if (yn == 0) {
    speakOpenDeviceStatus();
    return;
  }

  if (yn == 1) {
    speakLockDeviceStatus();
    return;
  }
}

Future<void> _processDeviceStatus(rpc_cmd rpc) async {
  var deviceStatus = rpc.paraBin;
  var ref = createContainerWithRooContext();
  var devStatusNotifier = ref.read(devStatusManagerProvider.notifier);
  devStatusNotifier.updateWithBytes(deviceStatus);

  // 同步锁机状态
  if (!AppConfig.lockDeviceStIsInit) {
    var devStatus = devStatusNotifier.devStatus;
    var isForbiddenCall = devStatus.getForbiddenCall() == 1;
    var isForbiddenListen = devStatus.getForbiddenListen() == 1;
    if (isForbiddenListen && isForbiddenCall) {
      AppConfig.lockDeviceSt = DeviceLockStatus.lockListenAndCall;
    } else if (isForbiddenListen) {
      AppConfig.lockDeviceSt = DeviceLockStatus.lockListen;
    } else if (isForbiddenCall) {
      AppConfig.lockDeviceSt = DeviceLockStatus.lockCall;
    } else {
      AppConfig.lockDeviceSt = DeviceLockStatus.open;
    }
  }

  var devStatus = devStatusNotifier.devStatus;
  // 开启语音监听时正在通话，需要清除通话提示
  if (devStatus.getEnableMonitoringFunc() == 1 && pttCallStatus.isBc15Calling) {
    pttCallStatus.clearBc15CallTimer();
    pttCallStatus.isBc15Calling = false;
    removeCurrentSnackBar();
  }
}

Future<void> _processQueryAppConfig(rpc_cmd rpc) async {
  rpc.cmd = cmd_code.cmd_resp_query_app_config.value;

  var config = app_config();
  // 获取当前的配置
  var ref = createContainerWithRooContext();
  var account = ref.read(accountProvider);
  config.dmrid = account.dmrId;
  config.password = account.password;
  var serverSetting = ref.read(serverSettingProvider);
  config.host = serverSetting.host;
  config.port = serverSetting.port;

  rpc.body = config.writeToBuffer();

  await sendRpcCmd(rpc, noResponse: true);
}

Future<void> _processSetAppConfig(rpc_cmd rpc) async {
  rpc.cmd = cmd_code.cmd_resp_set_app_config.value;

  // 检验配置是否有效
  var config = app_config.fromBuffer(rpc.body);
  if (config.dmrid.isEmpty || config.password.isEmpty || config.host.isEmpty || config.port == 0) {
    rpc.res = 1;
    rpc.paraStr = 'invalid config';
    sendRpcCmd(rpc, noResponse: true);
    return;
  }
  var ref = createContainerWithRooContext();
  var account = ref.read(accountProvider);
  var accountNotifier = ref.read(accountProvider.notifier);
  // 更新dmrId和pw到登录页面的loginInfo riverpod
  final loginInfoNotifier = ref.read(loginInfoProvider.notifier);
  loginInfoNotifier.update(LoginState(dmrId: config.dmrid, password: config.password));
  await sendRpcCmd(rpc, noResponse: true);

  // 如果已经登录，则先退出登录
  var serverSettingNotifier = ref.read(serverSettingProvider.notifier);
  if (account.isLogin) {
    // 清除任务组
    setCurrentTaskGroupDmrId('');
    await loginQuit();
    accountNotifier.logout(isInit: true);
    rootNavigatorKey.currentContext!.go(loginRouterPath);
  }

  // 保存当前写入的账号密码
  var userInfo = ref.read(accountProvider);
  var info = userInfo.copyWith(dmrId: config.dmrid, password: config.password, canEditLoginParam: config.canEditLoginParam);
  accountNotifier.updateAccount(info);

  // 更新服务器设置
  serverSettingNotifier.update(server_addr(host: config.host, port: config.port));
  var isOk = await updateServerSetting(config.host, config.port);
  if (!isOk) {
    // rpc.res = 1;
    // rpc.paraStr = 'update server setting failed';
    // sendRpcCmd(rpc, noResponse: true);
    return;
  }
  serverSettingNotifier.updateIsUpdateServerSetting(true);

  // 必须亮屏，否则无法正常播报语音和更新页面
  PlatformChannel.wakeUp();

  loginInfoNotifier.updateIsLogging(true);
  // 重新登录
  isOk = await loginHandler(config.dmrid, pwd: config.password, ignoreSessionId: true);
  loginInfoNotifier.updateIsLogging(false);
  if (!isOk) {
    // rpc.res = 1;
    // rpc.paraStr = 'login failed';
    // await sendRpcCmd(rpc, noResponse: true);
    return;
  }

  // 登录成功，修正登录状态和页面跳转
  // await sendRpcCmd(rpc, noResponse: true);
  WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
    if (rootNavigatorKey.currentContext == null) {
      return;
    }
    rootNavigatorKey.currentContext!.go(homeRouterPath);
  });
}

void _processKcpAutoLoginResult(rpc_cmd rpc) async {
  // 只处理自动登录的结果
  if (rpc.paraInt != 1) return;
  var ref = createContainerWithRooContext();
  var serverConnectNotifier = ref.read(serverConnectProvider.notifier);
  serverConnectNotifier.update(true);
  var accountNotifier = ref.read(accountProvider.notifier);
  var account = ref.read(accountProvider);
  var failedCodes = [1, 10, 303, 404];
  if (failedCodes.contains(rpc.res)) {
    // kcp自动登录失败后，再次尝试密码登录
    var isOk = await loginHandler(account.dmrId, pwd: account.password, ignoreSessionId: true, ignoreFalseMsg: true);
    if (isOk) {
      showSnackBarWithOption(Text(serverReconnected.i18n), type: SnackBarMessageType.success);
      PlatformChannel.speakText(serverReconnected.i18n);
      accountNotifier.needSpeakAccount = false;
    } else {
      // 密码登录失败 清除并跳转到登录页
      showSnackBarWithOption(Text(serverReconnectedLoginFailed.i18n), type: SnackBarMessageType.warning);
      PlatformChannel.speakText(serverReconnectedLoginFailed.i18n);
      var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
      loginInfoNotifier.removePW();
      rootNavigatorKey.currentContext!.go(loginRouterPath);
    }
    return;
  }
  account.sessionId = rpc.paraStr;
  accountNotifier.login(account);
  accountNotifier.needSpeakAccount = false;
  // 向服务器上报当前收听组
  var currentListenGroup = ref.read(listenGroupsProvider);
  var listenGroupInfo = PocDefaultGroup(
    defaultListenGroupDmrids: currentListenGroup,
    defaultSendGroupDmrid: defaultSendGroupDmrid,
  );
  await updatePocListenGroup(listenGroupInfo);
  showSnackBarWithOption(Text(serverReconnected.i18n), type: SnackBarMessageType.success);
  PlatformChannel.speakText(serverReconnected.i18n);

  // 读取本地pocConfig
  final pocConfigNotifier = ref.read(pocConfigStoreProvider.notifier);
  // 将pocConfig同步到goproxy中
  syncPocConfigToProxy(pocConfigNotifier.config);

  // 初始化数据完成， 通知到goproxy
  var rpcCmd = rpcCmdWithSeqNo(
    cmd: cmd_code.cmd_notify_init_data_finish,
  );
  sendRpcCmd(rpcCmd, noResponse: true);
}

Future<void> _processForceExit(rpc_cmd rpc) async {
  // 正在播放语音时，被强制下线时清除提示框
  removeCurrentSnackBar();
  var ref = createContainerWithRooContext();
  // 刷新登录状态
  await ref.read(accountProvider.notifier).logout();
  // 清除登录页面的登录信息
  var loginInfoNotifier = ref.read(loginInfoProvider.notifier);
  loginInfoNotifier.removePW();
  var p = ref.read(serverSettingProvider.notifier);
  p.updateIsUpdateServerSetting(false);
  if (rootNavigatorKey.currentContext == null) {
    return;
  }
  rootNavigatorKey.currentContext!.go(loginRouterPath);
  showSnackBarWithOption(Text(forcedOffline.i18n), type: SnackBarMessageType.warning);
  PlatformChannel.speakText(forcedOffline.i18n);
}

StreamSubscription<rpc_cmd>? _sub;
// 接收服务器主动推送的数据
void listenRpcMessageStream() {
  // 收听服务器广播的指令处理 _globalPushEvent
  _sub?.cancel();
  _sub = rpcMessageStream.listen((rpcCmd) {
    _globalPushEvent[rpcCmd.cmd]?.call(rpcCmd);
  });
}

// 监听配置工具指令
StreamSubscription<rpc_cmd>? _subConfig;

void listenConfigRpcMessageStream() {
  _subConfig?.cancel();
  _subConfig = rpcMessageStream.listen((rpcCmd) {
    if (rpcCmd.cmd == cmd_code.cmd_req_query_app_config.value) {
      // 收到服务器发送的查询配置指令
      _processQueryAppConfig(rpcCmd);
    } else if (rpcCmd.cmd == cmd_code.cmd_req_set_app_config.value) {
      // 收到服务器发送的更新配置指令
      _processSetAppConfig(rpcCmd);
    }
  });
}

Completer<app_proto.gps84?>? _locationOnce;
Timer? _locationOnceTimer;

// 3分钟定位超时
Future<app_proto.gps84?> reportLocationOnce({int force = 1, Duration timeout = const Duration(seconds: 3 * 60)}) async {
  var rpcCmd = rpcCmdWithSeqNo(
    cmd: cmd_code.cmd_req_gps_location_once,
  );
  rpcCmd.paraInt = $fixnum.Int64(force);
  await sendRpcCmd(rpcCmd, noResponse: true);
  _locationOnce = Completer<app_proto.gps84?>();
  _locationOnceTimer = Timer(timeout, () {
    _locationOnce?.complete(null);
    _locationOnce = null;
    _locationOnceTimer = null;
  });
  return _locationOnce!.future;
}

void _handleGpsInfo(Notify notify) {
  final ref = createContainerWithRooContext();
  final account = ref.read(accountProvider);
  var gpsInfo = gps_info.fromBuffer(notify.body);
  if (gpsInfo.dmrid != account.dmrId || gpsInfo.activeStatus != 1) {
    return;
  }

  if (_locationOnce?.isCompleted == true) {
    return;
  }

  _locationOnce?.complete(gpsInfo.gpsInfo);
  _locationOnce = null;
  _locationOnceTimer?.cancel();
  _locationOnceTimer = null;
}

// 将本地的pocConfig同步到goproxy
Future<void> syncPocConfigToProxy(PocConfig config) async {
  var rpcCmd = rpcCmdWithSeqNo(
    cmd: cmd_code.cmd_sync_poc_config_to_proxy,
    body: config.writeToBuffer(),
  );
  await sendRpcCmd(rpcCmd, noResponse: true);
}
