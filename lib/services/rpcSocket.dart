import 'dart:async';
import 'dart:typed_data';

import '../app_proto/app_proto.pb.dart';
import '../app_proto/bf8100.pb.dart';
import '../util/config.dart';
import '../util/logger.dart';
import '../util/socket.dart';
import 'rpcCmd.dart';

class RpcSocketConfig {
  static Duration pingInterval = const Duration(seconds: 30);
  static Duration pingTimeout = const Duration(seconds: 6);
  static int pingFailCount = 3;

  // 请求超时时间
  static Duration requestTimeout = const Duration(seconds: 15);
}

// 接收到的消息的流广播
final rpcMessageController = StreamController<rpc_cmd>.broadcast();

Stream<rpc_cmd> get rpcMessageStream => rpcMessageController.stream;

// 全局唯一的socket
late final WebSocketClient rpcSocket;

// 发送rpcCmd请求命令，并返回结果，即请求/响应
Future<rpc_cmd> sendRpcCmd(rpc_cmd rpcCmd, {cmd_code? responseCmdCode, Duration? timeout, bool noResponse = false}) async {
  // 没有响应，则直接发送请求
  if (noResponse) {
    // 发送请求
    rpcSocket.send(rpcCmd.writeToBuffer());
    return rpc_cmd();
  }

  var resultCompleter = Completer<rpc_cmd>();

  // 应答的命令号
  var resCmdCode = responseCmdCode != null ? responseCmdCode.value : rpcCmd.cmd + 1;

  // 接收结果
  late Timer timeoutTimer;
  late StreamSubscription sub;
  sub = rpcMessageStream.listen((resRpcCMd) {
    // 同一个命令序号，且为指定的响应命令，则视为响应请求
    if (resRpcCMd.cmd == resCmdCode) {
      resultCompleter.complete(resRpcCMd);
      sub.cancel();
      timeoutTimer.cancel();
    }
  });

  // 发送请求
  rpcSocket.send(rpcCmd.writeToBuffer());

  // 超时计时器
  var requestTimeout = timeout ?? RpcSocketConfig.requestTimeout;
  timeoutTimer = Timer(requestTimeout, () {
    logger.w("sendRpcCmd timeout");
    timeoutTimer.cancel();
    sub.cancel();
    if (resultCompleter.isCompleted) {
      return;
    }
    resultCompleter.completeError(TimeoutException(null, requestTimeout));
  });

  return resultCompleter.future;
}

// 发送rpcCmd请求命令，接收到结果时触发onData回调
Future<StateError?> sendRpcCmdWithCallback(
  rpc_cmd rpcCmd,
  void Function(rpc_cmd) onData, {
  cmd_code? responseCmdCode,
  Duration? timeout,
  Duration? receivedTimeout,
  bool Function(rpc_cmd)? checkIsEnd,
}) async {
  var resultCompleter = Completer<StateError?>();
  // 应答的命令号
  var resCmdCode = responseCmdCode != null ? responseCmdCode.value : rpcCmd.cmd + 1;

  // 接收结果
  late Timer timeoutTimer;
  late StreamSubscription sub;
  var receivedTimeout0 = receivedTimeout ?? const Duration(milliseconds: 120);
  Timer? receivedTimeoutTimer;
  // 请求结束
  void reqEnd() {
    receivedTimeoutTimer?.cancel();
    sub.cancel();
    if (resultCompleter.isCompleted) {
      return;
    }
    resultCompleter.complete();
  }

  sub = rpcMessageStream.listen((resRpcCMd) {
    // 同一个命令序号，且为指定的响应命令，则视为响应请求
    if (resRpcCMd.cmd != resCmdCode) {
      return;
    }

    // 接收到数据，取消超时计时器
    timeoutTimer.cancel();
    // 调用回调，返回数据
    onData(resRpcCMd);
    // 存在结束回调，则调用回调判断是否分包结束
    if (checkIsEnd != null) {
      bool isEnd = checkIsEnd(resRpcCMd);
      if (isEnd) {
        reqEnd();
        return;
      }
    }

    // 已经在接收，在指定数据时间内，没有再接收到数据，则认为请求结束
    receivedTimeoutTimer?.cancel();
    receivedTimeoutTimer = Timer(receivedTimeout0, () {
      reqEnd();
    });
  });

  // 发送请求
  rpcSocket.send(rpcCmd.writeToBuffer());

  // 超时计时器
  var requestTimeout = timeout ?? RpcSocketConfig.requestTimeout;
  timeoutTimer = Timer(requestTimeout, () {
    logger.w("sendRpcCmdWithStream timeout");
    timeoutTimer.cancel();
    sub.cancel();
    if (resultCompleter.isCompleted) {
      return;
    }
    resultCompleter.completeError(TimeoutException("Timeout", RpcSocketConfig.requestTimeout));
  });

  return resultCompleter.future;
}

late Completer<bool> _pingCompleter;

// 发送心跳包
Future<bool> ping() async {
  _pingCompleter = Completer<bool>();
  var rpcCmd = rpc_cmd();
  rpcCmd.cmd = cmd_code.cmd_req_ping.value;
  Future.delayed(RpcSocketConfig.pingTimeout, () {
    // 如果ping已经完成，则不再触发定时器
    if (_pingCompleter.isCompleted) {
      return;
    }
    logger.w("ping timeout");
    _pingCompleter.complete(false);
  });
  rpcSocket.send(rpcCmd.writeToBuffer());
  return _pingCompleter.future;
}

// 回应心跳包
void pong() {
  var rpcCmd = rpc_cmd();
  rpcCmd.cmd = cmd_code.cmd_resp_ping.value;
  rpcSocket.send(rpcCmd.writeToBuffer());
}

void _onMessage(Uint8List message) {
  final rpcCMd = rpc_cmd.fromBuffer(message);
  final cmdCode = cmd_code.valueOf(rpcCMd.cmd);
  // logger.d('onMessage: cmdCode=$cmdCode, $rpcCMd');
  switch (cmdCode) {
    // 收到Ping，直接回应Pong
    case cmd_code.cmd_req_ping:
      pong();
      break;

    // 收到Pong，则完成ping的Future
    case cmd_code.cmd_resp_ping:
      if (!_pingCompleter.isCompleted) {
        _pingCompleter.complete(true);
      }
      break;

    default:
      logger.d('onMessage: cmdCode=$cmdCode, $rpcCMd');
      rpcMessageController.add(rpcCMd);
  }
}

// 初始化socket
Future<void> initRpcSocket() async {
  var port = AppConfig.serverPort;
  rpcSocket = WebSocketClient(AppConfig.getServerAddress(), _onMessage, resolveUrlWithAutoReconnect: () {
    if (++port >= AppConfig.serverPort + 3) {
      port = AppConfig.serverPort;
    }

    return AppConfig.getServerAddress(port: port);
  }, onOpen: () {
    // 开始心跳连接
    _checkPingHeartbeat();
    // 监听配置工具指令
    listenConfigRpcMessageStream();
  });
}

Future<void> _checkPingHeartbeat() async {
  var pingFailCount = 0;

  Future.doWhile(() async {
    final pingResult = await ping();

    // 如果socket关闭，则不再触发心跳连接
    if (!rpcSocket.isOpen) {
      return false;
    }

    // ping失败，计算失败次数，如果失败次数超过3次，则断开连接重连
    if (!pingResult) {
      pingFailCount++;
      if (pingFailCount > RpcSocketConfig.pingFailCount) {
        logger.e('ping failed, reconnect');
        await rpcSocket.reconnect();
        return false;
      }
      return true;
    }

    // ping成功，计算下次心跳的间隔时间
    // 正常心跳间隔期间，有数据交互，则延长心跳间隔时间
    await Future.delayed(RpcSocketConfig.pingInterval);
    // var lastReceivedTime = rpcSocket.lastReceivedTime;
    // var now = DateTime.now();
    // var diffDuration = now.difference(lastReceivedTime);
    // if (diffDuration.inMilliseconds < RpcSocketConfig.pingInterval.inMilliseconds) {
    //   await Future.delayed(RpcSocketConfig.pingInterval - diffDuration);
    // }
    return rpcSocket.isOpen;
  });
}
