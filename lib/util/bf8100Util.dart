// 系统中心DMRID
const systemCenterDmrId = '00000000';

// 将十进制DMRID转换为十六进制
String toHexDmrId(int intDmrId) {
  var hexDmrId = intDmrId.toRadixString(16).toUpperCase();
  // 0x80
  if (intDmrId >>> 24 == 0x80) {
    return hexDmrId;
  }

  return hexDmrId.padLeft(8, '0');
}

// 将十六进制DMRID转换为十进制
int toIntDmrId(String hexDmrId) {
  return int.parse(hexDmrId, radix: 16);
}

// is group
bool checkDmrIdIsGroup(int dmrIdInt) {
  return dmrIdInt >>> 24 == 0x80;
}

String extractSysIdFromDmrId(int dmrId) {
  int sysId = (dmrId & 0xF80000) >> 19;
  return '$sysId'.padLeft(2, '0');
}
