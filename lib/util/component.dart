import 'package:bf8100deviceapp/util/bf8100Util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:marquee/marquee.dart';

import '../i18n/locale.dart';
import '../iconfont/iconfont.dart';
import '../riverpod/alarm.dart';
import '../riverpod/contacts.dart';
import '../riverpod/serverConnect.dart';
import 'customKey.dart';

const defaultFontSize = 16.0;

ButtonStyle defaultAlertDialogButtonStyle = ButtonStyle(
  overlayColor: WidgetStateProperty.resolveWith((Set<WidgetState> state) {
    if (state.contains(WidgetState.focused)) {
      return Colors.blue;
    }
    return null;
  }),
  foregroundColor: WidgetStateProperty.resolveWith((Set<WidgetState> state) {
    if (state.contains(WidgetState.focused)) {
      return Colors.white;
    }
    return null;
  }),
);

Future<bool> openAlertDialog(
  BuildContext dialogContext,
  Widget contentWidget, {
  Widget? titleWidget,
  KeyEventResult Function(KeyUpEvent)? onKeyUpEvent,
  KeyEventResult Function(KeyDownEvent)? onKeyDownEvent,
  KeyEventResult Function(KeyRepeatEvent)? onKeyRepeatEvent,
  ButtonStyle? confirmBtnStyle,
  ButtonStyle? cancelBtnStyle,
}) async {
  return await showDialog<bool?>(
          context: dialogContext,
          builder: (ctx) {
            final FocusNode confirmBtnFocusNode = FocusNode();
            final FocusNode cancelBtnFocusNode = FocusNode();

            return Focus(
              focusNode: FocusNode(),
              onKeyEvent: (FocusNode node, KeyEvent event) {
                if (event is KeyDownEvent) {
                  if (event.logicalKey == CustomLogicalKeyboardKey.down) {
                    confirmBtnFocusNode.hasFocus ? cancelBtnFocusNode.requestFocus() : confirmBtnFocusNode.requestFocus();
                    return KeyEventResult.handled;
                  } else if (event.logicalKey == CustomLogicalKeyboardKey.up) {
                    confirmBtnFocusNode.hasFocus ? cancelBtnFocusNode.requestFocus() : confirmBtnFocusNode.requestFocus();
                    return KeyEventResult.handled;
                  } else if (event.logicalKey == LogicalKeyboardKey.goBack) {
                    Navigator.of(ctx).pop(false);
                    return KeyEventResult.handled;
                  }
                  if (onKeyDownEvent != null) {
                    return onKeyDownEvent(event);
                  }
                  return KeyEventResult.ignored;
                } else if (event is KeyUpEvent) {
                  if (onKeyUpEvent != null) {
                    return onKeyUpEvent(event);
                  }
                  return KeyEventResult.ignored;
                } else if (event is KeyRepeatEvent) {
                  if (event.logicalKey == CustomLogicalKeyboardKey.select) {
                    return KeyEventResult.handled;
                  }
                  if (onKeyRepeatEvent != null) {
                    return onKeyRepeatEvent(event);
                  }
                  return KeyEventResult.ignored;
                }
                return KeyEventResult.ignored;
              },
              child: AlertDialog(
                  title: titleWidget,
                  content: contentWidget,
                  contentPadding: const EdgeInsets.all(8),
                  iconPadding: const EdgeInsets.all(0),
                  titlePadding: const EdgeInsets.only(top: 8),
                  actionsPadding: const EdgeInsets.only(bottom: 8),
                  insetPadding: const EdgeInsets.all(8),
                  actionsAlignment: MainAxisAlignment.center,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  actions: [
                    ElevatedButton(
                      autofocus: true,
                      focusNode: confirmBtnFocusNode,
                      onPressed: () {
                        Navigator.of(ctx).pop(true);
                      },
                      style: confirmBtnStyle ?? defaultAlertDialogButtonStyle,
                      child: Text(confirm.i18n),
                    ),
                    ElevatedButton(
                      focusNode: cancelBtnFocusNode,
                      onPressed: () {
                        Navigator.of(ctx).pop(false);
                      },
                      style: cancelBtnStyle ?? defaultAlertDialogButtonStyle,
                      child: Text(cancel.i18n),
                    ),
                  ]),
            );
          }) ??
      false;
}

// 带有呼叫目标的AppBar 使用ConsumerState
class AppBarWithState extends ConsumerWidget implements PreferredSizeWidget {
  const AppBarWithState({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var contactsProviderNotifier = ref.watch(contactsProvider.notifier);
    // 发射组
    var txDmrId = ref.watch(callTargetProvider.select((callTarget) => callTarget.defaultCallDmrId));
    var callBackDmrId = ref.watch(callTargetProvider.select((callTarget) => callTarget.callBackSpeakTarget));
    var txContact = contactsProviderNotifier.lookupContact(txDmrId);
    var txContactName = txContact?.name ?? txDmrId;
    var finalTxDmrId = txDmrId;
    if (callBackDmrId.isNotEmpty) {
      var txContact = contactsProviderNotifier.lookupContact(callBackDmrId);
      if (txContact == null) {
        var noPermissionDevice = noPermissionDevices[callBackDmrId];
        txContactName = noPermissionDevice?.selfId ?? callBackDmrId;
      } else {
        txContactName = txContact.name;
      }
      finalTxDmrId = callBackDmrId;
    }

    var alarming = ref.watch(alarmingProvider);
    List<Widget> actions = [];
    if (alarming) {
      actions.add(const Icon(
        Icons.warning,
        color: Colors.red,
      ));
    }
    var icon = checkDmrIdIsGroup(toIntDmrId(finalTxDmrId)) ? Icons.groups : Icons.person;
    // 默认发射组为动态组时, 存在回呼目标, appBar应该显示回呼目标的icon
    var callBackContact = contactsProviderNotifier.lookupContact(callBackDmrId);
    if (callBackContact != null) {
      icon = callBackContact.isDynamicGroup() ? Iconfont.dynamicGroup : (callBackContact.isGroup ? Icons.groups : Icons.person);
    } else if (txContact != null && txContact.isDynamicGroup()) {
      icon = Iconfont.dynamicGroup;
    }

    List<Widget> rowChildren = [
      Icon(icon),
      const SizedBox(width: 4),
      Flexible(
        child: Text(
          txContactName,
          style: const TextStyle(fontSize: 16),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    ];

    var serverConnect = ref.watch(serverConnectProvider);

    return AppBar(
      leadingWidth: 32,
      leading: serverConnect ? null : const Padding(padding: EdgeInsets.only(left: 10), child: Icon(Iconfont.disconnect2, color: Colors.red)),
      title: Row(mainAxisAlignment: MainAxisAlignment.center, children: rowChildren),
      centerTitle: true,
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(36.0);
}

// 查找下一个可以聚焦的节点
// 如果要反向查找，则 reverse = true
FocusNode? findNextFocusNode(List<FocusNode> focusNodes, int startIndex, {bool reverse = false}) {
  var stepVal = reverse ? -1 : 1;
  var nextFocusNodeIndex = (startIndex + stepVal) % focusNodes.length;
  var node = focusNodes[nextFocusNodeIndex];
  while (startIndex != nextFocusNodeIndex) {
    if (node.canRequestFocus) {
      return node;
    }

    // 查找下一个节点
    nextFocusNodeIndex = (nextFocusNodeIndex + stepVal) % focusNodes.length;
    node = focusNodes[nextFocusNodeIndex];
  }

  return null;
}

// 判断文本宽度是否超过指定宽度, 超过则使用滚动效果, 否则使用文本样式
class MarqueeText extends ConsumerStatefulWidget {
  const MarqueeText({
    super.key,
    required this.text,
    required this.width,
    this.height = 24.0,
    this.style = const TextStyle(fontSize: defaultFontSize),
    this.blankSpace = 40.0,
    this.velocity = 40.0,
    this.startPadding = 0,
    this.pauseAfterRound = const Duration(milliseconds: 1000),
    this.scrollAxis = Axis.horizontal,
    this.isScroll = true,
    this.startAfter = const Duration(milliseconds: 1000),
  });

  final String text;
  final double width;
  final double height;
  final TextStyle style;
  final double blankSpace;
  final double velocity;
  final double startPadding;
  final Duration pauseAfterRound;
  final Axis scrollAxis;
  final bool isScroll;
  final Duration startAfter;

  @override
  ConsumerState<MarqueeText> createState() => MarqueeTextState();
}

class MarqueeTextState extends ConsumerState<MarqueeText> {
  late TextPainter _textPainter;
  bool _overflow = false;
  bool _isScroll = false;

  @override
  void initState() {
    super.initState();
    _textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      text: TextSpan(text: widget.text, style: widget.style),
      maxLines: 1,
    );
    _calculateTextWidth();
  }

  @override
  void didUpdateWidget(covariant MarqueeText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text || oldWidget.width != widget.width || oldWidget.isScroll != widget.isScroll) {
      _textPainter = TextPainter(
        text: TextSpan(text: widget.text, style: widget.style),
        maxLines: 1,
        textDirection: TextDirection.ltr,
      );
      _calculateTextWidth();
    }
  }

  void _calculateTextWidth() {
    _textPainter.layout(maxWidth: double.infinity);
    setState(() {
      _overflow = _textPainter.width > widget.width;
      _isScroll = widget.isScroll;
    });
  }

  @override
  Widget build(BuildContext context) {
    return (_overflow && _isScroll)
        ? SizedBox(
            width: widget.width,
            height: widget.height,
            child: Marquee(
              text: widget.text,
              style: widget.style,
              scrollAxis: Axis.horizontal,
              blankSpace: widget.blankSpace,
              velocity: widget.velocity,
              startPadding: widget.startPadding,
              pauseAfterRound: widget.pauseAfterRound,
              startAfter: widget.startAfter,
            ),
          )
        : SizedBox(
            width: widget.width,
            child: Text(
              widget.text,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              softWrap: false,
            ),
          );
  }
}

Widget buildLoadAnimation(String text) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(
          width: 30,
          height: 30,
          child: CircularProgressIndicator(),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 10),
          child: Text(
            text,
            style: const TextStyle(color: Colors.black, fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    ),
  );
}
