import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'logger.dart';

class AppConfig {
  static String _serverProtocol = "ws";
  static String _serverHost = "127.0.0.1";
  static int _serverPort = 12255;
  static String _serverProxy = "proxy";

  static String get serverHost => _serverHost;

  static int get serverPort => _serverPort;

  static String get serverProxy => _serverProxy;

  static String getServerAddress({String? host, int? port}) => "$_serverProtocol://${host ?? _serverHost}:${port ?? _serverPort}/$_serverProxy";

  static bool lockDeviceStIsInit = false;
  static DeviceLockStatus lockDeviceSt = DeviceLockStatus.open;
}

// Release 返回true
const bool isProMode = bool.fromEnvironment("dart.vm.product");

Future<void> loadDotenv() async {
  if (isProMode) {
    return;
  }

  try {
    await dotenv.load(fileName: ".env");
    AppConfig._serverProtocol = dotenv.env["SERVER_PROTOCOL"] ?? AppConfig._serverProtocol;
    AppConfig._serverHost = dotenv.env["SERVER_HOST"] ?? AppConfig._serverHost;
    AppConfig._serverPort = dotenv.env["SERVER_PORT"] != null ? int.parse(dotenv.env["SERVER_PORT"]!) : AppConfig._serverPort;
    AppConfig._serverProxy = dotenv.env["SERVER_PROXY"] ?? AppConfig._serverProxy;
  } catch (err) {
    logger.w("load .env failed, error: $err");
  }
}

// 设备锁机状态
// 00=开机；01=禁听锁机,02=禁发锁机,03=禁发禁听锁机。
enum DeviceLockStatus {
  open(0),
  lockListen(1),
  lockCall(2),
  lockListenAndCall(3);

  const DeviceLockStatus(this.value);

  factory DeviceLockStatus.fromValue(int value) {
    return DeviceLockStatus.values.firstWhere((element) => element.value == value);
  }

  final int value;
}
