import 'package:flutter/services.dart';

// 自定义的HotKeyCode
enum HotKeyCode {
  back(4),
  call(5),
  up(19),
  down(20),
  select(23),
  home(131),
  sos(260),
  ptt(261),
  lock(264),
  p2(265),
  p3(266),
  vb(267);

  const HotKeyCode(this.keyCode);

  final int keyCode;

  // 通过keyCode值来获取对应的HotKey
  static HotKeyCode fromKeyCode(int keyCode) {
    return HotKeyCode.values.firstWhere((element) => element.keyCode == keyCode);
  }

  // 判断是否有指定的KeyCode
  static bool hasKeyCode(int keyCode) {
    return HotKeyCode.values.any((element) => element.keyCode == keyCode);
  }

  @override
  String toString() {
    return 'HotKeyCode{keyCode:$keyCode, name: $name}';
  }
}

// 自定义的HotKeyCode的KeyCode
class CustomHotKeyCode {
  static final back = HotKeyCode.back.keyCode;
  static final call = HotKeyCode.call.keyCode;
  static final up = HotKeyCode.up.keyCode;
  static final down = HotKeyCode.down.keyCode;
  static final select = HotKeyCode.select.keyCode;
  static final home = HotKeyCode.home.keyCode;
  static final sos = HotKeyCode.sos.keyCode;
  static final ptt = HotKeyCode.ptt.keyCode;
  static final lock = HotKeyCode.lock.keyCode;
  static final p2 = HotKeyCode.p2.keyCode;
  static final p3 = HotKeyCode.p3.keyCode;
  static final vb = HotKeyCode.vb.keyCode;
}

// 自定义的LogicalKeyboardKey
class CustomLogicalKeyboardKey {
  static const down = LogicalKeyboardKey(0x100000301);
  static const up = LogicalKeyboardKey(0x100000304);

  // enter, select, confirm
  static const select = LogicalKeyboardKey(0x10000050c);
  static const call = LogicalKeyboardKey(0x100001002);
  static const back = LogicalKeyboardKey(0x100001005);
  static const home = LogicalKeyboardKey(0x100000801);

  // lock
  static const p1 = LogicalKeyboardKey(0x1100000108);

  static get lock => p1;

  // single call
  static const p2 = LogicalKeyboardKey(0x01100000109);

  // group call
  static const p3 = LogicalKeyboardKey(0x0110000010a);
  static const sos = LogicalKeyboardKey(0x100000d59);
  static const vb = LogicalKeyboardKey(0x110000010b);
  static const ptt = LogicalKeyboardKey(0x100000d57);
}

// 自定义的KeyCode与Flutter的LogicalKeyboardKey的映射关系
final Map<int, LogicalKeyboardKey> _logicalKeyMap = {
  CustomHotKeyCode.back: CustomLogicalKeyboardKey.back,
  CustomHotKeyCode.call: CustomLogicalKeyboardKey.call,
  CustomHotKeyCode.up: CustomLogicalKeyboardKey.up,
  CustomHotKeyCode.down: CustomLogicalKeyboardKey.down,
  CustomHotKeyCode.select: CustomLogicalKeyboardKey.select,
  CustomHotKeyCode.home: CustomLogicalKeyboardKey.home,
  CustomHotKeyCode.sos: CustomLogicalKeyboardKey.sos,
  CustomHotKeyCode.ptt: CustomLogicalKeyboardKey.ptt,
  CustomHotKeyCode.lock: CustomLogicalKeyboardKey.p1,
  CustomHotKeyCode.p2: CustomLogicalKeyboardKey.p2,
  CustomHotKeyCode.p3: CustomLogicalKeyboardKey.p3,
  CustomHotKeyCode.vb: CustomLogicalKeyboardKey.vb,
};

LogicalKeyboardKey? getLogicalKeyboardKeyByKeyCode(int keyCode) {
  return _logicalKeyMap[keyCode];
}

String? getKeyCodeCharacter(int keyCode) {
  var hasKeyCode = HotKeyCode.hasKeyCode(keyCode);
  return hasKeyCode ? HotKeyCode.fromKeyCode(keyCode).name : null;
}

enum KeyEventType {
  onKeyDown('onKeyDown'),
  onKeyUp('onKeyUp'),
  onKeyLongPress('onKeyLongPress');

  const KeyEventType(this.name);

  final String name;

  // 通过name值来获取对应的KeyEventType
  static KeyEventType fromName(String name) {
    return KeyEventType.values.firstWhere((element) => element.name == name);
  }

  @override
  String toString() {
    return 'KeyEventType{name: $name}';
  }
}

class DispatchKeyEvent {
  final int keyCode;
  final String keyCodeName;
  final int repeatCount;

  DispatchKeyEvent(this.keyCode, this.keyCodeName, this.repeatCount);

  @override
  String toString() {
    return 'DispatchKeyEvent{keyCode: $keyCode, keyCodeName: $keyCodeName, repeatCount: $repeatCount}';
  }

  DispatchKeyEvent.fromMap(Map<dynamic, dynamic> map)
      : keyCode = map['keyCode'],
        keyCodeName = map['keyCodeName'],
        repeatCount = map['repeatCount'];

  KeyEvent? toKeyEvent(KeyEventType eventType) {
    switch (eventType) {
      case KeyEventType.onKeyDown:
        return convertToKeyDownEvent(this);
      case KeyEventType.onKeyUp:
        return convertToKeyUpEvent(this);
      case KeyEventType.onKeyLongPress:
        return convertToKeyRepeatEvent(this);
      default:
        return null;
    }
  }
}

// 转换为KeyDownEvent
KeyEvent? convertToKeyDownEvent(DispatchKeyEvent event) {
  var logicalKey = getLogicalKeyboardKeyByKeyCode(event.keyCode);
  if (logicalKey == null) {
    return null;
  }

  return KeyDownEvent(
    physicalKey: const PhysicalKeyboardKey(0x00000000),
    logicalKey: logicalKey,
    timeStamp: Duration(milliseconds: DateTime.now().millisecondsSinceEpoch),
    character: getKeyCodeCharacter(event.keyCode) ?? event.keyCodeName,
  );
}

// 转换为KeyRepeatEvent
KeyEvent? convertToKeyRepeatEvent(DispatchKeyEvent event) {
  var logicalKey = getLogicalKeyboardKeyByKeyCode(event.keyCode);
  if (logicalKey == null) {
    return null;
  }

  return KeyRepeatEvent(
    physicalKey: const PhysicalKeyboardKey(0x00000000),
    logicalKey: logicalKey,
    timeStamp: Duration(milliseconds: DateTime.now().millisecondsSinceEpoch),
    character: getKeyCodeCharacter(event.keyCode) ?? event.keyCodeName,
  );
}

// 转换为KeyUpEvent
KeyEvent? convertToKeyUpEvent(DispatchKeyEvent event) {
  var logicalKey = getLogicalKeyboardKeyByKeyCode(event.keyCode);
  if (logicalKey == null) {
    return null;
  }

  return KeyUpEvent(
    physicalKey: const PhysicalKeyboardKey(0x00000000),
    logicalKey: logicalKey,
    timeStamp: Duration(milliseconds: DateTime.now().millisecondsSinceEpoch),
  );
}

enum UniproHotkeyAction {
  // 主页
  homeDown("unipro.hotkey.home.down"),
  homeUp("unipro.hotkey.home.up"),
  homeLong("unipro.hotkey.home.long"),

  // 菜单
  callDown("unipro.hotkey.call.down"),
  callUp("unipro.hotkey.call.up"),
  callLong("unipro.hotkey.call.long"),

  // 锁屏
  p1Down("unipro.hotkey.p1.down"),
  p1Up("unipro.hotkey.p1.up"),
  p1Long("unipro.hotkey.p1.long"),

  // 单呼通讯录
  p2Down("unipro.hotkey.p2.down"),
  p2Up("unipro.hotkey.p2.up"),
  p2Long("unipro.hotkey.p2.long"),

  // 组呼通讯录
  p3Down("unipro.hotkey.p3.down"),
  p3Up("unipro.hotkey.p3.up"),
  p3Long("unipro.hotkey.p3.long"),

  // 紧急报警
  sosDown("unipro.hotkey.sos.down"),
  sosUp("unipro.hotkey.sos.up"),
  sosLong("unipro.hotkey.sos.long"),

  // 系统状态查询
  vbDown("unipro.hotkey.vb.down"),
  vbUp("unipro.hotkey.vb.up"),
  vbLong("unipro.hotkey.vb.long"),

  // 呼叫
  pttDown("unipro.hotkey.ptt.down"),
  pttUp("unipro.hotkey.ptt.up"),
  headsetPttDown("unipro.hotkey.headset.ptt.down"),
  headsetPttUp("unipro.hotkey.headset.ptt.up"),

  // 耳机插入
  headsetDown('unipro.hotkey.headset.down'),

  // 音量变更
  mediaVolumeChanged('android.media.VOLUME_CHANGED_ACTION');

  const UniproHotkeyAction(this.action);

  final String action;

  // 通过keyCode值来获取对应的HotKey
  static UniproHotkeyAction fromAction(String action) {
    return UniproHotkeyAction.values.firstWhere((element) => element.action == action);
  }

  // 判断是否有指定的action
  static bool hasAction(String action) {
    return UniproHotkeyAction.values.any((element) => element.action == action);
  }

  @override
  String toString() {
    return 'UniproHotkeyAction(action: $action)';
  }
}

// unipro.hotkey
class UniproHotkey {
  static final String homeDown = UniproHotkeyAction.homeDown.action;
  static final String homeUp = UniproHotkeyAction.homeUp.action;
  static final String homeLong = UniproHotkeyAction.homeLong.action;

  static final String callDown = UniproHotkeyAction.callDown.action;
  static final String callUp = UniproHotkeyAction.callUp.action;
  static final String callLong = UniproHotkeyAction.callLong.action;

  static final String p1Down = UniproHotkeyAction.p1Down.action;
  static final String p1Up = UniproHotkeyAction.p1Up.action;
  static final String p1Long = UniproHotkeyAction.p1Long.action;

  static final String p2Down = UniproHotkeyAction.p2Down.action;
  static final String p2Up = UniproHotkeyAction.p2Up.action;
  static final String p2Long = UniproHotkeyAction.p2Long.action;

  static final String p3Down = UniproHotkeyAction.p3Down.action;
  static final String p3Up = UniproHotkeyAction.p3Up.action;
  static final String p3Long = UniproHotkeyAction.p3Long.action;

  static final String sosDown = UniproHotkeyAction.sosDown.action;
  static final String sosUp = UniproHotkeyAction.sosUp.action;
  static final String sosLong = UniproHotkeyAction.sosLong.action;

  static final String vbDown = UniproHotkeyAction.vbDown.action;
  static final String vbUp = UniproHotkeyAction.vbUp.action;
  static final String vbLong = UniproHotkeyAction.vbLong.action;

  static final String pttDown = UniproHotkeyAction.pttDown.action;
  static final String pttUp = UniproHotkeyAction.pttUp.action;

  static final String headsetDown = UniproHotkeyAction.headsetDown.action;
  static final String mediaVolumeChanged = UniproHotkeyAction.mediaVolumeChanged.action;
}
