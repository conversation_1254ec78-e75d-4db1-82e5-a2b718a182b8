// 格式化时间
String formatDateTime(DateTime t) {
  return t.toString().split('.')[0];
}

// nowStr 返回当前时间
String nowStr({bool isUtc = true}) {
  DateTime now = isUtc ? DateTime.now().toUtc() : DateTime.now();
  return formatDateTime(now);
}

// 将Utc时间转换为本地时间
String utcToLocal(String utcTime) {
  return formatDateTime(DateTime.parse("${utcTime}Z").toLocal());
}

// 将本地时间转换为Utc时间
String localToUtc(String localTime) {
  return formatDateTime(DateTime.parse(localTime).toUtc());
}
