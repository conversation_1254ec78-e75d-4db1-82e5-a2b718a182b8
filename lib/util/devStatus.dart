/// DevStatus class represents a 6-byte status
class DevStatus {
  final List<int> _status;

  // Getter for raw status bytes
  List<int> get status => List.unmodifiable(_status);

  // Creates a default DevStatus with 6 bytes initialized to 0
  DevStatus() : _status = List.filled(6, 0);

  // Creates a DevStatus from an existing byte list
  DevStatus.fromBytes(List<int> bytes) : _status = List.from(bytes.isEmpty ? DevStatus.createDefaultDevStatus().status : bytes);

  // CreateDefaultDevStatus 创建默认的状态参数
  static DevStatus createDefaultDevStatus() {
    var status = DevStatus();
    status.setEnableEmergencyAlarm(1);
    status.setChannelNo(1);
    status.setDevPriority(1);
    return status;
  }

  // SetByteWithPos 修改参数指定位数据
  void _setByteWithPos(int byteIndex, int bitIndex, int val) {
    var target = _status[byteIndex];
    if (val == 1) {
      // Set specified bit to 1
      var mask = 1 << bitIndex;
      var result = target | mask;
      _status[byteIndex] = result;
      return;
    }
    // Clear specified bit
    var mask = ~(1 << bitIndex);
    var result = target & mask;
    _status[byteIndex] = result;
  }

  // SetByteWithRange 将给定的整数按指定位范围设置到指定的参数上
  void _setByteWithRange(int byteIndex, int startIndex, int endIndex, int val) {
    var target = _status[byteIndex];
    // Determine which bits need to be modified
    var mask = 0;
    for (var i = startIndex; i <= endIndex; i++) {
      mask |= 1 << i;
    }
    // Clear original values in target bits
    target &= ~mask;
    // Move new value to correct position and merge with target byte
    var result = target | (val << startIndex);
    _status[byteIndex] = result;
  }

  // GetByteWithRange 读取指定位范围数据
  int _getByteWithRange(int byteIndex, int startIndex, int endIndex) {
    var target = _status[byteIndex];
    // Create mask
    var mask = 0;
    for (var i = startIndex; i <= endIndex; i++) {
      mask |= 1 << i;
    }
    // Extract with AND operation
    var result = target & mask;
    // Right shift to adjust
    return result >> startIndex;
  }

  // GetByteWithPos 读取指定位数据
  int _getByteWithPos(int byteIndex, int bitIndex) {
    var target = _status[byteIndex];
    // Create mask
    var mask = 1 << bitIndex;
    // Extract with AND operation
    var result = target & mask;
    // Right shift to adjust
    return result >> bitIndex;
  }

  // SetDevType 终端类型为车载台
  void setDevType(int value) {
    _setByteWithPos(0, 0, value);
    _saveToDb();
  }

  int getDevType() => _getByteWithPos(0, 0);

  // SetForbiddenListen 锁机状态BIT0位(禁听)
  void setForbiddenListen(int value) {
    _setByteWithPos(0, 1, value);
    _saveToDb();
  }

  int getForbiddenListen() => _getByteWithPos(0, 1);

  // SetForbiddenCall 锁机状态BIT1位(禁发)
  void setForbiddenCall(int value) {
    _setByteWithPos(0, 2, value);
    _saveToDb();
  }

  int getForbiddenCall() => _getByteWithPos(0, 2);

  // SetAutoPositionMonitoring 已GPS自动监控定位
  void setAutoPositionMonitoring(int value) {
    _setByteWithPos(0, 3, value);
    _saveToDb();
  }

  int getAutoPositionMonitoring() => _getByteWithPos(0, 3);

  // SetCentralPositionMonitoring 已中心监控定位
  void setCentralPositionMonitoring(int value) {
    _setByteWithPos(0, 4, value);
    _saveToDb();
  }

  int getCentralPositionMonitoring() => _getByteWithPos(0, 4);

  // SetEmergencyAlarmAutoPositioning 已紧急报警自动定位
  void setEmergencyAlarmAutoPositioning(int value) {
    _setByteWithPos(0, 5, value);
    _saveToDb();
  }

  int getEmergencyAlarmAutoPositioning() => _getByteWithPos(0, 5);

  // SetRoaming 已漫游
  void setRoaming(int value) {
    _setByteWithPos(0, 6, value);
    _saveToDb();
  }

  int getRoaming() => _getByteWithPos(0, 6);

  // SetOffline 已脱网
  void setOffline(int value) {
    _setByteWithPos(0, 7, value);
    _saveToDb();
  }

  int getOffline() => _getByteWithPos(0, 7);

  // SetEmergencyAllCallDispatch 已紧急全呼调度
  void setEmergencyAllCallDispatch(int value) {
    _setByteWithPos(1, 0, value);
    _saveToDb();
  }

  int getEmergencyAllCallDispatch() => _getByteWithPos(1, 0);

  // SetBaseStationGroupCallDispatch 已基站群呼调度
  void setBaseStationGroupCallDispatch(int value) {
    _setByteWithPos(1, 1, value);
    _saveToDb();
  }

  int getBaseStationGroupCallDispatch() => _getByteWithPos(1, 1);

  // SetChannelGroupCallDispatch 已信道群呼调度
  void setChannelGroupCallDispatch(int value) {
    _setByteWithPos(1, 2, value);
    _saveToDb();
  }

  int getChannelGroupCallDispatch() => _getByteWithPos(1, 2);

  // SetDynamicGroupCallDispatch 已动态组呼调度
  void setDynamicGroupCallDispatch(int value) {
    _setByteWithPos(1, 3, value);
    _saveToDb();
  }

  int getDynamicGroupCallDispatch() => _getByteWithPos(1, 3);

  // SetLevelGroupCallDispatch 已级别组呼调度
  void setLevelGroupCallDispatch(int value) {
    _setByteWithPos(1, 4, value);
    _saveToDb();
  }

  int getLevelGroupCallDispatch() => _getByteWithPos(1, 4);

  // SetFixedGroupCallDispatch 已固定组呼调度
  void setFixedGroupCallDispatch(int value) {
    _setByteWithPos(1, 5, value);
    _saveToDb();
  }

  int getFixedGroupCallDispatch() => _getByteWithPos(1, 5);

  // SetRoamingNetworkDispatch 已漫游联网调度
  void setRoamingNetworkDispatch(int value) {
    _setByteWithPos(1, 6, value);
    _saveToDb();
  }

  int getRoamingNetworkDispatch() => _getByteWithPos(1, 6);

  // SetWorkAreaHasExceededLimit 已工作区越界
  void setWorkAreaHasExceededLimit(int value) {
    _setByteWithPos(1, 7, value);
    _saveToDb();
  }

  int getWorkAreaHasExceededLimit() => _getByteWithPos(1, 7);

  // SetEnableEmergencyAlarm 开启紧急报警
  void setEnableEmergencyAlarm(int value) {
    _setByteWithPos(2, 0, value);
    _saveToDb();
  }

  int getEnableEmergencyAlarm() => _getByteWithPos(2, 0);

  // SetEnableMobileMonitoring 开启移动监控
  void setEnableMobileMonitoring(int value) {
    _setByteWithPos(2, 1, value);
    _saveToDb();
  }

  int getEnableMobileMonitoring() => _getByteWithPos(2, 1);

  // SetEnableSentryMonitoring 开启岗哨监控
  void setEnableSentryMonitoring(int value) {
    _setByteWithPos(2, 2, value);
    _saveToDb();
  }

  int getEnableSentryMonitoring() => _getByteWithPos(2, 2);

  // SetEnableOutOfBoundsMonitoring 开启出界监控
  void setEnableOutOfBoundsMonitoring(int value) {
    _setByteWithPos(2, 3, value);
    _saveToDb();
  }

  int getEnableOutOfBoundsMonitoring() => _getByteWithPos(2, 3);

  // SetEnableInBoundsMonitoring 开启入界监控
  void setEnableInBoundsMonitoring(int value) {
    _setByteWithPos(2, 4, value);
    _saveToDb();
  }

  int getEnableInBoundsMonitoring() => _getByteWithPos(2, 4);

  // SetEnableSchedulingFunc 开启调度功能
  void setEnableSchedulingFunc(int value) {
    _setByteWithPos(2, 5, value);
    _saveToDb();
  }

  int getEnableSchedulingFunc() => _getByteWithPos(2, 5);

  // SetEnableMonitoringFunc 开启监听功能
  void setEnableMonitoringFunc(int value) {
    _setByteWithPos(2, 6, value);
    _saveToDb();
  }

  int getEnableMonitoringFunc() => _getByteWithPos(2, 6);

  // SetEnablePeripheralFunc 开启外设功能
  void setEnablePeripheralFunc(int value) {
    _setByteWithPos(2, 7, value);
    _saveToDb();
  }

  int getEnablePeripheralFunc() => _getByteWithPos(2, 7);

  // SetEmergencyAlarm 发生紧急报警
  void setEmergencyAlarm(int value) {
    _setByteWithPos(3, 0, value);
    _saveToDb();
  }

  int getEmergencyAlarm() => _getByteWithPos(3, 0);

  // SetMobileMonitoringAlarm 移动监控报警
  void setMobileMonitoringAlarm(int value) {
    _setByteWithPos(3, 1, value);
    _saveToDb();
  }

  int getMobileMonitoringAlarm() => _getByteWithPos(3, 1);

  // SetSentryMonitoringAlarm 岗哨监控报警
  void setSentryMonitoringAlarm(int value) {
    _setByteWithPos(3, 2, value);
    _saveToDb();
  }

  int getSentryMonitoringAlarm() => _getByteWithPos(3, 2);

  // SetOutOfBoundsMonitoringAlarm 出界监控报警
  void setOutOfBoundsMonitoringAlarm(int value) {
    _setByteWithPos(3, 3, value);
    _saveToDb();
  }

  int getOutOfBoundsMonitoringAlarm() => _getByteWithPos(3, 3);

  // SetInBoundsMonitoringAlarm 入界监控报警
  void setInBoundsMonitoringAlarm(int value) {
    _setByteWithPos(3, 4, value);
    _saveToDb();
  }

  int getInBoundsMonitoringAlarm() => _getByteWithPos(3, 4);

  // SetForcedOfflineAlarm 强行脱网报警
  void setForcedOfflineAlarm(int value) {
    _setByteWithPos(3, 5, value);
    _saveToDb();
  }

  int getForcedOfflineAlarm() => _getByteWithPos(3, 5);

  // SetUnderVoltageAlarm 发生欠压报警
  void setUnderVoltageAlarm(int value) {
    _setByteWithPos(3, 6, value);
    _saveToDb();
  }

  int getUnderVoltageAlarm() => _getByteWithPos(3, 6);

  // SetGpsFailureAlarm GPS故障报警
  void setGpsFailureAlarm(int value) {
    _setByteWithPos(3, 7, value);
    _saveToDb();
  }

  int getGpsFailureAlarm() => _getByteWithPos(3, 7);

  // SetChannelNo 信道状态位，即信道号
  void setChannelNo(int value) {
    // Channel uses 12 bits
    value = value & 0x0FFF;
    // Low byte uses 5 bits
    var lowByte = value & 0x1F;
    // High byte uses 7 bits
    var highByte = value >> 5;
    _setByteWithRange(4, 0, 4, lowByte);
    _setByteWithRange(5, 0, 6, highByte);
    _saveToDb();
  }

  int getChannelNo() {
    var lowByte = _getByteWithRange(4, 0, 4);
    var highByte = _getByteWithRange(5, 0, 6);
    return (highByte << 5) + lowByte;
  }

  // SetDutyAircraft 兼任值班机
  void setDutyAircraft(int value) {
    _setByteWithPos(4, 5, value);
    _saveToDb();
  }

  int getDutyAircraft() => _getByteWithPos(4, 5);

  // SetDevPriority 级别BIT0位，级别BIT1位，即终端优先级
  void setDevPriority(int value) {
    _setByteWithRange(4, 6, 7, value);
    _saveToDb();
  }

  int getDevPriority() => _getByteWithRange(4, 6, 7);

  // SetCommandAircraft 指挥机标志
  void setCommandAircraft(int value) {
    _setByteWithPos(5, 7, value);
    _saveToDb();
  }

  int getCommandAircraft() => _getByteWithPos(5, 7);

  // Save to database
  void _saveToDb() {
    // Implement database saving logic here
    // Note: This should be implemented according to your specific database requirements
  }
}
