import 'dart:async';

import 'package:geolocator/geolocator.dart';

import '../app_proto/app_proto.pb.dart';
import 'dateTime.dart';
import 'logger.dart';

final LocationSettings defaultLocationSettings = AndroidSettings(
  accuracy: LocationAccuracy.best,
  timeLimit: const Duration(seconds: 6),
  forceLocationManager: true,
);

Future<bool> _handlePermission() async {
  bool serviceEnabled;
  LocationPermission permission;

  // Test if location services are enabled.
  serviceEnabled = await Geolocator.isLocationServiceEnabled();
  if (!serviceEnabled) {
    // Location services are not enabled don't continue
    // accessing the position and request users of the
    // App to enable the location services.
    return Future.error('Location services are disabled.');
  }

  permission = await Geolocator.checkPermission();
  if (permission == LocationPermission.denied) {
    permission = await Geolocator.requestPermission();
    if (permission == LocationPermission.denied) {
      // Permissions are denied, next time you could try
      // requesting permissions again (this is also where
      // Android's shouldShowRequestPermissionRationale
      // returned true. According to Android guidelines
      // your App should show an explanatory UI now.
      return Future.error('Location permissions are denied');
    }
  }

  if (permission == LocationPermission.deniedForever) {
    // Permissions are denied forever, handle appropriately.
    return Future.error('Location permissions are permanently denied, we cannot request permissions.');
  }

  return true;
}

Future<Position?> getLastKnownPosition() async {
  await _handlePermission();
  Position? position = await Geolocator.getLastKnownPosition();
  logger.i("getLastKnownPosition: $position");
  return position;
}

Future<Position> getCurrentPosition({LocationSettings? locationSettings}) async {
  await _handlePermission();
  Position position = await Geolocator.getCurrentPosition(locationSettings: locationSettings ?? defaultLocationSettings);
  logger.i("getCurrentPosition: $position");
  return position;
}

typedef PositionCallback = void Function(Position position);
typedef PositionErrorCallback = void Function(Object error);
typedef PositionStreamCancelCallback = void Function();

PositionStreamCancelCallback getPositionStream(PositionCallback onData, {LocationSettings? locationSettings, PositionErrorCallback? onError}) {
  final positionStream = Geolocator.getPositionStream(locationSettings: locationSettings ?? defaultLocationSettings);
  StreamSubscription<Position>? positionStreamSubscription;

  void cancelListenPosition() {
    positionStreamSubscription?.cancel();
    positionStreamSubscription = null;
  }

  positionStreamSubscription = positionStream.listen((Position position) {
    logger.i("getPositionStream: $position");
    onData(position);
  });
  positionStream.handleError((err) {
    logger.e("getPositionStream handleError: $err");
    cancelListenPosition();
  });

  return cancelListenPosition;
}

// 尝试获取已知位置，先获取当前位置，失败后再获取上次知道的位置，可能为空
// 返回值为元组，第一个值为位置，第二个值是否为当前位置
Future<(Position?, bool)> tryGetKnownPosition({LocationSettings? locationSettings}) async {
  try {
    var position = await getCurrentPosition(locationSettings: locationSettings);
    return (position, true);
  } catch (e) {
    logger.w("tryGetKnownPosition getCurrentPosition error: $e");
    return (await getLastKnownPosition(), false);
  }
}

// m/s转换为km/h
double speed2kmh(double speed) {
  return speed * 3.6;
}

// m/s转换为km/h
double speed2ms(double speed) {
  return speed / 3.6;
}

// 将定位的Position转换为gps84， 10分钟内的数据默认
gps84 getGps84FromPosition(Position position, {bool? isLastKnown}) {
  var av = isLastKnown == true ? 2 : 1;
  if (position.isMocked) {
    av = 0;
  }

  return gps84(
    gpsTime: formatDateTime(position.timestamp),
    av: av,
    lat: position.latitude,
    lon: position.longitude,
    speed: speed2kmh(position.speed),
    direction: position.heading.toInt(),
    altitude: position.altitude.toInt(),
  );
}
