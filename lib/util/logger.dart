import 'dart:io';

import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

// 加载运行环境
const bool isProduct = bool.fromEnvironment("dart.vm.product");

Future<String> getLogPath() async {
  var p = await getApplicationCacheDirectory();
  return p.path;

  // var logPath = '${p.path}/logger';
  // // Ensure the directory exists
  // var dir = Directory(logPath);
  // if (!await dir.exists()) {
  //   await dir.create(recursive: true);
  // }
  //
  // return logPath;
}

class FileOutput extends LogOutput {
  final File file;

  FileOutput(this.file);

  @override
  void output(OutputEvent event) {
    for (var line in event.lines) {
      file.writeAsStringSync('$line\n', mode: FileMode.append, flush: true);
    }
  }
}

class MultiOutput extends LogOutput {
  final List<LogOutput> outputs;

  MultiOutput(this.outputs);

  @override
  void output(OutputEvent event) {
    for (var output in outputs) {
      output.output(event);
    }
  }
}

late final Logger logger;

// 清除指定天数之前的日志文件，默认保留3天
Future<void> _clearOldLogs({int keepDays = 3, String? logPath}) async {
  var p = logPath ?? await getLogPath();
  var dir = Directory(p);
  // 将指定日期外的文件删除
  var now = DateTime.now();
  var keepDate = now.subtract(Duration(days: keepDays));
  await for (var file in dir.list()) {
    if (file is File) {
      var lastModified = await file.lastModified();
      if (lastModified.isBefore(keepDate)) {
        await file.delete();
      }
    }
  }
}

Future<void> initLogger() async {
  // Logger.level = isProduct ? Level.warning : Level.debug;

  // 清除旧日志文件
  var p = await getLogPath();
  _clearOldLogs(logPath: p);

  // 创建日志文件, 命名格式为ogapp-20250115.log
  var logCreateTime = DateTimeFormat.onlyDate(DateTime.now()).replaceAll('-', '');
  var file = File('$p/poc-${logCreateTime}.log');
  List<LogOutput> logOutputs = [FileOutput(file)];
  if (!isProduct) {
    logOutputs.insert(0, ConsoleOutput());
  }

  logger = Logger(
    printer: PrettyPrinter(
      // Number of method calls to be displayed
      methodCount: 3,
      // Number of method calls if stacktrace is provided
      errorMethodCount: 8,
      // Width of the output
      lineLength: 120,
      // Colorful log messages
      colors: true,
      // Print an emoji for each log message
      printEmojis: true,
      // Should each log print contain a timestamp
      dateTimeFormat: DateTimeFormat.dateAndTime,
    ),
    // level: isProduct ? Level.warning : Level.debug,
    level: Level.debug,
    output: MultiOutput(logOutputs), // Use the custom FileOutput
  );
}
