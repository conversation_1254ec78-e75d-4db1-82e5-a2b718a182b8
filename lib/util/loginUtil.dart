import 'dart:async';

import 'package:bf8100deviceapp/util/platformChannel.dart';
import 'package:bf8100deviceapp/util/snackBarMessage.dart';
import 'package:flutter/cupertino.dart';

import '../app_proto/app_proto.pb.dart';
import '../app_proto/bf8100.pb.dart';
import '../app_proto/bf_radio.pb.dart';
import '../i18n/locale.dart';
import '../riverpod/account.dart';
import '../riverpod/contacts.dart';
import '../riverpod/serverConnect.dart';
import '../riverpod/serverSetting.dart';
import '../router.dart';
import '../services/funcKeyEvent.dart';
import '../services/initPocData.dart';
import '../services/rpcCmd.dart';
import '../services/rpcSocket.dart';
import 'logger.dart';
import 'sqlite.dart';

class LoginResCode {
  // 0:登录成功，
  // 1:重复登录
  // 4:密码不对
  // 5:没有指定密码登录
  // 10:登录失败,不存在此设备
  // 44:bad param
  // 303: 用户没有指定设备
  // 404: 此session id不存在,需要换用户名密码登录
  // 500: 服务器内部错误
  static const int success = 0;
  static const int repeatLogin = 1;
  static const int passwordError = 4;
  static const int noPassword = 5;
  static const int noDevice = 10;
  static const int badParam = 44;
  static const int userNoDevice = 303;
  static const int sessionIdNotExist = 404;
  static const int serverError = 500;

  static Map<int, String> msgByCode = {
    success: loginSuccess.i18n,
    repeatLogin: loginRepeat.i18n,
    passwordError: loginPasswordError.i18n,
    noPassword: loginNoPassword.i18n,
    noDevice: loginNoDevice.i18n,
    badParam: loginBadParam.i18n,
    userNoDevice: loginUserNoDevice.i18n,
    sessionIdNotExist: loginSessionIdNotExist.i18n,
    serverError: loginServerError.i18n,
  };

  static String getMsgByCode(int code) {
    var msg = msgByCode[code];
    if (msg == null) {
      return 'unknown error code: $code';
    }
    return msg;
  }
}

// 登录成功后，加载完本地或从服务器获取完联系人
// 防止一登录成功后，服务器就发送了加入任务组，此时还没有联系人，加入任务组时会把本地保存的联系人覆盖掉
Completer<bool> contactIsInitCompleter = Completer<bool>();

Future<void> _loginSuccessHandler(rpc_cmd loginResp, String dmrId, String pw) async {
  var ref = createContainerWithRooContext();
  var p = ref.read(accountProvider.notifier);
  final loginRespBody = resp_login.fromBuffer(loginResp.body);
  // 将userInfo存储在本地
  var account = ref.read(accountProvider);
  var canEditLoginParam = account.canEditLoginParam;
  p.login(UserInfo(
      isLogin: true,
      name: loginRespBody.device.selfId,
      dmrId: dmrId.toUpperCase(),
      sessionId: loginResp.paraStr,
      password: pw,
      priority: loginRespBody.device.priority,
      orgRid: loginRespBody.device.orgId,
      canEditLoginParam: canEditLoginParam));

  // 登陆成功后，初始化本地数据
  var isInit = await initPocData(loginRespBody.settingLastUpdateTime);
  if (!isInit) {
    return;
  }
  // 初始化数据完成， 通知到goproxy
  var rpcCmd = rpcCmdWithSeqNo(
    cmd: cmd_code.cmd_notify_init_data_finish,
  );
  sendRpcCmd(rpcCmd, noResponse: true);
  contactIsInitCompleter.complete(true);
  // 将默认的发射组设置到当前的呼叫目标上
  var callTargetNotifier = ref.read(callTargetProvider.notifier);
  callTargetNotifier.updateDefaultCallDmrId(defaultCallDmrId);

  speakLoginAccountInfo();

  // 向服务器上报当前收听组
  var currentListenGroup = ref.read(listenGroupsProvider);
  var contacts = ref.read(contactsProvider);
  Contact? taskGroup;
  try {
    taskGroup = contacts.firstWhere((Contact c) => c.isTaskGroup());
  } catch (e) {
    taskGroup = null;
  }
  // 任务组存在？ 判断当前收听组中是否包含任务组
  if (taskGroup != null) {
    var notIn = !currentListenGroup.contains(taskGroup.intDmrId);
    if (notIn) {
      setCurrentTaskGroupDmrId(taskGroup.dmrId);
      currentListenGroup.add(taskGroup.intDmrId);
    }
  }

  var listenGroupInfo = PocDefaultGroup(
    defaultListenGroupDmrids: currentListenGroup,
    defaultSendGroupDmrid: defaultSendGroupDmrid,
  );
  updatePocListenGroup(listenGroupInfo);
  queryOnlineDevice(processOnlineDevices);
}

// 登录成功回返sessionId,登录失败返回空字符串
Future<bool> loginHandler(String dmrId, {bool ignoreFalseMsg = false, bool ignoreSessionId = false, bool sendLoginTimeOut = false, String pwd = '', String sessionId = ''}) async {
  bool isPasswordLogin = sessionId.isEmpty || ignoreSessionId;
  final req = req_login();
  req.userName = dmrId.toUpperCase();
  req.userPass = isPasswordLogin ? pwd : sessionId;
  req.loginMethod = isPasswordLogin ? 0 : 1;
  req.canDisplayMap = false;
  logger.i('login req: $req');
  final data = req.writeToBuffer();
  var rpc = rpcCmdWithSeqNo();
  rpc.body = data;
  rpc.cmd = cmd_code.cmd_req_login.value;
  // 登录前将该completer重置，防止再次登录报错completer已完成
  contactIsInitCompleter = Completer<bool>();

  try {
    // 监听服务器广播的指令处理 _globalPushEvent
    listenRpcMessageStream();
    rpc_cmd loginResp = await sendRpcCmd(rpc, responseCmdCode: cmd_code.cmd_resp_login);
    logger.i('loginResp: $loginResp');
    switch (loginResp.res) {
      case LoginResCode.success:
        await _loginSuccessHandler(loginResp, dmrId, pwd);
        var ref = createContainerWithRooContext();
        // 清除网络未连接状态
        var serverConnectNotifier = ref.read(serverConnectProvider.notifier);
        serverConnectNotifier.update(true);
        onFuncKeyEvent();
        return true;
      // case LoginResCode.sessionIdNotExist:
      // case LoginResCode.repeatLogin:
      // case LoginResCode.passwordError:
      // case LoginResCode.noPassword:
      // case LoginResCode.noDevice:
      // case LoginResCode.badParam:
      // case LoginResCode.userNoDevice:
      // case LoginResCode.serverError:
      default:
        if (ignoreFalseMsg) {
          return false;
        }
        var failedReason = LoginResCode.getMsgByCode(loginResp.res);
        PlatformChannel.speakText(failedReason);
        showSnackBarWithOption(Text(failedReason), type: SnackBarMessageType.warning);
        return false;
    }
  } on TimeoutException catch (e) {
    logger.e('login timeout: $e');
    if (sendLoginTimeOut) {
      _notifyAutoLoginTimeoutHandler(dmrId);
    }
    if (ignoreFalseMsg) {
      return false;
    }
    var failedReason = requestTimeout.i18n;
    PlatformChannel.speakText(failedReason);
    showSnackBarWithOption(Text(failedReason), type: SnackBarMessageType.warning);
    return false;
  } catch (err) {
    logger.e('login error: $err');
    return false;
  }
}

Future<void> _notifyAutoLoginTimeoutHandler(String dmrId) async {
  // 如果是用户主动退出登录，不再通知到goproxy
  bool activeLogout = await isUserActiveLogout(dmrId);
  if (activeLogout) {
    return;
  }

  var configBuffer = PocConfigTable.queryWithBuffer(dmrId);
  // 登录超时，通知到goproxy, 只用发送一次
  var rpcCmd = rpcCmdWithSeqNo(
    cmd: cmd_code.cmd_notify_login_timeout,
    paraStr: dmrId,
    body: configBuffer ?? <int>[],
  );
  sendRpcCmd(rpcCmd, noResponse: true);
}

// 更新服务器设置
Future<bool> setServerSetting() async {
  var ref = createContainerWithRooContext();
  var sp = ref.read(serverSettingProvider.notifier);
  if (sp.isUpdateServerSetting) {
    return true;
  }

  var serverSetting = ref.read(serverSettingProvider);
  logger.i('updateServerSetting: host=${serverSetting.host}, port=${serverSetting.port}');
  var isOk = await updateServerSetting(serverSetting.host, serverSetting.port);
  if (!isOk) {
    showSnackBarWithOption(Text(updateServerFailed.i18n), type: SnackBarMessageType.warning);
    return false;
  }
  // 已经更新过服务器设置标记
  logger.i('update server setting in login page');
  sp.updateIsUpdateServerSetting(true);
  return true;
}

void speakLoginAccountInfo() {
  var ref = createContainerWithRooContext();
  var accountNotifier = ref.read(accountProvider.notifier);
  if (!accountNotifier.needSpeakAccount) {
    return;
  }

  // 播报登录成功
  accountNotifier.needSpeakAccount = false;
  PlatformChannel.speakText(loginSuccess.i18n);
  showSnackBarWithOption(Text(loginSuccess.i18n), type: SnackBarMessageType.success);

  // 播报登录用户信息
  var account = ref.read(accountProvider);
  PlatformChannel.speakText(speechCurrentUser.i18n.fill([account.name]));

  // 播报发射组信息
  var contactsProviderNotifier = ref.read(contactsProvider.notifier);
  var txDmrId = ref.read(callTargetProvider).defaultCallDmrId;
  var txContact = contactsProviderNotifier.lookupContact(txDmrId);
  PlatformChannel.speakText(speechCurrentTxGroup.i18n.fill([txContact?.name ?? '']));
}

void processOnlineDevices(rpc_cmd rpc) {
  var ref = createContainerWithRooContext();
  var onlineDeviceDmrIdsNotify = ref.read(onlineDeviceDmrIdsProvider.notifier);
  var onlineDevices = cc183.fromBuffer(rpc.body);
  logger.i('processOnlineDevices: $onlineDevices');
  onlineDeviceDmrIdsNotify.update(onlineDevices.dmrids);
}
