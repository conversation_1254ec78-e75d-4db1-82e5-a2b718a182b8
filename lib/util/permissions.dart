import 'package:permission_handler/permission_handler.dart';

// 因为使用了33+版本编译，需要显式申请权限
Future<void> initPermissions() async {
  // 允许定位
  await Permission.location.request();
  await Permission.locationWhenInUse.request();
  await Permission.locationAlways.request();

  // 允许存储
  await Permission.storage.request();
  await Permission.manageExternalStorage.request();

  // 允许通知
  await Permission.notification.request();

  // 允许蓝牙
  await Permission.bluetooth.request();
  await Permission.bluetoothConnect.request();

  // 允许麦克风
  await Permission.microphone.request();

  // 允许媒体播放
  await Permission.mediaLibrary.request();
}
