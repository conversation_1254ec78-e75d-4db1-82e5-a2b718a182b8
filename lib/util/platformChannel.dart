import 'dart:async';

import 'package:flutter/services.dart';

import 'logger.dart';
import 'customKey.dart';

// 需要初始化，接收来自Android端的事件
class PlatformChannel {
  static late final MethodChannel _platformChannel;

  // static late final EventChannel _platformEventChannel;
  // static late final StreamSubscription _receiveStreamSubscription;

  static final StreamController<UniproHotkeyAction> _hotkeyActionController = StreamController<UniproHotkeyAction>.broadcast();

  static Stream<UniproHotkeyAction> get hotkeyActionStream => _hotkeyActionController.stream;

  static void onHotKeyEvent(MethodCall call) {
    try {
      var action = call.arguments["action"];
      var hotkeyAction = UniproHotkeyAction.fromAction(action);
      _hotkeyActionController.add(hotkeyAction);
    } catch (err) {
      logger.e("_platformChannel.setMethodCallHandler catch error: $err");
    }
  }

  static void mainActivityFinish(MethodCall call) {
    // 主Activity退出，结束Flutter进程
    logger.w("mainActivityFinish, exit flutter process");
    SystemNavigator.pop();
  }

  // 初始化
  static void initChannel() {
    // Flutter -> Android
    _platformChannel = const MethodChannel('com.bfdx.bf8100deviceapp/method');
    _platformChannel.setMethodCallHandler((MethodCall call) async {
      // logger.d('setMethodCallHandler: call=$call, arguments.runtimeType=${call.arguments.runtimeType}');

      if (call.method == "onHotKeyEvent") {
        onHotKeyEvent(call);
        return;
      }

      if (call.method == "mainActivityFinish") {
        mainActivityFinish(call);
        return;
      }
    });

    // Android -> Flutter
    // _platformEventChannel = const EventChannel("com.bfdx.bf8100deviceapp/event");
    // _receiveStreamSubscription = _platformEventChannel.receiveBroadcastStream().listen((event) {
    //   logger.d('receiveBroadcastStream: event=$event, runtimeType=${event.runtimeType}');
    // });
  }

  static Future<dynamic> invokeMethod(String method, [dynamic arguments]) async {
    return _platformChannel.invokeMethod(method, arguments);
  }

  static void dispose() {
    _platformChannel.setMethodCallHandler(null);
    // _receiveStreamSubscription.cancel();
    _hotkeyActionController.close();
  }

  // 设置TTS语言
  static Future<bool> setSpeechLanguage(String language) async {
    return await invokeMethod('setSpeechLanguage', {'language': language});
  }

  // 调用TTS引擎播报文本
  // mediaIdle = true, 只有在设备空闲时才播放
  static Future<bool> speakText(String text, {TTSQueueMode queueMode = TTSQueueMode.add, bool mediaIdle = false}) async {
    return await invokeMethod('speakText', {'text': text, 'queueMode': queueMode.value, 'mediaIdle': mediaIdle});
  }

  // 控制LED灯常亮
  static Future<bool> ledSetPtt(LEDMode mode) async {
    return await invokeMethod('ledSetPtt', {'mode': mode.value});
  }

  // 控制LED灯闪烁
  static Future<bool> ledSetFlicker(LEDColorType colorType, {bool? flicker, int? delayOn, int? delayOff}) async {
    var args = <String, Object>{'color': colorType.value};
    if (flicker != null) {
      args['flicker'] = flicker;
    }
    if (delayOn != null) {
      args['delay_on'] = delayOn;
    }
    if (delayOff != null) {
      args['delay_off'] = delayOff;
    }
    return await invokeMethod('ledSetFlicker', args);
  }

  // 从Android端获取应用版本信息
  static Future<AppBuildInfo> getAppBuildInfo() async {
    final data = (await invokeMethod('getAppBuildInfo')).cast<String, dynamic>();
    return AppBuildInfo.fromJson(data);
  }

  static Future<bool> checkAccessibilityEnabled() async {
    return await invokeMethod('checkAccessibilityEnabled');
  }

  static Future<void> openAccessibilitySettings() async {
    return await invokeMethod('openAccessibilitySettings');
  }

  static Future<void> silentInstallApp(String filePath) async {
    var args = <String, Object>{'filePath': filePath};
    return await invokeMethod('silentInstallApp', args);
  }

  // 打开内置的设置页面
  static Future<void> openUniproSettings() async {
    return await invokeMethod('openUniproSettings');
  }

  // 唤醒屏幕，默认30秒
  static Future<void> wakeUp({int timeout = 30 * 1000}) async {
    return await invokeMethod('wakeUp', {'timeout': timeout});
  }

  // 熄屏
  static Future<void> wakeLock() async {
    return await invokeMethod('wakeLock');
  }

  // 播放指定的音频文件
  static Future<bool> playSoundEffects(String resourceName) async {
    try {
      return (await invokeMethod('playSoundEffects', {'resourceName': resourceName}));
    } on PlatformException catch (e) {
      logger.d("playSoundEffects error: ${e.message}");
      return false;
    }
  }
}

// TTS引擎播报文本队列模式，参考TextToSpeech
enum TTSQueueMode {
  // QUEUE_FLUSH
  flush(0),
  // QUEUE_ADD
  add(1),
  // QUEUE_DESTROY
  destroy(2);

  const TTSQueueMode(this.value);

  final int value;

  static TTSQueueMode fromValue(int value) {
    return TTSQueueMode.values.firstWhere((element) => element.value == value);
  }
}

// LED灯常亮模式
enum LEDMode {
  turnOff("turn_off"),
  red("red"),
  green("green");
  // yellow("yellow");

  const LEDMode(this.value);

  final String value;
}

// unipro.led.set亮灯类型
enum LEDColorType {
  none(0),
  red(1),
  green(2);
  // yellow(3);

  const LEDColorType(this.value);

  final int value;
}

class AppBuildInfo {
  // 格式: ${major}.${minor}.${patch}
  String version;

  // YYYY-MM-DD HH:mm:ss
  String buildTime;

  AppBuildInfo({required this.version, required this.buildTime});

  factory AppBuildInfo.fromJson(Map<String, dynamic> json) {
    return AppBuildInfo(
      version: json['version'],
      buildTime: json['buildTime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'buildTime': buildTime,
    };
  }

  @override
  String toString() {
    return 'AppBuildInfo{version: $version, buildTime: $buildTime}';
  }
}
