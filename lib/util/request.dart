import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

import 'logger.dart';
import 'snackBarMessage.dart';

class Bf8100Request {
  static const _baseUrl = 'http://t2.bfdx.net:2235';
  static const _softwarePath = '/bf8100/assets/software';
  static Dio? _bf8100Dio;

  static Dio getDio() {
    _bf8100Dio ??= Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 10),
    ));

    return _bf8100Dio!;
  }

  static void dispose() {
    if (_bf8100Dio == null) return;
    _bf8100Dio!.close(force: true);
  }

  static List<String> _parseAppAssets(String body) {
    var r = RegExp(r'<a .*>(BF8100DeviceApp-(.*)-release\.apk)</a>');
    var res = r.allMatches(body);
    var result = <String>[];
    for (Match m in res) {
      var version = m.group(2) ?? '';
      if (version.isEmpty) continue;
      result.add(version.trim());
    }

    return result;
  }

  // 版本字符串比较函数
  // -1: version1 < version2
  // 0: version1 == version2
  // 1: version1 > version2
  static int compareVersionStrings(String version1, String version2) {
    List<int> v1 = version1.split('.').map(int.parse).toList();
    List<int> v2 = version2.split('.').map(int.parse).toList();
    int len = max(v1.length, v2.length);
    for (int i = 0; i < len; i++) {
      int val1 = (i < v1.length) ? v1[i] : 0;
      int val2 = (i < v2.length) ? v2[i] : 0;
      if (val1 < val2) {
        return -1;
      } else if (val1 > val2) {
        return 1;
      }
    }
    return 0;
  }

  // 请求最新版本
  static Future<String?> requestAppLatestVersion(String currentVersion) async {
    try {
      final dio = getDio();
      var response = await dio.get<String>(_softwarePath, queryParameters: {'r': Random().nextInt(0xFFFFFFFF).toString()});
      if (response.statusCode != 200) {
        return null;
      }

      var result = _parseAppAssets(response.data.toString());
      if (result.isEmpty) {
        return null;
      }

      if (result.length == 1) {
        return result.first;
      }

      // 服务器上最新的版本号
      var latestVersion = result.reduce((v1, v2) {
        var result = compareVersionStrings(v1, v2);
        return result == 0 ? v1 : (result > 0 ? v1 : v2);
      });

      // 判断服务器上最新的版本号是否大于当前版本号
      if (compareVersionStrings(latestVersion, currentVersion) > 0) {
        return latestVersion;
      }
      return null;
    } catch (err) {
      logger.e('Bf8100Request _requestAppVersions err: $err');
      return null;
    }
  }

  static String _getApkFileName(String latestVersion) {
    return 'BF8100DeviceApp-$latestVersion-release.apk';
  }

  static Future<String> _getApkFilePath(String latestVersion) async {
    return '${(await getTemporaryDirectory()).path}/${_getApkFileName(latestVersion)}';
  }

  static Future<String?> _detectApkFilePath(String latestVersion) async {
    final filePath = await _getApkFilePath(latestVersion);
    final f = File(filePath);
    final isExist = await f.exists();
    if (isExist) {
      return filePath;
    }
    return null;
  }

  static Future<String?> downloadApp(String latestVersion) async {
    try {
      var result = await _detectApkFilePath(latestVersion);
      if (result != null) {
        return result;
      }

      final dio = getDio();
      final fileName = _getApkFileName(latestVersion);
      final appPath = await _getApkFilePath(latestVersion);
      final response = await dio.download('$_softwarePath/$fileName', appPath);
      if (response.statusCode != 200) {
        showSnackBarWithOption(Text(response.statusMessage ?? response.statusCode.toString()));
        return null;
      }
      return appPath;
    } catch (err) {
      logger.e('Bf8100Request downloadApp err: $err');
      return null;
    }
  }
}
