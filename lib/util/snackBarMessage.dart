import 'dart:async';

import 'package:flutter/material.dart';

import '../router.dart';

const Duration _snackBarDisplayDuration = Duration(milliseconds: 4000);

enum SnackBarMessageType {
  none,
  info,
  success,
  warning,
  error,
}

Color infoColor = Colors.lightBlue;
Color successColor = Colors.green;
Color warningColor = Colors.orange;
Color errorColor = Colors.red;
Map<SnackBarMessageType, Color> _snackBarColors = {
  SnackBarMessageType.info: infoColor,
  SnackBarMessageType.success: successColor,
  SnackBarMessageType.warning: warningColor,
  SnackBarMessageType.error: errorColor,
};
double _snackBarHeight = 0;

double get snackBarHeight => _snackBarHeight;

ScaffoldFeatureController<SnackBar, SnackBarClosedReason> showSnackBarWithOption(
  Widget content, {
  Duration? duration,
  Color? backgroundColor,
  SnackBarAction? action,
  SnackBarMessageType type = SnackBarMessageType.none,
}) {
  Color? bgColor = type == SnackBarMessageType.none ? backgroundColor : _snackBarColors[type];
  return showSnackBar(
    SnackBar(
      content: Center(child: content),
      backgroundColor: bgColor,
      duration: duration ?? _snackBarDisplayDuration,
      action: action,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),
    hideCurrent: true,
  );
}

ScaffoldFeatureController<SnackBar, SnackBarClosedReason> showSnackBar(SnackBar snackBar, {bool hideCurrent = true}) {
  final scaffoldMessenger = ScaffoldMessenger.of(rootNavigatorKey.currentContext!);
  // 移除当前的SnackBar
  if (hideCurrent) {
    scaffoldMessenger.hideCurrentSnackBar();
  }
  return scaffoldMessenger.showSnackBar(snackBar);
}

void removeCurrentSnackBar({SnackBarClosedReason reason = SnackBarClosedReason.remove}) {
  ScaffoldMessenger.of(rootNavigatorKey.currentContext!).removeCurrentSnackBar(reason: reason);
  _snackBarHeight = 0;
}

ScaffoldFeatureController<SnackBar, SnackBarClosedReason> showSnackBarWithOptionWithNLines(String msg,
    {int lines = 2, Duration duration = _snackBarDisplayDuration, SnackBarMessageType type = SnackBarMessageType.none}) {
  return showSnackBarWithOption(
      Text(
        msg,
        textAlign: TextAlign.center,
        maxLines: lines,
        overflow: TextOverflow.ellipsis,
      ),
      type: type,
      duration: duration);
}

OverlayEntry createOverlay(Widget Function(BuildContext context) builder,
    {double? top, double bottom = 0, double? left, double? right, double? width, double height = 36, Color? bgColor, EdgeInsetsGeometry padding = const EdgeInsets.fromLTRB(0, 4, 0, 4)}) {
  OverlayEntry overlayEntry = OverlayEntry(builder: (context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        _snackBarHeight = renderBox.size.height;
      }
    });
    return Positioned(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      width: width ?? MediaQuery.of(context).size.width,
      height: height,
      child: Material(
        color: Colors.transparent,
        child: Container(
          padding: padding,
          decoration: BoxDecoration(
            color: bgColor ?? Colors.lightBlue.shade400,
          ),
          // content的内容需要使用builder包裹，执行markNeedsBuild才能更新内容
          child: builder(context),
        ),
      ),
    );
  });
  Overlay.of(rootNavigatorKey.currentContext!).insert(overlayEntry);
  return overlayEntry;
}

// duration为单位秒
VoidCallback createOverlayWithDuration(Widget Function(BuildContext context, int duration) builder,
    {int duration = 5,
    VoidCallback? onTimeEnd,
    double? top,
    double bottom = 0,
    double? left,
    double? right,
    double? width,
    double height = 36,
    Color? bgColor,
    EdgeInsetsGeometry padding = const EdgeInsets.fromLTRB(0, 4, 0, 4)}) {
  late VoidCallback cancel;
  OverlayEntry? entry;
  entry = createOverlay((BuildContext context) {
    return builder(context, duration);
  }, top: top, bottom: bottom, left: left, right: right, width: width, height: height, bgColor: bgColor, padding: padding);
  var timer = Timer.periodic(const Duration(seconds: 1), (_) {
    if (duration <= 0) {
      cancel();
      onTimeEnd?.call();
      return;
    }
    duration--;
    entry?.markNeedsBuild();
  });
  cancel = () {
    entry?.remove();
    entry = null;
    timer.cancel();
    _snackBarHeight = 0;
  };
  return cancel;
}

// 传入Position,可以更新overlay的位置
VoidCallback createOverlayWithPosition(Widget Function(BuildContext context) builder, {int duration = 3000, VoidCallback? onTimeEnd, VoidCallback? onUpdate}) {
  late VoidCallback cancel;
  OverlayEntry? entry;
  entry = OverlayEntry(
    builder: (context) => builder(context),
  );
  Overlay.of(rootNavigatorKey.currentContext!).insert(entry);
  var timer = Timer.periodic(const Duration(milliseconds: 200), (_) {
    if (duration <= 0) {
      cancel();
      onTimeEnd?.call();
      return;
    }
    duration -= 200;
    entry?.markNeedsBuild();
    onUpdate?.call();
  });
  cancel = () {
    entry?.remove();
    entry = null;
    timer.cancel();
  };
  return cancel;
}
