import 'dart:async' show Completer, FutureOr, StreamController, StreamSubscription, Timer;
import 'dart:io' show WebSocket;

import 'package:flutter/services.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

import 'logger.dart';

typedef OnMessage = void Function(Uint8List message);

// WebSocket的封装，实现自动重连
class WebSocketClient {
  late IOWebSocketChannel _socket;
  late StreamSubscription _sub;

  final int maxReconnectTimes;
  final Iterable<String>? protocols;
  final Map<String, dynamic>? headers;
  final Duration pingInterval;
  final Duration connectTimeout;
  final Duration reconnectInterval;
  final OnMessage onMessage;
  final String Function()? resolveUrlWithAutoReconnect;
  final VoidCallback? onOpen;
  String _url;

  String get url => _url;

  int _status = WebSocket.closed;

  final StreamController<int> _statusController = StreamController<int>.broadcast();

  Stream<int> get statusStream => _statusController.stream;

  int get status => _status;

  bool get isOpen => _status == WebSocket.open;

  DateTime _lastReceivedTime = DateTime.now();

  DateTime get lastReceivedTime => _lastReceivedTime;
  var _reconnectTimes = 0;

  bool _isStopAutoReconnect = false;

  WebSocketClient(
    String url,
    this.onMessage, {
    this.protocols,
    this.headers,
    this.pingInterval = const Duration(seconds: 5),
    this.connectTimeout = const Duration(seconds: 6),
    this.reconnectInterval = const Duration(seconds: 6),
    this.maxReconnectTimes = 10,
    bool immediateConnect = true,
    this.resolveUrlWithAutoReconnect,
    this.onOpen,
  }) : _url = url {
    // 立即连接服务器
    if (immediateConnect) {
      connect();
    }
  }

  void setUrl(String url) {
    _url = url;
  }

  void _updateStatus(int status) {
    _status = status;
    _statusController.add(status);
  }

  FutureOr<dynamic> Function(dynamic V) socketDoneCallback(IOWebSocketChannel oldSocket) {
    return (_) async {
      logger.d("WebSocketClient: connect done, old socket:${oldSocket.hashCode}");
      if (oldSocket != _socket) {
        logger.w("WebSocketClient: reconnect socket changed: ${oldSocket.hashCode} -> ${_socket.hashCode}");
        return;
      }
      _updateStatus(WebSocket.closed);
      _sub.cancel();

      if (++_reconnectTimes > maxReconnectTimes) {
        logger.d("WebSocketClient: reconnect max times");
        return;
      }

      // 自动重连
      await Future.delayed(reconnectInterval);
      // 防止重复连接
      if (_status != WebSocket.closed) {
        return;
      }
      if (_isStopAutoReconnect) {
        return;
      }
      connect();
    };
  }

  Future<void> connect() async {
    _updateStatus(WebSocket.connecting);
    _socket = IOWebSocketChannel.connect(url,
        protocols: protocols, headers: headers, pingInterval: pingInterval, connectTimeout: connectTimeout);
    logger.d("WebSocketClient: connect url=$url, socket: ${_socket.hashCode}");

    // 等待socket关闭，并重连
    _socket.sink.done.then(socketDoneCallback(_socket));

    // 监听socket消息
    _sub = _socket.stream.listen((data) {
      _lastReceivedTime = DateTime.now();
      // logger.d("WebSocketClient: receive: $data");
      onMessage(data);
    }, onError: (Object err) {
      logger.e("WebSocketClient stream listen error: $err");
      // 连接失败，重置url
      if (err is WebSocketChannelException) {
        _url = resolveUrlWithAutoReconnect?.call() ?? _url;
      }
    }, cancelOnError: true);

    // 等待socket准备好
    await _socket.ready;
    _updateStatus(WebSocket.open);
    _reconnectTimes = 0;
    onOpen?.call();
    logger.d("WebSocketClient: connect success");
  }

  Future<void> reconnect() async {
    await close();
    await connect();
  }

  void send(Uint8List bytes) {
    if (!isOpen) {
      logger.e("WebSocketClient send: socket is not open");
      return;
    }

    logger.d("WebSocketClient send: length=${bytes.length}, $bytes");
    _socket.sink.add(bytes);
  }

  Future<void> close({int? closeCode, String? closeReason}) {
    logger.d('WebSocketClient close: closeCode=$closeCode, closeReason=$closeReason');
    _updateStatus(WebSocket.closing);
    return _socket.sink.close(closeCode, closeReason);
  }

  void stopAutoReconnect() {
    _isStopAutoReconnect = true;
  }

  // 等待socket准备好
  Future<bool> awaitReady({Duration timeout = const Duration(seconds: 20)}) async {
    if (isOpen) {
      return Future.value(true);
    }

    var result = Completer<bool>();
    late Timer timer;
    late StreamSubscription sub;
    sub = statusStream.listen((status) {
      if (isOpen) {
        result.complete(true);
        sub.cancel();
        timer.cancel();
      }
    });

    // 超时了，做最后检测处理
    timer = Timer(timeout, () {
      if (!result.isCompleted) {
        result.complete(isOpen);
      }
      sub.cancel();
    });
    return result.future;
  }
}
