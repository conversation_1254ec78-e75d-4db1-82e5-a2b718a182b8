import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqlite3/sqlite3.dart';

import '../app_proto/app_proto.pb.dart';
import '../app_proto/bf8100.pb.dart';
import '../riverpod/account.dart';
import '../router.dart' show createContainerWithRooContext;

String appSupportDir = "/tmp";
late final Database stateDb;

Future<void> initSqlite() async {
  Directory dir = await getApplicationSupportDirectory();
  appSupportDir = dir.path;
  var stateDbPath = join(appSupportDir, "data.db");
  stateDb = sqlite3.open(stateDbPath);

  // 创建表
  DevSmsTable.init();
  PocConfigTable.init();
}

class DevSmsTable {
  static String _getMd5Str(short_messages sms) {
    return md5.convert(sms.writeToBuffer()).toString();
  }

  static Future<void> init() async {
    stateDb.execute('''
    create table if not exists dev_sms (
      pid text not null,
      dmrId text not null,
      sms blob not null
    );
  ''');
  }

  static List<short_messages> query() {
    final ref = createContainerWithRooContext();
    final dmrId = ref.read(accountProvider).dmrId;
    final ResultSet resultSet = stateDb.select('SELECT * FROM dev_sms WHERE dmrId = ?', [dmrId]);
    List<short_messages> result = [];
    for (final Row row in resultSet) {
      result.add(short_messages.fromBuffer(row['sms']));
    }

    return result;
  }

  static void insert(short_messages sms) {
    final ref = createContainerWithRooContext();
    final dmrId = ref.read(accountProvider).dmrId;
    final md5Str = _getMd5Str(sms);
    stateDb.execute('INSERT INTO dev_sms (pid, dmrId, sms) VALUES (?, ?, ?)', [md5Str, dmrId, sms.writeToBuffer()]);
  }

  static void delete(short_messages sms) {
    final ref = createContainerWithRooContext();
    final dmrId = ref.read(accountProvider).dmrId;
    final md5Str = _getMd5Str(sms);
    stateDb.execute('DELETE FROM dev_sms WHERE dmrId = ? AND pid = ?', [dmrId, md5Str]);
  }
}

// pocConfig数据表
class PocConfigTable {
  static Future<void> init() async {
    stateDb.execute('''
    create table if not exists poc_config (
      dmrId text not null primary key,
      config blob not null
    );
  ''');
  }

  static PocConfig? query([String? dmrId]) {
    String finalDmrId;
    if (dmrId == null) {
      final ref = createContainerWithRooContext();
      finalDmrId = ref.read(accountProvider).dmrId;
    } else {
      finalDmrId = dmrId;
    }
    final ResultSet resultSet = stateDb.select('SELECT * FROM poc_config WHERE dmrId = ?;', [finalDmrId]);
    if (resultSet.rows.isEmpty) {
      return null;
    }

    final Row row = resultSet.first;
    final List<int> configBytes = row['config'] as List<int>;
    return PocConfig.fromBuffer(configBytes);
  }

  static List<int>? queryWithBuffer([String? dmrId]) {
    String finalDmrId;
    if (dmrId == null) {
      final ref = createContainerWithRooContext();
      finalDmrId = ref.read(accountProvider).dmrId;
    } else {
      finalDmrId = dmrId;
    }
    final ResultSet resultSet = stateDb.select('SELECT * FROM poc_config WHERE dmrId = ?;', [finalDmrId]);
    if (resultSet.rows.isEmpty) {
      return null;
    }

    final Row row = resultSet.first;
    return row['config'] as List<int>;
  }

  static void insert(PocConfig config) {
    final ref = createContainerWithRooContext();
    final dmrId = ref.read(accountProvider).dmrId;
    stateDb.execute('INSERT INTO poc_config (dmrId, config) VALUES (?, ?);', [dmrId, config.writeToBuffer()]);
  }

  static void updateOrInsert(PocConfig config) {
    final ref = createContainerWithRooContext();
    final dmrId = ref.read(accountProvider).dmrId;
    stateDb.execute('UPDATE poc_config SET config = ? WHERE dmrId = ?;', [config.writeToBuffer(), dmrId]);
    if (stateDb.updatedRows == 0) {
      insert(config);
    }
  }

  static void delete() {
    final ref = createContainerWithRooContext();
    final dmrId = ref.read(accountProvider).dmrId;
    stateDb.execute('DELETE FROM poc_config WHERE dmrId = ?;', [dmrId]);
  }
}
