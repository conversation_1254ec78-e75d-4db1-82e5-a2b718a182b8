// 如果是空，则返回false
import '../i18n/locale.dart';

bool isRequired(String? value) {
  return !(value == null || value.isEmpty);
}

String? requiredHintText(String? value) {
  var valid = isRequired(value);
  return valid ? null : required.i18n;
}

// 如果大于指定长度，则返回false
bool isMaxLength(String? value, {int len = 16}) {
  if (value == null || value.isEmpty) {
    return true;
  }

  return value.length <= len;
}

String? maxLengthHintText(String? value, {int len = 16}) {
  var valid = isMaxLength(value, len: len);
  return valid ? null : maxLengthLimit.i18n.fill([len]);
}

String? rangeValue(int value, {int min = 0x00, int max = 0xFFFF}) {
  if (value < min || value > max) {
    return rangeValueLimit.i18n.fill([min, max]);
  }

  return null;
}
