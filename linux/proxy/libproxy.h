/* Code generated by cmd/cgo; DO NOT EDIT. */

/* package goproxy/libproxy */


#line 1 "cgo-builtin-export-prolog"

#include <stddef.h>

#ifndef GO_CGO_EXPORT_PROLOGUE_H
#define GO_CGO_EXPORT_PROLOGUE_H

#ifndef GO_CGO_GOSTRING_TYPEDEF
typedef struct { const char *p; ptrdiff_t n; } _GoString_;
#endif

#endif

/* Start of preamble from import "C" comments.  */




/* End of preamble from import "C" comments.  */


/* Start of boilerplate cgo prologue.  */
#line 1 "cgo-gcc-export-header-prolog"

#ifndef GO_CGO_PROLOGUE_H
#define GO_CGO_PROLOGUE_H

typedef signed char GoInt8;
typedef unsigned char GoUint8;
typedef short GoInt16;
typedef unsigned short GoUint16;
typedef int GoInt32;
typedef unsigned int GoUint32;
typedef long long GoInt64;
typedef unsigned long long GoUint64;
typedef GoInt64 GoInt;
typedef GoUint64 GoUint;
typedef size_t GoUintptr;
typedef float GoFloat32;
typedef double GoFloat64;
#ifdef _MSC_VER
#include <complex.h>
typedef _Fcomplex GoComplex64;
typedef _Dcomplex GoComplex128;
#else
typedef float _Complex GoComplex64;
typedef double _Complex GoComplex128;
#endif

/*
  static assertion to make sure the file is being used on architecture
  at least with matching size of GoInt.
*/
typedef char _check_for_64_bit_pointer_matching_GoInt[sizeof(void*)==64/8 ? 1:-1];

#ifndef GO_CGO_GOSTRING_TYPEDEF
typedef _GoString_ GoString;
#endif
typedef void *GoMap;
typedef void *GoChan;
typedef struct { void *t; void *v; } GoInterface;
typedef struct { void *data; GoInt len; GoInt cap; } GoSlice;

#endif

/* End of boilerplate cgo prologue.  */

#ifdef __cplusplus
extern "C" {
#endif

extern void ProxyMain();
extern void SetLogOutput(void* cb);
extern GoString TestStr();
extern void ExitProxyMain();
extern void Test();
extern GoUint16 GetProxyPort();
extern void GetValidLocation(GoInt64 t, GoFloat64 lon, GoFloat64 lat, GoFloat64 direction, GoFloat64 speed, GoFloat64 altitude);
extern void GetPhoneStateCallIn();
extern int GetDenoiseSetting();
extern void On8K16BitsSamples(void* data, GoInt sumLen, GoInt dataLen);
extern void On48K16BitsSamples(void* data, GoInt sumLen, GoInt dataLen);
extern void GetPhoneStateCallOut();
extern void GetPhoneStateIDLE();
extern void GetNetworkConn();
extern void GetNetworkLoseConn();
extern void RegisterWakeLockFunc(void* lock, void* release);
extern void RegisterMediaStateChangeFunc(void* start);
extern void RegisterKcpStateChangeFunc(void* conn, void* connLose);
extern void RegisterUpdateNotificationChangeFunc(void* updateTitle, void* updateContent);
extern void RegisterLocatorFunc(void* locationOnce, void* start, void* stop);
extern void RegisterLocationOnceFunc(void* locationOnce);
extern void RegisterRecorderFunc(void* start, void* stop);
extern void RegisterPlayerFunc(void* start, void* stop);
extern void RegisterOkErrPlayerFunc(void* ok, void* err);
extern int GetOnePackPcmData(void* buffer, GoInt32 frameCount);
extern void RegisterOpusCodecFunc(void* encode, void* decode);
extern void SetAppFilesDirPath(GoString filesDirPath);
extern void InitSqliteDataBase();
extern void RegisterGetAppBuildInfoFunc(void* cb);

#ifdef __cplusplus
}
#endif
