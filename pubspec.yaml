name: bf8100deviceapp
description: "dmr device app"
publish_to: 'none'
#真正的版本号在version.properties中，此处的版本号会被覆盖
version: 1.0.52

environment:
  sdk: '>=3.4.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  intl: ^0.19.0
  cupertino_icons: ^1.0.6
  flutter_launcher_icons: ^0.13.1
  flutter_localizations:
    sdk: flutter
  go_router: ^14.2.3
  shared_preferences: 2.4.0
  shared_preferences_android: 2.4.0
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  i18n_extension: ^12.0.1
  logger: ^2.4.0
  fixnum: ^1.1.0
  protobuf: ^3.1.0
  web_socket_channel: ^3.0.1
  flutter_dotenv: ^5.1.0
  geolocator: ^13.0.1
  permission_handler: ^11.3.1
  flutter_keyboard_visibility: ^6.0.0
  path_provider: ^2.1.5
  sqlite3: ^2.4.7
  path: ^1.9.0
  sqlite3_flutter_libs: ^0.5.26
  crypto: ^3.0.6
  dio: ^5.7.0
  marquee: ^2.2.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0
  riverpod_generator: ^2.4.0
  build_runner: ^2.4.11
  custom_lint: ^0.6.4
  riverpod_lint: ^2.3.10

flutter:
  uses-material-design: true

  assets:
    - assets/bfdx-icon.png
    - .env
    - assets/iconfont/
    - version.properties
  fonts:
    - family: AliyunIconfont
      fonts:
        - asset: assets/iconfont/iconfont.ttf
#        - asset: assets/iconfont/iconfont.woff
#        - asset: assets/iconfont/iconfont.woff2