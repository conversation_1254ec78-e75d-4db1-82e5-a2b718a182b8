import 'package:bf8100deviceapp/app_proto/app_db.pb.dart';
import 'package:bf8100deviceapp/riverpod/contacts.dart';
import 'package:flutter_test/flutter_test.dart';

List<Contact> contacts = [
  Contact<db_org>(
    originData: db_org(orgShortName: 'org1', dmrId: '80000001'),
    dataType: 1,
  ),
  Contact<db_org>(
    originData: db_org(orgShortName: 'org2', dmrId: '80000002'),
    dataType: 1,
  ),
  Contact<db_org>(
    originData: db_org(orgShortName: 'org3', dmrId: '80000003'),
    dataType: 1,
  ),
  Contact<db_device>(
    originData: db_device(selfId: 'dev1', dmrId: '00000010'),
    dataType: 2,
  ),
  Contact<db_device>(
    originData: db_device(selfId: 'dev2', dmrId: '00000011'),
    dataType: 2,
  ),
];

void main() {
  test('Contact encode/decode test', () async {
    var cM = contacts.map((c) => c.toMap()).toList();
    print("cM: $cM");

    var contacts2 = cM.map((c) => Contact.fromMap(c)).toList();
    print("contacts2: $contacts2");

    expect(contacts.length, contacts2.length);
    expect(contacts[0].dmrId, contacts2[0].dmrId);
  });
}
