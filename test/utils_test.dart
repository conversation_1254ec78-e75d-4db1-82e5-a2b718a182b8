import 'package:flutter_test/flutter_test.dart';
import 'package:bf8100deviceapp/util/dateTime.dart';

void main() {
  test('DateTime test', () async {
    var utcTime = "2025-02-05 07:33:00";
    var localTime = utcToLocal(utcTime);
    print("localTime = $localTime");
    expect(localTime, "2025-02-05 15:33:00");

    var time2 = localToUtc(localTime);
    print("time2 = $time2");
    expect(time2, utcTime);
  });
}
